"use client";

import { But<PERSON> } from "@/components/ui/button";

interface TimePeriodFilterProps {
  period: string;
  onChange: (period: string) => void;
}

export function TimePeriodFilter({
  period,
  onChange,
}: TimePeriodFilterProps) {
  return (
    <div className="flex items-center space-x-2 mb-4">
      <span className="text-sm font-medium">Time Period:</span>
      <div className="flex space-x-1">
        <Button
          variant={period === "7days" ? "default" : "outline"}
          size="sm"
          onClick={() => onChange("7days")}
        >
          Week
        </Button>
        <Button
          variant={period === "30days" ? "default" : "outline"}
          size="sm"
          onClick={() => onChange("30days")}
        >
          Month
        </Button>
        <Button
          variant={period === "90days" ? "default" : "outline"}
          size="sm"
          onClick={() => onChange("90days")}
        >
          Quarter
        </Button>
        <Button
          variant={period === "12months" ? "default" : "outline"}
          size="sm"
          onClick={() => onChange("12months")}
        >
          Year
        </Button>
        <Button
          variant={period === "lifetime" ? "default" : "outline"}
          size="sm"
          onClick={() => onChange("lifetime")}
        >
          Lifetime
        </Button>
      </div>
    </div>
  );
}