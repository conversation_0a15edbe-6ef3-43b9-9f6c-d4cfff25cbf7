import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/next-auth";
import db from "@/lib/shared-db";

export async function GET(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  try {
    const session: any = await getServerSession(authOptions);
    if (!session?.userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const tenantId = searchParams.get("tenantId");
    let { userId } = params;
    const split = userId?.split("_");
    let ip = "";

    if (split?.[0] === "user") {
      userId = split?.[1];
    } else if (split?.[0] === "ip") {
      ip = split?.[1];
      userId = "";
    }

    if (!tenantId) {
      return NextResponse.json(
        { error: "Tenant ID is required" },
        { status: 400 }
      );
    }

    // Get user sessions with comprehensive tracking data
    const sessions = await db.chatbotSession.findMany({
      where: {
        OR: [
          { ...(userId ? { userId: userId } : {}) },
          { ipAddress: ip }, // If userId is actually an IP hash
        ],
        chatbot: {
          tenantId: tenantId,
        },
        tenantId: tenantId, // Ensure tenant isolation
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            emailHash: true,
            createdAt: true,
          },
        },
        chatbot: {
          select: {
            id: true,
            name: true,
            access: true,
          },
        },
        messages: {
          select: {
            id: true,
            role: true,
            content: true,
            createdAt: true,
          },
          orderBy: {
            createdAt: "asc",
          },
        },
      },
      orderBy: {
        startedAt: "desc",
      },
    });

    if (sessions.length === 0) {
      return NextResponse.json(
        { error: "User not found or no sessions available" },
        { status: 404 }
      );
    }

    // Aggregate user information from all sessions
    const userInfo = {
      // Basic user information
      userId: sessions[0].user?.id || null,
      name: sessions[0].user?.name || "Anonymous User",
      email: sessions[0].user?.email || null,
      emailHash: sessions[0].user?.emailHash || null,
      userCreatedAt: sessions[0].user?.createdAt || null,

      // Session statistics
      totalSessions: sessions.length,

      // Message statistics - calculated from actual ChatbotMessage records
      totalMessages: sessions.reduce(
        (sum, session) => sum + (session.messages?.length || 0),
        0
      ),
      totalUserMessages: sessions.reduce(
        (sum, session) => sum + (session.messages?.filter((msg: any) => msg.role === "user").length || 0),
        0
      ),
      totalAssistantMessages: sessions.reduce(
        (sum, session) => sum + (session.messages?.filter((msg: any) => msg.role === "assistant").length || 0),
        0
      ),
      totalDuration: sessions.reduce(
        (sum, session) => sum + (session.duration || 0),
        0
      ),
      averageDuration:
        sessions.length > 0
          ? sessions.reduce(
              (sum, session) => sum + (session.duration || 0),
              0
            ) / sessions.length
          : 0,

      // Time tracking
      firstSeen: sessions[sessions.length - 1]?.startedAt,
      lastSeen: sessions[0]?.lastActivityAt,

      // Geographic information (from most recent session)
      location: {
        country: sessions[0]?.country,
        city: sessions[0]?.city,
        region: sessions[0]?.region,
        timezone: sessions[0]?.timezone,
      },

      // Device information (aggregated from all sessions)
      devices: {
        types: [...new Set(sessions.map((s) => s.deviceType).filter(Boolean))],
        operatingSystems: [
          ...new Set(sessions.map((s) => s.operatingSystem).filter(Boolean)),
        ],
        browsers: [
          ...new Set(sessions.map((s) => s.browserName).filter(Boolean)),
        ],
        screenResolutions: [
          ...new Set(
            sessions
              .map((s) =>
                s.screenWidth && s.screenHeight
                  ? `${s.screenWidth}x${s.screenHeight}`
                  : null
              )
              .filter(Boolean)
          ),
        ],
      },

      // Network information
      network: {
        ipAddresses: [
          ...new Set(sessions.map((s) => s.ipAddress).filter(Boolean)),
        ],
        isps: [...new Set(sessions.map((s) => s.isp).filter(Boolean))],
        organizations: [
          ...new Set(sessions.map((s) => s.organization).filter(Boolean)),
        ],
        connectionTypes: [
          ...new Set(sessions.map((s) => s.connectionType).filter(Boolean)),
        ],
      },

      // Web tracking
      web: {
        domains: [...new Set(sessions.map((s) => s.domain).filter(Boolean))],
        referrers: [
          ...new Set(sessions.map((s) => s.referrer).filter(Boolean)),
        ],
        languages: [
          ...new Set(sessions.map((s) => s.language).filter(Boolean)),
        ],
        userAgents: [
          ...new Set(sessions.map((s) => s.userAgent).filter(Boolean)),
        ],
      },

      // Marketing attribution
      marketing: {
        utmSources: [
          ...new Set(sessions.map((s) => s.utmSource).filter(Boolean)),
        ],
        utmMediums: [
          ...new Set(sessions.map((s) => s.utmMedium).filter(Boolean)),
        ],
        utmCampaigns: [
          ...new Set(sessions.map((s) => s.utmCampaign).filter(Boolean)),
        ],
        utmTerms: [...new Set(sessions.map((s) => s.utmTerm).filter(Boolean))],
        utmContents: [
          ...new Set(sessions.map((s) => s.utmContent).filter(Boolean)),
        ],
      },

      // Behavior analytics
      behavior: {
        isReturning: sessions.some((s) => s.isReturning),
        maxPreviousVisits: Math.max(
          ...sessions.map((s) => s.previousVisits || 0)
        ),
        averageSessionDepth:
          sessions.length > 0
            ? sessions.reduce((sum, s) => sum + (s.sessionDepth || 1), 0) /
              sessions.length
            : 1,
        totalErrors: sessions.reduce((sum, s) => sum + s.errorCount, 0),
        errorRate:
          sessions.length > 0
            ? (sessions.reduce((sum, s) => sum + s.errorCount, 0) /
                sessions.length) *
              100
            : 0,
      },

      // Satisfaction metrics
      satisfaction: {
        ratings: sessions.map((s) => s.satisfactionRating).filter(Boolean),
        averageRating: (() => {
          const ratings = sessions
            .map((s) => s.satisfactionRating)
            .filter(Boolean);
          return ratings.length > 0
            ? ratings.reduce((sum, rating) => sum + rating, 0) / ratings.length
            : null;
        })(),
        feedback: sessions.map((s) => s.feedback).filter(Boolean),
      },

      // Performance metrics
      performance: {
        averageResponseTime: (() => {
          const responseTimes = sessions
            .map((s) => s.averageResponseTime)
            .filter(Boolean);
          return responseTimes.length > 0
            ? responseTimes.reduce((sum, time) => sum + time, 0) /
                responseTimes.length
            : null;
        })(),
        chatbots: [
          ...new Set(
            sessions.map((s) => ({
              id: s.chatbot.id,
              name: s.chatbot.name,
              access: s.chatbot.access,
            }))
          ),
        ],
      },

      // Recent activity
      recentSessions: sessions.slice(0, 10).map((session) => ({
        id: session.id,
        sessionToken: session.sessionToken,
        chatbotName: session.chatbot.name,
        startedAt: session.startedAt,
        endedAt: session.endedAt,
        duration: session.duration,
        // Message counts from actual ChatbotMessage records
        messagesCount: session.messages?.length || 0,
        userMessagesCount: session.messages?.filter((msg: any) => msg.role === "user").length || 0,
        assistantMessagesCount: session.messages?.filter((msg: any) => msg.role === "assistant").length || 0,
        domain: session.domain,
        pageUrl: session.pageUrl,
        pageTitle: session.pageTitle,
        deviceType: session.deviceType,
        browserName: session.browserName,
        country: session.country,
        city: session.city,
        satisfactionRating: session.satisfactionRating,
        errorCount: session.errorCount,
        metadata: session.metadata,
      })),
    };

    // Fetch user's chatbots if userId is available
    let userChatbots: any[] = [];
    if (userId) {
      const chatbots = await db.chatbot.findMany({
        where: {
          userId: userId,
          tenantId: tenantId,
        },
        include: {
          _count: {
            select: {
              sessions: true,
              messages: true,
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
      });

      // Calculate additional statistics for each chatbot
      userChatbots = await Promise.all(
        chatbots.map(async (chatbot: any) => {
          // Get error count from sessions
          const errorStats = await db.chatbotSession.aggregate({
            where: {
              chatbotId: chatbot.id,
            },
            _sum: {
              errorCount: true,
            },
          });

          // Get detailed message counts from actual ChatbotMessage records
          const userMessages = await db.chatbotMessage.count({
            where: {
              chatbotId: chatbot.id,
              role: "user",
            },
          });

          const assistantMessages = await db.chatbotMessage.count({
            where: {
              chatbotId: chatbot.id,
              role: "assistant",
            },
          });

          const totalMessages = userMessages + assistantMessages;

          return {
            id: chatbot.id,
            name: chatbot.name,
            type: chatbot.type,
            access: chatbot.access,
            createdAt: chatbot.createdAt.toISOString(),
            isActive: chatbot.isActive,
            usageCount: chatbot.usageCount || 0,
            monthlyUsage: chatbot.monthlyUsage || 0,
            lastUsedAt: chatbot.lastUsedAt?.toISOString(),
            sessionsCount: chatbot._count.sessions,
            // Message counts from actual ChatbotMessage records
            messagesCount: totalMessages,
            userMessagesCount: userMessages,
            assistantMessagesCount: assistantMessages,
            averageResponseTime: null, // Could be calculated if needed
            errorCount: errorStats._sum.errorCount || 0,
          };
        })
      );
    }

    const finalUserInfo = {
      ...userInfo,
      userChatbots,
    };

    return NextResponse.json({ user: finalUserInfo });
  } catch (error) {
    console.error("Error fetching user details:", error);
    return NextResponse.json(
      { error: "Failed to fetch user details" },
      { status: 500 }
    );
  }
}
