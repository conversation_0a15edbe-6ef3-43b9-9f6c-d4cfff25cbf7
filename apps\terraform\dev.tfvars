resource_group_name           = "ceruniq-dev"
location                      = "Switzerland North"
static_app_location           = "westeurope"
vnet_name                     = "ceruniq-vnet-dev"
backend_app_service_plan_name = "ceruniq-app-services-dev"
backend_app_service_name      = "ceruniq-fastapi-dev"
swa_name                      = "ceruniq-nextjs"
database_name                 = "ceruniq-dev"
cosmosdb_password             = "8cqyf3hkiW3DA2Y"
cosmosdb_username             = "azadmin"
cosmosdb_name                 = "ceruniq-dev-qa-cluster"
cosmosdb_location             = "southindia"
cosmosdb_tire                 = "M30"
cosmosdb_size                 = "32"
environment                   = "dev"
storage_account_name          = "devceruniqstorage"
data_location                 = "Switzerland"
app_service_sku_name          = "B2"
admin_swa_name                = "ceruniq-admin-dev"
