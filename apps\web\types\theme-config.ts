export interface ThemeConfig {
  brandName?: string;
  logoUrl?: string;
  faviconUrl?: string;
  fullAppLogoUrl?: string;

  // Light theme color fields
  lightPrimaryColor?: string;
  lightSecondaryColor?: string;
  lightAccentColor?: string;
  lightNavigationBackgroundColor?: string;
  lightContentBackgroundColor?: string;
  lightForegroundColor?: string;

  // Dark theme color fields
  darkPrimaryColor?: string;
  darkSecondaryColor?: string;
  darkAccentColor?: string;
  darkNavigationBackgroundColor?: string;
  darkContentBackgroundColor?: string;
  contentBackgroundColor?: string;
  navigationBackgroundColor?: string;
  darkForegroundColor?: string;
  primaryColor?: string;
  secondaryColor?: string;
  accentColor?: string;
  foregroundColor?: string;

  themePreset?: ThemePreset;
  version?: number;
}

export type ThemePreset = "light" | "dark";

export interface ThemeColors {
  primary: string;
  secondary: string;
  accent: string;
  background: string;
  foreground: string;
  border: string;
  input: string;
  ring: string;
  card: string;
  cardForeground: string;
  popover: string;
  popoverForeground: string;
  primaryForeground: string;
  secondaryForeground: string;
  muted: string;
  mutedForeground: string;
  accentForeground: string;
  destructive: string;
  destructiveForeground: string;
}

export interface ThemePresetDefinition {
  name: string;
  displayName: string;
  description: string;
  colors: {
    light: Partial<ThemeColors>;
    dark: Partial<ThemeColors>;
  };
}

export interface CustomThemeVariables {
  "--primary": string;
  "--primary-foreground": string;
  "--secondary": string;
  "--secondary-foreground": string;
  "--accent": string;
  "--accent-foreground": string;
  "--background": string;
  "--foreground": string;
  "--border": string;
  "--input": string;
  "--ring": string;
  "--card": string;
  "--card-foreground": string;
  "--popover": string;
  "--popover-foreground": string;
  "--muted": string;
  "--muted-foreground": string;
  "--destructive": string;
  "--destructive-foreground": string;
  "--radius": string;
}

export interface ThemeConfigFormData {
  brandName: string;
  logoUrl: string;
  faviconUrl: string;
  fullAppLogoUrl: string;

  // Light theme color fields
  lightPrimaryColor: string;
  lightSecondaryColor: string;
  lightAccentColor: string;
  lightNavigationBackgroundColor: string;
  lightContentBackgroundColor: string;
  lightForegroundColor: string;

  // Dark theme color fields
  darkPrimaryColor: string;
  darkSecondaryColor: string;
  darkAccentColor: string;
  darkNavigationBackgroundColor: string;
  darkContentBackgroundColor: string;
  darkForegroundColor: string;

  themePreset: ThemePreset;
}
