"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { useLanguage } from "@/lib/language-context";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";

import toast from "react-hot-toast";
import { addUserToGroup } from "@/services/src/group";
import { getUsersWithCustomRoles } from "@/services/src/users";
import { getCookie } from "@/utils/cookies";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import { ChevronsUpDown, Users } from "lucide-react";
interface AddUserToGroupDialogProps {
  groupId: string;
  tenantId?: string;
  trigger: React.ReactNode;
  onUserAdded?: (addedUser?: CustomRoleUser, isOptimistic?: boolean) => void; // Callback to refresh parent component with user data
}

interface CustomRoleUser {
  id: string;
  name: string;
  email: string;
  image?: string;
  membership: Array<{
    role: string;
    customRoleId?: string;
    customRole?: {
      id: string;
      name: string;
    };
  }>;
  workspaceMember: Array<{
    customRoleId?: string;
    customRole?: {
      id: string;
      name: string;
    };
    workspace: {
      id: string;
      name: string;
    };
  }>;
}

export function AddUserToGroupDialog({
  groupId,
  tenantId,
  trigger,
  onUserAdded,
}: AddUserToGroupDialogProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingUsers, setIsLoadingUsers] = useState(false);
  const [selectedUser, setSelectedUser]: any = useState();
  const [users, setUsers] = useState<CustomRoleUser[]>([]);
  const [open, setOpen] = useState(false); // Add state for popover open/close

  const { t } = useLanguage();
  const userId = getCookie("userId") ?? "";

  // Load users with custom roles when dialog opens
  useEffect(() => {
    if (isOpen && tenantId && userId) {
      loadUsersWithCustomRoles();
    }
  }, [isOpen, tenantId, userId]);

  const loadUsersWithCustomRoles = async () => {
    try {
      setIsLoadingUsers(true);
      const result = await getUsersWithCustomRoles(userId, tenantId ?? "");
      if (result?.error) {
        toast.error(result.error);
        return;
      }
      setUsers(result.data || []);
    } catch (error) {
      toast.error(t("common.error"));
    } finally {
      setIsLoadingUsers(false);
    }
  };

  const onSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!tenantId || !userId || !selectedUser) {
      toast.error(t("common.error"));
      return;
    }

    try {
      setIsLoading(true);

      // Store the selected user for optimistic updates
      const addedUser = selectedUser;

      // Optimistically update parent component first
      if (onUserAdded) {
        onUserAdded(addedUser, true); // true indicates optimistic update
      }

      // Close dialog and clear selection optimistically
      setIsOpen(false);
      setSelectedUser();

      const result = await addUserToGroup(
        {
          groupId,
          userId: addedUser?.id,
          tenantId,
        },
        userId
      );

      if (result?.error) {
        // If there's an error, show error message and trigger a refresh to revert optimistic updates
        toast.error(result.error || t("common.error"));

        // Trigger a non-optimistic update to refresh the data and revert the optimistic change
        if (onUserAdded) {
          onUserAdded(undefined, false); // false indicates we need to fetch fresh data
        }
        return;
      }

      // User added successfully

      // Success! Show success message and trigger a non-optimistic update to ensure we have the latest data from server
      toast.success(t("groups.userAddedToGroup"));

      if (onUserAdded) {
        onUserAdded(addedUser, false); // false indicates we should fetch fresh data to confirm
      }

    } catch (error) {
      toast.error(t("groups.failedToAddUserToGroup"));

      // On error, trigger a refresh to revert optimistic updates
      if (onUserAdded) {
        onUserAdded(undefined, false);
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>{trigger}</DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{t("groups.addUserToGroup")}</DialogTitle>
          <DialogDescription>
            {t("groups.addUserToGroupDescription")}
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={onSubmit}>
          <div className="space-y-4 py-4 overflow-scroll">
            <div className="flex-col space-y-2 overflow-y-auto items-center">
              <Label className="mr-2">{t("common.selectUser")}</Label>
              {isLoadingUsers ? (
                <div className="flex items-center justify-center py-8">
                  <div className="text-sm text-muted-foreground">
                    {t("common.loading")}...
                  </div>
                </div>
              ) : users.length === 0 ? (
                <div className="flex items-center justify-center py-8">
                  <div className="text-sm text-muted-foreground">
                    {t("common.noUsers")}
                  </div>
                </div>
              ) : (
                <Popover open={open} modal={true} onOpenChange={setOpen}>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      role="combobox"
                      aria-expanded={open}
                      className="w-full md:w-80 justify-between"
                    >
                      <div className="flex items-center">
                        <Users className="mr-2 h-4 w-4" />
                        {selectedUser
                          ? selectedUser.email
                          : t("common.selectUser")}
                      </div>
                      <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent
                    className="w-[--radix-popover-trigger-width] p-0"
                    side="bottom"
                    sideOffset={4}
                  >
                    <Command shouldFilter={true}>
                      <CommandInput
                        placeholder={t("common.searchPlaceholder")}
                        className="border-none focus:ring-0"
                      />
                      <CommandList className="max-h-[120px] overflow-y-auto">
                        <CommandEmpty className="py-6 text-center text-sm text-muted-foreground">
                          {t("common.noUsers")}
                        </CommandEmpty>
                        <CommandGroup>
                          {users.map((user) => (
                            <CommandItem
                              key={user.id}
                              value={user.email}
                              onSelect={() => {
                                setSelectedUser(user);
                                setOpen(false); // Close the popover when user is selected
                              }}
                              className="cursor-pointer hover:bg-accent"
                            >
                              <span className="truncate">{user.email}</span>
                            </CommandItem>
                          ))}
                        </CommandGroup>
                      </CommandList>
                    </Command>
                  </PopoverContent>
                </Popover>
              )}
            </div>
          </div>
          <DialogFooter>
            <Button type="submit" disabled={isLoading || !selectedUser}>
              {isLoading ? t("common.adding") : t("common.add")}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
