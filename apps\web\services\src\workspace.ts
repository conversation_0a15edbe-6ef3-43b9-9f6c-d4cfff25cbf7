import { apiUrl, fetchJson } from "..";

export const createWorkspace = async (data, tenantId, userId) => {
  try {
    const headers = {
      "Content-Type": "application/json",
      "x-user-id": userId,
      "x-tenant-id": tenantId,
    };

    const response = await fetch(`${apiUrl}/workspaces`, {
      method: "POST",
      headers,
      body: JSON.stringify(data),
    });
    const responseData = await response.json();
    if (!response.ok) {
      throw new Error(responseData?.error || "Network response was not ok");
    }
    return responseData;
  } catch (error) {
    console.log({ "Create workspace api calling error": error });
    return { error: error?.message || "Error Creating The Workspace" };
  }
};
export const getWorkspace = async (tenantId, userId, workspaceSlug = "") => {
  if (workspaceSlug) {
    // Pass workspaceSlug in both query params and headers
    const response = await fetchJson(
      `${apiUrl}/workspaces?slug=${workspaceSlug}`,
      userId,
      tenantId,
      workspaceSlug
    );
    return response;
  }
  const response = await fetchJson(`${apiUrl}/workspaces`, userId, tenantId);
  return response;
};

export const getWorkspaceMember = async (tenantId, userId, workspaceSlug) => {
  const response = await fetchJson(
    `${apiUrl}/workspaces/members?workspaceSlug=${workspaceSlug}`,
    userId,
    tenantId,
    workspaceSlug
  );
  return response;
};

export const inviteWorkspaceMember = async (data, tenantId, userId) => {
  try {
    const url = `${apiUrl}/workspaces/members`;
    const headers = {
      "Content-Type": "application/json",
      "x-user-id": userId,
      "x-tenant-id": tenantId,
    };

    // Add workspace slug to headers if available
    if (data.workspaceSlug) {
      headers["x-workspace-slug"] = data.workspaceSlug;
    }

    const response = await fetch(url, {
      method: "POST",
      headers,
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Network response was not ok");
    }
    return await response.json();
  } catch (error) {
    console.log({ "Invite user api error": error });
    return { error: error.message || "Error inviting user" };
  }
};

export const removeWorkspaceMember = async (data, tenantId, userId) => {
  try {
    const url = `${apiUrl}/workspaces/members?workspaceSlug=${data.slug}&memberId=${data.memberId}`;
    const response = await fetch(url, {
      method: "DELETE",
      headers: {
        "x-user-id": userId,
        "x-tenant-id": tenantId,
        "x-workspace-slug": data.slug,
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Network response was not ok");
    }
    return await response.json();
  } catch (error) {
    console.log({ "Remove workspace member api error": error });
    return { error: error.message || "Error removing workspace member" };
  }
};

export const updateWorkspace = async (
  id,
  data,
  tenantId,
  userId,
  workspaceSlug = null
) => {
  try {
    const url = `${apiUrl}/workspaces?id=${id}`;
    const headers = {
      "Content-Type": "application/json",
      "x-user-id": userId,
      "x-tenant-id": tenantId,
    };

    // Add workspace slug to headers if available
    if (workspaceSlug) {
      headers["x-workspace-slug"] = workspaceSlug;
    }

    const response = await fetch(url, {
      method: "PUT",
      headers,
      body: JSON.stringify(data),
    });
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Network response was not ok");
    }
    return await response.json();
  } catch (error) {
    console.log({ "Update workspace api error": error });
    return { error: error.message || "Error updating workspace" };
  }
};

export const deleteWorkspace = async (data, tenantId, userId) => {
  try {
    const url = `${apiUrl}/workspaces?slug=${data.slug}`;
    const response = await fetch(url, {
      method: "DELETE",
      headers: {
        "x-user-id": userId,
        "x-tenant-id": tenantId,
        "x-workspace-slug": data.slug,
      },
    });
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Network response was not ok");
    }
    return await response.json();
  } catch (error) {
    console.log({ "Delete workspace api error": error });
    return { error: error.message || "Error deleting workspace" };
  }
};

// Get all user-workspace assignments with roles
export const getUserWorkspaceAssignments = async (
  tenantId: string,
  userId: string,
  filters?: {
    userId?: string;
    workspaceId?: string;
    role?: string;
    groupId?: string;
  }
) => {
  try {
    let url = `${apiUrl}/user-workspace-management`;

    // Add filters as query parameters if they exist
    if (filters) {
      const queryParams = new URLSearchParams();
      if (filters.userId) queryParams.append("userId", filters.userId);
      if (filters.workspaceId)
        queryParams.append("workspaceId", filters.workspaceId);
      if (filters.role) queryParams.append("role", filters.role);
      if (filters.groupId) queryParams.append("groupId", filters.groupId);

      if (queryParams.toString()) {
        url += `?${queryParams.toString()}`;
      }
    }

    // Extract workspaceSlug from filters if available
    const workspaceSlug = filters?.workspaceId
      ? await getWorkspaceSlugById(filters.workspaceId, tenantId, userId)
      : undefined;

    const response = await fetchJson(url, userId, tenantId, workspaceSlug);
    return response;
  } catch (error) {
    console.error("Error fetching user-workspace assignments:", error);
    return { error: "Failed to fetch user-workspace assignments" };
  }
};

// Add a user to a workspace with a specific role
export const addUserToWorkspace = async (
  data: {
    userId: string;
    workspaceSlug: string;
    role: string;
    customRoleId?: string;
  },
  tenantId: string,
  currentUserId: string
) => {
  try {
    const url = `${apiUrl}/user-workspace-management`;
    const headers = {
      "Content-Type": "application/json",
      "x-user-id": currentUserId,
      "x-tenant-id": tenantId,
    };

    // Add workspace slug to headers if available
    if (data.workspaceSlug) {
      headers["x-workspace-slug"] = data.workspaceSlug;
    }

    const response = await fetch(url, {
      method: "POST",
      headers,
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Network response was not ok");
    }
    return await response.json();
  } catch (error) {
    console.error("Error adding user to workspace:", error);
    return { error: error.message || "Error adding user to workspace" };
  }
};

// Helper function to get workspace slug by ID
export const getWorkspaceSlugById = async (
  workspaceId: string,
  tenantId: string,
  userId: string
): Promise<string | undefined> => {
  try {
    const response = await fetchJson(
      `${apiUrl}/workspaces?id=${workspaceId}`,
      userId,
      tenantId
    );

    if (response?.workspace?.slug) {
      return response.workspace.slug;
    }
    return undefined;
  } catch (error) {
    console.error("Error fetching workspace slug by ID:", error);
    return undefined;
  }
};

// Remove a user from a workspace
export const removeUserFromWorkspace = async (
  data: {
    userId: string;
    workspaceSlug: string;
  },
  tenantId: string,
  currentUserId: string
) => {
  try {
    const url = `${apiUrl}/user-workspace-management`;
    const headers = {
      "Content-Type": "application/json",
      "x-user-id": currentUserId,
      "x-tenant-id": tenantId,
    };

    // Add workspace slug to headers if available
    if (data.workspaceSlug) {
      headers["x-workspace-slug"] = data.workspaceSlug;
    }

    const response = await fetch(url, {
      method: "DELETE",
      headers,
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Network response was not ok");
    }
    return await response.json();
  } catch (error) {
    console.error("Error removing user from workspace:", error);
    return { error: error.message || "Error removing user from workspace" };
  }
};
