const CLIENT_ID = process.env.NEXT_PUBLIC_MICROSOFT_CLIENT_ID || "";
const TENANT_ID = process.env.NEXT_PUBLIC_MICROSOFT_TENANT_ID || "";

const APP_URL = process.env.NEXT_PUBLIC_API_BASE_URL || "";
const OAUTH_URL = `https://login.microsoftonline.com/common/oauth2/v2.0/authorize`;

const scopes = [
  "offline_access",
  "Files.ReadWrite",
  "Files.ReadWrite.All",
  "Sites.ReadWrite.All",
  "User.Read",
];

export const outlookLogin = async ({ tenantId, userId }) => {
  const redirectUrl = `${APP_URL}/api/integration/outlook/callback`;
  const options = {
    client_id: CLIENT_ID,
    scope: scopes.join(" "),
    redirect_uri: redirectUrl,
    state: JSON.stringify({ tenantId, userId }),
    response_mode: "query",
    response_type: "code",
  };
  const searchParams = new URLSearchParams(options).toString();

  window.open(`${OAUTH_URL}?${searchParams}`);
};

interface CreateFolderParams {
  name: string;
  parentFolderId?: string;
  tenantId: string;
  userId: string;
}

interface ListFoldersParams {
  tenantId: string;
  userId: string;
  parentFolderId?: string;
  skipToken?: string;
  workspaceSlug?: string;
}

interface ListSitesParams {
  userId: string;
  tenantId: string;
  workspaceSlug?: string;
}

interface ListSiteFoldersParams {
  tenantId: string;
  userId: string;
  siteId: string;
  driveId?: string;
  folderId?: string;
  skipToken?: string;
  workspaceSlug?: string;
}

interface UploadFileParams {
  name: string;
  slug?: string;
  size: number;
  tenantId: string;
  userId: string;
  siteId?: string;
  driveId?: string;
}

interface DeleteItemParams {
  tenantId: string;
  userId: string;
  itemId: string;
  siteId?: string;
  driveId?: string;
}

interface SyncOptions {
  folderId: string;
  tenantId: string;
  userId: string;
  workspaceSlug: string;
  pageId?: string;
  recursive?: boolean;
  checkSubFiles?: boolean;
  parentFolderId?: string;
  slug: string;
  siteId?: string; // SharePoint site ID
  driveId?: string; // SharePoint drive ID
}

interface SyncResult {
  synced: boolean;
  syncedFiles?: number;
  error?: string;
  folderId?: string;
  localFolderId?: string;
}

export const outlookDriveService = {
  // List available SharePoint sites
  listSites: async ({ tenantId, userId, workspaceSlug }: ListSitesParams) => {
    const params = new URLSearchParams({ tenantId, userId });

    const headers = {
      "x-user-id": userId,
      "x-tenant-id": tenantId,
    };

    // Add workspace slug to headers if available
    if (workspaceSlug) {
      headers["x-workspace-slug"] = workspaceSlug;
    }

    const response = await fetch(
      `${APP_URL}/api/integration/outlook/sites?${params}`,
      {
        method: "GET",
        headers,
      }
    );

    if (!response.ok) {
      const error = await response.json();
      return { error: error.message || "Failed to list SharePoint sites" };
    }

    return await response.json();
  },

  // List folders in a SharePoint site
  listSiteFolders: async ({
    tenantId,
    siteId,
    driveId,
    folderId,
    skipToken,
    userId,
    workspaceSlug,
  }: ListSiteFoldersParams) => {
    const params = new URLSearchParams({ tenantId, siteId, userId });
    if (driveId) params.append("driveId", driveId);
    if (folderId) params.append("folderId", folderId);
    if (skipToken) params.append("skipToken", skipToken);

    try {
      const headers = {
        "x-user-id": userId,
        "x-tenant-id": tenantId,
      };

      // Add workspace slug to headers if available
      if (workspaceSlug) {
        headers["x-workspace-slug"] = workspaceSlug;
      }

      const response = await fetch(
        `${APP_URL}/api/integration/outlook/sites/folders?${params}`,
        {
          method: "GET",
          headers,
        }
      );

      if (!response.ok) {
        const error = await response.json();
        console.error("Error listing site folders:", error);
        return { error: error.message || "Failed to list site folders" };
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error("Error listing site folders:", error);
      return { error: "Failed to list site folders" };
    }
  },

  syncFolder: async (options: SyncOptions): Promise<SyncResult> => {
    try {
      const headers = {
        "Content-Type": "application/json",
        "x-user-id": options.userId,
        "x-tenant-id": options.tenantId,
        "x-workspace-slug": options.workspaceSlug,
      };

      const response = await fetch(`${APP_URL}/api/integration/outlook/sync`, {
        method: "POST",
        headers,
        body: JSON.stringify({
          ...options,
          recursive: options.recursive !== false, // Default to true
          checkSubFiles: options.checkSubFiles !== false, // Default to true
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to sync with OneDrive");
      }

      return {
        synced: data.synced,
        syncedFiles: data.syncedFiles,
        error: data.error,
        folderId: data.folderId,
        localFolderId: data.localFolderId,
      };
    } catch (error: any) {
      console.error("OneDrive sync error:", error);
      return {
        synced: false,
        error: error.message || "Failed to sync with OneDrive",
      };
    }
  },
  createFolder: async ({
    name,
    parentFolderId,
    tenantId,
    userId,
  }: CreateFolderParams) => {
    const response = await fetch(`${APP_URL}/api/integration/outlook/drive`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ name, parentFolderId, tenantId, userId }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || "Failed to create folder");
    }

    const data = await response.json();
    return data.folder;
  },

  listFolders: async ({
    tenantId,
    parentFolderId,
    skipToken,
    userId,
  }: ListFoldersParams) => {
    const params = new URLSearchParams({ tenantId, userId });
    if (parentFolderId) params.append("parentFolderId", parentFolderId);
    if (skipToken) params.append("skipToken", skipToken);

    const headers = {
      "x-user-id": userId,
      "x-tenant-id": tenantId,
    };

    const response = await fetch(
      `${APP_URL}/api/integration/outlook/drive?${params}`,
      {
        method: "GET",
        headers,
      }
    );

    if (!response.ok) {
      const error = await response.json();
      return { error: error.message || "Failed to list folders" };
    }

    return await response.json();
  },

  initiateFileUpload: async ({
    name,
    slug,
    size,
    tenantId,
    userId,
  }: UploadFileParams) => {
    const response = await fetch(`${APP_URL}/api/integration/outlook/drive`, {
      method: "PUT",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ name, slug, size, tenantId, userId }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || "Failed to initiate file upload");
    }

    const data = await response.json();
    return data;
  },

  deleteItem: async ({ tenantId, itemId, userId }: DeleteItemParams) => {
    const params = new URLSearchParams({ tenantId, itemId, userId });
    const response = await fetch(
      `${APP_URL}/api/integration/outlook/drive?${params}`,
      {
        method: "DELETE",
      }
    );

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || "Failed to delete item");
    }

    return await response.json();
  },
};
