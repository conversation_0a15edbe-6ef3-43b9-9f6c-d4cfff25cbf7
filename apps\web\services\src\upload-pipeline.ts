interface UploadTarget {
  name: string;
  upload: (file: File, params: any) => Promise<any>;
  revert?: (fileId: string, params: any) => Promise<any>;
}

interface UploadPipelineConfig {
  maxRetries: number;
  retryDelay: number;
  targets: UploadTarget[];
}

interface UploadResult {
  success: boolean;
  targetName: string;
  fileId?: string;
  error?: any;
}

class UploadPipeline {
  private config: UploadPipelineConfig;
  private uploadResults: UploadResult[] = [];

  constructor(config: UploadPipelineConfig) {
    this.config = config;
  }

  private async delay(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  private async retryUpload(
    target: UploadTarget,
    file: File,
    params: any,
    attempt: number = 1
  ): Promise<UploadResult> {
    try {
      const result = await target.upload(file, params);
      return {
        success: true,
        targetName: target.name,
        fileId: result.id || result.fileId,
      };
    } catch (error) {
      if (attempt < this.config.maxRetries) {
        await this.delay(this.config.retryDelay * attempt);
        return this.retryUpload(target, file, params, attempt + 1);
      }
      return {
        success: false,
        targetName: target.name,
        error,
      };
    }
  }

  private async revertUploads(): Promise<void> {
    const successfulUploads = this.uploadResults.filter(
      (result) => result.success
    );

    for (const result of successfulUploads) {
      const target = this.config.targets.find(
        (t) => t.name === result.targetName
      );
      if (target?.revert && result.fileId) {
        try {
          await target.revert(result.fileId, {});
        } catch (error) {
          console.error(
            `Failed to revert upload for ${result.targetName}:`,
            error
          );
        }
      }
    }
  }

  async uploadFile(file: File, params: any): Promise<UploadResult[]> {
    this.uploadResults = [];
    let hasFailure = false;

    try {
      const uploadPromises = this.config.targets.map((target) =>
        this.retryUpload(target, file, params)
      );

      const results = await Promise.all(uploadPromises);
      this.uploadResults = results;

      hasFailure = results.some((result) => !result.success);
      if (hasFailure) {
        await this.revertUploads();
        throw new Error("Upload failed for one or more targets");
      }

      return results;
    } catch (error) {
      if (!hasFailure) {
        await this.revertUploads();
      }
      throw error;
    }
  }
}

// Create default upload targets
const defaultTargets: UploadTarget[] = [];

// Export a configured instance of the upload pipeline
export const uploadPipeline = new UploadPipeline({
  maxRetries: 3,
  retryDelay: 1000,
  targets: defaultTargets,
});
