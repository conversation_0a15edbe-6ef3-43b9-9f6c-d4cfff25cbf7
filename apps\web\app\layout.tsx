import "./globals.css";
import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON> as <PERSON>ont<PERSON><PERSON> } from "next/font/google";
import { cn } from "@/lib/utils";
import { ThemeProvider } from "@/components/theme/theme-provider";
import { LanguageProvider } from "@/lib/language-context";
import { SessionProvider } from "@/components/auth/session-provider";
import db from "@/lib/shared-db";
import { Toaster } from "react-hot-toast";
import LanguageHtmlAttributes from "@/components/language-html-attributes";
import DatadogInit from "@/components/datadog-init";
import { RealTimeNotificationProvider } from "@/components/providers/real-time-notification-provider";
import { cookies } from "next/headers";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/next-auth";

const fontSans = FontSans({
  subsets: ["latin"],
  variable: "--font-sans",
});

export async function generateMetadata(): Promise<Metadata> {
  const session: any = await getServerSession(authOptions);
  const tenantId =
    cookies().get("currentOrganizationId")?.value ??
    session?.memberships?.[0]?.tenant?.id;

  const isPartnerConsole =
    process.env.NEXT_PUBLIC_IS_PARTNER_CONSOLE === "1";

  const themeConfigDetails = isPartnerConsole && tenantId
    ? await db.themeConfiguration.findUnique({
      where: { tenantId },
    })
    : null;

  return {
    title: themeConfigDetails?.brandName || "Swiss Knowledge Hub",
    description: "Swiss Knowledge Hub",
    icons: {
      icon: [
        {
          url: themeConfigDetails?.faviconUrl || "/favicon.png",
          media: "(prefers-color-scheme: light)",
        },
        {
          url: themeConfigDetails?.faviconUrl || "/favicon-dark.png",
          media: "(prefers-color-scheme: dark)",
        },
      ],
    },
  };
}
export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}): JSX.Element {
  return (
    <html lang="en" suppressHydrationWarning>
      {/* <Script
        strategy="afterInteractive"
        src={`https://www.googletagmanager.com/gtag/js?id=${process.env.NEXT_PUBLIC_GA_TRACKING_ID}`}
      />
      <Script
        id="clarity"
        strategy="afterInteractive"
        dangerouslySetInnerHTML={{
          __html: `
          !function(c,l,a,r,i,t,y) {
            if (typeof a[c] !== 'function') {
              a[c] = function() {
                (a[c].q = a[c].q || []).push(arguments)
              };
            }
            if (a[c].v || a[c].t) {
              return a[c]("event", c, "dup." + i.projectId);
            }
            a[c].t = true;
            t = l.createElement(r);
            t.async = true;
            t.src = "https://www.clarity.ms/s/0.7.59/clarity.js";
            y = l.getElementsByTagName(r)[0];
            y.parentNode.insertBefore(t, y);
            a[c]("start", i);
            a[c].q.unshift(a[c].q.pop());
            a[c]("set", "C_IS", "0");
            a[c]("set", "C_V", "v_longTaskControl");
          }("clarity", document, window, "script", {
            "projectId": "${process.env.NEXT_PUBLIC_CLARITY_ID}",
            "upload": "https://r.clarity.ms/collect",
            "expire": 365,
            "cookies": ["_uetmsclkid", "_uetvid"],
            "track": true,
            "content": true,
            "dob": 1836,
            "longTask": 30
          });
        `,
        }}
      />
      <Script
        src="https://qa.one.flinkk.io/sdk.js"
        data-inbox-id="68419bd80a5d97c527f43826"
      ></Script>

      <Script
        id="gtag-init"
        strategy="afterInteractive"
        dangerouslySetInnerHTML={{
          __html: `
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', '${process.env.NEXT_PUBLIC_GA_TRACKING_ID}', {
              page_path: window.location.pathname,
            });
          `,
        }}
      /> */}
      <body className={cn(fontSans.variable, "bg-background font-sans")}>
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
        >
          <LanguageProvider>
            <LanguageHtmlAttributes />

            <SessionProvider>
              <RealTimeNotificationProvider>
                <DatadogInit />
                {children}
                <Toaster position="top-center" reverseOrder={false} />
              </RealTimeNotificationProvider>
            </SessionProvider>
          </LanguageProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}
