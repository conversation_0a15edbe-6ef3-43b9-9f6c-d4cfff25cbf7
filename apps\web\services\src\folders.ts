import { apiUrl, fetchJson } from "..";

export const createFolder = async (data, tenantId, userId) => {
  try {
    const headers = {
      "Content-Type": "application/json",
      "x-user-id": userId,
      "x-tenant-id": tenantId,
    };

    // Add workspace slug to headers if available in data
    if (data.workspaceSlug) {
      headers["x-workspace-slug"] = data.workspaceSlug;
    }

    const response = await fetch(`${apiUrl}/folders`, {
      method: "POST",
      headers,
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error("Network response was not ok");
    }
    return await response.json();
  } catch (error) {
    console.log({ "Create folder api calling error": error });
    return { error: "Error Creating The Folder" };
  }
};

export const updateFolder = async (data, tenantId, userId) => {
  try {
    const headers = {
      "Content-Type": "application/json",
      "x-user-id": userId,
      "x-tenant-id": tenantId,
    };

    // Add workspace slug to headers if available in data
    if (data.workspaceSlug) {
      headers["x-workspace-slug"] = data.workspaceSlug;
    }

    const response = await fetch(`${apiUrl}/folders`, {
      method: "PUT",
      headers,
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error("Network response was not ok");
    }
    return await response.json();
  } catch (error) {
    console.log({ "update folders api calling error": error });
    return { error: "Error Update The folders" };
  }
};

export const getFolder = async ({
  tenantId,
  userId,
  workspaceSlug,
  folderId = "",
}) => {
  try {
    if (folderId) {
      const response = await fetchJson(
        `${apiUrl}/folders?folderId=${folderId}&workspaceSlug=${encodeURIComponent(
          workspaceSlug
        )}`,
        userId,
        tenantId,
        workspaceSlug // Pass the raw workspaceSlug as the fourth parameter for headers
      );
      return response;
    }
    const response = await fetchJson(
      `${apiUrl}/folders?workspaceSlug=${encodeURIComponent(workspaceSlug)}`,
      userId,
      tenantId,
      workspaceSlug // Pass the raw workspaceSlug as the fourth parameter for headers
    );
    console.log(
      `Fetched all folders for workspace ${workspaceSlug}:`,
      response
    );
    return response;
  } catch (error) {
    console.error("Error in getFolder service:", error);
    throw error;
  }
};

export const deleteFolder = async (
  folderId,
  tenantId,
  userId,
  workspaceSlug = null,
  force = false
) => {
  try {
    const url = `${apiUrl}/folders?id=${folderId}${force ? "&force=true" : ""}`;
    const headers = {
      "x-user-id": userId,
      "x-tenant-id": tenantId,
    };

    // Add workspace slug to headers if available
    if (workspaceSlug) {
      headers["x-workspace-slug"] = workspaceSlug;
    }

    const response = await fetch(url, {
      method: "DELETE",
      headers,
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Network response was not ok");
    }
    return await response.json();
  } catch (error) {
    console.log({ "delete folder api error": error });
    return { error: error.message || "Error deleting folder" };
  }
};
