import { apiUrl, fetchJson } from "..";

// Get all users for a tenant
export const getUsers = async (
  userId: string,
  tenantId: string,
  customRoleOnly?: boolean
) => {
  try {
    const queryParams = new URLSearchParams();
    if (customRoleOnly) {
      queryParams.append("customRoleOnly", "true");
    }

    const url = `${apiUrl}/users${
      queryParams.toString() ? `?${queryParams.toString()}` : ""
    }`;
    const response = await fetchJson(url, userId, tenantId);
    return response;
  } catch (error) {
    console.error("Error fetching users:", error);
    return { error: "Failed to fetch users" };
  }
};

// Get users with custom roles only
export const getUsersWithCustomRoles = async (
  userId: string,
  tenantId: string
) => {
  return getUsers(userId, tenantId, true);
};

// Get a specific user by ID
export const getUser = async (
  userId: string,
  tenantId: string,
  targetUserId: string
) => {
  try {
    const response = await fetchJson(
      `${apiUrl}/users/${targetUserId}`,
      userId,
      tenantId
    );
    return response;
  } catch (error) {
    console.error("Error fetching user:", error);
    return { error: "Failed to fetch user" };
  }
};
