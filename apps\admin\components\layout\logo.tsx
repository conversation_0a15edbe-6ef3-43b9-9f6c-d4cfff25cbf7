import React, { useState, useEffect } from "react";
import { useTheme } from "next-themes";

export const Logo = ({ isCollapsed }) => {
  const { resolvedTheme } = useTheme();
  const [mounted, setMounted] = useState(false);

  // Prevent hydration mismatch by only rendering theme-dependent content after mount
  useEffect(() => {
    setMounted(true);
  }, []);

  // During SSR and before hydration, always render the light theme version
  if (!mounted) {
    if (!isCollapsed) {
      return (
        <svg
          width="90"
          height="50"
          viewBox="0 0 70 30"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            data-figma-bg-blur-radius="40"
            d="M11.9868 6.7935L0.323672 11.9958C-0.279589 12.3303 0.0723151 12.7762 0.474489 12.9248L11.7355 17.9231C12.5332 18.2671 12.7014 18.1972 13.4578 17.8825L13.495 17.867L24.3035 13.0734C25.3089 12.5904 25.2587 12.1445 24.404 11.7357L13.1934 6.75634C12.7579 6.53723 12.4922 6.55247 11.9868 6.7935Z"
            fill="#2D3691"
          />
          <path
            d="M24.404 17.7357C25.2587 18.1445 25.3089 18.5904 24.3035 19.0734L13.495 23.867L13.4578 23.8825C12.7014 24.1972 12.5332 24.2671 11.7355 23.9231L0.474489 18.9248C0.0723151 18.7762 -0.279589 18.3303 0.323672 17.9958L3.38889 16.5L11.2729 20.2105C12.6354 20.7535 12.8636 20.5471 13.6766 20.2105L21.8804 16.5584L24.404 17.7357Z"
            fill="#2D3691"
            fillOpacity="0.75"
          />
          <path
            d="M24.404 23.8486C25.2587 24.2574 25.3089 24.7033 24.3035 25.1863L13.495 29.9799L13.4578 29.9954C12.7014 30.3101 12.5332 30.38 11.7355 30.036L0.474489 25.0377C0.0723151 24.8891 -0.279589 24.4432 0.323672 24.1087L3.38889 22.6129L11.2729 26.3234C12.6354 26.8664 12.8636 26.66 13.6766 26.3234L21.8804 22.6713L24.404 23.8486Z"
            fill="#2D3691"
            fillOpacity="0.3"
          />
        </svg>
      );
    }
    return (
      <svg
        width="40"
        height="40"
        viewBox="0 0 46 52"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          data-figma-bg-blur-radius="40"
          d="M20.422 11.6952L0.551441 20.6511C-0.476336 21.2268 0.123203 21.9945 0.808388 22.2504L19.9937 30.8551C21.3748 31.4569 21.6484 31.3227 22.9914 30.7585L41.4059 22.5062C43.1189 21.6746 43.0332 20.907 41.5772 20.2033L22.4775 11.6312C21.7356 11.254 21.283 11.2802 20.422 11.6952Z"
          fill="#2D3691"
        />
        <path
          d="M41.5772 30.2777C43.0332 30.9947 43.1189 31.777 41.4059 32.6244L22.9914 41.0336C21.6484 41.6085 21.3748 41.7452 19.9937 41.132L0.808388 32.3637C0.123203 32.1029 -0.476336 31.3207 0.551441 30.734L5.77603 28.3344L19.2229 34.7104C21.5354 35.7534 21.9636 35.2971 23.6766 34.7104L37.3804 28.3583L41.5772 30.2777Z"
          fill="#2D3691"
          fillOpacity="0.75"
        />
        <path
          d="M41.5772 40.3906C43.0333 41.1077 43.1189 41.8899 41.4059 42.7374L22.9914 51.1465C21.6484 51.7215 21.3748 51.8582 19.9937 51.245L0.808388 42.4766C0.123203 42.2159 -0.476336 41.4336 0.551441 40.847L5.77602 38.4474L19.2229 44.8234C21.5354 45.8664 21.9636 45.4101 23.6766 44.8234L37.3804 38.4713L41.5772 40.3906Z"
          fill="#2D3691"
          fillOpacity="0.3"
        />
        <path
          d="M37.8376 0.227843C36.7614 3.23538 35.5981 4.4366 32.1744 5.41093C31.9457 5.47247 31.9381 5.51262 32.1744 5.59604C35.5981 6.61063 36.8015 7.77579 37.8376 10.7791C37.946 11.0551 38.0111 11.0916 38.141 10.7791C39.2429 7.77531 40.4362 6.59925 43.8042 5.59604C44.0718 5.5201 44.0586 5.47982 43.8042 5.41093C40.3032 4.25757 39.0205 3.15159 38.141 0.227843C38.0222 -0.07324 37.9557 -0.0786433 37.8376 0.227843Z"
          fill="#2D3691"
        />
        <path
          d="M43.4323 9.10357C42.9839 10.4706 42.4992 11.0166 41.0727 11.4595C40.9774 11.4875 40.9742 11.5057 41.0727 11.5437C42.4992 12.0048 43.0006 12.5344 43.4323 13.8996C43.4775 14.025 43.5046 14.0416 43.5587 13.8996C44.0179 12.5342 44.5151 11.9997 45.9184 11.5437C46.0299 11.5091 46.0244 11.4908 45.9184 11.4595C44.4597 10.9353 43.9252 10.4325 43.5587 9.10357C43.5093 8.96671 43.4816 8.96425 43.4323 9.10357Z"
          fill="#2D3691"
        />
      </svg>
    );
  }

  const isDarkTheme = resolvedTheme === "dark";
  if (!isCollapsed && !isDarkTheme) {
    return (
      <svg
        width="90"
        height="50"
        viewBox="0 0 70 30"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          data-figma-bg-blur-radius="40"
          d="M11.9868 6.7935L0.323672 11.9958C-0.279589 12.3303 0.0723151 12.7762 0.474489 12.9248L11.7355 17.9231C12.5332 18.2671 12.7014 18.1972 13.4578 17.8825L13.495 17.867L24.3035 13.0734C25.3089 12.5904 25.2587 12.1445 24.404 11.7357L13.1934 6.75634C12.7579 6.53723 12.4922 6.55247 11.9868 6.7935Z"
          fill="#2D3691"
        />
        <path
          d="M24.404 17.5877C25.2587 18.0042 25.3089 18.4586 24.3035 18.9509L13.495 23.8356L13.4578 23.8514C12.7014 24.172 12.5332 24.2433 11.7355 23.8928L0.474489 18.7994C0.0723151 18.648 -0.279589 18.1936 0.323672 17.8528L3.39028 16.4589L11.283 20.1626C12.6404 20.7685 12.8917 20.5034 13.8972 20.1626L21.9407 16.4728L24.404 17.5877Z"
          fill="#2D3691"
          fillOpacity="0.75"
        />
        <path
          d="M24.404 23.4622C25.2587 23.8787 25.3089 24.3331 24.3035 24.8253L13.495 29.7101L13.4578 29.7258C12.7014 30.0465 12.5332 30.1178 11.7355 29.7673L0.474489 24.6739C0.0723151 24.5224 -0.279589 24.068 0.323672 23.7272L3.39028 22.3334L11.283 26.0371C12.6404 26.6429 12.8917 26.3779 13.8972 26.0371L21.9407 22.3472L24.404 23.4622Z"
          fill="#2D3691"
          fillOpacity="0.3"
        />
        <path
          d="M22.2091 0.13235C21.5775 1.87937 20.8946 2.57714 18.8851 3.14311C18.7508 3.17886 18.7464 3.20218 18.8851 3.25064C20.8946 3.83999 21.601 4.51681 22.2091 6.2614C22.2728 6.42168 22.311 6.4429 22.3872 6.2614C23.034 4.51653 23.7344 3.83338 25.7113 3.25064C25.8684 3.20652 25.8606 3.18312 25.7113 3.14311C23.6563 2.47314 22.9034 1.8307 22.3872 0.13235C22.3175 -0.0425437 22.2785 -0.0456824 22.2091 0.13235Z"
          fill="url(#paint0_linear_217_2)"
        />
        <path
          d="M25.4931 5.28809C25.2299 6.0822 24.9454 6.39936 24.1081 6.65662C24.0521 6.67287 24.0503 6.68347 24.1081 6.7055C24.9454 6.97339 25.2397 7.28103 25.4931 8.07403C25.5196 8.14688 25.5355 8.15653 25.5673 8.07403C25.8368 7.2809 26.1286 6.97038 26.9523 6.7055C27.0178 6.68545 27.0146 6.67481 26.9523 6.65662C26.0961 6.35209 25.7824 6.06007 25.5673 5.28809C25.5383 5.2086 25.522 5.20717 25.4931 5.28809Z"
          fill="url(#paint1_linear_217_2)"
        />
        <path
          d="M38.008 24.192C37.112 24.192 36.2533 24.0747 35.432 23.84C34.6107 23.5947 33.9493 23.28 33.448 22.896L34.328 20.944C34.808 21.2853 35.3733 21.568 36.024 21.792C36.6853 22.0053 37.352 22.112 38.024 22.112C38.536 22.112 38.9467 22.064 39.256 21.968C39.576 21.8613 39.8107 21.7173 39.96 21.536C40.1093 21.3547 40.184 21.1467 40.184 20.912C40.184 20.6133 40.0667 20.3787 39.832 20.208C39.5973 20.0267 39.288 19.8827 38.904 19.776C38.52 19.6587 38.0933 19.552 37.624 19.456C37.1653 19.3493 36.7013 19.2213 36.232 19.072C35.7733 18.9227 35.352 18.7307 34.968 18.496C34.584 18.2613 34.2693 17.952 34.024 17.568C33.7893 17.184 33.672 16.6933 33.672 16.096C33.672 15.456 33.8427 14.8747 34.184 14.352C34.536 13.8187 35.0587 13.3973 35.752 13.088C36.456 12.768 37.336 12.608 38.392 12.608C39.096 12.608 39.7893 12.6933 40.472 12.864C41.1547 13.024 41.7573 13.2693 42.28 13.6L41.48 15.568C40.9573 15.2693 40.4347 15.0507 39.912 14.912C39.3893 14.7627 38.8773 14.688 38.376 14.688C37.8747 14.688 37.464 14.7467 37.144 14.864C36.824 14.9813 36.5947 15.136 36.456 15.328C36.3173 15.5093 36.248 15.7227 36.248 15.968C36.248 16.256 36.3653 16.4907 36.6 16.672C36.8347 16.8427 37.144 16.9813 37.528 17.088C37.912 17.1947 38.3333 17.3013 38.792 17.408C39.2613 17.5147 39.7253 17.6373 40.184 17.776C40.6533 17.9147 41.08 18.1013 41.464 18.336C41.848 18.5707 42.1573 18.88 42.392 19.264C42.6373 19.648 42.76 20.1333 42.76 20.72C42.76 21.3493 42.584 21.9253 42.232 22.448C41.88 22.9707 41.352 23.392 40.648 23.712C39.9547 24.032 39.0747 24.192 38.008 24.192ZM47.8111 21.44L47.6671 18.448L53.0271 12.8H55.9071L51.0751 18L49.6351 19.536L47.8111 21.44ZM45.4911 24V12.8H48.0671V24H45.4911ZM53.1551 24L49.1711 19.12L50.8671 17.28L56.1791 24H53.1551ZM65.9749 12.8H68.5669V24H65.9749V12.8ZM60.8869 24H58.2949V12.8H60.8869V24ZM66.1669 19.408H60.6949V17.216H66.1669V19.408Z"
          fill="#2D3691"
        />
        <defs>
          <clipPath
            id="bgblur_0_217_2_clip_path"
            transform="translate(40 33.3982)"
          >
            <path d="M11.9868 6.7935L0.323672 11.9958C-0.279589 12.3303 0.0723151 12.7762 0.474489 12.9248L11.7355 17.9231C12.5332 18.2671 12.7014 18.1972 13.4578 17.8825L13.495 17.867L24.3035 13.0734C25.3089 12.5904 25.2587 12.1445 24.404 11.7357L13.1934 6.75634C12.7579 6.53723 12.4922 6.55247 11.9868 6.7935Z" />
          </clipPath>
          <linearGradient
            id="paint0_linear_217_2"
            x1="22.2894"
            y1="6.88778"
            x2="20.546"
            y2="-0.410053"
            gradientUnits="userSpaceOnUse"
          >
            <stop stopColor="#C4CAFF" />
            <stop offset="0.465" stopColor="#2D3691" stopOpacity="0.75" />
            <stop offset="1" stopColor="#2D3691" />
          </linearGradient>
          <linearGradient
            id="paint1_linear_217_2"
            x1="25.5265"
            y1="8.35875"
            x2="24.6708"
            y2="5.07524"
            gradientUnits="userSpaceOnUse"
          >
            <stop stopColor="#C4CAFF" />
            <stop offset="0.465" stopColor="#2D3691" stopOpacity="0.75" />
            <stop offset="1" stopColor="#2D3691" />
          </linearGradient>
        </defs>
      </svg>
    );
  }
  if (!isCollapsed && isDarkTheme) {
    return (
      <svg
        width="85"
        height="37"
        viewBox="0 0 85 37"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          data-figma-bg-blur-radius="40"
          d="M16.2944 9.04534L0.926485 15.2881C0.131601 15.6894 0.595286 16.2245 1.12521 16.4029L15.9632 22.4009C17.0143 22.8137 17.2359 22.7297 18.2326 22.3521L18.2816 22.3336L32.5234 16.5813C33.8482 16.0016 33.782 15.4665 32.6559 14.976L17.8842 9.00075C17.3103 8.73781 16.9603 8.7561 16.2944 9.04534Z"
          fill="white"
        />
        <path
          d="M32.6559 21.9984C33.782 22.4982 33.8482 23.0435 32.5234 23.6342L18.2816 29.4958C17.2429 29.8966 17.0313 29.9919 15.9632 29.5645L1.12521 23.4524C0.595286 23.2707 0.131601 22.7254 0.926485 22.3164L4.96719 20.6438L15.367 25.0882C17.1555 25.8153 17.4867 25.4972 18.8115 25.0882L29.4101 20.6604L32.6559 21.9984Z"
          fill="white"
          fillOpacity="0.75"
        />
        <path
          d="M32.6559 29.0477C33.782 29.5475 33.8482 30.0928 32.5234 30.6835L18.2816 36.5451L18.2326 36.5641C17.2359 36.9489 17.0143 37.0344 15.9632 36.6138L1.12521 30.5017C0.595286 30.32 0.131601 29.7747 0.926485 29.3658L4.96719 27.6931L15.367 32.1375C17.1555 32.8646 17.4867 32.5465 18.8115 32.1375L29.4101 27.7098L32.6559 29.0477Z"
          fill="white"
          fillOpacity="0.3"
        />
        <path
          d="M29.7637 1.05189C28.9314 3.14831 28.0317 3.98563 25.3838 4.6648C25.2069 4.7077 25.201 4.73568 25.3838 4.79383C28.0316 5.50106 28.9624 6.31324 29.7637 8.40674C29.8476 8.59909 29.8979 8.62455 29.9983 8.40674C30.8506 6.3129 31.7734 5.49312 34.3783 4.79383C34.5853 4.74089 34.5751 4.71282 34.3783 4.6648C31.6706 3.86083 30.6786 3.08991 29.9983 1.05189C29.9065 0.842014 29.8551 0.838247 29.7637 1.05189Z"
          fill="white"
        />
        <path
          d="M34.0906 7.23882C33.7438 8.19174 33.369 8.57234 32.2657 8.88105C32.192 8.90055 32.1895 8.91327 32.2657 8.9397C33.3689 9.26117 33.7567 9.63034 34.0906 10.5819C34.1256 10.6694 34.1466 10.6809 34.1884 10.5819C34.5435 9.63019 34.928 9.25756 36.0134 8.9397C36.0996 8.91564 36.0954 8.90288 36.0134 8.88105C34.8852 8.51561 34.4718 8.16519 34.1884 7.23882C34.1501 7.14342 34.1287 7.14171 34.0906 7.23882Z"
          fill="white"
        />
        <path
          d="M48.8806 29.192C47.9846 29.192 47.1259 29.0747 46.3046 28.84C45.4832 28.5947 44.8219 28.28 44.3206 27.896L45.2006 25.944C45.6806 26.2853 46.2459 26.568 46.8966 26.792C47.5579 27.0053 48.2246 27.112 48.8966 27.112C49.4086 27.112 49.8192 27.064 50.1286 26.968C50.4486 26.8613 50.6832 26.7173 50.8326 26.536C50.9819 26.3547 51.0566 26.1467 51.0566 25.912C51.0566 25.6133 50.9392 25.3787 50.7046 25.208C50.4699 25.0267 50.1606 24.8827 49.7766 24.776C49.3926 24.6587 48.9659 24.552 48.4966 24.456C48.0379 24.3493 47.5739 24.2213 47.1046 24.072C46.6459 23.9227 46.2246 23.7307 45.8406 23.496C45.4566 23.2613 45.1419 22.952 44.8966 22.568C44.6619 22.184 44.5446 21.6933 44.5446 21.096C44.5446 20.456 44.7152 19.8747 45.0566 19.352C45.4086 18.8187 45.9312 18.3973 46.6246 18.088C47.3286 17.768 48.2086 17.608 49.2646 17.608C49.9686 17.608 50.6619 17.6933 51.3446 17.864C52.0272 18.024 52.6299 18.2693 53.1526 18.6L52.3526 20.568C51.8299 20.2693 51.3072 20.0507 50.7846 19.912C50.2619 19.7627 49.7499 19.688 49.2486 19.688C48.7472 19.688 48.3366 19.7467 48.0166 19.864C47.6966 19.9813 47.4672 20.136 47.3286 20.328C47.1899 20.5093 47.1206 20.7227 47.1206 20.968C47.1206 21.256 47.2379 21.4907 47.4726 21.672C47.7072 21.8427 48.0166 21.9813 48.4006 22.088C48.7846 22.1947 49.2059 22.3013 49.6646 22.408C50.1339 22.5147 50.5979 22.6373 51.0566 22.776C51.5259 22.9147 51.9526 23.1013 52.3366 23.336C52.7206 23.5707 53.0299 23.88 53.2646 24.264C53.5099 24.648 53.6326 25.1333 53.6326 25.72C53.6326 26.3493 53.4566 26.9253 53.1046 27.448C52.7526 27.9707 52.2246 28.392 51.5206 28.712C50.8272 29.032 49.9472 29.192 48.8806 29.192ZM58.6837 26.44L58.5397 23.448L63.8997 17.8H66.7797L61.9477 23L60.5077 24.536L58.6837 26.44ZM56.3637 29V17.8H58.9397V29H56.3637ZM64.0277 29L60.0437 24.12L61.7397 22.28L67.0517 29H64.0277ZM76.8474 17.8H79.4394V29H76.8474V17.8ZM71.7594 29H69.1674V17.8H71.7594V29ZM77.0394 24.408H71.5674V22.216H77.0394V24.408Z"
          fill="white"
        />
        <defs>
          <clipPath
            id="bgblur_0_180_6_clip_path"
            transform="translate(39.5 31.1847)"
          >
            <path d="M16.2944 9.04534L0.926485 15.2881C0.131601 15.6894 0.595286 16.2245 1.12521 16.4029L15.9632 22.4009C17.0143 22.8137 17.2359 22.7297 18.2326 22.3521L18.2816 22.3336L32.5234 16.5813C33.8482 16.0016 33.782 15.4665 32.6559 14.976L17.8842 9.00075C17.3103 8.73781 16.9603 8.7561 16.2944 9.04534Z" />
          </clipPath>
        </defs>
      </svg>
    );
  }
  if (isDarkTheme) {
    return (
      <svg
        width="40"
        height="40"
        viewBox="0 0 46 52"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <foreignObject
          x="-40"
          y="-28.6348"
          width="122.68"
          height="99.8832"
        ></foreignObject>
        <path
          data-figma-bg-blur-radius="40"
          d="M20.422 11.6952L0.551441 20.6512C-0.476336 21.2269 0.123203 21.9945 0.808388 22.2504L19.9937 30.8552C21.3748 31.4569 21.6484 31.3228 22.9914 30.7585L41.4059 22.5063C43.1189 21.6747 43.0332 20.907 41.5772 20.2034L22.4775 11.6313C21.7356 11.2541 21.283 11.2803 20.422 11.6952Z"
          fill="white"
        />
        <path
          d="M41.5772 30.2777C43.0332 30.9948 43.1189 31.777 41.4059 32.6245L22.9914 41.0336C21.6484 41.6086 21.3748 41.7453 19.9937 41.1321L0.808388 32.3637C0.123203 32.103 -0.476336 31.3207 0.551441 30.734L5.77603 28.3345L19.2229 34.7105C21.5354 35.7535 21.9636 35.2971 23.6766 34.7105L37.3804 28.3584L41.5772 30.2777Z"
          fill="white"
          fillOpacity="0.75"
        />
        <path
          d="M41.5772 40.3906C43.0333 41.1077 43.1189 41.8899 41.4059 42.7374L22.9914 51.1465C21.6484 51.7215 21.3748 51.8582 19.9937 51.245L0.808388 42.4766C0.123203 42.2159 -0.476336 41.4336 0.551441 40.847L5.77602 38.4474L19.2229 44.8234C21.5354 45.8664 21.9636 45.4101 23.6766 44.8234L37.3804 38.4713L41.5772 40.3906Z"
          fill="white"
          fillOpacity="0.3"
        />
        <path
          d="M37.8376 0.227843C36.7614 3.23538 35.5981 4.4366 32.1744 5.41093C31.9457 5.47247 31.9381 5.51262 32.1744 5.59604C35.5981 6.61063 36.8015 7.77579 37.8376 10.7791C37.946 11.0551 38.0111 11.0916 38.141 10.7791C39.2429 7.77531 40.4362 6.59925 43.8042 5.59604C44.0718 5.5201 44.0586 5.47982 43.8042 5.41093C40.3032 4.25757 39.0205 3.15159 38.141 0.227843C38.0222 -0.07324 37.9557 -0.0786433 37.8376 0.227843Z"
          fill="white"
        />
        <path
          d="M43.4323 9.10357C42.9839 10.4706 42.4992 11.0166 41.0727 11.4595C40.9774 11.4875 40.9742 11.5057 41.0727 11.5437C42.4992 12.0048 43.0006 12.5344 43.4323 13.8996C43.4775 14.025 43.5046 14.0416 43.5587 13.8996C44.0179 12.5342 44.5151 11.9997 45.9184 11.5437C46.0299 11.5091 46.0244 11.4908 45.9184 11.4595C44.4597 10.9353 43.9252 10.4325 43.5587 9.10357C43.5093 8.96671 43.4816 8.96425 43.4323 9.10357Z"
          fill="white"
        />
        <defs>
          <clipPath
            id="bgblur_0_180_17_clip_path"
            transform="translate(40 28.6348)"
          >
            <path d="M20.422 11.6952L0.551441 20.6512C-0.476336 21.2269 0.123203 21.9945 0.808388 22.2504L19.9937 30.8552C21.3748 31.4569 21.6484 31.3228 22.9914 30.7585L41.4059 22.5063C43.1189 21.6747 43.0332 20.907 41.5772 20.2034L22.4775 11.6313C21.7356 11.2541 21.283 11.2803 20.422 11.6952Z" />
          </clipPath>
        </defs>
      </svg>
    );
  }
  return (
    <svg
      width="40"
      height="40"
      viewBox="0 0 46 52"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        data-figma-bg-blur-radius="40"
        d="M20.422 11.6952L0.551441 20.6511C-0.476336 21.2268 0.123203 21.9945 0.808388 22.2504L19.9937 30.8551C21.3748 31.4569 21.6484 31.3227 22.9914 30.7585L41.4059 22.5062C43.1189 21.6746 43.0332 20.907 41.5772 20.2033L22.4775 11.6312C21.7356 11.254 21.283 11.2802 20.422 11.6952Z"
        fill="#2D3691"
      />
      <path
        d="M41.5772 30.2777C43.0332 30.9947 43.1189 31.777 41.4059 32.6244L22.9914 41.0336C21.6484 41.6085 21.3748 41.7452 19.9937 41.132L0.808388 32.3637C0.123203 32.1029 -0.476336 31.3207 0.551441 30.734L5.77603 28.3344L19.2229 34.7104C21.5354 35.7534 21.9636 35.2971 23.6766 34.7104L37.3804 28.3583L41.5772 30.2777Z"
        fill="#2D3691"
        fillOpacity="0.75"
      />
      <path
        d="M41.5772 40.3906C43.0333 41.1077 43.1189 41.8899 41.4059 42.7374L22.9914 51.1465C21.6484 51.7215 21.3748 51.8582 19.9937 51.245L0.808388 42.4766C0.123203 42.2159 -0.476336 41.4336 0.551441 40.847L5.77602 38.4474L19.2229 44.8234C21.5354 45.8664 21.9636 45.4101 23.6766 44.8234L37.3804 38.4713L41.5772 40.3906Z"
        fill="#2D3691"
        fillOpacity="0.3"
      />
      <path
        d="M37.8376 0.227843C36.7614 3.23538 35.5981 4.4366 32.1744 5.41093C31.9457 5.47247 31.9381 5.51262 32.1744 5.59604C35.5981 6.61063 36.8015 7.77579 37.8376 10.7791C37.946 11.0551 38.0111 11.0916 38.141 10.7791C39.2429 7.77531 40.4362 6.59925 43.8042 5.59604C44.0718 5.5201 44.0586 5.47982 43.8042 5.41093C40.3032 4.25757 39.0205 3.15159 38.141 0.227843C38.0222 -0.07324 37.9557 -0.0786433 37.8376 0.227843Z"
        fill="url(#paint0_linear_166_2)"
      />
      <path
        d="M43.4323 9.10357C42.9839 10.4706 42.4992 11.0166 41.0727 11.4595C40.9774 11.4875 40.9742 11.5057 41.0727 11.5437C42.4992 12.0048 43.0006 12.5344 43.4323 13.8996C43.4775 14.025 43.5046 14.0416 43.5587 13.8996C44.0179 12.5342 44.5151 11.9997 45.9184 11.5437C46.0299 11.5091 46.0244 11.4908 45.9184 11.4595C44.4597 10.9353 43.9252 10.4325 43.5587 9.10357C43.5093 8.96671 43.4816 8.96425 43.4323 9.10357Z"
        fill="url(#paint1_linear_166_2)"
      />
      <defs>
        <clipPath
          id="bgblur_0_166_2_clip_path"
          transform="translate(40 28.6348)"
        >
          <path d="M20.422 11.6952L0.551441 20.6511C-0.476336 21.2268 0.123203 21.9945 0.808388 22.2504L19.9937 30.8551C21.3748 31.4569 21.6484 31.3227 22.9914 30.7585L41.4059 22.5062C43.1189 21.6746 43.0332 20.907 41.5772 20.2033L22.4775 11.6312C21.7356 11.254 21.283 11.2802 20.422 11.6952Z" />
        </clipPath>
        <linearGradient
          id="paint0_linear_166_2"
          x1="37.9743"
          y1="11.8575"
          x2="34.9451"
          y2="-0.691668"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#C4CAFF" />
          <stop offset="0.465" stopColor="#2D3691" stopOpacity="0.75" />
          <stop offset="1" stopColor="#2D3691" />
        </linearGradient>
        <linearGradient
          id="paint1_linear_166_2"
          x1="43.4893"
          y1="14.3898"
          x2="42.0028"
          y2="8.74469"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#C4CAFF" />
          <stop offset="0.465" stopColor="#2D3691" stopOpacity="0.75" />
          <stop offset="1" stopColor="#2D3691" />
        </linearGradient>
      </defs>
    </svg>
  );
};
