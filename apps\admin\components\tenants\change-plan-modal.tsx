"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { ArrowUpDown, Loader2 } from "lucide-react";
import { useRouter } from "next/navigation";
import { useToast } from "@/components/ui/use-toast";

interface Plan {
  id: string;
  name: string;
  type: string;
  description?: string;
  price?: number;
  includedUsers: number;
  vectorStoreGB: number;
  webSearchLimit: number;
  additionalUserFee?: number;
}

interface Tenant {
  id: string;
  name: string;
  plan?: string;
  status: "active" | "inactive" | "suspended";
}

interface ChangePlanModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  tenant: Tenant | null;
  onSuccess?: (message: string) => void;
  onError?: (error: string) => void;
  availablePlans: Plan[];
}

export function ChangePlanModal({
  open,
  onOpenChange,
  tenant,
  onSuccess,
  onError,
  availablePlans
}: ChangePlanModalProps) {
  const router = useRouter();
  const { toast } = useToast();
  const [selectedPlanId, setSelectedPlanId] = useState<string>("");
  const [isChangingPlan, setIsChangingPlan] = useState(false);

  // Reset state when modal closes
  useEffect(() => {
    if (!open) {
      setSelectedPlanId("");
    }
  }, [open]);

  const handlePlanChange = async () => {
    if (!tenant || !selectedPlanId) return;

    setIsChangingPlan(true);
    try {
      // Use the API endpoint instead of server action
      const response = await fetch(`/api/tenants/${tenant.id}/change-plan`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          planId: selectedPlanId,
          activateAccount: tenant.status !== 'active'
        }),
      });

      if (response.ok) {
        const result = await response.json();
        console.log('Plan changed successfully:', result);

        // Show success toast
        toast({
          title: "Success",
          description: result.message || `Plan changed successfully to ${result.subscription?.planName}`,
        });

        // Call onSuccess callback if provided
        if (onSuccess) {
          onSuccess(result.message || `Plan changed successfully to ${result.subscription?.planName}`);
        }

        // Close modal and reset state
        onOpenChange(false);

        // Refresh the page to show updated data
        router.refresh();
      } else {
        const errorData = await response.json();
        console.error('Failed to change plan:', errorData.error);
        toast({
          title: "Error",
          description: errorData.error || 'Failed to change plan',
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Error changing plan:', error);
      toast({
        title: "Error",
        description: 'An error occurred while changing the plan',
        variant: "destructive",
      });
    } finally {
      setIsChangingPlan(false);
    }
  };

  const handleClose = () => {
    onOpenChange(false);
  };

  if (!tenant) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Change Plan</DialogTitle>
          <DialogDescription>
            Change the plan for tenant "{tenant.name}".
          </DialogDescription>
        </DialogHeader>
        <div className="space-y-4">
          <div>
            <label className="text-sm font-medium mb-2 block">Current Plan</label>
            <div className="p-2 bg-muted rounded-md text-sm">
              {tenant.plan || 'No Plan'}
            </div>
          </div>
          <div>
            <label className="text-sm font-medium mb-2 block">New Plan</label>
            <Select
              value={selectedPlanId}
              onValueChange={setSelectedPlanId}
              disabled={isChangingPlan}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select new plan" />
              </SelectTrigger>
              <SelectContent>
                {availablePlans.map((plan) => (
                  <SelectItem key={plan.id} value={plan.id}>
                    <div className="flex flex-col">
                      <span className="font-medium">{plan.name}</span>
                      <span className="text-xs text-muted-foreground">
                        {plan.price ? `CHF ${plan.price}/month` : 'Custom pricing'} •
                        {plan.includedUsers} users •
                        {plan.vectorStoreGB}GB storage
                      </span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          {selectedPlanId && (
            <div className="text-sm text-muted-foreground">
              <p>This will:</p>
              <ul className="list-disc list-inside mt-1 space-y-1">
                <li>Deactivate the current subscription</li>
                <li>Create a new subscription with the selected plan</li>
                {tenant.status !== 'active' && (
                  <li>Reactivate the tenant account</li>
                )}
              </ul>
            </div>
          )}
        </div>
        <DialogFooter>
          <Button 
            variant="outline" 
            onClick={handleClose}
            disabled={isChangingPlan}
          >
            Cancel
          </Button>
          <Button
            onClick={handlePlanChange}
            disabled={!selectedPlanId || isChangingPlan}
          >
            {isChangingPlan ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Changing Plan...
              </>
            ) : (
              <>
                <ArrowUpDown className="mr-2 h-4 w-4" />
                Change Plan
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
