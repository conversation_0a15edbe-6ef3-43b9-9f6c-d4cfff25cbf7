import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/next-auth";
import db from "@/lib/shared-db";

interface BlockUserRequest {
  reason?: string;
  blockType: 'email' | 'ip' | 'user';
  duration?: number; // Duration in hours, null for permanent
}

export async function POST(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  try {
    const session: any = await getServerSession(authOptions);
    if (!session?.userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const tenantId = searchParams.get("tenantId");
    let { userId } = params;

    if (!tenantId) {
      return NextResponse.json(
        { error: "Tenant ID is required" },
        { status: 400 }
      );
    }

    const body: BlockUserRequest = await request.json();
    const { reason, blockType, duration } = body;

    // Parse the userId to determine the type
    const split = userId?.split("_");
    let actualUserId = "";
    let ipAddress = "";

    if (split?.[0] === "user") {
      actualUserId = split?.[1];
    } else if (split?.[0] === "ip") {
      ipAddress = split?.[1];
    }

    // Create blocked user entry
    const blockedUser = await db.blockedUser.create({
      data: {
        tenantId: tenantId,
        blockedBy: session.userId,
        reason: reason || "Blocked by administrator",
        blockType: blockType,
        
        // Set the appropriate identifier based on block type
        userId: actualUserId || undefined,
        email: blockType === 'email' ? actualUserId : undefined,
        ipAddress: blockType === 'ip' ? ipAddress : undefined,
        
        // Set expiration if duration is provided
        expiresAt: duration ? new Date(Date.now() + duration * 60 * 60 * 1000) : null,
        
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    });

    return NextResponse.json({
      success: true,
      message: `User blocked successfully`,
      blockedUser: {
        id: blockedUser.id,
        blockType: blockedUser.blockType,
        reason: blockedUser.reason,
        expiresAt: blockedUser.expiresAt,
        createdAt: blockedUser.createdAt,
      },
    });

  } catch (error) {
    console.error("Error blocking user:", error);
    return NextResponse.json(
      { error: "Failed to block user" },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  try {
    const session: any = await getServerSession(authOptions);
    if (!session?.userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const tenantId = searchParams.get("tenantId");
    let { userId } = params;

    if (!tenantId) {
      return NextResponse.json(
        { error: "Tenant ID is required" },
        { status: 400 }
      );
    }

    // Parse the userId to determine the type
    const split = userId?.split("_");
    let actualUserId = "";
    let ipAddress = "";

    if (split?.[0] === "user") {
      actualUserId = split?.[1];
    } else if (split?.[0] === "ip") {
      ipAddress = split?.[1];
    }

    // Find and delete the blocked user entry completely
    const whereClause: any = {
      tenantId: tenantId,
      isActive: true,
    };

    if (actualUserId) {
      whereClause.OR = [
        { userId: actualUserId },
        { email: actualUserId },
      ];
    } else if (ipAddress) {
      whereClause.ipAddress = ipAddress;
    }

    // First, let's check what records exist before deleting
    const existingBlocks = await db.blockedUser.findMany({
      where: whereClause,
    });

    // Delete the blocked user records completely
    const deletedBlocks = await db.blockedUser.deleteMany({
      where: whereClause,
    });


    if (deletedBlocks.count === 0) {
      return NextResponse.json({
        success: false,
        message: `No active blocks found for this user`,
        deletedCount: 0,
      }, { status: 404 });
    }

    return NextResponse.json({
      success: true,
      message: `User unblocked successfully`,
      deletedCount: deletedBlocks.count,
    });

  } catch (error) {
    return NextResponse.json(
      { error: "Failed to unblock user" },
      { status: 500 }
    );
  }
}

// GET - Check if user is blocked
export async function GET(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  try {
    const session: any = await getServerSession(authOptions);
    if (!session?.userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const tenantId = searchParams.get("tenantId");
    let { userId } = params;

    if (!tenantId) {
      return NextResponse.json(
        { error: "Tenant ID is required" },
        { status: 400 }
      );
    }

    // Parse the userId to determine the type
    const split = userId?.split("_");
    let actualUserId = "";
    let ipAddress = "";

    if (split?.[0] === "user") {
      actualUserId = split?.[1];
    } else if (split?.[0] === "ip") {
      ipAddress = split?.[1];
    }

    // Check if user is blocked - build query step by step
    let whereClause: any = {
      tenantId: tenantId,
      isActive: true,
      AND: [
        // Check expiration
        {
          OR: [
            { expiresAt: null }, // Permanent blocks
            { expiresAt: { gt: new Date() } }, // Non-expired blocks
          ],
        },
      ],
    };

    // Add user identification criteria
    if (actualUserId) {
      whereClause.AND.push({
        OR: [
          { userId: actualUserId },
          { email: actualUserId },
        ],
      });
    } else if (ipAddress) {
      whereClause.AND.push({
        ipAddress: ipAddress,
      });
    }


    const blockedUser = await db.blockedUser.findFirst({
      where: whereClause,
      orderBy: { createdAt: 'desc' },
    });

    return NextResponse.json({
      isBlocked: !!blockedUser,
      blockInfo: blockedUser ? {
        id: blockedUser.id,
        blockType: blockedUser.blockType,
        reason: blockedUser.reason,
        expiresAt: blockedUser.expiresAt,
        createdAt: blockedUser.createdAt,
      } : null,
    });

  } catch (error) {
    return NextResponse.json(
      { error: "Failed to check block status" },
      { status: 500 }
    );
  }
}
