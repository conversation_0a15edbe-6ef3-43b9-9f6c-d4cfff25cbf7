import axios from "axios";
import { apiUrl } from "..";
import { SubscriptionWithPlan } from "./subscriptions";

export interface VectorStoreUsage {
  id: string;
  tenantId: string;
  usageGB: number;
  timestamp: string;
  createdAt: string;
  updatedAt: string;
}

export interface VectorStoreUsageSummary {
  currentUsage: number;
  totalUsage: number;
  limit: number;
  subscription: SubscriptionWithPlan | null;
}

export interface VectorStoreUsageResponse {
  usage: VectorStoreUsage;
}

export interface VectorStoreUsageSummaryResponse
  extends VectorStoreUsageSummary {}

// Get vector store usage summary for a tenant
export const getVectorStoreUsageSummary = async (
  tenantId: string,
  userId: string
): Promise<VectorStoreUsageSummary> => {
  try {
    const response = await axios.get<VectorStoreUsageSummaryResponse>(
      `${apiUrl}/vector-store-usage?tenantId=${tenantId}&userId=${userId}`
    );
    return response.data;
  } catch (error) {
    console.error(
      `Error fetching vector store usage for tenant ${tenantId}:`,
      error
    );
    throw error;
  }
};

// Record new vector store usage with retry and failover
export const recordVectorStoreUsage = async (
  tenantId: string,
  usageGB: number,
  maxRetries = 3,
  retryDelay = 1000
): Promise<VectorStoreUsage | null> => {
  let retries = 0;

  while (retries < maxRetries) {
    try {
      const response = await axios.post<VectorStoreUsageResponse>(
        `${apiUrl}/vector-store-usage`,
        {
          tenantId,
          usageGB,
        },
        { timeout: 5000 } // 5 second timeout
      );
      return response.data.usage;
    } catch (error) {
      retries++;
      console.error(
        `Error recording vector store usage for tenant ${tenantId} (attempt ${retries}/${maxRetries}):`,
        error
      );

      // If we've reached max retries, don't throw, just return null
      if (retries >= maxRetries) {
        console.warn(
          `Failed to record vector store usage after ${maxRetries} attempts. Continuing without recording usage.`
        );
        return null;
      }

      // Wait before retrying (exponential backoff)
      await new Promise((resolve) =>
        setTimeout(resolve, retryDelay * Math.pow(2, retries - 1))
      );
    }
  }

  return null; // This should never be reached due to the return in the if statement above
};
