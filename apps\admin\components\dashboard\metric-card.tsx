import { Card, CardContent } from "@/components/ui/card";
import { LucideIcon } from "lucide-react";
import { cn } from "@/lib/utils";

interface MetricCardProps {
  title: string;
  value: string;
  description?: string;
  icon: LucideIcon;
  trend?: {
    value: number;
    isPositive: boolean;
  };
  color?: "default" | "indigo" | "green" | "blue" | "amber";
}

export function MetricCard({
  title,
  value,
  description,
  icon: Icon,
  trend,
  color = "default",
}: MetricCardProps) {
  const colorStyles = {
    default: {
      iconBg: "bg-gray-100 dark:bg-gray-800",
      iconColor: "text-gray-500 dark:text-gray-400",
      trendUp: "text-green-600 dark:text-green-500",
      trendDown: "text-red-600 dark:text-red-500",
    },
    indigo: {
      iconBg: "bg-indigo-100 dark:bg-indigo-950",
      iconColor: "text-indigo-600 dark:text-indigo-400",
      trendUp: "text-green-600 dark:text-green-500",
      trendDown: "text-red-600 dark:text-red-500",
    },
    green: {
      iconBg: "bg-green-100 dark:bg-green-950",
      iconColor: "text-green-600 dark:text-green-400",
      trendUp: "text-green-600 dark:text-green-500",
      trendDown: "text-red-600 dark:text-red-500",
    },
    blue: {
      iconBg: "bg-blue-100 dark:bg-blue-950",
      iconColor: "text-blue-600 dark:text-blue-400",
      trendUp: "text-green-600 dark:text-green-500",
      trendDown: "text-red-600 dark:text-red-500",
    },
    amber: {
      iconBg: "bg-amber-100 dark:bg-amber-950",
      iconColor: "text-amber-600 dark:text-amber-400",
      trendUp: "text-green-600 dark:text-green-500",
      trendDown: "text-red-600 dark:text-red-500",
    },
  };

  return (
    <Card className="overflow-hidden">
      <CardContent className="p-0">
        <div className="flex items-center gap-4 p-6">
          <div
            className={cn(
              "flex h-12 w-12 items-center justify-center rounded-lg",
              colorStyles[color].iconBg
            )}
          >
            <Icon className={cn("h-6 w-6", colorStyles[color].iconColor)} />
          </div>
          <div className="space-y-1">
            <p className="text-sm font-medium text-muted-foreground">{title}</p>
            <div className="flex items-baseline gap-2">
              <p className="text-2xl font-bold tracking-tight">{value}</p>
              {trend && (
                <div
                  className={cn(
                    "flex items-center text-xs font-medium",
                    trend.isPositive
                      ? colorStyles[color].trendUp
                      : colorStyles[color].trendDown
                  )}
                >
                  {trend.isPositive ? "↑" : "↓"} {trend.value}%
                </div>
              )}
            </div>
            {description && (
              <p className="text-xs text-muted-foreground">{description}</p>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
