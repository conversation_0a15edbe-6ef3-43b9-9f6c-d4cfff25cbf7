@tailwind base;
@tailwind components;
@tailwind utilities;

@layer utilities {
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  .line-clamp-1 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
  }

  .line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }

  .line-clamp-3 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }

  /* Ensure proper text wrapping in markdown content */
  .markdown {
    word-wrap: break-word;
    overflow-wrap: break-word;
    hyphens: auto;
  }

  .markdown * {
    max-width: 100%;
    word-wrap: break-word;
    overflow-wrap: break-word;
  }

  .markdown pre {
    white-space: pre-wrap;
    word-wrap: break-word;
    overflow-wrap: break-word;
    overflow-x: auto;
  }

  .markdown code {
    word-wrap: break-word;
    overflow-wrap: break-word;
  }

  /* Quick Ask Dialog specific styles */
  .quick-ask-message {
    word-wrap: break-word;
    overflow-wrap: break-word;
    hyphens: auto;
    text-align: left;
  }

  .quick-ask-message * {
    max-width: 100%;
    word-wrap: break-word;
    overflow-wrap: break-word;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    overflow: hidden;
    height: 100vh;
  }

  html {
    height: 100vh;
    overflow: hidden;
  }

  html {
    height: 100vh;
    overflow: hidden;
  }

  :root {
    --background: 234.1 54% 100%;
    --foreground: 234.1 2.7% 10%;
    --card: 234.1 27% 100%;
    --card-foreground: 234.1 2.7% 15%;
    --popover: 234.1 54% 100%;
    --popover-foreground: 234.1 97.7% 10%;
    --primary: 234.1 52.9% 37.5%;
    --primary-foreground: 0 0% 100%;
    --secondary: 234.1 20.8% 90%;
    --secondary-foreground: 0 0% 0%;
    --muted: 272.1 20.8% 95%;
    --muted-foreground: 234.1 2.7% 40%;
    --accent: 272.1 20.8% 90%;
    --accent-foreground: 234.1 2.7% 15%;
    --destructive: 0 77% 50%;
    --destructive-foreground: 234.1 2.7% 100%;
    --border: 234.1 25.4% 82%;
    --input: 234.1 25.4% 50%;
    --ring: 234.1 88.7% 45.3%;
    --radius: 0.5rem;
    --chart-1: 195.9 72% 53%;
    --chart-2: 267.9 72% 53%;
    --chart-3: 339.9 72% 53%;
    --chart-4: 51.9 72% 53%;
    --chart-5: 123.9 72% 53%;
    --sidebar-background: 234, 54%, 95%;
    --sidebar-foreground: 234, 3%, 9%;
    --sidebar-primary: 234, 53%, 34%;
    --sidebar-primary-foreground: 0, 0%, 100%;
    --sidebar-accent: 272, 21%, 86%;
    --sidebar-accent-foreground: 0 0% 0%;
    --sidebar-border: 234, 25%, 78%;
    --sidebar-ring: 224, 98%, 45%;
  }

  .dark {
    --background: 234.1 31.6% 10%;
    --foreground: 234.1 2.7% 100%;
    --card: 234.1 27% 10%;
    --card-foreground: 234.1 2.7% 100%;
    --popover: 234.1 31.6% 5%;
    --popover-foreground: 234.1 2.7% 100%;
    --primary: 234.1 52.9% 37.5%;
    --primary-foreground: 0 0% 100%;
    --secondary: 234.1 20.8% 20%;
    --secondary-foreground: 0 0% 100%;
    --muted: 272.1 20.8% 25%;
    --muted-foreground: 234.1 2.7% 65%;
    --accent: 272.1 20.8% 25%;
    --accent-foreground: 234.1 2.7% 95%;
    --destructive: 0 77% 50%;
    --destructive-foreground: 234.1 2.7% 100%;
    --border: 234.1 25.4% 50%;
    --input: 234.1 25.4% 50%;
    --ring: 234.1 88.7% 45.3%;
    --radius: 0.5rem;
    --chart-1: 195.9 72% 53%;
    --chart-2: 267.9 72% 53%;
    --chart-3: 339.9 72% 53%;
    --chart-4: 51.9 72% 53%;
    --chart-5: 123.9 72% 53%;
    --sidebar-background: 234, 32%, 13%;
    --sidebar-foreground: 234, 3%, 100%;
    --sidebar-primary: 234, 53%, 30%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 272, 21%, 21%;
    --sidebar-accent-foreground: 0 0% 100%;
    --sidebar-border: 234, 25%, 48%;
    --sidebar-ring: 224, 98%, 45%;
  }
}

.markDown ol li {
  margin-top: 8px;
}

.markDown pre {
  overflow-x: scroll;
  margin-top: 8px;
  margin-bottom: 8px;
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Hide scrollbar for Chrome, Safari and Opera */
.scrollbar::-webkit-scrollbar {
  display: none;
}

/* Hide scrollbar for IE, Edge and Firefox */
.scrollbar {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}

.markDown ol li {
  margin-top: 8px;
}

.markDown pre {
  overflow-x: scroll;
  margin-top: 8px;
  margin-bottom: 8px;
}
/* Proper heading styles for markdown content */
.markdown h1,
.markdown h2,
.markdown h3,
.markdown h4,
.markdown h5,
.markdown h6 {
  margin-top: 1.5em;
  margin-bottom: 0.5em;
  font-weight: 600;
  line-height: 1.25;
  color: hsl(var(--foreground));
}

/* Remove top margin for first heading */
.markdown h1:first-child,
.markdown h2:first-child,
.markdown h3:first-child,
.markdown h4:first-child,
.markdown h5:first-child,
.markdown h6:first-child {
  margin-top: 0;
}

.markdown h1 {
  font-size: 2em;
  border-bottom: 1px solid hsl(var(--border));
  padding-bottom: 0.3em;
}

.markdown h2 {
  font-size: 1.5em;
  border-bottom: 1px solid hsl(var(--border));
  padding-bottom: 0.3em;
}

.markdown h3 {
  font-size: 1.25em;
}

.markdown h4 {
  font-size: 1.1em;
}

.markdown h5 {
  font-size: 1em;
}

.markdown h6 {
  font-size: 0.9em;
}

/* Ensure proper paragraph spacing */
.markdown p {
  margin: 0.5em;
  color: hsl(var(--foreground));
}

/* Ensure proper table styling within markdown content */
.markdown table {
  border-collapse: collapse;
  margin: 1rem 0;
  max-width: 100%;
  overflow-x: auto;
  display: block;
  white-space: nowrap;
}

.markdown th,
.markdown td {
  border: 1px solid hsl(var(--border));
  padding: 0.5rem;
  text-align: left;
  white-space: nowrap;
}

.markdown th {
  background-color: hsl(var(--muted));
  font-weight: 600;
}

.markdown tbody tr:nth-child(even) {
  background-color: hsl(var(--muted) / 0.3);
}

/* Ensure code blocks don't overflow */
.markdown pre {
  overflow-x: auto;
  max-width: 100%;
  white-space: pre;
  margin: 1rem 0;
  padding: 1rem;
  background-color: hsl(var(--muted));
  border-radius: 0.375rem;
  border: 1px solid hsl(var(--border));
}

.markdown code {
  word-break: break-all;
  white-space: pre-wrap;
  background-color: hsl(var(--muted));
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-size: 0.875em;
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas,
    "Liberation Mono", Menlo, monospace;
}

.markdown pre code {
  word-break: normal;
  white-space: pre;
  background-color: transparent;
  padding: 0;
  border-radius: 0;
  font-size: inherit;
}

/* Ensure wide content doesn't break layout */
.markdown {
  overflow-wrap: break-word;
  word-wrap: break-word;
  hyphens: auto;
}

/* Handle long URLs and text */
.markdown p {
  word-break: break-word;
  overflow-wrap: break-word;
}

/* Ensure lists don't overflow */
.markdown ul,
.markdown ol {
  padding-left: 1.5rem;
  margin: 1rem 0;
}

.markdown li {
  margin-bottom: 0.5rem;
  word-break: break-word;
}

/* Line clamp utilities for text truncation */
.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}

/* DateTime input styling for consistent display across browsers */
input[type="datetime-local"] {
  color-scheme: light dark;
}

/* Ensure datetime-local inputs display properly in dark mode */
.dark input[type="datetime-local"] {
  color-scheme: dark;
}

/* Styling for imported HTML content from URL imports */
.imported-html-content {
  line-height: 1.6;
  font-size: 16px;
}

.imported-html-content h1,
.imported-html-content h2,
.imported-html-content h3,
.imported-html-content h4,
.imported-html-content h5,
.imported-html-content h6 {
  margin-top: 1.5em;
  margin-bottom: 0.5em;
  font-weight: 600;
  line-height: 1.25;
  color: hsl(var(--foreground));
}

.imported-html-content h1 {
  font-size: 2em;
  border-bottom: 1px solid hsl(var(--border));
  padding-bottom: 0.3em;
}
.imported-html-content h2 {
  font-size: 1.5em;
  border-bottom: 1px solid hsl(var(--border));
  padding-bottom: 0.3em;
}
.imported-html-content h3 {
  font-size: 1.25em;
}
.imported-html-content h4 {
  font-size: 1.1em;
}
.imported-html-content h5 {
  font-size: 1em;
}
.imported-html-content h6 {
  font-size: 0.9em;
}

.imported-html-content p {
  margin-bottom: 1em;
  color: hsl(var(--foreground));
}

.imported-html-content a {
  color: hsl(var(--primary));
  text-decoration: underline;
  text-underline-offset: 2px;
}

.imported-html-content a:hover {
  color: hsl(var(--primary) / 0.8);
}

.imported-html-content img {
  max-width: 100%;
  height: auto;
  border-radius: 0.375rem;
  margin: 1em 0;
  box-shadow:
    0 1px 3px 0 rgb(0 0 0 / 0.1),
    0 1px 2px -1px rgb(0 0 0 / 0.1);
}

.imported-html-content blockquote {
  border-left: 4px solid hsl(var(--primary));
  padding-left: 1em;
  margin: 1em 0;
  font-style: italic;
  color: hsl(var(--muted-foreground));
  background-color: hsl(var(--muted) / 0.3);
  padding: 1em;
  border-radius: 0.375rem;
}

.imported-html-content ul,
.imported-html-content ol {
  margin: 1em 0;
  padding-left: 2em;
}

.imported-html-content li {
  margin-bottom: 0.5em;
}

.imported-html-content code {
  background-color: hsl(var(--muted));
  color: hsl(var(--foreground));
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-size: 0.875em;
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas,
    "Liberation Mono", Menlo, monospace;
}

.imported-html-content pre {
  background-color: hsl(var(--muted));
  color: hsl(var(--foreground));
  padding: 1rem;
  border-radius: 0.375rem;
  overflow-x: auto;
  margin: 1em 0;
  border: 1px solid hsl(var(--border));
}

.imported-html-content pre code {
  background-color: transparent;
  padding: 0;
  border-radius: 0;
  font-size: inherit;
}

.imported-html-content table {
  width: 100%;
  border-collapse: collapse;
  margin: 1em 0;
  border: 1px solid hsl(var(--border));
  border-radius: 0.375rem;
  overflow: hidden;
}

.imported-html-content th,
.imported-html-content td {
  border: 1px solid hsl(var(--border));
  padding: 0.75rem;
  text-align: left;
}

.imported-html-content th {
  background-color: hsl(var(--muted));
  font-weight: 600;
  color: hsl(var(--foreground));
}

.imported-html-content tbody tr:nth-child(even) {
  background-color: hsl(var(--muted) / 0.3);
}

.imported-html-content tbody tr:hover {
  background-color: hsl(var(--muted) / 0.5);
}

.imported-html-content hr {
  border: none;
  border-top: 1px solid hsl(var(--border));
  margin: 2em 0;
}

.imported-html-content strong,
.imported-html-content b {
  font-weight: 600;
  color: hsl(var(--foreground));
}

.imported-html-content em,
.imported-html-content i {
  font-style: italic;
}

.imported-html-content mark {
  background-color: hsl(var(--primary) / 0.2);
  color: hsl(var(--foreground));
  padding: 0.125rem 0.25rem;
  border-radius: 0.125rem;
}

/* Handle nested lists */
.imported-html-content ul ul,
.imported-html-content ol ol,
.imported-html-content ul ol,
.imported-html-content ol ul {
  margin: 0.5em 0;
}

/* Style for definition lists */
.imported-html-content dl {
  margin: 1em 0;
}

.imported-html-content dt {
  font-weight: 600;
  margin-top: 1em;
  color: hsl(var(--foreground));
}

.imported-html-content dd {
  margin-left: 1em;
  margin-bottom: 0.5em;
  color: hsl(var(--muted-foreground));
}

/* Ensure proper spacing for first and last elements */
.imported-html-content > *:first-child {
  margin-top: 0;
}

.imported-html-content > *:last-child {
  margin-bottom: 0;
}

/* Custom Media Player Styles */
/* Audio/Video Progress Bar */
input[type="range"] {
  -webkit-appearance: none;
  appearance: none;
  background: transparent;
  cursor: pointer;
}

/* Audio Progress Bar */
input[type="range"]::-webkit-slider-track {
  background: hsl(var(--muted));
  height: 8px;
  border-radius: 4px;
}

input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  background: hsl(var(--primary));
  height: 16px;
  width: 16px;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s ease;
}

input[type="range"]::-webkit-slider-thumb:hover {
  transform: scale(1.2);
  box-shadow: 0 0 0 4px hsl(var(--primary) / 0.2);
}

input[type="range"]::-moz-range-track {
  background: hsl(var(--muted));
  height: 8px;
  border-radius: 4px;
  border: none;
}

input[type="range"]::-moz-range-thumb {
  background: hsl(var(--primary));
  height: 16px;
  width: 16px;
  border-radius: 50%;
  cursor: pointer;
  border: none;
  transition: all 0.2s ease;
}

input[type="range"]::-moz-range-thumb:hover {
  transform: scale(1.2);
  box-shadow: 0 0 0 4px hsl(var(--primary) / 0.2);
}

/* Video Progress Bar (Red theme) */
.video-progress input[type="range"]::-webkit-slider-track {
  background: rgba(255, 255, 255, 0.3);
  height: 4px;
  border-radius: 2px;
}

.video-progress input[type="range"]::-webkit-slider-thumb {
  background: #ef4444;
  height: 12px;
  width: 12px;
}

.video-progress input[type="range"]::-moz-range-track {
  background: rgba(255, 255, 255, 0.3);
  height: 4px;
  border-radius: 2px;
}

.video-progress input[type="range"]::-moz-range-thumb {
  background: #ef4444;
  height: 12px;
  width: 12px;
}

/* Volume Control */
.volume-control input[type="range"] {
  -webkit-appearance: none;
  appearance: none;
  background: transparent;
  height: 24px;
  width: 100%;
  min-width: 80px;
  cursor: pointer;
}

.volume-control input[type="range"]::-webkit-slider-track {
  background: hsl(var(--muted));
  height: 8px;
  border-radius: 4px;
  border: 1px solid hsl(var(--border));
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
}

.volume-control input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  background: hsl(var(--primary));
  height: 18px;
  width: 18px;
  border-radius: 50%;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  margin-top: -7px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.volume-control input[type="range"]::-webkit-slider-thumb:hover {
  transform: scale(1.1);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.3);
}

.volume-control input[type="range"]::-moz-range-track {
  background: hsl(var(--muted));
  height: 8px;
  border-radius: 4px;
  border: 1px solid hsl(var(--border));
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
}

.volume-control input[type="range"]::-moz-range-thumb {
  background: hsl(var(--primary));
  height: 18px;
  width: 18px;
  border-radius: 50%;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  cursor: pointer;
  border: none;
  transition: all 0.2s ease;
}

.volume-control input[type="range"]::-moz-range-thumb:hover {
  transform: scale(1.1);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.3);
}

/* Video Volume Control (White theme) */
.video-volume input[type="range"] {
  -webkit-appearance: none;
  appearance: none;
  background: transparent;
  height: 20px;
  width: 100%;
  min-width: 80px;
  cursor: pointer;
}

.video-volume input[type="range"]::-webkit-slider-track {
  background: rgba(255, 255, 255, 0.6);
  height: 6px;
  border-radius: 3px;
  border: 1px solid rgba(255, 255, 255, 0.8);
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.2);
}

.video-volume input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  background: white;
  height: 16px;
  width: 16px;
  border-radius: 50%;
  border: 2px solid rgba(0, 0, 0, 0.1);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
  margin-top: -7px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.video-volume input[type="range"]::-webkit-slider-thumb:hover {
  transform: scale(1.1);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.5);
}

.video-volume input[type="range"]::-moz-range-track {
  background: rgba(255, 255, 255, 0.6);
  height: 6px;
  border-radius: 3px;
  border: 1px solid rgba(255, 255, 255, 0.8);
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.2);
}

.video-volume input[type="range"]::-moz-range-thumb {
  background: white;
  height: 16px;
  width: 16px;
  border-radius: 50%;
  border: 2px solid rgba(0, 0, 0, 0.1);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
  cursor: pointer;
  border: none;
  transition: all 0.2s ease;
}

.video-volume input[type="range"]::-moz-range-thumb:hover {
  transform: scale(1.1);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.5);
}

/* Focus styles for accessibility */
input[type="range"]:focus {
  outline: 2px solid hsl(var(--ring));
  outline-offset: 2px;
}

/* Ensure volume controls override global styles */
.volume-control input[type="range"]:focus,
.video-volume input[type="range"]:focus {
  outline: 2px solid hsl(var(--ring));
  outline-offset: 2px;
}

/* Media player container focus */
.media-player-container:focus {
  outline: 2px solid hsl(var(--ring));
  outline-offset: 2px;
}

/* High specificity volume control overrides */
.media-player-container .volume-control input[type="range"] {
  -webkit-appearance: none !important;
  appearance: none !important;
  background: transparent !important;
  height: 24px !important;
  width: 100% !important;
  min-width: 80px !important;
  cursor: pointer !important;
}

.media-player-container
  .volume-control
  input[type="range"]::-webkit-slider-track {
  background: hsl(var(--muted)) !important;
  height: 8px !important;
  border-radius: 4px !important;
  border: 1px solid hsl(var(--border)) !important;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1) !important;
}

.media-player-container
  .volume-control
  input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none !important;
  appearance: none !important;
  background: hsl(var(--primary)) !important;
  height: 18px !important;
  width: 18px !important;
  border-radius: 50% !important;
  border: 2px solid white !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2) !important;
  margin-top: -7px !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
}

.media-player-container .video-volume input[type="range"] {
  -webkit-appearance: none !important;
  appearance: none !important;
  background: transparent !important;
  height: 20px !important;
  width: 100% !important;
  min-width: 80px !important;
  cursor: pointer !important;
}

.media-player-container
  .video-volume
  input[type="range"]::-webkit-slider-track {
  background: rgba(255, 255, 255, 0.6) !important;
  height: 6px !important;
  border-radius: 3px !important;
  border: 1px solid rgba(255, 255, 255, 0.8) !important;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.2) !important;
}

.media-player-container
  .video-volume
  input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none !important;
  appearance: none !important;
  background: white !important;
  height: 16px !important;
  width: 16px !important;
  border-radius: 50% !important;
  border: 2px solid rgba(0, 0, 0, 0.1) !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.4) !important;
  margin-top: -7px !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
}

/* Keyboard shortcut styling */
kbd {
  display: inline-block;
  padding: 2px 6px;
  font-size: 11px;
  line-height: 1;
  color: hsl(var(--foreground));
  background-color: hsl(var(--muted));
  border: 1px solid hsl(var(--border));
  border-radius: 4px;
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas,
    "Liberation Mono", Menlo, monospace;
  font-weight: 500;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}
