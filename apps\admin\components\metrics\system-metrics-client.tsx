"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { formatNumber } from "@/lib/utils";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  XAxis,
  <PERSON>Axis,
  CartesianGrid,
  <PERSON>lt<PERSON>,
  Legend,
  ResponsiveContainer,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Pie<PERSON>hart,
  Pie,
  Cell,
} from "recharts";

// Types (matching the server component)
interface ErrorMetrics {
  totalRequests: number;
  failedRequests: number;
  errorRate: number;
  errorsByEndpoint: Array<{
    endpoint: string;
    errorCount: number;
  }>;
  errorsByTenant: Array<{
    tenantId: string;
    tenantName: string;
    errorCount: number;
  }>;
}

interface LatencyMetrics {
  averageLatency: number;
  p95Latency: number;
  p99Latency: number;
  latencyByEndpoint: Array<{
    endpoint: string;
    requestCount: number;
    averageLatency: number;
    p95Latency: number;
  }>;
  latencyOverTime: Array<{
    date: string;
    averageLatency: number;
  }>;
}

interface SystemResourceUsage {
  cpuUsage: number;
  memoryUsage: number;
  storageUsage: number;
  activeConnections: number;
}

interface SystemMetricsData {
  errorMetrics: ErrorMetrics;
  latencyMetrics: LatencyMetrics;
  systemResourceUsage: SystemResourceUsage;
  period: string;
}

interface SystemMetricsClientProps {
  data: SystemMetricsData;
}

export function SystemMetricsClient({ data }: SystemMetricsClientProps) {
  const { errorMetrics, latencyMetrics, systemResourceUsage } = data;

  // Colors for the charts
  const COLORS = ["#0088FE", "#00C49F", "#FFBB28", "#FF8042", "#8884d8"];

  // Fallback data for charts if API returns empty
  const fallbackErrorsByEndpoint = [
    { endpoint: "/api/example", errorCount: 0 },
    { endpoint: "/api/test", errorCount: 0 },
  ];

  const fallbackErrorsByTenant = [
    { tenantName: "No Data", errorCount: 1 },
  ];

  const fallbackLatencyByEndpoint = [
    { endpoint: "/api/example", averageLatency: 0, p95Latency: 0 },
    { endpoint: "/api/test", averageLatency: 0, p95Latency: 0 },
  ];

  return (
    <div className="space-y-6">
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">
              Total Requests
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatNumber(errorMetrics.totalRequests)}
            </div>
            <p className="text-xs text-muted-foreground">
              {errorMetrics.failedRequests || 0} failed requests
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Error Rate</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {errorMetrics.errorRate}%
            </div>
            <p className="text-xs text-muted-foreground">
              {errorMetrics.failedRequests || 0} failed requests
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">
              Average Latency
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {latencyMetrics.averageLatency} ms
            </div>
            <p className="text-xs text-muted-foreground">
              Across all endpoints
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">P95 Latency</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {latencyMetrics.p95Latency} ms
            </div>
            <p className="text-xs text-muted-foreground">95th percentile</p>
          </CardContent>
        </Card>
      </div>

      {/* System Resource Usage Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">CPU Usage</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {systemResourceUsage.cpuUsage}%
            </div>
            <p className="text-xs text-muted-foreground">Current usage</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Memory Usage</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {systemResourceUsage.memoryUsage}%
            </div>
            <p className="text-xs text-muted-foreground">Current usage</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Storage Usage</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {systemResourceUsage.storageUsage}%
            </div>
            <p className="text-xs text-muted-foreground">Disk usage</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Active Connections</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatNumber(systemResourceUsage.activeConnections)}
            </div>
            <p className="text-xs text-muted-foreground">Current connections</p>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Errors by Endpoint</CardTitle>
          </CardHeader>
          <CardContent className="h-[300px]">
            {errorMetrics.errorsByEndpoint.length === 0 ? (
              <div className="flex h-full items-center justify-center">
                <p className="text-muted-foreground">No error data available</p>
              </div>
            ) : (
              <ResponsiveContainer width="100%" height="100%">
                <BarChart
                  data={errorMetrics.errorsByEndpoint.length > 0 
                    ? errorMetrics.errorsByEndpoint 
                    : fallbackErrorsByEndpoint}
                  margin={{
                    top: 20,
                    right: 30,
                    left: 20,
                    bottom: 5,
                  }}
                  layout="vertical"
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis type="number" />
                  <YAxis dataKey="endpoint" type="category" width={150} />
                  <Tooltip
                    formatter={(value) => [`${value} errors`, "Count"]}
                  />
                  <Legend />
                  <Bar dataKey="errorCount" fill="#FF8042" name="Error Count" />
                </BarChart>
              </ResponsiveContainer>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Errors by Tenant</CardTitle>
          </CardHeader>
          <CardContent className="h-[300px]">
            {errorMetrics.errorsByTenant.length === 0 ? (
              <div className="flex h-full items-center justify-center">
                <p className="text-muted-foreground">No error data available</p>
              </div>
            ) : (
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={errorMetrics.errorsByTenant.length > 0 
                      ? errorMetrics.errorsByTenant 
                      : fallbackErrorsByTenant}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ tenantName, percent }) =>
                      `${tenantName}: ${(percent * 100).toFixed(0)}%`
                    }
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="errorCount"
                    nameKey="tenantName"
                  >
                    {(errorMetrics.errorsByTenant.length > 0 
                      ? errorMetrics.errorsByTenant 
                      : fallbackErrorsByTenant).map((_, index) => (
                      <Cell
                        key={`cell-${index}`}
                        fill={COLORS[index % COLORS.length]}
                      />
                    ))}
                  </Pie>
                  <Tooltip
                    formatter={(value, name, props) => [
                      `${value} errors`,
                      props.payload.tenantName,
                    ]}
                  />
                </PieChart>
              </ResponsiveContainer>
            )}
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Latency by Endpoint</CardTitle>
        </CardHeader>
        <CardContent className="h-[300px]">
          {latencyMetrics.latencyByEndpoint.length === 0 ? (
            <div className="flex h-full items-center justify-center">
              <p className="text-muted-foreground">No latency data available</p>
            </div>
          ) : (
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={latencyMetrics.latencyByEndpoint.length > 0 
                  ? latencyMetrics.latencyByEndpoint 
                  : fallbackLatencyByEndpoint}
                margin={{
                  top: 20,
                  right: 30,
                  left: 20,
                  bottom: 5,
                }}
                layout="vertical"
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis type="number" unit=" ms" />
                <YAxis dataKey="endpoint" type="category" width={150} />
                <Tooltip formatter={(value) => [`${value} ms`, "Latency"]} />
                <Legend />
                <Bar
                  dataKey="averageLatency"
                  fill="#0088FE"
                  name="Avg Latency"
                />
                <Bar dataKey="p95Latency" fill="#00C49F" name="P95 Latency" />
              </BarChart>
            </ResponsiveContainer>
          )}
        </CardContent>
      </Card>

      {/* Latency Over Time Chart */}
      {latencyMetrics.latencyOverTime.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Latency Over Time</CardTitle>
          </CardHeader>
          <CardContent className="h-[300px]">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart
                data={latencyMetrics.latencyOverTime}
                margin={{
                  top: 20,
                  right: 30,
                  left: 20,
                  bottom: 5,
                }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis unit=" ms" />
                <Tooltip formatter={(value) => [`${value} ms`, "Avg Latency"]} />
                <Legend />
                <Line
                  type="monotone"
                  dataKey="averageLatency"
                  stroke="#8884d8"
                  activeDot={{ r: 8 }}
                  name="Average Latency"
                />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
