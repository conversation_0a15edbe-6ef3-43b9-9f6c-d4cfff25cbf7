import { authOptions } from "@/lib/next-auth";
import dbPromise from "@/lib/shared-db";
import { getServerSession } from "next-auth/next";
import { NextResponse } from "next/server";
import { revalidatePath } from "next/cache";

export async function PUT(
  request: Request,
  { params }: { params: { id: string } }
) {
  const session = await getServerSession(authOptions);

  if (!session) {
    return NextResponse.json(
      { error: "Unauthorized" },
      { status: 401 }
    );
  }

  let body: any = null;

  try {
    const db = await dbPromise;
    body = await request.json();
    const { fileUploadLimitDuringChat } = body;

    console.log(`Processing file upload limit update request for tenant ${params.id}:`, { fileUploadLimitDuringChat });

    // Validate required fields
    if (fileUploadLimitDuringChat === undefined || fileUploadLimitDuringChat === null) {
      console.log("Validation failed: File upload limit is required");
      return NextResponse.json(
        { error: "File upload limit is required" },
        { status: 400 }
      );
    }

    // Validate file upload limit value
    if (typeof fileUploadLimitDuringChat !== 'number' || fileUploadLimitDuringChat < 1 || fileUploadLimitDuringChat > 10) {
      return NextResponse.json(
        { error: "File upload limit must be a number between 1 and 10" },
        { status: 400 }
      );
    }

    const { id: tenantId } = params;

    // Verify tenant exists
    const tenant = await db.tenant.findUnique({
      where: { id: tenantId },
      select: {
        id: true,
        name: true
      }
    });

    if (!tenant) {
      return NextResponse.json(
        { error: "Tenant not found" },
        { status: 404 }
      );
    }

    // Update the tenant with the new file upload limit
    const updatedTenant = await db.tenant.update({
      where: { id: tenantId },
      data: {
        fileUploadLimitDuringChat: fileUploadLimitDuringChat,
        updatedAt: new Date() // Update the timestamp
      },
      select: {
        id: true,
        name: true,
        fileUploadLimitDuringChat: true
      }
    });

    // Log the change for audit purposes
    console.log(`File upload limit updated for tenant ${tenantId}: ${fileUploadLimitDuringChat} files`);

    // ✅ Revalidate the tenant detail page to ensure fresh data
    revalidatePath(`/tenants/${tenantId}`);

    return NextResponse.json({
      message: "File upload limit updated successfully",
      tenant: {
        id: updatedTenant.id,
        name: updatedTenant.name,
        fileUploadLimitDuringChat: updatedTenant.fileUploadLimitDuringChat
      }
    });

  } catch (error) {
    console.error("Error updating file upload limit:", error);

    console.error("Error details:", {
      tenantId: params.id,
      fileUploadLimitDuringChat: body?.fileUploadLimitDuringChat,
      errorMessage: error instanceof Error ? error.message : 'Unknown error',
      errorStack: error instanceof Error ? error.stack : undefined
    });

    return NextResponse.json(
      {
        error: "Failed to update file upload limit",
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
