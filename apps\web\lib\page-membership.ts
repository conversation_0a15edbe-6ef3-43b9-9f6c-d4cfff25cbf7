import db from "./shared-db";

// Define enums locally until Prisma client is updated
export enum PageMemberRole {
  MEMBER = "MEMBER",
  EDITOR = "EDITOR",
  ADMIN = "ADMIN",
}

export enum PageMemberSource {
  MANUAL = "MANUAL",
  SHAREPOINT = "SHAREPOINT",
  WORKSPACE = "WORKSPACE",
}

export interface PageMembershipInfo {
  id: string;
  role: PageMemberRole;
  source: PageMemberSource;
  sharePointPermissionLevel?: string;
  lastSharePointCheck?: Date;
  user: {
    id: string;
    name: string;
    email: string;
  };
  createdAt: Date;
  updatedAt: Date;
}

export interface CreatePageMemberInput {
  pageId: string;
  userId: string;
  tenantId: string;
  role: PageMemberRole;
  source?: PageMemberSource;
  sharePointPermissionLevel?: string;
}

/**
 * Convert SharePoint permission level to PageMemberRole
 */
export function sharePointPermissionToRole(
  permissionLevel: string
): PageMemberRole {
  switch (permissionLevel.toLowerCase()) {
    case "owner":
    case "full control":
      return PageMemberRole.ADMIN;
    case "write":
    case "edit":
    case "contribute":
      return PageMemberRole.EDITOR;
    case "read":
    case "view":
    default:
      return PageMemberRole.MEMBER;
  }
}

/**
 * Check if user has page membership
 */
export async function getPageMembership(
  userId: string,
  pageId: string
): Promise<PageMembershipInfo | null> {
  try {
    const membership = await db.pageMember.findUnique({
      where: {
        pageId_userId: {
          pageId,
          userId,
        },
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    return membership;
  } catch (error) {
    console.error("Error getting page membership:", error);
    return null;
  }
}

/**
 * Create or update page membership based on SharePoint access
 */
export async function createOrUpdateSharePointMembership(
  userId: string,
  pageId: string,
  tenantId: string,
  sharePointPermissionLevel: string
): Promise<PageMembershipInfo | null> {
  try {
    const role = sharePointPermissionToRole(sharePointPermissionLevel);

    const membership = await db.pageMember.upsert({
      where: {
        pageId_userId: {
          pageId,
          userId,
        },
      },
      update: {
        role,
        sharePointPermissionLevel,
        lastSharePointCheck: new Date(),
        // Only update source if it's currently SHAREPOINT or if it's being created
        source: PageMemberSource.SHAREPOINT,
      },
      create: {
        pageId,
        userId,
        tenantId,
        role,
        source: PageMemberSource.SHAREPOINT,
        sharePointPermissionLevel,
        lastSharePointCheck: new Date(),
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    return membership;
  } catch (error) {
    console.error("Error creating/updating SharePoint membership:", error);
    return null;
  }
}

/**
 * Create manual page membership
 */
export async function createPageMembership(
  input: CreatePageMemberInput
): Promise<PageMembershipInfo | null> {
  try {
    const membership = await db.pageMember.create({
      data: {
        pageId: input.pageId,
        userId: input.userId,
        tenantId: input.tenantId,
        role: input.role,
        source: input.source || PageMemberSource.MANUAL,
        sharePointPermissionLevel: input.sharePointPermissionLevel,
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    return membership;
  } catch (error) {
    console.error("Error creating page membership:", error);
    return null;
  }
}

/**
 * Update page membership role
 */
export async function updatePageMembershipRole(
  userId: string,
  pageId: string,
  role: PageMemberRole
): Promise<PageMembershipInfo | null> {
  try {
    const membership = await db.pageMember.update({
      where: {
        pageId_userId: {
          pageId,
          userId,
        },
      },
      data: {
        role,
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    return membership;
  } catch (error) {
    console.error("Error updating page membership role:", error);
    return null;
  }
}

/**
 * Remove page membership
 */
export async function removePageMembership(
  userId: string,
  pageId: string
): Promise<boolean> {
  try {
    await db.pageMember.delete({
      where: {
        pageId_userId: {
          pageId,
          userId,
        },
      },
    });

    return true;
  } catch (error) {
    console.error("Error removing page membership:", error);
    return false;
  }
}

/**
 * Get all page members
 */
export async function getPageMembers(
  pageId: string
): Promise<PageMembershipInfo[]> {
  try {
    const members = await db.pageMember.findMany({
      where: {
        pageId,
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
      orderBy: [
        { role: "asc" }, // ADMIN first, then EDITOR, then MEMBER
        { createdAt: "asc" },
      ],
    });

    return members;
  } catch (error) {
    console.error("Error getting page members:", error);
    return [];
  }
}

/**
 * Check if user has specific permission on page
 */
export async function hasPagePermission(
  userId: string,
  pageId: string,
  requiredRole: PageMemberRole
): Promise<boolean> {
  try {
    const membership = await getPageMembership(userId, pageId);

    if (!membership) {
      return false;
    }

    // Define role hierarchy
    const roleHierarchy = {
      [PageMemberRole.MEMBER]: 1,
      [PageMemberRole.EDITOR]: 2,
      [PageMemberRole.ADMIN]: 3,
    };

    return roleHierarchy[membership.role] >= roleHierarchy[requiredRole];
  } catch (error) {
    console.error("Error checking page permission:", error);
    return false;
  }
}
