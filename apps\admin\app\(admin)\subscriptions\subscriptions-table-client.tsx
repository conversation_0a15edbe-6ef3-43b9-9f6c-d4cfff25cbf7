"use client";

import { useState, useEffect, useCallback, useTransition } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { formatCurrency, formatDate } from "@/lib/utils";
import { Eye, MoreHorizontal, Search, X } from "lucide-react";
import Link from "next/link";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  PieChart,
  Pie,
  Cell,
  ResponsiveContainer,
  Tooltip,
  Legend,
} from "recharts";

// Custom hook for debouncing values
function useDebounceLocal<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
}

// Types
interface Subscription {
  id: string;
  tenantName: string;
  tenantId: string;
  plan: string;
  startDate: string;
  endDate: string | null;
  status: string;
  billingInterval: string;
  amount: number;
  additionalUsers: number;
  additionalStorage: number;
}

interface PlanDistribution {
  name: string;
  value: number;
}

interface BillingDistribution {
  name: string;
  value: number;
}

interface SubscriptionsData {
  subscriptions: Subscription[];
  planDistribution: PlanDistribution[];
  billingDistribution: BillingDistribution[];
  allTenants: { id: string; name: string }[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  searchQuery: string;
}

interface SubscriptionsTableClientProps {
  data: SubscriptionsData;
}

const COLORS = ["#0088FE", "#00C49F", "#FFBB28", "#FF8042", "#8884D8"];

export function SubscriptionsTableClient({ data }: SubscriptionsTableClientProps) {
  const { subscriptions, planDistribution, billingDistribution, pagination, searchQuery, allTenants } = data;
  const router = useRouter();
  const searchParams = useSearchParams();
  const [isPending, startTransition] = useTransition();
  const [searchInput, setSearchInput] = useState(searchQuery);
  const [selectedTenantId, setSelectedTenantId] = useState<string | undefined>();

  // Debounce the search input to avoid excessive URL updates
  const debouncedSearchInput = useDebounceLocal(searchInput, 400);

  // Handle search with URL update
  const handleSearch = useCallback((query: string) => {
    const params = new URLSearchParams(searchParams?.toString());
    if (query.trim()) {
      params.set("search", query.trim());
    } else {
      params.delete("search");
    }
    params.set("page", "1"); // Reset to first page on search

    startTransition(() => {
      router.push(`/subscriptions?${params.toString()}`);
    });
  }, [searchParams, router]);

  // Effect to trigger search when debounced input changes
  useEffect(() => {
    // Only trigger search if the debounced value is different from current search query
    if (debouncedSearchInput !== searchQuery) {
      handleSearch(debouncedSearchInput);
    }
  }, [debouncedSearchInput, searchQuery, handleSearch]);

  useEffect(() => {
    const tenantIdFromURL = searchParams?.get("tenant") || "";
    setSelectedTenantId(tenantIdFromURL);
  }, [searchParams]);

  // Handle pagination
  const handlePageChange = (newPage: number) => {
    const params = new URLSearchParams(searchParams?.toString());
    params.set("page", newPage.toString());

    startTransition(() => {
      router.push(`/subscriptions?${params.toString()}`);
    });
  };

  // Handle clear search
  const handleClearSearch = () => {
    setSearchInput("");
    // The useEffect will handle the URL update when debouncedSearchInput changes
  };

  const handleRowClick = (subscriptionsId: string) => {
    startTransition(() => {
      router.push(`/subscriptions/${subscriptionsId}`);
    });
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold">Subscriptions</h1>
        <p className="text-muted-foreground">
          Manage all subscriptions in the system
        </p>
      </div>

      {/* Charts */}
      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Plan Distribution</CardTitle>
          </CardHeader>
          <CardContent className="h-[300px]">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={planDistribution}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {planDistribution.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip formatter={(value) => `${value} tenant(s)`} />
                <Legend />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Billing Interval</CardTitle>
          </CardHeader>
          <CardContent className="h-[300px]">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={billingDistribution}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {billingDistribution.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip formatter={(value) => `${value} tenant(s)`} />
                <Legend />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Subscriptions Table */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>
              All Subscriptions
              {searchQuery && (
                <span className="ml-2 text-sm font-normal text-muted-foreground">
                  (filtered by "{searchQuery}")
                </span>
              )}
            </CardTitle>
            <div className="relative flex w-full max-w-2xl gap-2 justify-end">
              <div className="relative w-[180px]">
                <Select
                  value={selectedTenantId}
                  onValueChange={(value) => {
                    setSelectedTenantId(value);
                    const params = new URLSearchParams(searchParams?.toString());

                    if (value) {
                      params.set("tenant", value);
                    } else {
                      params.delete("tenant");
                    }

                    params.set("page", "1");

                    startTransition(() => {
                      router.push(`/subscriptions?${params.toString()}`);
                    });
                  }}
                >
                  <SelectTrigger className={`w-full ${selectedTenantId ? 'pr-9' : ''}`}>
                    <SelectValue placeholder="Select tenant" />
                  </SelectTrigger>
                  <SelectContent>
                    {allTenants.map((tenant: any) => (
                      <SelectItem key={tenant.id} value={tenant.id}>
                        {tenant.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                {selectedTenantId ? (
                  <button
                    type="button"
                    onClick={() => {
                      setSelectedTenantId("");
                      const params = new URLSearchParams(searchParams?.toString());
                      params.delete("tenant");
                      params.set("page", "1");

                      startTransition(() => {
                        router.push(`/subscriptions?${params.toString()}`);
                      });
                    }}
                    className="absolute right-2 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground"
                  >
                    <X className="h-4 w-4" />
                  </button>
                ) : null}
              </div>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                <Input
                  placeholder="Search tenants, plans, status..."
                  value={searchInput}
                  onChange={(e) => setSearchInput(e.target.value)}
                  className="pl-9 pr-9"
                />
                {searchInput && (
                  <button
                    onClick={handleClearSearch}
                    className="absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground"
                    type="button"
                  >
                    <X className="h-4 w-4" />
                  </button>
                )}
              </div>
            </div>
          </div>
          {isPending && (
            <div className="mt-2 text-sm text-muted-foreground">
              Searching...
            </div>
          )}
        </CardHeader>
        <CardContent>
          {subscriptions.length === 0 ? (
            <div className="flex h-32 items-center justify-center">
              <p className="text-muted-foreground">
                {searchQuery ? "No subscriptions found matching your search." : "No subscriptions found."}
              </p>
            </div>
          ) : (
            <>
              <div className="overflow-x-auto">
                <table className="w-full text-sm">
                  <thead>
                    <tr className="border-b">
                      <th className="px-4 py-3 text-left font-medium">Tenant</th>
                      <th className="px-4 py-3 text-left font-medium">Plan</th>
                      <th className="px-4 py-3 text-left font-medium">Start Date</th>
                      <th className="px-4 py-3 text-left font-medium">End Date</th>
                      <th className="px-4 py-3 text-left font-medium">Status</th>
                      <th className="px-4 py-3 text-left font-medium">Billing</th>
                      <th className="px-4 py-3 text-left font-medium">Amount</th>
                      <th className="px-4 py-3 text-right font-medium">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {subscriptions.map((subscription) => (
                      <tr
                        key={subscription.id}
                        className="border-b cursor-pointer hover:bg-muted/50 transition-color"
                        tabIndex={0}
                        onClick={() => handleRowClick(subscription.id)}
                        onKeyDown={(e) => {
                          if (e.key === 'Enter' || e.key === ' ') {
                            e.preventDefault();
                            handleRowClick(subscription.id);
                          }
                        }}
                      >
                        <td className="px-4 py-3">{subscription.tenantName}</td>
                        <td className="px-4 py-3">{subscription.plan}</td>
                        <td className="px-4 py-3">{formatDate(subscription.startDate)}</td>
                        <td className="px-4 py-3">
                          {subscription.endDate ? formatDate(subscription.endDate) : "N/A"}
                        </td>
                        <td className="px-4 py-3">
                          <span
                            className={`rounded-full px-2 py-1 text-xs font-medium ${subscription.status === "active"
                              ? "bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100"
                              : subscription.status === "trial"
                                ? "bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100"
                                : "bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100"
                              }`}
                          >
                            {subscription.status.charAt(0).toUpperCase() + subscription.status.slice(1)}
                          </span>
                        </td>
                        <td className="px-4 py-3">
                          {subscription.billingInterval === "year" ? "Yearly" : "Monthly"}
                        </td>
                        <td className="px-4 py-3">{formatCurrency(subscription.amount)}</td>
                        <td className="px-4 py-3 text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button
                                variant="ghost"
                                size="icon"
                                onClick={(e) => e.stopPropagation()}
                              >
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem
                                onClick={(e) => {
                                  e.stopPropagation();
                                }}
                                className="text-red-600 focus:text-red-600 focus:bg-red-50"
                              >
                                Delete
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {/* Pagination */}
              {pagination.totalPages > 1 && (
                <div className="mt-4 flex items-center justify-between">
                  <div className="text-sm text-muted-foreground">
                    Showing {(pagination.page - 1) * pagination.limit + 1} to{" "}
                    {Math.min(pagination.page * pagination.limit, pagination.total)} of{" "}
                    {pagination.total} results
                  </div>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePageChange(pagination.page - 1)}
                      disabled={pagination.page <= 1 || isPending}
                    >
                      Previous
                    </Button>
                    <div className="flex gap-1">
                      {Array.from({ length: Math.min(5, pagination.totalPages) }, (_, i) => {
                        const pageNum = Math.max(1, pagination.page - 2) + i;
                        if (pageNum > pagination.totalPages) return null;

                        return (
                          <Button
                            key={pageNum}
                            variant={pageNum === pagination.page ? "default" : "outline"}
                            size="sm"
                            onClick={() => handlePageChange(pageNum)}
                            disabled={isPending}
                            className="w-8"
                          >
                            {pageNum}
                          </Button>
                        );
                      })}
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePageChange(pagination.page + 1)}
                      disabled={pagination.page >= pagination.totalPages || isPending}
                    >
                      Next
                    </Button>
                  </div>
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
