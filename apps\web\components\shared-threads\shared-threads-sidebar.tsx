"use client";

import React, { useState } from "react";
import {
  Search,
  RefreshCw,
  Users,
  Lock,
  Globe,
  MessageCircle,
  Filter,
  X,
} from "lucide-react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Skeleton } from "@/components/ui/skeleton";
import { cn } from "@/lib/utils";
import { useLanguage } from "@/lib/language-context";
import { useSharedThreads } from "./shared-threads-provider";
import { SharedThreadItem } from "./shared-thread-item";

interface SharedThreadsSidebarProps {
  className?: string;
}

export const SharedThreadsSidebar: React.FC<SharedThreadsSidebarProps> = ({
  className,
}) => {
  const { t } = useLanguage();
  const {
    privateThreads,
    publicThreads,
    unreadCounts,
    loading,
    error,
    searchTerm,
    setSearchTerm,
    refreshThreads,
  } = useSharedThreads();

  const [privateExpanded, setPrivateExpanded] = useState(true);
  const [publicExpanded, setPublicExpanded] = useState(true);
  const [filterType, setFilterType] = useState<"all" | "unread">("all");

  const handleRefresh = async () => {
    await refreshThreads();
  };

  const clearSearch = () => {
    setSearchTerm("");
  };

  // Filter threads based on filter type
  const filteredPrivateThreads = privateThreads.filter((thread) => {
    if (filterType === "unread") {
      return thread.hasNewActivity;
    }
    return true;
  });

  const filteredPublicThreads = publicThreads.filter((thread) => {
    if (filterType === "unread") {
      return thread.hasNewActivity;
    }
    return true;
  });

  const totalThreads = filteredPrivateThreads.length + filteredPublicThreads.length;

  return (
    <div className={cn("flex flex-col h-full bg-background", className)}>
      {/* Header */}
      <div className="p-4 border-b border-border">
        <div className="flex items-center justify-between mb-3">
          <h2 className="text-lg font-semibold flex items-center gap-2">
            <Users className="h-5 w-5" />
            Shared Threads
          </h2>
          <div className="flex items-center gap-1">
            {unreadCounts.totalUnreadCount > 0 && (
              <Badge variant="destructive" className="h-5 px-1.5 text-xs">
                {unreadCounts.totalUnreadCount}
              </Badge>
            )}
            <Button
              variant="ghost"
              size="sm"
              onClick={handleRefresh}
              disabled={loading}
              className="h-8 w-8 p-0"
            >
              <RefreshCw className={cn("h-4 w-4", loading && "animate-spin")} />
            </Button>
          </div>
        </div>

        {/* Search */}
        <div className="relative mb-3">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search threads..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-9 pr-9 h-9"
          />
          {searchTerm && (
            <Button
              variant="ghost"
              size="sm"
              onClick={clearSearch}
              className="absolute right-1 top-1/2 transform -translate-y-1/2 h-7 w-7 p-0"
            >
              <X className="h-3 w-3" />
            </Button>
          )}
        </div>

        {/* Filter */}
        <div className="flex items-center gap-2">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm" className="h-8">
                <Filter className="h-3 w-3 mr-1" />
                {filterType === "all" ? "All" : "Unread"}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="start">
              <DropdownMenuItem onClick={() => setFilterType("all")}>
                All Threads
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setFilterType("unread")}>
                Unread Only
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Content */}
      <ScrollArea className="flex-1">
        <div className="p-4 space-y-4">
          {error && (
            <div className="text-center text-sm text-destructive bg-destructive/10 p-3 rounded-lg">
              {error}
            </div>
          )}

          {loading && (
            <div className="space-y-3">
              {Array.from({ length: 3 }).map((_, i) => (
                <div key={i} className="space-y-2">
                  <Skeleton className="h-4 w-3/4" />
                  <Skeleton className="h-16 w-full" />
                </div>
              ))}
            </div>
          )}

          {!loading && (
            <>
              {/* Private Shared Threads */}
              {filteredPrivateThreads.length > 0 && (
                <Collapsible open={privateExpanded} onOpenChange={setPrivateExpanded}>
                  <CollapsibleTrigger className="flex items-center justify-between w-full p-2 hover:bg-muted rounded-lg">
                    <div className="flex items-center gap-2">
                      <Lock className="h-4 w-4 text-orange-600" />
                      <span className="font-medium text-sm">Private Shared</span>
                      {unreadCounts.privateUnreadCount > 0 && (
                        <Badge variant="destructive" className="h-4 px-1 text-xs">
                          {unreadCounts.privateUnreadCount}
                        </Badge>
                      )}
                    </div>
                    <span className="text-xs text-muted-foreground">
                      {filteredPrivateThreads.length}
                    </span>
                  </CollapsibleTrigger>
                  <CollapsibleContent className="space-y-2 mt-2">
                    {filteredPrivateThreads.map((thread) => (
                      <SharedThreadItem key={thread.id} thread={thread} />
                    ))}
                  </CollapsibleContent>
                </Collapsible>
              )}

              {/* Separator */}
              {filteredPrivateThreads.length > 0 && filteredPublicThreads.length > 0 && (
                <Separator />
              )}

              {/* Public Shared Threads */}
              {filteredPublicThreads.length > 0 && (
                <Collapsible open={publicExpanded} onOpenChange={setPublicExpanded}>
                  <CollapsibleTrigger className="flex items-center justify-between w-full p-2 hover:bg-muted rounded-lg">
                    <div className="flex items-center gap-2">
                      <Globe className="h-4 w-4 text-green-600" />
                      <span className="font-medium text-sm">Public Shared</span>
                      {unreadCounts.publicUnreadCount > 0 && (
                        <Badge variant="destructive" className="h-4 px-1 text-xs">
                          {unreadCounts.publicUnreadCount}
                        </Badge>
                      )}
                    </div>
                    <span className="text-xs text-muted-foreground">
                      {filteredPublicThreads.length}
                    </span>
                  </CollapsibleTrigger>
                  <CollapsibleContent className="space-y-2 mt-2">
                    {filteredPublicThreads.map((thread) => (
                      <SharedThreadItem key={thread.id} thread={thread} />
                    ))}
                  </CollapsibleContent>
                </Collapsible>
              )}

              {/* Empty State */}
              {!loading && totalThreads === 0 && (
                <div className="text-center py-8">
                  <MessageCircle className="h-12 w-12 text-muted-foreground mx-auto mb-3" />
                  <h3 className="font-medium text-sm mb-1">No shared threads</h3>
                  <p className="text-xs text-muted-foreground">
                    {searchTerm
                      ? "No threads match your search"
                      : filterType === "unread"
                      ? "No unread threads"
                      : "Shared threads will appear here"}
                  </p>
                </div>
              )}
            </>
          )}
        </div>
      </ScrollArea>
    </div>
  );
};
