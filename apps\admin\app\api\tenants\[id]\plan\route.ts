import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/next-auth";
import dbPromise from "@/lib/shared-db";

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  let body: any = null;

  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    const db = await dbPromise;
    const tenantId = params.id;

    // Parse request body to get the desired plan type
    body = await request.json();
    const { planType } = body;

    console.log(`Processing tenant plan update request for tenant ${tenantId}:`, { planType });

    // Validate payload - planType should be one of the valid plan types
    const validPlanTypes = ["STARTER", "TEAM", "PRO", "ENTERPRISE", "SOLO"];
    if (!validPlanTypes.includes(planType)) {
      return NextResponse.json(
        { error: `Plan type must be one of: ${validPlanTypes.join(", ")}` },
        { status: 400 }
      );
    }

    // Verify tenant exists
    const tenant = await db.tenant.findUnique({
      where: { id: tenantId },
      select: {
        id: true,
        name: true,
        Subscription: {
          where: { isActive: true },
          include: { plan: true }
        }
      }
    });

    if (!tenant) {
      return NextResponse.json(
        { error: "Tenant not found" },
        { status: 404 }
      );
    }

    // Find the plan by type
    const newPlan = await db.plan.findFirst({
      where: { 
        type: planType,
        isActive: true 
      }
    });

    if (!newPlan) {
      return NextResponse.json(
        { error: `No active plan found for type: ${planType}` },
        { status: 404 }
      );
    }

    const currentSubscription = tenant.Subscription[0];
    const currentPlanName = currentSubscription?.plan?.name || 'No Plan';

    // If tenant has an active subscription, deactivate it first
    if (currentSubscription) {
      await db.subscription.update({
        where: { id: currentSubscription.id },
        data: { isActive: false }
      });
    }

    // Create new subscription with the new plan
    const newSubscription = await db.subscription.create({
      data: {
        tenantId: tenantId,
        planId: newPlan.id,
        isActive: true,
        startDate: new Date(),
        additionalStorageGB: 0,
        additionalUsers: 0
      },
      include: {
        plan: true
      }
    });

    // Log the plan change for audit purposes
    console.log(`Plan changed for tenant ${tenantId}: ${currentPlanName} -> ${newPlan.name}`);

    return NextResponse.json({
      message: `Plan changed to ${newPlan.name} successfully`,
      subscription: {
        id: newSubscription.id,
        planName: newSubscription.plan.name,
        planType: newSubscription.plan.type,
        startDate: newSubscription.startDate,
        isActive: newSubscription.isActive
      }
    });

  } catch (error) {
    console.error("Error updating tenant plan:", error);
    
    return NextResponse.json(
      { 
        error: "Failed to update tenant plan",
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
