"""
Chart and Diagram Generation Instructions for AI Responses
"""

CHART_GENERATION_INSTRUCTIONS = {
    "en": """
## Chart and Diagram Generation Capabilities

You have the ability to generate interactive charts, graphs, and diagrams in your responses. When appropriate, use these visualization capabilities to enhance your answers.

### Supported Chart Types:

1. **Line Charts** - For trends, time series, and continuous data
2. **Bar Charts** - For comparisons and categorical data
3. **Area Charts** - For showing volume and filled trends
4. **Pie Charts** - For proportions and percentages
5. **Scatter Plots** - For correlation analysis
6. **Mermaid Diagrams** - For flowcharts, processes, and relationships

### Chart Syntax:

#### Line Chart Example:
```chart:line
{
  "data": [
    {"month": "Jan", "sales": 12000, "profit": 2400},
    {"month": "Feb", "sales": 15000, "profit": 3000},
    {"month": "Mar", "sales": 18000, "profit": 3600}
  ],
  "config": {
    "title": "Monthly Performance",
    "xKey": "month",
    "showLegend": true,
    "height": 350
  }
}
```

#### Bar Chart with CSV Data:
```chart:bar
department,revenue,expenses
Sales,450000,120000
Marketing,280000,95000
Engineering,520000,180000
```

#### Pie Chart:
```chart:pie
{
  "data": [
    {"category": "Product A", "value": 35},
    {"category": "Product B", "value": 28},
    {"category": "Product C", "value": 18},
    {"category": "Others", "value": 19}
  ],
  "config": {
    "title": "Market Share Distribution"
  }
}
```

#### Mermaid Flowchart:
```mermaid
graph TD
    A[Start Process] --> B{Decision Point}
    B -->|Yes| C[Action 1]
    B -->|No| D[Action 2]
    C --> E[End]
    D --> E
```

#### Mermaid Sequence Diagram:
```mermaid
sequenceDiagram
    participant User
    participant System
    participant Database
    
    User->>System: Request Data
    System->>Database: Query
    Database-->>System: Results
    System-->>User: Response
```

### When to Use Charts:

- **Data Analysis**: When presenting numerical data, statistics, or metrics
- **Comparisons**: When comparing different categories, time periods, or values
- **Trends**: When showing changes over time or patterns
- **Processes**: When explaining workflows, procedures, or decision trees
- **Relationships**: When showing connections between entities or concepts
- **Proportions**: When displaying parts of a whole or percentages

### Guidelines:

1. **Always provide context** before and after charts
2. **Use appropriate chart types** for the data being presented
3. **Include meaningful titles** and labels
4. **Keep data clear and readable**
5. **Explain insights** derived from the visualizations
6. **Use charts to supplement, not replace** textual explanations

### Data Formats Supported:

- **JSON format** with data array and optional config
- **CSV-like format** with headers and comma-separated values
- **Simple arrays** for basic datasets

Remember: Charts should enhance understanding and make complex data more accessible to users.
""",

    "de": """
## Diagramm- und Chart-Generierungsfähigkeiten

Sie haben die Möglichkeit, interaktive Diagramme, Grafiken und Schaubilder in Ihren Antworten zu erstellen. Nutzen Sie diese Visualisierungsfähigkeiten, wenn angemessen, um Ihre Antworten zu verbessern.

### Unterstützte Diagrammtypen:

1. **Liniendiagramme** - Für Trends, Zeitreihen und kontinuierliche Daten
2. **Balkendiagramme** - Für Vergleiche und kategorische Daten
3. **Flächendiagramme** - Für Volumen und gefüllte Trends
4. **Kreisdiagramme** - Für Proportionen und Prozentsätze
5. **Streudiagramme** - Für Korrelationsanalysen
6. **Mermaid-Diagramme** - Für Flussdiagramme, Prozesse und Beziehungen

### Diagramm-Syntax:

#### Liniendiagramm Beispiel:
```chart:line
{
  "data": [
    {"monat": "Jan", "umsatz": 12000, "gewinn": 2400},
    {"monat": "Feb", "umsatz": 15000, "gewinn": 3000},
    {"monat": "Mär", "umsatz": 18000, "gewinn": 3600}
  ],
  "config": {
    "title": "Monatliche Leistung",
    "xKey": "monat",
    "showLegend": true,
    "height": 350
  }
}
```

#### Balkendiagramm mit CSV-Daten:
```chart:bar
abteilung,umsatz,ausgaben
Vertrieb,450000,120000
Marketing,280000,95000
Entwicklung,520000,180000
```

#### Mermaid Flussdiagramm:
```mermaid
graph TD
    A[Prozess starten] --> B{Entscheidungspunkt}
    B -->|Ja| C[Aktion 1]
    B -->|Nein| D[Aktion 2]
    C --> E[Ende]
    D --> E
```

### Wann Diagramme verwenden:

- **Datenanalyse**: Bei der Präsentation numerischer Daten, Statistiken oder Metriken
- **Vergleiche**: Beim Vergleich verschiedener Kategorien, Zeiträume oder Werte
- **Trends**: Bei der Darstellung von Veränderungen über Zeit oder Mustern
- **Prozesse**: Bei der Erklärung von Arbeitsabläufen oder Entscheidungsbäumen
- **Beziehungen**: Bei der Darstellung von Verbindungen zwischen Entitäten
- **Proportionen**: Bei der Anzeige von Teilen eines Ganzen oder Prozentsätzen

### Richtlinien:

1. **Immer Kontext bereitstellen** vor und nach Diagrammen
2. **Angemessene Diagrammtypen verwenden** für die präsentierten Daten
3. **Aussagekräftige Titel** und Beschriftungen einschließen
4. **Daten klar und lesbar halten**
5. **Erkenntnisse erklären**, die aus den Visualisierungen abgeleitet werden
6. **Diagramme zur Ergänzung verwenden**, nicht als Ersatz für textuelle Erklärungen

Denken Sie daran: Diagramme sollten das Verständnis verbessern und komplexe Daten für Benutzer zugänglicher machen.
"""
}

def get_chart_instructions(language: str = "en") -> str:
    """Get chart generation instructions for the specified language."""
    return CHART_GENERATION_INSTRUCTIONS.get(language, CHART_GENERATION_INSTRUCTIONS["en"])

# Chart suggestion patterns for different query types
CHART_SUGGESTION_PATTERNS = {
    "trends": ["trend", "over time", "timeline", "progress", "growth", "decline", "change"],
    "comparisons": ["compare", "versus", "vs", "difference", "better", "worse", "ranking"],
    "distributions": ["distribution", "breakdown", "share", "percentage", "proportion", "split"],
    "correlations": ["relationship", "correlation", "connection", "impact", "effect", "influence"],
    "processes": ["process", "workflow", "steps", "procedure", "flow", "sequence", "how to"],
    "hierarchies": ["structure", "organization", "hierarchy", "tree", "levels", "categories"]
}

def suggest_chart_type(query: str, data_context: str = "") -> str:
    """
    Suggest appropriate chart type based on query content and data context.
    
    Args:
        query: User's question or request
        data_context: Context about available data
        
    Returns:
        Suggested chart type or empty string if no clear suggestion
    """
    query_lower = query.lower()
    
    # Check for time-based data
    if any(word in query_lower for word in ["time", "month", "year", "day", "week", "timeline", "trend"]):
        return "line"
    
    # Check for comparison keywords
    if any(word in query_lower for word in ["compare", "comparison", "versus", "vs", "difference"]):
        return "bar"
    
    # Check for proportion/percentage keywords
    if any(word in query_lower for word in ["share", "percentage", "proportion", "distribution", "breakdown"]):
        return "pie"
    
    # Check for process/workflow keywords
    if any(word in query_lower for word in ["process", "workflow", "steps", "procedure", "flow"]):
        return "mermaid"
    
    # Check for correlation keywords
    if any(word in query_lower for word in ["relationship", "correlation", "scatter", "correlation"]):
        return "scatter"
    
    return ""

def should_generate_chart(query: str, data_available: bool = False) -> bool:
    """
    Determine if a chart should be generated based on the query and available data.
    
    Args:
        query: User's question or request
        data_available: Whether numerical data is available
        
    Returns:
        True if a chart would be beneficial
    """
    query_lower = query.lower()
    
    # Chart-friendly keywords
    chart_keywords = [
        "chart", "graph", "plot", "visualize", "show", "display",
        "trend", "compare", "analysis", "data", "statistics",
        "breakdown", "distribution", "performance", "metrics"
    ]
    
    # Process-friendly keywords for diagrams
    process_keywords = [
        "process", "workflow", "steps", "procedure", "flow",
        "diagram", "flowchart", "sequence", "how to"
    ]
    
    return (
        any(keyword in query_lower for keyword in chart_keywords) or
        any(keyword in query_lower for keyword in process_keywords) or
        data_available
    )
