"use client";

import React, { useState } from "react";
import { UseFormReturn } from "react-hook-form";
import { Palette, X, <PERSON>, <PERSON> } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ThemeConfigFormValues } from "../wrapper-screens/organization-settings/theme-config/theme-config-page";
import { QuickColorPresets } from "./quick-color-presets";

interface ThemeColorCustomizationProps {
  form: UseFormReturn<ThemeConfigFormValues>;
  previewMode: boolean;
  applyPreviewColors: (themeData: Partial<ThemeConfigFormValues>) => void;
  isAdmin: boolean;
}

const ThemeColorCustomization: React.FC<ThemeColorCustomizationProps> = ({
  form,
  previewMode,
  applyPreviewColors,
  isAdmin,
}) => {
  const [activeTab, setActiveTab] = useState<"light" | "dark">("light");

  // Color field configurations for each theme mode
  const colorFields = {
    light: [
      { name: "Primary", field: "lightPrimaryColor" as keyof ThemeConfigFormValues, defaultColor: "#3b82f6" },
      { name: "Secondary", field: "lightSecondaryColor" as keyof ThemeConfigFormValues, defaultColor: "#64748b" },
      { name: "Accent", field: "lightAccentColor" as keyof ThemeConfigFormValues, defaultColor: "#8b5cf6" },
      { name: "Navigation Background", field: "lightNavigationBackgroundColor" as keyof ThemeConfigFormValues, defaultColor: "#f8fafc" },
      { name: "Content Background", field: "lightContentBackgroundColor" as keyof ThemeConfigFormValues, defaultColor: "#ffffff" },
      { name: "Text", field: "lightForegroundColor" as keyof ThemeConfigFormValues, defaultColor: "#0f172a" },
    ],
    dark: [
      { name: "Primary", field: "darkPrimaryColor" as keyof ThemeConfigFormValues, defaultColor: "#60a5fa" },
      { name: "Secondary", field: "darkSecondaryColor" as keyof ThemeConfigFormValues, defaultColor: "#94a3b8" },
      { name: "Accent", field: "darkAccentColor" as keyof ThemeConfigFormValues, defaultColor: "#a855f7" },
      { name: "Navigation Background", field: "darkNavigationBackgroundColor" as keyof ThemeConfigFormValues, defaultColor: "#0f172a" },
      { name: "Content Background", field: "darkContentBackgroundColor" as keyof ThemeConfigFormValues, defaultColor: "#1e293b" },
      { name: "Text", field: "darkForegroundColor" as keyof ThemeConfigFormValues, defaultColor: "#f1f5f9" },
    ],
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Palette className="h-5 w-5" />
          Color Customization
        </CardTitle>
        <CardDescription>
          Define your brand colors for both light and dark themes. These colors will be applied across the entire application.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as "light" | "dark")}>
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger
              value="light"
              className="flex items-center gap-2 border border-transparent data-[state=active]:border-primary rounded-md px-4 py-2"
            >
              <Sun className="h-4 w-4" />
              Light Theme
            </TabsTrigger>

            <TabsTrigger
              value="dark"
              className="flex items-center gap-2 border border-transparent data-[state=active]:border-primary rounded-md px-4 py-2"
            >
              <Moon className="h-4 w-4" />
              Dark Theme
            </TabsTrigger>
          </TabsList>

          <TabsContent value="light" className="space-y-6 mt-6">
            <div className="space-y-6">
              {/* Color Preview Section */}
              <div className="p-4 border rounded-lg bg-muted/30">
                <h4 className="text-sm font-medium mb-3">Light Theme Color Preview</h4>
                <div className="flex flex-wrap gap-3">
                  {colorFields.light.map((color) => (
                    <div key={color.field} className="flex items-center gap-2">
                      <div
                        className="w-6 h-6 rounded border border-gray-300"
                        style={{
                          backgroundColor: form.watch(color.field) || color.defaultColor,
                        }}
                      />
                      <span className="text-xs text-muted-foreground">{color.name}</span>
                    </div>
                  ))}
                </div>
              </div>

              {/* Color Input Fields */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {colorFields.light.map((color) => (
                  <FormField
                    key={color.field}
                    control={form.control}
                    name={color.field}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="flex items-center gap-2">
                          <div
                            className="w-3 h-3 rounded-full"
                            style={{ backgroundColor: field.value || color.defaultColor }}
                          ></div>
                          {color.name} Color
                        </FormLabel>
                        <FormDescription className="text-xs">
                          {color.name} color used in the light theme.
                        </FormDescription>
                        <FormControl>
                          <div className="flex items-center space-x-2">
                            <input
                              type="color"
                              className="w-14 h-10 p-1 border rounded cursor-pointer"
                              value={field.value || ""}
                              disabled={!isAdmin}
                              onChange={(e) => {
                                field.onChange(e.target.value);
                                if (previewMode) {
                                  applyPreviewColors({
                                    ...form.getValues(),
                                    [color.field]: e.target.value,
                                  });
                                }
                              }}
                            />
                            <Input
                              placeholder={color.defaultColor}
                              value={field.value || ""}
                              disabled={!isAdmin}
                              onChange={(e) => {
                                field.onChange(e.target.value);
                                if (previewMode) {
                                  applyPreviewColors({
                                    ...form.getValues(),
                                    [color.field]: e.target.value,
                                  });
                                }
                              }}
                              className="font-mono text-sm"
                            />
                            {field.value && (
                              <Button
                                type="button"
                                variant="ghost"
                                size="sm"
                                onClick={() => {
                                  field.onChange("");
                                  if (previewMode) {
                                    applyPreviewColors({
                                      ...form.getValues(),
                                      [color.field]: "",
                                    });
                                  }
                                }}
                                disabled={!isAdmin}
                                className="h-8 w-8 p-0"
                              >
                                <X className="h-3 w-3" />
                              </Button>
                            )}
                          </div>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                ))}
              </div>

              {/* Unified Quick Color Presets */}
              <QuickColorPresets
                form={form}
                previewMode={previewMode}
                applyPreviewColors={applyPreviewColors}
                isAdmin={isAdmin}
              />

            </div>
          </TabsContent>
          <TabsContent value="dark" className="space-y-6 mt-6">
            <div className="space-y-6">
              {/* Color Preview Section */}
              <div className="p-4 border rounded-lg bg-muted/30">
                <h4 className="text-sm font-medium mb-3">Dark Theme Color Preview</h4>
                <div className="flex flex-wrap gap-3">
                  {colorFields.light.map((color) => (
                    <div key={color.field} className="flex items-center gap-2">
                      <div
                        className="w-6 h-6 rounded border border-gray-300"
                        style={{
                          backgroundColor: form.watch(color.field) || color.defaultColor,
                        }}
                      />
                      <span className="text-xs text-muted-foreground">{color.name}</span>
                    </div>
                  ))}
                </div>
              </div>

              {/* Color Input Fields */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {colorFields.dark.map((color) => (
                  <FormField
                    key={color.field}
                    control={form.control}
                    name={color.field}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="flex items-center gap-2">
                          <div
                            className="w-3 h-3 rounded-full"
                            style={{ backgroundColor: field.value || color.defaultColor }}
                          ></div>
                          {color.name} Color
                        </FormLabel>
                        <FormDescription className="text-xs">
                          {color.name} color used in the dark theme.
                        </FormDescription>
                        <FormControl>
                          <div className="flex items-center space-x-2">
                            <input
                              type="color"
                              className="w-14 h-10 p-1 border rounded cursor-pointer"
                              value={field.value || ""}
                              disabled={!isAdmin}
                              onChange={(e) => {
                                field.onChange(e.target.value);
                                if (previewMode) {
                                  applyPreviewColors({
                                    ...form.getValues(),
                                    [color.field]: e.target.value,
                                  });
                                }
                              }}
                            />
                            <Input
                              placeholder={color.defaultColor}
                              value={field.value || ""}
                              disabled={!isAdmin}
                              onChange={(e) => {
                                field.onChange(e.target.value);
                                if (previewMode) {
                                  applyPreviewColors({
                                    ...form.getValues(),
                                    [color.field]: e.target.value,
                                  });
                                }
                              }}
                              className="font-mono text-sm"
                            />
                            {field.value && (
                              <Button
                                type="button"
                                variant="ghost"
                                size="sm"
                                onClick={() => {
                                  field.onChange("");
                                  if (previewMode) {
                                    applyPreviewColors({
                                      ...form.getValues(),
                                      [color.field]: "",
                                    });
                                  }
                                }}
                                disabled={!isAdmin}
                                className="h-8 w-8 p-0"
                              >
                                <X className="h-3 w-3" />
                              </Button>
                            )}
                          </div>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                ))}

              </div>

              {/* Unified Quick Color Presets */}
              <QuickColorPresets
                form={form}
                previewMode={previewMode}
                applyPreviewColors={applyPreviewColors}
                isAdmin={isAdmin}
              />

            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default ThemeColorCustomization;
