import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Copy, Check, Download } from "lucide-react";
import { useTranslatedToast } from "@/hooks/use-translated-toast";

/**
 * CodeBlock component with copy functionality
 */
export const CodeBlock: React.FC<{
  children: React.ReactNode;
  className?: string;
  inline?: boolean;
}> = ({ children, className, inline }) => {
  const [copied, setCopied] = React.useState(false);
  const toast = useTranslatedToast();

  const text = String(children).replace(/\n$/, "");

  // Detect downloadable content patterns
  const isDownloadableContent = React.useMemo(() => {
    // Check for markdown link format: [Download filename](data:...)
    const markdownLinkPattern = /\[Download [^[\]]+\]\(data:[^)]+\)/;
    // Check for data URLs anywhere in content
    const dataUrlPattern = /data:[^;\s]+;[^,\s]+,[A-Za-z0-9+/=]+/;
    // Check for download text followed by data URL (more flexible)
    const flexiblePattern =
      /\[Download [^[\]]+\][\s\S]*?data:[^;\s]+;[^,\s]+,[A-Za-z0-9+/=]+/;
    // Check for vCard content
    const vcardPattern = /BEGIN:VCARD[\s\S]*END:VCARD/;
    // Check for other file-like content
    const fileContentPattern = /\.(vcf|csv|json|xml|txt|md)\b/i;

    return (
      markdownLinkPattern.test(text) ||
      (text.includes("[Download") && dataUrlPattern.test(text)) ||
      flexiblePattern.test(text) ||
      vcardPattern.test(text) ||
      fileContentPattern.test(text)
    );
  }, [text]);

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(text);
      setCopied(true);
      toast.success("toast.contentCopied");
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      toast.error("toast.copyFailed");
    }
  };

  const handleDownload = () => {
    try {
      // Try standard markdown link format first: [Download filename](data:...)
      const markdownLinkMatch = text.match(
        /\[Download ([^[\]]+)\]\((data:[^)]+)\)/
      );

      if (markdownLinkMatch) {
        const [, filename, dataUrl] = markdownLinkMatch;
        const link = document.createElement("a");
        link.href = dataUrl;
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        toast.success("toast.downloadedFile", { filename });
        return;
      }

      // Try flexible pattern: [Download filename] followed by data URL anywhere
      const filenameMatch = text.match(/\[Download ([^[\]]+)\]/);
      const dataUrlMatch = text.match(/(data:[^;\s]+;[^,\s]+,[A-Za-z0-9+/=]+)/);

      if (filenameMatch && dataUrlMatch) {
        const filename = filenameMatch[1];
        const dataUrl = dataUrlMatch[1];
        const link = document.createElement("a");
        link.href = dataUrl;
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        toast.success("toast.downloadedFile", { filename });
        return;
      }

      if (text.includes("BEGIN:VCARD")) {
        // Handle raw vCard content
        const blob = new Blob([text], { type: "text/vcard" });
        const url = URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = url;
        link.download = "contacts.vcf";
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
        toast.success("toast.downloadedContacts");
      } else {
        // Generic file download
        const blob = new Blob([text], { type: "text/plain" });
        const url = URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = url;
        link.download = "file.txt";
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
        toast.success("toast.downloadedGenericFile");
      }
    } catch (err) {
      toast.error("toast.downloadFailed");
    }
  };

  // For inline code, don't show copy button
  if (inline) {
    return (
      <code className="bg-muted px-1 py-0.5 rounded text-sm font-mono">
        {children}
      </code>
    );
  }

  // For code blocks, show copy button and download button if applicable
  return (
    <div className="relative group">
      <pre className="bg-muted p-4 rounded-lg overflow-x-auto border">
        <code className={className}>{children}</code>
      </pre>
      <div className="absolute top-2 right-2 flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
        {/* Download button for downloadable content */}
        {isDownloadableContent && (
          <Button
            variant="ghost"
            size="icon"
            className="h-8 w-8 bg-background/80 hover:bg-background"
            onClick={handleDownload}
            title="Download file"
          >
            <Download className="h-4 w-4" />
          </Button>
        )}

        {/* Copy button */}
        <Button
          variant="ghost"
          size="icon"
          className="h-8 w-8 bg-background/80 hover:bg-background"
          onClick={copyToClipboard}
          title={isDownloadableContent ? "Copy content" : "Copy code"}
        >
          {copied ? (
            <Check className="h-4 w-4 text-green-600" />
          ) : (
            <Copy className="h-4 w-4" />
          )}
        </Button>
      </div>
    </div>
  );
};
