import { NextResponse } from "next/server";
import db from "@/lib/shared-db";
import crypto from "crypto";
import Mailer from "@/lib/email/send-email";
import {
  getLanguageFromRequest,
  createServerTranslator,
} from "@/lib/server-translations";

export async function POST(request: Request) {
  const language = getLanguageFromRequest(request);
  const t = createServerTranslator(language);
  try {
    const body = await request.json();
    const { email } = body;

    if (!email) {
      return NextResponse.json(
        { error: t("api.errors.emailRequired") },
        { status: 400 }
      );
    }

    const emailHash = crypto.createHash("sha256").update(email).digest("hex");
    // Check if user with given email exists
    const user = await db.user.findUnique({
      where: { emailHash },
    });

    // Don't reveal if user exists or not for security reasons
    if (!user) {
      return NextResponse.json(
        {
          message: t("api.success.passwordResetEmailSent"),
        },
        { status: 200 }
      );
    }

    // Generate reset token
    const token = crypto.randomBytes(32).toString("hex");
    const expires = new Date();
    expires.setHours(expires.getHours() + 2); // Token expires in 2 hours

    // In development mode, use a simpler token for easier testing
    const finalToken =
      process.env.NODE_ENV === "development"
        ? `reset-${Math.floor(100000 + Math.random() * 900000)}`
        : token;

    // Store token in database
    await db.verificationToken.create({
      data: {
        identifier: email,
        token: finalToken,
        expires,
      },
    });

    if (process.env.NODE_ENV === "development") {
      console.log("Development reset token created:", finalToken);
    }

    // Build reset URL
    const baseUrl =
      process.env.NEXT_PUBLIC_API_BASE_URL ||
      (process.env.NODE_ENV === "development" ? "http://localhost:3000" : "");
    const resetUrl = `${baseUrl}/reset-password/${finalToken}`;

    // Create email template (simplified for this example)
    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2>Reset Your Password</h2>
        <p>We received a request to reset your password. If you didn't make this request, you can ignore this email.</p>
        <p>To reset your password, click the link below:</p>
        <p>
          <a href="${resetUrl}" style="display: inline-block; padding: 10px 20px; background-color: #4f46e5; color: white; text-decoration: none; border-radius: 5px;">
            Reset Password
          </a>
        </p>
        <p>Or copy and paste this link in your browser:</p>
        <p>${resetUrl}</p>
        <p>This link will expire in 2 hours.</p>
      </div>
    `;

    // Send email (with fallback for development)
    try {
      await Mailer.send({
        from:
          process.env.NEXT_PUBLIC_SEND_EMAIL_FROM ||
          "<EMAIL>",
        to: email,
        subject: t("api.email.subjects.resetPassword"),
        html,
      });
      console.log("Password reset email would be sent to:", email);
      console.log("Password reset link:", resetUrl);
    } catch (emailError) {
      // In development, log the error but don't fail
      console.error("Failed to send password reset email:", emailError);

      if (process.env.NODE_ENV === "production") {
        return NextResponse.json(
          { error: t("api.errors.failedToSendPasswordResetEmail") },
          { status: 500 }
        );
      }

      // In development, continue despite email error
      console.log("Continuing despite email error in development environment");
    }

    return NextResponse.json({
      message: t("api.success.passwordResetEmailSent"),
    });
  } catch (error) {
    console.error("Password reset request error:", error);
    return NextResponse.json(
      { error: t("toast.somethingWentWrong") },
      { status: 500 }
    );
  }
}
