import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/next-auth";
import db from "@/lib/shared-db";

// Helper function to create a consistent user identifier
function createUserKey(session: any): string {
  // For authenticated users, use userId or email
  if (session.user?.id) {
    return `user_${session.user.id}`;
  }
  if (session.user?.email) {
    return `email_${session.user.email}`;
  }
  
  // For anonymous users, use IP address
  if (session.ipAddress) {
    return `ip_${session.ipAddress}`;
  }
  
  // Fallback to session ID
  return `session_${session.id}`;
}

// Helper function to create a display name for the user
function createUserDisplayName(session: any, userKey: string): string {
  if (session.user?.name) {
    return session.user.name;
  }
  if (session.user?.email) {
    return session.user.email;
  }
  
  // For anonymous users, create a friendly name
  if (userKey.startsWith("ip_")) {
    const location =
      session.country && session.city
        ? `${session.city}, ${session.country}`
        : session.country || "Unknown Location";
    const device = session.deviceType || "Unknown Device";
    return `Anonymous User (${location}, ${device})`;
  }
  
  return "Anonymous User";
}

export async function GET(request: NextRequest) {
  try {
    const session: any = await getServerSession(authOptions);
    if (!session?.userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const tenantId = searchParams.get("tenantId");
    const timeRange = searchParams.get("timeRange") || "7d";
    const selectedChatbot = searchParams.get("chatbot") || "all";
    const searchTerm = searchParams.get("search") || "";

    if (!tenantId) {
      return NextResponse.json(
        { error: "Tenant ID is required" },
        { status: 400 }
      );
    }

    // Calculate date range
    const now = new Date();
    let startDate: Date;
    
    switch (timeRange) {
      case "1d":
        startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
        break;
      case "7d":
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case "30d":
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      case "90d":
        startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
        break;
      default:
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    }

    // Build query filters
    const whereClause: any = {
      chatbot: {
        tenantId: tenantId,
        userId: session.userId,
      },
      startedAt: {
        gte: startDate,
      },
    };

    if (selectedChatbot !== "all") {
      whereClause.chatbotId = selectedChatbot;
    }

    // Get all sessions
    const sessions = await db.chatbotSession.findMany({
      where: whereClause,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            emailHash: true,
          },
        },
        chatbot: {
          select: {
            id: true,
            name: true,
          },
        },
      },
      orderBy: {
        startedAt: "desc",
      },
    });

    // Process sessions to create user analytics
    const userMap = new Map<string, any>();

    sessions.forEach((session) => {
      // Create a unique identifier for the user using the helper function
      const userKey = createUserKey(session);
      const displayName = createUserDisplayName(session, userKey);

      if (!userMap.has(userKey)) {
        userMap.set(userKey, {
          userKey,
          userId: session.user?.id,
          name: displayName,
          email: session.user?.email,
          emailHash: session.user?.emailHash,
          totalSessions: 0,
          totalMessages: 0,
          totalDuration: 0,
          firstSeen: session.startedAt,
          lastSeen: session.lastActivityAt,
          countries: new Set(),
          cities: new Set(),
          devices: new Set(),
          browsers: new Set(),
          domains: new Set(),
          userAgents: new Set(),
          ipAddresses: new Set(),
          errorCount: 0,
          satisfactionRatings: [],
          chatbots: new Set(),
        });
      }

      const user = userMap.get(userKey);
      user.totalSessions += 1;
      user.totalMessages += session.messagesCount;
      user.totalDuration += session.duration || 0;
      user.errorCount += session.errorCount;

      if (session.startedAt < user.firstSeen) {
        user.firstSeen = session.startedAt;
      }
      if (session.lastActivityAt > user.lastSeen) {
        user.lastSeen = session.lastActivityAt;
      }

      // Add unique values to sets
      if (session.country) user.countries.add(session.country);
      if (session.city) user.cities.add(session.city);
      if (session.deviceType) user.devices.add(session.deviceType);
      if (session.browserName) user.browsers.add(session.browserName);
      if (session.domain) user.domains.add(session.domain);
      if (session.userAgent) user.userAgents.add(session.userAgent);
      if (session.ipAddress) user.ipAddresses.add(session.ipAddress);
      if (session.satisfactionRating) user.satisfactionRatings.push(session.satisfactionRating);
      if (session.chatbot.name) user.chatbots.add(session.chatbot.name);
    });

    // Convert to array and calculate derived metrics
    const users = Array.from(userMap.values()).map((user) => ({
      ...user,
      averageDuration: user.totalSessions > 0 ? user.totalDuration / user.totalSessions : 0,
      errorRate: user.totalSessions > 0 ? (user.errorCount / user.totalSessions) * 100 : 0,
      countries: Array.from(user.countries),
      cities: Array.from(user.cities),
      devices: Array.from(user.devices),
      browsers: Array.from(user.browsers),
      domains: Array.from(user.domains),
      userAgents: Array.from(user.userAgents),
      ipAddresses: Array.from(user.ipAddresses),
      chatbots: Array.from(user.chatbots),
    }));

    // Apply search filter
    const filteredUsers = searchTerm
      ? users.filter((user) =>
          user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          user.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
          user.countries.some((country: string) =>
            country.toLowerCase().includes(searchTerm.toLowerCase())
          ) ||
          user.cities.some((city: string) =>
            city.toLowerCase().includes(searchTerm.toLowerCase())
          )
        )
      : users;

    // Create CSV content
    const csvHeaders = [
      "User Key",
      "Name",
      "Email",
      "Total Sessions",
      "Total Messages",
      "Total Duration (seconds)",
      "Average Duration (seconds)",
      "First Seen",
      "Last Seen",
      "Countries",
      "Cities",
      "Devices",
      "Browsers",
      "Domains",
      "Chatbots",
      "IP Addresses Count",
      "Error Count",
      "Error Rate (%)",
      "Satisfaction Ratings",
      "Average Satisfaction"
    ];

    const csvRows = filteredUsers.map(user => [
      user.userKey,
      user.name || "Anonymous User",
      user.email || "N/A",
      user.totalSessions,
      user.totalMessages,
      user.totalDuration,
      user.averageDuration.toFixed(2),
      user.firstSeen.toISOString(),
      user.lastSeen.toISOString(),
      user.countries.join("; "),
      user.cities.join("; "),
      user.devices.join("; "),
      user.browsers.join("; "),
      user.domains.join("; "),
      user.chatbots.join("; "),
      user.ipAddresses.length,
      user.errorCount,
      user.errorRate.toFixed(2),
      user.satisfactionRatings.length > 0 
        ? user.satisfactionRatings.join("; ")
        : "N/A",
      user.satisfactionRatings.length > 0
        ? (user.satisfactionRatings.reduce((sum: number, rating: number) => sum + rating, 0) / user.satisfactionRatings.length).toFixed(2)
        : "N/A"
    ]);

    // Combine headers and rows
    const csvContent = [csvHeaders, ...csvRows]
      .map(row => row.map(cell => `"${String(cell).replace(/"/g, '""')}"`).join(","))
      .join("\n");

    // Return CSV as response
    return new NextResponse(csvContent, {
      status: 200,
      headers: {
        "Content-Type": "text/csv",
        "Content-Disposition": `attachment; filename="user-analytics-${new Date().toISOString().split('T')[0]}.csv"`,
      },
    });

  } catch (error) {
    console.error("Error exporting user analytics:", error);
    return NextResponse.json(
      { error: "Failed to export user analytics" },
      { status: 500 }
    );
  }
}
