import { NextRequest, NextResponse } from "next/server";
import { authOptions } from "@/lib/next-auth";
import db from "@/lib/shared-db";
import { getServerSession } from "next-auth/next";

export async function POST(request: NextRequest) {
  try {
    const session: any = await getServerSession(authOptions);
    if (!session?.userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const { changelogId, dismissed = false } = body;

    if (!changelogId) {
      return NextResponse.json(
        { error: "Changelog ID is required" },
        { status: 400 }
      );
    }

    const userId = session.userId;

    // Check if already viewed
    const existingView = await db.userChangelogView.findFirst({
      where: {
        userId,
        changelogId,
      },
    });

    if (existingView) {
      // Update existing view
      await db.userChangelogView.update({
        where: { id: existingView.id },
        data: {
          dismissed,
          viewedAt: new Date(),
        },
      });
    } else {
      // Create new view record
      await db.userChangelogView.create({
        data: {
          userId,
          changelogId,
          dismissed,
          viewedAt: new Date(),
        },
      });
    }

    return NextResponse.json({
      success: true,
      message: "Changelog marked as viewed",
    });
  } catch (error) {
    console.error("Error marking changelog as viewed:", error);
    return NextResponse.json(
      { error: "Failed to mark changelog as viewed" },
      { status: 500 }
    );
  }
}
