import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import db from "@/lib/shared-db";
import { authOptions } from "@/lib/next-auth";
import { BlobServiceClient } from "@azure/storage-blob";

export async function POST(req: NextRequest) {
  try {
    const session: any = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id, tenantId } = await req.json();

    if (!id) {
      return NextResponse.json(
        { error: "Page ID is required" },
        { status: 400 }
      );
    }

    // Check if the user has access to the page
    const page = await db.page.findUnique({
      where: { id },
      include: {
        workspace: {
          include: {
            tenant: true,
          },
        },
        folders: {
          include: {
            files: true,
          },
        },
        files: true,
      },
    });

    if (!page) {
      return NextResponse.json({ error: "Page not found" }, { status: 404 });
    }

    // Verify tenant access
    if (page.workspace.tenantId !== tenantId) {
      return NextResponse.json({ error: "Access denied" }, { status: 403 });
    }

    // Initialize Azure Blob Storage client for file deletion
    const blobServiceClient = BlobServiceClient.fromConnectionString(
      process.env.AZURE_STORAGE_CONNECTION_STRING || ""
    );
    const containerClient = blobServiceClient.getContainerClient(
      process.env.AZURE_STORAGE_CONTAINER_NAME || ""
    );

    // Use transaction to ensure data consistency
    const result = await db.$transaction(async (prisma) => {
      // Collect all files that need to be deleted from blob storage
      const filesToDeleteFromBlob: string[] = [];

      // Find all synced folders (those with cloud storage IDs)
      const syncedFolders = await prisma.folder.findMany({
        where: {
          pageId: id,
          OR: [
            { gDriveFolderId: { not: null } },
            { oneDriveFolderId: { not: null } },
          ],
        },
        include: {
          files: true,
        },
      });

      // Find all synced files directly under the page
      const syncedFiles = await prisma.file.findMany({
        where: {
          pageId: id,
          OR: [
            { gDriveFileId: { not: null } },
            { oneDriveFileId: { not: null } },
          ],
        },
      });

      // Collect blob URLs for deletion
      for (const folder of syncedFolders) {
        for (const file of folder.files) {
          if (file.url) {
            const parts = decodeURIComponent(
              file.url.split("/").slice(-3).join("/")
            );
            filesToDeleteFromBlob.push(parts);
          }
        }
      }

      for (const file of syncedFiles) {
        if (file.url) {
          const parts = decodeURIComponent(
            file.url.split("/").slice(-3).join("/")
          );
          filesToDeleteFromBlob.push(parts);
        }
      }

      // Delete files from synced folders
      for (const folder of syncedFolders) {
        await prisma.file.deleteMany({
          where: {
            folderId: folder.id,
          },
        });
      }

      // Delete synced files directly under the page
      await prisma.file.deleteMany({
        where: {
          pageId: id,
          OR: [
            { gDriveFileId: { not: null } },
            { oneDriveFileId: { not: null } },
          ],
        },
      });

      // Delete synced folders
      await prisma.folder.deleteMany({
        where: {
          pageId: id,
          OR: [
            { gDriveFolderId: { not: null } },
            { oneDriveFolderId: { not: null } },
          ],
        },
      });

      // Remove sync connection from the page
      await prisma.page.update({
        where: { id },
        data: {
          gDriveFolderId: null,
          oneDriveFolderId: null,
          sharePointSiteId: null,
          sharePointDriveId: null,
        },
      });

      // Delete files from blob storage (outside of transaction to avoid timeout)
      return { filesToDeleteFromBlob };
    });

    // Delete files from Azure Blob Storage
    const deletionPromises = result.filesToDeleteFromBlob.map(async (blobPath) => {
      try {
        const blobClient = containerClient.getBlobClient(blobPath);
        await blobClient.delete();
      } catch (storageError) {
        console.error(`Error deleting blob ${blobPath}:`, storageError);
        // Continue with other deletions even if one fails
      }
    });

    // Wait for all blob deletions to complete
    await Promise.allSettled(deletionPromises);

    return NextResponse.json({
      message: "Sync revoked and synchronized content deleted successfully",
      data: { id },
    });
  } catch (error) {
    console.error("Error revoking sync and deleting content:", error);
    return NextResponse.json(
      { error: "Failed to revoke sync and delete content" },
      { status: 500 }
    );
  }
}
