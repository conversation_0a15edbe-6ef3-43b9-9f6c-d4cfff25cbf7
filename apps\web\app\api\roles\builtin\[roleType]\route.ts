import { NextResponse } from "next/server";
import { getToken } from "next-auth/jwt";
import db from "@/lib/shared-db";
import {
  hasAllRequiredParentPermissions,
  PARENT_RESOURCE_MAP
} from "@/lib/permission-hierarchy";

/**
 * GET /api/roles/builtin/[roleType]
 * Get permissions for a built-in role
 */
export async function GET(
  request: Request,
  { params }: { params: { roleType: string } }
) {
  try {
    const url = new URL(request.url);
    const tenantId = url.searchParams.get("tenantId");
    const userId = request.headers.get("x-user-id");
    const roleType = params.roleType.toUpperCase();

    if (!tenantId || !userId) {
      return NextResponse.json(
        { error: "TenantId and UserId are required" },
        { status: 400 }
      );
    }

    // Verify user has owner or admin access to this tenant
    const membership = await db.membership.findFirst({
      where: {
        userId,
        tenantId,
        role: { in: ["OWNER", "ADMIN"] },
      },
    });

    if (!membership) {
      return NextResponse.json(
        { error: "Only owners and admins can view built-in role configurations" },
        { status: 403 }
      );
    }

    // Validate role type
    if (!["ADMIN", "MEMBER"].includes(roleType)) {
      return NextResponse.json(
        { error: "Invalid role type. Only ADMIN and MEMBER roles can be configured." },
        { status: 400 }
      );
    }

    // Check if there's a custom configuration for this built-in role
    const roleConfig = await db.builtInRoleConfig.findFirst({
      where: {
        tenantId,
        roleType,
      },
      include: {
        permissions: {
          include: {
            permission: true,
          },
        },
      },
    });

    let permissions: string[] = [];

    if (roleConfig) {
      // Return configured permissions
      permissions = roleConfig.permissions.map((p: any) => p.permissionId);
    } else {
      // Return default permissions for the role
      permissions = await getDefaultPermissionsForRole(roleType);
    }

    return NextResponse.json({ permissions });
  } catch (error) {
    console.error("Error fetching built-in role configuration:", error);
    return NextResponse.json(
      { error: "Failed to fetch role configuration" },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/roles/builtin/[roleType]
 * Update permissions for a built-in role
 */
export async function PUT(
  request: Request,
  { params }: { params: { roleType: string } }
) {
  try {
    const token = await getToken({ req: request as any });
    if (!token) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const { permissions, tenantId } = body;
    const roleType = params.roleType.toUpperCase();

    if (!tenantId || !permissions) {
      return NextResponse.json(
        { error: "TenantId and permissions are required" },
        { status: 400 }
      );
    }

    // Verify user has owner access to this tenant
    const membership = await db.membership.findFirst({
      where: {
        userId: token.sub,
        tenantId,
        role: "OWNER",
      },
    });

    if (!membership) {
      return NextResponse.json(
        { error: "Only owners can modify built-in role configurations" },
        { status: 403 }
      );
    }

    // Validate role type
    if (!["ADMIN", "MEMBER"].includes(roleType)) {
      return NextResponse.json(
        { error: "Invalid role type. Only ADMIN and MEMBER roles can be configured." },
        { status: 400 }
      );
    }

    // Validate that all permission IDs exist
    const validPermissions = await db.permission.findMany({
      where: {
        id: { in: permissions },
      },
    });

    if (validPermissions.length !== permissions.length) {
      return NextResponse.json(
        { error: "One or more invalid permission IDs" },
        { status: 400 }
      );
    }

    // Validate permission hierarchy consistency
    const allPermissions = await db.permission.findMany();
    const hierarchyErrors: string[] = [];

    for (const permissionId of permissions) {
      const permission = validPermissions.find((p: any) => p.id === permissionId);
      if (!permission) continue;

      // Check if all required parent permissions are included
      if (!hasAllRequiredParentPermissions(allPermissions, permissions, permission)) {
        const parentResource = PARENT_RESOURCE_MAP[permission.resource as keyof typeof PARENT_RESOURCE_MAP];
        if (parentResource) {
          hierarchyErrors.push(
            `Permission ${permission.action} on ${permission.resource} requires ${permission.action} on ${parentResource}`
          );
        }
      }
    }

    if (hierarchyErrors.length > 0) {
      return NextResponse.json(
        {
          error: "Permission hierarchy validation failed",
          details: hierarchyErrors
        },
        { status: 400 }
      );
    }

    // Find or create the built-in role configuration
    let roleConfig = await db.builtInRoleConfig.findFirst({
      where: {
        tenantId,
        roleType,
      },
    });

    if (!roleConfig) {
      roleConfig = await db.builtInRoleConfig.create({
        data: {
          tenantId,
          roleType,
        },
      });
    }

    // Delete existing permissions
    await db.builtInRolePermission.deleteMany({
      where: {
        builtInRoleConfigId: roleConfig.id,
      },
    });

    // Add new permissions
    if (permissions.length > 0) {
      const permissionConnections = permissions.map((permissionId: string) => ({
        permissionId,
        builtInRoleConfigId: roleConfig.id,
      }));

      await db.builtInRolePermission.createMany({
        data: permissionConnections,
      });
    }

    return NextResponse.json({ 
      message: `${roleType} role configuration updated successfully`,
      roleConfig: {
        id: roleConfig.id,
        roleType: roleConfig.roleType,
        permissions: permissions,
      }
    });
  } catch (error) {
    console.error("Error updating built-in role configuration:", error);
    return NextResponse.json(
      { error: "Failed to update role configuration" },
      { status: 500 }
    );
  }
}

/**
 * Get default permissions for a built-in role
 */
async function getDefaultPermissionsForRole(roleType: string): Promise<string[]> {
  const allPermissions = await db.permission.findMany();
  
  switch (roleType) {
    case "ADMIN":
      // Admins get most permissions except sensitive ones
      return allPermissions
        .filter((p: any) => !(p.resource === "MEMBER" && p.action === "DELETE"))
        .map((p: any) => p.id);
    
    case "MEMBER":
      // Members get basic read permissions
      return allPermissions
        .filter((p: any) => p.action === "READ")
        .map((p: any) => p.id);
    
    default:
      return [];
  }
}
