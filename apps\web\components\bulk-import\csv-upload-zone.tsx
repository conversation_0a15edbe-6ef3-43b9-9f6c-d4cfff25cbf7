"use client";

import React, { useCallback, useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Upload, FileText, Download, AlertCircle, X, Info } from "lucide-react";

import { cn } from "@/lib/utils";
import { generateCSVTemplate } from "@/lib/utils/csv-parser";
import { useLanguage } from "@/lib/language-context";

interface CustomRole {
  id: string;
  name: string;
  description?: string;
}

interface CSVUploadZoneProps {
  onFileSelect: (file: File) => void;
  tenantId: string;
  isLoading?: boolean;
  error?: string;
  className?: string;
}

export function CSVUploadZone({
  onFileSelect,
  tenantId,
  isLoading = false,
  error,
  className,
}: CSVUploadZoneProps) {
  const { t } = useLanguage();
  const [isDragOver, setIsDragOver] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [customRoles, setCustomRoles] = useState<CustomRole[]>([]);
  const [loadingRoles, setLoadingRoles] = useState(false);

  // Fetch custom roles on component mount
  useEffect(() => {
    const fetchCustomRoles = async () => {
      setLoadingRoles(true);
      try {
        const response = await fetch(`/api/roles?tenantId=${tenantId}`);
        if (response.ok) {
          const data = await response.json();
          setCustomRoles(data.customRoles || []);
        }
      } catch (error) {
        console.error("Failed to fetch custom roles:", error);
      } finally {
        setLoadingRoles(false);
      }
    };

    fetchCustomRoles();
  }, [tenantId]);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(false);
  }, []);

  const handleDrop = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault();
      e.stopPropagation();
      setIsDragOver(false);

      const files = Array.from(e.dataTransfer.files);
      const csvFile = files.find(
        (file) =>
          file.type === "text/csv" || file.name.toLowerCase().endsWith(".csv")
      );

      if (csvFile) {
        setSelectedFile(csvFile);
        onFileSelect(csvFile);
      }
    },
    [onFileSelect]
  );

  const handleFileInput = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const file = e.target.files?.[0];
      if (file) {
        setSelectedFile(file);
        onFileSelect(file);
      }
    },
    [onFileSelect]
  );

  const handleDownloadTemplate = useCallback(() => {
    const csvContent = generateCSVTemplate(customRoles);
    const blob = new Blob([csvContent], { type: "text/csv" });
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.download = "member-import-template.csv";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }, [customRoles]);

  const handleRemoveFile = useCallback(() => {
    setSelectedFile(null);
  }, []);

  return (
    <div className={cn("space-y-4", className)}>
      {/* Upload Zone */}
      <Card
        className={cn(
          "border-2 border-dashed transition-all duration-200",
          isDragOver && "border-primary bg-primary/5",
          error && "border-destructive",
          "hover:border-primary/50"
        )}
      >
        <CardContent className="p-8">
          <div
            className="text-center space-y-4"
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
          >
            {selectedFile ? (
              // File Selected State
              <div className="space-y-4">
                <div className="flex items-center justify-center gap-3">
                  <FileText className="h-8 w-8 text-primary" />
                  <div className="text-left">
                    <p className="font-medium text-sm">{selectedFile.name}</p>
                    <p className="text-xs text-muted-foreground">
                      {(selectedFile.size / 1024).toFixed(1)} KB
                    </p>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleRemoveFile}
                    className="ml-2"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>

                {error && (
                  <div className="flex items-center gap-2 text-destructive text-sm">
                    <AlertCircle className="h-4 w-4" />
                    <span>{error}</span>
                  </div>
                )}
              </div>
            ) : (
              // Upload State
              <div className="space-y-4">
                <div className="flex justify-center">
                  <div className="rounded-full bg-primary/10 p-4">
                    <Upload className="h-8 w-8 text-primary" />
                  </div>
                </div>

                <div className="space-y-2">
                  <h3 className="font-semibold text-lg">{t("bulkImport.members.csvUpload.title")}</h3>
                  <p className="text-muted-foreground text-sm">
                    {t("bulkImport.members.csvUpload.description")}
                  </p>
                </div>

                <div className="space-y-3">
                  <input
                    type="file"
                    accept=".csv,text/csv"
                    onChange={handleFileInput}
                    className="hidden"
                    id="csv-upload"
                    disabled={isLoading}
                  />
                  <label htmlFor="csv-upload">
                    <Button
                      variant="outline"
                      className="cursor-pointer"
                      disabled={isLoading}
                      asChild
                    >
                      <span>
                        <FileText className="h-4 w-4 mr-2" />
                        {t("bulkImport.members.csvUpload.chooseFile")}
                      </span>
                    </Button>
                  </label>

                  <div className="text-xs text-muted-foreground">
                    {t("bulkImport.members.csvUpload.supportedFormat")}
                  </div>
                </div>

                {error && (
                  <div className="flex items-center gap-2 text-destructive text-sm">
                    <AlertCircle className="h-4 w-4" />
                    <span>{error}</span>
                  </div>
                )}
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Template Download */}
      <div className="flex items-center justify-between p-4 bg-muted/50 rounded-lg">
        <div className="space-y-1">
          <p className="text-sm font-medium">{t("bulkImport.members.csvUpload.template.title")}</p>
          <p className="text-xs text-muted-foreground">
            {t("bulkImport.members.csvUpload.template.description")}
          </p>
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={handleDownloadTemplate}
          className="gap-2"
        >
          <Download className="h-4 w-4" />
          {t("bulkImport.members.csvUpload.template.downloadButton")}
        </Button>
      </div>

      {/* Format Instructions */}
      <Card>
        <CardContent className="p-4">
          <h4 className="font-medium text-sm mb-3">{t("bulkImport.members.csvUpload.formatRequirements.title")}</h4>
          <div className="space-y-2 text-xs text-muted-foreground">
            <div className="flex items-start gap-2">
              <span className="font-medium text-foreground">
                {t("bulkImport.members.csvUpload.formatRequirements.requiredColumns")}
              </span>
              <span>email, role</span>
            </div>
            <div className="flex items-start gap-2">
              <span className="font-medium text-foreground">
                {t("bulkImport.members.csvUpload.formatRequirements.optionalColumns")}
              </span>
              <span>name</span>
            </div>
            <div className="flex items-start gap-2">
              <span className="font-medium text-foreground">{t("bulkImport.members.csvUpload.formatRequirements.validRoles")}</span>
              <span>MEMBER, ADMIN, CUSTOM</span>
            </div>
            <div className="flex items-start gap-2">
              <span className="font-medium text-foreground">{t("bulkImport.members.csvUpload.formatRequirements.note")}</span>
              <span>
                {t("bulkImport.members.csvUpload.formatRequirements.customRoleNote")}
              </span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Custom Roles Reference */}
      {customRoles.length > 0 && (
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2 mb-3">
              <Info className="h-4 w-4 text-blue-600" />
              <h4 className="font-medium text-sm">{t("bulkImport.members.csvUpload.customRoles.title")}</h4>
            </div>
            <div className="space-y-2">
              {customRoles.map((role) => (
                <div
                  key={role.id}
                  className="flex items-center p-2 bg-muted/50 rounded text-xs"
                >
                  <div>
                    <span className="font-medium">{role.name}</span>
                    {role.description && (
                      <span className="text-muted-foreground ml-2">
                        - {role.description}
                      </span>
                    )}
                  </div>
                </div>
              ))}
            </div>
            <p className="text-xs text-muted-foreground mt-3">
              {t("bulkImport.members.csvUpload.customRoles.usage")}
            </p>
          </CardContent>
        </Card>
      )}

      {loadingRoles && (
        <Card>
          <CardContent className="p-4 text-center">
            <div className="flex items-center justify-center gap-2 text-sm text-muted-foreground">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
              {t("bulkImport.members.csvUpload.customRoles.loading")}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
