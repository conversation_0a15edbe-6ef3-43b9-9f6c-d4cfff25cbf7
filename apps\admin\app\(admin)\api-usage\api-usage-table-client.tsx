"use client";

import { useState, useEffect, useCallback, useTransition } from "react";
import { useR<PERSON>er, useSearchParams } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { formatNumber, formatDate, formatTime, cn } from "@/lib/utils";
import { Search, X, Clock, CheckCircle, XCircle, Activity } from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  <PERSON><PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line,
} from "recharts";

// Safe Chart Wrapper Component with enhanced protection
function SafeChart({
  children,
  data,
  fallback = "Chart unavailable"
}: {
  children: React.ReactNode;
  data: any[];
  fallback?: string;
}) {
  // Pre-render validation
  if (!data || !Array.isArray(data) || data.length === 0) {
    return (
      <div className="flex h-full items-center justify-center">
        <p className="text-muted-foreground">No data available</p>
      </div>
    );
  }

  // Check if data contains any invalid values
  const hasValidData = data.some(item =>
    item &&
    typeof item === 'object' &&
    typeof item.value === 'number' &&
    !isNaN(item.value) &&
    isFinite(item.value) &&
    item.value > 0
  );

  if (!hasValidData) {
    return (
      <div className="flex h-full items-center justify-center">
        <p className="text-muted-foreground">No valid data available</p>
      </div>
    );
  }

  try {
    return <>{children}</>;
  } catch (error) {
    console.error('Chart rendering error:', error);
    return (
      <div className="flex h-full items-center justify-center">
        <p className="text-muted-foreground">{fallback}</p>
      </div>
    );
  }
}

// Custom hook for debouncing values
function useDebounceLocal<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
}

// Types
interface ApiRequest {
  id: string;
  userId: string | null;
  userName: string;
  tenantId: string;
  tenantName: string;
  endpoint: string;
  method: string;
  statusCode: number;
  timestamp: string;
  duration: number;
  success: boolean;
  errorMessage: string | null;
}

type DailyUsage = {
  name: string;
  [endpoint: string]: number | string; // Keep this for additional endpoint data
};


interface MethodUsage {
  name: string;
  value: number;
}

interface EndpointUsage {
  name: string;
  value: number;
}

interface StatusCodeUsage {
  name: string;
  value: number;
  color: string;
}

interface ApiUsageData {
  apiRequests: ApiRequest[];
  dailyUsage: DailyUsage[];
  methodUsage: MethodUsage[];
  endpointUsage: EndpointUsage[];
  statusCodeUsage: StatusCodeUsage[];
  dailyHttpRequestCount:any;
  metrics: {
    totalRequests: number;
    successRate: number;
    averageResponseTime: number;
    errorRate: number;
  };
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  searchQuery: string;
}

interface ApiUsageTableClientProps {
  data: ApiUsageData;
}

function formatDuration(duration: number): string {
  // Handle NaN and invalid values
  if (!duration || isNaN(duration) || duration < 0) return '0ms';
  if (duration < 1000) return `${Math.round(duration)}ms`;
  return `${(duration / 1000).toFixed(2)}s`;
}

// Helper function to validate chart data
function validateChartData(data: any[]): any[] {
  if (!Array.isArray(data)) return [];
  return data.filter(item =>
    item &&
    typeof item === 'object' &&
    typeof item.value === 'number' &&
    !isNaN(item.value) &&
    isFinite(item.value) &&
    item.value > 0
  );
}

// Helper function to get safe domain for charts
function getSafeDomain(data: any[]): [number, number] {
  if (!data || data.length === 0) {
    return [0, 1];
  }

  const values = data.map(item => {
    if (!item || typeof item !== 'object') return null;
    const val = item.value;
    if (typeof val !== 'number' || isNaN(val) || !isFinite(val) || val <= 0) return null;
    return val;
  }).filter(val => val !== null) as number[];

  if (values.length === 0) {
    return [0, 1];
  }

  const max = Math.max(...values);
  const min = Math.min(...values);

  // Ensure we have valid numbers
  if (!isFinite(max) || !isFinite(min) || isNaN(max) || isNaN(min) || max <= 0) {
    return [0, 1];
  }

  const domain: [number, number] = [0, Math.max(1, Math.ceil(max * 1.1))];
  return domain;
}

export function ApiUsageTableClient({ data }: ApiUsageTableClientProps) {
  const {
    apiRequests,
    dailyUsage: rawDailyUsage,
    methodUsage: rawMethodUsage,
    endpointUsage: rawEndpointUsage,
    statusCodeUsage: rawStatusCodeUsage,
    dailyHttpRequestCount,
    metrics,
    pagination,
    searchQuery,
  } = data;
  // Validate all chart data to prevent Recharts errors
  const dailyUsage = rawDailyUsage;
  const methodUsage = validateChartData(rawMethodUsage);
  const endpointUsage = validateChartData(rawEndpointUsage);
  const statusCodeUsage = validateChartData(rawStatusCodeUsage);

  // Extract unique endpoints for the dropdown
  const uniqueEndpoints = endpointUsage.map(item => item.name);

  const router = useRouter();
  const searchParams = useSearchParams();
  const [isPending, startTransition] = useTransition();
  const [searchInput, setSearchInput] = useState(searchQuery);
  const [selectedEndpoint, setSelectedEndpoint] = useState<string>(
    searchParams?.get("endpoint") || ""
  );

  // Debounce the search input to avoid excessive URL updates
  const debouncedSearchInput = useDebounceLocal(searchInput, 400);

  // Handle search with URL update
  const handleSearch = useCallback(
    (query: string) => {
      const params = new URLSearchParams(searchParams?.toString());
      if (query.trim()) {
        params.set("search", query.trim());
      } else {
        params.delete("search");
      }
      params.set("page", "1"); // Reset to first page on search

      startTransition(() => {
        router.push(`/api-usage?${params.toString()}`);
      });
    },
    [searchParams, router]
  );

  // Effect to trigger search when debounced input changes
  useEffect(() => {
    // Only trigger search if the debounced value is different from current search query
    if (debouncedSearchInput !== searchQuery) {
      handleSearch(debouncedSearchInput);
    }
  }, [debouncedSearchInput, searchQuery, handleSearch]);

  // Handle pagination
  const handlePageChange = (newPage: number) => {
    const params = new URLSearchParams(searchParams?.toString());
    params.set("page", newPage.toString());

    startTransition(() => {
      router.push(`/api-usage?${params.toString()}`);
    });
  };

  // Handle clear search
  const handleClearSearch = () => {
    setSearchInput("");
    // The useEffect will handle the URL update when debouncedSearchInput changes
  };

  // Handle endpoint selection
  const handleEndpointChange = (value: string) => {
    setSelectedEndpoint(value);

    const params = new URLSearchParams(searchParams?.toString());
    if (value) {
      params.set("endpoint", value);
    } else {
      params.delete("endpoint");
    }
    params.set("page", "1"); // Reset to first page on endpoint change

    startTransition(() => {
      router.push(`/api-usage?${params.toString()}`);
    });
  };

  const COLORS = ["#8884d8", "#82ca9d", "#ffc658", "#ff8042", "#a4de6c", "#d0ed57"];

  // Handle clear endpoint filter
  const handleClearEndpoint = () => {
    setSelectedEndpoint("");

    const params = new URLSearchParams(searchParams?.toString());
    params.delete("endpoint");
    params.set("page", "1"); // Reset to first page

    startTransition(() => {
      router.push(`/api-usage?${params.toString()}`);
    });
  };

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (!active || !payload || payload.length === 0) return null;

    return (
      <div className="rounded-lg border bg-white p-3 shadow-md dark:bg-zinc-900 dark:border-zinc-700">
        <p className="text-xs text-muted-foreground mb-2">{label}</p>
        {payload.map((entry: any, index: number) => (
          <div key={`item-${index}`} className="text-sm flex gap-2 items-center mb-1">
            <span
              className="inline-block w-2 h-2 rounded-full"
              style={{ backgroundColor: entry.color }}
            />
            <span className="text-[13px] font-medium" style={{ color: entry.color }}>
              {entry.name}
            </span>
            <span className="text-[13px] text-muted-foreground">: {entry.value}</span>
          </div>
        ))}
      </div>
    );
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold">API Usage Analytics</h1>
        <p className="text-muted-foreground">
          Monitor API request patterns, performance, and user activity
        </p>
      </div>

      {/* Metrics Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Total Requests
            </CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatNumber(metrics.totalRequests)}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Success Rate</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {isNaN(metrics.successRate) ? '0.0' : metrics.successRate.toFixed(1)}%
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Avg Response Time
            </CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatDuration(metrics.averageResponseTime)}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Error Rate</CardTitle>
            <XCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {isNaN(metrics.errorRate) ? '0.0' : metrics.errorRate.toFixed(1)}%
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Charts */}
      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Daily API Requests</CardTitle>
          </CardHeader>
          <CardContent className="h-[300px]">
            {dailyUsage.length === 0 ? (
              <div className="flex h-full items-center justify-center">
                <p className="text-muted-foreground">No data available</p>
              </div>
            ) : (
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={dailyUsage}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis domain={getSafeDomain(dailyUsage)} />
                  <Tooltip content={<CustomTooltip />} />

                  {/* 🔁 Dynamically render a <Line /> for each endpoint */}
                  {Object.keys(dailyUsage[0] || {})
                    .filter((key) => key !== "name")
                    .map((endpoint, index) => (
                      <Line
                        key={endpoint}
                        type="monotone"
                        dataKey={endpoint}
                        stroke={COLORS[index % COLORS.length]}
                        strokeWidth={2}
                        dot={false}
                      />
                    ))}
                </LineChart>
              </ResponsiveContainer>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>HTTP Methods Distribution</CardTitle>
          </CardHeader>
          <CardContent className="h-[300px]">
            {methodUsage.length === 0 ? (
              <div className="flex h-full items-center justify-center">
                <p className="text-muted-foreground">No data available</p>
              </div>
            ) : (
              <SafeChart data={methodUsage} fallback="HTTP methods chart unavailable">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={methodUsage}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, percent }) =>
                        `${name}: ${(percent * 100).toFixed(0)}%`
                      }
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {methodUsage.map((_, index) => (
                        <Cell
                          key={`cell-${index}`}
                          fill={`hsl(${index * 45}, 70%, 60%)`}
                        />
                      ))}
                    </Pie>
                    <Tooltip content={<CustomTooltip />} />
                    <Legend />
                  </PieChart>
                </ResponsiveContainer>
              </SafeChart>
            )}
          </CardContent>
        </Card>
      </div>

      {/* API Requests Table */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>
              API Request Details
              {(searchQuery || selectedEndpoint) && (
                <span className="ml-2 text-sm font-normal text-muted-foreground">
                  (filtered by {searchQuery && <>"{searchQuery}"</>}
                  {searchQuery && selectedEndpoint && " and "}
                  {selectedEndpoint && <>endpoint "{selectedEndpoint}"</>})
                </span>
              )}
            </CardTitle>
            <div className="flex gap-2 w-full md:w-auto">
              <div className="relative w-[180px]">
                <Select value={selectedEndpoint} onValueChange={handleEndpointChange}>
                  <SelectTrigger className={`${selectedEndpoint ? "pr-8" : ""}`}>
                    <SelectValue placeholder="Filter by endpoint" />
                  </SelectTrigger>
                  <SelectContent>
                    {uniqueEndpoints.map((endpoint) => (
                      <SelectItem key={endpoint} value={endpoint}>
                        {endpoint}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {selectedEndpoint && (
                  <button
                    type="button"
                    onClick={handleClearEndpoint}
                    className="absolute right-2 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground"
                  >
                    <X className="h-4 w-4" />
                  </button>
                )}
              </div>
              <div className="relative w-64">
                <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                <Input
                  placeholder="Search endpoints, users, methods..."
                  value={searchInput}
                  onChange={(e) => setSearchInput(e.target.value)}
                  className="pl-9 pr-9"
                />
                {searchInput && (
                  <button
                    onClick={handleClearSearch}
                    className="absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground"
                    type="button"
                  >
                    <X className="h-4 w-4" />
                  </button>
                )}
              </div>
            </div>
          </div>
          {isPending && (
            <div className="mt-2 text-sm text-muted-foreground">
              Searching...
            </div>
          )}
        </CardHeader>
        <CardContent>
          {apiRequests.length === 0 ? (
            <div className="flex h-32 items-center justify-center">
              <p className="text-muted-foreground">
                {searchQuery
                  ? "No API requests found matching your search."
                  : "No API requests found."}
              </p>
            </div>
          ) : (
            <>
              <div className="overflow-x-auto">
                <table className="w-full text-sm">
                  <thead>
                    <tr className="border-b">
                      <th className="px-4 py-3 text-left font-medium">
                        Endpoint
                      </th>
                      <th className="px-4 py-3 text-left font-medium">
                        Method
                      </th>
                      <th className="px-4 py-3 text-left font-medium">
                        Status
                      </th>
                      <th className="px-4 py-3 text-left font-medium">User</th>
                      <th className="px-4 py-3 text-left font-medium">
                        Tenant
                      </th>
                      <th className="px-4 py-3 text-left font-medium">
                        Duration
                      </th>
                      <th className="px-4 py-3 text-left font-medium">
                        Timestamp
                      </th>
                      <th className="px-4 py-3 text-left font-medium">Success/Error</th>
                    </tr>
                  </thead>
                  <tbody>
                    {apiRequests.map((request) => (
                      <tr key={request.id} className="border-b">
                        <td className="px-4 py-3">
                          <code className="rounded bg-muted px-2 py-1 text-xs">
                            {request.endpoint}
                          </code>
                        </td>
                        <td className="px-4 py-3">
                          <Badge
                            className={cn({
                              "bg-green-600 text-white": request.method === "GET",
                              "bg-orange-800 text-white": request.method === "POST",
                              "bg-gray-600 text-white": request.method === "PUT",
                              "bg-yellow-500 text-black": request.method === "PATCH",
                              "bg-red-600 text-white": request.method === "DELETE",
                              "border border-gray-300 text-muted-foreground": !["GET", "POST", "PUT", "PATCH", "DELETE"].includes(request.method),
                            })}
                          >
                            {request.method}
                          </Badge>
                        </td>
                        <td className="px-4 py-3">
                          <Badge
                            className={cn({
                              "bg-green-700 text-white": request.statusCode >= 200 && request.statusCode < 300,
                              "bg-yellow-400 text-black": request.statusCode >= 300 && request.statusCode < 400,
                              "bg-red-500 text-white": request.statusCode >= 400,
                              "border border-gray-300": request.statusCode < 200,
                            })}
                          >
                            {request.statusCode}
                          </Badge>
                        </td>
                        <td className="px-4 py-3">{request.userName}</td>
                        <td className="px-4 py-3">{request.tenantName}</td>
                        <td className="px-4 py-3">
                          <span
                            className={`${request.duration > 50000
                              ? "text-red-600"
                              : request.duration > 2000
                                ? "text-yellow-600"
                                : "text-green-600"
                              }`}
                          >
                            {formatDuration(request.duration)}
                          </span>
                        </td>
                        <td className="px-4 py-3">
                          <div className="text-xs">
                            <div>{formatDate(request.timestamp)}</div>
                            <div className="text-muted-foreground">
                              {formatTime(request.timestamp)}
                            </div>
                          </div>
                        </td>
                        <td className="px-4 py-3">
                          {request.errorMessage ? (
                            <span
                              className="text-red-600 text-xs"
                              title={request.errorMessage}
                            >
                              Error
                            </span>
                          ) : (
                            <span className="text-green-600 text-xs">
                              Success
                            </span>
                          )}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {/* Pagination */}
              {pagination.totalPages > 1 && (
                <div className="mt-4 flex items-center justify-between">
                  <div className="text-sm text-muted-foreground">
                    Showing {(pagination.page - 1) * pagination.limit + 1} to{" "}
                    {Math.min(
                      pagination.page * pagination.limit,
                      pagination.total
                    )}{" "}
                    of {pagination.total} results
                  </div>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePageChange(pagination.page - 1)}
                      disabled={pagination.page <= 1 || isPending}
                    >
                      Previous
                    </Button>
                    <div className="flex gap-1">
                      {Array.from(
                        { length: Math.min(5, pagination.totalPages) },
                        (_, i) => {
                          const pageNum = Math.max(1, pagination.page - 2) + i;
                          if (pageNum > pagination.totalPages) return null;

                          return (
                            <Button
                              key={pageNum}
                              variant={
                                pageNum === pagination.page
                                  ? "default"
                                  : "outline"
                              }
                              size="sm"
                              onClick={() => handlePageChange(pageNum)}
                              disabled={isPending}
                              className="w-8"
                            >
                              {pageNum}
                            </Button>
                          );
                        }
                      )}
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePageChange(pagination.page + 1)}
                      disabled={
                        pagination.page >= pagination.totalPages || isPending
                      }
                    >
                      Next
                    </Button>
                  </div>
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Daily HTTP Method Usage</CardTitle>
        </CardHeader>
        <CardContent className="h-[300px]">
          {dailyHttpRequestCount.length === 0 ? (
            <div className="flex h-full items-center justify-center">
              <p className="text-muted-foreground">No data available</p>
            </div>
          ) : (
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={dailyHttpRequestCount}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip content={<CustomTooltip />} />

                {/* Render a line for each HTTP method */}
                {Object.keys(dailyHttpRequestCount[0])
                  .filter((key) => key !== "name")
                  .map((method, index) => (
                    <Line
                      key={method}
                      type="monotone"
                      dataKey={method}
                      stroke={COLORS[index % COLORS.length]}
                      strokeWidth={2}
                      dot={false}
                    />
                  ))}
              </LineChart>
            </ResponsiveContainer>
          )}
        </CardContent>
      </Card>

    </div>
  );
}
