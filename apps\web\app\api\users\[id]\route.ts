import { NextResponse } from "next/server";

import db from "@/lib/shared-db";
import { withPermission } from "@/lib/permission-middleware";

// Get a specific user by ID
export const GET = withPermission(
  async (request: Request, params: { [key: string]: string }) => {
    try {
      const userId = params.id;
      const tenantId = request.headers.get("x-tenant-id");

      if (!userId || !tenantId) {
        return NextResponse.json(
          { error: "User ID and Tenant ID are required" },
          { status: 400 }
        );
      }

      // Get the user
      const user = await db.user.findUnique({
        where: {
          id: userId,
        },
        select: {
          id: true,
          name: true,
          email: true,
          image: true,
          membership: {
            where: {
              tenantId,
            },
            select: {
              role: true,
              customRoleId: true,
              customRole: true,
            },
          },
          workspaceMember: {
            where: {
              workspace: {
                tenantId,
              },
            },
            include: {
              workspace: true,
              customRole: true,
            },
          },
        },
      });

      if (!user) {
        return NextResponse.json({ error: "User not found" }, { status: 404 });
      }

      // Verify user is a member of the tenant
      if (user.membership.length === 0) {
        return NextResponse.json(
          { error: "User is not a member of this tenant" },
          { status: 403 }
        );
      }

      return NextResponse.json({
        data: user,
        message: "User retrieved successfully",
      });
    } catch (error) {
      console.error("Error fetching user:", error);
      return NextResponse.json(
        { error: "Failed to fetch user" },
        { status: 500 }
      );
    }
  },
  "READ",
  "MEMBER"
);
