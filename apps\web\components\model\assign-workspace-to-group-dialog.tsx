"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { useLanguage } from "@/lib/language-context";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  <PERSON><PERSON>Title,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { useRouter } from "next/navigation";
import toast from "react-hot-toast";
import { assignWorkspaceToGroup } from "@/services/src/group";
import { Check, ChevronsUpDown } from "lucide-react";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { getWorkspace } from "@/services";
import { getCookie } from "@/utils/cookies";
import { cn } from "@/lib/utils";

interface Workspace {
  id: string;
  name: string;
  description?: string;
}

interface AssignWorkspaceToGroupDialogProps {
  groupId: string;
  tenantId?: string;
  trigger: React.ReactNode;
  onWorkspaceAssigned?: () => void; // Callback to refresh parent component
}

export function AssignWorkspaceToGroupDialog({
  groupId,
  tenantId,
  trigger,
  onWorkspaceAssigned,
}: AssignWorkspaceToGroupDialogProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [workspaces, setWorkspaces] = useState<Workspace[]>([]);
  const [selectedWorkspace, setSelectedWorkspace] = useState<Workspace | null>(
    null
  );
  const [open, setOpen] = useState(false);
  const router = useRouter();
  const { t } = useLanguage();
  const userId = getCookie("userId");

  useEffect(() => {
    const fetchWorkspaces = async () => {
      try {
        const response = await getWorkspace(tenantId, userId);
        if (response?.workspaces) {
          setWorkspaces(response.workspaces);
        }
      } catch (error) {
        console.error("Error fetching workspaces:", error);
      }
    };

    if (isOpen) {
      fetchWorkspaces();
    }
  }, [isOpen, tenantId]);

  const onSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedWorkspace || !tenantId) {
      toast.error(t("groups.selectWorkspace"));
      return;
    }

    try {
      setIsLoading(true);
      const result = await assignWorkspaceToGroup(
        {
          groupId,
          workspaceId: selectedWorkspace.id,
          tenantId,
        },
        userId
      );
      if (result?.error) {
        toast.error(result.error || t("common.error"));
        return;
      }
      toast.success(t("groups.workspaceAssignedToGroup"));
      setIsOpen(false);
      setSelectedWorkspace(null);

      // Trigger parent component refresh
      if (onWorkspaceAssigned) {
        onWorkspaceAssigned();
      } else {
        // Fallback to router refresh if no callback provided
        router.refresh();
      }
    } catch (error) {
      toast.error(t("groups.failedToAssignWorkspace"));
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>{trigger}</DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{t("groups.assignWorkspaceToGroup")}</DialogTitle>
          <DialogDescription>
            {t("groups.assignWorkspaceDescription")}
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={onSubmit}>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label>{t("groups.selectWorkspace")}</Label>
              <Popover open={open} modal={true} onOpenChange={setOpen}>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    role="combobox"
                    aria-expanded={open}
                    className="w-full justify-between"
                  >
                    {selectedWorkspace
                      ? selectedWorkspace.name
                      : t("groups.selectWorkspace")}
                    <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-full p-0 max-h-[300px] overflow-hidden">
                  <Command>
                    <CommandInput
                      placeholder={t("workspace.searchPages")}
                      className="h-9"
                    />
                    <CommandEmpty>{t("groups.noWorkspacesFound")}</CommandEmpty>
                    <CommandGroup className="max-h-[240px] overflow-y-auto">
                      {workspaces.map((workspace) => (
                        <CommandItem
                          key={workspace?.id}
                          value={workspace?.name}
                          onSelect={() => {
                            setSelectedWorkspace(workspace);
                            setOpen(false);
                          }}
                        >
                          {workspace?.name}
                          <Check
                            className={cn(
                              "ml-auto h-4 w-4",
                              selectedWorkspace?.id === workspace?.id
                                ? "opacity-100"
                                : "opacity-0"
                            )}
                          />
                        </CommandItem>
                      ))}
                    </CommandGroup>
                  </Command>
                </PopoverContent>
              </Popover>
            </div>
          </div>
          <DialogFooter>
            <Button type="submit" disabled={isLoading || !selectedWorkspace}>
              {isLoading ? t("common.assigning") : t("common.assign")}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
