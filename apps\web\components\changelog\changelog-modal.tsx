"use client";

import React, { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { X, ChevronLeft, ChevronRight, ExternalLink } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";

import { useLanguage } from "@/lib/language-context";
import ReactMarkdown from "react-markdown";
import { CodeBlock } from "@/components/wrapper-screens/chat/components/CodeBlock";
import { useLanguageDetection } from "@/hooks/useLanguageDetection";
// import ReactMarkdown from "react-markdown";
// import remarkGfm from "remark-gfm";

interface Changelog {
  id: string;
  title: string;
  content: string;
  version?: string;
  type: "RELEASE" | "HOTFIX" | "MAINTENANCE" | "ANNOUNCEMENT";
  priority: "LOW" | "MEDIUM" | "HIGH" | "CRITICAL";
  publishedAt: string;
  githubCommitSha?: string;
  deploymentId?: string;
}

interface ChangelogModalProps {
  changelogs: Changelog[];
  isOpen: boolean;
  onClose: () => void;
  onMarkViewed: (
    changelogId: string,
    dismissed?: boolean,
    removeFromList?: boolean
  ) => void;
  onMarkMultipleAsViewed: (changelogIds: string[], dismissed?: boolean) => void;
  onRemoveViewedChangelogs: (changelogIds: string[]) => void;
}

const ChangelogModal: React.FC<ChangelogModalProps> = ({
  changelogs,
  isOpen,
  onClose,
  onMarkViewed,
  onMarkMultipleAsViewed,
  onRemoveViewedChangelogs,
}) => {
  const { t } = useLanguage();
  const { currentLanguage } = useLanguageDetection();
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isClosing, setIsClosing] = useState(false);
  const [viewedChangelogIds, setViewedChangelogIds] = useState<string[]>([]);

  // Reset index when changelogs change
  useEffect(() => {
    setCurrentIndex(0);
  }, [changelogs]);

  const currentChangelog = changelogs[currentIndex];
  const hasMultiple = changelogs.length > 1;

  const handleClose = (dismissed = false) => {
    setIsClosing(true);

    if (dismissed) {
      // If dismissed, mark ALL changelogs as viewed to prevent them from showing again
      const allChangelogIds = changelogs.map((c) => c.id);

      // Mark all as viewed in the database using batch function
      onMarkMultipleAsViewed(allChangelogIds, true);

      // Remove all changelogs from the list
      onRemoveViewedChangelogs(allChangelogIds);
    } else {
      // Normal close - only mark current and previously viewed changelogs
      if (
        currentChangelog &&
        !viewedChangelogIds.includes(currentChangelog.id)
      ) {
        onMarkViewed(currentChangelog.id, dismissed, false);
        setViewedChangelogIds((prev) => [...prev, currentChangelog.id]);
      }

      // Remove all viewed changelogs from the list
      if (viewedChangelogIds.length > 0 || currentChangelog) {
        const allViewedIds =
          currentChangelog && !viewedChangelogIds.includes(currentChangelog.id)
            ? [...viewedChangelogIds, currentChangelog.id]
            : viewedChangelogIds;
        onRemoveViewedChangelogs(allViewedIds);
      }
    }

    setTimeout(() => {
      onClose();
      setIsClosing(false);
      setViewedChangelogIds([]); // Reset for next time
    }, 200);
  };

  const handleNext = () => {
    if (currentIndex < changelogs.length - 1) {
      // Mark current as viewed and track it, but DON'T remove from list during navigation
      if (
        currentChangelog &&
        !viewedChangelogIds.includes(currentChangelog.id)
      ) {
        onMarkViewed(currentChangelog.id, false, false);
        setViewedChangelogIds((prev) => [...prev, currentChangelog.id]);
      }
      setCurrentIndex(currentIndex + 1);
    }
  };

  const handlePrevious = () => {
    if (currentIndex > 0) {
      setCurrentIndex(currentIndex - 1);
    }
  };

  const handleConfirm = () => {
    if (hasMultiple && currentIndex < changelogs.length - 1) {
      handleNext();
    } else {
      // This is the last changelog or only one, so close and remove from list
      handleClose(false);
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "CRITICAL":
        return "bg-red-500 text-white";
      case "HIGH":
        return "bg-orange-500 text-white";
      case "MEDIUM":
        return "bg-blue-500 text-white";
      case "LOW":
        return "bg-gray-500 text-white";
      default:
        return "bg-gray-500 text-white";
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case "RELEASE":
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";
      case "HOTFIX":
        return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200";
      case "MAINTENANCE":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200";
      case "ANNOUNCEMENT":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200";
    }
  };

  if (!isOpen || !currentChangelog) return null;

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50"
            onClick={() => handleClose(true)}
          />

          {/* Modal */}
          <motion.div
            initial={{ opacity: 0, scale: 0.95, y: 20 }}
            animate={{
              opacity: isClosing ? 0 : 1,
              scale: isClosing ? 0.95 : 1,
              y: isClosing ? 20 : 0,
            }}
            exit={{ opacity: 0, scale: 0.95, y: 20 }}
            transition={{ duration: 0.2 }}
            className="fixed inset-0 z-50 flex items-center justify-center p-4 overflow-hidden"
          >
            <div className="bg-white dark:bg-gray-900 rounded-xl shadow-2xl max-w-2xl w-full max-h-[90vh] min-h-[50vh] flex flex-col overflow-hidden mx-auto my-auto">
              {/* Header */}
              <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
                <div className="flex items-center gap-3">
                  <div className="flex items-center gap-2">
                    <Badge
                      className={getPriorityColor(currentChangelog.priority)}
                    >
                      {currentChangelog.priority}
                    </Badge>
                    <Badge
                      variant="outline"
                      className={getTypeColor(currentChangelog.type)}
                    >
                      {currentChangelog.type}
                    </Badge>
                    {currentChangelog.version && (
                      <Badge variant="secondary">
                        v{currentChangelog.version}
                      </Badge>
                    )}
                  </div>
                  {hasMultiple && (
                    <span className="text-sm text-gray-500 dark:text-gray-400">
                      {currentIndex + 1} of {changelogs.length}
                    </span>
                  )}
                </div>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => handleClose(true)}
                  className="h-8 w-8"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>

              {/* Content */}
              <div
                className="flex-1 overflow-y-auto p-6"
                style={{
                  scrollbarWidth: "thin",
                  scrollbarColor: "rgb(156 163 175) transparent",
                }}
              >
                <div className="space-y-4 pb-4">
                  <div>
                    <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2">
                      {currentChangelog.title}
                    </h2>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      {new Date(
                        currentChangelog.publishedAt
                      ).toLocaleDateString(
                        currentLanguage === "de" ? "de-DE" : "en-US",
                        {
                          year: "numeric",
                          month: "long",
                          day: "numeric",
                          hour: "2-digit",
                          minute: "2-digit",
                        }
                      )}
                    </p>
                  </div>

                  {/* Content rendering with code block support */}
                  <ReactMarkdown
                    className="markdown"
                    components={{
                      code: ({
                        inline,
                        children,
                        className,
                        ...props
                      }: any) => (
                        <CodeBlock
                          inline={inline}
                          className={className}
                          {...props}
                        >
                          {children}
                        </CodeBlock>
                      ),
                      pre: ({ children }: any) => <>{children}</>, // Let CodeBlock handle the pre wrapper
                    }}
                  >
                    {currentChangelog.content}
                  </ReactMarkdown>

                  {/* Metadata */}
                  {(currentChangelog.githubCommitSha ||
                    currentChangelog.deploymentId) && (
                    <div className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
                      <div className="flex flex-wrap gap-4 text-xs text-gray-500 dark:text-gray-400">
                        {currentChangelog.githubCommitSha && (
                          <div>
                            <span className="font-medium">Commit:</span>{" "}
                            <code className="bg-gray-100 dark:bg-gray-800 px-1 py-0.5 rounded">
                              {currentChangelog.githubCommitSha.substring(0, 8)}
                            </code>
                          </div>
                        )}
                        {currentChangelog.deploymentId && (
                          <div>
                            <span className="font-medium">Deployment:</span>{" "}
                            <code className="bg-gray-100 dark:bg-gray-800 px-1 py-0.5 rounded">
                              {currentChangelog.deploymentId}
                            </code>
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Footer */}
              <div className="flex items-center justify-between p-6 border-t border-gray-200 dark:border-gray-700">
                <div className="flex items-center gap-2">
                  {hasMultiple && currentIndex > 0 && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handlePrevious}
                      className="gap-2"
                    >
                      <ChevronLeft className="h-4 w-4" />
                      {t("common.previous")}
                    </Button>
                  )}
                </div>

                <div className="flex items-center gap-2">
                  <Button variant="outline" onClick={() => handleClose(true)}>
                    {t("common.dismiss")}
                  </Button>
                  <Button onClick={handleConfirm}>
                    {hasMultiple && currentIndex < changelogs.length - 1 ? (
                      <>
                        {t("common.next")}
                        <ChevronRight className="h-4 w-4 ml-2" />
                      </>
                    ) : (
                      t("common.confirm")
                    )}
                  </Button>
                </div>
              </div>
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
};

export default ChangelogModal;
