/**
 * Swiss Knowledge Hub Chatbot SDK - Performance Tests
 * 
 * Performance and load testing for the chatbot SDK
 */

// Performance Test Utilities
const PerformanceUtils = {
  // Measure function execution time
  measureTime: async (fn, label = 'Operation') => {
    const start = performance.now();
    const result = await fn();
    const end = performance.now();
    const duration = end - start;
    
    console.log(`${label}: ${duration.toFixed(2)}ms`);
    return { result, duration };
  },

  // Measure memory usage (if available)
  measureMemory: () => {
    if (performance.memory) {
      return {
        used: performance.memory.usedJSHeapSize,
        total: performance.memory.totalJSHeapSize,
        limit: performance.memory.jsHeapSizeLimit
      };
    }
    return null;
  },

  // Create load test with multiple concurrent requests
  loadTest: async (fn, concurrency = 10, iterations = 100) => {
    const results = [];
    const startTime = performance.now();
    
    for (let i = 0; i < iterations; i += concurrency) {
      const batch = [];
      for (let j = 0; j < concurrency && (i + j) < iterations; j++) {
        batch.push(fn());
      }
      
      const batchResults = await Promise.allSettled(batch);
      results.push(...batchResults);
    }
    
    const endTime = performance.now();
    const totalTime = endTime - startTime;
    
    const successful = results.filter(r => r.status === 'fulfilled').length;
    const failed = results.filter(r => r.status === 'rejected').length;
    
    return {
      totalTime,
      successful,
      failed,
      throughput: (successful / totalTime) * 1000, // requests per second
      averageTime: totalTime / iterations
    };
  },

  // Monitor resource usage over time
  monitorResources: (duration = 10000, interval = 1000) => {
    const measurements = [];
    const startTime = performance.now();
    
    return new Promise((resolve) => {
      const monitor = setInterval(() => {
        const currentTime = performance.now();
        const elapsed = currentTime - startTime;
        
        measurements.push({
          timestamp: elapsed,
          memory: PerformanceUtils.measureMemory(),
          timing: performance.timing ? {
            domContentLoaded: performance.timing.domContentLoadedEventEnd - performance.timing.navigationStart,
            loadComplete: performance.timing.loadEventEnd - performance.timing.navigationStart
          } : null
        });
        
        if (elapsed >= duration) {
          clearInterval(monitor);
          resolve(measurements);
        }
      }, interval);
    });
  }
};

// SDK Performance Tests
describe('SDK Performance Tests', () => {
  const TEST_CONFIG = {
    chatbotId: 'test-chatbot-id',
    apiKey: 'skh_test_key_1234567890abcdef'
  };

  test('SDK initialization performance', async () => {
    const { duration } = await PerformanceUtils.measureTime(async () => {
      // Mock SDK initialization
      return new Promise(resolve => {
        setTimeout(() => {
          resolve({ instanceId: 'test-instance' });
        }, Math.random() * 100); // Simulate variable load time
      });
    }, 'SDK Initialization');
    
    // Should initialize within 500ms
    expect(duration).toBeLessThan(500);
  });

  test('Configuration loading performance', async () => {
    const mockFetch = () => new Promise(resolve => {
      setTimeout(() => {
        resolve({
          ok: true,
          json: () => Promise.resolve({
            id: TEST_CONFIG.chatbotId,
            customization: { theme: { primaryColor: '#007bff' } }
          })
        });
      }, Math.random() * 200);
    });
    
    const { duration } = await PerformanceUtils.measureTime(async () => {
      const response = await mockFetch();
      return await response.json();
    }, 'Configuration Loading');
    
    // Should load configuration within 1 second
    expect(duration).toBeLessThan(1000);
  });

  test('Chat message performance', async () => {
    const mockChatRequest = () => new Promise(resolve => {
      setTimeout(() => {
        resolve({
          ok: true,
          json: () => Promise.resolve({
            response: 'Test response',
            sessionToken: 'session-123',
            sources: []
          })
        });
      }, Math.random() * 2000); // Simulate AI response time
    });
    
    const { duration } = await PerformanceUtils.measureTime(async () => {
      const response = await mockChatRequest();
      return await response.json();
    }, 'Chat Message');
    
    // Should respond within 5 seconds
    expect(duration).toBeLessThan(5000);
  });

  test('Multiple concurrent chat requests', async () => {
    const mockChatRequest = () => new Promise(resolve => {
      setTimeout(() => {
        resolve({
          response: 'Concurrent response',
          sessionToken: 'session-' + Math.random(),
          sources: []
        });
      }, Math.random() * 1000);
    });
    
    const results = await PerformanceUtils.loadTest(
      mockChatRequest,
      5, // 5 concurrent requests
      25  // 25 total requests
    );
    
    console.log('Concurrent Chat Test Results:', results);
    
    // Should handle at least 80% success rate
    expect(results.successful / (results.successful + results.failed)).toBeGreaterThan(0.8);
    
    // Should maintain reasonable throughput
    expect(results.throughput).toBeGreaterThan(1); // At least 1 request per second
  });

  test('Memory usage monitoring', async () => {
    const initialMemory = PerformanceUtils.measureMemory();
    
    // Simulate SDK operations
    const operations = [];
    for (let i = 0; i < 100; i++) {
      operations.push(new Promise(resolve => {
        setTimeout(() => {
          // Simulate DOM manipulation and event handling
          const element = document.createElement('div');
          element.innerHTML = `<p>Message ${i}</p>`;
          resolve(element);
        }, 10);
      }));
    }
    
    await Promise.all(operations);
    
    const finalMemory = PerformanceUtils.measureMemory();
    
    if (initialMemory && finalMemory) {
      const memoryIncrease = finalMemory.used - initialMemory.used;
      console.log(`Memory increase: ${(memoryIncrease / 1024 / 1024).toFixed(2)}MB`);
      
      // Should not increase memory by more than 10MB
      expect(memoryIncrease).toBeLessThan(10 * 1024 * 1024);
    }
  });

  test('DOM manipulation performance', async () => {
    const { duration } = await PerformanceUtils.measureTime(async () => {
      // Simulate adding 100 chat messages
      const container = document.createElement('div');
      
      for (let i = 0; i < 100; i++) {
        const message = document.createElement('div');
        message.className = 'skh-message user';
        message.textContent = `Message ${i}`;
        container.appendChild(message);
      }
      
      // Simulate scrolling to bottom
      container.scrollTop = container.scrollHeight;
      
      return container;
    }, 'DOM Manipulation (100 messages)');
    
    // Should complete within 100ms
    expect(duration).toBeLessThan(100);
  });

  test('CSS animation performance', async () => {
    const { duration } = await PerformanceUtils.measureTime(async () => {
      return new Promise(resolve => {
        const element = document.createElement('div');
        element.style.cssText = `
          position: fixed;
          bottom: 20px;
          right: 20px;
          width: 60px;
          height: 60px;
          background: #007bff;
          border-radius: 50%;
          transition: transform 0.3s ease;
        `;
        
        document.body.appendChild(element);
        
        // Trigger animation
        requestAnimationFrame(() => {
          element.style.transform = 'scale(1.1)';
          
          setTimeout(() => {
            element.style.transform = 'scale(1)';
            document.body.removeChild(element);
            resolve();
          }, 300);
        });
      });
    }, 'CSS Animation');
    
    // Should complete within 500ms
    expect(duration).toBeLessThan(500);
  });
});

// Load Testing Scenarios
describe('Load Testing Scenarios', () => {
  test('High-frequency message sending', async () => {
    const mockSendMessage = () => new Promise(resolve => {
      setTimeout(() => {
        resolve({ success: true, messageId: Math.random() });
      }, Math.random() * 100);
    });
    
    const results = await PerformanceUtils.loadTest(
      mockSendMessage,
      10, // 10 concurrent users
      100 // 100 messages total
    );
    
    console.log('High-frequency messaging results:', results);
    
    // Should handle high load with good success rate
    expect(results.successful).toBeGreaterThan(90);
    expect(results.throughput).toBeGreaterThan(5); // 5+ messages per second
  });

  test('Multiple chatbot instances', async () => {
    const createChatbotInstance = () => new Promise(resolve => {
      setTimeout(() => {
        resolve({
          instanceId: 'instance-' + Math.random(),
          initialized: true
        });
      }, Math.random() * 200);
    });
    
    const results = await PerformanceUtils.loadTest(
      createChatbotInstance,
      5,  // 5 concurrent initializations
      50  // 50 total instances
    );
    
    console.log('Multiple instances results:', results);
    
    // Should handle multiple instances efficiently
    expect(results.successful).toBeGreaterThan(45);
    expect(results.averageTime).toBeLessThan(300);
  });

  test('Stress test with resource monitoring', async () => {
    // Start resource monitoring
    const monitoringPromise = PerformanceUtils.monitorResources(5000, 500);
    
    // Run stress test
    const stressTest = async () => {
      const operations = [];
      
      for (let i = 0; i < 50; i++) {
        operations.push(
          new Promise(resolve => {
            setTimeout(() => {
              // Simulate heavy operations
              const data = new Array(1000).fill(0).map(() => Math.random());
              const processed = data.map(x => x * 2).filter(x => x > 1);
              resolve(processed.length);
            }, Math.random() * 100);
          })
        );
      }
      
      return Promise.all(operations);
    };
    
    const [stressResults, resourceData] = await Promise.all([
      stressTest(),
      monitoringPromise
    ]);
    
    console.log('Stress test completed:', stressResults.length, 'operations');
    console.log('Resource monitoring data:', resourceData);
    
    // Analyze resource usage
    if (resourceData.length > 0) {
      const memoryUsage = resourceData.map(d => d.memory?.used || 0);
      const maxMemory = Math.max(...memoryUsage);
      const minMemory = Math.min(...memoryUsage.filter(m => m > 0));
      
      console.log(`Memory range: ${(minMemory / 1024 / 1024).toFixed(2)}MB - ${(maxMemory / 1024 / 1024).toFixed(2)}MB`);
      
      // Memory should not grow excessively
      if (minMemory > 0) {
        expect(maxMemory / minMemory).toBeLessThan(2); // Less than 2x growth
      }
    }
  });
});

// Performance Benchmarks
const PERFORMANCE_BENCHMARKS = {
  sdkInitialization: 500,    // ms
  configurationLoad: 1000,   // ms
  chatResponse: 5000,        // ms
  domManipulation: 100,      // ms
  memoryIncrease: 10,        // MB
  successRate: 0.95,         // 95%
  throughput: 5              // requests/second
};

// Performance Report Generator
const generatePerformanceReport = (results) => {
  const report = {
    timestamp: new Date().toISOString(),
    benchmarks: PERFORMANCE_BENCHMARKS,
    results: results,
    recommendations: []
  };
  
  // Analyze results and generate recommendations
  if (results.averageResponseTime > PERFORMANCE_BENCHMARKS.chatResponse) {
    report.recommendations.push('Consider optimizing chat response time');
  }
  
  if (results.successRate < PERFORMANCE_BENCHMARKS.successRate) {
    report.recommendations.push('Improve error handling and retry logic');
  }
  
  if (results.memoryUsage > PERFORMANCE_BENCHMARKS.memoryIncrease) {
    report.recommendations.push('Investigate memory leaks and optimize DOM management');
  }
  
  return report;
};

// Export for use in other test files
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    PerformanceUtils,
    PERFORMANCE_BENCHMARKS,
    generatePerformanceReport
  };
}

console.log('Swiss Knowledge Hub Chatbot SDK - Performance Test Suite Loaded');
console.log('Performance Benchmarks:', PERFORMANCE_BENCHMARKS);
