"""
Webhook endpoints for handling external service callbacks.

This module handles webhooks from various external services including:
- Azure Video Indexer processing completion
- Other async processing services
"""

import logging
from fastapi import APIRouter, HTTPException, Request, status
from typing import Dict, Any
import json

logger = logging.getLogger(__name__)

router = APIRouter()


@router.post("/video-processing")
async def azure_video_indexer_webhook(request: Request):
    """
    Webhook endpoint for Azure Video Indexer processing completion notifications.
    
    This endpoint receives callbacks from Azure Video Indexer when video processing
    completes, allowing us to update job status and deliver results.
    
    Expected webhook payload from Azure Video Indexer:
    {
        "id": "video_id",
        "state": "Processed" | "Failed",
        "accountId": "account_id",
        "processingProgress": "100%",
        "moderationState": "OK",
        "reviewState": "None"
    }
    """
    try:
        # Get the raw request body
        body = await request.body()
        
        # Parse JSON payload
        try:
            webhook_data = json.loads(body.decode('utf-8'))
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON in webhook payload: {e}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid JSON payload"
            )
        
        
        # Extract video ID and state from webhook
        video_id = webhook_data.get("id")
        video_state = webhook_data.get("state", "").lower()
        
        if not video_id:
            logger.error("No video ID found in webhook payload")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Missing video ID in webhook payload"
            )
        
        # Import video processing service
        from src.services.video_processing_service import video_processing_service
        
        # Handle the webhook completion
        await video_processing_service.handle_webhook_completion(video_id, webhook_data)

        return {
            "status": "success",
            "message": f"Webhook processed for video {video_id}",
            "video_id": video_id,
            "state": video_state
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error processing Azure Video Indexer webhook: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Webhook processing failed: {str(e)}"
        )


@router.get("/video-processing/health")
async def webhook_health_check():
    """
    Health check endpoint for webhook service.
    
    This endpoint can be used by Azure Video Indexer or monitoring services
    to verify that the webhook endpoint is accessible and functioning.
    """
    return {
        "status": "healthy",
        "service": "video-processing-webhook",
        "timestamp": "2025-01-01T00:00:00Z"  # This will be updated with actual timestamp
    }


@router.post("/video-processing/test")
async def test_webhook(test_data: Dict[str, Any]):
    """
    Test endpoint for webhook functionality.
    
    This endpoint allows testing the webhook processing logic without
    requiring actual Azure Video Indexer callbacks.
    
    Example test payload:
    {
        "id": "test-video-id",
        "state": "Processed",
        "accountId": "test-account",
        "processingProgress": "100%"
    }
    """
    try:

        video_id = test_data.get("id", "test-video-id")
        
        # Import video processing service
        from src.services.video_processing_service import video_processing_service
        
        # Handle the test webhook
        await video_processing_service.handle_webhook_completion(video_id, test_data)
        
        return {
            "status": "success",
            "message": f"Test webhook processed for video {video_id}",
            "test_data": test_data
        }
        
    except Exception as e:
        logger.error(f"Error processing test webhook: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Test webhook failed: {str(e)}"
        )
