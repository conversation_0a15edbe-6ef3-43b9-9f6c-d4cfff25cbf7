"use client";

import React, { useState, useEffect } from "react";
import {
  X,
  Send,
  MessageCircle,
  CheckCircle,
  MoreVertical,
  AtSign,
  Edit,
  Trash2,
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";

import { SimpleMentionInput } from "./SimpleMentionInput";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuPortal,
} from "@/components/ui/dropdown-menu";

import toast from "react-hot-toast";
import { useLanguage } from "@/lib/language-context";

interface User {
  id: string;
  name: string;
  email: string;
  image?: string;
}

interface Comment {
  id: string;
  parentId?: string;
  content: string;
  author: User;
  status: "ACTIVE" | "RESOLVED" | "DELETED";
  mentions?: string[];
  createdAt: string;
  updatedAt: string;
  replies?: Comment[];
  isOptimistic?: boolean; // Flag for optimistic updates
}

interface CommentSidebarProps {
  messageId: string;
  chatId: string;
  currentUser: User;
  onClose: () => void;
}

export const CommentSidebar: React.FC<CommentSidebarProps> = ({
  messageId,
  chatId,
  currentUser,
  onClose,
}) => {
  const { t } = useLanguage();
  const [comments, setComments] = useState<Comment[]>([]);
  const [newComment, setNewComment] = useState("");
  const [commentMentions, setCommentMentions] = useState<string[]>([]);
  const [replyTo, setReplyTo] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [showResolved, setShowResolved] = useState(false);

  // Load comments
  useEffect(() => {
    loadComments();
  }, [messageId, showResolved]);

  const loadComments = async () => {
    try {
      setLoading(true);
      const response = await fetch(
        `/api/chat/${chatId}/messages/${messageId}/comments?includeResolved=${showResolved}`
      );

      if (response.ok) {
        const data = await response.json();
        setComments(data.comments);
      }
    } catch (error) {
      console.error("Error loading comments:", error);
      toast.error(t("commentSidebar.failedToLoadComments"));
    } finally {
      setLoading(false);
    }
  };

  const handleSubmitComment = async () => {
    if (!newComment.trim()) return;

    console.log("Submitting comment with mentions:", commentMentions);

    // Create optimistic comment for immediate UI update
    const optimisticComment: Comment = {
      id: `temp-${Date.now()}`, // Temporary ID
      content: newComment.trim(),
      author: currentUser,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      status: "ACTIVE",
      parentId: replyTo || undefined,
      replies: [],
      isOptimistic: true, // Flag to identify optimistic updates
    };

    // Store original comment content and mentions for potential rollback
    const originalComment = newComment;
    const originalMentions = [...commentMentions];
    const originalReplyTo = replyTo;

    try {
      setSubmitting(true);

      // Optimistic update: Add comment immediately to UI
      if (replyTo) {
        // Handle reply to existing comment
        setComments(prevComments =>
          prevComments.map(comment => {
            if (comment.id === replyTo || comment.parentId === replyTo) {
              // Find the parent comment and add reply
              const parentComment = comment.parentId ?
                prevComments.find(c => c.id === comment.parentId) || comment :
                comment;

              return {
                ...parentComment,
                replies: [...(parentComment.replies || []), optimisticComment]
              };
            }
            return comment;
          })
        );
      } else {
        // Handle new top-level comment
        setComments(prevComments => [...prevComments, optimisticComment]);
      }

      // Clear form immediately for better UX
      setNewComment("");
      setCommentMentions([]);
      setReplyTo(null);

      // Make API call
      const response = await fetch(
        `/api/chat/${chatId}/messages/${messageId}/comments`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            content: originalComment,
            parentId: originalReplyTo,
            mentions: originalMentions,
          }),
        }
      );

      if (response.ok) {
        const data = await response.json();
        const actualComment = data.comment;

        // Replace optimistic comment with actual comment from server
        setComments(prevComments => {
          if (originalReplyTo) {
            // Handle reply replacement
            return prevComments.map(comment => {
              if (comment.id === originalReplyTo || comment.parentId === originalReplyTo) {
                const parentComment = comment.parentId ?
                  prevComments.find(c => c.id === comment.parentId) || comment :
                  comment;

                return {
                  ...parentComment,
                  replies: (parentComment.replies || []).map(reply =>
                    reply.id === optimisticComment.id ? actualComment : reply
                  )
                };
              }
              return comment;
            });
          } else {
            // Handle top-level comment replacement
            return prevComments.map(comment =>
              comment.id === optimisticComment.id ? actualComment : comment
            );
          }
        });

        toast.success(t("commentSidebar.commentAdded"));
      } else {
        // Rollback optimistic update on failure
        setComments(prevComments => {
          if (originalReplyTo) {
            // Remove optimistic reply
            return prevComments.map(comment => {
              if (comment.id === originalReplyTo || comment.parentId === originalReplyTo) {
                const parentComment = comment.parentId ?
                  prevComments.find(c => c.id === comment.parentId) || comment :
                  comment;

                return {
                  ...parentComment,
                  replies: (parentComment.replies || []).filter(reply =>
                    reply.id !== optimisticComment.id
                  )
                };
              }
              return comment;
            });
          } else {
            // Remove optimistic top-level comment
            return prevComments.filter(comment => comment.id !== optimisticComment.id);
          }
        });

        // Restore form state
        setNewComment(originalComment);
        setCommentMentions(originalMentions);
        setReplyTo(originalReplyTo);

        toast.error(t("commentSidebar.failedToAddComment"));
      }
    } catch (error) {
      console.error("Error submitting comment:", error);

      // Rollback optimistic update on error
      setComments(prevComments => {
        if (originalReplyTo) {
          return prevComments.map(comment => {
            if (comment.id === originalReplyTo || comment.parentId === originalReplyTo) {
              const parentComment = comment.parentId ?
                prevComments.find(c => c.id === comment.parentId) || comment :
                comment;

              return {
                ...parentComment,
                replies: (parentComment.replies || []).filter(reply =>
                  reply.id !== optimisticComment.id
                )
              };
            }
            return comment;
          });
        } else {
          return prevComments.filter(comment => comment.id !== optimisticComment.id);
        }
      });

      // Restore form state
      setNewComment(originalComment);
      setCommentMentions(originalMentions);
      setReplyTo(originalReplyTo);

      toast.error(t("commentSidebar.failedToAddComment"));
    } finally {
      setSubmitting(false);
    }
  };

  const handleResolveComment = async (commentId: string, resolve: boolean) => {
    try {
      const response = await fetch(`/api/comments/${commentId}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          status: resolve ? "RESOLVED" : "ACTIVE",
        }),
      });

      if (response.ok) {
        await loadComments();
        toast.success(resolve ? t("commentSidebar.commentResolved") : t("commentSidebar.commentReopened"));
      } else {
        toast.error(t("commentSidebar.failedToUpdateComment"));
      }
    } catch (error) {
      console.error("Error updating comment:", error);
      toast.error(t("commentSidebar.failedToUpdateComment"));
    }
  };

  const handleDeleteComment = async (commentId: string) => {
    try {
      const response = await fetch(`/api/comments/${commentId}`, {
        method: "DELETE",
      });

      if (response.ok) {
        await loadComments();
        toast.success(t("commentSidebar.commentDeleted"));
      } else {
        toast.error(t("commentSidebar.failedToDeleteComment"));
      }
    } catch (error) {
      console.error("Error deleting comment:", error);
      toast.error(t("commentSidebar.failedToDeleteComment"));
    }
  };

  const handleEditComment = async (commentId: string, newContent: string) => {
    try {
      const response = await fetch(`/api/comments/${commentId}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          content: newContent,
        }),
      });

      if (response.ok) {
        await loadComments();
        toast.success(t("commentSidebar.commentUpdated"));
      } else {
        toast.error(t("commentSidebar.failedToUpdateComment"));
      }
    } catch (error) {
      console.error("Error updating comment:", error);
      toast.error(t("commentSidebar.failedToUpdateComment"));
      throw error;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString(undefined, {
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase()
      .slice(0, 2);
  };

  const renderCommentContent = (content: string) => {
    // Convert @Name mentions to styled spans
    const parts = content.split(/(@\w+)/g);

    return parts.map((part, index) => {
      const mentionMatch = part.match(/@(\w+)/);
      if (mentionMatch) {
        const [, name] = mentionMatch;
        return (
          <span
            key={index}
            className="inline-flex items-center px-2 py-1 mx-0.5 rounded-full text-xs font-medium bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-sm hover:shadow-md transition-shadow cursor-pointer"
            title={t("commentSidebar.mentionedUser", { name })}
          >
            <AtSign className="h-3 w-3 mr-1" />
            {name}
          </span>
        );
      }
      return part;
    });
  };

  const activeComments = comments.filter((c) => c.status === "ACTIVE");
  const resolvedComments = comments.filter((c) => c.status === "RESOLVED");

  return (
    <div
      style={{
        zIndex: 10000,
      }}
      className="fixed right-0 top-0 h-full w-96 bg-white dark:bg-gray-800 border-l border-gray-200 dark:border-gray-700 shadow-lg z-50"
    >
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center gap-2">
          <MessageCircle className="h-5 w-5 text-primary" />
          <h3 className="font-semibold text-gray-900 dark:text-gray-100">
            {t("commentSidebar.title")}
          </h3>
          <Badge variant="secondary">{activeComments.length}</Badge>
        </div>
        <Button variant="ghost" size="sm" onClick={onClose}>
          <X className="h-4 w-4" />
        </Button>
      </div>

      {/* Comments List */}
      <ScrollArea className="flex-1 h-[calc(100vh-200px)]">
        <div className="p-4 space-y-4">
          {loading ? (
            <div className="text-center text-gray-500 dark:text-gray-400">
              {t("commentSidebar.loadingComments")}
            </div>
          ) : (
            <>
              {/* Active Comments */}
              {activeComments.map((comment) => (
                <div
                  key={comment.id}
                  className="transition-all duration-300 ease-in-out"
                >
                  <CommentItem
                    comment={comment}
                    currentUser={currentUser}
                    onReply={(commentId) => setReplyTo(commentId)}
                    onResolve={() => handleResolveComment(comment.id, true)}
                    onDelete={() => handleDeleteComment(comment.id)}
                    onEdit={handleEditComment}
                    formatDate={formatDate}
                    getInitials={getInitials}
                    renderCommentContent={renderCommentContent}
                    isOptimistic={comment.isOptimistic}
                  />
                </div>
              ))}

              {/* Resolved Comments Toggle */}
              {resolvedComments.length > 0 && (
                <div className="pt-4 border-t border-gray-200 dark:border-gray-700">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowResolved(!showResolved)}
                    className="w-full justify-start"
                  >
                    <CheckCircle className="h-4 w-4 mr-2" />
                    {showResolved
                      ? t("commentSidebar.hideResolved", { count: resolvedComments.length })
                      : t("commentSidebar.showResolved", { count: resolvedComments.length })
                    }
                  </Button>

                  {showResolved && (
                    <div className="mt-2 space-y-2">
                      {resolvedComments.map((comment) => (
                        <div
                          key={comment.id}
                          className="transition-all duration-300 ease-in-out"
                        >
                          <CommentItem
                            comment={comment}
                            currentUser={currentUser}
                            onReply={(commentId) => setReplyTo(commentId)}
                            onResolve={() =>
                              handleResolveComment(comment.id, false)
                            }
                            onDelete={() => handleDeleteComment(comment.id)}
                            onEdit={handleEditComment}
                            formatDate={formatDate}
                            getInitials={getInitials}
                            renderCommentContent={renderCommentContent}
                            isResolved
                            isOptimistic={comment.isOptimistic}
                          />
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              )}

              {activeComments.length === 0 && resolvedComments.length === 0 && (
                <div className="text-center text-gray-500 dark:text-gray-400 py-8">
                  {t("commentSidebar.noCommentsYet")}
                </div>
              )}
            </>
          )}
        </div>
      </ScrollArea>

      {/* Comment Input */}
      <div className="p-4 border-t border-gray-200 dark:border-gray-700">
        {replyTo && (
          <div className="mb-2 bg-gray-50 dark:bg-gray-800 rounded text-xs text-gray-600 dark:text-gray-400">
            <div className="flex items-center justify-between">
              <span>
                💬 {t("commentSidebar.replyingTo", {
                  name: (() => {
                    // Find the comment in active comments, resolved comments, or their replies
                    const allComments = [...activeComments, ...resolvedComments];
                    const targetComment =
                      allComments.find((c) => c.id === replyTo) ||
                      allComments
                        .flatMap((c) => c.replies || [])
                        .find((r) => r.id === replyTo);
                    return targetComment?.author.name || t("commentSidebar.comment");
                  })()
                })}
              </span>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setReplyTo(null)}
                className="h-auto p-1 text-xs hover:bg-gray-200 dark:hover:bg-gray-700"
              >
                ✕
              </Button>
            </div>
          </div>
        )}

        <div className="flex items-center gap-2 w-full">
          <SimpleMentionInput
            value={newComment}
            onChange={(value, mentions) => {
              setNewComment(value);
              setCommentMentions(mentions);
            }}
            placeholder={t("commentSidebar.addCommentPlaceholder")}
            className="flex-1 min-h-[80px] resize-none w-full"
            onKeyDown={(e) => {
              if (e.key === "Enter" && (e.metaKey || e.ctrlKey)) {
                e.preventDefault();
                handleSubmitComment();
              }
            }}
          />
          <Button
            onClick={handleSubmitComment}
            disabled={!newComment.trim() || submitting}
            size="sm"
            className="self-end"
          >
            <Send className="h-4 w-4" />
          </Button>
        </div>

        <div className="mt-1 text-xs text-gray-500 dark:text-gray-400">
          {t("commentSidebar.pressToSend")}
        </div>
      </div>
    </div>
  );
};

// Comment Item Component
interface CommentItemProps {
  comment: Comment;
  currentUser: User;
  onReply: (commentId: string) => void; // Pass the comment ID to reply to
  onResolve: () => void;
  onDelete: () => void;
  onEdit: (commentId: string, newContent: string) => void;
  formatDate: (date: string) => string;
  getInitials: (name: string) => string;
  renderCommentContent?: any;
  isResolved?: boolean;
  isOptimistic?: boolean;
}

const CommentItem: React.FC<CommentItemProps> = ({
  comment,
  currentUser,
  onReply,
  onResolve,
  onDelete,
  onEdit,
  formatDate,
  getInitials,
  renderCommentContent,
  isResolved = false,
  isOptimistic = false,
}) => {
  const { t } = useLanguage();
  const isAuthor = comment.author.id === currentUser.id;
  const [isEditing, setIsEditing] = useState(false);
  const [editContent, setEditContent] = useState(comment.content);
  const [isSubmittingEdit, setIsSubmittingEdit] = useState(false);

  const handleStartEdit = () => {
    setIsEditing(true);
    setEditContent(comment.content);
  };

  const handleCancelEdit = () => {
    setIsEditing(false);
    setEditContent(comment.content);
  };

  const handleSubmitEdit = async () => {
    if (!editContent.trim() || editContent === comment.content) {
      handleCancelEdit();
      return;
    }

    setIsSubmittingEdit(true);
    try {
      await onEdit(comment.id, editContent.trim());
      setIsEditing(false);
    } catch (error) {
      console.error("Error editing comment:", error);
    } finally {
      setIsSubmittingEdit(false);
    }
  };

  return (
    <div className={`space-y-2 ${isResolved ? "opacity-60" : ""}`}>
      <div className="flex items-start gap-2">
        <Avatar className="h-6 w-6">
          <AvatarImage src={comment.author.image} />
          <AvatarFallback className="text-xs">
            {getInitials(comment.author.name)}
          </AvatarFallback>
        </Avatar>

        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2 mb-1">
            <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
              {comment.author.name}
            </span>
            <span className="text-xs text-gray-500 dark:text-gray-400">
              {formatDate(comment.createdAt)}
            </span>
            {isOptimistic && (
              <Badge variant="outline" className="text-xs text-blue-600 border-blue-300">
                {t("commentSidebar.sending")}
              </Badge>
            )}
            {isResolved && (
              <Badge variant="secondary" className="text-xs">
                {t("commentSidebar.resolved")}
              </Badge>
            )}
          </div>

          {isEditing ? (
            <div className="space-y-2">
              <SimpleMentionInput
                value={editContent}
                onChange={(value) => setEditContent(value)}
                placeholder={t("commentSidebar.editCommentPlaceholder")}
                className="min-h-[60px] text-sm"
                onKeyDown={(e) => {
                  if (e.key === "Enter" && (e.metaKey || e.ctrlKey)) {
                    e.preventDefault();
                    handleSubmitEdit();
                  } else if (e.key === "Escape") {
                    e.preventDefault();
                    handleCancelEdit();
                  }
                }}
              />
              <div className="flex items-center gap-2">
                <Button
                  onClick={handleSubmitEdit}
                  disabled={!editContent.trim() || isSubmittingEdit}
                  size="sm"
                  className="h-6 text-xs"
                >
                  {t("commentSidebar.save")}
                </Button>
                <Button
                  onClick={handleCancelEdit}
                  variant="ghost"
                  size="sm"
                  className="h-6 text-xs"
                >
                  {t("commentSidebar.cancel")}
                </Button>
              </div>
            </div>
          ) : (
            <div className="text-sm text-gray-700 dark:text-gray-300 whitespace-pre-wrap">
              {renderCommentContent(comment.content)}
            </div>
          )}

          <div className="flex items-center gap-2 mt-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onReply(comment.parentId || comment.id)}
              className="text-xs h-6"
              disabled={isOptimistic}
            >
              {t("commentSidebar.reply")}
            </Button>

            <Button
              variant="ghost"
              size="sm"
              onClick={onResolve}
              className="text-xs h-6"
              disabled={isOptimistic}
            >
              {isResolved ? t("commentSidebar.reopen") : t("commentSidebar.resolve")}
            </Button>

            {isAuthor && !isEditing && !isOptimistic && (
              <DropdownMenu modal={false}>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                    <MoreVertical className="h-3 w-3" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuPortal>
                  <DropdownMenuContent
                    align="end"
                    className="z-[99999] bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 shadow-lg min-w-[120px]"
                    sideOffset={5}
                  >
                    <DropdownMenuItem
                      onClick={handleStartEdit}
                      className="flex items-center gap-2 px-3 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer"
                    >
                      <Edit className="h-3 w-3" />
                      {t("commentSidebar.edit")}
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      onClick={onDelete}
                      className="flex items-center gap-2 px-3 py-2 text-sm text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20 cursor-pointer"
                    >
                      <Trash2 className="h-3 w-3" />
                      {t("commentSidebar.delete")}
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenuPortal>
              </DropdownMenu>
            )}
          </div>
        </div>
      </div>

      {/* Replies */}
      {comment.replies && comment.replies.length > 0 && (
        <div className="ml-8 space-y-2">
          {comment.replies.map((reply) => (
            <div
              key={reply.id}
              className="transition-all duration-300 ease-in-out"
            >
              <CommentItem
                comment={reply}
                currentUser={currentUser}
                onReply={onReply} // This will now correctly handle replies to replies
                onResolve={() => {}}
                onDelete={() => onDelete()}
                onEdit={onEdit}
                formatDate={formatDate}
                getInitials={getInitials}
                renderCommentContent={renderCommentContent}
                isOptimistic={reply.isOptimistic}
              />
            </div>
          ))}
        </div>
      )}
    </div>
  );
};
