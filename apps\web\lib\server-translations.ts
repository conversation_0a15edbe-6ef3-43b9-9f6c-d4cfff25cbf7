import fs from 'fs';
import path from 'path';

// Cache for loaded translations
const translationCache = new Map<string, Record<string, any>>();

/**
 * Load translations for a specific language
 */
function loadTranslations(language: string): Record<string, any> {
  if (translationCache.has(language)) {
    return translationCache.get(language)!;
  }

  try {
    const translationPath = path.join(process.cwd(), 'i18n', 'locales', `${language}.json`);
    const translationContent = fs.readFileSync(translationPath, 'utf-8');
    const translations = JSON.parse(translationContent);
    
    translationCache.set(language, translations);
    return translations;
  } catch (error) {
    console.warn(`Failed to load translations for language: ${language}`, error);
    // Fallback to English if available
    if (language !== 'en') {
      return loadTranslations('en');
    }
    return {};
  }
}

/**
 * Get a nested value from an object using dot notation
 */
function getNestedValue(obj: Record<string, any>, path: string): any {
  return path.split('.').reduce((current, key) => {
    return current && current[key] !== undefined ? current[key] : undefined;
  }, obj);
}

/**
 * Replace placeholders in a string with provided parameters
 */
function replacePlaceholders(text: string, params?: Record<string, any>): string {
  if (!params) return text;
  
  return text.replace(/\{(\w+)\}/g, (match, key) => {
    return params[key] !== undefined ? String(params[key]) : match;
  });
}

/**
 * Get translated message for server-side use
 * @param key - Translation key (e.g., 'auth.emailRequired')
 * @param language - Language code (default: 'en')
 * @param params - Parameters for placeholder replacement
 * @returns Translated message
 */
export function getServerTranslation(
  key: string, 
  language: string = 'en', 
  params?: Record<string, any>
): string {
  const translations = loadTranslations(language);
  const message = getNestedValue(translations, key);
  
  if (message === undefined) {
    console.warn(`Translation key not found: ${key} for language: ${language}`);
    // Try fallback to English if not already English
    if (language !== 'en') {
      return getServerTranslation(key, 'en', params);
    }
    // Return the key itself as fallback
    return key;
  }
  
  return replacePlaceholders(String(message), params);
}

/**
 * Get language from request headers
 */
export function getLanguageFromRequest(request: Request): string {
  try {
    const acceptLanguage = request.headers.get('accept-language');
    if (acceptLanguage) {
      // Parse accept-language header and get the first preferred language
      const languages = acceptLanguage
        .split(',')
        .map(lang => lang.split(';')[0].trim().toLowerCase());
      
      // Check if we support any of the preferred languages
      const supportedLanguages = ['en', 'de'];
      for (const lang of languages) {
        if (supportedLanguages.includes(lang)) {
          return lang;
        }
        // Check for language without region (e.g., 'en' from 'en-US')
        const baseLang = lang.split('-')[0];
        if (supportedLanguages.includes(baseLang)) {
          return baseLang;
        }
      }
    }
  } catch (error) {
    console.warn('Failed to parse accept-language header:', error);
  }
  
  // Default to English
  return 'en';
}

/**
 * Create a translation function for a specific language
 */
export function createServerTranslator(language: string) {
  return (key: string, params?: Record<string, any>) => {
    return getServerTranslation(key, language, params);
  };
}

/**
 * Clear translation cache (useful for development)
 */
export function clearTranslationCache(): void {
  translationCache.clear();
}
