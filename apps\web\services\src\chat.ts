import { apiUrl, fetchJson } from "..";

export const createChat = async (data, workspaceSlug = null) => {
  try {
    const headers = {
      "Content-Type": "application/json",
    };

    // Add workspace slug to headers if available
    if (workspaceSlug) {
      headers["x-workspace-slug"] = workspaceSlug;
    } else if (data.workspaceSlug) {
      headers["x-workspace-slug"] = data.workspaceSlug;
    }

    const response = await fetch(`${apiUrl}/chat/id`, {
      method: "POST",
      headers,
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error("Network response was not ok");
    }
    return await response.json();
  } catch (error) {
    console.log({ "Create Chat api calling error": error });
    return { error: "Error Creating The Chat" };
  }
};

export const updateChat = async (data, workspaceSlug = null) => {
  try {
    const headers = {
      "Content-Type": "application/json",
    };

    // Add workspace slug to headers if available
    if (workspaceSlug) {
      headers["x-workspace-slug"] = workspaceSlug;
    } else if (data.workspaceSlug) {
      headers["x-workspace-slug"] = data.workspaceSlug;
    }

    const response = await fetch(`${apiUrl}/chat/${data.id}`, {
      method: "PUT",
      headers,
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error("Network response was not ok");
    }
    return await response.json();
  } catch (error) {
    console.log({ "update chats api calling error": error });
    return { error: "Error Update The Chat" };
  }
};

export const getChat = async ({
  id = "",
  userId,
  tenantId,
  workspaceSlug = null,
}) => {
  try {
    if (id) {
      const response = await fetchJson(
        `${apiUrl}/chat/${id}?id=${id}&userId=${userId}&tenantId=${tenantId}`,
        userId,
        tenantId,
        workspaceSlug
      );

      return response;
    }
    const response = await fetchJson(
      `${apiUrl}/chat/id?userId=${userId}&tenantId=${tenantId}`,
      userId,
      tenantId,
      workspaceSlug
    );

    return response;
  } catch (error) {
    console.error("Error in getChat service:", error);
    throw error;
  }
};
export const getChatIdOrCreate = async ({ userId, tenantId }) => {
  try {
    const response = await fetchJson(
      `${apiUrl}/chat?userId=${userId}&tenantId=${tenantId}`,
      userId,
      tenantId
    );

    return response;
  } catch (error) {
    console.error("Error in getChat service:", error);
    throw error;
  }
};

export const deleteChat = async (
  id,
  userId = null,
  tenantId = null,
  workspaceSlug = null
) => {
  try {
    const url = `${apiUrl}/chat/${id}?id=${id}`;
    const headers = {};

    // Add headers if available
    if (userId) {
      headers["x-user-id"] = userId;
    }

    if (tenantId) {
      headers["x-tenant-id"] = tenantId;
    }

    if (workspaceSlug) {
      headers["x-workspace-slug"] = workspaceSlug;
    }

    const response = await fetch(url, {
      method: "DELETE",
      headers,
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Network response was not ok");
    }
    return await response.json();
  } catch (error) {
    console.log({ "delete Chat api error": error });
    return { error: error.message || "Error deleting Chat" };
  }
};
