import { NextResponse } from "next/server";
import { getToken } from "next-auth/jwt";
import db from "@/lib/shared-db";

/**
 * GET /api/roles/[id]/workspaces
 * Get all workspaces assigned to a custom role
 */
export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const roleId = params.id;
    if (!roleId) {
      return NextResponse.json(
        { error: "Role ID is required" },
        { status: 400 }
      );
    }

    const token = await getToken({ req: request as any });
    if (!token) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get the role
    const customRole = await db.customRole.findUnique({
      where: { id: roleId },
    });

    if (!customRole) {
      return NextResponse.json({ error: "Role not found" }, { status: 404 });
    }

    // Verify user has access to this tenant
    const membership = await db.membership.findFirst({
      where: {
        userId: token.sub,
        tenantId: customRole.tenantId,
      },
    });

    if (!membership) {
      return NextResponse.json(
        { error: "You don't have access to this tenant" },
        { status: 403 }
      );
    }

    // Get all workspaces assigned to this role
    const customRoleWorkspaces = await db.customRoleWorkspace.findMany({
      where: {
        customRoleId: roleId,
      },
      include: {
        workspace: true,
      },
    });

    const workspaces = customRoleWorkspaces.map((crw: any) => crw.workspace);

    return NextResponse.json({ workspaces });
  } catch (error) {
    console.error("Error fetching role workspaces:", error);
    return NextResponse.json(
      { error: "Failed to fetch role workspaces" },
      { status: 500 }
    );
  }
}

/**
 * POST /api/roles/[id]/workspaces
 * Assign workspaces to a custom role
 */
export async function POST(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const roleId = params.id;
    if (!roleId) {
      return NextResponse.json(
        { error: "Role ID is required" },
        { status: 400 }
      );
    }

    const body = await request.json();
    const { workspaceIds } = body;

    if (!workspaceIds || !Array.isArray(workspaceIds)) {
      return NextResponse.json(
        { error: "Workspace IDs are required" },
        { status: 400 }
      );
    }

    // Get the role
    const customRole = await db.customRole.findUnique({
      where: { id: roleId },
    });

    if (!customRole) {
      return NextResponse.json({ error: "Role not found" }, { status: 404 });
    }

    // Get all workspaces to verify they exist and belong to the tenant
    const workspaces = await db.workspace.findMany({
      where: {
        id: { in: workspaceIds },
        tenantId: customRole.tenantId,
      },
    });

    if (workspaces.length !== workspaceIds.length) {
      return NextResponse.json(
        {
          error:
            "One or more workspaces not found or do not belong to this tenant",
        },
        { status: 400 }
      );
    }

    // First, delete existing workspace associations
    await db.customRoleWorkspace.deleteMany({
      where: {
        customRoleId: roleId,
      },
    });

    // Create new workspace associations
    for (const workspace of workspaces) {
      await db.customRoleWorkspace.create({
        data: {
          customRoleId: roleId,
          workspaceId: workspace.id,
        },
      });
    }

    // Now, update all workspace members that use this role
    // Get all members with this role (both direct members and group members)
    const membersWithRole = await db.membership.findMany({
      where: {
        customRoleId: roleId,
      },
    });

    // Get all groups with this role
    const groupsWithRole = await db.group.findMany({
      where: {
        customRoleId: roleId,
      },
      include: {
        groupMembers: true,
      },
    });

    // Collect all user IDs from groups
    const groupMemberUserIds = groupsWithRole.flatMap((group: any) =>
      group.groupMembers.map((member: any) => member.userId)
    );

    // Combine direct members and group members
    const allUserIds = [
      ...membersWithRole.map((member: any) => member.userId),
      ...groupMemberUserIds,
    ];

    // Get unique user IDs
    const uniqueUserIds = [...new Set(allUserIds)];

    // Get all workspaces that were previously assigned to this role but are no longer
    const previousWorkspaces = await db.workspaceMember.findMany({
      where: {
        customRoleId: roleId,
        NOT: {
          workspaceId: {
            in: workspaces.map((w: any) => w.id),
          },
        },
      },
      select: {
        workspaceId: true,
        userId: true,
        id: true,
      },
      distinct: ["workspaceId", "userId"],
    });

    // Remove access to workspaces that are no longer assigned to this role
    for (const prevWorkspace of previousWorkspaces) {
      // Only remove if the user is in our list of users with this role
      if (uniqueUserIds.includes(prevWorkspace.userId)) {
        // Check if the user has access to this workspace through another role or direct membership
        const hasOtherAccess = await db.workspaceMember.findFirst({
          where: {
            userId: prevWorkspace.userId,
            workspaceId: prevWorkspace.workspaceId,
            NOT: {
              customRoleId: roleId,
            },
          },
        });

        if (!hasOtherAccess) {
          // Remove the workspace member entry
          await db.workspaceMember.delete({
            where: {
              id: prevWorkspace.id,
            },
          });
        } else {
          // Update to remove the custom role reference
          await db.workspaceMember.update({
            where: {
              id: prevWorkspace.id,
            },
            data: {
              customRoleId: null,
            },
          });
        }
      }
    }

    // For each member with this role, ensure they have access to all current workspaces
    for (const userId of uniqueUserIds) {
      // Get the user's membership
      const membership = await db.membership.findFirst({
        where: {
          userId,
          tenantId: customRole.tenantId,
        },
      });

      if (membership) {
        for (const workspace of workspaces) {
          // Check if this member already has access to this workspace
          const existingMember = await db.workspaceMember.findFirst({
            where: {
              userId,
              workspaceId: workspace.id,
            },
          });

          if (!existingMember) {
            // Add this member to the workspace with the same role
            await db.workspaceMember.create({
              data: {
                userId,
                workspaceId: workspace.id,
                membershipId: membership.id,
                customRoleId: roleId,
                role: membership.role,
              },
            });
          } else if (existingMember.customRoleId !== roleId) {
            // Update the existing member to use this role
            await db.workspaceMember.update({
              where: {
                id: existingMember.id,
              },
              data: {
                customRoleId: roleId,
              },
            });
          }
        }
      }
    }

    return NextResponse.json({
      message: "Workspaces assigned to role successfully",
      workspaces,
    });
  } catch (error) {
    console.error("Error assigning workspaces to role:", error);
    return NextResponse.json(
      { error: "Failed to assign workspaces to role" },
      { status: 500 }
    );
  }
}
