"""
CopilotKit Chat API Endpoint for Swiss Knowledge Hub

This endpoint provides the new high-performance agentic RAG system
using CopilotKit with intelligent tool selection and parallel execution.
"""
import io
from fastapi import APIRouter, Depends, File, HTTPException, UploadFile, status
from typing import Dict, Any, Optional, List
from pydantic import BaseModel
from src.utils.markitdown_converter import MarkitdownConverter
from src.services.copilotkit_rag_service import copilotkit_rag_service
from src.services.azure_video_indexer import azure_video_indexer_service
from src.api.deps import get_db
from src.api.auth import get_current_user
from starlette.responses import StreamingResponse
import json
import datetime
import logging
import aiohttp
import base64
import os
from bson import ObjectId

# Try to import PIL, but handle gracefully if not available
try:
    from PIL import Image
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False
    logging.warning("PIL not available - image processing will be limited")

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

router = APIRouter()


class JSONEncoder(json.JSONEncoder):
    """Custom JSON encoder that handles MongoDB ObjectIds and other non-serializable types."""

    def default(self, obj):
        if isinstance(obj, ObjectId):
            return str(obj)
        elif isinstance(obj, datetime.datetime):
            return obj.isoformat()
        elif hasattr(obj, '__dict__'):
            # Handle custom objects by converting to dict
            return obj.__dict__
        return super().default(obj)


def safe_json_dumps(obj, **kwargs):
    """Safely serialize objects to JSON, handling ObjectIds and other types."""
    try:
        return json.dumps(obj, cls=JSONEncoder, ensure_ascii=False, **kwargs)
    except Exception as e:
        logger.error(f"JSON serialization error: {e}")
        # Fallback: try to clean the object
        cleaned_obj = clean_for_json(obj)
        return json.dumps(cleaned_obj, ensure_ascii=False, **kwargs)


def clean_for_json(obj):
    """Recursively clean an object to make it JSON serializable."""
    if isinstance(obj, ObjectId):
        return str(obj)
    elif isinstance(obj, datetime.datetime):
        return obj.isoformat()
    elif isinstance(obj, dict):
        return {key: clean_for_json(value) for key, value in obj.items()}
    elif isinstance(obj, list):
        return [clean_for_json(item) for item in obj]
    elif hasattr(obj, '__dict__'):
        return clean_for_json(obj.__dict__)
    else:
        return obj

class CopilotKitChatQuery(BaseModel):
    """Request model for CopilotKit chat queries."""
    question: str
    stream: bool = True
    previous_message: Optional[str] = None
    image_context: Optional[str] = None
    audio_context: Optional[str] = None  # Audio transcription context
    video_context: Optional[str] = None  # Video analysis context (NEW)
    search_mode: str = "hybrid"  # "internal", "web", "hybrid", "deep_research", "mcp"
    include_web_results: bool = False
    max_tokens: Optional[int] = None
    temperature: Optional[float] = None
    images: Optional[List[str]] = None  # Base64 encoded images
    audio_files: Optional[List[str]] = None  # NEW: Audio file URLs for transcription
    deep_answer: bool = False  # NEW: Use DeepSeek R1 for expanded research-grade answers
    language: str = "en"  # Language code for localized responses (en, de)


class CopilotKitChatResponse(BaseModel):
    """Response model for CopilotKit chat queries."""
    answer: str
    title: str
    sources: List[Dict[str, Any]]
    tools_used: List[str]
    elapsed_time: float
    thinking: Optional[str] = None  # For DeepSeek R1 compatibility
    status: int = 200

@router.post("/chat", response_model=Dict[str, Any], status_code=200)
async def copilotkit_chat(
    query: CopilotKitChatQuery,
    current_user: str,
    user_name: str,
    tenant_id: str,
    chat_id: Optional[str] = None,  # NEW: For persistent memory and image context
    db = Depends(get_db),
):
    """
    Chat with the CopilotKit agentic RAG system.
    Provides high-performance document retrieval with intelligent tool selection.
    Enhanced with proper image context handling and persistent memory.
    """
    # Set the database client
    copilotkit_rag_service.db_client = db

    try:
        image_context = ""
        audio_context = ""
        video_context = ""
        logger.info(f"CopilotKit chat request from user {current_user}: {query.question}")

        # Enhanced image and audio context handling
        enhanced_question = query.question

        # Step 1: Process new images if provided
        if query.images and len(query.images) > 0 and not query.image_context:
            logger.info(f"Processing {len(query.images)} new images")
            try:
                # Always process new images to get fresh context
                new_image_context = await _analyze_images(query.images)
                if new_image_context:
                    image_context = new_image_context
                    enhanced_question = f"{query.question}\n\n Attached Document Analysis:\n{image_context}"
                    logger.info("Enhanced question with new image analysis")
                else:
                    logger.warning("Image analysis returned empty context")
            except Exception as e:
                logger.error(f"Image analysis failed: {e}")
                # Continue without image context

        # Step 2: Use provided image context if no new images
        elif query.image_context:
            logger.info("Using provided image context from previous message")
            image_context = query.image_context
            enhanced_question = f"{query.question}\n\nAttached Document Analysis:\n{image_context}"

        # Step 3: Process new audio files if provided
        if query.audio_files and len(query.audio_files) > 0 and not query.audio_context:
            logger.info(f"Processing {len(query.audio_files)} new audio files")
            try:
                # Process audio files for transcription
                # Note: This would require the audio files to be uploaded first
                # For now, we'll use the provided audio context
                logger.info("Audio file processing not yet implemented - use audio_context field")
            except Exception as e:
                logger.error(f"Audio processing failed: {e}")
                # Continue without audio context

        # Step 4: Use provided audio context if available
        if query.audio_context:
            logger.info("Using provided audio context")
            audio_context = query.audio_context

            # Add clear instruction to analyze the audio content regardless of interface language
            audio_instruction = "Please analyze the following audio transcription content. Respond in the same language as the user's question, but make sure to process and understand the audio content regardless of its language:"

            if image_context:
                enhanced_question = f"{enhanced_question}\n\n{audio_instruction}\n\nAudio Transcription:\n{audio_context}"
            else:
                enhanced_question = f"{query.question}\n\n{audio_instruction}\n\nAudio Transcription:\n{audio_context}"

            logger.info(f"Enhanced question with audio context (length: {len(enhanced_question)} chars)")
            logger.info(f"Enhanced question preview: {enhanced_question[:300]}...")

        # Step 5: Use provided video context if available
        if query.video_context:
            logger.info("Using provided video context")
            video_context = query.video_context
            if image_context or audio_context:
                enhanced_question = f"{enhanced_question}\n\nVideo Analysis:\n{video_context}"
            else:
                enhanced_question = f"{query.question}\n\nVideo Analysis:\n{video_context}"

        # Determine search mode and web results inclusion
        search_mode = query.search_mode
        include_web_results = query.include_web_results

        # Handle deep_research mode - map to hybrid with enhanced capabilities
        if search_mode == "deep_research":
            search_mode = "hybrid"  # Use hybrid search for comprehensive coverage
            include_web_results = True  # Always include web results for deep research
            # deep_answer flag is already set from query.deep_answer

        # Auto-detect web search need if not explicitly set
        if search_mode == "hybrid" and not include_web_results:
            web_indicators = ["current", "recent", "latest", "news", "today", "2024", "2025"]
            include_web_results = any(indicator in query.question.lower() for indicator in web_indicators)

        # Execute query with CopilotKit agent
        if query.stream:
            return StreamingResponse(
                _stream_copilotkit_response(
                    enhanced_question,
                    current_user,
                    tenant_id,
                    user_name,
                    search_mode,
                    include_web_results,
                    query.previous_message,
                    chat_id,  # Pass chat_id for persistent memory
                    query.deep_answer,  # Pass deep_answer flag
                    query.language,  # Pass language for localization

                ),
                media_type="text/plain",
                headers={
                    "Cache-Control": "no-cache",
                    "Connection": "keep-alive",
                    "X-Accel-Buffering": "no"  # Disable nginx buffering
                }
            )
        else:
            # For non-streaming, get the first (and only) result from the generator
            async for result in copilotkit_rag_service.query_workspace(
                user_id=current_user,
                question=enhanced_question,
                tenant_id=tenant_id,
                user_name=user_name,
                search_mode=search_mode,
                include_web_results=include_web_results,
                stream=False,
                previous_message=query.previous_message,
                chat_id=chat_id,  # Pass chat_id for persistent memory
                deep_answer=query.deep_answer,  # Pass deep_answer flag
                language=query.language,  # Pass language for localization

            ):
                # Enhance result with image context metadata if new images were processed
                if query.images and image_context:
                    if "metadata" not in result:
                        result["metadata"] = {}
                    result["metadata"]["image_context"] = image_context
                    result["metadata"]["has_new_images"] = True
                    result["metadata"]["image_count"] = len(query.images)
                    logger.info("Added image context to response metadata")

                return result

    except Exception as e:
        logger.error(f"Error in CopilotKit chat: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"CopilotKit chat failed: {str(e)}"
        )

@router.post("/image-analysis", response_model=Dict[str, Any], status_code=200)
async def copilotkit_image_analysis(
    images: List[UploadFile] = File(...)
):
    """
    Analyze images and return image context.
    """

    try:
        image_context = ""
        # Step 1: Process new images if provided
        try:
            # Always process new images to get fresh context
            new_image_context = await _analyze_uploaded_images(images)
            if new_image_context:
                image_context = new_image_context
                logger.info("Enhanced question with new image analysis")
            else:
                logger.warning("Image analysis returned empty context")
            return {"image_context": image_context}

        except Exception as e:
            logger.error(f"Image analysis failed: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Image analysis failed: {str(e)}"
            )

    except Exception as e:
        logger.error(f"Error in CopilotKit chat: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"CopilotKit chat failed: {str(e)}"
        )

from pydantic import BaseModel
from typing import List

class AudioFileInfo(BaseModel):
    url: str
    name: str
    type: str
    size: int

class AudioAnalysisRequest(BaseModel):
    audio_files: List[AudioFileInfo]

class VideoFileInfo(BaseModel):
    url: str
    name: str
    type: str
    size: int

class VideoAnalysisRequest(BaseModel):
    video_files: List[VideoFileInfo]

@router.post("/audio-analysis", response_model=Dict[str, Any], status_code=200)
async def copilotkit_audio_analysis(
    request: AudioAnalysisRequest,
    current_user=Depends(get_current_user),
    db=Depends(get_db)
):
    """
    Start asynchronous audio analysis using Azure Video Indexer.

    This endpoint immediately returns a job ID for tracking the processing status.
    Audio processing happens in the background to avoid timeout issues.
    """
    try:
        logger.info(f"Audio analysis request with {len(request.audio_files)} files")

        if not request.audio_files:
            return {
                "job_id": None,
                "message": "No audio files provided",
                "status": "failed"
            }

        # Log the request details for debugging
        logger.info("Audio analysis request details:")
        for i, audio_file in enumerate(request.audio_files):
            logger.info(f"  File {i+1}: {audio_file.name} ({audio_file.type}, {audio_file.size} bytes)")
            logger.info(f"    URL: {audio_file.url}")

        logger.info(f"Starting async audio analysis for {len(request.audio_files)} files")

        # Import the audio processing service
        from src.services.audio_processing_service import audio_processing_service

        # Set database client
        audio_processing_service.db_client = db

        # Convert audio files to the format expected by the service
        audio_files = []
        for audio_file in request.audio_files:
            audio_files.append({
                "url": audio_file.url,
                "name": audio_file.name,
                "type": audio_file.type,
                "size": audio_file.size
            })

        # Create async processing job
        job_id = await audio_processing_service.create_audio_processing_job(
            audio_files=audio_files,
            user_id=current_user.get("userId"),
            tenant_id=current_user.get("organizationId")
        )

        logger.info(f"Audio processing job {job_id} created successfully")

        return {
            "job_id": job_id,
            "message": "Audio analysis started successfully",
            "status": "processing",
            "files_submitted": len(request.audio_files)
        }

    except Exception as e:
        logger.error(f"Error in CopilotKit audio analysis: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Audio analysis failed: {str(e)}"
        )


@router.get("/audio-analysis/{job_id}/status")
async def get_audio_analysis_status(job_id: str, current_user=Depends(get_current_user), db=Depends(get_db)):
    """
    Get the status of an audio analysis job.

    Args:
        job_id: The job ID returned from the audio-analysis endpoint

    Returns:
        Job status information including progress and results
    """
    try:
        from src.services.audio_processing_service import audio_processing_service

        # Set database client
        audio_processing_service.db_client = db

        job_status = await audio_processing_service.get_job_status(job_id)

        if not job_status:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Audio analysis job {job_id} not found"
            )

        # Safe check for audioContext - handle None values properly
        audio_context = job_status.get("audioContext")
        if not isinstance(audio_context, str):
            audio_context = str(audio_context) if audio_context is not None else ""

        return {
            "job_id": job_status["jobId"],
            "status": job_status["status"],
            "audio_context": audio_context,
            "error_message": job_status.get("errorMessage"),
            "created_at": job_status.get("createdAt"),
            "updated_at": job_status.get("updatedAt"),
            "completed_at": job_status.get("completedAt"),
            "files_processed": job_status.get("filesProcessed", 0)
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting audio analysis status for job {job_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get audio analysis status: {str(e)}"
        )


@router.post("/video-analysis")
async def copilotkit_video_analysis(request: VideoAnalysisRequest, current_user=Depends(get_current_user), db=Depends(get_db)):
    """
    Start asynchronous video analysis using Azure Video Indexer.

    This endpoint immediately returns a job ID for tracking the processing status.
    Video processing happens in the background to avoid timeout issues.

    Returns:
        - job_id: Unique identifier for tracking the processing job
        - status: Initial job status ("pending")
        - message: Information about the job creation
    """
    try:
        if not request.video_files:
            logger.warning("No video files provided in request")
            return {"job_id": None, "status": "failed", "message": "No video files provided"}

        logger.info(f"Starting async video analysis for {len(request.video_files)} files")

        # Import the video processing service
        from src.services.video_processing_service import video_processing_service

        # Set database client
        video_processing_service.db_client = db

        # Convert video files to the format expected by the service
        video_files = []
        for video_file in request.video_files:
            video_files.append({
                "url": video_file.url,
                "name": video_file.name,
                "type": video_file.type,
                "size": video_file.size
            })

        # Create async processing job
        job_id = await video_processing_service.create_video_processing_job(
            video_files=video_files,
            user_id=current_user.get("userId"),
            tenant_id=current_user.get("organizationId")
        )

        logger.info(f"Video analysis job created: {job_id}")

        return {
            "job_id": job_id,
            "status": "pending",
            "message": "Video analysis job created successfully. Processing started in background.",
            "files_queued": len(request.video_files),
            "estimated_time": "5-15 minutes depending on video length and complexity"
        }

    except Exception as e:
        logger.error(f"Error creating video analysis job: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create video analysis job: {str(e)}"
        )


@router.get("/video-analysis/{job_id}/status")
async def get_video_analysis_status(job_id: str, current_user=Depends(get_current_user), db=Depends(get_db)):
    """
    Get the status of a video analysis job.

    Args:
        job_id: The job ID returned from the video-analysis endpoint

    Returns:
        Job status information including progress and results
    """
    try:
        from src.services.video_processing_service import video_processing_service

        # Set database client
        video_processing_service.db_client = db

        job_status = await video_processing_service.get_job_status(job_id)

        if not job_status:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Video analysis job {job_id} not found"
            )

        # Safe check for videoContext - handle None values properly
        video_context = job_status.get("videoContext")
        if not isinstance(video_context, str):
            video_context = str(video_context) if video_context is not None else ""

        if job_status.get("status") == "failed" or video_context.lower().endswith("processing failed"):
            raise HTTPException(
                status_code=500,
                detail=job_status.get("errorMessage") or video_context or "Video processing failed",
            )

        logger.info(f"Video analysis job status: {job_status}")
        return job_status

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting video analysis job status: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get job status: {str(e)}"
        )
async def _stream_copilotkit_response(
    question: str,
    user_id: str,
    tenant_id: str,
    user_name: str,
    search_mode: str,
    include_web_results: bool,
    previous_message: Optional[str] = None,
    chat_id: Optional[str] = None,  # NEW: For persistent memory and image context
    deep_answer: bool = False,  # NEW: Use DeepSeek R1 for expanded answers
    language: str = "en",  # NEW: Language code for localization

):
    """Stream the CopilotKit agent response with enhanced image context handling."""
    try:
        # Get the streaming generator from the service (don't await for streaming)
        stream_generator = copilotkit_rag_service.query_workspace(
            user_id=user_id,
            question=question,
            tenant_id=tenant_id,
            user_name=user_name,
            search_mode=search_mode,
            include_web_results=include_web_results,
            stream=True,
            previous_message=previous_message,
            chat_id=chat_id,  # Pass chat_id for persistent memory
            deep_answer=deep_answer,  # Pass deep_answer flag
            language=language,  # Pass language for localization
        )

        # Now iterate over the async generator
        async for chunk in stream_generator:
            try:
                # Enhance final chunk with image context metadata if present
                if chunk.get("done"):
                    if "metadata" not in chunk:
                        chunk["metadata"] = {}
                    chunk["metadata"]["has_image_context"] = True

                # Format chunk for streaming
                chunk_data = safe_json_dumps(chunk)
                sse_data = f"data: {chunk_data}\n\n"
                yield sse_data

                # Force flush for real-time streaming
                import asyncio
                await asyncio.sleep(0)  # Allow other tasks to run

                # End stream on completion
                if chunk.get("done"):
                    break

            except Exception as e:
                logger.error(f"Error formatting chunk: {e}")
                # Send error chunk
                error_chunk = {
                    "error": f"Chunk formatting failed: {str(e)}",
                    "done": True
                }
                yield f"data: {safe_json_dumps(error_chunk)}\n\n"
                break

    except Exception as e:
        logger.error(f"Error in streaming CopilotKit response: {e}")
        error_chunk = {
            "error": f"Streaming failed: {str(e)}",
            "answer": "",
            "title": question[:50] + "..." if len(question) > 50 else question,
            "sources": [],
            "done": True
        }
        yield f"data: {safe_json_dumps(error_chunk)}\n\n"

async def _analyze_uploaded_images(images: List[str]) -> str:
    """Analyze uploaded images using Azure Vision API with proper format detection."""
    if not images:
        return ""

    try:
        # Process all images and combine descriptions
        image_descriptions = []
        async with aiohttp.ClientSession() as session:
            for i, image_data in enumerate(images[:5]):  # Limit to first 3 images

                fileType = image_data.content_type
                if fileType in ["image/jpg", "image/jpeg", "image/png", "image/gif", "image/bmp", "image/webp"]:
                    logger.info(f"Analyzing image {i+1} of type {fileType}")
                    try:
                        contents = await image_data.read()
                        if PIL_AVAILABLE:
                            img = Image.open(io.BytesIO(contents))
                            # Convert image to base64 for API calls if needed
                            buffered = io.BytesIO()
                            img.save(buffered, format=img.format or "JPEG")
                            img_str = base64.b64encode(buffered.getvalue()).decode()
                            img_format = (img.format or "png").lower()
                        else:
                            # Fallback when PIL is not available
                            img_str = base64.b64encode(contents).decode()
                            img_format = "jpeg"  # Default format

                        # Configure the client based on the model type
                        endpoint = f"{os.getenv('AZURE_VISION_API_ENDPOINT')}/openai/deployments/{os.getenv('AZURE_VISION_MODEL')}/chat/completions?api-version=2024-02-15-preview"
                        headers = {
                            "Content-Type": "application/json",
                            "api-key": os.getenv("DEEPSEEK_API_KEY")
                        }
                        messages = [
                            {"role": "system", "content":[
                                {"type": "text", "text": "You are an AI assistant that provides detailed descriptions of images."}
                            ]},
                            {
                                "role": "user",
                                "content": [
                                    {"type": "text", "text": "Provide a detailed description of this image. Include all visible elements, text, and context."},
                                    {"type": "image_url", "image_url": {"url":f"data:image/{img_format};base64,{img_str}"}}
                                ]
                            }
                        ]

                        data = {
                            "messages": messages,
                            "model": os.getenv('AZURE_VISION_MODEL',"mistral-medium-2505"),
                            "temperature": 0.7,
                        }
                        async with session.post(endpoint, headers=headers, json=data) as response:
                            if response.status == 200:
                                result = await response.json()
                                description = result["choices"][0]["message"]["content"]
                                image_descriptions.append(f"Image {i+1}: {description}")
                                logger.info(f"Image {i+1} analysis completed successfully")
                            else:
                                try:
                                    error_body = await response.json()
                                except aiohttp.ContentTypeError:
                                    error_body = await response.text()
                                logger.error(f"Image {i+1} analysis failed with status {response.status}: {error_body}")
                                image_descriptions.append(f"Image {i+1}: Analysis failed - {error_body}")

                    except Exception as e:
                        logger.error(f"Error analyzing image {i+1}: {e}")
                        image_descriptions.append(f"Image {i+1}: Error processing image - {str(e)}")
                else:

                    contents = await image_data.read()

                    file_like = io.BytesIO(contents)
                    file_like.seek(0)
                    markitdown_converter = MarkitdownConverter()
                    documents = markitdown_converter.extract_text(file_like)
                    if documents:
                        image_descriptions.append(f"{fileType} {i+1}: {documents}")
                    else:
                        image_descriptions.append(f"{fileType} {i+1}: Error processing image")

            # Combine all image descriptions
            if image_descriptions:
                combined_description = "\n".join(image_descriptions)
                logger.info(f"Successfully analyzed {len(image_descriptions)} images")
                return combined_description
            else:
                logger.warning("No images were successfully analyzed")
                return ""

    except Exception as e:
        logger.error(f"Error in image analysis pipeline: {e}")
        return ""

async def _analyze_images(images: List[str]) -> str:
    """Analyze uploaded images using Azure Vision API with proper format detection."""
    if not images:
        return ""

    try:
        # Process all images and combine descriptions
        image_descriptions = []

        for i, image_data in enumerate(images[:5]):  # Limit to first 3 images
            try:
                async with aiohttp.ClientSession() as session:
                    async with session.get(image_data) as img_response:
                        if img_response.status != 200:
                            logger.error(f"Failed to fetch image {i+1} from URL: {image_data}")
                            image_descriptions.append(f"Image {i+1}: Failed to download image.")
                            continue
                        img_bytes = await img_response.read()

                        if PIL_AVAILABLE:
                            img = Image.open(io.BytesIO(img_bytes))
                            if img.format not in ["JPEG", "PNG", "GIF", "BMP", "WEBP"]:
                                contents = await image_data.read()

                                file_like = io.BytesIO(contents)
                                file_like.seek(0)
                                markitdown_converter = MarkitdownConverter()
                                documents = markitdown_converter.extract_text(file_like)
                                if documents:
                                    image_descriptions.append(f"{img.format} {i+1}: {documents}")
                                else:
                                    image_descriptions.append(f"{img.format} {i+1}: Error processing image")
                                continue

                            # Convert image to base64 for API calls if needed
                            buffered = io.BytesIO()
                            img.save(buffered, format=img.format or "JPEG")
                            img_str = base64.b64encode(buffered.getvalue()).decode()
                            img_format = (img.format or "png").lower()
                        else:
                            # Fallback when PIL is not available
                            img_str = base64.b64encode(img_bytes).decode()
                            img_format = "jpeg"  # Default format

                        # Configure the client based on the model type
                        endpoint = f"{os.getenv('AZURE_VISION_API_ENDPOINT')}/openai/deployments/{os.getenv('AZURE_VISION_MODEL')}/chat/completions?api-version=2024-02-15-preview"
                        headers = {
                            "Content-Type": "application/json",
                            "api-key": os.getenv("DEEPSEEK_API_KEY")
                        }
                        messages = [
                            {"role": "system", "content":[
                                {"type": "text", "text": "You are an AI assistant that provides detailed descriptions of images."}
                            ]},
                            {
                                "role": "user",
                                "content": [
                                    {"type": "text", "text": "Provide a detailed description of this image. Include all visible elements, text, and context."},
                                    {"type": "image_url", "image_url": {"url":f"data:image/{img_format};base64,{img_str}"}}
                                ]
                            }
                        ]

                        data = {
                            "messages": messages,
                            "model": os.getenv('AZURE_VISION_MODEL',"mistral-medium-2505"),
                            "temperature": 0.7,
                        }
                    async with session.post(endpoint, headers=headers, json=data) as response:
                        if response.status == 200:
                            result = await response.json()
                            description = result["choices"][0]["message"]["content"]
                            image_descriptions.append(f"Image {i+1}: {description}")
                            logger.info(f"Image {i+1} analysis completed successfully")
                        else:
                            try:
                                error_body = await response.json()
                            except aiohttp.ContentTypeError:
                                error_body = await response.text()
                            logger.error(f"Image {i+1} analysis failed with status {response.status}: {error_body}")
                            image_descriptions.append(f"Image {i+1}: Analysis failed - {error_body}")

            except Exception as e:
                logger.error(f"Error analyzing image {i+1}: {e}")
                image_descriptions.append(f"Image {i+1}: Error processing image - {str(e)}")

        # Combine all image descriptions
        if image_descriptions:
            combined_description = "\n".join(image_descriptions)
            logger.info(f"Successfully analyzed {len(image_descriptions)} images")
            return combined_description
        else:
            logger.warning("No images were successfully analyzed")
            return ""

    except Exception as e:
        logger.error(f"Error in image analysis pipeline: {e}")
        return ""

async def _analyze_audio_from_urls(audio_files: List[AudioFileInfo]) -> str:
    """Analyze audio files from blob URLs using Azure Video Indexer for transcription."""
    if not audio_files:
        return ""

    try:
        audio_transcriptions = []

        for i, audio_file in enumerate(audio_files[:3]):  # Limit to first 3 audio files
            try:
                # Check if file is actually an audio file
                if not audio_file.type or not audio_file.type.startswith('audio/'):
                    logger.warning(f"File {audio_file.name} is not an audio file: {audio_file.type}")
                    audio_transcriptions.append(f"Audio {i+1}: File is not an audio format")
                    continue

                file_size_mb = round(audio_file.size / (1024 * 1024), 2)
                audio_format = audio_file.type.split('/')[-1].upper()
                if audio_format == 'MPEG':
                    audio_format = 'MP3'

                # Check if Azure Video Indexer is configured
                if not azure_video_indexer_service.configured:
                    fallback_msg = f"Audio {i+1} ({audio_file.name}):\n"
                    fallback_msg += f"File uploaded successfully ({file_size_mb}MB {audio_format})\n"
                    fallback_msg += f"Azure Video Indexer not configured - transcription unavailable\n"
                    fallback_msg += f"To enable transcription, set AZURE_VIDEO_INDEXER_ACCOUNT_ID and AZURE_VIDEO_INDEXER_ACCESS_TOKEN"
                    audio_transcriptions.append(fallback_msg)
                    continue

                # Use synchronous transcription for immediate results
                transcription_result = await azure_video_indexer_service.transcribe_audio(
                    audio_file.url,
                    audio_file.name,
                    language="en-US",
                    wait_for_completion=True,
                    max_wait_seconds=300  # 5 minutes timeout
                )

                if transcription_result.get("error"):
                    error_msg = f"Audio {i+1} ({audio_file.name}):\n"
                    error_msg += f"Transcription failed: {transcription_result['error']}\n"
                    error_msg += f"File details: {file_size_mb}MB {audio_format}"
                    logger.error(f"Transcription failed for {audio_file.name}: {transcription_result['error']}")
                    audio_transcriptions.append(error_msg)
                    continue

                # Extract transcript and metadata
                transcript = transcription_result.get("transcript", "")
                metadata = transcription_result.get("metadata", {})

                if transcript:
                    # Create rich transcription result
                    result = f"Audio {i+1} - {audio_file.name}:\n"
                    result += f"File Info: {file_size_mb}MB {audio_format}\n"
                    result += f"Duration: {metadata.get('duration', 0)} seconds\n"
                    result += f"Language: {metadata.get('language', 'Unknown')}\n"

                    # Add keywords if available
                    keywords = metadata.get('keywords', [])
                    if keywords:
                        result += f"Keywords: {', '.join(keywords[:5])}\n"

                    # Add topics if available
                    topics = metadata.get('topics', [])
                    if topics:
                        result += f"Topics: {', '.join(topics[:3])}\n"

                    result += f"Sentiment: {metadata.get('sentiment', 'Neutral')}\n"
                    result += f"\nTranscript:\n{transcript}"

                    audio_transcriptions.append(result)
                    logger.info(f"Successfully transcribed {audio_file.name} ({len(transcript)} characters)")
                else:
                    # No transcript content
                    fallback_msg = f"Audio {i+1} ({audio_file.name}):\n"
                    fallback_msg += f"File processed successfully but no speech detected\n"
                    fallback_msg += f"File details: {file_size_mb}MB {audio_format}\n"
                    fallback_msg += f"Duration: {metadata.get('duration', 0)} seconds"
                    audio_transcriptions.append(fallback_msg)
                    logger.warning(f"No transcript content for {audio_file.name}")

            except Exception as e:
                logger.error(f"Error processing audio file {i+1}: {e}")
                import traceback
                traceback.print_exc()
                audio_transcriptions.append(f"Audio {i+1}: Error processing audio file - {str(e)}")

        # Combine all transcriptions
        if audio_transcriptions:
            combined_transcription = "\n\n".join(audio_transcriptions)
            logger.info(f"Successfully processed {len(audio_transcriptions)} audio files")
            return combined_transcription
        else:
            logger.warning("No audio files were successfully processed")
            return ""

    except Exception as e:
        logger.error(f"Error in audio transcription pipeline: {e}")
        import traceback
        traceback.print_exc()
        return ""


async def _analyze_video_from_urls(video_files: List[VideoFileInfo]) -> str:
    """Analyze video files from blob URLs using Azure Video Indexer for comprehensive insights."""
    if not video_files:
        return ""

    try:
        video_analyses = []

        for i, video_file in enumerate(video_files[:3]):  # Limit to first 3 video files
            try:
                # Check if file is actually a video file
                if not video_file.type or not video_file.type.startswith('video/'):
                    logger.warning(f"File {video_file.name} is not a video file: {video_file.type}")
                    video_analyses.append(f"Video {i+1}: File is not a video format")
                    continue

                file_size_mb = round(video_file.size / (1024 * 1024), 2)
                video_format = video_file.type.split('/')[-1].upper()
                if video_format == 'QUICKTIME':
                    video_format = 'MOV'

                # Check if Azure Video Indexer is configured
                if not azure_video_indexer_service.configured:
                    fallback_msg = f"Video {i+1} ({video_file.name}):\n"
                    fallback_msg += f"File uploaded successfully ({file_size_mb}MB {video_format})\n"
                    fallback_msg += f"Azure Video Indexer not configured - analysis unavailable\n"
                    fallback_msg += f"To enable analysis, set AZURE_VIDEO_INDEXER_ACCOUNT_ID and AZURE_VIDEO_INDEXER_ACCESS_TOKEN"
                    video_analyses.append(fallback_msg)
                    continue

                # Analyze video using Azure Video Indexer with the provided URL
                # (URL should already have SAS token if it's a video file from upload endpoint)
                logger.info(f"Starting comprehensive video analysis for {video_file.name}")
                analysis_result = await azure_video_indexer_service.analyze_video(
                    video_file.url,
                    video_file.name
                )

                if analysis_result.get("error"):
                    error_msg = f"Video {i+1} ({video_file.name}):\n"
                    error_msg += f"Analysis failed: {analysis_result['error']}\n"
                    error_msg += f"File details: {file_size_mb}MB {video_format}"
                    logger.error(f"Video analysis failed for {video_file.name}: {analysis_result['error']}")
                    video_analyses.append(error_msg)
                    continue

                # Extract comprehensive analysis results
                transcript = analysis_result.get("transcript", "")
                metadata = analysis_result.get("metadata", {})
                visual_insights = analysis_result.get("visual_insights", {})

                if transcript or visual_insights:
                    # Create rich video analysis result
                    result = f"Video {i+1} - {video_file.name}:\n"
                    result += f"File Info: {file_size_mb}MB {video_format}\n"
                    result += f"Duration: {metadata.get('duration', 0)} seconds\n"
                    result += f"Resolution: {metadata.get('resolution', 'Unknown')}\n"
                    result += f"Language: {metadata.get('language', 'Unknown')}\n"

                    # Add visual insights
                    if visual_insights:
                        detected_objects = visual_insights.get('objects', [])
                        if detected_objects:
                            result += f"Detected Objects: {', '.join(detected_objects[:10])}\n"

                        detected_faces = visual_insights.get('faces', [])
                        if detected_faces:
                            result += f"Detected Faces: {len(detected_faces)} person(s)\n"

                        detected_text = visual_insights.get('text', [])
                        if detected_text:
                            result += f"Text in Video: {', '.join(detected_text[:5])}\n"

                        scenes = visual_insights.get('scenes', [])
                        if scenes:
                            result += f"Key Scenes: {len(scenes)} scene(s) detected\n"

                    # Add keywords and topics
                    keywords = metadata.get('keywords', [])
                    if keywords:
                        result += f"Keywords: {', '.join(keywords[:5])}\n"

                    topics = metadata.get('topics', [])
                    if topics:
                        result += f"Topics: {', '.join(topics[:3])}\n"

                    result += f"Sentiment: {metadata.get('sentiment', 'Neutral')}\n"

                    # Add transcript if available
                    if transcript:
                        result += f"\nTranscript:\n{transcript}"

                    video_analyses.append(result)
                    logger.info(f"Successfully analyzed {video_file.name} (transcript: {len(transcript)} chars, visual insights: {len(str(visual_insights))} chars)")
                else:
                    # No content detected
                    fallback_msg = f"Video {i+1} ({video_file.name}):\n"
                    fallback_msg += f"File processed successfully but no content detected\n"
                    fallback_msg += f"File details: {file_size_mb}MB {video_format}\n"
                    fallback_msg += f"Duration: {metadata.get('duration', 0)} seconds"
                    video_analyses.append(fallback_msg)
                    logger.warning(f"No content detected for {video_file.name}")

            except Exception as e:
                logger.error(f"Error processing video file {i+1}: {e}")
                import traceback
                traceback.print_exc()
                video_analyses.append(f"Video {i+1}: Error processing video file - {str(e)}")

        # Combine all analyses
        if video_analyses:
            combined_analysis = "\n\n".join(video_analyses)
            logger.info(f"Successfully processed {len(video_analyses)} video files")
            return combined_analysis
        else:
            logger.warning("No video files were successfully processed")
            return ""

    except Exception as e:
        logger.error(f"Error in video analysis pipeline: {e}")
        import traceback
        traceback.print_exc()
        return ""


@router.get("/health", status_code=200)
async def copilotkit_health_check():
    """Health check endpoint for CopilotKit service."""
    return {
        "status": "healthy",
        "service": "copilotkit-rag",
        "timestamp": datetime.datetime.now(datetime.timezone.utc).isoformat()
    }

@router.get("/tools", status_code=200)
async def get_available_tools():
    """Get information about available CopilotKit tools."""
    return {
        "tools": [
            {
                "name": "vector_search_tool",
                "description": "Perform semantic similarity search and document retrieval",
                "capabilities": ["similarity-search", "mmr-search", "hybrid-search", "multi-workspace", "parallel-search", "relevance-scoring"]
            },
            {
                "name": "web_search_tool",
                "description": "Search the web for current information",
                "capabilities": ["query-optimization", "result-formatting", "citation-ready"]
            }
        ],
        "search_modes": ["internal", "web", "hybrid"],
        "features": [
            "intelligent-tool-selection",
            "parallel-execution",
            "streaming-responses",
            "citation-generation",
            "multi-modal-support"
        ]
    }

@router.post("/benchmark", status_code=200)
async def benchmark_performance(
    query: CopilotKitChatQuery,
    current_user: str,
    user_name: str,
    tenant_id: str,
    db = Depends(get_db)
):
    """
    Benchmark the performance of the CopilotKit agent.
    Returns detailed timing and performance metrics.
    """
    copilotkit_rag_service.db_client = db

    start_time = datetime.datetime.now(datetime.timezone.utc)

    try:
        # Execute non-streaming query for accurate timing
        async for result in copilotkit_rag_service.query_workspace(
            user_id=current_user,
            question=query.question,
            tenant_id=tenant_id,
            user_name=user_name,
            search_mode=query.search_mode,
            include_web_results=query.include_web_results,
            stream=False,
            previous_message=query.previous_message
        ):
            break  # Get the first (and only) result

        end_time = datetime.datetime.now(datetime.timezone.utc)
        total_time = (end_time - start_time).total_seconds()

        return {
            "performance_metrics": {
                "total_time_seconds": total_time,
                "tools_used": result.get("tools_used", []),
                "sources_found": len(result.get("sources", [])),
                "answer_length": len(result.get("answer", "")),
                "status": result.get("status", 200)
            },
            "result": result,
            "benchmark_timestamp": end_time.isoformat()
        }

    except Exception as e:
        logger.error(f"Error in performance benchmark: {e}")
        return {
            "error": f"Benchmark failed: {str(e)}",
            "performance_metrics": {
                "total_time_seconds": -1,
                "status": 500
            }
        }
