import { NextResponse } from "next/server";
import { getToken } from "next-auth/jwt";
import db from "@/lib/shared-db";

export async function GET(request: Request) {
  try {
    const token = await getToken({ req: request as any });
    if (!token) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get all active storage tiers
    const storageTiers = await db.storageTier.findMany({
      where: {
        isActive: true,
      },
      orderBy: {
        sizeGB: "asc",
      },
    });

    return NextResponse.json({ storageTiers });
  } catch (error) {
    console.error("Error fetching storage tiers:", error);
    return NextResponse.json(
      { error: "Failed to fetch storage tiers" },
      { status: 500 }
    );
  }
}
