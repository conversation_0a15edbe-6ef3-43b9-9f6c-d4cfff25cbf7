import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import db from "@/lib/shared-db";
import { authOptions } from "@/lib/next-auth/src/auth-options";
import { withPermission } from "@/lib/permission-middleware";
import { hasPermission } from "@/lib/permissions";
import { BlobServiceClient } from "@azure/storage-blob";
import { getTranslations } from "@/lib/server-i18n";
import jwt from "jsonwebtoken";

//Get folders for a workspace (with nested structure)
const getFolders = async (request: Request) => {
  try {
    const url = new URL(request.url);
    const rawWorkspaceSlug = url.searchParams.get("workspaceSlug");
    const userId = request.headers.get("x-user-id");
    if (!userId) {
      return NextResponse.json({ error: "x-user-id header required" }, { status: 400 });
    }
    const tenantId = request.headers.get("x-tenant-id");
    if (!tenantId) {
      return NextResponse.json({ error: "x-tenant-id header required" }, { status: 400 });
    }
    const workspaceSlug = rawWorkspaceSlug
      ? decodeURI(rawWorkspaceSlug)
      : rawWorkspaceSlug;
    const folderId = url.searchParams.get("folderId");

    if (!workspaceSlug) {
      return NextResponse.json(
        { error: "Workspace ID is required" },
        { status: 400 }
      );
    }

    if (folderId) {
      const [folder, workspaceMember, membership] = await Promise.all([
        db.folder.findUnique({
          where: { id: folderId },
          include: {
            parentRelations: {
              include: { child: true }, // Fetch parent folders
            },
            childRelations: {
              include: { child: true, parent: true }, // Fetch child folders
            },
            files: true,
          },
        }),
        db.workspaceMember.findMany({
          where: {
            userId,
            workspace: {
              slug: workspaceSlug,
            },
          },
          include: {
            customRole: {
              include: {
                permissions: {
                  include: {
                    permission: true,
                  },
                },
              },
            },
          },
        }),
        db.membership.findMany({
          where: {
            userId,
            tenantId,
          },
        }),
      ]);

      const isOwnerOrAdmin =
        membership?.[0]?.role === "OWNER" || membership?.[0]?.role === "ADMIN";

      if (!folder) {
        return NextResponse.json(
          { error: "Folder not found" },
          { status: 404 }
        );
      }
      const hierarchy = await db.folderHierarchy.findMany({
        where: {
          pageId: folder?.pageId,
        },
        include: {
          parent: true,
          child: true,
        },
      });

      folder.hierarchy = hierarchy;

      // Use the updated hasPermission function that respects built-in role configurations
      const folderPermissions = {
        workspace: {
          create: await hasPermission(userId, tenantId, "CREATE", "WORKSPACE", workspaceSlug),
          read: await hasPermission(userId, tenantId, "READ", "WORKSPACE", workspaceSlug),
          update: await hasPermission(userId, tenantId, "UPDATE", "WORKSPACE", workspaceSlug),
          delete: await hasPermission(userId, tenantId, "DELETE", "WORKSPACE", workspaceSlug),
        },
        folder: {
          create: await hasPermission(userId, tenantId, "CREATE", "FOLDER", workspaceSlug),
          read: await hasPermission(userId, tenantId, "READ", "FOLDER", workspaceSlug),
          update: await hasPermission(userId, tenantId, "UPDATE", "FOLDER", workspaceSlug),
          delete: await hasPermission(userId, tenantId, "DELETE", "FOLDER", workspaceSlug),
        },
        file: {
          create: await hasPermission(userId, tenantId, "CREATE", "FILE", workspaceSlug),
          read: await hasPermission(userId, tenantId, "READ", "FILE", workspaceSlug),
          update: await hasPermission(userId, tenantId, "UPDATE", "FILE", workspaceSlug),
          delete: await hasPermission(userId, tenantId, "DELETE", "FILE", workspaceSlug),
        },
      };

      return NextResponse.json(
        {
          folder,
          permission: folderPermissions,
        },
        { status: 200 }
      );
    }

    // Fetch all root folders in the workspace (top-level folders with no parent)
    const folders = await db.folder.findMany({
      where: {
        workspace: {
          slug: workspaceSlug,
        },
        parentRelations: { none: {} },
      },
      include: { childRelations: { include: { child: true } }, files: true },
    });

    return NextResponse.json({ folders }, { status: 200 });
  } catch (error) {
    console.error("Error fetching folders:", error);
    return NextResponse.json(
      { error: "Failed to fetch folders" },
      { status: 500 }
    );
  }
};

// ✅ Create a new folder
const createFolder = async (request: Request) => {
  try {
    // Get userId and tenantId from headers
    const userIdFromHeader = request.headers.get("x-user-id");
    const tenantIdFromHeader = request.headers.get("x-tenant-id");

    // Fallback to session if headers are not available
    const session: any = await getServerSession(authOptions);
    const userId = userIdFromHeader || session?.userId || session?.user?.email;

    if (!userId) {
      return NextResponse.json(
        { error: "Unauthorized - userId is required" },
        { status: 401 }
      );
    }

    const {
      name,
      workspaceSlug: slug,
      pageId,
      parentIds = [],
    } = await request.json();
    const workspaceSlug = decodeURI(slug);
    if (!name || !workspaceSlug) {
      return NextResponse.json(
        { error: "Name and workspace ID are required" },
        { status: 400 }
      );
    }
    const workspace = await db.workspace.findFirst({
      where: {
        slug: workspaceSlug,
      },
    });
    // Create folder
    const folder = await db.folder.create({
      data: { name, workspaceId: workspace?.id, pageId },
    });

    // Handle multiple parent-child relationships
    if (parentIds.length > 0) {
      await db.folderHierarchy.createMany({
        data: parentIds.map((parentId: string) => ({
          parentId,
          pageId,
          childId: folder.id,
        })),
      });
    }

    return NextResponse.json(
      { message: "Folder created successfully", folder },
      { status: 201 }
    );
  } catch (error) {
    console.error("Error creating folder:", error);
    return NextResponse.json(
      { error: "Failed to create folder" },
      { status: 500 }
    );
  }
};

// ✅ Update a folder
const updateFolder = async (request: Request) => {
  try {
    // Get userId and tenantId from headers
    const userIdFromHeader = request.headers.get("x-user-id");
    const tenantIdFromHeader = request.headers.get("x-tenant-id");

    // Fallback to session if headers are not available
    const session: any = await getServerSession(authOptions);
    const userId = userIdFromHeader || session?.userId || session?.user?.email;

    if (!userId) {
      return NextResponse.json(
        { error: "Unauthorized - userId is required" },
        { status: 401 }
      );
    }

    const { id, name } = await request.json();
    if (!id || !name) {
      return NextResponse.json(
        { error: "Folder ID and name are required" },
        { status: 400 }
      );
    }

    const folder = await db.folder.update({ where: { id }, data: { name } });
    return NextResponse.json({
      message: "Folder updated successfully",
      folder,
    });
  } catch (error) {
    console.error("Error updating folder:", error);
    return NextResponse.json(
      { error: "Failed to update folder" },
      { status: 500 }
    );
  }
};

// ✅ Delete a folder (with cascading delete for relationships)
const deleteFolder = async (request: Request) => {
  try {
    // Get userId and tenantId from headers
    const userIdFromHeader = request.headers.get("x-user-id");
    const tenantIdFromHeader = request.headers.get("x-tenant-id");

    // Fallback to session if headers are not available
    const session: any = await getServerSession(authOptions);
    const userId = userIdFromHeader || session?.userId || session?.user?.email;

    if (!userId) {
      return NextResponse.json(
        { error: "Unauthorized - userId is required" },
        { status: 401 }
      );
    }

    const url = new URL(request.url);
    const folderId = url.searchParams.get("id");

    if (!folderId)
      return NextResponse.json(
        { error: "Folder ID is required" },
        { status: 400 }
      );
    const payload = {
      userId: session.userId,
      email: session.user?.email,
      name: session.user?.name,
      // Include the current organization if available
      organizationId: session.currentOrganization?.id,
      // Add any other information you need
      iat: Math.floor(Date.now() / 1000),
    };

    // Generate a token directly
    const secret = process.env.NEXTAUTH_SECRET;
    if (!secret) {
      throw new Error("NEXTAUTH_SECRET is not defined");
    }

    // Create a token that expires in 1 hour
    const token = jwt.sign(payload, secret, { expiresIn: "1h" });

    const folder = await db.folder.findUnique({
      where: { id: folderId },
      include: {
        parentRelations: true,
        childRelations: true,
        files: true,
        workspace: true,
      },
    });

    if (!folder)
      return NextResponse.json({ error: "Folder not found" }, { status: 404 });

    // If force delete and folder has files, delete vectors and blob storage for each file
    if (folder.files.length > 0) {
      const workspaceSlug = folder.workspace?.slug;
      const tenantId = tenantIdFromHeader;

      // Delete vectors for each file
      for (const file of folder.files) {
        try {
          // Delete from vector store
          const vectorDeleteResponse = await fetch(
            `${
              process.env.NEXT_PUBLIC_API_BASE_URL || "http://localhost:8000"
            }/api/v1/workspace-chat/delete-file?file_id=${
              file.id
            }&workspaceSlug=${encodeURIComponent(
              workspaceSlug || ""
            )}&tenant_id=${tenantId}`,
            {
              method: "GET",
              headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${token}`,
              },
            }
          );

          if (!vectorDeleteResponse.ok) {
            console.error(`Failed to delete vectors for file ${file.id}`);
          }

          // Delete from Azure Blob Storage
          try {
            const blobServiceClient = BlobServiceClient.fromConnectionString(
              process.env.AZURE_STORAGE_CONNECTION_STRING || ""
            );
            const containerClient = blobServiceClient.getContainerClient(
              process.env.AZURE_STORAGE_CONTAINER_NAME || ""
            );
            const parts = decodeURIComponent(
              file?.url?.split("/").slice(-3)?.join("/")
            );
            const blobClient = containerClient.getBlobClient(parts || "");
            await blobClient.delete();
          } catch (storageError) {
            console.error(
              `Error deleting file ${file.id} from storage:`,
              storageError
            );
          }
        } catch (error) {
          console.error(`Error deleting file ${file.id}:`, error);
          // Continue with other files even if one fails
        }
      }
    }
    const deleteFolderAndFiles = await db.$transaction(async (tx) => {
      const flod = await tx.folder.delete({ where: { id: folderId } });
      const file = await tx.file.deleteMany({ where: { parentId: folderId } });
      return { flod, file };
    });
    if (!deleteFolderAndFiles) {
      console.error("Error deleting folder:", deleteFolderAndFiles);
      const { t } = getTranslations();
      return NextResponse.json(
        { error: t("api.errors.failedToDeleteFolder") },
        { status: 500 }
      );
    }
    return NextResponse.json({ message: "Folder deleted successfully" });
  } catch (error) {
    console.error("Error deleting folder:", error);
    return NextResponse.json(
      { error: "Failed to delete folder" },
      { status: 500 }
    );
  }
};

// Export the handlers with permission checks
export const GET = withPermission(getFolders, "READ", "FOLDER");
export const POST = withPermission(createFolder, "CREATE", "FOLDER");
export const PUT = withPermission(updateFolder, "UPDATE", "FOLDER");
export const DELETE = withPermission(deleteFolder, "DELETE", "FOLDER");
