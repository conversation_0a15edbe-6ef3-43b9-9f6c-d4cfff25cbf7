import { ThemeConfig } from "@/types/theme-config";
import { updateThemeCache } from "@/lib/theme-cache";

export interface UpdateThemeConfigRequest {
  tenantId: string;
  themeConfig: Partial<ThemeConfig>;
}

export interface ThemeConfigResponse {
  success: boolean;
  message?: string;
  data?: ThemeConfig;
}

export interface ThemeConfigurationData {
  id: string;
  tenantId: string;
  brandName?: string;
  logoUrl?: string;
  faviconUrl?: string;
  fullAppLogoUrl?: string;

  // Light theme color fields
  lightPrimaryColor?: string;
  lightSecondaryColor?: string;
  lightAccentColor?: string;
  lightNavigationBackgroundColor?: string;
  lightContentBackgroundColor?: string;
  lightForegroundColor?: string;

  // Dark theme color fields
  darkPrimaryColor?: string;
  darkSecondaryColor?: string;
  darkAccentColor?: string;
  darkNavigationBackgroundColor?: string;
  darkContentBackgroundColor?: string;
  darkForegroundColor?: string;

  themePreset?: string;
  isActive: boolean;
  version: number;
  createdAt: string;
  updatedAt: string;
}

export async function updateThemeConfig(
  tenantId: string,
  themeConfig: Partial<ThemeConfig>,
): Promise<ThemeConfigResponse> {
  // Check if partner console is enabled
  const isPartnerConsole = process.env.NEXT_PUBLIC_IS_PARTNER_CONSOLE === "1";
  if (!isPartnerConsole) {
    return {
      success: false,
      message: "Theme configuration is only available in Partner Console mode",
    };
  }

  try {
    const baseUrl =
      typeof window !== "undefined"
        ? ""
        : process.env.NEXTAUTH_URL || "http://localhost:3000";
    const response = await fetch(
      `${baseUrl}/api/tenant/${tenantId}/theme-config`,
      {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(themeConfig),
      },
    );

    if (!response.ok) {
      const errorData = await response
        .json()
        .catch(() => ({ error: "Unknown error" }));
      throw new Error(
        errorData.error || `HTTP error! status: ${response.status}`,
      );
    }

    const data = await response.json();

    // Update cache with new theme config
    if (data.themeConfig) {
      updateThemeCache(tenantId, data.themeConfig);
    }

    return {
      success: true,
      message: data.message || "Theme configuration updated successfully",
      data: data.themeConfig,
    };
  } catch (error) {
    console.error("Error updating theme config:", error);
    return {
      success: false,
      message:
        error instanceof Error
          ? error.message
          : "Failed to update theme configuration",
    };
  }
}

export async function getThemeConfig(
  tenantId: string,
): Promise<ThemeConfig | null> {
  // Check if partner console is enabled
  const isPartnerConsole = process.env.NEXT_PUBLIC_IS_PARTNER_CONSOLE === "1";
  if (!isPartnerConsole) {
    return null;
  }

  try {
    const baseUrl =
      typeof window !== "undefined"
        ? ""
        : process.env.NEXTAUTH_URL || "http://localhost:3000";

    const response = await fetch(
      `${baseUrl}/api/tenant/${tenantId}/theme-config`,
      {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
        credentials: "include", // Important for cookies to be sent
      },
    );

    if (!response.ok) {
      if (response.status === 404) {
        // No theme config exists yet, return null
        return null;
      }
      const errorData = await response
        .json()
        .catch(() => ({ error: "Unknown error" }));
      throw new Error(
        errorData.error || `HTTP error! status: ${response.status}`,
      );
    }

    const data = await response.json();
    console.log("fetch_theme", data);

    const themeConfig = data.themeConfig || data;

    // Update cache with fetched theme config
    if (themeConfig) {
      updateThemeCache(tenantId, themeConfig);
    }

    return themeConfig;
  } catch (error) {
    console.error("Error fetching theme config:", error);
    return null;
  }
}

export async function createThemeConfig(
  tenantId: string,
  themeConfig: Partial<ThemeConfig>,
): Promise<ThemeConfigResponse> {
  // Check if partner console is enabled
  const isPartnerConsole = process.env.NEXT_PUBLIC_IS_PARTNER_CONSOLE === "1";
  if (!isPartnerConsole) {
    return {
      success: false,
      message: "Theme configuration is only available in Partner Console mode",
    };
  }

  try {
    const baseUrl =
      typeof window !== "undefined"
        ? ""
        : process.env.NEXTAUTH_URL || "http://localhost:3000";
    const response = await fetch(
      `${baseUrl}/api/tenant/${tenantId}/theme-config`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(themeConfig),
      },
    );

    if (!response.ok) {
      const errorData = await response
        .json()
        .catch(() => ({ error: "Unknown error" }));
      throw new Error(
        errorData.error || `HTTP error! status: ${response.status}`,
      );
    }

    const data = await response.json();

    // Update cache with new theme config
    if (data.themeConfig) {
      updateThemeCache(tenantId, data.themeConfig);
    }

    return {
      success: true,
      message: data.message || "Theme configuration created successfully",
      data: data.themeConfig,
    };
  } catch (error) {
    console.error("Error creating theme config:", error);
    return {
      success: false,
      message:
        error instanceof Error
          ? error.message
          : "Failed to create theme configuration",
    };
  }
}

export async function uploadLogo(
  tenantId: string,
  file: File,
  type: "logo" | "favicon" | "fullAppLogo",
): Promise<{ success: boolean; url?: string; message?: string }> {
  // Check if partner console is enabled
  const isPartnerConsole = process.env.NEXT_PUBLIC_IS_PARTNER_CONSOLE === "1";
  if (!isPartnerConsole) {
    return {
      success: false,
      message: "Logo upload is only available in Partner Console mode",
    };
  }

  try {
    const formData = new FormData();
    formData.append("file", file);
    formData.append("type", type);

    const response = await fetch(`/api/tenant/${tenantId}/upload-logo`, {
      method: "POST",
      body: formData,
    });

    if (!response.ok) {
      const errorData = await response
        .json()
        .catch(() => ({ error: "Unknown error" }));
      throw new Error(
        errorData.error || `HTTP error! status: ${response.status}`,
      );
    }

    const data = await response.json();
    return {
      success: true,
      url: data.url,
      message: data.message || "Logo uploaded successfully",
    };
  } catch (error) {
    console.error("Error uploading logo:", error);
    return {
      success: false,
      message: error instanceof Error ? error.message : "Failed to upload logo",
    };
  }
}

export async function deleteLogo(
  tenantId: string,
  type: "logo" | "favicon" | "fullAppLogo",
): Promise<{ success: boolean; message?: string }> {
  // Check if partner console is enabled
  const isPartnerConsole = process.env.NEXT_PUBLIC_IS_PARTNER_CONSOLE === "1";
  if (!isPartnerConsole) {
    return {
      success: false,
      message: "Logo deletion is only available in Partner Console mode",
    };
  }

  try {
    const response = await fetch(
      `/api/tenant/${tenantId}/upload-logo?type=${type}`,
      {
        method: "DELETE",
      },
    );

    if (!response.ok) {
      const errorData = await response
        .json()
        .catch(() => ({ error: "Unknown error" }));
      throw new Error(
        errorData.error || `HTTP error! status: ${response.status}`,
      );
    }

    const data = await response.json();
    return {
      success: true,
      message: data.message || "Logo removed successfully",
    };
  } catch (error) {
    console.error("Error removing logo:", error);
    return {
      success: false,
      message: error instanceof Error ? error.message : "Failed to remove logo",
    };
  }
}

export async function resetThemeConfig(
  tenantId: string,
): Promise<ThemeConfigResponse> {
  // Check if partner console is enabled
  const isPartnerConsole = process.env.NEXT_PUBLIC_IS_PARTNER_CONSOLE === "1";
  if (!isPartnerConsole) {
    return {
      success: false,
      message: "Theme reset is only available in Partner Console mode",
    };
  }

  try {
    const baseUrl =
      typeof window !== "undefined"
        ? ""
        : process.env.NEXTAUTH_URL || "http://localhost:3000";
    const response = await fetch(
      `${baseUrl}/api/tenant/${tenantId}/theme-config`,
      {
        method: "DELETE",
      },
    );

    if (!response.ok) {
      const errorData = await response
        .json()
        .catch(() => ({ error: "Unknown error" }));
      throw new Error(
        errorData.error || `HTTP error! status: ${response.status}`,
      );
    }

    const data = await response.json();
    return {
      success: true,
      message: data.message || "Theme configuration reset to default",
    };
  } catch (error) {
    console.error("Error resetting theme config:", error);
    return {
      success: false,
      message:
        error instanceof Error
          ? error.message
          : "Failed to reset theme configuration",
    };
  }
}

export async function getThemeConfigurationHistory(
  tenantId: string,
): Promise<ThemeConfigurationData[]> {
  // Check if partner console is enabled
  const isPartnerConsole = process.env.NEXT_PUBLIC_IS_PARTNER_CONSOLE === "1";
  if (!isPartnerConsole) {
    return [];
  }

  try {
    const response = await fetch(
      `/api/tenant/${tenantId}/theme-config/history`,
    );

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data.history || [];
  } catch (error) {
    console.error("Error fetching theme config history:", error);
    return [];
  }
}

export async function activateThemeConfig(
  tenantId: string,
): Promise<ThemeConfigResponse> {
  // Check if partner console is enabled
  const isPartnerConsole = process.env.NEXT_PUBLIC_IS_PARTNER_CONSOLE === "1";
  if (!isPartnerConsole) {
    return {
      success: false,
      message: "Theme activation is only available in Partner Console mode",
    };
  }

  try {
    const response = await fetch(
      `/api/tenant/${tenantId}/theme-config/activate`,
      {
        method: "POST",
      },
    );

    if (!response.ok) {
      const errorData = await response
        .json()
        .catch(() => ({ error: "Unknown error" }));
      throw new Error(
        errorData.error || `HTTP error! status: ${response.status}`,
      );
    }

    const data = await response.json();
    return {
      success: true,
      message: data.message || "Theme configuration activated successfully",
    };
  } catch (error) {
    console.error("Error activating theme config:", error);
    return {
      success: false,
      message:
        error instanceof Error
          ? error.message
          : "Failed to activate theme configuration",
    };
  }
}

export async function deactivateThemeConfig(
  tenantId: string,
): Promise<ThemeConfigResponse> {
  // Check if partner console is enabled
  const isPartnerConsole = process.env.NEXT_PUBLIC_IS_PARTNER_CONSOLE === "1";
  if (!isPartnerConsole) {
    return {
      success: false,
      message: "Theme deactivation is only available in Partner Console mode",
    };
  }

  try {
    const response = await fetch(
      `/api/tenant/${tenantId}/theme-config/deactivate`,
      {
        method: "POST",
      },
    );

    if (!response.ok) {
      const errorData = await response
        .json()
        .catch(() => ({ error: "Unknown error" }));
      throw new Error(
        errorData.error || `HTTP error! status: ${response.status}`,
      );
    }

    const data = await response.json();
    return {
      success: true,
      message: data.message || "Theme configuration deactivated successfully",
    };
  } catch (error) {
    console.error("Error deactivating theme config:", error);
    return {
      success: false,
      message:
        error instanceof Error
          ? error.message
          : "Failed to deactivate theme configuration",
    };
  }
}

export function validateThemeColors(themeConfig: Partial<ThemeConfig>): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];
  const hexColorRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;

  // Validate light theme color fields
  const lightColorFields = [
    "lightPrimaryColor",
    "lightSecondaryColor",
    "lightAccentColor",
    "lightNavigationBackgroundColor",
    "lightContentBackgroundColor",
    "lightForegroundColor",
  ] as const;

  // Validate dark theme color fields
  const darkColorFields = [
    "darkPrimaryColor",
    "darkSecondaryColor",
    "darkAccentColor",
    "darkNavigationBackgroundColor",
    "darkContentBackgroundColor",
    "darkForegroundColor",
  ] as const;

  // Validate light theme color fields
  lightColorFields.forEach((field) => {
    const value = themeConfig[field];
    if (value && !hexColorRegex.test(value)) {
      errors.push(`${field} must be a valid hex color (e.g., #FF0000)`);
    }
  });

  // Validate dark theme color fields
  darkColorFields.forEach((field) => {
    const value = themeConfig[field];
    if (value && !hexColorRegex.test(value)) {
      errors.push(`${field} must be a valid hex color (e.g., #FF0000)`);
    }
  });

  // Validate URLs
  if (themeConfig.logoUrl && !isValidUrl(themeConfig.logoUrl)) {
    errors.push("Logo URL must be a valid URL");
  }



  if (themeConfig.faviconUrl && !isValidUrl(themeConfig.faviconUrl)) {
    errors.push("Favicon URL must be a valid URL");
  }

  // Validate brand name
  if (themeConfig.brandName && themeConfig.brandName.trim().length < 1) {
    errors.push("Brand name cannot be empty");
  }

  // Validate theme preset
  if (
    themeConfig.themePreset &&
    !["light", "dark"].includes(themeConfig.themePreset)
  ) {
    errors.push("Theme preset must be either 'light' or 'dark'");
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

function isValidUrl(url: string): boolean {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
}

export function generateThemeVariables(
  themeConfig: ThemeConfig,
  currentTheme?: "light" | "dark"
): Record<string, string> {
  const variables: Record<string, string> = {};

  // Helper function to convert space-separated HSL to comma-separated for sidebar variables
  const toSidebarFormat = (hsl: string): string => {
    return hsl.replace(/ /g, ", ");
  };

  // Determine which color set to use based on theme mode
  const getColorValue = (lightColor?: string, darkColor?: string): string | undefined => {
    // If we have theme-specific colors, use them
    if (currentTheme === "light" && lightColor) return lightColor;
    if (currentTheme === "dark" && darkColor) return darkColor;

    // If no current theme specified, prefer light colors, then dark
    return lightColor || darkColor;
  };

  // Convert hex colors to HSL for CSS variables
  const primaryColor = getColorValue(themeConfig.lightPrimaryColor, themeConfig.darkPrimaryColor);
  if (primaryColor) {
    const primaryHsl = hexToHsl(primaryColor);
    variables["--primary"] = primaryHsl;
    // Also set sidebar-specific primary color (comma-separated format)
    variables["--sidebar-primary"] = toSidebarFormat(primaryHsl);
  }

  const secondaryColor = getColorValue(themeConfig.lightSecondaryColor, themeConfig.darkSecondaryColor);
  if (secondaryColor) {
    const secondaryHsl = hexToHsl(secondaryColor);
    variables["--secondary"] = secondaryHsl;
    // Also set sidebar-specific accent color (sidebar uses accent for secondary interactions)
    variables["--sidebar-accent"] = toSidebarFormat(secondaryHsl);
  }

  const accentColor = getColorValue(themeConfig.lightAccentColor, themeConfig.darkAccentColor);
  const foregroundColor = getColorValue(themeConfig.lightForegroundColor, themeConfig.darkForegroundColor);
  if (accentColor) {
    const accentHsl = hexToHsl(accentColor);
    variables["--accent"] = accentHsl;
    // Set accent foreground for better contrast
    variables["--accent-foreground"] = foregroundColor
      ? hexToHsl(foregroundColor)
      : "234.1 2.7% 15%"; // fallback
  }

  // Handle split background colors
  const navigationBackgroundColor = getColorValue(
    themeConfig.lightNavigationBackgroundColor,
    themeConfig.darkNavigationBackgroundColor
  );
  if (navigationBackgroundColor) {
    const navigationBgHsl = hexToHsl(navigationBackgroundColor);
    // Apply to navigation components (sidebar and header)
    variables["--sidebar-background"] = toSidebarFormat(navigationBgHsl);
    // Also apply to header/navigation areas
    variables["--navigation-background"] = navigationBgHsl;
  }

  const contentBackgroundColor = getColorValue(
    themeConfig.lightContentBackgroundColor,
    themeConfig.darkContentBackgroundColor
  );
  if (contentBackgroundColor) {
    const contentBgHsl = hexToHsl(contentBackgroundColor);
    // Apply to main content areas
    variables["--background"] = contentBgHsl;
    variables["--card"] = contentBgHsl;
    variables["--popover"] = contentBgHsl;
    variables["--muted"] = contentBgHsl;
  }



  if (foregroundColor) {
    const foregroundHsl = hexToHsl(foregroundColor);
    variables["--foreground"] = foregroundHsl;
    // Also set sidebar and card foreground colors
    variables["--sidebar-foreground"] = toSidebarFormat(foregroundHsl);
    variables["--card-foreground"] = foregroundHsl;
    variables["--popover-foreground"] = foregroundHsl;
    // Set sidebar accent foreground for better contrast (comma-separated format)
    variables["--sidebar-accent-foreground"] = toSidebarFormat(foregroundHsl);

    // Apply foreground color to all text-related CSS variables
    variables["--accent-foreground"] = foregroundHsl;

    // Create a muted version for secondary text (slightly more transparent)
    const mutedForegroundHsl = foregroundHsl.replace(/(\d+)%\)$/, (_, lightness) => {
      const lightnessValue = parseInt(lightness);
      const mutedLightness = Math.max(30, lightnessValue - 25); // Ensure minimum readability
      return `${mutedLightness}%)`;
    });
    variables["--muted-foreground"] = mutedForegroundHsl;
    variables["--secondary-foreground"] = foregroundHsl;
  }

  return variables;
}

function hexToHsl(hex: string): string {
  // Remove # if present
  hex = hex.replace("#", "");

  // Convert to RGB
  const r = parseInt(hex.substring(0, 2), 16) / 255;
  const g = parseInt(hex.substring(2, 4), 16) / 255;
  const b = parseInt(hex.substring(4, 6), 16) / 255;

  const max = Math.max(r, g, b);
  const min = Math.min(r, g, b);
  let h = 0;
  let s = 0;
  const l = (max + min) / 2;

  if (max !== min) {
    const d = max - min;
    s = l > 0.5 ? d / (2 - max - min) : d / (max + min);

    switch (max) {
      case r:
        h = (g - b) / d + (g < b ? 6 : 0);
        break;
      case g:
        h = (b - r) / d + 2;
        break;
      case b:
        h = (r - g) / d + 4;
        break;
    }
    h /= 6;
  }

  return `${Math.round(h * 360)} ${Math.round(s * 100)}% ${Math.round(
    l * 100,
  )}%`;
}
