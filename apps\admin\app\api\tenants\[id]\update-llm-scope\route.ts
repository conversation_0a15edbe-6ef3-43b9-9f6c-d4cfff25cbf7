import { authOptions } from "@/lib/next-auth";
import dbPromise from "@/lib/shared-db";
import { getServerSession } from "next-auth/next";
import { NextResponse } from "next/server";
import { revalidatePath } from "next/cache";

export async function POST(
  request: Request,
  { params }: { params: { id: string } }
) {
  const session = await getServerSession(authOptions);

  if (!session) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  let body: any = null;

  try {
    const db = await dbPromise;
    body = await request.json();
    const { llmScope } = body;

    console.log(
      `Processing LLM scope update request for tenant ${params.id}:`,
      { llmScope }
    );

    // Validate required fields
    if (!llmScope) {
      console.log("Validation failed: LLM scope is required");
      return NextResponse.json(
        { error: "LLM scope is required" },
        { status: 400 }
      );
    }

    // Validate llmScope array
    if (!Array.isArray(llmScope) || llmScope.length === 0) {
      return NextResponse.json(
        { error: "Invalid LLM scope value - must be a non-empty array" },
        { status: 400 }
      );
    }

    const { id: tenantId } = params;

    // Verify tenant exists (don't select llmScope to avoid Prisma client issues)
    const tenant = await db.tenant.findUnique({
      where: { id: tenantId },
      select: {
        id: true,
        name: true,
      },
    });

    if (!tenant) {
      return NextResponse.json({ error: "Tenant not found" }, { status: 404 });
    }

    // Use upsert logic to handle both INSERT and UPDATE scenarios
    // For MongoDB, we can safely update the field even if it doesn't exist
    const updatedTenant = await db.tenant.update({
      where: { id: tenantId },
      data: {
        llmScope: llmScope,
        updatedAt: new Date(), // Update the timestamp
      },
      select: {
        id: true,
        name: true,
        // Note: Don't select llmScope here to avoid Prisma client issues
      },
    });

    // Log the scope change for audit purposes
    console.log(
      `LLM scope updated for tenant ${tenantId}: Previous value unknown -> ${llmScope}`
    );

    // ✅ Revalidate the tenant detail page to ensure fresh data
    revalidatePath(`/tenants/${tenantId}`);

    return NextResponse.json({
      message: "LLM scope updated successfully",
      tenant: {
        id: updatedTenant.id,
        name: updatedTenant.name,
        llmScope: llmScope, // Return the value we just set
      },
    });
  } catch (error) {
    console.error("Error updating LLM scope:", error);

    console.error("Error details:", {
      tenantId: params.id,
      llmScope: body?.llmScope,
      errorMessage: error instanceof Error ? error.message : "Unknown error",
      errorStack: error instanceof Error ? error.stack : undefined,
    });

    return NextResponse.json(
      {
        error: "Failed to update LLM scope",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}
