import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/next-auth";
import dbPromise from "@/lib/shared-db";

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  let body: any = null;

  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    const db = await dbPromise;
    const tenantId = params.id;

    // Parse request body to get the desired activation status
    body = await request.json();
    const { isActiveTenant } = body;

    console.log(`Processing tenant activation update request for tenant ${tenantId}:`, { isActiveTenant });

    // Validate payload
    if (typeof isActiveTenant !== 'boolean') {
      return NextResponse.json(
        { error: "isActiveTenant must be a boolean value" },
        { status: 400 }
      );
    }

    // Verify tenant exists
    const tenant = await db.tenant.findUnique({
      where: { id: tenantId },
      select: {
        id: true,
        name: true
      }
    });

    if (!tenant) {
      return NextResponse.json(
        { error: "Tenant not found" },
        { status: 404 }
      );
    }

    // Get current activation status for logging
    const currentTenant = await db.tenant.findUnique({
      where: { id: tenantId }
    });

    // ✅ Properly handle missing field - only default to true if field doesn't exist
    const currentActivationStatus = (currentTenant as any)?.hasOwnProperty('isActiveTenant')
      ? (currentTenant as any).isActiveTenant
      : true;

    const updatedTenant = await db.tenant.update({
      where: { id: tenantId },
      data: {
        isActiveTenant: isActiveTenant,
        updatedAt: new Date() // Update the timestamp
      },
      select: {
        id: true,
        name: true,
        isActiveTenant: true // Now we can select it since Prisma client is updated
      }
    });

    return NextResponse.json({
      message: `Tenant ${isActiveTenant ? 'activated' : 'deactivated'} successfully`,
      tenant: {
        id: updatedTenant.id,
        name: updatedTenant.name,
        isActiveTenant: updatedTenant.isActiveTenant
      }
    });

  } catch (error) {
    console.error("Error toggling tenant activation:", error);
    
    return NextResponse.json(
      { 
        error: "Failed to toggle tenant activation",
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
