<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Swiss Knowledge Hub Chatbot SDK - Test Page</title>
    <style>
        body {
            font-family: 'Inter', sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f8f9fa;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .test-section {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .test-controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        
        button {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            background: #007bff;
            color: white;
            cursor: pointer;
            font-size: 14px;
        }
        
        button:hover {
            background: #0056b3;
        }
        
        button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        
        .config-form {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .form-group {
            display: flex;
            flex-direction: column;
        }
        
        label {
            font-weight: 500;
            margin-bottom: 5px;
        }
        
        input, select {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .inline-container {
            height: 500px;
            border: 1px solid #ddd;
            border-radius: 8px;
            margin-top: 20px;
        }
        
        .status {
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .logs {
            background: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            max-height: 200px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }
        
        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }
        
        .log-entry.error {
            color: #dc3545;
        }
        
        .log-entry.success {
            color: #28a745;
        }
        
        .log-entry.info {
            color: #007bff;
        }
    </style>
<!-- Swiss Knowledge Hub Chatbot -->
    <script src="https://red-sand-0a2a96d03.1.azurestaticapps.net/sdk/chatbot-sdk.js"></script>
    <script>
    SwissKnowledgeHub.init({
        chatbotId: '688cc6524623082132229339',
        apiKey: 'skh_55863754fbac4b8ac755db02f6d64fb476e68012ed5072d2c0d8b0af1f1a5792',
        // apiBase: 'https://red-sand-0a2a96d03.1.azurestaticapps.net',  // Explicitly set API base
        position: 'bottom-right',
        theme: {
        primaryColor: '#007bff',
        fontFamily: 'Inter, sans-serif'
        }
    });
    </script>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Swiss Knowledge Hub Chatbot SDK</h1>
            <p>Test Page for SDK Integration</p>
        </div>

        <!-- Status Display -->
        <div id="status" class="status info">
            Ready to test. Configure your chatbot settings below.
        </div>

        <!-- Configuration Section -->
        <div class="test-section">
            <h2>Configuration</h2>
            <form id="configForm" class="config-form">
                <div class="form-group">
                    <label for="chatbotId">Chatbot ID</label>
                    <input type="text" id="chatbotId" placeholder="Enter your chatbot ID" required>
                </div>
                
                <div class="form-group">
                    <label for="apiKey">API Key</label>
                    <input type="password" id="apiKey" placeholder="Enter your API key" required>
                </div>
                
                <div class="form-group">
                    <label for="position">Position</label>
                    <select id="position">
                        <option value="bottom-right">Bottom Right</option>
                        <option value="bottom-left">Bottom Left</option>
                        <option value="top-right">Top Right</option>
                        <option value="top-left">Top Left</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="size">Size</label>
                    <select id="size">
                        <option value="small">Small</option>
                        <option value="medium" selected>Medium</option>
                        <option value="large">Large</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="mode">Mode</label>
                    <select id="mode">
                        <option value="widget" selected>Widget</option>
                        <option value="inline">Inline</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="primaryColor">Primary Color</label>
                    <input type="color" id="primaryColor" value="#007bff">
                </div>
                
                <div class="form-group">
                    <label for="greeting">Greeting Message</label>
                    <input type="text" id="greeting" placeholder="Hello! How can I help you?">
                </div>
                
                <div class="form-group">
                    <label for="debug">Debug Mode</label>
                    <select id="debug">
                        <option value="false">Disabled</option>
                        <option value="true">Enabled</option>
                    </select>
                </div>
            </form>
        </div>

        <!-- Test Controls -->
        <div class="test-section">
            <h2>Test Controls</h2>
            <div class="test-controls">
                <button onclick="initializeChatbot()">Initialize Chatbot</button>
                <button onclick="destroyChatbot()" disabled id="destroyBtn">Destroy Chatbot</button>
                <button onclick="testConfiguration()">Test Configuration</button>
                <button onclick="clearLogs()">Clear Logs</button>
                <button onclick="downloadLogs()">Download Logs</button>
            </div>
        </div>

        <!-- Inline Container (shown when inline mode is selected) -->
        <div class="test-section" id="inlineSection" style="display: none;">
            <h2>Inline Chatbot Container</h2>
            <div id="inline-chatbot-container" class="inline-container"></div>
        </div>

        <!-- Logs Section -->
        <div class="test-section">
            <h2>Logs</h2>
            <div id="logs" class="logs">
                <div class="log-entry info">[INFO] Test page loaded successfully</div>
            </div>
        </div>
    </div>

</body>
</html>
