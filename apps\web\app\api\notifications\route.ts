import { NextResponse } from "next/server";
import db from "@/lib/shared-db";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/next-auth";

// Get user notifications
export async function GET(req: Request) {
  try {
    const session: any = await getServerSession(authOptions);
    if (!session) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const { searchParams } = new URL(req.url);
    const status = searchParams.get("status"); // 'unread', 'read', 'all'
    const limit = parseInt(searchParams.get("limit") || "20");
    const offset = parseInt(searchParams.get("offset") || "0");

    // Get user's current tenant from membership
    const membership = await db.membership.findFirst({
      where: {
        userId: session.userId,
      },
      include: {
        tenant: true,
      },
    });

    if (!membership) {
      return new NextResponse("No tenant access", { status: 403 });
    }

    // Build where clause
    const where: any = {
      userId: session.userId,
      tenantId: membership.tenantId,
    };

    if (status && status !== "all") {
      where.status = status.toUpperCase();
    }

    // Get notifications
    const notifications = await db.notification.findMany({
      where,
      include: {
        triggerUser: {
          select: {
            id: true,
            name: true,
            email: true,
            image: true,
          },
        },
        chat: {
          include: {
            ThreadShare: {
              select: {
                shareToken: true,
              },
            },
          },
        },
        relatedMessage: {
          select: {
            id: true,
            content: true,
          },
        },
        comment: {
          select: {
            id: true,
            content: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
      take: limit,
      skip: offset,
    });

    // Get unread count
    const unreadCount = await db.notification.count({
      where: {
        userId: session.userId,
        tenantId: membership.tenantId,
        status: "UNREAD",
      },
    });

    return NextResponse.json({
      notifications,
      unreadCount,
      hasMore: notifications.length === limit,
    });
  } catch (error) {
    console.error("[NOTIFICATIONS_GET]", error);
    return NextResponse.json({ error: "Internal Error" }, { status: 500 });
  }
}

// Mark notifications as read
export async function PUT(req: Request) {
  try {
    const session: any = await getServerSession(authOptions);
    if (!session) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const { notificationIds, markAllAsRead } = await req.json();

    // Get user's current tenant from membership
    const membership = await db.membership.findFirst({
      where: {
        userId: session.userId,
      },
    });

    if (!membership) {
      return new NextResponse("No tenant access", { status: 403 });
    }

    if (markAllAsRead) {
      // Mark all notifications as read
      await db.notification.updateMany({
        where: {
          userId: session.userId,
          tenantId: membership.tenantId,
          status: "UNREAD",
        },
        data: {
          status: "READ",
        },
      });
    } else if (notificationIds && notificationIds.length > 0) {
      // Mark specific notifications as read
      await db.notification.updateMany({
        where: {
          id: {
            in: notificationIds,
          },
          userId: session.userId,
          tenantId: membership.tenantId,
        },
        data: {
          status: "READ",
        },
      });
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("[NOTIFICATIONS_PUT]", error);
    return NextResponse.json({ error: "Internal Error" }, { status: 500 });
  }
}
