"use client";

import React, { useRef, useState } from "react";
import {
  X,
  Upload,
  Loader2,
  FileImage,
  File,
  FileText,
  FileSpreadsheet,
  Music,
} from "lucide-react";
import { useLanguage } from "@/lib/language-context";
import { useTranslatedToast } from "@/hooks/use-translated-toast";
import { ImageAttachment } from "../types";
import { cn } from "@/lib/utils";
import { getChatAcceptAttribute } from "@/lib/constant/supported-extensions";

import { validateFileUpload, formatFileSize } from "@/lib/utils/file-utils";
import Image from "next/image";
import { copilotKitChatService } from "@/services/copilotkit-chat";
import { AudioFileStatus } from "./AudioProcessingStatus";
interface ImageUploadProps {
  onImagesSelected: (files: ImageAttachment[]) => void;
  selectedImages: ImageAttachment[];
  onRemoveImage: (fileId: string) => void;
  disabled?: boolean;
  maxImages?: number;
  setImageContext?: (imageContext: string) => void;
  setAudioContext?: (audioContext: string) => void;
}

function ImageUpload({
  onImagesSelected,
  selectedImages,
  setImageContext,
  setAudioContext,
  onRemoveImage,
  disabled = false,
  maxImages = 5,
}: ImageUploadProps) {
  const { t } = useLanguage();
  const toast = useTranslatedToast();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isDragOver, setIsDragOver] = useState(false);
  const [uploadingImages] = useState<Set<string>>(new Set());

  // Helper function to get file icon and colors based on MIME type
  const getFileIcon = (mimeType: string) => {
    if (mimeType.startsWith("image/")) {
      return {
        icon: FileImage,
        bgColor: "from-blue-50 to-blue-100 dark:from-blue-950 dark:to-blue-900",
        iconColor: "text-blue-600 dark:text-blue-400",
        textColor: "text-blue-700 dark:text-blue-300",
      };
    } else if (mimeType === "application/pdf") {
      return {
        icon: FileText,
        bgColor: "from-red-50 to-red-100 dark:from-red-950 dark:to-red-900",
        iconColor: "text-red-600 dark:text-red-400",
        textColor: "text-red-700 dark:text-red-300",
      };
    } else if (
      mimeType.includes("spreadsheet") ||
      mimeType.includes("excel") ||
      mimeType.includes("csv")
    ) {
      return {
        icon: FileSpreadsheet,
        bgColor:
          "from-green-50 to-green-100 dark:from-green-950 dark:to-green-900",
        iconColor: "text-green-600 dark:text-green-400",
        textColor: "text-green-700 dark:text-green-300",
      };
    } else if (
      mimeType.includes("wordprocessingml") ||
      mimeType.includes("ms-word")
    ) {
      return {
        icon: FileText,
        bgColor: "from-blue-50 to-blue-100 dark:from-blue-950 dark:to-blue-900",
        iconColor: "text-blue-600 dark:text-blue-400",
        textColor: "text-blue-700 dark:text-blue-300",
      };
    } else if (
      mimeType.includes("presentationml") ||
      mimeType.includes("ms-powerpoint")
    ) {
      return {
        icon: FileText,
        bgColor:
          "from-orange-50 to-orange-100 dark:from-orange-950 dark:to-orange-900",
        iconColor: "text-orange-600 dark:text-orange-400",
        textColor: "text-orange-700 dark:text-orange-300",
      };
    } else if (mimeType.includes("markdown")) {
      return {
        icon: FileText,
        bgColor:
          "from-indigo-50 to-indigo-100 dark:from-indigo-950 dark:to-indigo-900",
        iconColor: "text-indigo-600 dark:text-indigo-400",
        textColor: "text-indigo-700 dark:text-indigo-300",
      };
    } else if (mimeType.startsWith("audio/")) {
      return {
        icon: Music,
        bgColor:
          "from-purple-50 to-purple-100 dark:from-purple-950 dark:to-purple-900",
        iconColor: "text-purple-600 dark:text-purple-400",
        textColor: "text-purple-700 dark:text-purple-300",
      };
    } else if (mimeType.startsWith("text/")) {
      return {
        icon: FileText,
        bgColor: "from-gray-50 to-gray-100 dark:from-gray-950 dark:to-gray-900",
        iconColor: "text-gray-600 dark:text-gray-400",
        textColor: "text-gray-700 dark:text-gray-300",
      };
    }
    return {
      icon: File,
      bgColor: "from-gray-50 to-gray-100 dark:from-gray-950 dark:to-gray-900",
      iconColor: "text-gray-600 dark:text-gray-400",
      textColor: "text-gray-700 dark:text-gray-300",
    };
  };

  // Helper function to get file type label
  const getFileTypeLabel = (mimeType: string) => {
    if (mimeType === "application/pdf") return "PDF";
    if (mimeType.includes("spreadsheet") || mimeType.includes("excel"))
      return "Excel";
    if (mimeType.includes("csv")) return "CSV";
    if (mimeType.includes("wordprocessingml") || mimeType.includes("ms-word"))
      return "Word";
    if (
      mimeType.includes("presentationml") ||
      mimeType.includes("ms-powerpoint")
    )
      return "PowerPoint";
    if (mimeType.includes("markdown")) return "Markdown";
    if (mimeType.startsWith("audio/")) return "Audio";
    if (mimeType.startsWith("text/")) return "Text";
    if (mimeType.startsWith("image/")) return "Image";
    return "File";
  };

  const validateFile = (file: File): boolean => {
    const validation = validateFileUpload(file);

    if (!validation.valid) {
      toast.error(
        t("chat.invalidFileFormat") || validation.error || "Invalid file"
      );
      return false;
    }

    return true;
  };

  const processFiles = async (files: FileList | File[]) => {
    const fileArray = Array.from(files);
    const validFiles = fileArray.filter(validateFile);

    // Check if adding these files would exceed the limit
    const remainingSlots = maxImages - selectedImages.length;
    if (validFiles.length > remainingSlots) {
      if (remainingSlots === 0) {
        toast.error(
          t("chat.maxFilesReached") ||
            `Maximum ${maxImages} files reached. Remove some files to upload more.`
        );
      } else {
        toast.error(
          t("chat.tooManyFiles") ||
            `Can only upload ${remainingSlots} more file${
              remainingSlots === 1 ? "" : "s"
            }. Maximum ${maxImages} files allowed.`
        );
      }
      return;
    }

    const newFiles: ImageAttachment[] = [];
    toast.loading(t("chat.uploadingFiles") || "Uploading files...");

    const otherFiles = validFiles.filter(
      (file) => !file.type.startsWith("audio/")
    );

    // Start image/document analysis for non-media files (before upload)
    const imageAnalysisPromise =
      otherFiles.length > 0
        ? copilotKitChatService.imageAnalysis(otherFiles).catch((error) => {
            console.error("Image/document analysis failed:", error);
            return { image_context: "", error: "Image analysis failed" };
          })
        : Promise.resolve({ image_context: "" });

    // Upload all files to get blob URLs
    const fileUploadPromises = validFiles.map(async (file) => {
      try {
        const preview = URL.createObjectURL(file);
        const uploadedFile = await uploadFileToServer(file);

        if (!uploadedFile) {
          toast.error("toast.fileUploadFailed", { filename: file.name });
          return;
        }

        const fileAttachment: ImageAttachment = {
          id: uploadedFile.id,
          url: uploadedFile.url,
          name: uploadedFile.name,
          type: uploadedFile.type,
          size: uploadedFile.size,
          preview,
        };

        newFiles.push(fileAttachment);
      } catch (error) {
        toast.remove();
        console.error("Error processing file:", error);
        toast.error(t("chat.fileProcessingError") || "Error processing file.");
      }
    });

    try {
      // Wait for both file uploads and image analysis to complete
      const [imageAnalysisResult] = await Promise.all([
        imageAnalysisPromise,
        Promise.all(fileUploadPromises),
      ]);

      // Now analyze media files using uploaded file info
      console.log("\n🔍 Analyzing uploaded files by type...");
      const uploadedAudioFiles = newFiles.filter((file) =>
        file.type.startsWith("audio/")
      );

      // Handle audio processing asynchronously
      let audioAnalysisResult: any = { audio_context: "" };
      let audioJobId: string | null = null;

      if (uploadedAudioFiles.length > 0) {
        try {
          console.log("🎵 Starting asynchronous audio analysis...");
          const audioResponse =
            await copilotKitChatService.audioAnalysis(uploadedAudioFiles);

          if (audioResponse.job_id) {
            audioJobId = audioResponse.job_id;
            console.log(`🎵 Audio processing job started: ${audioJobId}`);

            // Set initial processing status - don't set audio_context yet to avoid confusion
            audioAnalysisResult = {
              audio_context: "", // Empty initially, will be populated when processing completes
              job_id: audioJobId,
              status: "processing",
            };

            // Start background status monitoring
            if (audioJobId) {
              copilotKitChatService.startAudioProcessingStatusLogging(
                audioJobId,
                uploadedAudioFiles[0]?.id || "",
                "", // userId - will be handled by auth headers
                "" // tenantId - will be handled by auth headers
              );
            }
          } else {
            console.error("❌ No job ID returned from audio analysis");
            audioAnalysisResult = {
              audio_context: "",
              error: "Audio analysis failed to start",
            };
          }
        } catch (error) {
          console.error("❌ Audio analysis failed:", error);
          audioAnalysisResult = {
            audio_context: "",
            error: "Audio analysis failed",
          };
        }
      }

      // Set context from analysis results
      const imageContext = imageAnalysisResult?.image_context || "";
      const audioContext = audioAnalysisResult?.audio_context || "";

      // Check for analysis errors and show warnings
      const hasImageError = imageAnalysisResult?.error;
      const hasAudioError = audioAnalysisResult?.error;

      setImageContext?.(imageContext);
      setAudioContext?.(audioContext);

      newFiles.forEach((file) => {
        if (file.type.startsWith("audio/")) {
          // Audio files get audio context and job ID for status tracking
          (file as any).audioContext = audioContext;
          (file as any).audioJobId = audioJobId;
          (file as any).audioStatus = audioAnalysisResult?.status || "unknown";
        } else {
          // Other files (images, PDFs, documents) get image context
          file.imageContext = imageContext;
        }
      });

      toast.remove();

      // Show appropriate success/warning message
      if (hasImageError || hasAudioError) {
        const errorMessages: string[] = [];
        if (hasImageError) errorMessages.push("image analysis");
        if (hasAudioError) errorMessages.push("audio analysis");
        toast.success("toast.filesUploadedWithAnalysisErrors", {
          errors: errorMessages.join(" and "),
        });
      } else {
        toast.success("chat.uploadSuccess");
      }

      if (newFiles.length > 0) {
        onImagesSelected([...selectedImages, ...newFiles]);
      }
    } catch (error) {
      toast.remove();
      console.error("Error in file processing:", error);
      toast.error(t("chat.fileProcessingError") || "Error processing files.");
    }
  };

  const uploadFileToServer = async (
    file: File
  ): Promise<ImageAttachment | null> => {
    try {
      // Get tenant ID from cookies or context
      const tenantId = document.cookie
        .split("; ")
        .find((row) => row.startsWith("currentOrganizationId="))
        ?.split("=")[1];
      const userId = document.cookie
        .split("; ")
        .find((row) => row.startsWith("userId="))
        ?.split("=")[1];

      if (!tenantId) {
        throw new Error("Tenant ID not found");
      }

      const formData = new FormData();
      formData.append("file", file);
      formData.append("tenantId", tenantId);

      const response = await fetch("/api/chat/upload-image", {
        method: "POST",
        body: formData,
        headers: {
          "x-user-id": userId ?? "",
          "x-tenant-id": tenantId,
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Upload failed");
      }

      const result = await response.json();
      return result;
    } catch (error) {
      console.error("Error uploading file:", error);
      return null;
    }
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files && files.length > 0) {
      processFiles(files);
    }
    // Reset input value to allow selecting the same file again
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const handleDragOver = (event: React.DragEvent) => {
    event.preventDefault();
    // Only allow drag over if we haven't reached the limit
    if (selectedImages.length < maxImages && !disabled) {
      setIsDragOver(true);
    }
  };

  const handleDragLeave = (event: React.DragEvent) => {
    event.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (event: React.DragEvent) => {
    event.preventDefault();
    setIsDragOver(false);

    // Check if we can accept more files
    if (selectedImages.length >= maxImages) {
      toast.error(
        t("chat.maxFilesReached") ||
          `Maximum ${maxImages} files reached. Remove some files to upload more.`
      );
      return;
    }

    const files = event.dataTransfer.files;
    if (files && files.length > 0) {
      processFiles(files);
    }
  };

  const handleRemoveImage = (fileId: string) => {
    // Clean up object URL to prevent memory leaks
    const file = selectedImages.find((img) => img.id === fileId);
    if (file?.preview) {
      URL.revokeObjectURL(file.preview);
    }
    onRemoveImage(fileId);
  };

  return (
    <div className="">
      {/* Upload Button and Drag Area */}
      <div
        className={cn(
          "relative transition-all duration-200 ease-in-out",
          isDragOver && "scale-105"
        )}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        <input
          ref={fileInputRef}
          type="file"
          accept={getChatAcceptAttribute()}
          multiple
          onChange={handleFileSelect}
          className="hidden"
          disabled={disabled || selectedImages.length >= maxImages}
        />

        {/* Enhanced Drag overlay */}
        {isDragOver && selectedImages.length < maxImages && (
          <div className="absolute inset-0 bg-gradient-to-br from-primary/10 to-primary/5 border-2 border-dashed border-primary/60 rounded-xl flex items-center justify-center z-10 backdrop-blur-sm">
            <div className="text-center animate-pulse">
              <div className="bg-primary/10 rounded-full p-3 mx-auto mb-2 w-fit">
                <Upload className="h-6 w-6 text-primary" />
              </div>
              <p className="text-sm text-primary font-medium">
                {t("chat.dropFilesHere") || "Drop files here"}
              </p>
              <p className="text-xs text-primary/70 mt-1">
                {t("chat.slotsRemaining", {
                  count: maxImages - selectedImages.length,
                })}
              </p>
            </div>
          </div>
        )}
      </div>

      {/* Enhanced File Previews - Hide audio files during processing */}
      {selectedImages.length > 0 && (
        <div className="flex gap-2 w-full mb-2 overflow-x-auto pb-1">
          {selectedImages
            .filter((file) => {
              // Hide audio files that are currently processing
              if (file.type.startsWith("audio/")) {
                const audioJobId = (file as any).audioJobId;
                const audioStatus = (file as any).audioStatus;
                // Only show audio files if processing is completed or failed (not processing)
                return (
                  !audioJobId ||
                  audioStatus === "completed" ||
                  audioStatus === "failed"
                );
              }
              // Show all non-audio files
              return true;
            })
            .map((file) => {
              const isUploading = uploadingImages.has(file.id);

              return (
                <div
                  key={file.id}
                  className={cn(
                    "relative group bg-gradient-to-br from-muted/50 to-muted rounded-lg overflow-hidden border border-border/50",
                    "hover:border-primary/50 transition-all duration-200 hover:shadow-lg",
                    "w-28 h-12 sm:w-32 sm:h-14 flex-shrink-0",
                    isUploading && "animate-pulse"
                  )}
                >
                  {file.type.startsWith("image/") ? (
                    <Image
                      src={file.url || file.preview || ""}
                      alt={file.name}
                      width={128}
                      height={56}
                      className={cn(
                        "w-full h-full object-cover transition-all duration-200",
                        "group-hover:scale-105",
                        isUploading && "opacity-70"
                      )}
                    />
                  ) : (
                    (() => {
                      const {
                        icon: IconComponent,
                        bgColor,
                        iconColor,
                        textColor,
                      } = getFileIcon(file.type);
                      const typeLabel = getFileTypeLabel(file.type);
                      return (
                        <div
                          className={`flex items-center justify-center w-full h-full bg-gradient-to-br ${bgColor} p-2 gap-2`}
                        >
                          <IconComponent
                            className={`h-5 w-5 ${iconColor} flex-shrink-0`}
                          />
                          <p
                            className={`text-[10px] ${textColor} font-medium text-center line-clamp-2 flex-1`}
                          >
                            {typeLabel}
                          </p>
                        </div>
                      );
                    })()
                  )}

                  {/* Loading overlay */}
                  {isUploading && (
                    <div className="absolute inset-0 bg-black/40 flex items-center justify-center">
                      <div className="bg-white/90 rounded-full p-2">
                        <Loader2 className="h-4 w-4 animate-spin text-primary" />
                      </div>
                    </div>
                  )}

                  {/* Small remove button in top-right corner on hover */}
                  <button
                    onClick={() => handleRemoveImage(file.id)}
                    disabled={isUploading}
                    className={cn(
                      "absolute -top-1 -right-1 bg-destructive text-destructive-foreground",
                      "rounded-full p-1 opacity-0 group-hover:opacity-100",
                      "transition-all duration-200 hover:scale-110 active:scale-95",
                      "shadow-md border border-background z-10",
                      isUploading && "opacity-0 cursor-not-allowed"
                    )}
                    title={t("chat.removeFile")}
                  >
                    <X className="h-3 w-3" />
                  </button>

                  {/* Enhanced File info overlay */}
                  <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-1.5 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                    <div className="flex items-center justify-between gap-1">
                      <div className="flex items-center gap-1 flex-1 min-w-0">
                        {(() => {
                          const { icon: IconComponent } = getFileIcon(
                            file.type
                          );
                          return (
                            <IconComponent className="h-2.5 w-2.5 text-white/80 flex-shrink-0" />
                          );
                        })()}
                        <p className="text-[10px] text-white/90 truncate font-medium">
                          {file.name}
                        </p>
                      </div>
                      <p className="text-[10px] text-white/70 flex-shrink-0">
                        {formatFileSize(file.size)}
                      </p>
                    </div>
                  </div>
                </div>
              );
            })}
        </div>
      )}

      {/* Audio Processing Status - Only show during processing */}
      {selectedImages.some((file) => {
        if (!file.type.startsWith("audio/")) return false;
        const audioJobId = (file as any).audioJobId;
        const audioStatus = (file as any).audioStatus;
        // Only show if there's a job ID and status is actively processing (not completed or failed)
        return (
          audioJobId && audioStatus !== "completed" && audioStatus !== "failed"
        );
      }) && (
        <div className="mt-3 space-y-2">
          <div className="text-xs font-medium text-muted-foreground mb-2">
            {t("audioProcessing.title") || "Audio Processing Status"}
          </div>
          {selectedImages
            .filter((file) => {
              if (!file.type.startsWith("audio/")) return false;
              const audioJobId = (file as any).audioJobId;
              const audioStatus = (file as any).audioStatus;
              // Only show files that are actively processing (not completed or failed)
              return (
                audioJobId &&
                audioStatus !== "completed" &&
                audioStatus !== "failed"
              );
            })
            .map((file) => (
              <AudioFileStatus
                key={file.id}
                file={{
                  name: file.name,
                  audioJobId: (file as any).audioJobId,
                  audioStatus: (file as any).audioStatus,
                }}
                onStatusUpdate={(audioContext) => {
                  console.log(
                    `🎵 Audio processing completed for ${file.name}:`,
                    audioContext
                  );

                  // Update the audio context when processing completes
                  setAudioContext?.(audioContext);

                  // Update the file status to completed so UI can transition properly
                  const updatedImages = selectedImages.map((img) =>
                    img.id === file.id
                      ? ({ ...img, audioStatus: "completed" } as any)
                      : img
                  );
                  onImagesSelected(updatedImages);
                }}
                onError={(error) => {
                  console.error(
                    `❌ Audio processing failed for ${file.name}:`,
                    error
                  );

                  // Update the file status to failed so UI can handle it properly
                  const updatedImages = selectedImages.map((img) =>
                    img.id === file.id
                      ? ({ ...img, audioStatus: "failed" } as any)
                      : img
                  );
                  onImagesSelected(updatedImages);
                }}
              />
            ))}
        </div>
      )}

      {/* Upload Counter */}
      {selectedImages.length > 0 && (
        <div className="flex items-center justify-between mb-2 mt-2">
          <div className="flex items-center gap-2">
            <div
              className={cn(
                "text-xs font-medium px-2 py-1 rounded-full transition-colors duration-200",
                selectedImages.length >= maxImages
                  ? "bg-destructive/10 text-destructive border border-destructive/20"
                  : selectedImages.length >= maxImages - 1
                    ? "bg-orange-100 text-orange-700 border border-orange-200 dark:bg-orange-950 dark:text-orange-300 dark:border-orange-800"
                    : "bg-muted text-muted-foreground border border-border"
              )}
            >
              {t("chat.filesUploaded", {
                current: selectedImages.length,
                max: maxImages,
              })}
            </div>
            {selectedImages.length >= maxImages && (
              <span className="text-xs text-destructive font-medium">
                {t("chat.limitReached")}
              </span>
            )}
          </div>
        </div>
      )}
    </div>
  );
}

export { ImageUpload };
