"""
Changelog management endpoints for deployment/release notes
"""
import logging
from datetime import datetime
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, HTTPException, Depends, Query
from pydantic import BaseModel
from prisma import Prisma
from src.api.deps import get_db
from src.api.auth import get_current_user

logger = logging.getLogger(__name__)

router = APIRouter()

class ChangelogCreate(BaseModel):
    title: str
    content: str
    version: Optional[str] = None
    type: str = "RELEASE"  # RELEASE, HOTFIX, MAINTENANCE, ANNOUNCEMENT
    priority: str = "MEDIUM"  # LOW, MEDIUM, HIGH, CRITICAL
    targetTenants: List[str] = []  # Empty means all tenants
    targetEnvironment: Optional[str] = None  # "dev", "qa", "prod", null means all
    publishedAt: Optional[datetime] = None
    expiresAt: Optional[datetime] = None
    githubCommitSha: Optional[str] = None
    deploymentId: Optional[str] = None

class ChangelogUpdate(BaseModel):
    title: Optional[str] = None
    content: Optional[str] = None
    version: Optional[str] = None
    type: Optional[str] = None
    priority: Optional[str] = None
    isActive: Optional[bool] = None
    targetTenants: Optional[List[str]] = None
    targetEnvironment: Optional[str] = None
    publishedAt: Optional[datetime] = None
    expiresAt: Optional[datetime] = None

class ChangelogResponse(BaseModel):
    id: str
    title: str
    content: str
    version: Optional[str]
    type: str
    priority: str
    isActive: bool
    targetTenants: List[str]
    targetEnvironment: Optional[str]
    publishedAt: datetime
    expiresAt: Optional[datetime]
    githubCommitSha: Optional[str]
    deploymentId: Optional[str]
    authorId: Optional[str]
    createdAt: datetime
    updatedAt: datetime

class UserChangelogViewCreate(BaseModel):
    changelogId: str
    dismissed: bool = False

@router.post("/", response_model=ChangelogResponse)
async def create_changelog(
    changelog_data: ChangelogCreate,
    db: Prisma = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """
    Create a new changelog entry (Admin only)
    """
    try:
        # Create changelog
        changelog = await db.changelog.create(
            data={
                "title": changelog_data.title,
                "content": changelog_data.content,
                "version": changelog_data.version,
                "type": changelog_data.type,
                "priority": changelog_data.priority,
                "targetTenants": changelog_data.targetTenants,
                "targetEnvironment": changelog_data.targetEnvironment,
                "publishedAt": changelog_data.publishedAt or datetime.utcnow(),
                "expiresAt": changelog_data.expiresAt,
                "githubCommitSha": changelog_data.githubCommitSha,
                "deploymentId": changelog_data.deploymentId,
                "authorId": current_user.get("id"),
            }
        )
        
        return ChangelogResponse(**changelog.dict())
        
    except Exception as e:
        logger.error(f"Error creating changelog: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to create changelog: {str(e)}")

@router.get("/", response_model=List[ChangelogResponse])
async def get_changelogs(
    active_only: bool = Query(True, description="Filter for active changelogs only"),
    environment: Optional[str] = Query(None, description="Filter by target environment"),
    tenant_id: Optional[str] = Query(None, description="Filter by tenant ID"),
    limit: int = Query(50, description="Maximum number of changelogs to return"),
    skip: int = Query(0, description="Number of changelogs to skip"),
    db: Prisma = Depends(get_db)
):
    """
    Get changelogs with optional filtering
    """
    try:
        # Build filter conditions
        where_conditions = {}
        
        if active_only:
            where_conditions["isActive"] = True
            
        if environment:
            where_conditions["OR"] = [
                {"targetEnvironment": environment},
                {"targetEnvironment": None}  # Include changelogs for all environments
            ]
            
        # Handle tenant filtering
        if tenant_id:
            where_conditions["OR"] = where_conditions.get("OR", []) + [
                {"targetTenants": {"has": tenant_id}},
                {"targetTenants": {"equals": []}}  # Include changelogs for all tenants
            ]
        
        # Add expiration filter
        current_time = datetime.utcnow()
        where_conditions["OR"] = where_conditions.get("OR", []) + [
            {"expiresAt": {"gt": current_time}},
            {"expiresAt": None}  # Include changelogs without expiration
        ]
        
        changelogs = await db.changelog.find_many(
            where=where_conditions,
            order_by={"publishedAt": "desc"},
            take=limit,
            skip=skip
        )
        
        return [ChangelogResponse(**changelog.dict()) for changelog in changelogs]
        
    except Exception as e:
        logger.error(f"Error fetching changelogs: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch changelogs: {str(e)}")

@router.get("/user/{user_id}", response_model=List[ChangelogResponse])
async def get_user_unviewed_changelogs(
    user_id: str,
    tenant_id: str = Query(..., description="User's tenant ID"),
    environment: Optional[str] = Query(None, description="Current environment"),
    db: Prisma = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """
    Get changelogs that a specific user hasn't viewed yet
    """
    try:
        # Verify user access (users can only see their own unviewed changelogs)
        if current_user.get("id") != user_id:
            raise HTTPException(status_code=403, detail="Access denied")
        
        # Get user's viewed changelog IDs
        viewed_changelogs = await db.userchangelogview.find_many(
            where={"userId": user_id},
            select={"changelogId": True}
        )
        viewed_ids = [view.changelogId for view in viewed_changelogs]
        
        # Build filter conditions for unviewed changelogs
        where_conditions = {
            "isActive": True,
            "publishedAt": {"lte": datetime.utcnow()},
            "id": {"notIn": viewed_ids} if viewed_ids else {}
        }
        
        # Filter by environment
        if environment:
            where_conditions["OR"] = [
                {"targetEnvironment": environment},
                {"targetEnvironment": None}
            ]
        
        # Filter by tenant
        where_conditions["OR"] = where_conditions.get("OR", []) + [
            {"targetTenants": {"has": tenant_id}},
            {"targetTenants": {"equals": []}}
        ]
        
        # Add expiration filter
        current_time = datetime.utcnow()
        where_conditions["OR"] = where_conditions.get("OR", []) + [
            {"expiresAt": {"gt": current_time}},
            {"expiresAt": None}
        ]
        
        changelogs = await db.changelog.find_many(
            where=where_conditions,
            order_by={"priority": "desc", "publishedAt": "desc"}
        )
        
        return [ChangelogResponse(**changelog.dict()) for changelog in changelogs]
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching user unviewed changelogs: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch unviewed changelogs: {str(e)}")

@router.post("/view")
async def mark_changelog_viewed(
    view_data: UserChangelogViewCreate,
    db: Prisma = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """
    Mark a changelog as viewed by the current user
    """
    try:
        user_id = current_user.get("id")
        
        # Check if already viewed
        existing_view = await db.userchangelogview.find_first(
            where={
                "userId": user_id,
                "changelogId": view_data.changelogId
            }
        )
        
        if existing_view:
            # Update existing view
            await db.userchangelogview.update(
                where={"id": existing_view.id},
                data={
                    "dismissed": view_data.dismissed,
                    "viewedAt": datetime.utcnow()
                }
            )
        else:
            # Create new view record
            await db.userchangelogview.create(
                data={
                    "userId": user_id,
                    "changelogId": view_data.changelogId,
                    "dismissed": view_data.dismissed,
                    "viewedAt": datetime.utcnow()
                }
            )
        
        return {"success": True, "message": "Changelog marked as viewed"}
        
    except Exception as e:
        logger.error(f"Error marking changelog as viewed: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to mark changelog as viewed: {str(e)}")

@router.put("/{changelog_id}", response_model=ChangelogResponse)
async def update_changelog(
    changelog_id: str,
    changelog_data: ChangelogUpdate,
    db: Prisma = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """
    Update an existing changelog (Admin only)
    """
    try:
        # Check if changelog exists
        existing_changelog = await db.changelog.find_unique(
            where={"id": changelog_id}
        )
        
        if not existing_changelog:
            raise HTTPException(status_code=404, detail="Changelog not found")
        
        # Update changelog
        update_data = {k: v for k, v in changelog_data.dict().items() if v is not None}
        
        changelog = await db.changelog.update(
            where={"id": changelog_id},
            data=update_data
        )
        
        return ChangelogResponse(**changelog.dict())
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating changelog: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to update changelog: {str(e)}")

@router.delete("/{changelog_id}")
async def delete_changelog(
    changelog_id: str,
    db: Prisma = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """
    Delete a changelog (Admin only)
    """
    try:
        # Check if changelog exists
        existing_changelog = await db.changelog.find_unique(
            where={"id": changelog_id}
        )
        
        if not existing_changelog:
            raise HTTPException(status_code=404, detail="Changelog not found")
        
        # Delete changelog (this will cascade delete user views)
        await db.changelog.delete(
            where={"id": changelog_id}
        )
        
        return {"success": True, "message": "Changelog deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting changelog: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to delete changelog: {str(e)}")
