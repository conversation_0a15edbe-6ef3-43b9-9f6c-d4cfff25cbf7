import { NextResponse } from "next/server";
import db from "@/lib/shared-db";
import crypto from "crypto";

export async function GET(request: Request) {
  try {
    const url = new URL(request.url);
    const token = url.searchParams.get("token");

    if (!token) {
      return NextResponse.json(
        { error: "Invitation token is required" },
        { status: 400 }
      );
    }

    // Find the invitation by token
    const invitation = await db.invitation.findUnique({
      where: { token },
      include: {
        tenant: true,
        inviter: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    if (!invitation) {
      return NextResponse.json(
        { error: "Invalid invitation token" },
        { status: 404 }
      );
    }

    // Check if invitation has expired
    if (invitation.expires < new Date()) {
      return NextResponse.json(
        { error: "Invitation has expired" },
        { status: 400 }
      );
    }

    // Check if invitation has already been accepted
    if (invitation.accepted) {
      return NextResponse.json(
        { error: "Invitation has already been accepted" },
        { status: 400 }
      );
    }

    // Check if a user with this email already exists
    const emailHash = crypto
      .createHash("sha256")
      .update(invitation.email)
      .digest("hex");

    const existingUser = await db.user.findUnique({
      where: { emailHash },
      select: {
        id: true,
        name: true,
        email: true,
      },
    });

    return NextResponse.json({
      data: {
        email: invitation.email,
        role: invitation.role,
        tenantName: invitation.tenant.name,
        inviterName: invitation.inviter.name,
        userExists: !!existingUser,
        existingUserName: existingUser?.name,
      },
    });
  } catch (error) {
    console.error("Verify invitation error:", error);
    return NextResponse.json(
      { error: "Failed to verify invitation" },
      { status: 500 }
    );
  }
}
