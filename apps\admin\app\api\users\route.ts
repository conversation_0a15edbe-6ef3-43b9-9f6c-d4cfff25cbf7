import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/next-auth";
import db from "@/lib/shared-db";

export async function GET(request: Request) {
  const session = await getServerSession(authOptions);

  if (!session) {
    return NextResponse.json(
      { error: "Unauthorized" },
      { status: 401 }
    );
  }

  try {
    
    const { searchParams } = new URL(request.url);

    // Parse query parameters
    const searchQuery = searchParams.get("search") || "";
    const page = parseInt(searchParams.get("page") || "1", 10);
    const limit = parseInt(searchParams.get("limit") || "5", 10);

    // Validate pagination parameters
    const validPage = Math.max(1, page);
    const validLimit = Math.min(Math.max(1, limit), 100); // Max 100 items per page

    // Build search conditions for users
    const userSearchConditions = searchQuery
      ? {
          OR: [
            { name: { contains: searchQuery, mode: "insensitive" as const } },
            { email: { contains: searchQuery, mode: "insensitive" as const } },
          ],
        }
      : {};

    // Build search conditions for tenants (for tenant name search)
    const tenantSearchConditions = searchQuery
      ? {
          OR: [
            { name: { contains: searchQuery, mode: "insensitive" as const } },
          ],
        }
      : {};

    // Fetch users with search and pagination
    const users = await db.user.findMany({
      where: userSearchConditions,
      include: {
        membership: {
          include: {
            tenant: {
              select: {
                id: true,
                name: true,
              },
            },
          },
          where: searchQuery
            ? {
                tenant: tenantSearchConditions,
              }
            : undefined,
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    // Transform the data to match the mockUsers structure
    let transformedUsers = users.flatMap((user: any) =>
      user.membership.map((membership: any) => ({
        id: user.id,
        name: user.name || "Unknown",
        email: user.email || "Unknown",
        tenantName: membership.tenant.name,
        tenantId: membership.tenantId,
        role: membership.role,
        lastActive: user.updatedAt.toISOString(), // Using updatedAt as proxy for lastActive
        createdAt: user.createdAt.toISOString(),
      }))
    );

    // Filter by all user fields if search query is provided
    if (searchQuery) {
      transformedUsers = transformedUsers.filter((user: any) =>
        user.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        user.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
        user.tenantName.toLowerCase().includes(searchQuery.toLowerCase()) ||
        user.role.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Get total count for pagination
    const total = transformedUsers.length;

    // Apply pagination
    const startIndex = (validPage - 1) * validLimit;
    const paginatedUsers = transformedUsers.slice(startIndex, startIndex + validLimit);

    // Get aggregation data for charts
    const usersByTenant = await db.membership.groupBy({
      by: ["tenantId"],
      _count: {
        userId: true,
      },
    });

    const usersByRole = await db.membership.groupBy({
      by: ["role"],
      _count: {
        userId: true,
      },
    });

    // Transform aggregation data
    const tenantStats = await Promise.all(
      usersByTenant.map(async (item: any) => {
        const tenant = await db.tenant.findUnique({
          where: { id: item.tenantId },
          select: { name: true },
        });
        return {
          name: tenant?.name || "Unknown",
          users: item._count.userId,
        };
      })
    );

    const roleStats = usersByRole.map((item: any) => ({
      name: item.role,
      users: item._count.userId,
    }));

    // Calculate pagination info
    const totalPages = Math.ceil(total / validLimit);

    return NextResponse.json({
      data: {
        users: paginatedUsers,
        usersByTenant: tenantStats,
        usersByRole: roleStats,
        pagination: {
          page: validPage,
          limit: validLimit,
          total,
          totalPages,
        },
        searchQuery,
      },
      message: "Users retrieved successfully",
    });
  } catch (error) {
    console.error("Error fetching users:", error);
    return NextResponse.json(
      { error: "Failed to fetch users" },
      { status: 500 }
    );
  }
}
