"use client";

import React, { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Bot,
  Plus,
  Search,
  MoreHorizontal,
  Edit,
  Trash2,
  Eye,
  Globe,
  Lock,
  Code,
  Monitor,
  Filter,
  Calendar,
  BarChart,
  MessageSquare,
  Clock,
  TrendingUp,
  Activity,
} from "lucide-react";
import Link from "next/link";
import {
  chatbotService,
  ChatbotConfig,
  getCompaniesForDropdown,
} from "@/services";
import { getCookie } from "@/utils/cookies";
import { toast } from "react-hot-toast";
import { useRouter } from "next/navigation";
import { useLanguage } from "@/lib/language-context";
interface ChatbotAnalytics {
  totalSessions: number;
  totalMessages: number;
  averageResponseTime: number;
  errorRate: number;
  lastActivity: string;
  activeUsers: number;
}

interface ChatbotWithAnalytics extends ChatbotConfig {
  analytics?: ChatbotAnalytics;
}

export default function ChatbotsPage() {
  const { t } = useLanguage();
  const [chatbots, setChatbots] = useState<ChatbotWithAnalytics[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [typeFilter, setTypeFilter] = useState<string>("all");
  const [accessFilter, setAccessFilter] = useState<string>("all");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [companies, setCompanies] = useState<{ id: string; name: string }[]>(
    []
  );
  const [analyticsLoading, setAnalyticsLoading] = useState(false);
  const router = useRouter();

  // Load companies for name mapping
  useEffect(() => {
    const loadCompanies = async () => {
      try {
        const result = await getCompaniesForDropdown();
        if ("companies" in result) {
          setCompanies(result.companies);
        } else {
          console.error("Failed to load companies:", result.error);
        }
      } catch (error) {
        console.error("Error loading companies:", error);
      }
    };

    loadCompanies();
  }, []);

  const loadChatbotAnalytics = async (chatbotIds: string[]) => {
    if (chatbotIds.length === 0) return;

    setAnalyticsLoading(true);
    try {
      const tenantId = getCookie("currentOrganizationId");
      if (!tenantId) return;

      // Fetch analytics for all chatbots
      const analyticsPromises = chatbotIds.map(async (chatbotId) => {
        try {
          const response = await fetch(
            `/api/analytics/chatbots/${chatbotId}?tenantId=${tenantId}&timeRange=7d`
          );
          if (response.ok) {
            const data = await response.json();
            return { chatbotId, analytics: data.analytics };
          }
        } catch (error) {
          console.error(
            `Error fetching analytics for chatbot ${chatbotId}:`,
            error
          );
        }
        return { chatbotId, analytics: null };
      });

      const analyticsResults = await Promise.all(analyticsPromises);

      // Update chatbots with analytics data
      setChatbots((prevChatbots) =>
        prevChatbots.map((chatbot) => {
          const analyticsResult = analyticsResults.find(
            (r) => r.chatbotId === chatbot.id
          );
          return {
            ...chatbot,
            analytics: analyticsResult?.analytics || {
              totalSessions: 0,
              totalMessages: 0,
              averageResponseTime: 0,
              errorRate: 0,
              lastActivity: "Never",
              activeUsers: 0,
            },
          };
        })
      );
    } catch (error) {
      console.error("Error loading chatbot analytics:", error);
    } finally {
      setAnalyticsLoading(false);
    }
  };

  // Load chatbots from API
  useEffect(() => {
    const loadChatbots = async () => {
      try {
        setIsLoading(true);
        setError(null);

        const tenantId = getCookie("currentOrganizationId");
        if (!tenantId) {
          setError("Organization not found. Please select an organization.");
          return;
        }

        const result = await chatbotService.getChatbots(tenantId);
        if ("error" in result) {
          setError(result.error);
        } else {
          setChatbots(result.chatbots);
          // Load analytics for the chatbots
          loadChatbotAnalytics(result?.chatbots.map((c) => c.id) as any);
        }
      } catch (error) {
        console.error("Error loading chatbots:", error);
        setError(t("chatbot.failedToLoadChatbots"));
      } finally {
        setIsLoading(false);
      }
    };

    loadChatbots();
  }, []);

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "web-snippet":
        return <Code className="h-4 w-4" />;
      case "inline-embedding":
        return <Monitor className="h-4 w-4" />;
      case "dedicated-page":
        return <Globe className="h-4 w-4" />;
      default:
        return <Bot className="h-4 w-4" />;
    }
  };

  const getTypeLabel = (type: string) => {
    switch (type) {
      case "web-snippet":
        return "Web Snippet";
      case "inline-embedding":
        return "Inline Embedding";
      case "dedicated-page":
        return "Dedicated Page";
      default:
        return type;
    }
  };

  const getStatusBadge = (isActive: boolean) => {
    return (
      <Badge
        variant={isActive ? "default" : "secondary"}
        style={
          isActive
            ? {
                backgroundColor: "hsl(var(--primary) / 0.1)",
                color: "hsl(var(--primary))",
                borderColor: "hsl(var(--primary) / 0.2)",
              }
            : {
                backgroundColor: "hsl(var(--muted))",
                color: "hsl(var(--muted-foreground))",
                borderColor: "hsl(var(--border))",
              }
        }
      >
        {isActive ? "Active" : "Inactive"}
      </Badge>
    );
  };

  const filteredChatbots = chatbots.filter((chatbot) => {
    const matchesSearch =
      chatbot.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      chatbot.description.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesType = typeFilter === "all" || chatbot.type === typeFilter;
    const matchesAccess =
      accessFilter === "all" || chatbot.access === accessFilter;
    const matchesStatus =
      statusFilter === "all" ||
      (statusFilter === "active" && chatbot.isActive) ||
      (statusFilter === "inactive" && !chatbot.isActive);

    return matchesSearch && matchesType && matchesAccess && matchesStatus;
  });

  const handleDelete = async (id: string) => {
    if (confirm(t("chatbot.deleteConfirm"))) {
      try {
        const tenantId = getCookie("currentOrganizationId");
        if (!tenantId) {
          toast.error("Organization not found");
          return;
        }

        const result = await chatbotService.deleteChatbot(id, tenantId);
        if ("error" in result) {
          toast.error(result.error);
        } else {
          setChatbots(chatbots.filter((bot) => bot.id !== id));
          toast.success(t("chatbot.deletedSuccess"));
        }
      } catch (error) {
        console.error("Error deleting chatbot:", error);
        toast.error(t("chatbot.failedToDelete"));
      }
    }
  };

  const toggleStatus = async (id: string) => {
    try {
      const tenantId = getCookie("currentOrganizationId");
      if (!tenantId) {
        toast.error("Organization not found");
        return;
      }

      const chatbot = chatbots.find((bot) => bot.id === id);
      if (!chatbot) return;

      const result = await chatbotService.updateChatbot(id, {
        isActive: !chatbot.isActive,
        tenantId,
      });

      if ("error" in result) {
        toast.error(result.error);
      } else {
        setChatbots(
          chatbots.map((bot) =>
            bot.id === id ? { ...bot, isActive: !bot.isActive } : bot
          )
        );
        toast.success(
          `Chatbot ${
            !chatbot.isActive ? "activated" : "deactivated"
          } successfully`
        );
      }
    } catch (error) {
      console.error("Error updating chatbot status:", error);
      toast.error("Failed to update chatbot status");
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold">{t("chatbot.management")}</h1>
          <p className="text-muted-foreground">{t("chatbot.manageChatbots")}</p>
        </div>
        <Link href="/settings/partner/chatbot">
          <Button>
            <Plus className="mr-2 h-4 w-4" />
            {t("chatbot.createNewChatbot")}
          </Button>
        </Link>
      </div>

      {/* Filters and Search */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-col sm:flex-row gap-4">
            {/* Search */}
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder={t("chatbot.searchChatbots")}
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>

            {/* Filters */}
            <div className="flex gap-2">
              <Select value={typeFilter} onValueChange={setTypeFilter}>
                <SelectTrigger className="w-[140px]">
                  <SelectValue placeholder="Type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">{t("chatbot.allTypes")}</SelectItem>
                  <SelectItem value="web-snippet">
                    {t("chatbot.webSnippet")}
                  </SelectItem>
                  <SelectItem value="inline-embedding">
                    {t("chatbot.inlineEmbedding")}
                  </SelectItem>
                  <SelectItem value="dedicated-page">
                    {t("chatbot.dedicatedPage")}
                  </SelectItem>
                </SelectContent>
              </Select>

              <Select value={accessFilter} onValueChange={setAccessFilter}>
                <SelectTrigger className="w-[120px]">
                  <SelectValue placeholder="Access" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">{t("chatbot.allAccess")}</SelectItem>
                  <SelectItem value="public">{t("chatbot.public")}</SelectItem>
                  <SelectItem value="private">
                    {t("chatbot.private")}
                  </SelectItem>
                </SelectContent>
              </Select>

              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-[120px]">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">{t("chatbot.allStatus")}</SelectItem>
                  <SelectItem value="active">{t("company.active")}</SelectItem>
                  <SelectItem value="inactive">
                    {t("company.inactive")}
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Chatbots Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Bot className="h-5 w-5" />
            Chatbots ({filteredChatbots.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <div className="h-8 w-8 animate-spin rounded-full border-2 border-blue-500 border-t-transparent" />
              <span className="ml-2 text-muted-foreground">
                {t("chatbot.loadingChatbots")}
              </span>
            </div>
          ) : error ? (
            <div className="text-center py-8">
              <p className="text-red-600 mb-4">{error}</p>
              <Button onClick={() => window.location.reload()}>Retry</Button>
            </div>
          ) : filteredChatbots.length === 0 ? (
            <div className="text-center py-8">
              <Bot className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">
                {t("chatbot.noChatbotsFound")}
              </h3>
              <p className="text-muted-foreground mb-4">
                {searchQuery ||
                typeFilter !== "all" ||
                accessFilter !== "all" ||
                statusFilter !== "all"
                  ? t("chatbot.tryAdjustingFilters")
                  : t("chatbot.getStartedByCreating")}
              </p>
              {!searchQuery &&
                typeFilter === "all" &&
                accessFilter === "all" &&
                statusFilter === "all" && (
                  <Link href="/settings/partner/chatbot">
                    <Button>
                      <Plus className="mr-2 h-4 w-4" />
                      {t("chatbot.createYourFirstChatbot")}
                    </Button>
                  </Link>
                )}
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>{t("common.name")}</TableHead>
                  <TableHead>{t("common.type")}</TableHead>
                  <TableHead>{t("company.access")}</TableHead>
                  <TableHead>{t("company.company")}</TableHead>
                  <TableHead>{t("company.status")}</TableHead>
                  <TableHead>
                    <div className="flex items-center gap-1">
                      <MessageSquare className="h-4 w-4" />
                      {t("chatbot.sessions")}
                    </div>
                  </TableHead>
                  <TableHead>
                    <div className="flex items-center gap-1">
                      <Activity className="h-4 w-4" />
                      {t("chatbot.messages")}
                    </div>
                  </TableHead>
                  <TableHead>
                    <div className="flex items-center gap-1">
                      <Clock className="h-4 w-4" />
                      {t("chatbot.responseTime")}
                    </div>
                  </TableHead>
                  <TableHead>
                    <div className="flex items-center gap-1">
                      <TrendingUp className="h-4 w-4" />
                      {t("chatbot.performance")}
                    </div>
                  </TableHead>
                  <TableHead className="text-right">
                    {t("common.actions")}
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredChatbots.map((chatbot) => (
                  <TableRow
                    key={chatbot.id}
                    onClick={() => {
                      router.push(`/settings/partner/chatbots/${chatbot.id}`);
                    }}
                  >
                    <TableCell>
                      <div>
                        <div className="font-medium">{chatbot.name}</div>
                        <div className="text-sm text-muted-foreground">
                          {chatbot.description}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        {getTypeIcon(chatbot.type)}
                        <span className="text-sm">
                          {getTypeLabel(chatbot.type)}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        {chatbot.access === "public" ? (
                          <Globe className="h-4 w-4" />
                        ) : (
                          <Lock className="h-4 w-4" />
                        )}
                        <span className="text-sm capitalize">
                          {chatbot.access}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>
                      {chatbot.companyIds && chatbot.companyIds.length > 0 ? (
                        <div className="flex flex-wrap gap-1">
                          {chatbot.companyIds
                            .map((companyId) => {
                              const company = companies.find(
                                (c) => c.id === companyId
                              );
                              return company
                                ? { id: companyId, name: company.name }
                                : null;
                            })
                            .filter(Boolean) // Remove null entries (deleted companies)
                            .slice(0, 2)
                            .map((company) => (
                              <Badge
                                key={company!.id}
                                variant="outline"
                                className="text-xs"
                                title={company!.name}
                              >
                                {company!.name}
                              </Badge>
                            ))}
                          {chatbot.companyIds.filter((companyId) =>
                            companies.find((c) => c.id === companyId)
                          ).length > 2 && (
                            <Badge variant="outline" className="text-xs">
                              +
                              {chatbot.companyIds.filter((companyId) =>
                                companies.find((c) => c.id === companyId)
                              ).length - 2}{" "}
                              {t("chatbot.moreCompanies")}
                            </Badge>
                          )}
                          {chatbot.companyIds.filter((companyId) =>
                            companies.find((c) => c.id === companyId)
                          ).length === 0 && (
                            <span className="text-sm text-muted-foreground">
                              {t("chatbot.noValidCompanies")}
                            </span>
                          )}
                        </div>
                      ) : (
                        <span className="text-sm text-muted-foreground">-</span>
                      )}
                    </TableCell>
                    <TableCell>
                      {getStatusBadge(chatbot.isActive ?? true)}
                    </TableCell>
                    {/* Sessions */}
                    <TableCell>
                      <div className="text-sm">
                        {analyticsLoading ? (
                          <div className="animate-pulse bg-muted h-4 w-8 rounded"></div>
                        ) : (
                          <div className="font-medium">
                            {chatbot.analytics?.totalSessions || 0}
                          </div>
                        )}
                      </div>
                    </TableCell>

                    {/* Messages */}
                    <TableCell>
                      <div className="text-sm">
                        {analyticsLoading ? (
                          <div className="animate-pulse bg-muted h-4 w-8 rounded"></div>
                        ) : (
                          <div className="font-medium">
                            {chatbot.analytics?.totalMessages || 0}
                          </div>
                        )}
                      </div>
                    </TableCell>

                    {/* Average Response Time */}
                    <TableCell>
                      <div className="text-sm">
                        {analyticsLoading ? (
                          <div className="animate-pulse bg-muted h-4 w-12 rounded"></div>
                        ) : (
                          <div className="font-medium">
                            {chatbot.analytics?.averageResponseTime
                              ? `${chatbot.analytics.averageResponseTime.toFixed(
                                  1
                                )}s`
                              : "N/A"}
                          </div>
                        )}
                      </div>
                    </TableCell>

                    {/* Performance */}
                    <TableCell>
                      <div className="text-sm">
                        {analyticsLoading ? (
                          <div className="animate-pulse bg-muted h-4 w-16 rounded"></div>
                        ) : (
                          <div className="space-y-1">
                            <div className="flex items-center gap-1">
                              <div
                                className={`w-2 h-2 rounded-full ${
                                  (chatbot.analytics?.errorRate || 0) < 5
                                    ? "bg-green-500"
                                    : (chatbot.analytics?.errorRate || 0) < 15
                                      ? "bg-yellow-500"
                                      : "bg-red-500"
                                }`}
                              ></div>
                              <span className="text-xs">
                                {(chatbot.analytics?.errorRate || 0).toFixed(1)}
                                % {t("chatbot.errorRate")}
                              </span>
                            </div>
                            <div className="text-xs text-muted-foreground">
                              {chatbot.analytics?.activeUsers || 0} active users
                            </div>
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem asChild>
                            <Link
                              href={`/settings/partner/chatbots/${chatbot.id}`}
                            >
                              <Eye className="mr-2 h-4 w-4" />
                              {t("chatbot.viewDetails")}
                            </Link>
                          </DropdownMenuItem>
                          <DropdownMenuItem asChild>
                            <Link
                              href={`/settings/partner/chatbots/${chatbot.id}/edit`}
                            >
                              <Edit className="mr-2 h-4 w-4" />
                              {t("chatbot.edit")}
                            </Link>
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() =>
                              chatbot.id && toggleStatus(chatbot.id)
                            }
                          >
                            <Bot className="mr-2 h-4 w-4" />
                            {chatbot.isActive
                              ? t("chatbot.deactivate")
                              : t("chatbot.activate")}
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() =>
                              chatbot.id && handleDelete(chatbot.id)
                            }
                            className="text-red-600"
                          >
                            <Trash2 className="mr-2 h-4 w-4" />
                            {t("chatbot.delete")}
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
