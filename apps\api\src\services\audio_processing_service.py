"""
Audio Processing Service for handling asynchronous audio analysis jobs.

This service manages the lifecycle of audio processing jobs, including:
- Creating and tracking audio processing jobs
- Background processing with Azure Video Indexer
- Job status updates and result delivery
- Integration with chat interface for real-time updates
"""

import asyncio
import logging
import uuid
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any

from bson import ObjectId
from src.services.azure_video_indexer import azure_video_indexer_service

logger = logging.getLogger(__name__)


class AudioProcessingService:
    """Service for managing asynchronous audio processing jobs."""

    def __init__(self):
        self.db_client = None  # Will be set by endpoints
        self._processing_tasks: Dict[str, asyncio.Task] = {}
    
    async def create_audio_processing_job(
        self,
        audio_files: List[Dict[str, Any]],
        user_id: str,
        tenant_id: str,
        chat_id: Optional[str] = None,
        file_id: Optional[str] = None,
        workspace_slug: Optional[str] = None
    ) -> str:
        """
        Create a new audio processing job and start background processing.
        
        Args:
            audio_files: List of audio file information (url, name, type, size)
            user_id: ID of the user requesting the processing
            tenant_id: ID of the tenant
            chat_id: Optional chat ID for context
            file_id: Optional file ID for status updates
            workspace_slug: Optional workspace slug for indexing
            
        Returns:
            Job ID for tracking the processing status
        """
        try:
            # Generate unique job ID
            job_id = str(uuid.uuid4())
            
            logger.info(f"Creating audio processing job {job_id} for user {user_id}")
            
            # Create job record in database
            job_data = {
                "jobId": job_id,
                "status": "pending",
                "audioFiles": audio_files,
                "userId": ObjectId(user_id),
                "tenantId": ObjectId(tenant_id),
                "chatId": ObjectId(chat_id) if chat_id else None,
                "fileId": file_id,  # Store file ID for status updates
                "workspaceSlug": workspace_slug,  # Store workspace slug for indexing
                "createdAt": datetime.now(timezone.utc),
                "updatedAt": datetime.now(timezone.utc)
            }

            await self.db_client.AudioProcessingJob.insert_one(job_data)
            
            # Start background processing
            task = asyncio.create_task(self._process_audio_job(job_id))
            self._processing_tasks[job_id] = task
            
            logger.info(f"Audio processing job {job_id} created and background processing started")
            return job_id
            
        except Exception as e:
            logger.error(f"Failed to create audio processing job: {e}")
            raise
    
    async def get_job_status(self, job_id: str) -> Optional[Dict[str, Any]]:
        """
        Get the current status of an audio processing job.
        Checks Azure Video Indexer status and updates job when processing completes.

        Args:
            job_id: The job ID to check

        Returns:
            Job status information or None if job not found
        """
        try:
            job = await self.db_client.AudioProcessingJob.find_one(
                {"jobId": job_id}
            )

            if not job:
                return None

            current_status = job.get("status", "unknown")
            azure_video_ids = job.get("azureVideoIds", [])

            # If job is still processing, check Azure Video Indexer status
            if current_status == "processing" and azure_video_ids:
                await self._check_and_update_azure_status(job_id, azure_video_ids)

                # Refetch job after potential update
                job = await self.db_client.AudioProcessingJob.find_one(
                    {"jobId": job_id}
                )

            return {
                "jobId": job["jobId"],
                "status": job["status"],
                "audioContext": job.get("audioContext"),
                "errorMessage": job.get("errorMessage"),
                "createdAt": job["createdAt"].isoformat() if job.get("createdAt") else None,
                "updatedAt": job["updatedAt"].isoformat() if job.get("updatedAt") else None,
                "completedAt": job["completedAt"].isoformat() if job.get("completedAt") else None,
                "filesProcessed": len(job["audioFiles"]) if job.get("audioFiles") else 0
            }

        except Exception as e:
            logger.error(f"Failed to get job status for {job_id}: {e}")
            return None

    async def _check_and_update_azure_status(self, job_id: str, azure_video_ids: list):
        """
        Check Azure Video Indexer status and update job when processing completes.

        Args:
            job_id: The job ID to update
            azure_video_ids: List of Azure Video IDs to check
        """
        try:
            all_completed = True
            audio_analyses = []

            for video_id in azure_video_ids:
                # Check if audio is processed in Azure Video Indexer
                video_index = await azure_video_indexer_service.get_video_index(video_id)

                if not video_index:
                    all_completed = False
                    continue

                state = video_index.get("state", "").lower()

                if state == "processed":
                    # Extract analysis data for audio
                    transcript = azure_video_indexer_service.extract_transcript(video_index)
                    metadata = azure_video_indexer_service.extract_audio_metadata(video_index)

                    # Format analysis result for audio
                    audio_name = video_index.get("name", f"Audio {video_id}")
                    analysis_text = f"Audio: {audio_name}\n\n"

                    if transcript:
                        analysis_text += f"Transcript:\n{transcript}\n\n"

                    if metadata:
                        duration = metadata.get('duration', 0)
                        language = metadata.get('language', 'unknown')
                        keywords = metadata.get('keywords', [])
                        sentiment = metadata.get('sentiment', 'neutral')
                        
                        analysis_text += f"Duration: {duration} seconds\n"
                        analysis_text += f"Language: {language}\n"
                        if keywords:
                            analysis_text += f"Keywords: {', '.join(keywords[:5])}\n"
                        analysis_text += f"Sentiment: {sentiment}\n"

                    audio_analyses.append(analysis_text)

                elif state == "failed":
                    logger.error(f"Audio processing failed for {video_id}")
                    audio_analyses.append(f"Audio {video_id}: Processing failed")
                    # Continue processing other audio files even if one fails
                else:
                    # Still processing
                    all_completed = False

            # If all audio files are completed, update job status
            if all_completed and audio_analyses:
                audio_context = "\n\n".join(audio_analyses)
                await self._complete_job(job_id, audio_context, azure_video_ids)

                # Complete file indexing with audio analysis results
                await self.complete_file_indexing(job_id, audio_analyses)

                # Update file status to COMPLETED
                await self._update_file_status_to_completed(job_id)

                logger.info(f"Audio processing job {job_id} completed successfully")
            elif all_completed and not audio_analyses:
                # All audio files failed
                await self._fail_job(job_id, "All audio processing failed")

                # Update file status to FAILED
                await self._update_file_status_to_failed(job_id)

        except Exception as e:
            logger.error(f"Error checking Azure status for job {job_id}: {e}")

    async def _process_audio_job(self, job_id: str):
        """
        Background processing of audio job.
        
        Args:
            job_id: The job ID to process
        """
        try:
            logger.info(f"Starting background audio processing for job {job_id}")
            
            # Get job details
            job = await self.db_client.AudioProcessingJob.find_one({"jobId": job_id})
            if not job:
                logger.error(f"Audio processing job {job_id} not found")
                return
            
            # Update status to processing
            await self.db_client.AudioProcessingJob.update_one(
                {"jobId": job_id},
                {
                    "$set": {
                        "status": "processing",
                        "updatedAt": datetime.now(timezone.utc)
                    }
                }
            )
            
            audio_files = job["audioFiles"]
            azure_video_ids = []
            
            # Check if Azure Video Indexer is configured
            if not azure_video_indexer_service.configured:
                logger.warning(f"Azure Video Indexer not configured for job {job_id}")

                # Create fallback audio context
                fallback_context = []
                for i, audio_file in enumerate(audio_files):
                    file_size_mb = round(audio_file.get('size', 0) / (1024 * 1024), 2)
                    audio_format = audio_file.get('type', 'unknown').split('/')[-1].upper()

                    fallback_msg = f"Audio {i+1} ({audio_file['name']}):\n"
                    fallback_msg += f"File uploaded successfully ({file_size_mb}MB {audio_format})\n"
                    fallback_msg += f"Azure Video Indexer not configured - transcription unavailable\n"
                    fallback_msg += f"To enable transcription, set AZURE_VIDEO_INDEXER_ACCOUNT_ID and ACCESS_TOKEN"

                    fallback_context.append(fallback_msg)

                # Complete job with fallback context
                combined_context = "\n\n".join(fallback_context)
                await self._complete_job(job_id, combined_context, [])
                return

            # Start processing each audio file and collect Azure Video IDs
            for i, audio_file in enumerate(audio_files[:3]):  # Limit to first 3 audio files
                try:
                    logger.info(f"Starting processing for audio {i+1}/{len(audio_files[:3])}: {audio_file['name']}")

                    # Start audio analysis in Azure Video Indexer (returns immediately with video_id)
                    analysis_result = await azure_video_indexer_service.transcribe_audio(
                        audio_file['url'],
                        audio_file['name'],
                        language="en-US",
                        wait_for_completion=False  # Async processing
                    )

                    if analysis_result.get("error"):
                        logger.error(f"Failed to start audio analysis for {audio_file['name']}: {analysis_result['error']}")
                        continue

                    video_id = analysis_result.get("video_id")
                    if video_id:
                        azure_video_ids.append(video_id)
                        logger.info(f"Audio analysis started for {audio_file['name']}, Azure Video ID: {video_id}")
                    else:
                        logger.error(f"No video ID returned for audio file {audio_file['name']}")

                except Exception as file_error:
                    logger.error(f"Error processing audio file {audio_file['name']}: {file_error}")
                    continue

            if not azure_video_ids:
                logger.error(f"No audio files were successfully submitted for processing in job {job_id}")
                await self._fail_job(job_id, "Failed to submit audio files for processing")
                return

            # Store Azure Video IDs in job record
            await self.db_client.AudioProcessingJob.update_one(
                {"jobId": job_id},
                {
                    "$set": {
                        "azureVideoIds": azure_video_ids,
                        "updatedAt": datetime.now(timezone.utc)
                    }
                }
            )

            # Job is now in processing state with Azure Video IDs stored
            # Start automatic background polling of Azure Video Indexer
            logger.info(f"Job {job_id} is now processing. Starting automatic Azure Video Indexer polling...")

            # Start background polling task
            await self._start_background_polling(job_id, azure_video_ids)
                
        except Exception as e:
            logger.error(f"Error in background audio processing for job {job_id}: {e}")
            await self._fail_job(job_id, f"Processing failed: {str(e)}")
        finally:
            # Clean up task reference
            if job_id in self._processing_tasks:
                del self._processing_tasks[job_id]

    async def _start_background_polling(self, job_id: str, azure_video_ids: list):
        """Start background polling of Azure Video Indexer status."""
        try:
            max_polls = 60  # Poll for up to 60 times (30 minutes with 30s intervals)
            poll_count = 0

            while poll_count < max_polls:
                await asyncio.sleep(30)  # Wait 30 seconds between polls
                poll_count += 1

                logger.info(f"Polling Azure Video Indexer status for job {job_id} (poll {poll_count}/{max_polls})")

                # Check if all videos are processed
                await self._check_and_update_azure_status(job_id, azure_video_ids)

                # Check if job is completed
                job = await self.db_client.AudioProcessingJob.find_one({"jobId": job_id})
                if job and job.get("status") in ["completed", "failed"]:
                    logger.info(f"Audio processing job {job_id} finished with status: {job.get('status')}")
                    break

            # If we've reached max polls and job is still processing, mark as timeout
            if poll_count >= max_polls:
                job = await self.db_client.AudioProcessingJob.find_one({"jobId": job_id})
                if job and job.get("status") == "processing":
                    logger.warning(f"Audio processing job {job_id} timed out after {max_polls} polls")
                    await self._fail_job(job_id, "Processing timeout - Azure Video Indexer took too long")

        except Exception as e:
            logger.error(f"Error in background polling for job {job_id}: {e}")
            await self._fail_job(job_id, f"Polling failed: {str(e)}")

    async def _complete_job(self, job_id: str, audio_context: str, azure_video_ids: list):
        """Mark job as completed with results."""
        try:
            await self.db_client.AudioProcessingJob.update_one(
                {"jobId": job_id},
                {
                    "$set": {
                        "status": "completed",
                        "audioContext": audio_context,
                        "azureVideoIds": azure_video_ids,
                        "completedAt": datetime.now(timezone.utc),
                        "updatedAt": datetime.now(timezone.utc)
                    }
                }
            )
            logger.info(f"Audio processing job {job_id} marked as completed")
        except Exception as e:
            logger.error(f"Failed to complete job {job_id}: {e}")

    async def _fail_job(self, job_id: str, error_message: str):
        """Mark job as failed with error message."""
        try:
            await self.db_client.AudioProcessingJob.update_one(
                {"jobId": job_id},
                {
                    "$set": {
                        "status": "failed",
                        "errorMessage": error_message,
                        "completedAt": datetime.now(timezone.utc),
                        "updatedAt": datetime.now(timezone.utc)
                    }
                }
            )
            logger.error(f"Audio processing job {job_id} marked as failed: {error_message}")
        except Exception as e:
            logger.error(f"Failed to mark job {job_id} as failed: {e}")

    async def complete_file_indexing(self, job_id: str, audio_analysis_results: List[str]):
        """Complete file indexing with audio analysis results (similar to video processing)."""
        try:
            # Get job details
            job = await self.db_client.AudioProcessingJob.find_one({"jobId": job_id})
            if not job:
                logger.error(f"Job {job_id} not found for file indexing")
                return

            workspace_slug = job.get("workspaceSlug")
            file_id = job.get("fileId")

            if not workspace_slug or not file_id:
                logger.info(f"No workspace or file ID for job {job_id}, skipping file indexing")
                return

            # Get workspace
            workspace = await self.db_client.Workspace.find_one({"slug": workspace_slug})
            if not workspace:
                logger.error(f"Workspace {workspace_slug} not found for job {job_id}")
                return

            # Create documents from audio analysis results
            from langchain.schema.document import Document
            documents = []

            for i, analysis_text in enumerate(audio_analysis_results):
                # Skip failed audio analysis results
                if not analysis_text.strip():
                    continue
                if "processing failed" in analysis_text.lower():
                    logger.warning(f"Skipping failed audio analysis result for job {job_id}, index {i}")
                    continue

                document = Document(
                    page_content=analysis_text.lower(),
                    metadata={
                        "source": f"audio_analysis_{job_id}_{i}",
                        "file_name": f"audio_analysis_{i}",
                        "file_type": "audio",
                        "transcription_service": "azure_video_indexer",
                        "workspace_id": str(workspace["_id"]),
                        "fileId": file_id,
                        "job_id": job_id
                    }
                )
                documents.append(document)

            if documents:
                # Index documents using RAG agent
                from src.agents.rag_agent import rag_agent
                await rag_agent.add_documents_to_vector_store(documents, str(workspace["_id"]))
                logger.info(f"Successfully indexed {len(documents)} audio analysis documents for job {job_id}")
            else:
                logger.warning(f"No valid audio analysis results to index for job {job_id}")

        except Exception as e:
            logger.error(f"Failed to complete file indexing for job {job_id}: {e}")

    async def _update_file_status_to_completed(self, job_id: str):
        """Update file status to COMPLETED."""
        try:
            job = await self.db_client.AudioProcessingJob.find_one({"jobId": job_id})
            if not job or not job.get("fileId"):
                return

            file_id = job["fileId"]
            await self.db_client.File.update_one(
                {"fileId": file_id},
                {"$set": {"status": "COMPLETED", "updatedAt": datetime.now(timezone.utc)}}
            )
            logger.info(f"Updated file {file_id} status to COMPLETED for job {job_id}")
        except Exception as e:
            logger.error(f"Failed to update file status for job {job_id}: {e}")

    async def _update_file_status_to_failed(self, job_id: str):
        """Update file status to FAILED."""
        try:
            job = await self.db_client.AudioProcessingJob.find_one({"jobId": job_id})
            if not job or not job.get("fileId"):
                return

            file_id = job["fileId"]
            await self.db_client.File.update_one(
                {"fileId": file_id},
                {"$set": {"status": "FAILED", "updatedAt": datetime.now(timezone.utc)}}
            )
            logger.info(f"Updated file {file_id} status to FAILED for job {job_id}")
        except Exception as e:
            logger.error(f"Failed to update file status for job {job_id}: {e}")


# Global instance
audio_processing_service = AudioProcessingService()
