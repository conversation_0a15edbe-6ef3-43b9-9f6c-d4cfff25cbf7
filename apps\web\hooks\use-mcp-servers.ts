import { useState, useEffect } from "react";
import { toast } from "react-hot-toast";
import {
  MCPServer,
  MCPServerCreate,
  MCPServerUpdate,
  MCPServerToolsResponse,
  MCPServerTestResponse,
  MCPServerTool,
} from "@/types/mcp-server";
import { getCookie } from "@/utils/cookies";

export function useMCPServers() {
  const [servers, setServers] = useState<MCPServer[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const tenantId = getCookie("currentOrganizationId") || "";

  const fetchServers = async (includePublic: boolean = true) => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(
        `/api/mcp-servers?include_public=${includePublic}&tenantId=${tenantId}`
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to fetch MCP servers");
      }

      const data = await response.json();
      setServers(data);
    } catch (err: any) {
      setError(err.message);
      toast.error(err.message);
    } finally {
      setLoading(false);
    }
  };

  const createServer = async (
    serverData: MCPServerCreate
  ): Promise<MCPServer | null> => {
    try {
      const response = await fetch("/api/mcp-servers", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(serverData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to create MCP server");
      }

      const newServer = await response.json();
      setServers((prev) => [newServer, ...prev]);
      toast.success("MCP server created successfully");
      return newServer;
    } catch (err: any) {
      toast.error(err.message);
      return null;
    }
  };

  const updateServer = async (
    serverId: string,
    serverData: MCPServerUpdate
  ): Promise<MCPServer | null> => {
    try {
      const response = await fetch(`/api/mcp-servers/${serverId}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(serverData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to update MCP server");
      }

      const updatedServer = await response.json();
      setServers((prev) =>
        prev.map((server) => (server.id === serverId ? updatedServer : server))
      );
      toast.success("MCP server updated successfully");
      return updatedServer;
    } catch (err: any) {
      toast.error(err.message);
      return null;
    }
  };

  const deleteServer = async (serverId: string): Promise<boolean> => {
    try {
      const response = await fetch(`/api/mcp-servers/${serverId}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to delete MCP server");
      }

      setServers((prev) => prev.filter((server) => server.id !== serverId));
      toast.success("MCP server deleted successfully");
      return true;
    } catch (err: any) {
      toast.error(err.message);
      return false;
    }
  };

  // Manual start/stop removed - servers now use on-demand management

  const toggleServerStatus = async (serverId: string): Promise<boolean> => {
    try {
      const response = await fetch(`/api/mcp-servers/${serverId}/toggle`, {
        method: "POST",
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(
          errorData.error || "Failed to toggle MCP server status"
        );
      }

      const updatedServer = await response.json();
      setServers((prev) =>
        prev.map((server) => (server.id === serverId ? updatedServer : server))
      );

      const statusText =
        updatedServer.status === "ACTIVE" ? "activated" : "deactivated";
      toast.success(`MCP server ${statusText} successfully`);
      return true;
    } catch (err: any) {
      toast.error(err.message);
      return false;
    }
  };

  const testServer = async (
    serverId: string
  ): Promise<MCPServerTestResponse | null> => {
    try {
      const response = await fetch(
        `/api/mcp-servers/${serverId}/test?tenantId=${tenantId}`,
        { method: "POST" }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to test MCP server");
      }

      const result: MCPServerTestResponse = await response.json();

      // Update server status in local state
      setServers((prev: any) =>
        prev.map((server) =>
          server.id === serverId
            ? { ...server, status: result.status, last_error: null }
            : server
        )
      );

      toast.success(result.message);
      return result;
    } catch (err: any) {
      toast.error(err.message);
      return null;
    }
  };

  const getServerTools = async (
    serverId: string
  ): Promise<MCPServerTool[] | null> => {
    try {
      const response = await fetch(
        `/api/mcp-servers/${serverId}/tools?tenantId=${tenantId}`
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to get MCP server tools");
      }

      const result: MCPServerTool[] = await response.json();
      return result;
    } catch (err: any) {
      toast.error(err.message);
      return null;
    }
  };

  const getServer = async (serverId: string): Promise<MCPServer | null> => {
    try {
      const response = await fetch(
        `/api/mcp-servers/${serverId}?tenantId=${tenantId}`
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to get MCP server");
      }

      const server: MCPServer = await response.json();
      return server;
    } catch (err: any) {
      toast.error(err.message);
      return null;
    }
  };

  useEffect(() => {
    fetchServers();
  }, []);

  return {
    servers,
    loading,
    error,
    fetchServers,
    createServer,
    updateServer,
    deleteServer,
    toggleServerStatus,
    // startServer and stopServer removed - using on-demand management
    testServer,
    getServerTools,
    getServer,
  };
}
