import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/next-auth/src/auth-options";
import { redirect } from "next/navigation";
import { getUsers, getWorkspace, getCustomRoles, getGroups } from "@/services";
import { cookies } from "next/headers";
import { UserWorkspaceManagement } from "@/components/wrapper-screens/settings/user-workspace-management";

export default async function UserWorkspaceManagementPage() {
  const session = await getServerSession(authOptions);

  if (!session) {
    redirect("/sign-in");
  }

  const tenantId = (await cookies().get("currentOrganizationId")?.value) ?? "";
  const userId = (await cookies().get("userId")?.value) || "";

  if (!tenantId || !userId) {
    redirect("/");
  }
  const [usersResponse, workspacesResponse, rolesResponse, groupsResponse] =
    await Promise.all([
      getUsers(userId, tenantId),
      getWorkspace(tenantId, userId),
      getCustomRoles(tenantId, userId),
      getGroups(userId, tenantId),
    ]);
  // Fetch initial data
  const users = usersResponse?.data || [];

  const workspaces = workspacesResponse?.workspaces || [];

  const roles = rolesResponse?.customRoles || [];

  const groups = groupsResponse?.data || [];

  return (
    <div className="p-2">
      <UserWorkspaceManagement
        initialUsers={users}
        initialWorkspaces={workspaces}
        initialRoles={roles}
        initialGroups={groups}
        tenantId={tenantId}
        userId={userId}
      />
    </div>
  );
}
