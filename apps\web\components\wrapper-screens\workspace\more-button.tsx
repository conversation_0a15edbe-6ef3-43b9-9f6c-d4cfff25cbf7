"use client";

import React from "react";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { MoreVertical, Pencil, Trash2 } from "lucide-react";
import { useLanguage } from "@/lib/language-context";
import { UpdateGuard, DeleteGuard } from "@/components/permission-guard";

interface MoreButtonProps {
  handleDelete: (id: string, name: string, type: string) => void;
  file: any;
  setIsCreateFolderOpen: (value: string | null) => void;
  type: "folder" | "file";
  setUpdateData: (data: any) => void;
  setNewFolderName: (name: string) => void;
  isSyncedWithCloud?: boolean;
  permission: any;
}

export const MoreButton = ({
  handleDelete,
  file,
  setIsCreateFolderOpen,
  type,
  setUpdateData,
  setNewFolderName,
  isSyncedWithCloud = false,
  permission,
}: MoreButtonProps) => {
  const { t } = useLanguage();

  const resource = type.toUpperCase() as "FOLDER" | "FILE";

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="icon"
          className="h-8 w-8 opacity-0 group-hover:opacity-100 transition-opacity"
        >
          <MoreVertical className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuLabel>{t("workspace.actions")}</DropdownMenuLabel>
        <DropdownMenuSeparator />

        <UpdateGuard
          resource={resource}
          hasPermission={permission?.file?.update}
        >
          <DropdownMenuItem
            disabled={isSyncedWithCloud}
            onClick={() => {
              // Use setTimeout to ensure the dropdown closes first
              setTimeout(() => {
                setIsCreateFolderOpen("rename");
                setNewFolderName(file.name);
                setUpdateData({ ...file, type: type });
              }, 100);
            }}
          >
            <Pencil className="h-4 w-4 mr-2" />
            {type === "folder"
              ? t("workspace.renameFolder")
              : t("workspace.renameFile")}
          </DropdownMenuItem>
        </UpdateGuard>

        <DeleteGuard
          resource={resource}
          hasPermission={permission?.file?.delete}
        >
          <DropdownMenuItem
            disabled={isSyncedWithCloud}
            onClick={() => handleDelete(file.id, file.name, type)}
            className="text-destructive focus:text-destructive"
          >
            <Trash2 className="h-4 w-4 mr-2" />
            {type === "folder"
              ? t("workspace.deleteFolder")
              : t("workspace.deleteFile")}
          </DropdownMenuItem>
        </DeleteGuard>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default MoreButton;
