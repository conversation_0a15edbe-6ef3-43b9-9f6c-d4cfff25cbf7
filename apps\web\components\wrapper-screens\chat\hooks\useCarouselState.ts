import { useState, useRef, useEffect, useMemo, useCallback } from 'react';
import { Message, DisplayIndices, RegeneratedMessageCounts } from '../types';

export const useCarouselState = (processedMessages: Message[]) => {
  // Local state to track displayed message indices (messageId -> displayIndex)
  const [displayIndices, setDisplayIndices] = useState<DisplayIndices>({});

  // Create a stable reference for tracking regenerated and edited messages
  const regeneratedMessageCounts = useMemo(() => {
    const counts: RegeneratedMessageCounts = {};
    processedMessages.forEach((message) => {
      if (message.id && message.regeneratedMessages) {
        counts[message.id] = message.regeneratedMessages.length;
      }
    });
    return counts;
  }, [processedMessages]);

  const editedMessageCounts = useMemo(() => {
    const counts: RegeneratedMessageCounts = {};
    processedMessages.forEach((message) => {
      if (message.id && message.editedMessages) {
        counts[message.id] = message.editedMessages.length;
      }
    });
    return counts;
  }, [processedMessages]);

  // Use a ref to track the current display indices to prevent flickering during renders
  const displayIndicesRef = useRef<DisplayIndices>({});

  // Track which messages have manually set display indices
  const manuallySetIndicesRef = useRef<Set<string>>(new Set());

  // Update the display indices ref whenever the state changes
  useEffect(() => {
    displayIndicesRef.current = displayIndices;
  }, [displayIndices]);

  // Function to update both display index and manually set indices in a single batch
  const updateDisplayIndex = useCallback(
    (messageId: string, newIndex: number, isManuallySet: boolean) => {
      // Update the refs immediately to prevent flickering
      displayIndicesRef.current = {
        ...displayIndicesRef.current,
        [messageId]: newIndex,
      };

      if (isManuallySet) {
        const newSet = new Set(manuallySetIndicesRef.current);
        newSet.add(messageId);
        manuallySetIndicesRef.current = newSet;
      } else {
        const newSet = new Set(manuallySetIndicesRef.current);
        newSet.delete(messageId);
        manuallySetIndicesRef.current = newSet;
      }

      // Then update the state (this will be batched by React)
      setDisplayIndices((prev) => ({
        ...prev,
        [messageId]: newIndex,
      }));
    },
    []
  );

  // Reset manually set indices when component unmounts or when messages change significantly
  useEffect(() => {
    // Reset manually set indices when messages change significantly (e.g., new chat loaded)
    manuallySetIndicesRef.current = new Set();

    // Return cleanup function to reset when component unmounts
    return () => {
      manuallySetIndicesRef.current = new Set();
    };
  }, [processedMessages.length]);

  // Update display indices when regenerated or edited message counts change
  useEffect(() => {
    // For each message with regenerated or edited versions, set the display index to show the most recent one
    let hasChanges = false;
    const newDisplayIndices: DisplayIndices = {
      ...displayIndicesRef.current,
    };

    // Check each message with regenerated versions
    Object.entries(regeneratedMessageCounts).forEach(([messageId, count]) => {
      if (count > 0) {
        // Only update indices for messages that haven't been manually set by navigation
        if (!manuallySetIndicesRef.current.has(messageId)) {
          const editedCount = editedMessageCounts[messageId] || 0;
          const totalVersions = 1 + editedCount + count; // 1 for original + edited + regenerated
          const targetIndex = totalVersions - 1; // Show the most recent version

          // If this message doesn't have a display index yet, set it to show the most recent version
          if (!newDisplayIndices[messageId]) {
            newDisplayIndices[messageId] = targetIndex;
            hasChanges = true;
          }
          // If the number of versions has increased, update to show the newest one
          else if (newDisplayIndices[messageId] < targetIndex) {
            newDisplayIndices[messageId] = targetIndex;
            hasChanges = true;
          }
        }
      }
    });

    // Check each message with edited versions (that don't have regenerated versions)
    Object.entries(editedMessageCounts).forEach(([messageId, count]) => {
      if (count > 0 && !regeneratedMessageCounts[messageId]) {
        // Only update indices for messages that haven't been manually set by navigation
        if (!manuallySetIndicesRef.current.has(messageId)) {
          // If this message doesn't have a display index yet, set it to show the most recent edited version
          if (!newDisplayIndices[messageId]) {
            // Show the most recent edited message
            newDisplayIndices[messageId] = count;
            hasChanges = true;
          }
          // If the number of edited messages has increased, update to show the newest one
          else if (newDisplayIndices[messageId] < count) {
            newDisplayIndices[messageId] = count;
            hasChanges = true;
          }
        }
      }
    });

    // Only update state if there are actual changes to avoid infinite loops
    if (hasChanges) {
      // Update the refs first
      displayIndicesRef.current = newDisplayIndices;

      // Then update the state
      setDisplayIndices(newDisplayIndices);
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [regeneratedMessageCounts, editedMessageCounts]);

  // Function to check if a message index was manually set
  const isManuallySetIndex = useCallback(
    (messageId: string) => {
      return manuallySetIndicesRef.current.has(messageId);
    },
    []
  );

  return {
    displayIndices,
    displayIndicesRef,
    updateDisplayIndex,
    isManuallySetIndex,
  };
};
