import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/next-auth";
import db from "@/lib/shared-db";
import crypto from "crypto";

// GET /api/companies/invite/[slug] - Get company info for invite page
export async function GET(
  _request: Request,
  { params }: { params: { slug: string } }
) {
  try {
    const session: any = await getServerSession(authOptions);
    const { slug } = params;

    // Find company by slug (case-insensitive)
    const company = await db.company.findFirst({
      where: {
        slug: {
          equals: slug,
          mode: "insensitive",
        },
        status: "active", // Only show active companies
      },
      include: {
        members: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
          orderBy: {
            createdAt: "asc",
          },
        },
        tenant: {
          select: {
            id: true,
            name: true,
            themeConfiguration: {
              select: {
                id: true,
                tenantId: true,
                brandName: true,
                logoUrl: true,
                faviconUrl: true,
                fullAppLogoUrl: true,
                primaryColor: true,
                secondaryColor: true,
                accentColor: true,
                navigationBackgroundColor: true,
                contentBackgroundColor: true,
                foregroundColor: true,
                themePreset: true,
                isActive: true,
                version: true,
                updatedAt: true,
              },
            },
          },
        },
      },
    });

    if (!company) {
      return NextResponse.json(
        { error: "Company not found or invitation is no longer valid" },
        { status: 404 }
      );
    }

    // Check if user is already a member (if authenticated)
    let alreadyMember = false;
    if (session?.user?.email) {
      const emailHash = crypto
        .createHash("sha256")
        .update(session.user.email)
        .digest("hex");

      const user = await db.user.findUnique({
        where: { emailHash },
      });

      if (user) {
        const existingMembership = await db.companyMember.findFirst({
          where: {
            companyId: company.id,
            userId: user.id,
          },
        });
        alreadyMember = !!existingMembership;
      }
    }

    // Format company data
    const formattedCompany = {
      id: company.id,
      name: company.name,
      slug: company.slug,
      description: company.description || "",
      maxSignups: company.maxSignups,
      currentSignups: company.members.length,
      status: company.status,
      createdAt: company.createdAt.toISOString(),
      tenant: {
        id: company.tenant.id,
        name: company.tenant.name,
        themeConfiguration: company.tenant.themeConfiguration,
      },
    };

    // Format members data (only show basic info for privacy)
    const formattedMembers = company.members
      .filter((m: any) => m.status === "ACTIVE") // Only show active members
      .map((member: any) => ({
        id: member.id,
        name: member.user.name,
        role: member.role.toLowerCase(),
        status: member.status.toLowerCase(),
      }));

    return NextResponse.json({
      company: formattedCompany,
      members: formattedMembers,
      alreadyMember,
    });
  } catch (error) {
    console.error("Error fetching company invite:", error);
    return NextResponse.json(
      { error: "Failed to fetch company invitation" },
      { status: 500 }
    );
  }
}
