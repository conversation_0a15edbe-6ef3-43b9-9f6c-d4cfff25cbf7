"use client";

import React, { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

import {
  Users,
  UserPlus,
  MoreHorizontal,
  Edit,
  Trash2,
  Crown,
  Eye,
  Check,
  ChevronsUpDown,
} from "lucide-react";
import { useLanguage } from "@/lib/language-context";
import { toast } from "react-hot-toast";

interface PageMember {
  id: string;
  role: "MEMBER" | "EDITOR" | "ADMIN";
  source: "MANUAL" | "SHAREPOINT" | "WORKSPACE";
  user: {
    id: string;
    name: string;
    email: string;
  };
  sharePointPermissionLevel?: string;
  createdAt: string;
}

interface PageMembersManagerProps {
  pageId: string;
  tenantId: string;
  currentUserRole?: "MEMBER" | "EDITOR" | "ADMIN";
  onMembershipChange?: () => void;
}

const roleIcons = {
  MEMBER: Eye,
  EDITOR: Edit,
  ADMIN: Crown,
};

const roleColors = {
  MEMBER: "bg-blue-100 text-blue-800",
  EDITOR: "bg-green-100 text-green-800",
  ADMIN: "bg-purple-100 text-purple-800",
};

const sourceColors = {
  MANUAL: "bg-gray-100 text-gray-800",
  SHAREPOINT: "bg-orange-100 text-orange-800",
  WORKSPACE: "bg-indigo-100 text-indigo-800",
};

export function PageMembersManager({
  pageId,
  tenantId,
  currentUserRole,
  onMembershipChange,
}: PageMembersManagerProps) {
  const { t } = useLanguage();
  const [members, setMembers] = useState<PageMember[]>([]);
  const [loading, setLoading] = useState(true);
  const [showAddDialog, setShowAddDialog] = useState(false);

  const [selectedUser, setSelectedUser] = useState<{
    id: string;
    name: string;
    email: string;
  } | null>(null);
  const [userSearchOpen, setUserSearchOpen] = useState(false);
  const [userSearchQuery, setUserSearchQuery] = useState("");
  const [availableUsers, setAvailableUsers] = useState<
    { id: string; name: string; email: string }[]
  >([]);
  const [newMemberRole, setNewMemberRole] = useState<
    "MEMBER" | "EDITOR" | "ADMIN"
  >("MEMBER");
  const [actionLoading, setActionLoading] = useState<string | null>(null);

  const canManageMembers = currentUserRole === "ADMIN";

  // Helper function to get user initials
  const getInitials = (name: string) => {
    if (!name) return "?";
    return name
      .split(" ")
      .map((part) => part[0])
      .join("")
      .toUpperCase()
      .substring(0, 2);
  };

  useEffect(() => {
    loadMembers();
  }, [pageId]);

  // Search for users when dialog opens
  useEffect(() => {
    if (showAddDialog) {
      searchUsers("");
    }
  }, [showAddDialog]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        userSearchOpen &&
        !(event.target as Element).closest(".user-search-dropdown")
      ) {
        setUserSearchOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [userSearchOpen]);

  const searchUsers = async (query: string) => {
    try {
      const response = await fetch(
        `/api/users/search?q=${encodeURIComponent(query)}&limit=10`
      );
      if (response.ok) {
        const data = await response.json();
        // Filter out users who are already members
        const memberUserIds = members.map((m) => m.user.id);
        const filteredUsers = data.users.filter(
          (user: any) => !memberUserIds.includes(user.id)
        );
        setAvailableUsers(filteredUsers);
      }
    } catch (error) {
      console.error("Error searching users:", error);
    }
  };

  const loadMembers = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/pages/${pageId}/members`);
      if (response.ok) {
        const data = await response.json();
        setMembers(data.members || []);
      } else {
        toast.error("Failed to load page members");
      }
    } catch (error) {
      console.error("Error loading members:", error);
      toast.error("Failed to load page members");
    } finally {
      setLoading(false);
    }
  };

  const addMember = async () => {
    if (!selectedUser) {
      toast.error("Please select a user");
      return;
    }

    try {
      setActionLoading("add");
      const response = await fetch(`/api/pages/${pageId}/members`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          targetUserId: selectedUser.email, // API expects email to find user
          role: newMemberRole,
          tenantId,
        }),
      });

      if (response.ok) {
        toast.success("Member added successfully");
        setShowAddDialog(false);
        setSelectedUser(null);
        setNewMemberRole("MEMBER");
        setUserSearchQuery("");
        loadMembers();
        onMembershipChange?.();
      } else {
        const error = await response.json();
        toast.error(error.error || "Failed to add member");
      }
    } catch (error) {
      console.error("Error adding member:", error);
      toast.error("Failed to add member");
    } finally {
      setActionLoading(null);
    }
  };

  const updateMemberRole = async (
    userId: string,
    newRole: "MEMBER" | "EDITOR" | "ADMIN"
  ) => {
    try {
      setActionLoading(`role-${userId}`);
      const response = await fetch(`/api/pages/${pageId}/members`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          targetUserId: userId,
          role: newRole,
        }),
      });

      if (response.ok) {
        toast.success("Member role updated successfully");
        loadMembers();
        onMembershipChange?.();
      } else {
        const error = await response.json();
        toast.error(error.error || "Failed to update member role");
      }
    } catch (error) {
      console.error("Error updating member role:", error);
      toast.error("Failed to update member role");
    } finally {
      setActionLoading(null);
    }
  };

  const removeMember = async (userId: string) => {
    try {
      setActionLoading(`remove-${userId}`);
      const response = await fetch(
        `/api/pages/${pageId}/members?userId=${userId}`,
        {
          method: "DELETE",
        }
      );

      if (response.ok) {
        toast.success("Member removed successfully");
        loadMembers();
        onMembershipChange?.();
      } else {
        const error = await response.json();
        toast.error(error.error || "Failed to remove member");
      }
    } catch (error) {
      console.error("Error removing member:", error);
      toast.error("Failed to remove member");
    } finally {
      setActionLoading(null);
    }
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            {t("page.members") || "Page Members"}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-4">
            {t("common.loading") || "Loading..."}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            {t("page.members") || "Page Members"} ({members.length})
          </CardTitle>
          {canManageMembers && (
            <Dialog open={showAddDialog} onOpenChange={setShowAddDialog}>
              <DialogTrigger asChild>
                <Button size="sm">
                  <UserPlus className="h-4 w-4 mr-2" />
                  {t("page.addMember") || "Add Member"}
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>
                    {t("page.addMember") || "Add Page Member"}
                  </DialogTitle>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="user">
                      {t("common.user") || "Select User"}
                    </Label>
                    <div className="relative user-search-dropdown">
                      <Button
                        variant="outline"
                        onClick={() => setUserSearchOpen(!userSearchOpen)}
                        className="w-full justify-between"
                      >
                        {selectedUser ? (
                          <div className="flex items-center gap-2 overflow-hidden">
                            <Avatar className="h-6 w-6 flex-shrink-0">
                              <AvatarFallback className="text-xs">
                                {getInitials(selectedUser.name)}
                              </AvatarFallback>
                            </Avatar>
                            <div className="flex flex-col items-start min-w-0">
                              <span className="font-medium truncate">
                                {selectedUser.name}
                              </span>
                              <span className="text-xs text-muted-foreground truncate">
                                {selectedUser.email}
                              </span>
                            </div>
                          </div>
                        ) : (
                          "Select user..."
                        )}
                        <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                      </Button>

                      {userSearchOpen && (
                        <div className="absolute z-50 w-full mt-1 bg-white border border-gray-200 rounded-md shadow-lg max-h-60 overflow-auto">
                          <div className="p-2 border-b">
                            <input
                              type="text"
                              placeholder="Search users..."
                              value={userSearchQuery}
                              onChange={(e) => {
                                setUserSearchQuery(e.target.value);
                                searchUsers(e.target.value);
                              }}
                              className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                              autoFocus
                            />
                          </div>
                          <div className="py-1">
                            {availableUsers.length === 0 ? (
                              <div className="px-3 py-2 text-sm text-gray-500">
                                No users found.
                              </div>
                            ) : (
                              availableUsers.map((user) => (
                                <button
                                  key={user.id}
                                  onClick={() => {
                                    setSelectedUser(user);
                                    setUserSearchOpen(false);
                                  }}
                                  className="w-full flex items-center gap-2 px-3 py-2 text-left hover:bg-gray-100 focus:bg-gray-100 focus:outline-none"
                                >
                                  <Avatar className="h-8 w-8 flex-shrink-0">
                                    <AvatarFallback className="text-xs">
                                      {getInitials(user.name)}
                                    </AvatarFallback>
                                  </Avatar>
                                  <div className="flex flex-col flex-1 min-w-0">
                                    <span className="font-medium truncate text-sm">
                                      {user.name}
                                    </span>
                                    <span className="text-xs text-gray-500 truncate">
                                      {user.email}
                                    </span>
                                  </div>
                                  {selectedUser?.id === user.id && (
                                    <Check className="h-4 w-4 text-blue-600 flex-shrink-0" />
                                  )}
                                </button>
                              ))
                            )}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="role">{t("common.role") || "Role"}</Label>
                    <Select
                      value={newMemberRole}
                      onValueChange={(value) =>
                        setNewMemberRole(value as "MEMBER" | "EDITOR" | "ADMIN")
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="MEMBER">
                          {t("role.member") || "Member"} -{" "}
                          {t("role.memberDesc") || "View only"}
                        </SelectItem>
                        <SelectItem value="EDITOR">
                          {t("role.editor") || "Editor"} -{" "}
                          {t("role.editorDesc") || "View and edit"}
                        </SelectItem>
                        <SelectItem value="ADMIN">
                          {t("role.admin") || "Admin"} -{" "}
                          {t("role.adminDesc") || "Full access"}
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="flex justify-end gap-2">
                    <Button
                      variant="outline"
                      onClick={() => setShowAddDialog(false)}
                    >
                      {t("common.cancel") || "Cancel"}
                    </Button>
                    <Button
                      onClick={addMember}
                      disabled={actionLoading === "add"}
                    >
                      {actionLoading === "add"
                        ? t("common.adding") || "Adding..."
                        : t("common.add") || "Add"}
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          )}
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {members.map((member) => {
            const RoleIcon = roleIcons[member.role];
            return (
              <div
                key={member.id}
                className="flex items-center justify-between p-3 border rounded-lg"
              >
                <div className="flex items-center gap-3">
                  <Avatar>
                    <AvatarFallback>
                      {getInitials(member.user.name)}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <div className="font-medium">{member.user.name}</div>
                    <div className="text-sm text-muted-foreground">
                      {member.user.email}
                    </div>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Badge className={roleColors[member.role]}>
                    <RoleIcon className="h-3 w-3 mr-1" />
                    {member.role}
                  </Badge>
                  <Badge
                    variant="outline"
                    className={sourceColors[member.source]}
                  >
                    {member.source}
                  </Badge>
                  {canManageMembers && member.source === "MANUAL" && (
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem
                          onClick={() =>
                            updateMemberRole(member.user.id, "MEMBER")
                          }
                          disabled={member.role === "MEMBER"}
                        >
                          <Eye className="h-4 w-4 mr-2" />
                          Make Member
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() =>
                            updateMemberRole(member.user.id, "EDITOR")
                          }
                          disabled={member.role === "EDITOR"}
                        >
                          <Edit className="h-4 w-4 mr-2" />
                          Make Editor
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() =>
                            updateMemberRole(member.user.id, "ADMIN")
                          }
                          disabled={member.role === "ADMIN"}
                        >
                          <Crown className="h-4 w-4 mr-2" />
                          Make Admin
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => removeMember(member.user.id)}
                          className="text-red-600"
                        >
                          <Trash2 className="h-4 w-4 mr-2" />
                          Remove
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  )}
                </div>
              </div>
            );
          })}
          {members.length === 0 && (
            <div className="text-center py-8 text-muted-foreground">
              <Users className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>{t("page.noMembers") || "No page members yet"}</p>
              {canManageMembers && (
                <p className="text-sm mt-2">
                  {t("page.addFirstMember") ||
                    "Add the first member to get started"}
                </p>
              )}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
