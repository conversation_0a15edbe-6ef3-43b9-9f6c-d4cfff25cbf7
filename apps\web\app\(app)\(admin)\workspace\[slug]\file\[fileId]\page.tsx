import FilePage from "@/components/wrapper-screens/workspace/file-wrapper";
import { getFile, getVectorStoreUsageSummary } from "@/services";
import { cookies } from "next/headers";
import { redirect } from "next/navigation";
import { hasPermission } from "@/lib/permissions";

export default async function Page({ params }) {
  const { fileId, slug } = params;

  const tenantId = cookies().get("currentOrganizationId")?.value;
  const userId = cookies().get("userId")?.value;

  if (!tenantId || !userId) {
    return redirect("/sign-in");
  }

  try {
    const [fileData, usageSummary] = await Promise.all([
      getFile(tenantId, userId, fileId, slug),
      getVectorStoreUsageSummary(tenantId, userId),
    ]);

    // Handle case where file data is not found
    if (!fileData || !fileData.file) {
      return redirect(`/my-hub`);
    }

    return (
      <FilePage
        workspaceSlug={slug}
        tenantId={tenantId}
        fileId={fileId}
        permission={fileData?.permission}
        file={fileData.file}
        usageSummary={usageSummary}
      />
    );
  } catch (error) {
    console.error("Error loading folder:", error);
    return redirect(`/workspace/${slug}`);
  }
}
