import ProjectCard from "@/components/cards/home/<USER>";
import { getWorkspace } from "@/services";
import { cookies } from "next/headers";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import { getTranslations } from "@/lib/server-i18n";
import CreateWorkspaceButton from "@/components/workspace/create-workspace-button";

export default async function Page() {
  const tenantId = cookies().get("currentOrganizationId")?.value;
  const userId = cookies().get("userId")?.value;
  const [workspace] = await Promise.all([getWorkspace(tenantId, userId)]);
  const { t } = await getTranslations();

  return (
    <div className="h-[calc(100vh-64px)] px-6 py-8">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-bold tracking-tight">
          {t("sidebar.myHub")}
        </h1>
        <CreateWorkspaceButton permission={workspace?.permission} />
      </div>

      {(workspace?.workspaces ?? []).length === 0 ? (
        <div className="flex flex-col items-center justify-center py-20">
          <div className="rounded-full bg-primary/10 p-6 mb-6">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="h-12 w-12 text-primary"
            >
              <path d="M3 3h18v18H3z" />
              <path d="M12 8v8" />
              <path d="M8 12h8" />
            </svg>
          </div>
          <h2 className="text-2xl font-semibold mb-2">
            {t("workspace.noWorkspacesYet")}
          </h2>
          <p className="text-muted-foreground mb-6 text-center max-w-md">
            {t("workspace.createFirstWorkspace")}
          </p>
          <CreateWorkspaceButton permission={workspace?.permission} size="lg" />
        </div>
      ) : (
        <div className="flex flex-wrap  gap-4">
          {(workspace?.workspaces ?? []).map((project) => (
            <ProjectCard
              workspace={project}
              key={project?.id}
              initials={project?.initials}
              title={project?.name}
              description={project?.description}
            />
          ))}
        </div>
      )}
    </div>
  );
}
