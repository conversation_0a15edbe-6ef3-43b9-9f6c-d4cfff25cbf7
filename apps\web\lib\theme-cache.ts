"use client";

import { ThemeConfig } from "@/types/theme-config";

interface CachedThemeData {
  config: ThemeConfig;
  timestamp: number;
  version: number;
}

class ThemeCache {
  private cache = new Map<string, CachedThemeData>();
  private readonly CACHE_DURATION = 5 * 60 * 1000; // 5 minutes
  private readonly STORAGE_KEY = "theme-cache";

  constructor() {
    // Load cache from localStorage on initialization
    this.loadFromStorage();
  }

  private loadFromStorage() {
    if (typeof window === "undefined") return;
    
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      if (stored) {
        const data = JSON.parse(stored);
        // Convert plain object back to Map
        this.cache = new Map(Object.entries(data));
      }
    } catch (error) {
      console.warn("Failed to load theme cache from storage:", error);
    }
  }

  private saveToStorage() {
    if (typeof window === "undefined") return;
    
    try {
      // Convert Map to plain object for storage
      const data = Object.fromEntries(this.cache);
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(data));
    } catch (error) {
      console.warn("Failed to save theme cache to storage:", error);
    }
  }

  set(tenantId: string, config: ThemeConfig): void {
    const cachedData: CachedThemeData = {
      config,
      timestamp: Date.now(),
      version: config.version || 1,
    };
    
    this.cache.set(tenantId, cachedData);
    this.saveToStorage();
    
    console.log(`Theme cache: Stored config for tenant ${tenantId}, version ${cachedData.version}`);
  }

  get(tenantId: string): ThemeConfig | null {
    const cached = this.cache.get(tenantId);
    
    if (!cached) {
      console.log(`Theme cache: No cached config for tenant ${tenantId}`);
      return null;
    }

    const now = Date.now();
    const isExpired = now - cached.timestamp > this.CACHE_DURATION;
    
    if (isExpired) {
      console.log(`Theme cache: Cached config expired for tenant ${tenantId}`);
      this.cache.delete(tenantId);
      this.saveToStorage();
      return null;
    }

    console.log(`Theme cache: Retrieved cached config for tenant ${tenantId}, version ${cached.version}`);
    return cached.config;
  }

  isStale(tenantId: string, serverVersion: number): boolean {
    const cached = this.cache.get(tenantId);
    
    if (!cached) {
      return true; // No cache means it's stale
    }

    const versionMismatch = cached.version < serverVersion;
    const isExpired = Date.now() - cached.timestamp > this.CACHE_DURATION;
    
    if (versionMismatch || isExpired) {
      console.log(`Theme cache: Config is stale for tenant ${tenantId}. Cached version: ${cached.version}, Server version: ${serverVersion}, Expired: ${isExpired}`);
      return true;
    }

    return false;
  }

  invalidate(tenantId: string): void {
    this.cache.delete(tenantId);
    this.saveToStorage();
    console.log(`Theme cache: Invalidated cache for tenant ${tenantId}`);
  }

  clear(): void {
    this.cache.clear();
    this.saveToStorage();
    console.log("Theme cache: Cleared all cached configs");
  }

  // Get cache statistics for debugging
  getStats(): { size: number; entries: Array<{ tenantId: string; version: number; age: number }> } {
    const now = Date.now();
    const entries = Array.from(this.cache.entries()).map(([tenantId, data]) => ({
      tenantId,
      version: data.version,
      age: Math.round((now - data.timestamp) / 1000), // age in seconds
    }));

    return {
      size: this.cache.size,
      entries,
    };
  }
}

// Create a singleton instance
export const themeCache = new ThemeCache();

// Helper function to get theme config with caching
export async function getCachedThemeConfig(tenantId: string): Promise<ThemeConfig | null> {
  if (!tenantId) return null;

  // Check cache first
  const cached = themeCache.get(tenantId);
  if (cached) {
    return cached;
  }

  // If not in cache, fetch from API
  try {
    const { getThemeConfig } = await import("@/services/theme-config");
    const config = await getThemeConfig(tenantId);
    
    if (config) {
      // Store in cache
      themeCache.set(tenantId, config);
    }
    
    return config;
  } catch (error) {
    console.error("Failed to fetch theme config:", error);
    return null;
  }
}

// Helper function to update cache when theme changes
export function updateThemeCache(tenantId: string, config: ThemeConfig): void {
  themeCache.set(tenantId, config);
}

// Helper function to invalidate cache
export function invalidateThemeCache(tenantId: string): void {
  themeCache.invalidate(tenantId);
}
