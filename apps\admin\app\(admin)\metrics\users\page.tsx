import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/next-auth";
import { redirect } from "next/navigation";
import db from "@/lib/shared-db";
import { addDays } from "date-fns";
import { UserMetricsClient } from "@/components/metrics/user-metrics-client";

// Types for our data structures
interface UserMetrics {
  activeUserCount: number;
  totalUsers: number;
  activeUserPercentage: number;
  newUsers: number;
  userGrowthRate: number;
}

interface ActiveUsersOverTime {
  month?: string;
  date?: string;
  activeUsers: number;
}

interface RoleDistribution {
  name: string;
  value: number;
}

interface FeatureUsage {
  name: string;
  value: number;
}

interface UserMetricsData {
  userMetrics: UserMetrics;
  activeUsersOverTime: ActiveUsersOverTime[];
  roleDistribution: RoleDistribution[];
  featureUsage: FeatureUsage[];
  period: string;
}

// Helper function to convert date range to period parameter
function dateRangeToPeriod(from: Date | null, to: Date | null): string {
  if (!from || !to) {
    return "lifetime";
  }
  const diffTime = Math.abs(to.getTime() - from.getTime());
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  if (diffDays <= 7) return "7days";
  if (diffDays <= 30) return "30days";
  if (diffDays <= 90) return "90days";
  if (diffDays <= 180) return "6months";
  if (diffDays <= 365) return "12months";
  return "12months";
}

// Server-side data fetching functions
async function fetchUserMetrics(fromDate: Date | null, toDate: Date | null, period: string): Promise<UserMetrics> {
  // Count unique users who have made API requests in the period
  const activeUsers = await db.aPIRequest.groupBy({
    by: ['userId'],
    where: fromDate && toDate ? {
      timestamp: {
        gte: fromDate,
        lte: toDate
      }
    } : {},
    _count: {
      userId: true
    }
  });

  // Get total users
  const totalUsers = await db.user.count();

  // Calculate active user percentage
  const activeUserCount = activeUsers.length;
  const activeUserPercentage = totalUsers > 0
    ? Math.round((activeUserCount / totalUsers) * 100 * 100) / 100
    : 0;

  const whereClause = !(fromDate && toDate )
    ? {}
    : {
      createdAt: {
        gte: fromDate,
        lte: toDate
      }
    }

  // Calculate new users in the period
  const newUsers = await db.user.count({
    where: whereClause
  });

  // Calculate user growth rate (compare with previous period)
  const periodDuration = fromDate && toDate  ? toDate.getTime() - fromDate.getTime() : null;
  let previousPeriodStart;
  if(periodDuration){
    previousPeriodStart = fromDate && toDate  ? new Date(fromDate.getTime() - periodDuration) : null;
  }
  const previousPeriodEnd = fromDate;

  const previousPeriodNewUsers = await db.user.count({
    where: fromDate && toDate  ? {
      createdAt: {
        gte: previousPeriodStart,
        lte: previousPeriodEnd
      }
    } : {},
  });

  const userGrowthRate = previousPeriodNewUsers > 0
    ? Math.round(((newUsers - previousPeriodNewUsers) / previousPeriodNewUsers) * 100 * 100) / 100
    : newUsers > 0 ? 100 : 0;

  return {
    activeUserCount,
    totalUsers,
    activeUserPercentage,
    newUsers,
    userGrowthRate,
  };
}

async function fetchActiveUsersOverTime(fromDate: Date | null, toDate: Date | null, period: string): Promise<ActiveUsersOverTime[]> {
  const isLifetime = !fromDate || !toDate;
  // Get all API requests in the period
  const apiRequests = await db.aPIRequest.findMany({
    where: isLifetime
      ? {}
      : {
        timestamp: {
          gte: fromDate!,
          lte: toDate!,
        },
      },
    select: {
      userId: true,
      timestamp: true,
    },
    orderBy: {
      timestamp: "asc",
    },
  });

  let unit: "days" | "months" = "months";
  // Determine if we should group by days or months
  if (!isLifetime) {
    const diffTime = Math.abs(toDate!.getTime() - fromDate!.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    unit = diffDays <= 30 ? "days" : "months";
  }

  // Group by month or day and count unique users
  const groupedData: Record<string, Set<string>> = {};
  const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

  apiRequests.forEach(request => {
    const date = new Date(request.timestamp);

    if (unit === 'days') {
      // Format as YYYY-MM-DD
      const day = date.toISOString().split('T')[0];
      if (!groupedData[day]) {
        groupedData[day] = new Set();
      }
      groupedData[day].add(request.userId);
    } else {
      // Format as Month YYYY (e.g., "Jan 2023")
      const monthYear = `${monthNames[date.getMonth()]} ${date.getFullYear()}`;
      if (!groupedData[monthYear]) {
        groupedData[monthYear] = new Set();
      }
      groupedData[monthYear].add(request.userId);
    }
  });

  // Convert to array format for charts
  let timeSeriesData: ActiveUsersOverTime[] = [];

  if (unit === 'days' && fromDate && toDate) {
    // For days, ensure all days in the range are represented
    let currentDate = new Date(fromDate);

    while (currentDate <= toDate) {
      const day = currentDate.toISOString().split('T')[0];
      timeSeriesData.push({
        date: day,
        activeUsers: groupedData[day] ? groupedData[day].size : 0
      });

      currentDate.setDate(currentDate.getDate() + 1);
    }
  } else {
    // For months, convert the grouped data to array
    for (const [monthYear, userSet] of Object.entries(groupedData)) {
      timeSeriesData.push({
        month: monthYear,
        activeUsers: userSet.size
      });
    }

    // Sort by date
    timeSeriesData.sort((a, b) => {
      const [aMonth, aYear] = a.month!.split(' ');
      const [bMonth, bYear] = b.month!.split(' ');

      if (aYear !== bYear) {
        return parseInt(aYear) - parseInt(bYear);
      }

      return monthNames.indexOf(aMonth) - monthNames.indexOf(bMonth);
    });
  }

  return timeSeriesData;
}

async function fetchUserRoleDistribution(_fromDate: Date | null, _toDate: Date | null): Promise<RoleDistribution[]> {
  const isLifetime = !_fromDate || !_toDate;

  const roleDistribution = await db.membership.groupBy({
    by: ['role'],
    where: isLifetime
      ? {}
      : {
        createdAt: {
          gte: _fromDate!,
          lte: _toDate!
        }
      },
    _count: {
      _all: true
    }
  });

  return roleDistribution.map(item => ({
    name: item.role,
    value: item._count._all,
  }));
}


async function fetchFeatureUsage(fromDate: Date | null, toDate: Date | null): Promise<FeatureUsage[]> {
  const isLifetime = !fromDate || !toDate;

  const apiUsage = await db.aPIRequest.groupBy({
    by: ['endpoint'],
    where: isLifetime ? {} : { timestamp: { gte: fromDate, lte: toDate } },
    _count: {
      _all: true
    }
  });

  // Sort by usage count (descending) and take top 10
  apiUsage.sort((a, b) => b._count._all - a._count._all);

  return apiUsage.slice(0, 10).map(item => ({
    name: item.endpoint.split('/').pop() || item.endpoint,
    value: item._count._all
  }));
}


// Main server component
export default async function UserMetricsPage({
  searchParams,
}: {
  searchParams: { from?: string; to?: string };
}) {
  const session = await getServerSession(authOptions);
  if (!session) {
    redirect("/auth/signin");
  }

  // Parse date range from search params
  // For lifetime queries, both dates will be null
  const fromDate = searchParams.from ? new Date(searchParams.from) : null;
  const toDate = searchParams.to ? new Date(searchParams.to) : null;

  // If no dates provided, default to last 30 days (but not for lifetime)
  const isLifetimeQuery = !searchParams.from && !searchParams.to;
  const actualFromDate = isLifetimeQuery ? null : (fromDate || addDays(new Date(), -30));
  const actualToDate = isLifetimeQuery ? null : (toDate || new Date());
  const period = dateRangeToPeriod(actualFromDate, actualToDate);

  // Fetch all data in parallel
  const [userMetrics, activeUsersOverTime, roleDistribution, featureUsage] = await Promise.all([
    fetchUserMetrics(actualFromDate, actualToDate, period),
    fetchActiveUsersOverTime(actualFromDate, actualToDate, period),
    fetchUserRoleDistribution(actualFromDate, actualToDate),
    fetchFeatureUsage(actualFromDate, actualToDate),
  ]);

  const metricsData: UserMetricsData = {
    userMetrics,
    activeUsersOverTime,
    roleDistribution,
    featureUsage,
    period,
  };

  return <UserMetricsClient data={metricsData} />;
}
