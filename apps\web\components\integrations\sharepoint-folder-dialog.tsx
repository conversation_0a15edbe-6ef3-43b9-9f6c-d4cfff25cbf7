"use client";

import { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Di<PERSON>Footer,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { SharePointSiteSelector } from "./sharepoint-site-selector";
import { SharePointFolderSelector } from "./sharepoint-folder-selector";
import { Input } from "@/components/ui/input";
import { useLanguage } from "@/lib/language-context";
import { Loader2 } from "lucide-react";

interface SharePointFolderDialogProps {
  isOpen: boolean;
  onClose: () => void;
  tenantId: string;
  onFolderSelect: (
    folderId: string,
    folderName: string,
    siteId: string,
    driveId: string,
    folderPath?: string,
    selectedSiteName?: string | null
  ) => void;
  onCreateFolder?: (
    name: string,
    siteId: string,
    driveId: string,
    folderId?: string
  ) => Promise<void>;
  isLoading?: boolean;
}

export function SharePointFolderDialog({
  isOpen,
  onClose,
  tenantId,
  onFolderSelect,
  onCreateFolder,
  isLoading = false,
}: SharePointFolderDialogProps) {
  const [selectedSiteId, setSelectedSiteId] = useState<string | null>(null);
  const [selectedSiteName, setSelectedSiteName] = useState<string | null>(null);
  const [selectedFolderId, setSelectedFolderId] = useState<string | null>(null);
  const [selectedFolderName, setSelectedFolderName] = useState<string | null>(
    null
  );
  const [selectedDriveId, setSelectedDriveId] = useState<string | null>(null);
  const [selectedFolderPath, setSelectedFolderPath] = useState<string | null>(
    null
  );
  const [newFolderName, setNewFolderName] = useState("");
  const [showCreateFolder, setShowCreateFolder] = useState(false);
  const { t } = useLanguage();

  const handleSiteSelect = (siteId: string, siteName: string) => {
    setSelectedSiteId(siteId);
    setSelectedSiteName(siteName);
    // Reset folder selection when site changes
    setSelectedFolderId(null);
    setSelectedFolderName(null);
    setSelectedDriveId(null);
  };

  const handleFolderSelect = (
    folderId: string,
    folderName: string,
    driveId: string,
    folderPath?: string
  ) => {
    setSelectedFolderId(folderId);
    setSelectedFolderName(folderName);
    setSelectedDriveId(driveId);
    setSelectedFolderPath(folderPath || null);
  };

  const handleSelectClick = () => {
    if (selectedSiteId && selectedFolderId && selectedDriveId) {
      onFolderSelect(
        selectedFolderId,
        selectedFolderName ||
          t("integration.selectedFolder") ||
          "Selected Folder",
        selectedSiteId,
        selectedDriveId,
        selectedFolderPath || undefined,
        selectedSiteName
      );
      onClose();
    }
  };

  const handleCreateFolderClick = async () => {
    if (onCreateFolder && newFolderName && selectedSiteId && selectedDriveId) {
      await onCreateFolder(
        newFolderName,
        selectedSiteId,
        selectedDriveId,
        selectedFolderId || undefined
      );
      setNewFolderName("");
      setShowCreateFolder(false);
      onClose();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[525px]">
        <DialogHeader>
          <DialogTitle>
            {t("workspace.syncSharePointFolder") || "Sync SharePoint Folder"}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          <div className="space-y-2">
            <label className="text-sm font-medium">
              {t("integration.selectSharePointSite") ||
                "Select SharePoint Site"}
            </label>
            <SharePointSiteSelector
              tenantId={tenantId}
              onSiteSelect={handleSiteSelect}
              selectedSiteId={selectedSiteId || undefined}
            />
          </div>

          {selectedSiteId && (
            <div className="space-y-2">
              <label className="text-sm font-medium">
                {t("integration.selectSharePointFolder") ||
                  "Select SharePoint Folder"}
              </label>
              <SharePointFolderSelector
                tenantId={tenantId}
                siteId={selectedSiteId}
                driveId={selectedDriveId || undefined}
                onFolderSelect={handleFolderSelect}
                selectedFolderId={selectedFolderId || undefined}
                selectedFolderName={selectedFolderName || undefined}
              />
            </div>
          )}

          {/* Sync disclaimer */}
          <div className="bg-blue-50 dark:bg-blue-950 border border-blue-200 dark:border-blue-800 p-3 rounded-md">
            <div className="flex items-start">
              <div className="flex-shrink-0 pt-0.5">
                <svg
                  className="h-5 w-5 text-blue-400"
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                  aria-hidden="true"
                >
                  <path
                    fillRule="evenodd"
                    d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                    clipRule="evenodd"
                  />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm text-blue-700 dark:text-blue-300">
                  {selectedFolderName ? (
                    <>
                      <span className="font-semibold">Selected path:</span>{" "}
                      {selectedSiteName} / {selectedFolderName}
                    </>
                  ) : (
                    t("integration.syncDisclaimer") ||
                    "All files and subfolders within the selected folder will be synchronized and available in read-only mode."
                  )}
                </p>
              </div>
            </div>
          </div>

          {selectedSiteId && selectedDriveId && onCreateFolder && (
            <div className="space-y-2">
              {showCreateFolder ? (
                <div className="space-y-2">
                  <label className="text-sm font-medium">
                    {t("workspace.createNewFolder") || "Create New Folder"}
                  </label>
                  <div className="flex gap-2">
                    <Input
                      value={newFolderName}
                      onChange={(e) => setNewFolderName(e.target.value)}
                      placeholder={t("workspace.folderName") || "Folder Name"}
                    />
                    <Button
                      size="sm"
                      onClick={() => setShowCreateFolder(false)}
                      variant="outline"
                    >
                      {t("common.cancel") || "Cancel"}
                    </Button>
                  </div>
                </div>
              ) : (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowCreateFolder(true)}
                >
                  {t("workspace.createNewFolder") || "Create New Folder"}
                </Button>
              )}
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            {t("common.cancel") || "Cancel"}
          </Button>

          {showCreateFolder && newFolderName ? (
            <Button
              onClick={handleCreateFolderClick}
              disabled={isLoading || !newFolderName.trim()}
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  {t("common.creating") || "Creating..."}
                </>
              ) : (
                t("workspace.createAndSync") || "Create & Sync"
              )}
            </Button>
          ) : (
            <Button
              onClick={handleSelectClick}
              disabled={
                !selectedSiteId || !selectedFolderId || !selectedDriveId
              }
            >
              {t("common.select") || "Select"}
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
