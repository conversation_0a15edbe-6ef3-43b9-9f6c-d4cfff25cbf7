"use client";

import { useState } from "react";
import { Loader2, Users } from "lucide-react";
import { Button } from "@/components/ui/button";
import { createCheckoutSession, updateSubscriptionAddons } from "@/services";
import { Plan } from "@/services/src/plans";
import { getCookie } from "@/utils/cookies";
import toast from "react-hot-toast";
import { useLanguage } from "@/lib/language-context";
import { PlanPrice } from "@/services/src/stripe-prices";

interface StripeCheckoutButtonProps {
  plan: Plan;
  variant?:
  | "default"
  | "outline"
  | "secondary"
  | "destructive"
  | "ghost"
  | "link";
  disabled?: boolean;
  billingInterval?: "month" | "year";
  stripePriceData?: PlanPrice;
}

export function StripeCheckoutButton({
  plan,
  variant = "default",
  disabled = false,
  billingInterval = "month",
  stripePriceData,
}: StripeCheckoutButtonProps) {
  const [loading, setLoading] = useState(false);
  const tenantId = getCookie("currentOrganizationId");
  const userId = getCookie("userId");
  const { t } = useLanguage();

  const proceedToCheckout = async () => {
    if (!tenantId || !userId) {
      toast.error(t("billing.loginRequiredForUpgrade"));
      return;
    }

    try {
      setLoading(true);

      // Check if this is an existing subscription update or a new subscription
      const isExistingSubscription = !!getCookie("hasActiveSubscription");

      if (isExistingSubscription) {
        // Update existing subscription
        const result = await updateSubscriptionAddons({
          tenantId,
          planId: plan.id,
          billingInterval,
          // If we have Stripe price data, pass the price IDs
          stripePriceId: stripePriceData
            ? billingInterval === "year"
              ? stripePriceData.yearlyPrice?.id
              : stripePriceData.monthlyPrice?.id
            : undefined,
          stripeUserPriceId: stripePriceData
            ? billingInterval === "year"
              ? stripePriceData.userYearlyPrice?.id
              : stripePriceData.userMonthlyPrice?.id
            : undefined,
        });

        // Show success message
        toast.success(t("billing.subscriptionUpdateSuccess"));

        // Redirect to Stripe customer portal to complete the update
        window.location.href = result.portalUrl;
      } else {
        // Create a new Stripe checkout session
        const session = await createCheckoutSession({
          planId: plan.id,
          tenantId,
          userId,
          successUrl: `${window.location.origin}/billing?success=true`,
          cancelUrl: `${window.location.origin}/billing?canceled=true`,
          billingInterval,
          // If we have Stripe price data, pass the price IDs
          stripePriceId: stripePriceData
            ? billingInterval === "year"
              ? stripePriceData.yearlyPrice?.id
              : stripePriceData.monthlyPrice?.id
            : undefined,
          stripeUserPriceId: stripePriceData
            ? billingInterval === "year"
              ? stripePriceData.userYearlyPrice?.id
              : stripePriceData.userMonthlyPrice?.id
            : undefined,
        });

        // Redirect to Stripe checkout
        window.location.href = session.url;
      }
    } catch (error) {
      console.error("Error processing subscription:", error);
      toast.error(
        error?.response?.data?.error ?? t("billing.checkoutSessionFailed")
      );
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="w-full min-w-0">
      <Button
        variant={variant}
        disabled={disabled || loading}
        onClick={() => {
          if (plan.type === "CUSTOM") {
            window.open("mailto:<EMAIL>");
          } else proceedToCheckout();
        }}
        className="w-full min-h-[44px] text-sm sm:text-base px-4"
      >
        <div className="flex items-center justify-center flex-wrap gap-2 text-center w-full text-white">
          {loading ? (
            <>
              <Loader2 className="h-4 w-4 animate-spin shrink-0" />
              <span>{t("billing.processing")}</span>
            </>
          ) : plan.type === "CUSTOM" ? (
            <span>{t("billing.contactUs")}</span>
          ) : (
            <>
              <span className="text-center break-words p-3">
                {getCookie("hasActiveSubscription")
                  ? t("billing.upgradePlan")
                  : t("billing.subscribe")}
                {billingInterval === "year" && (
                  <span> ({t("billing.yearly")})</span>
                )}
              </span>
            </>
          )}
        </div>
      </Button>


    </div>
  );
}
