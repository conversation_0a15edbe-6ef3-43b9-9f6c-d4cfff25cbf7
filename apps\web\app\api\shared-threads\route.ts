import { NextResponse } from "next/server";
import db from "@/lib/shared-db";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/next-auth";
import { cookies } from "next/headers";

// Get shared threads for current user/tenant
export async function GET(req: Request) {
  try {
    const session: any = await getServerSession(authOptions);
    if (!session) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const url = new URL(req.url);
    const search = url.searchParams.get("search") || "";
    const type = url.searchParams.get("type") as
      | "private"
      | "public"
      | undefined;
    const limit = parseInt(url.searchParams.get("limit") || "50");
    const offset = parseInt(url.searchParams.get("offset") || "0");

    // Get current tenant ID from cookies or session
    const cookieStore = cookies();
    const tenantId =
      cookieStore.get("currentOrganizationId")?.value ?? session?.tenantId;

    if (!tenantId) {
      return new NextResponse("No tenant selected", { status: 400 });
    }

    // Verify user has access to this tenant
    const membership = await db.membership.findFirst({
      where: {
        userId: session.userId,
        tenantId: tenantId,
      },
    });

    if (!membership) {
      return new NextResponse("Access denied", { status: 403 });
    }

    // Build where clause for filtering
    const whereClause: any = {
      tenantId: tenantId,
      // Exclude expired shares
      OR: [{ expiresAt: null }, { expiresAt: { gt: new Date() } }],
    };

    // For private threads, only show threads where user has access
    if (type === "private") {
      whereClause.AND = [{ isPublic: false }];
    } else if (type === "public") {
      whereClause.isPublic = true;
    } else {
      // Show both private (accessible) and public
      const privateWhere = {
        isPublic: false,
        OR: [
          { createdById: session.userId },
          { chat: { userId: session.userId } },
        ],
      };

      const publicWhere = {
        isPublic: true,
      };

      whereClause.OR = [
        { ...whereClause, ...privateWhere },
        { ...whereClause, ...publicWhere },
      ];
      delete whereClause.isPublic;
    }

    // Add search filter if provided
    if (search) {
      const searchFilter = {
        chat: {
          OR: [
            { title: { contains: search, mode: "insensitive" } },
            { description: { contains: search, mode: "insensitive" } },
          ],
        },
      };

      if (whereClause.AND) {
        whereClause.AND.push(searchFilter);
      } else {
        whereClause.AND = [searchFilter];
      }
    }

    // Get shared threads with related data
    const sharedThreads = await db.threadShare.findMany({
      where: whereClause,
      include: {
        chat: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
                image: true,
              },
            },
            messages: {
              take: 1,
              orderBy: {
                createdAt: "desc",
              },
              select: {
                id: true,
                content: true,
                createdAt: true,
                user: {
                  select: {
                    id: true,
                    name: true,
                    email: true,
                  },
                },
              },
            },
            _count: {
              select: {
                messages: true,
              },
            },
          },
        },
        createdBy: {
          select: {
            id: true,
            name: true,
            email: true,
            image: true,
          },
        },
        tenant: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
      },
      orderBy: {
        updatedAt: "desc",
      },
      skip: offset,
      take: limit,
    });

    // Get unread counts and comment statistics for each thread
    const threadsWithUnreadInfo = await Promise.all(
      sharedThreads.map(async (thread: any) => {
        // Get all comments for this chat to count them
        const allComments = await db.comment.findMany({
          where: {
            message: {
              chatId: thread.chatId,
            },
            status: "ACTIVE",
          },
          select: {
            id: true,
            mentions: true, // This is a JSON field containing user IDs
          },
        });

        // Calculate comment counts
        const totalComments = allComments.length;

        // Calculate mention counts for current user
        const userMentions = allComments.reduce(
          (total: number, comment: any) => {
            // mentions is a JSON array of user IDs
            const mentionArray = (comment.mentions as string[]) || [];
            return total + (mentionArray.includes(session.userId) ? 1 : 0);
          },
          0
        );

        // TODO: Implement actual unread count logic based on notifications
        const unreadCount = 0;
        const hasNewActivity = false;
        const lastActivityAt = thread.updatedAt;

        return {
          id: thread.id,
          chatId: thread.chatId,
          shareToken: thread.shareToken,
          isPublic: thread.isPublic,
          expiresAt: thread.expiresAt,
          createdAt: thread.createdAt,
          updatedAt: thread.updatedAt,
          unreadCount,
          hasNewActivity,
          lastActivityAt,
          totalComments,
          userMentions,
          chat: {
            ...thread.chat,
            messages: thread.chat.messages.map((msg: any) => ({
              id: msg.id,
              content: msg.content,
              createdAt: msg.createdAt,
              user: msg.user,
            })), // Remove comments from response to reduce payload
          },
          createdBy: thread.createdBy,
          tenant: thread.tenant,
        };
      })
    );

    // Separate into private and public threads
    const privateThreads = threadsWithUnreadInfo.filter(
      (thread) => !thread.isPublic
    );
    const publicThreads = threadsWithUnreadInfo.filter(
      (thread) => thread.isPublic
    );

    // Get total counts
    const totalCount = await db.threadShare.count({
      where: whereClause,
    });

    return NextResponse.json({
      privateThreads,
      publicThreads,
      totalCount,
      hasMore: offset + limit < totalCount,
    });
  } catch (error) {
    console.error("[SHARED_THREADS_GET]", error);
    return NextResponse.json({ error: "Internal Error" }, { status: 500 });
  }
}
