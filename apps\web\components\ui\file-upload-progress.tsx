"use client";

import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  CheckCircle, 
  XCircle, 
  Loader2, 
  Upload, 
  AlertTriangle,
  File
} from "lucide-react";
import { cn } from "@/lib/utils";

export interface FileUploadProgress {
  total: number;
  processed: number;
  successful: number;
  failed: number;
  currentBatch: string[];
}

interface FileUploadProgressProps {
  progress: FileUploadProgress;
  className?: string;
  showDetails?: boolean;
}

export function FileUploadProgress({ 
  progress, 
  className,
  showDetails = true 
}: FileUploadProgressProps) {
  const { total, processed, successful, failed, currentBatch } = progress;
  const isComplete = processed >= total;
  const progressPercentage = total > 0 ? Math.round((processed / total) * 100) : 0;
  const hasErrors = failed > 0;

  return (
    <div className={cn("space-y-4", className)}>
      {/* Progress Overview */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-base">
            {isComplete ? (
              <CheckCircle className="h-5 w-5 text-green-600" />
            ) : (
              <Loader2 className="h-5 w-5 animate-spin text-primary" />
            )}
            File Upload Progress
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Progress Bar */}
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>
                {processed} of {total} files processed
              </span>
              <span className="font-medium">{progressPercentage}%</span>
            </div>
            <Progress value={progressPercentage} className="h-2" />
          </div>

          {/* Status Badges */}
          <div className="flex flex-wrap gap-2">
            <Badge variant="outline" className="gap-1">
              <Upload className="h-3 w-3" />
              {total} Total
            </Badge>
            
            {successful > 0 && (
              <Badge variant="default" className="gap-1 bg-green-100 text-green-800 border-green-200">
                <CheckCircle className="h-3 w-3" />
                {successful} Successful
              </Badge>
            )}
            
            {failed > 0 && (
              <Badge variant="destructive" className="gap-1">
                <XCircle className="h-3 w-3" />
                {failed} Failed
              </Badge>
            )}
          </div>

          {/* Current Batch */}
          {currentBatch.length > 0 && (
            <div className="space-y-2">
              <div className="text-sm font-medium text-muted-foreground">
                Currently uploading:
              </div>
              <div className="space-y-1">
                {currentBatch.map((fileName, index) => (
                  <div key={index} className="flex items-center gap-2 text-sm">
                    <Loader2 className="h-3 w-3 animate-spin text-primary" />
                    <File className="h-3 w-3 text-muted-foreground" />
                    <span className="truncate">{fileName}</span>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Completion Status */}
          {isComplete && (
            <Alert className={cn(
              hasErrors 
                ? "border-yellow-200 bg-yellow-50 dark:border-yellow-800 dark:bg-yellow-900/20" 
                : "border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-900/20"
            )}>
              {hasErrors ? (
                <AlertTriangle className="h-4 w-4 text-yellow-600 dark:text-yellow-400" />
              ) : (
                <CheckCircle className="h-4 w-4 text-green-600 dark:text-green-400" />
              )}
              <AlertDescription>
                {hasErrors ? (
                  <>
                    Upload completed with {failed} error{failed !== 1 ? 's' : ''}. 
                    {successful > 0 && ` ${successful} file${successful !== 1 ? 's' : ''} uploaded successfully.`}
                  </>
                ) : (
                  `All ${successful} files uploaded successfully!`
                )}
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* Detailed View */}
      {showDetails && isComplete && (
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base">Upload Summary</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-3 gap-4 text-center">
              <div className="space-y-1">
                <div className="text-2xl font-bold text-muted-foreground">{total}</div>
                <div className="text-xs text-muted-foreground">Total Files</div>
              </div>
              <div className="space-y-1">
                <div className="text-2xl font-bold text-green-600">{successful}</div>
                <div className="text-xs text-muted-foreground">Successful</div>
              </div>
              <div className="space-y-1">
                <div className="text-2xl font-bold text-red-600">{failed}</div>
                <div className="text-xs text-muted-foreground">Failed</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

// Compact version for inline display
export function FileUploadProgressCompact({ 
  progress, 
  className 
}: FileUploadProgressProps) {
  const { total, processed, successful, failed } = progress;
  const progressPercentage = total > 0 ? Math.round((processed / total) * 100) : 0;
  const isComplete = processed >= total;

  return (
    <div className={cn("space-y-2", className)}>
      <div className="flex items-center justify-between text-sm">
        <span className="flex items-center gap-2">
          {isComplete ? (
            <CheckCircle className="h-4 w-4 text-green-600" />
          ) : (
            <Loader2 className="h-4 w-4 animate-spin text-primary" />
          )}
          Uploading files...
        </span>
        <span className="text-muted-foreground">
          {processed}/{total} ({progressPercentage}%)
        </span>
      </div>
      
      <Progress value={progressPercentage} className="h-1.5" />
      
      {isComplete && (
        <div className="flex gap-2 text-xs">
          <span className="text-green-600">✓ {successful} successful</span>
          {failed > 0 && <span className="text-red-600">✗ {failed} failed</span>}
        </div>
      )}
    </div>
  );
}
