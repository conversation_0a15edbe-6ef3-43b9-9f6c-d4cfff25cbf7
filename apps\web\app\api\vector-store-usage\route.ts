import { NextResponse } from "next/server";
import { getToken } from "next-auth/jwt";
import db from "@/lib/shared-db";
import { VectorStoreUsageRequestSchema } from "@/scripts/seed-subscription-schema";
import { z } from "zod";
import { withPermission } from "@/lib/permission-middleware";

// Get vector store usage for a tenant
export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const tenantId = searchParams.get("tenantId");
    const userId = searchParams.get("userId");

    if (!tenantId) {
      return NextResponse.json(
        { error: "Tenant ID is required" },
        { status: 400 }
      );
    }

    // Verify user has access to this tenant
    const membership = await db.membership.findFirst({
      where: {
        userId,
        tenantId,
      },
    });

    if (!membership) {
      return NextResponse.json(
        { error: "You don't have access to this tenant" },
        { status: 403 }
      );
    }

    // Get the latest vector store usage
    const latestUsage = await db.vectorStoreUsage.findFirst({
      where: {
        tenantId,
      },
      orderBy: {
        timestamp: "desc",
      },
    });

    // Get the total usage
    const totalUsage = await db.vectorStoreUsage.aggregate({
      where: {
        tenantId,
      },
      _sum: {
        usageGB: true,
      },
    });

    // Get the active subscription with plan details
    const subscription = await db.subscription.findFirst({
      where: {
        tenantId,
        isActive: true,
      },
      include: {
        plan: true,
      },
    });

    // Calculate the total storage limit including additional storage
    const baseStorageLimit = subscription?.plan?.vectorStoreGB || 0;
    const additionalStorage = subscription?.additionalStorageGB || 0;
    const totalStorageLimit = baseStorageLimit + additionalStorage;

    return NextResponse.json({
      currentUsage: latestUsage?.usageGB || 0,
      totalUsage: totalUsage._sum.usageGB || 0,
      limit: totalStorageLimit,
      subscription,
    });
  } catch (error) {
    console.error("Error fetching vector store usage:", error);
    return NextResponse.json(
      { error: "Failed to fetch vector store usage" },
      { status: 500 }
    );
  }
}

// Record new vector store usage
async function recordVectorStoreUsage(request: Request) {
  try {
    const token = await getToken({ req: request as any });
    if (!token) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();

    // Validate request body against schema
    let validatedData;
    try {
      validatedData = VectorStoreUsageRequestSchema.parse(body);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return NextResponse.json(
          {
            error: "Validation error",
            details: error.errors,
          },
          { status: 400 }
        );
      }
      throw error;
    }

    const { tenantId, usageGB } = validatedData;

    // Permission checking is handled by the withPermission middleware
    // Users with CREATE FILE permission can record vector store usage

    // Record the usage
    const usage = await db.vectorStoreUsage.create({
      data: {
        tenantId,
        usageGB,
        timestamp: new Date(),
      },
    });

    return NextResponse.json({ usage }, { status: 201 });
  } catch (error) {
    console.error("Error recording vector store usage:", error);
    return NextResponse.json(
      { error: "Failed to record vector store usage" },
      { status: 500 }
    );
  }
}

// Export the POST handler with permission middleware
export const POST = withPermission(recordVectorStoreUsage, "CREATE", "FILE");
