"use client";

import React, { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import {
  Card,
  CardContent,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Building,
  ArrowLeft,
  Eye,
  EyeOff,
  Loader2,
  UserPlus,
  CheckCircle,
  AlertCircle,
} from "lucide-react";
import toast from "react-hot-toast";
import Link from "next/link";
import { ThemeConfigProvider } from "@/lib/theme-config-context";
import { WhiteLabelThemeProvider } from "@/components/theme/white-label-theme-provider";
import { DynamicFaviconManager } from "@/components/theme/dynamic-favicon-manager";
import { useBrandName } from "@/lib/theme-config-context";
import { ThemeConfig } from "@/types/theme-config";
import { generateThemeVariables } from "@/services/theme-config";

// Helper function to convert ThemeConfiguration to ThemeConfig
function convertToThemeConfig(themeConfiguration?: ThemeConfiguration): ThemeConfig | undefined {
  if (!themeConfiguration) return undefined;

  return {
    brandName: themeConfiguration.brandName,
    logoUrl: themeConfiguration.logoUrl,
    faviconUrl: themeConfiguration.faviconUrl,
    fullAppLogoUrl: themeConfiguration.fullAppLogoUrl,
    primaryColor: themeConfiguration.primaryColor,
    secondaryColor: themeConfiguration.secondaryColor,
    accentColor: themeConfiguration.accentColor,
    navigationBackgroundColor: themeConfiguration.navigationBackgroundColor,
    contentBackgroundColor: themeConfiguration.contentBackgroundColor,
    foregroundColor: themeConfiguration.foregroundColor,
    themePreset: themeConfiguration.themePreset as "light" | "dark" | undefined,
  };
}

// Types
interface ThemeConfiguration {
  id: string;
  tenantId: string;
  brandName?: string;
  logoUrl?: string;
  faviconUrl?: string;
  fullAppLogoUrl?: string;
  primaryColor?: string;
  secondaryColor?: string;
  accentColor?: string;
  navigationBackgroundColor?: string;
  contentBackgroundColor?: string;
  foregroundColor?: string;
  themePreset?: string;
  isActive: boolean;
  version: number;
  updatedAt: string;
}

interface Company {
  id: string;
  name: string;
  slug: string;
  description: string;
  tenant: {
    id: string;
    name: string;
    themeConfiguration?: ThemeConfiguration;
  };
}

// Form schema
const signupSchema = z
  .object({
    name: z.string().min(2, {
      message: "Name must be at least 2 characters long",
    }),
    email: z.string().email({
      message: "Please enter a valid email address",
    }),
    password: z.string().min(8, {
      message: "Password must be at least 8 characters long",
    }),
    confirmPassword: z.string(),
    agreeToTerms: z.boolean().refine((value) => value === true, {
      message: "You must agree to the terms and conditions",
    }),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Passwords do not match",
    path: ["confirmPassword"],
  });

type SignupFormValues = z.infer<typeof signupSchema>;

// Main signup page content component
function SignupPageContent() {
  const params = useParams();
  const router = useRouter();
  const slug = params?.slug as string;
  const brandName = useBrandName();

  const [company, setCompany] = useState<Company | null>(null);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [registrationComplete, setRegistrationComplete] = useState(false);
  const [registeredEmail, setRegisteredEmail] = useState("");

  const form = useForm<SignupFormValues>({
    resolver: zodResolver(signupSchema),
    defaultValues: {
      name: "",
      email: "",
      password: "",
      confirmPassword: "",
      agreeToTerms: false,
    },
  });

  // Load company data
  useEffect(() => {
    if (slug) {
      loadCompanyData();
    }
  }, [slug]);

  // Apply theme configuration and update document title
  useEffect(() => {
    if (company?.tenant?.themeConfiguration) {
      const themeConfiguration = company.tenant.themeConfiguration;

      // Update document title
      if (themeConfiguration.brandName) {
        document.title = `Sign Up - ${themeConfiguration.brandName}`;
      }

      // Apply theme configuration
      const isPartnerConsole = process.env.NEXT_PUBLIC_IS_PARTNER_CONSOLE === "1";
      if (isPartnerConsole) {
        // Convert to proper ThemeConfig format
        const themeConfig = convertToThemeConfig(themeConfiguration);
        if (themeConfig) {
          // Generate and apply CSS variables for custom colors using the proper function
          const themeVariables = generateThemeVariables(themeConfig);
          const root = document.documentElement;

          // Apply color variables to the root element
          Object.entries(themeVariables).forEach(([property, value]) => {
            root.style.setProperty(property, value);
          });

          // Apply theme mode
          if (themeConfig.themePreset) {
            root.classList.remove('light', 'dark');
            root.classList.add(themeConfig.themePreset);
          }

          // Apply CSS overrides for hardcoded Tailwind classes on invite pages
          applyInvitePageOverrides();
        }
      }
    }
  }, [company]);

  // Function to apply CSS overrides for hardcoded classes on invite pages
  const applyInvitePageOverrides = () => {
    const styleId = "invite-page-theme-overrides";

    // Remove existing style element if it exists
    const existingStyle = document.getElementById(styleId);
    if (existingStyle) {
      existingStyle.remove();
    }

    const css = `
      /* Invite page background overrides */
      .min-h-screen.bg-gradient-to-br.from-blue-50.to-indigo-100,
      .min-h-screen.bg-gradient-to-br {
        background: hsl(var(--background)) !important;
      }

      /* Header background overrides */
      .bg-white.shadow-sm.border-b,
      .bg-white.border-b {
        background-color: hsl(var(--sidebar-background)) !important;
      }

      /* Card backgrounds */
      .bg-white.rounded-lg,
      .shadow-lg {
        background-color: hsl(var(--card)) !important;
      }

      /* Text color overrides */
      .text-gray-900 {
        color: hsl(var(--foreground)) !important;
      }

      .text-gray-600 {
        color: hsl(var(--muted-foreground)) !important;
      }

      .text-blue-600 {
        color: hsl(var(--primary)) !important;
      }

      /* Error page background */
      .bg-gradient-to-br.from-red-50.to-pink-100 {
        background: hsl(var(--background)) !important;
      }

      /* Loading spinner color */
      .text-blue-600.animate-spin {
        color: hsl(var(--primary)) !important;
      }

      /* Icon colors */
      .text-blue-600 {
        color: hsl(var(--primary)) !important;
      }

      /* Button overrides are handled by the button component using CSS custom properties */
    `;

    // Create and append style element
    const styleElement = document.createElement("style");
    styleElement.id = styleId;
    styleElement.textContent = css;
    document.head.appendChild(styleElement);
  };

  const loadCompanyData = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await fetch(`/api/companies/invite/${slug}`);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to load company");
      }

      setCompany(data.company);
    } catch (error: any) {
      setError(error.message || "Failed to load company");
    } finally {
      setLoading(false);
    }
  };

  const onSubmit = async (data: SignupFormValues) => {
    setSubmitting(true);
    try {
      const response = await fetch("/api/auth/register", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          name: data.name,
          email: data.email,
          password: data.password,
          inviteSlug: slug,
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || "Registration failed");
      }

      // Show success state
      setRegisteredEmail(data.email);
      setRegistrationComplete(true);
      toast.success(
        "Registration successful! Please check your email to verify your account."
      );
    } catch (error: any) {
      toast.error(error.message || "Registration failed");
    } finally {
      setSubmitting(false);
    }
  };

  // Show loading state
  if (loading) {
    return (
      <div className="min-h-screen py-12 flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
        <div className="text-center px-4">
          <Loader2 className="h-12 w-12 text-blue-600 mx-auto mb-4 animate-spin" />
          <h3 className="text-lg font-semibold mb-2">Loading invitation...</h3>
          <p className="text-gray-600">
            Please wait while we fetch the company details.
          </p>
        </div>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="min-h-screen py-12 flex items-center justify-center bg-gradient-to-br from-red-50 to-pink-100">
        <Card className="w-full max-w-md shadow-lg mx-4">
          <CardContent className="pt-6">
            <div className="text-center">
              <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">
                Invitation Not Found
              </h3>
              <p className="text-gray-600 mb-4">{error}</p>
              <Button onClick={() => router.push("/")} variant="outline">
                Go to Home
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!company) return null;

  // Show registration complete state
  if (registrationComplete) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-50 to-emerald-100 py-12 flex items-center justify-center">
        <Card className="w-full max-w-md shadow-lg mx-4">
          <CardContent className="pt-6">
            <div className="text-center space-y-4">
              <CheckCircle className="h-16 w-16 text-green-500 mx-auto" />
              <h2 className="text-2xl font-bold text-gray-900">
                Check Your Email
              </h2>
              <p className="text-gray-600">
                We've sent a verification email to{" "}
                <strong>{registeredEmail}</strong>
              </p>
              <p className="text-sm text-gray-500">
                Please click the verification link in your email to complete
                your registration and join {company.name}.
              </p>
              <div className="pt-4 space-y-2">
                <Button
                  onClick={() => router.push(`/invite/${slug}`)}
                  variant="outline"
                  className="w-full"
                >
                  Back to Invitation
                </Button>
                <p className="text-xs text-gray-400 text-center">
                  After verification, you'll be automatically added to{" "}
                  {company.name}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-full h-full">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center gap-3">
              {company?.tenant.themeConfiguration?.logoUrl ? (
                <img
                  src={company.tenant.themeConfiguration.logoUrl}
                  alt={brandName}
                  className="h-8 w-8 object-contain"
                />
              ) : (
                <Building className="h-8 w-8 text-blue-600" />
              )}
              <span className="text-xl font-bold text-gray-900">
                {brandName}
              </span>
            </div>
            <Link href={`/invite/${slug}`}>
              <Button variant="outline">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Invitation
              </Button>
            </Link>
          </div>
        </div>
      </div>

      <div className="py-6 pb-12">
        <div className="max-w-md mx-auto px-4 sm:px-6 lg:px-8">
          {/* Company Info */}
          <div className="text-center mb-6">
            <h1 className="text-2xl font-bold text-gray-900 mb-2">
              Join {company.name}
            </h1>
            <p className="text-gray-600">
              Create your account to join {company.name} and start collaborating
              with the team.
            </p>
          </div>

          {/* Signup Form */}
          <Card className="shadow-lg">
            <CardContent>
              <Form {...form}>
                <form
                  onSubmit={form.handleSubmit(onSubmit)}
                  className="space-y-4 mt-4"
                >
                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Full Name</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Enter your full name"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Email Address</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Enter your email address"
                            type="email"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="password"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Password</FormLabel>
                        <FormControl>
                          <div className="relative">
                            <Input
                              placeholder="Create a strong password"
                              type={showPassword ? "text" : "password"}
                              {...field}
                            />
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                              onClick={() => setShowPassword(!showPassword)}
                            >
                              {showPassword ? (
                                <EyeOff className="h-4 w-4" />
                              ) : (
                                <Eye className="h-4 w-4" />
                              )}
                            </Button>
                          </div>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="confirmPassword"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Confirm Password</FormLabel>
                        <FormControl>
                          <div className="relative">
                            <Input
                              placeholder="Confirm your password"
                              type={showConfirmPassword ? "text" : "password"}
                              {...field}
                            />
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                              onClick={() =>
                                setShowConfirmPassword(!showConfirmPassword)
                              }
                            >
                              {showConfirmPassword ? (
                                <EyeOff className="h-4 w-4" />
                              ) : (
                                <Eye className="h-4 w-4" />
                              )}
                            </Button>
                          </div>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="agreeToTerms"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                        <FormControl>
                          <Checkbox
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                        <div className="space-y-1 leading-none">
                          <FormLabel className="text-sm">
                            I agree to the{" "}
                            <Link
                              href="/terms"
                              className="text-blue-600 hover:underline"
                            >
                              Terms of Service
                            </Link>{" "}
                            and{" "}
                            <Link
                              href="/privacy"
                              className="text-blue-600 hover:underline"
                            >
                              Privacy Policy
                            </Link>
                          </FormLabel>
                          <FormMessage />
                        </div>
                      </FormItem>
                    )}
                  />

                  <Button
                    type="submit"
                    disabled={submitting}
                    className="w-full"
                    size="lg"
                  >
                    {submitting ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Creating Account...
                      </>
                    ) : (
                      <>
                        <UserPlus className="mr-2 h-4 w-4" />
                        Create Account & Join
                      </>
                    )}
                  </Button>
                </form>
              </Form>

              <div className="mt-6 text-center">
                <p className="text-sm text-gray-600">
                  Already have an account?{" "}
                  <Link
                    href={`/sign-in?callbackUrl=/invite/${slug}`}
                    className="text-blue-600 hover:underline"
                  >
                    Sign in instead
                  </Link>
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}

// Main export component with theme providers
export default function InviteSignupPage() {
  const params = useParams();
  const slug = params?.slug as string;
  const [company, setCompany] = useState<Company | null>(null);
  const [loading, setLoading] = useState(true);

  // Load company data to get tenant info for theme configuration
  useEffect(() => {
    if (slug) {
      loadCompanyData();
    }
  }, [slug]);

  const loadCompanyData = async () => {
    setLoading(true);
    try {
      const response = await fetch(`/api/companies/invite/${slug}`);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to load company");
      }

      setCompany(data.company);
    } catch (error: any) {
      console.error("Error loading company data:", error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!company) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Company Not Found</h1>
          <p className="text-gray-600">The invitation link is invalid or has expired.</p>
        </div>
      </div>
    );
  }

  const tenantId = company.tenant.id;
  const themeConfig = convertToThemeConfig(company.tenant.themeConfiguration);

  return (
    <ThemeConfigProvider tenantId={tenantId} initialConfig={themeConfig}>
      <WhiteLabelThemeProvider tenantId={tenantId} themeConfig={themeConfig}>
        <DynamicFaviconManager themeConfig={themeConfig || null} />
        <SignupPageContent />
      </WhiteLabelThemeProvider>
    </ThemeConfigProvider>
  );
}
