import { apiUrl, fetchJson } from "..";

export interface WebSearchResult {
  title: string;
  link: string;
  snippet: string;
  displayLink: string;
  source: string;
}

export interface WebSearchResponse {
  results: WebSearchResult[];
  cached: boolean;
  error?: string;
  limitExceeded?: boolean;
  limitInfo?: {
    current: number;
    limit: number;
    message: string | null;
  };
}

export const performWebSearch = async (
  query: string,
  tenantId: string,
  userId: string
): Promise<WebSearchResponse> => {
  try {
    const response = await fetchJson(
      `${apiUrl}/web-search?query=${encodeURIComponent(
        query
      )}&tenantId=${tenantId}&userId=${userId}`
    );
    return response;
  } catch (error: any) {
    console.error("Error in web search service:", error);
    
    // Check if the error has a response with data
    if (error.response && error.response.data) {
      return {
        results: [],
        cached: false,
        error: error.response.data.error || "Failed to perform web search",
        limitExceeded: error.response.data.limitExceeded,
        limitInfo: error.response.data.limitInfo,
      };
    }
    
    return {
      results: [],
      cached: false,
      error: error.message || "Failed to perform web search",
    };
  }
};
