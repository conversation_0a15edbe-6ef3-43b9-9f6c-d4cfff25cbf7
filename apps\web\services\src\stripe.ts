import axios from "axios";
import { apiUrl } from "..";
import { SubscriptionWithPlan } from "./subscriptions";

export interface StripeCheckoutSession {
  id: string;
  url: string;
}

export interface StripeCheckoutSessionResponse {
  session: StripeCheckoutSession;
}

export interface CreateCheckoutSessionRequest {
  planId: string;
  tenantId: string;
  userId: string;
  successUrl: string;
  cancelUrl: string;
  additionalUsers?: number;
  additionalStorageGB?: number;
  discountCode?: string;
  billingInterval?: "month" | "year";
  stripePriceId?: string; // Direct Stripe price ID for the plan
  stripeUserPriceId?: string; // Direct Stripe price ID for additional users
  stripeStoragePriceId?: string; // Direct Stripe price ID for storage
}

export interface StripePortalSessionResponse {
  url: string;
}

// Create a Stripe checkout session for plan subscription
export const createCheckoutSession = async (
  data: CreateCheckoutSessionRequest
): Promise<StripeCheckoutSession> => {
  try {
    const response = await axios.post<StripeCheckoutSessionResponse>(
      `${apiUrl}/stripe/create-checkout-session`,
      data
    );
    return response.data.session;
  } catch (error) {
    console.error("Error creating Stripe checkout session:", error);
    throw error;
  }
};

// Create a Stripe customer portal session for managing subscription
export const createCustomerPortalSession = async (
  tenantId: string,
  returnUrl: string
): Promise<string> => {
  try {
    const response = await axios.post<StripePortalSessionResponse>(
      `${apiUrl}/stripe/create-portal-session`,
      {
        tenantId,
        returnUrl,
      }
    );
    return response.data.url;
  } catch (error) {
    console.error("Error creating Stripe customer portal session:", error);
    throw error;
  }
};

// Get Stripe publishable key
export const getStripePublishableKey = async (): Promise<string> => {
  try {
    const response = await axios.get<{ publishableKey: string }>(
      `${apiUrl}/stripe/config`
    );
    return response.data.publishableKey;
  } catch (error) {
    console.error("Error getting Stripe publishable key:", error);
    throw error;
  }
};

// Update subscription add-ons (additional users and storage)
// Define the storage tier object structure
export interface StorageTierItem {
  id: string; // Storage tier ID
  size: number; // Size in GB (10, 50, 100)
  quantity: number; // Number of this tier
  price: number; // Price for display purposes
  stripePriceId?: string; // Stripe price ID for this tier
}

export interface UpdateSubscriptionAddonsRequest {
  tenantId: string;
  planId?: string;
  additionalUsers?: number;
  additionalStorageGB?: number; // Kept for backward compatibility
  updateType?: "all" | "users" | "storage" | "plan";
  billingInterval?: "month" | "year";
  stripePriceId?: string; // Direct Stripe price ID for the plan
  stripeUserPriceId?: string; // Direct Stripe price ID for additional users
  stripeStoragePriceId?: string; // Direct Stripe price ID for storage
  storageIncrement?: number; // The size of the storage increment being added (10, 50, or 100 GB)
  storageTiers?: string; // JSON string of storage tiers with quantities (for backward compatibility)
  storageTierItems?: StorageTierItem[]; // Array of storage tier objects
}

export interface UpdateSubscriptionAddonsResponse {
  subscription?: SubscriptionWithPlan;
  portalUrl: string;
}

export const updateSubscriptionAddons = async (
  data: UpdateSubscriptionAddonsRequest
): Promise<UpdateSubscriptionAddonsResponse> => {
  try {
    // Use the unified route for subscription updates
    const response = await axios.post<UpdateSubscriptionAddonsResponse>(
      `${apiUrl}/subscriptions/update-unified`,
      data
    );
    return response.data;
  } catch (error) {
    console.error("Error updating subscription add-ons:", error);
    throw error;
  }
};
