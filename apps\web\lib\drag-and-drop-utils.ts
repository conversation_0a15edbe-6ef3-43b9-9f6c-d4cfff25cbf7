import { DragEndEvent, DragStartEvent, DragOverEvent } from '@dnd-kit/core';

/**
 * Extracts chat ID from a draggable ID
 */
export function getChatIdFromDragId(id: string): string {
  return id.replace('chat-', '');
}

/**
 * Extracts group ID from a droppable ID
 */
export function getGroupIdFromDropId(id: string): string {
  return id.replace('group-', '');
}

/**
 * Checks if a drag operation is valid (chat to group)
 */
export function isValidDragOperation(event: DragEndEvent): boolean {
  const { active, over } = event;
  
  if (!over || !active.data.current || !over.data.current) {
    return false;
  }

  const dragData = active.data.current;
  const dropData = over.data.current;

  return dragData.type === 'chat' && dropData.type === 'group';
}

/**
 * Gets drag operation details from a drag end event
 */
export function getDragOperationDetails(event: DragEndEvent) {
  if (!isValidDragOperation(event)) {
    return null;
  }

  const dragData = event.active.data.current!;
  const dropData = event.over!.data.current!;

  return {
    chatId: dragData.chatId,
    chatTitle: dragData.chatTitle,
    sourceGroupId: dragData.sourceGroupId,
    targetGroupId: dropData.groupId,
  };
}

/**
 * Checks if the drag operation would result in no change
 */
export function isDragOperationNoOp(event: DragEndEvent): boolean {
  const details = getDragOperationDetails(event);
  if (!details) return true;
  
  return details.sourceGroupId === details.targetGroupId;
}

/**
 * Creates a drag data object for a chat item
 */
export function createChatDragData(chatId: string, chatTitle: string, sourceGroupId?: string) {
  return {
    type: 'chat' as const,
    chatId,
    chatTitle,
    sourceGroupId,
  };
}

/**
 * Creates a drop data object for a group
 */
export function createGroupDropData(groupId: string) {
  return {
    type: 'group' as const,
    groupId,
  };
}

/**
 * Generates accessibility announcements for drag start operations
 */
export function getDragStartAnnouncement(event: DragStartEvent, t: (key: string) => string) {
  const dragData = event.active.data.current;
  if (dragData?.type === 'chat') {
    return t('accessibility.dragStart').replace('{chatTitle}', dragData.chatTitle);
  }
  return '';
}

/**
 * Generates accessibility announcements for drag over operations
 */
export function getDragOverAnnouncement(event: DragOverEvent, t: (key: string) => string) {
  const dragData = event.active.data.current;
  const dropData = event.over?.data.current;

  if (dragData?.type === 'chat' && dropData?.type === 'group') {
    return t('accessibility.dragOver')
      .replace('{chatTitle}', dragData.chatTitle)
      .replace('{groupName}', dropData.groupName || 'group');
  }
  return '';
}

/**
 * Generates accessibility announcements for drag end operations
 */
export function getDragEndAnnouncement(event: DragEndEvent, t: (key: string) => string) {
  const details = getDragOperationDetails(event);
  if (details && !isDragOperationNoOp(event)) {
    return t('accessibility.dragEnd')
      .replace('{chatTitle}', details.chatTitle)
      .replace('{groupName}', 'target group');
  }
  return '';
}
