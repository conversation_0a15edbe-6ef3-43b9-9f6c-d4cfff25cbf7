import { PrismaAdapter } from "@next-auth/prisma-adapter";
import { type NextAuthOptions } from "next-auth";
import { type JWT } from "next-auth/jwt";
import CredentialsProvider from "next-auth/providers/credentials";
import { compare } from "bcrypt";
import db from "@/lib/shared-db";
import crypto from "crypto";

// Define custom types for token and cookies
interface ExtendedJWT extends JWT {
  userId?: string;
  role?: string;
  cookies?: {
    currentOrganizationId?: string;
    [key: string]: any;
  };
}

// Define membership type
interface Membership {
  tenantId: string;
  role: string;
  tenant?: any;
}

export const authOptions: NextAuthOptions = {
  adapter: PrismaAdapter(db),
  pages: {
    signIn: "/sign-in",
  },
  secret: process.env.NEXTAUTH_SECRET,
  session: { strategy: "jwt" },
  providers: [
    CredentialsProvider({
      name: "Credentials",
      credentials: {
        username: { label: "Userna<PERSON>", type: "text", placeholder: "jsmith" },
        password: { label: "Password", type: "password" },
      },
      async authorize(credentials) {
        if (!credentials?.username || !credentials?.password) {
          throw new Error("Username and password are required.");
        }

        try {
          // Find user by email
          const emailHash = crypto
            .createHash("sha256")
            .update(credentials.username)
            .digest("hex");
          const user = await db.user.findUnique({
            where: { emailHash },
            select: {
              id: true,
              email: true,
              name: true,
              password: true,
              image: true,
              emailVerified: true,
              membership: {
                include: {
                  tenant: true,
                },
              },
            },
          });

          if (!user) {
            throw new Error("User not found. Please check your email.");
          }

          // Check if the user has any organizations
          if (!user.membership || user.membership.length === 0) {
            throw new Error(
              "You don't have access to any organizations. Please contact an administrator."
            );
          }

          const passwordCorrect = await compare(
            credentials.password,
            user.password
          );

          if (!passwordCorrect) {
            throw new Error("Invalid password. Please try again.");
          }
          if (!user.emailVerified) {
            throw new Error("Email not verified. Please check your email.");
          }
          return {
            id: user.id,
            email: user.email,
            name: user.name,
            image: user.image,
          }; // Success
        } catch (error) {
          console.error("Authorization Error:", error);
          throw new Error(error.message || "Internal Server Error");
        }
      },
    }),
  ],
  logger: {
    error(code, metadata) {
      console.error(code, metadata);
    },
    warn(code) {
      console.warn(code);
    },
    debug(code, metadata) {
      console.debug(code, metadata);
    },
  },
  cookies: {
    sessionToken: {
      name: `${
        process.env.NODE_ENV === "production" ? "__Secure-" : ""
      }next-auth.session-token`,
      options: {
        httpOnly: true,
        sameSite: "lax",
        path: "/",
        secure: process.env.NODE_ENV === "production",
      },
    },
  },
  callbacks: {
    session: async ({ session, token }) => {
      if (session?.user?.email && token?.email) {
        const emailHash = crypto
          .createHash("sha256")
          .update(session?.user?.email)
          .digest("hex");
        const user = await db.user.findUnique({
          where: {
            emailHash,
          },
          include: {
            membership: {
              include: {
                tenant: true,
              },
            },
          },
        });

        if (user?.id) {
          // Add userId to session
          (session as any).userId = user?.id;

          // Add memberships to session
          (session as any).memberships = user?.membership;

          // Get the current organization ID from cookies
          const extendedToken = token as ExtendedJWT;
          const currentOrgId = extendedToken.cookies?.currentOrganizationId;

          // Find the current organization and role
          let currentOrg: any = null;
          let currentRole: string | null = null;

          if (currentOrgId && user.membership.length > 0) {
            // Find the membership for the current organization
            const currentMembership = user.membership.find(
              (m: Membership) => m.tenantId === currentOrgId
            );
            if (currentMembership) {
              currentOrg = currentMembership.tenant;
              currentRole = currentMembership.role;
            }
          }

          // Set current organization if not already set or found
          // Default to first organization if currentOrganization is not set
          if (!currentOrg && user.membership.length > 0) {
            currentOrg = user.membership[0].tenant;
            currentRole = user.membership[0].role;
          }

          // Add current organization and role to session
          if (currentOrg) {
            (session as any).currentOrganization = currentOrg;
            (session.user as any).role = currentRole;
          }
        }
      }

      return { ...session, ...token };
    },

    async signIn({ user }) {
      // If user object exists, authentication was successful in authorize()
      // The authorize() function already checked memberships and email verification
      return !!user;
    },

    async jwt({ token, session }) {
      const extendedToken = token as ExtendedJWT;

      // Get cookies from request if available (only during sign-in)
      if (session?.cookies) {
        extendedToken.cookies = session.cookies;
      }

      if (token?.email) {
        const emailHash = crypto
          .createHash("sha256")
          .update(token?.email)
          .digest("hex");
        const user = await db.user.findUnique({
          where: {
            emailHash,
          },
          select: {
            id: true,
            membership: {
              select: {
                role: true,
                tenantId: true,
              },
            },
          },
        });

        if (user?.id) {
          const extendedToken = token as ExtendedJWT;
          extendedToken.userId = user.id;

          // Add role information to token
          if (user.membership && user.membership.length > 0) {
            const currentOrgId = extendedToken.cookies?.currentOrganizationId;

            // Find the membership for the current organization
            let currentRole: string | undefined = undefined;
            if (currentOrgId) {
              const currentMembership = user.membership.find(
                (m: { tenantId: string }) => m.tenantId === currentOrgId
              );
              if (currentMembership) {
                currentRole = currentMembership.role;
              }
            }

            // Default to first organization's role if not found
            if (!currentRole && user.membership.length > 0) {
              currentRole = user.membership[0].role;
            }

            if (currentRole) {
              extendedToken.role = currentRole;
            }
          }
        }
      }
      return token;
    },
  },
};
