import { NextRequest, NextResponse } from "next/server";
import db from "@/lib/shared-db";
import { validateChatbotSecurity } from "@/lib/security/chatbot-security";
import {
  applyCORSHeaders,
  handleCORSPreflight,
  createChatbotCORSConfig,
} from "@/lib/security/cors";

interface ChatbotConfigResponse {
  id: string;
  name: string;
  description: string;
  customization?: {
    theme?: {
      primaryColor?: string;
      secondaryColor?: string;
      fontFamily?: string;
      borderRadius?: string;
    };
    position?: "bottom-right" | "bottom-left" | "top-right" | "top-left";
    size?: "small" | "medium" | "large";
    greeting?: string;
    placeholder?: string;
    showBranding?: boolean;
  };
  features: {
    searchModes: string[];
    maxTokens: number;
    allowImages: boolean;
    allowFiles: boolean;
  };
  rateLimits: {
    perMinute: number;
    perHour: number;
    perDay: number;
  };
  error?: string;
}

// Validate domain access
async function validateDomainAccess(
  chatbot: any,
  origin: string
): Promise<boolean> {
  // If no allowed domains specified, allow all
  if (!chatbot.allowedDomains || chatbot.allowedDomains.length === 0) {
    return true;
  }

  // Check if origin matches any allowed domain
  try {
    const originDomain = new URL(origin).hostname;
    return chatbot.allowedDomains.some((domain: string) => {
      // Support wildcard subdomains
      if (domain.startsWith("*.")) {
        const baseDomain = domain.slice(2);
        return originDomain.endsWith(baseDomain);
      }
      return originDomain === domain;
    });
  } catch {
    return false;
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: { chatbotId: string } }
) {
  try {
    const { chatbotId } = params;

    // Get API key from Authorization header or query parameter
    let apiKey: string | null = null;

    const authHeader = request.headers.get("authorization");
    if (authHeader && authHeader.startsWith("Bearer ")) {
      apiKey = authHeader.substring(7);
    } else {
      // Fallback to query parameter for easier integration
      const url = new URL(request.url);
      apiKey = url.searchParams.get("apiKey");
    }

    if (!apiKey) {
      const response = NextResponse.json(
        { error: "Missing API key" },
        { status: 401 }
      );
      return applyCORSHeaders(response, request);
    }

    // Validate security and authenticate the request
    const securityResult = await validateChatbotSecurity(
      request,
      chatbotId,
      apiKey
    );

    if (!securityResult.success) {
      // Special handling for private chatbot authentication requirement
      if (
        securityResult.error ===
        "Authentication required for private chatbot access"
      ) {
        // Get basic chatbot info for UI rendering (without full access)
        const basicChatbot = await db.chatbot.findUnique({
          where: { id: chatbotId, apiKey: apiKey },
          select: {
            id: true,
            name: true,
            description: true,
            customization: true,
            searchModes: true,
            access: true,
            isActive: true,
          },
        });

        if (basicChatbot && basicChatbot.isActive) {
          // Parse customization for UI rendering
          let customization: any = {};
          if (basicChatbot.customization) {
            try {
              customization =
                typeof basicChatbot.customization === "string"
                  ? JSON.parse(basicChatbot.customization)
                  : basicChatbot.customization;
            } catch (error) {
              console.error("Error parsing customization JSON:", error);
              customization = {};
            }
          }

          // Return limited config for private chatbot UI rendering
          const limitedConfig: ChatbotConfigResponse = {
            id: basicChatbot.id,
            name: basicChatbot.name,
            description: basicChatbot.description,
            customization: {
              theme: {
                primaryColor: customization.theme?.primaryColor || "#007bff",
                secondaryColor:
                  customization.theme?.secondaryColor || "#6c757d",
                fontFamily:
                  customization.theme?.fontFamily || "Inter, sans-serif",
                borderRadius: customization.theme?.borderRadius || "8px",
              },
              position: customization.position || "bottom-right",
              size: customization.size || "medium",
              greeting:
                customization.greeting ||
                `Hi! I'm ${basicChatbot.name}. How can I help you today?`,
              placeholder: customization.placeholder || "Type your message...",
              showBranding: customization.showBranding !== false,
            },
            features: {
              searchModes: basicChatbot.searchModes || ["internal"],
              maxTokens: 0,
              allowImages: false,
              allowFiles: false,
            },
            rateLimits: {
              perMinute: 0,
              perHour: 0,
              perDay: 0,
            },
            error: "Authentication required for private chatbot access",
          };

          const response = NextResponse.json(limitedConfig);
          return applyCORSHeaders(
            response,
            request,
            createChatbotCORSConfig(basicChatbot)
          );
        }
      }

      // For all other errors, return the original error response
      const response = NextResponse.json(
        { error: securityResult.error },
        { status: securityResult.statusCode || 500 }
      );

      return applyCORSHeaders(response, request, createChatbotCORSConfig({}));
    }

    const { chatbot } = securityResult;

    // Parse customization settings
    let customization: any = {};
    if (chatbot.customization) {
      try {
        customization =
          typeof chatbot.customization === "string"
            ? JSON.parse(chatbot.customization)
            : chatbot.customization;
      } catch (error) {
        console.error("Error parsing customization JSON:", error);
        customization = {};
      }
    }

    // Build configuration response
    const config: ChatbotConfigResponse = {
      id: chatbot.id,
      name: chatbot.name,
      description: chatbot.description,
      customization: {
        theme: {
          primaryColor: customization.theme?.primaryColor || "#007bff",
          secondaryColor: customization.theme?.secondaryColor || "#6c757d",
          fontFamily: customization.theme?.fontFamily || "Inter, sans-serif",
          borderRadius: customization.theme?.borderRadius || "8px",
        },
        position: customization.position || "bottom-right",
        size: customization.size || "medium",
        greeting:
          customization.greeting ||
          `Hi! I'm ${chatbot.name}. How can I help you today?`,
        placeholder: customization.placeholder || "Type your message...",
        showBranding: customization.showBranding !== false, // Default to true
      },
      features: {
        searchModes: chatbot.searchModes || ["internal"],
        maxTokens: chatbot.maxTokens || 4000,
        allowImages: chatbot.searchModes?.includes("multimodal") || false,
        allowFiles: false, // For now, disable file uploads in public chatbots
      },
      rateLimits: {
        perMinute: chatbot.rateLimitPerMinute,
        perHour: chatbot.rateLimitPerHour,
        perDay: chatbot.rateLimitPerDay,
      },
    };

    // Create response with CORS headers
    const response = NextResponse.json(config);
    response.headers.set("Cache-Control", "public, max-age=300"); // Cache for 5 minutes

    return applyCORSHeaders(
      response,
      request,
      createChatbotCORSConfig(chatbot)
    );
  } catch (error) {
    console.error("Error in chatbot config endpoint:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// Handle CORS preflight requests
export async function OPTIONS(request: NextRequest) {
  return handleCORSPreflight(request, {
    allowedMethods: ["GET", "OPTIONS"],
    allowedHeaders: [
      "Content-Type",
      "Authorization",
      "X-Requested-With",
      "X-User-Token",
    ],
    maxAge: 86400,
  });
}
