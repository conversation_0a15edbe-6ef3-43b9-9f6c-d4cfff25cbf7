#!/bin/bash

# Check Node.js version compatibility for Swiss Knowledge Hub

echo "🔍 Checking Node.js version compatibility..."

# Get current Node.js version
CURRENT_VERSION=$(node --version | sed 's/v//')
REQUIRED_VERSION="18.17.0"

echo "Current Node.js version: v$CURRENT_VERSION"
echo "Required Node.js version: >= v$REQUIRED_VERSION"

# Function to compare versions
version_compare() {
    if [[ $1 == $2 ]]; then
        return 0
    fi
    local IFS=.
    local i ver1=($1) ver2=($2)
    # fill empty fields in ver1 with zeros
    for ((i=${#ver1[@]}; i<${#ver2[@]}; i++)); do
        ver1[i]=0
    done
    for ((i=0; i<${#ver1[@]}; i++)); do
        if [[ -z ${ver2[i]} ]]; then
            # fill empty fields in ver2 with zeros
            ver2[i]=0
        fi
        if ((10#${ver1[i]} > 10#${ver2[i]})); then
            return 1
        fi
        if ((10#${ver1[i]} < 10#${ver2[i]})); then
            return 2
        fi
    done
    return 0
}

version_compare $CURRENT_VERSION $REQUIRED_VERSION
case $? in
    0) echo "✅ Node.js version is exactly the required version" ;;
    1) echo "✅ Node.js version is newer than required" ;;
    2) echo "❌ Node.js version is older than required"
       echo ""
       echo "Please upgrade Node.js to version $REQUIRED_VERSION or newer:"
       echo "  nvm install 18.20.8"
       echo "  nvm use 18.20.8"
       echo ""
       echo "Or if you don't have nvm:"
       echo "  Visit https://nodejs.org/ to download the latest version"
       exit 1 ;;
esac

echo "✅ Node.js version check passed!"
