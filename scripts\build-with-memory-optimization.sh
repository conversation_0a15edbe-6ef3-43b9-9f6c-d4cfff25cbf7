#!/bin/bash

# Build script with memory optimization for Swiss Knowledge Hub

echo "🚀 Starting optimized build process..."

# Check Node.js version first
echo "🔍 Checking Node.js version..."
./scripts/check-node-version.sh
if [ $? -ne 0 ]; then
    echo "❌ Node.js version check failed. Please upgrade Node.js and try again."
    exit 1
fi

# Set Node.js memory options
export NODE_OPTIONS="--max-old-space-size=8192"

# Clear any existing build artifacts
echo "🧹 Cleaning previous build artifacts..."
rm -rf apps/web/.next
rm -rf apps/admin/.next
rm -rf .turbo

# Clear node_modules cache if needed (uncomment if build still fails)
# echo "🧹 Clearing node_modules cache..."
# rm -rf node_modules/.cache

# Run the build with memory optimization
echo "🔨 Building applications with memory optimization..."
pnpm build

echo "✅ Build completed successfully!"
