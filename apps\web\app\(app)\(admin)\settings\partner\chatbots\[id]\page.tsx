"use client";

import React, { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

import {
  Bot,
  ArrowLeft,
  Edit,
  Globe,
  Lock,
  Code,
  Monitor,
  Copy,
  ExternalLink,
  BarChart3,
  Users,
  MessageSquare,
  Calendar,
  Settings,
  Trash2,
  Key,
  Shield,
  Eye,
  EyeOff,
  RefreshCw,
  Clock,
  TrendingUp,
  Star,
  Activity,
} from "lucide-react";
import Link from "next/link";
import { useParams, useRouter } from "next/navigation";
import { chatbotService, ChatbotConfig } from "@/services";
import { getCookie } from "@/utils/cookies";
import { toast } from "react-hot-toast";
import { useLanguage } from "@/lib/language-context";
import { useChatbotAccessDetails } from "@/hooks/useChatbotAccess";

type ChatbotType = "web-snippet" | "inline-embedding" | "dedicated-page";

export default function ChatbotDetailsPage() {
  const { t } = useLanguage();
  const params = useParams();
  const router = useRouter();
  const chatbotId = params?.id as string;

  const [chatbot, setChatbot] = useState<ChatbotConfig | null>(null);
  const [loading, setLoading] = useState(true);
  const [copied, setCopied] = useState(false);
  const [showApiKey, setShowApiKey] = useState(false);
  const [analytics, setAnalytics] = useState<any>(null);
  const [analyticsLoading, setAnalyticsLoading] = useState(false);

  // Get access details for private chatbots
  const { details: _accessDetails, loading: _accessLoading } =
    useChatbotAccessDetails(chatbot?.access === "private" ? chatbotId : null);
  const [isRegeneratingKey, setIsRegeneratingKey] = useState(false);
  const [embedCodeType, setEmbedCodeType] = useState<
    "html" | "react" | "vue" | "angular"
  >("html");

  useEffect(() => {
    loadChatbot();
  }, [chatbotId]);

  const loadAnalytics = async () => {
    if (!chatbotId) return;

    setAnalyticsLoading(true);
    try {
      const tenantId = getCookie("currentOrganizationId");
      if (!tenantId) return;

      const response = await fetch(
        `/api/analytics/chatbots/${chatbotId}?tenantId=${tenantId}&timeRange=7d`
      );

      if (response.ok) {
        const data = await response.json();
        setAnalytics(data.analytics);
      }
    } catch (error) {
      console.error("Error loading analytics:", error);
    } finally {
      setAnalyticsLoading(false);
    }
  };

  const loadChatbot = async () => {
    try {
      setLoading(true);
      const tenantId = getCookie("currentOrganizationId");

      if (!tenantId) {
        toast.error("Organization not found");
        router.push("/settings/partner/chatbots");
        return;
      }

      const response = await chatbotService.getChatbot(chatbotId, tenantId);

      if ("error" in response) {
        toast.error(response.error);
        router.push("/settings/partner/chatbots");
        return;
      }

      setChatbot(response.chatbot);
      // Load analytics after chatbot is loaded
      loadAnalytics();
    } catch (error) {
      console.error("Error loading chatbot:", error);
      toast.error("Failed to load chatbot");
      router.push("/settings/partner/chatbots");
    } finally {
      setLoading(false);
    }
  };

  const generateApiKey = async () => {
    if (!chatbot) return;

    try {
      setIsRegeneratingKey(true);
      const tenantId = getCookie("currentOrganizationId");

      if (!tenantId) {
        toast.error("Organization not found");
        return;
      }

      const response = await chatbotService.generateApiKey(chatbotId, tenantId);

      if ("error" in response) {
        toast.error(response.error);
        return;
      }

      // Update the chatbot with the new API key
      setChatbot({ ...chatbot, apiKey: response.apiKey });
      toast.success("API key generated successfully!");
    } catch (error) {
      console.error("Error generating API key:", error);
      toast.error("Failed to generate API key");
    } finally {
      setIsRegeneratingKey(false);
    }
  };

  const regenerateApiKey = async () => {
    if (!chatbot) return;

    try {
      setIsRegeneratingKey(true);
      const tenantId = getCookie("currentOrganizationId");

      if (!tenantId) {
        toast.error("Organization not found");
        return;
      }

      const response = await chatbotService.regenerateApiKey(
        chatbotId,
        tenantId
      );

      if ("error" in response) {
        toast.error(response.error);
        return;
      }

      // Update the chatbot with the new API key
      setChatbot({ ...chatbot, apiKey: response.apiKey });
      toast.success(t("chatbot.apiKeyRegenerated"));
    } catch (error) {
      console.error("Error regenerating API key:", error);
      toast.error(t("chatbot.failedToRegenerateApiKey"));
    } finally {
      setIsRegeneratingKey(false);
    }
  };

  const getTypeIcon = (type: ChatbotConfig["type"]) => {
    switch (type) {
      case "web-snippet":
        return <Code className="h-4 w-4" />;
      case "inline-embedding":
        return <Monitor className="h-4 w-4" />;
      case "dedicated-page":
        return <Globe className="h-4 w-4" />;
    }
  };

  const getTypeLabel = (type: ChatbotType) => {
    switch (type) {
      case "web-snippet":
        return "Web Snippet";
      case "inline-embedding":
        return "Inline Embedding";
      case "dedicated-page":
        return "Dedicated Page";
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  const getEmbedCode = (type: "html" | "react" | "vue" | "angular") => {
    if (!chatbot) return "";
    const baseUrl = window.location.origin;
    const apiKey = chatbot.apiKey;
    const chatbotId = chatbot.id;

    switch (type) {
      case "html":
        return `<!-- Swiss Knowledge Hub Chatbot -->
<script src="${baseUrl}/sdk/chatbot-sdk.js"></script>
<script>
  SwissKnowledgeHub.init({
    chatbotId: '${chatbotId}',
    apiKey: '${apiKey}',
    position: '${chatbot.customization?.position || "bottom-right"}',
    theme: {
      primaryColor: '${
        chatbot.customization?.theme?.primaryColor || "#007bff"
      }',
      fontFamily: '${
        chatbot.customization?.theme?.fontFamily || "Inter, sans-serif"
      }'
    }
  });
</script>`;

      case "react":
        return `// React Component Integration
import { useEffect } from 'react';

export function ChatbotWidget() {
  useEffect(() => {
    const script = document.createElement('script');
    script.src = '${baseUrl}/sdk/chatbot-sdk.js';
    script.onload = () => {
      window.SwissKnowledgeHub.init({
        chatbotId: '${chatbotId}',
        apiKey: '${apiKey}',
        position: '${chatbot.customization?.position || "bottom-right"}'
      });
    };
    document.head.appendChild(script);

    return () => {
      if (window.SwissKnowledgeHub) {
        window.SwissKnowledgeHub.destroyAll();
      }
    };
  }, []);

  return null;
}`;

      case "vue":
        return `<!-- Vue.js Component Integration -->
<template>
  <div></div>
</template>

<script>
export default {
  name: 'ChatbotWidget',
  mounted() {
    this.loadChatbot();
  },
  beforeDestroy() {
    if (window.SwissKnowledgeHub) {
      window.SwissKnowledgeHub.destroyAll();
    }
  },
  methods: {
    loadChatbot() {
      const script = document.createElement('script');
      script.src = '${baseUrl}/sdk/chatbot-sdk.js';
      script.onload = () => {
        window.SwissKnowledgeHub.init({
          chatbotId: '${chatbotId}',
          apiKey: '${apiKey}',
          position: '${chatbot.customization?.position || "bottom-right"}'
        });
      };
      document.head.appendChild(script);
    }
  }
}
</script>`;

      case "angular":
        return `// Angular Component Integration
import { Component, OnInit, OnDestroy } from '@angular/core';

@Component({
  selector: 'app-chatbot',
  template: ''
})
export class ChatbotComponent implements OnInit, OnDestroy {

  ngOnInit() {
    this.loadChatbot();
  }

  ngOnDestroy() {
    if ((window as any).SwissKnowledgeHub) {
      (window as any).SwissKnowledgeHub.destroyAll();
    }
  }

  private loadChatbot() {
    const script = document.createElement('script');
    script.src = '${baseUrl}/sdk/chatbot-sdk.js';
    script.onload = () => {
      (window as any).SwissKnowledgeHub.init({
        chatbotId: '${chatbotId}',
        apiKey: '${apiKey}',
        position: '${chatbot.customization?.position || "bottom-right"}'
      });
    };
    document.head.appendChild(script);
  }
}`;

      default:
        return "";
    }
  };

  const getInlineEmbedCode = (type: "html" | "react" | "vue" | "angular") => {
    if (!chatbot) return "";
    const baseUrl = window.location.origin;
    const apiKey = chatbot.apiKey;
    const chatbotId = chatbot.id;

    switch (type) {
      case "html":
        return `<!-- Swiss Knowledge Hub Inline Chatbot -->
<div id="chatbot-container" style="width: 100%; height: 500px;"></div>
<script src="${baseUrl}/sdk/chatbot-sdk.js"></script>
<script>
  SwissKnowledgeHub.init({
    chatbotId: '${chatbotId}',
    apiKey: '${apiKey}',
    mode: 'inline',
    container: '#chatbot-container',
    theme: {
      primaryColor: '${
        chatbot.customization?.theme?.primaryColor || "#007bff"
      }',
      fontFamily: '${
        chatbot.customization?.theme?.fontFamily || "Inter, sans-serif"
      }'
    }
  });
</script>`;

      case "react":
        return `// React Inline Chatbot Component
import { useEffect, useRef, useState } from 'react';

// Type declaration for the chatbot SDK
declare global {
  interface Window {
    SwissKnowledgeHub: {
      init: (config: any) => any;
      destroyAll: () => void;
    };
  }
}

export function InlineChatbot() {
  const containerRef = useRef(null);
  const [isLoaded, setIsLoaded] = useState(false);
  const instanceRef = useRef(null);

  useEffect(() => {
    // Prevent multiple initializations
    if (isLoaded || instanceRef.current) {
      return;
    }

    // Check if script is already loaded
    const existingScript = document.querySelector(
      'script[src*="chatbot-sdk.js"]'
    );

    const initializeChatbot = () => {
      if (
        window.SwissKnowledgeHub &&
        containerRef.current &&
        !instanceRef.current
      ) {
        try {
          // Destroy any existing instances first
          window.SwissKnowledgeHub.destroyAll();

          // Initialize new instance
          const instance = window.SwissKnowledgeHub.init({
            chatbotId: '${chatbotId}',
            apiKey: '${apiKey}',
            mode: 'inline',
            container: containerRef.current,
            theme: {
              primaryColor: '${
                chatbot.customization?.theme?.primaryColor || "#007bff"
              }',
              fontFamily: '${
                chatbot.customization?.theme?.fontFamily || "Inter, sans-serif"
              }'
            }
          });

          instanceRef.current = instance;
          setIsLoaded(true);
          console.log("Chatbot initialized successfully");
        } catch (error) {
          console.error("Failed to initialize chatbot:", error);
        }
      }
    };

    if (existingScript) {
      // Script already exists, just initialize
      initializeChatbot();
    } else {
      // Load script first
      const script = document.createElement('script');
      script.src = '${baseUrl}/sdk/chatbot-sdk.js';
      script.onload = initializeChatbot;
      script.onerror = () => {
        console.error("Failed to load chatbot SDK");
      };
      document.head.appendChild(script);
    }

    return () => {
      // Cleanup on unmount
      if (instanceRef.current) {
        try {
          if (window.SwissKnowledgeHub) {
            window.SwissKnowledgeHub.destroyAll();
          }
          instanceRef.current = null;
          setIsLoaded(false);
          console.log("Chatbot cleaned up");
        } catch (error) {
          console.error("Error during cleanup:", error);
        }
      }
    };
  }, []); // Empty dependency array to run only once

  return (
    <div style={{ height: "100vh" }}>
      <div
        ref={containerRef}
        style={{
          width: "100%",
          height: "100%",
          border: "1px solid #ddd",
          borderRadius: "8px",
          backgroundColor: "#f9f9f9",
        }}
      />
      {!isLoaded && (
        <div style={{ marginTop: "10px", color: "#666" }}>
          Loading chatbot...
        </div>
      )}
    </div>
  );
}
`;

      case "vue":
        return `<!-- Vue.js Inline Chatbot Component -->
<template>
  <div style="height: 100vh">
    <div
      ref="chatbotContainer"
      class="chatbot-container"
      :style="{
        width: '100%',
        height: '100%',
        border: '1px solid #ddd',
        borderRadius: '8px',
        backgroundColor: '#f9f9f9'
      }"
    ></div>
    <div v-if="!isLoaded" style="margin-top: 10px; color: #666;">
      Loading chatbot...
    </div>
  </div>
</template>

<script>
export default {
  name: 'InlineChatbot',
  data() {
    return {
      isLoaded: false,
      chatbotInstance: null
    };
  },
  mounted() {
    this.loadChatbot();
  },
  beforeDestroy() {
    this.cleanup();
  },
  methods: {
    loadChatbot() {
      // Prevent multiple initializations
      if (this.isLoaded || this.chatbotInstance) {
        return;
      }

      // Check if script is already loaded
      const existingScript = document.querySelector('script[src*="chatbot-sdk.js"]');

      const initializeChatbot = () => {
        if (window.SwissKnowledgeHub && this.$refs.chatbotContainer && !this.chatbotInstance) {
          try {
            // Destroy any existing instances first
            window.SwissKnowledgeHub.destroyAll();

            // Initialize new instance
            const instance = window.SwissKnowledgeHub.init({
              chatbotId: '${chatbotId}',
              apiKey: '${apiKey}',
              mode: 'inline',
              container: this.$refs.chatbotContainer,
              theme: {
                primaryColor: '${
                  chatbot.customization?.theme?.primaryColor || "#007bff"
                }',
                fontFamily: '${
                  chatbot.customization?.theme?.fontFamily ||
                  "Inter, sans-serif"
                }'
              }
            });

            this.chatbotInstance = instance;
            this.isLoaded = true;
            console.log("Chatbot initialized successfully");
          } catch (error) {
            console.error("Failed to initialize chatbot:", error);
          }
        }
      };

      if (existingScript) {
        // Script already exists, just initialize
        initializeChatbot();
      } else {
        // Load script first
        const script = document.createElement('script');
        script.src = '${baseUrl}/sdk/chatbot-sdk.js';
        script.onload = initializeChatbot;
        script.onerror = () => {
          console.error("Failed to load chatbot SDK");
        };
        document.head.appendChild(script);
      }
    },

    cleanup() {
      if (this.chatbotInstance) {
        try {
          if (window.SwissKnowledgeHub) {
            window.SwissKnowledgeHub.destroyAll();
          }
          this.chatbotInstance = null;
          this.isLoaded = false;
          console.log("Chatbot cleaned up");
        } catch (error) {
          console.error("Error during cleanup:", error);
        }
      }
    }
  }
}
</script>

<style scoped>
.chatbot-container {
  transition: all 0.3s ease;
}
</style>`;

      case "angular":
        return `// Angular Inline Chatbot Component
import { Component, OnInit, OnDestroy, ElementRef, ViewChild } from '@angular/core';

@Component({
  selector: 'app-inline-chatbot',
  template: \`
    <div style="height: 100vh">
      <div
        #chatbotContainer
        class="chatbot-container"
        [style.width]="'100%'"
        [style.height]="'100%'"
        [style.border]="'1px solid #ddd'"
        [style.border-radius]="'8px'"
        [style.background-color]="'#f9f9f9'"
      ></div>
      <div *ngIf="!isLoaded" style="margin-top: 10px; color: #666;">
        Loading chatbot...
      </div>
    </div>
  \`,
  styles: [\`
    .chatbot-container {
      transition: all 0.3s ease;
    }
  \`]
})
export class InlineChatbotComponent implements OnInit, OnDestroy {
  @ViewChild('chatbotContainer', { static: true })
  chatbotContainer!: ElementRef;

  isLoaded = false;
  chatbotInstance: any = null;

  ngOnInit() {
    this.loadChatbot();
  }

  ngOnDestroy() {
    this.cleanup();
  }

  private loadChatbot() {
    // Prevent multiple initializations
    if (this.isLoaded || this.chatbotInstance) {
      return;
    }

    // Check if script is already loaded
    const existingScript = document.querySelector('script[src*="chatbot-sdk.js"]');

    const initializeChatbot = () => {
      if ((window as any).SwissKnowledgeHub && this.chatbotContainer?.nativeElement && !this.chatbotInstance) {
        try {
          // Destroy any existing instances first
          (window as any).SwissKnowledgeHub.destroyAll();

          // Initialize new instance
          const instance = (window as any).SwissKnowledgeHub.init({
            chatbotId: '${chatbotId}',
            apiKey: '${apiKey}',
            mode: 'inline',
            container: this.chatbotContainer.nativeElement,
            theme: {
              primaryColor: '${
                chatbot.customization?.theme?.primaryColor || "#007bff"
              }',
              fontFamily: '${
                chatbot.customization?.theme?.fontFamily || "Inter, sans-serif"
              }'
            }
          });

          this.chatbotInstance = instance;
          this.isLoaded = true;
          console.log("Chatbot initialized successfully");
        } catch (error) {
          console.error("Failed to initialize chatbot:", error);
        }
      }
    };

    if (existingScript) {
      // Script already exists, just initialize
      initializeChatbot();
    } else {
      // Load script first
      const script = document.createElement('script');
      script.src = '${baseUrl}/sdk/chatbot-sdk.js';
      script.onload = initializeChatbot;
      script.onerror = () => {
        console.error("Failed to load chatbot SDK");
      };
      document.head.appendChild(script);
    }
  }

  private cleanup() {
    if (this.chatbotInstance) {
      try {
        if ((window as any).SwissKnowledgeHub) {
          (window as any).SwissKnowledgeHub.destroyAll();
        }
        this.chatbotInstance = null;
        this.isLoaded = false;
        console.log("Chatbot cleaned up");
      } catch (error) {
        console.error("Error during cleanup:", error);
      }
    }
  }
}`;

      default:
        return "";
    }
  };

  const handleDelete = () => {
    if (
      confirm(
        "Are you sure you want to delete this chatbot? This action cannot be undone."
      )
    ) {
      // In real app, make API call to delete
      router.push("/settings/partner/chatbots");
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-center">
          <Bot className="h-12 w-12 mx-auto mb-4 animate-pulse" />
          <p>{t("chatbot.loadingChatbotDetails")}</p>
        </div>
      </div>
    );
  }

  if (!chatbot) {
    return (
      <div className="text-center py-12">
        <Bot className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
        <h3 className="text-lg font-semibold mb-2">
          {t("chatbot.chatbotNotFound")}
        </h3>
        <p className="text-muted-foreground mb-4">
          {t("chatbot.chatbotNotFoundDescription")}
        </p>
        <Button asChild>
          <Link href="/settings/partner/chatbots">
            <ArrowLeft className="mr-2 h-4 w-4" />
            {t("chatbot.backToChatbots")}
          </Link>
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Link href="/settings/partner/chatbots">
            <Button variant="ghost" size="sm">
              <ArrowLeft className="mr-2 h-4 w-4" />
              {t("chatbot.backToChatbots")}
            </Button>
          </Link>
          <div>
            <h1 className="text-2xl font-bold flex items-center gap-2">
              <Bot className="h-6 w-6" />
              {chatbot.name}
            </h1>
            <p className="text-muted-foreground">{chatbot.description}</p>
          </div>
        </div>
        <div className="flex gap-2">
          <Link href={`/settings/partner/chatbots/${chatbot.id}/edit`}>
            <Button variant="outline">
              <Edit className="mr-2 h-4 w-4" />
              {t("chatbot.edit")}
            </Button>
          </Link>
          <Button variant="destructive" onClick={handleDelete}>
            <Trash2 className="mr-2 h-4 w-4" />
            {t("chatbot.delete")}
          </Button>
        </div>
      </div>

      {/* Status and Basic Info */}
      <Card>
        <CardContent className="pt-6">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div>
              <Label className="text-sm font-medium text-muted-foreground">
                {t("chatbot.status")}
              </Label>
              <div className="mt-1">
                <Badge
                  variant={chatbot.isActive ? "default" : "secondary"}
                  className={
                    chatbot.isActive
                      ? "bg-green-100 text-green-800"
                      : "bg-gray-100 text-gray-800"
                  }
                >
                  {chatbot.isActive ? t("common.active") : t("common.inactive")}
                </Badge>
              </div>
            </div>
            <div>
              <Label className="text-sm font-medium text-muted-foreground">
                {t("chatbot.type")}
              </Label>
              <div className="flex items-center gap-2 mt-1">
                {getTypeIcon(chatbot.type)}
                <span className="text-sm">{getTypeLabel(chatbot.type)}</span>
              </div>
            </div>
            <div>
              <Label className="text-sm font-medium text-muted-foreground">
                {t("chatbot.access")}
              </Label>
              <div className="flex items-center gap-2 mt-1">
                {chatbot.access === "public" ? (
                  <Globe className="h-4 w-4" />
                ) : (
                  <Lock className="h-4 w-4" />
                )}
                <span className="text-sm capitalize">{chatbot.access}</span>
              </div>
            </div>
            <div>
              <Label className="text-sm font-medium text-muted-foreground">
                {t("chatbot.created")}
              </Label>
              <div className="flex items-center gap-1 mt-1">
                <Calendar className="h-4 w-4" />
                <span className="text-sm">
                  {chatbot.createdAt
                    ? new Date(chatbot.createdAt).toLocaleDateString()
                    : "N/A"}
                </span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Tabs */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">{t("chatbot.overview")}</TabsTrigger>
          <TabsTrigger value="analytics">{t("chatbot.analytics")}</TabsTrigger>
          <TabsTrigger value="integration">
            {t("chatbot.integration")}
          </TabsTrigger>
          <TabsTrigger value="api-keys">{t("chatbot.apiKeys")}</TabsTrigger>
          <TabsTrigger value="security">{t("chatbot.security")}</TabsTrigger>
          <TabsTrigger value="access">
            {t("chatbot.accessManagement")}
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          {/* Quick Stats */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center gap-2">
                  <MessageSquare className="h-5 w-5 text-blue-600" />
                  <div>
                    <p className="text-2xl font-bold">
                      {analyticsLoading ? (
                        <div className="animate-pulse h-8 bg-muted rounded w-16"></div>
                      ) : (
                        (analytics?.totalSessions || 0).toLocaleString()
                      )}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      {t("chatbot.totalSessions")}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5 text-green-600" />
                  <div>
                    <p className="text-2xl font-bold">
                      {analyticsLoading ? (
                        <div className="animate-pulse h-8 bg-muted rounded w-16"></div>
                      ) : (
                        (analytics?.totalMessages || 0).toLocaleString()
                      )}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      {t("chatbot.totalMessages")}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center gap-2">
                  <Users className="h-5 w-5 text-purple-600" />
                  <div>
                    <p className="text-2xl font-bold">
                      {analyticsLoading ? (
                        <div className="animate-pulse h-8 bg-muted rounded w-16"></div>
                      ) : analytics?.averageResponseTime ? (
                        `${analytics.averageResponseTime.toFixed(1)}s`
                      ) : (
                        "N/A"
                      )}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      {t("chatbot.averageResponseTime")}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Configuration Details */}
          <Card>
            <CardHeader>
              <CardTitle>{t("chatbot.configurationDetails")}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label className="text-sm font-medium">
                  {t("common.name")}
                </Label>
                <p className="text-sm text-muted-foreground mt-1">
                  {chatbot.name}
                </p>
              </div>
              <div>
                <Label className="text-sm font-medium">
                  {t("common.description")}
                </Label>
                <p className="text-sm text-muted-foreground mt-1">
                  {chatbot.description}
                </p>
              </div>
              <Separator />
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium">
                    {t("common.createdAt")}
                  </Label>
                  <p className="text-sm text-muted-foreground mt-1">
                    {chatbot.updatedAt
                      ? new Date(chatbot.updatedAt).toLocaleDateString()
                      : "N/A"}
                  </p>
                </div>
                <div>
                  <Label className="text-sm font-medium">
                    {t("common.updatedAt")}
                  </Label>
                  <p className="text-sm text-muted-foreground mt-1">
                    {analyticsLoading
                      ? t("chatbot.loading")
                      : analytics?.lastActivity
                        ? new Date(analytics.lastActivity).toLocaleDateString()
                        : t("chatbot.noRecentActivity")}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          {/* Analytics Header */}
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold">
                {t("chatbot.analyticsAndMonitoring")}
              </h3>
              <p className="text-sm text-muted-foreground">
                {t("chatbot.monitorPerformance")}
              </p>
            </div>
            <Button variant="outline" size="sm" onClick={loadAnalytics}>
              <RefreshCw className="h-4 w-4 mr-2" />
              {t("chatbot.refresh")}
            </Button>
          </div>

          {analyticsLoading ? (
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              {[...Array(4)].map((_, i) => (
                <Card key={i}>
                  <CardContent className="p-3">
                    <div className="animate-pulse space-y-2">
                      <div className="h-4 bg-muted rounded w-3/4"></div>
                      <div className="h-8 bg-muted rounded w-1/2"></div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <>
              {/* Key Metrics */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <Card>
                  <CardContent className="p-3">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">
                          {t("chatbot.totalSessions")}
                        </p>
                        <p className="text-2xl font-bold">
                          {analytics?.totalSessions || 0}
                        </p>
                      </div>
                      <MessageSquare className="h-8 w-8 text-blue-500" />
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-3">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">
                          {t("chatbot.totalMessages")}
                        </p>
                        <p className="text-2xl font-bold">
                          {analytics?.totalMessages || 0}
                        </p>
                      </div>
                      <Activity className="h-8 w-8 text-green-500" />
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-3">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">
                          {t("chatbot.avgResponseTime")}
                        </p>
                        <p className="text-2xl font-bold">
                          {analytics?.averageResponseTime
                            ? `${analytics.averageResponseTime.toFixed(1)}s`
                            : "N/A"}
                        </p>
                      </div>
                      <Clock className="h-8 w-8 text-orange-500" />
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-3">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">
                          {t("chatbot.activeUsers")}
                        </p>
                        <p className="text-2xl font-bold">
                          {analytics?.activeUsers || 0}
                        </p>
                      </div>
                      <Users className="h-8 w-8 text-purple-500" />
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Performance & Satisfaction */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <TrendingUp className="h-5 w-5" />
                      {t("chatbot.performanceOverview")}
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span className="text-sm">{t("chatbot.errorRate")}</span>
                      <div className="flex items-center gap-2">
                        <div
                          className={`w-2 h-2 rounded-full ${
                            (analytics?.errorRate || 0) < 5
                              ? "bg-green-500"
                              : (analytics?.errorRate || 0) < 15
                                ? "bg-yellow-500"
                                : "bg-red-500"
                          }`}
                        ></div>
                        <span className="font-medium">
                          {(analytics?.errorRate || 0).toFixed(1)}%
                        </span>
                      </div>
                    </div>
                    {/* <div className="flex justify-between items-center">
                      <span className="text-sm">Avg Session Duration</span>
                      <span className="font-medium">
                        {analytics?.averageSessionDuration
                          ? `${Math.round(analytics.averageSessionDuration)}s`
                          : "N/A"}
                      </span>
                    </div> */}
                    <div className="flex justify-between items-center">
                      <span className="text-sm">
                        {t("chatbot.lastActivity")}
                      </span>
                      <span className="font-medium">
                        {analytics?.lastActivity &&
                        analytics.lastActivity !== "Never"
                          ? new Date(
                              analytics.lastActivity
                            ).toLocaleDateString()
                          : t("chatbot.never")}
                      </span>
                    </div>
                  </CardContent>
                </Card>

                {/* <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Star className="h-5 w-5" />
                      User Satisfaction
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span className="text-sm">Average Rating</span>
                      <div className="flex items-center gap-1">
                        {analytics?.averageSatisfaction ? (
                          <>
                            <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                            <span className="font-medium">
                              {analytics.averageSatisfaction.toFixed(1)}
                            </span>
                          </>
                        ) : (
                          <span className="font-medium text-muted-foreground">
                            N/A
                          </span>
                        )}
                      </div>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm">Total Feedback</span>
                      <span className="font-medium">
                        {analytics?.recentSessions?.filter(
                          (s: any) => s.satisfactionRating
                        ).length || 0}
                      </span>
                    </div>
                  </CardContent>
                </Card> */}
              </div>

              {/* Geographic & Device Distribution */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Card>
                  <CardHeader>
                    <CardTitle>{t("chatbot.geographicDistribution")}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      {analytics?.countries
                        ?.slice(0, 5)
                        .map((country: any, index: number) => (
                          <div
                            key={country.country}
                            className="flex justify-between items-center"
                          >
                            <span className="text-sm">
                              {index + 1}. {country.country}
                            </span>
                            <span className="font-medium">{country.count}</span>
                          </div>
                        )) || (
                        <p className="text-sm text-muted-foreground">
                          {t("chatbot.noDataAvailable")}
                        </p>
                      )}
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>{t("chatbot.deviceTypes")}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      {analytics?.devices?.map((device: any, index: number) => (
                        <div
                          key={device.device}
                          className="flex justify-between items-center"
                        >
                          <span className="text-sm">
                            {index + 1}. {device.device}
                          </span>
                          <span className="font-medium">{device.count}</span>
                        </div>
                      )) || (
                        <p className="text-sm text-muted-foreground">
                          {t("chatbot.noDataAvailable")}
                        </p>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </div>
            </>
          )}
        </TabsContent>

        <TabsContent value="integration" className="space-y-4">
          {/* Integration Instructions */}
          {chatbot.type === "web-snippet" && (
            <Card>
              <CardHeader>
                <CardTitle className="p-2">
                  {t("chatbot.webSnippetIntegration")}
                </CardTitle>
              </CardHeader>
              <CardContent className="px-4">
                <div>
                  <p className="text-sm text-muted-foreground mt-1">
                    {t("chatbot.chooseIntegrationMethod")}
                  </p>

                  <div className="flex gap-2 mt-3">
                    {(["html", "react", "vue", "angular"] as const).map(
                      (type) => (
                        <Button
                          key={type}
                          size="sm"
                          variant={
                            embedCodeType === type ? "default" : "outline"
                          }
                          onClick={() => setEmbedCodeType(type)}
                        >
                          {type.charAt(0).toUpperCase() + type.slice(1)}
                        </Button>
                      )
                    )}
                  </div>

                  <div className="relative mt-3">
                    <Textarea
                      value={getEmbedCode(embedCodeType)}
                      readOnly
                      className="font-mono text-xs"
                      rows={embedCodeType === "html" ? 8 : 15}
                    />
                    <Button
                      size="sm"
                      variant="outline"
                      className="absolute top-2 right-2"
                      onClick={() =>
                        copyToClipboard(getEmbedCode(embedCodeType))
                      }
                    >
                      <Copy className="h-4 w-4" />
                      {copied ? t("chatbot.copied") : t("chatbot.copy")}
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {chatbot.type === "inline-embedding" && (
            <Card>
              <CardHeader>
                <CardTitle className="">
                  {t("chatbot.inlineEmbeddingIntegration")}
                </CardTitle>
              </CardHeader>
              <CardContent className="px-4">
                <div>
                  <p className="text-sm text-muted-foreground mt-1">
                    {t("chatbot.embedDirectly")}
                  </p>

                  <div className="flex gap-2 mt-3">
                    {(["html", "react", "vue", "angular"] as const).map(
                      (type) => (
                        <Button
                          key={type}
                          size="sm"
                          variant={
                            embedCodeType === type ? "default" : "outline"
                          }
                          onClick={() => setEmbedCodeType(type)}
                        >
                          {type.charAt(0).toUpperCase() + type.slice(1)}
                        </Button>
                      )
                    )}
                  </div>

                  <div className="relative mt-3">
                    <Textarea
                      value={getInlineEmbedCode(embedCodeType)}
                      readOnly
                      className="font-mono text-xs"
                      rows={embedCodeType === "html" ? 12 : 18}
                    />
                    <Button
                      size="sm"
                      variant="outline"
                      className="absolute top-2 right-2"
                      onClick={() =>
                        copyToClipboard(getInlineEmbedCode(embedCodeType))
                      }
                    >
                      <Copy className="h-4 w-4" />
                      {copied ? t("chatbot.copied") : t("chatbot.copy")}
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {chatbot.type === "dedicated-page" && (
            <Card>
              <CardHeader>
                <CardTitle>{t("chatbot.dedicatedPageIntegration")}</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <p className="text-sm text-muted-foreground">
                  {t("chatbot.dedicatedPageIntegrationDescription")}
                </p>
                <div className="flex items-center gap-2">
                  <code className="flex-1 p-2 bg-muted rounded text-sm">
                    {`${window.location.origin}/chat/${chatbot.id}`}
                  </code>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() =>
                      copyToClipboard(
                        `${window.location.origin}/chat/${chatbot.id}`
                      )
                    }
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                  <Button size="sm" variant="outline" asChild>
                    <a
                      href={`${window.location.origin}/chat/${chatbot.id}`}
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      <ExternalLink className="h-4 w-4" />
                    </a>
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="api-keys" className="space-y-4">
          {/* API Key Management */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Key className="h-5 w-5" />
                {t("chatbot.apiKeyManagement")}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {chatbot.apiKey ? (
                <div>
                  <Label className="text-sm font-medium">
                    {t("chatbot.currentApiKey")}
                  </Label>
                  <div className="flex items-center gap-2 mt-2">
                    <code className="flex-1 p-2 bg-muted rounded text-sm font-mono">
                      {showApiKey
                        ? chatbot.apiKey
                        : chatbot.apiKey.substring(0, 8) +
                          "••••••••••••••••••••••••••••••••"}
                    </code>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => setShowApiKey(!showApiKey)}
                    >
                      {showApiKey ? (
                        <EyeOff className="h-4 w-4" />
                      ) : (
                        <Eye className="h-4 w-4" />
                      )}
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => copyToClipboard(chatbot.apiKey || "")}
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                  </div>
                  <p className="text-xs text-muted-foreground mt-1">
                    {t("chatbot.apiKeyDescription")}
                  </p>
                </div>
              ) : (
                <div>
                  <Label className="text-sm font-medium">
                    {t("chatbot.generateApiKey")}
                  </Label>
                  <p className="text-sm text-muted-foreground mt-1 mb-3">
                    {t("chatbot.generateApiKeyDescription")}
                  </p>
                  <Button
                    onClick={generateApiKey}
                    disabled={isRegeneratingKey}
                    className="w-full"
                  >
                    {isRegeneratingKey ? (
                      <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    ) : (
                      <Key className="h-4 w-4 mr-2" />
                    )}
                    {isRegeneratingKey
                      ? t("chatbot.regenerating")
                      : t("chatbot.regenerateApiKey")}
                  </Button>
                </div>
              )}

              <Separator />

              {chatbot.apiKey && (
                <div>
                  <Label className="text-sm font-medium">
                    {t("chatbot.regenerateApiKey")}
                  </Label>
                  <p className="text-sm text-muted-foreground mt-1">
                    {t("chatbot.regenerateApiKeyDescription")}
                  </p>
                  <Button
                    variant="destructive"
                    size="sm"
                    className="mt-2"
                    onClick={regenerateApiKey}
                    disabled={isRegeneratingKey}
                  >
                    {isRegeneratingKey ? (
                      <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    ) : (
                      <RefreshCw className="h-4 w-4 mr-2" />
                    )}
                    {isRegeneratingKey
                      ? t("chatbot.regenerating")
                      : t("chatbot.regenerated")}
                  </Button>
                </div>
              )}

              <Separator />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="security" className="space-y-4">
          {/* Security Settings */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                {t("chatbot.securityAndRateLimiting")}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label className="text-sm font-medium">
                  {t("chatbot.allowedDomains")}
                </Label>
                <p className="text-sm text-muted-foreground mt-1">
                  {t("chatbot.allowedDomainsDescription")}
                </p>
                <div className="flex flex-wrap gap-2 mt-2">
                  {(chatbot.allowedDomains || []).map((domain, index) => (
                    <Badge key={index} variant="secondary">
                      {domain}
                    </Badge>
                  ))}
                </div>
              </div>

              <Separator />

              <div>
                <Label className="text-sm font-medium">
                  {t("chatbot.rateLimits")}
                </Label>
                <p className="text-sm text-muted-foreground mt-1">
                  {t("chatbot.rateLimitsDescription")}
                </p>
                <div className="grid grid-cols-3 gap-4 mt-3">
                  <div className="text-center p-3 bg-muted rounded">
                    <div className="text-2xl font-bold text-blue-600">
                      {chatbot.rateLimitPerMinute || 60}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {t("chatbot.perMinute")}
                    </div>
                  </div>
                  <div className="text-center p-3 bg-muted rounded">
                    <div className="text-2xl font-bold text-green-600">
                      {chatbot.rateLimitPerHour || 1000}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {t("chatbot.perHour")}
                    </div>
                  </div>
                  <div className="text-center p-3 bg-muted rounded">
                    <div className="text-2xl font-bold text-purple-600">
                      {chatbot.rateLimitPerDay || 10000}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {t("chatbot.perDay")}
                    </div>
                  </div>
                </div>
              </div>

              <Separator />

              <div>
                <Label className="text-sm font-medium">
                  {t("chatbot.llmScopeAndSearchModes")}
                </Label>
                <p className="text-sm text-muted-foreground mt-1">
                  {t("chatbot.configureWhatDataSourcesThisChatbotCanAccess")}
                </p>
                <div className="flex flex-wrap gap-2 mt-2">
                  {(chatbot.llmScope || []).map((scope, index) => (
                    <Badge key={index} variant="outline">
                      {scope.replace("_", " ")}
                    </Badge>
                  ))}
                </div>
                <div className="flex flex-wrap gap-2 mt-2">
                  {(chatbot.searchModes || []).map((mode, index) => (
                    <Badge key={index} variant="secondary">
                      {mode} {t("chatbot.searchMode")}
                    </Badge>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="access" className="space-y-4">
          {/* Access Management */}
          <Card>
            <CardHeader>
              <CardTitle>{t("chatbot.accessManagement")}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label className="text-sm font-medium">
                  {t("chatbot.accessType")}
                </Label>
                <div className="flex items-center gap-2 mt-1">
                  {chatbot.access === "public" ? (
                    <Globe className="h-4 w-4" />
                  ) : (
                    <Lock className="h-4 w-4" />
                  )}
                  <span className="text-sm capitalize">{chatbot.access}</span>
                </div>
              </div>

              {(chatbot.access as "public" | "private") === "private" &&
                chatbot.allowedUsers.length > 0 && (
                  <div>
                    <Label className="text-sm font-medium">
                      {t("chatbot.allowedCompanies", {
                        count: chatbot.allowedUsers.length.toString(),
                      })}
                    </Label>
                    <div className="flex flex-wrap gap-2 mt-2">
                      {chatbot.allowedUsers.map((user, index) => (
                        <Badge key={index} variant="secondary">
                          {user}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}

              <div className="pt-4">
                <Link href={`/settings/partner/chatbots/${chatbot.id}/edit`}>
                  <Button variant="outline">
                    <Settings className="mr-2 h-4 w-4" />
                    {t("chatbot.modifyAccessSettings")}
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
