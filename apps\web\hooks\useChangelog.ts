"use client";

import { useState, useEffect, useCallback } from "react";
import { useSession } from "next-auth/react";
import toast from "react-hot-toast";

interface Changelog {
  id: string;
  title: string;
  content: string;
  version?: string;
  type: "RELEASE" | "HOTFIX" | "MAINTENANCE" | "ANNOUNCEMENT";
  priority: "LOW" | "MEDIUM" | "HIGH" | "CRITICAL";
  publishedAt: string;
  githubCommitSha?: string;
  deploymentId?: string;
}

interface UseChangelogReturn {
  changelogs: Changelog[];
  isLoading: boolean;
  error: string | null;
  showModal: boolean;
  setShowModal: (show: boolean) => void;
  markAsViewed: (
    changelogId: string,
    dismissed?: boolean,
    removeFromList?: boolean
  ) => Promise<void>;
  markMultipleAsViewed: (
    changelogIds: string[],
    dismissed?: boolean
  ) => Promise<void>;
  removeViewedChangelogs: (changelogIds: string[]) => void;
  refreshChangelogs: () => Promise<void>;
}

export const useChangelog = (tenantId?: string): UseChangelogReturn => {
  const { data: session }: any = useSession();
  const [changelogs, setChangelogs] = useState<Changelog[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showModal, setShowModal] = useState(false);

  const fetchChangelogs = useCallback(async () => {
    if (!session?.userId || !tenantId) {
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const environment = process.env.NODE_ENV;
      const url = `/api/changelog?tenantId=${tenantId}&environment=${environment}`;

      const response = await fetch(url, {
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch changelogs: ${response.statusText}`);
      }

      const data = await response.json();
      const fetchedChangelogs = data.data || [];

      setChangelogs(fetchedChangelogs);

      // Show modal if there are unviewed changelogs
      if (fetchedChangelogs.length > 0) {
        setShowModal(true);
      }
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "Failed to fetch changelogs";
      setError(errorMessage);
      console.error("Error fetching changelogs:", err);
    } finally {
      setIsLoading(false);
    }
  }, [session?.userId, tenantId]);

  const markAsViewed = useCallback(
    async (changelogId: string, dismissed = false, removeFromList = false) => {
      try {
        const response = await fetch("/api/changelog/view", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            changelogId,
            dismissed,
          }),
        });

        if (!response.ok) {
          throw new Error(
            `Failed to mark changelog as viewed: ${response.statusText}`
          );
        }

        // Only remove from list if explicitly requested (when modal is closing)
        if (removeFromList) {
          setChangelogs((prev) => prev.filter((c) => c.id !== changelogId));

          // If no more changelogs, hide modal
          if (changelogs.length <= 1) {
            setShowModal(false);
          }
        }
      } catch (err) {
        const errorMessage =
          err instanceof Error
            ? err.message
            : "Failed to mark changelog as viewed";
        console.error("Error marking changelog as viewed:", err);
        toast.error(errorMessage);
      }
    },
    [changelogs.length]
  );

  const markMultipleAsViewed = useCallback(
    async (changelogIds: string[], dismissed = false) => {
      try {
        // Mark all changelogs as viewed in parallel
        await Promise.all(
          changelogIds.map((changelogId) =>
            fetch("/api/changelog/view", {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
              },
              body: JSON.stringify({
                changelogId,
                dismissed,
              }),
            })
          )
        );
      } catch (err) {
        const errorMessage =
          err instanceof Error
            ? err.message
            : "Failed to mark changelogs as viewed";
        console.error("Error marking multiple changelogs as viewed:", err);
        toast.error(errorMessage);
      }
    },
    []
  );

  const removeViewedChangelogs = useCallback((changelogIds: string[]) => {
    setChangelogs((prev) => prev.filter((c) => !changelogIds.includes(c.id)));

    // If no more changelogs, hide modal
    setChangelogs((prev) => {
      if (prev.length === 0) {
        setShowModal(false);
      }
      return prev;
    });
  }, []);

  const refreshChangelogs = useCallback(async () => {
    await fetchChangelogs();
  }, [fetchChangelogs]);

  // Fetch changelogs on mount and when dependencies change
  useEffect(() => {
    fetchChangelogs();
  }, [fetchChangelogs]);

  // Check for new changelogs periodically (every 5 minutes)
  useEffect(() => {
    if (!session?.userId || !tenantId) return;

    const interval = setInterval(
      () => {
        fetchChangelogs();
      },
      5 * 60 * 1000
    ); // 5 minutes

    return () => clearInterval(interval);
  }, [session?.userId, tenantId, fetchChangelogs]);

  return {
    changelogs,
    isLoading,
    error,
    showModal,
    setShowModal,
    markAsViewed,
    markMultipleAsViewed,
    removeViewedChangelogs,
    refreshChangelogs,
  };
};
