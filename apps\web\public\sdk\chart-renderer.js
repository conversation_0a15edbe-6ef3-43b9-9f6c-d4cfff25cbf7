/**
 * Standalone Chart Renderer for Chatbot SDK
 * Provides chart and diagram rendering without React dependencies
 */

class ChartRenderer {
  constructor() {
    this.chartId = 0;
    this.loadedLibraries = new Set();
  }

  /**
   * Detects chart syntax in content
   */
  detectCharts(content) {
    const lines = content.split('\n');
    const charts = [];

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      
      if (line.startsWith('```')) {
        const chartMatch = line.match(/^```(chart:(\w+)|mermaid|json:chart)(.*)$/);
        if (chartMatch) {
          const [, fullType, chartType] = chartMatch;
          
          // Find the closing ```
          let endIndex = -1;
          let chartContent = '';
          
          for (let j = i + 1; j < lines.length; j++) {
            if (lines[j].trim() === '```') {
              endIndex = j;
              break;
            }
            chartContent += lines[j] + '\n';
          }
          
          if (endIndex !== -1) {
            charts.push({
              startIndex: i,
              endIndex,
              type: fullType === 'mermaid' ? 'mermaid' : 'chart',
              chartType: chartType || 'line',
              content: chartContent.trim()
            });
            
            i = endIndex; // Skip processed lines
          }
        }
      }
    }

    return charts;
  }

  /**
   * Renders content with charts
   */
  async renderContentWithCharts(content, container) {
    const charts = this.detectCharts(content);
    
    if (charts.length === 0) {
      return false; // No charts found
    }

    const lines = content.split('\n');
    let lastIndex = 0;

    for (const chart of charts) {
      // Add content before the chart
      if (chart.startIndex > lastIndex) {
        const beforeContent = lines
          .slice(lastIndex, chart.startIndex)
          .join('\n')
          .trim();
        
        if (beforeContent) {
          const contentDiv = document.createElement('div');
          contentDiv.innerHTML = this.parseMarkdown(beforeContent);
          container.appendChild(contentDiv);
        }
      }

      // Add the chart
      const chartContainer = document.createElement('div');
      chartContainer.className = 'skh-chart-container';
      chartContainer.style.cssText = 'margin: 16px 0; border: 1px solid #e2e8f0; border-radius: 8px; overflow: hidden;';
      
      if (chart.type === 'mermaid') {
        await this.renderMermaidDiagram(chart.content, chartContainer);
      } else {
        await this.renderChart(chart.chartType, chart.content, chartContainer);
      }
      
      container.appendChild(chartContainer);
      lastIndex = chart.endIndex + 1;
    }

    // Add remaining content
    if (lastIndex < lines.length) {
      const afterContent = lines.slice(lastIndex).join('\n').trim();
      if (afterContent) {
        const contentDiv = document.createElement('div');
        contentDiv.innerHTML = this.parseMarkdown(afterContent);
        container.appendChild(contentDiv);
      }
    }

    return true; // Charts were rendered
  }

  /**
   * Renders a Mermaid diagram
   */
  async renderMermaidDiagram(content, container) {
    try {
      // Load Mermaid if not already loaded
      if (!this.loadedLibraries.has('mermaid')) {
        await this.loadScript('https://cdn.jsdelivr.net/npm/mermaid@10.9.1/dist/mermaid.min.js');
        this.loadedLibraries.add('mermaid');
        
        // Initialize Mermaid
        mermaid.initialize({
          startOnLoad: false,
          theme: 'default',
          securityLevel: 'loose'
        });
      }

      const diagramId = `mermaid-${this.chartId++}`;
      const diagramDiv = document.createElement('div');
      diagramDiv.id = diagramId;
      diagramDiv.style.cssText = 'padding: 16px; background: white; text-align: center;';
      
      container.appendChild(diagramDiv);

      // Render the diagram
      const { svg } = await mermaid.render(diagramId + '-svg', content);
      diagramDiv.innerHTML = svg;

    } catch (error) {
      console.error('Error rendering Mermaid diagram:', error);
      container.innerHTML = `
        <div style="padding: 16px; text-align: center; color: #ef4444;">
          <p>Failed to render diagram</p>
          <details style="margin-top: 8px;">
            <summary style="cursor: pointer;">Show diagram code</summary>
            <pre style="margin-top: 8px; padding: 8px; background: #f1f5f9; border-radius: 4px; text-align: left; font-size: 12px; overflow: auto;">${content}</pre>
          </details>
        </div>
      `;
    }
  }

  /**
   * Renders a chart using Chart.js
   */
  async renderChart(type, content, container) {
    try {
      // Load Chart.js if not already loaded
      if (!this.loadedLibraries.has('chartjs')) {
        await this.loadScript('https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.js');
        this.loadedLibraries.add('chartjs');
      }

      // Parse chart data
      const chartData = this.parseChartData(content);
      if (!chartData) {
        throw new Error('Invalid chart data');
      }

      // Create canvas element
      const canvas = document.createElement('canvas');
      canvas.style.cssText = 'max-width: 100%; height: 300px;';
      
      const chartContainer = document.createElement('div');
      chartContainer.style.cssText = 'padding: 16px; background: white;';
      chartContainer.appendChild(canvas);
      container.appendChild(chartContainer);

      // Create chart
      new Chart(canvas, {
        type: this.mapChartType(type),
        data: chartData,
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              display: true,
              position: 'top'
            }
          },
          scales: type !== 'pie' ? {
            y: {
              beginAtZero: true
            }
          } : {}
        }
      });

    } catch (error) {
      console.error('Error rendering chart:', error);
      container.innerHTML = `
        <div style="padding: 16px; text-align: center; color: #ef4444;">
          <p>Failed to render chart</p>
          <details style="margin-top: 8px;">
            <summary style="cursor: pointer;">Show chart data</summary>
            <pre style="margin-top: 8px; padding: 8px; background: #f1f5f9; border-radius: 4px; text-align: left; font-size: 12px; overflow: auto;">${content}</pre>
          </details>
        </div>
      `;
    }
  }

  /**
   * Maps chart types to Chart.js types
   */
  mapChartType(type) {
    const typeMap = {
      'line': 'line',
      'bar': 'bar',
      'area': 'line',
      'pie': 'pie',
      'scatter': 'scatter'
    };
    return typeMap[type] || 'line';
  }

  /**
   * Parses chart data from various formats
   */
  parseChartData(content) {
    try {
      // Try JSON first
      if (content.trim().startsWith('{') || content.trim().startsWith('[')) {
        const parsed = JSON.parse(content);
        if (parsed.data) {
          return parsed.data;
        }
        return this.convertToChartJsFormat(Array.isArray(parsed) ? parsed : [parsed]);
      }

      // Try CSV format
      const csvData = this.parseCSV(content);
      return this.convertToChartJsFormat(csvData);
    } catch (error) {
      console.error('Error parsing chart data:', error);
      return null;
    }
  }

  /**
   * Parses CSV-like data
   */
  parseCSV(content) {
    const lines = content.trim().split('\n');
    if (lines.length < 2) return [];

    const headers = lines[0].split(',').map(h => h.trim());
    const data = [];

    for (let i = 1; i < lines.length; i++) {
      const values = lines[i].split(',').map(v => v.trim());
      if (values.length === headers.length) {
        const row = {};
        headers.forEach((header, index) => {
          const value = values[index];
          row[header] = isNaN(Number(value)) ? value : Number(value);
        });
        data.push(row);
      }
    }

    return data;
  }

  /**
   * Converts data to Chart.js format
   */
  convertToChartJsFormat(data) {
    if (!data || data.length === 0) return null;

    const firstItem = data[0];
    const keys = Object.keys(firstItem);
    
    // Find label key (string values)
    const labelKey = keys.find(key => typeof firstItem[key] === 'string') || keys[0];
    
    // Find data keys (numeric values)
    const dataKeys = keys.filter(key => typeof firstItem[key] === 'number');

    const labels = data.map(item => item[labelKey]);
    const datasets = dataKeys.map((key, index) => ({
      label: key,
      data: data.map(item => item[key]),
      backgroundColor: this.getColor(index, 0.6),
      borderColor: this.getColor(index, 1),
      borderWidth: 2
    }));

    return { labels, datasets };
  }

  /**
   * Gets color for chart elements
   */
  getColor(index, alpha = 1) {
    const colors = [
      `rgba(59, 130, 246, ${alpha})`, // blue
      `rgba(16, 185, 129, ${alpha})`, // green
      `rgba(245, 158, 11, ${alpha})`, // amber
      `rgba(239, 68, 68, ${alpha})`,  // red
      `rgba(139, 92, 246, ${alpha})`, // purple
      `rgba(6, 182, 212, ${alpha})`,  // cyan
    ];
    return colors[index % colors.length];
  }

  /**
   * Loads external script
   */
  loadScript(src) {
    return new Promise((resolve, reject) => {
      const script = document.createElement('script');
      script.src = src;
      script.onload = resolve;
      script.onerror = reject;
      document.head.appendChild(script);
    });
  }

  /**
   * Basic markdown parser (simplified)
   */
  parseMarkdown(content) {
    return content
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      .replace(/\*(.*?)\*/g, '<em>$1</em>')
      .replace(/`(.*?)`/g, '<code>$1</code>')
      .replace(/\n/g, '<br>');
  }
}

// Export for use in SDK
window.ChartRenderer = ChartRenderer;
