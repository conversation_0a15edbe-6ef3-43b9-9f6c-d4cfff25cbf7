from fastapi import HTT<PERSON>Ex<PERSON>, status, Request, Depends
from typing import Optional, Dict, Any
from src.config import settings
import logging
import jwt

# Set up logging
logger = logging.getLogger(__name__)

# JWT verification settings
JWT_SECRET = settings.NEXTAUTH_SECRET  # This should match your NextAuth secret
JWT_ALGORITHM = "HS256"  # Default algorithm used by jsonwebtoken

if not JWT_SECRET:
    logger.warning("NEXTAUTH_SECRET is not set. Authentication will not work properly.")
else:
    logger.info("JWT authentication configured successfully.")

def get_token_from_request(request: Request) -> Optional[str]:
    """
    Extract the JWT token from the request.
    
    Args:
        request: The FastAPI request object
        
    Returns:
        The JWT token if found, None otherwise
    """
    # Try to get the token from the Authorization header
    auth_header = request.headers.get("Authorization")
    if auth_header and auth_header.startswith("Bearer "):
        return auth_header.replace("Bearer ", "")
    
    # If no token in Authorization header, return None
    return None

# Create a custom dependency that handles token validation with better error handling
async def validate_token(request: Request):
    """
    Dependency to validate the authentication token without extracting user data.

    Returns:
        True if the token is valid

    Raises:
        HTTPException: If authentication fails
    """
    try:
        # Get the token from the request
        token = get_token_from_request(request)
        
        if not token:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Not authenticated",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        # Verify the token
        jwt.decode(token, JWT_SECRET, algorithms=[JWT_ALGORITHM])
        return True
    except jwt.ExpiredSignatureError:
        logger.error("Token has expired")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Token has expired",
            headers={"WWW-Authenticate": "Bearer"},
        )
    except jwt.InvalidTokenError as e:
        logger.error(f"Invalid token: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=f"Invalid token: {str(e)}",
            headers={"WWW-Authenticate": "Bearer"},
        )
    except Exception as e:
        # Handle any other errors
        logger.error(f"Authentication error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=f"Authentication error: {str(e)}",
            headers={"WWW-Authenticate": "Bearer"},
        )

# Create a dependency that validates the token and returns the payload
async def get_current_user(request: Request) -> Dict[str, Any]:
    """
    Dependency to get the current user from the token.

    Returns:
        The JWT payload containing user information

    Raises:
        HTTPException: If authentication fails
    """
    try:
        # Get the token from the request
        token = get_token_from_request(request)
        
        if not token:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Not authenticated",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        # Decode the token
        payload = jwt.decode(token, JWT_SECRET, algorithms=[JWT_ALGORITHM])
        return payload
    except jwt.ExpiredSignatureError:
        logger.error("Token has expired")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Token has expired",
            headers={"WWW-Authenticate": "Bearer"},
        )
    except jwt.InvalidTokenError as e:
        logger.error(f"Invalid token: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=f"Invalid token: {str(e)}",
            headers={"WWW-Authenticate": "Bearer"},
        )
    except Exception as e:
        # Handle any other errors
        logger.error(f"Authentication error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=f"Authentication error: {str(e)}",
            headers={"WWW-Authenticate": "Bearer"},
        )

# Create a dependency that validates the token and returns the user ID
async def get_current_user_id(request: Request) -> str:
    """
    Dependency to get the current user ID from the token.

    Returns:
        The user ID from the JWT payload

    Raises:
        HTTPException: If authentication fails or user ID is not found
    """
    payload = await get_current_user(request)
    
    # Extract the user ID from the payload
    user_id = payload.get("userId")
    if not user_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="User ID not found in token",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    return user_id

# Create a dependency that validates the token if present, without requiring authentication
async def validate_optional_token(request: Request) -> bool:
    """
    Validate the token if present, without requiring authentication.

    Args:
        request: The FastAPI request object

    Returns:
        True if a valid token is present, False otherwise
    """
    try:
        # Get the token from the request
        token = get_token_from_request(request)
        
        if not token:
            return False
        
        # Verify the token
        jwt.decode(token, JWT_SECRET, algorithms=[JWT_ALGORITHM])
        return True
    except Exception:
        return False
