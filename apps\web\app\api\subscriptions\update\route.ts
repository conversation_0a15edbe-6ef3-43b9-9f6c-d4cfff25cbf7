import { NextResponse } from "next/server";
import { getToken } from "next-auth/jwt";
import db from "@/lib/shared-db";
import <PERSON><PERSON> from "stripe";

// Initialize Stripe with the secret key
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY as string, {
  apiVersion: "2023-10-16" as any,
});

// Helper function to find a product by name
async function findProductByName(name: string): Promise<Stripe.Product | null> {
  try {
    const products = await stripe.products.list({
      active: true,
      limit: 100,
    });

    const product = products.data.find((p) => p.name === name);
    return product || null;
  } catch (error) {
    console.error(`Error finding product by name ${name}:`, error);
    return null;
  }
}

// Helper function to find a price for a product
async function findPriceForProduct(
  productId: string
): Promise<Stripe.Price | null> {
  try {
    const prices = await stripe.prices.list({
      product: productId,
      active: true,
      limit: 1,
    });

    return prices.data.length > 0 ? prices.data[0] : null;
  } catch (error) {
    console.error(`Error finding price for product ${productId}:`, error);
    return null;
  }
}

// Helper function to create a subscription for a specific component
async function createComponentSubscription(
  customerId: string,
  productName: string,
  productDescription: string,
  priceAmount: number,
  quantity: number = 1,
  metadata: Record<string, string> = {}
): Promise<string> {
  try {
    // Create or find the product
    let product = await findProductByName(productName);
    if (!product) {
      product = await stripe.products.create({
        name: productName,
        description: productDescription,
        active: true,
      });
    }

    // Create or find the price
    let price = await findPriceForProduct(product.id);
    if (!price) {
      price = await stripe.prices.create({
        product: product.id,
        currency: "chf",
        unit_amount: Math.round(priceAmount * 100), // Convert to cents
        recurring: { interval: "month" },
        active: true,
      });
    }

    // Create the subscription
    const subscription = await stripe.subscriptions.create({
      customer: customerId,
      items: [
        {
          price: price.id,
          quantity,
        },
      ],
      metadata,
    });

    return subscription.id;
  } catch (error) {
    console.error(`Error creating component subscription:`, error);
    throw error;
  }
}

export async function POST(request: Request) {
  try {
    const token = await getToken({ req: request as any });
    if (!token) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const { tenantId, planId, additionalUsers, additionalStorageGB } = body;

    // Validate required fields
    if (!tenantId) {
      return NextResponse.json(
        { error: "Tenant ID is required" },
        { status: 400 }
      );
    }

    // Verify user has admin access to this tenant
    const membership = await db.membership.findFirst({
      where: {
        userId: token.sub,
        tenantId,
        role: { in: ["ADMIN", "OWNER"] },
      },
    });

    if (!membership) {
      return NextResponse.json(
        { error: "You don't have admin permissions for this tenant" },
        { status: 403 }
      );
    }

    // Get the active subscription
    const subscription = await db.subscription.findFirst({
      where: {
        tenantId,
        isActive: true,
      },
      include: {
        plan: true,
      },
    });

    if (!subscription) {
      return NextResponse.json(
        { error: "No active subscription found" },
        { status: 404 }
      );
    }

    // Get the tenant's Stripe customer ID and subscription ID
    if (!subscription.stripeSubscriptionId || !subscription.stripeCustomerId) {
      return NextResponse.json(
        { error: "Subscription is not linked to Stripe" },
        { status: 400 }
      );
    }

    // If planId is provided, we need to create a checkout session for the plan upgrade
    if (planId && planId !== subscription.planId) {
      try {
        // Get the new plan's Stripe price ID
        const newPlan = await db.plan.findUnique({
          where: { id: planId },
        });

        if (!newPlan || !newPlan.stripePriceId) {
          return NextResponse.json(
            { error: "Invalid plan or missing Stripe price ID" },
            { status: 400 }
          );
        }

        console.log(
          `Creating checkout session for plan upgrade to ${newPlan.name} (${newPlan.stripePriceId})`
        );

        // Create line items for the checkout session
        const lineItems: any[] = [
          {
            price: newPlan.stripePriceId,
            quantity: 1,
          },
        ];

        // Add additional users if any
        if (additionalUsers && additionalUsers > 0) {
          // Create a price data for additional users
          lineItems.push({
            price_data: {
              currency: "chf",
              product_data: {
                name: "Additional Users",
                description: `Additional users beyond the ${newPlan.includedUsers} included in your plan`,
              },
              unit_amount: Math.round(newPlan.additionalUserFee * 100), // Convert to cents
              recurring: {
                interval: "month",
              },
            },
            quantity: additionalUsers,
          });
        }

        // Add additional storage if any
        if (additionalStorageGB && additionalStorageGB > 0) {
          // Determine storage tier and price
          let storagePrice = 0;
          let tierName = "";

          if (additionalStorageGB <= 10) {
            storagePrice = 29;
            tierName = "10GB";
          } else if (additionalStorageGB <= 50) {
            storagePrice = 79;
            tierName = "50GB";
          } else {
            storagePrice = 149;
            tierName = "100GB";
          }

          // Create a price data for additional storage
          lineItems.push({
            price_data: {
              currency: "chf",
              product_data: {
                name: `Additional Vector Storage (${tierName})`,
                description: `Additional vector storage beyond the ${newPlan.vectorStoreGB} GB included in your plan`,
              },
              unit_amount: Math.round(storagePrice * 100), // Convert to cents
              recurring: {
                interval: "month",
              },
            },
            quantity: 1,
          });
        }

        // Create a checkout session for the plan upgrade
        const checkoutSession = await stripe.checkout.sessions.create({
          customer: subscription.stripeCustomerId as string,
          payment_method_types: ["card"],
          line_items: lineItems as any,
          mode: "subscription",
          success_url: `${request.headers.get("origin")}/billing?success=true`,
          cancel_url: `${request.headers.get("origin")}/billing?canceled=true`,
          allow_promotion_codes: true, // Enable promotion code field in checkout
          subscription_data: {
            trial_period_days: 14,
          },
          metadata: {
            tenantId,
            planId,
            additionalUsers:
              additionalUsers?.toString() ||
              subscription.additionalUsers.toString(),
            additionalStorageGB: (
              additionalStorageGB ||
              subscription.additionalStorageGB ||
              0
            ).toString(),
            userId: token.sub as string,
          },
        });

        console.log(
          `Checkout session created successfully:`,
          checkoutSession.id
        );

        return NextResponse.json({
          portalUrl: checkoutSession.url,
        });
      } catch (stripeError) {
        console.error(
          "Error creating checkout session for plan upgrade:",
          stripeError
        );
        return NextResponse.json(
          { error: "Failed to create checkout session for plan upgrade" },
          { status: 500 }
        );
      }
    }

    // If only add-ons are changing (no plan change), update the subscription directly
    if (
      (additionalUsers !== undefined &&
        additionalUsers !== subscription.additionalUsers) ||
      (additionalStorageGB !== undefined &&
        additionalStorageGB !== subscription.additionalStorageGB)
    ) {
      try {
        console.log(
          `Updating subscription add-ons: additionalUsers=${additionalUsers}, additionalStorageGB=${additionalStorageGB}`
        );

        // Update the subscription in our database first
        const updatedSubscription = await db.subscription.update({
          where: {
            id: subscription.id,
          },
          data: {
            additionalUsers:
              additionalUsers !== undefined
                ? additionalUsers
                : subscription.additionalUsers,
            additionalStorageGB:
              additionalStorageGB !== undefined
                ? additionalStorageGB
                : subscription.additionalStorageGB || 0,
          },
          include: {
            plan: true,
          },
        });

        // Instead of updating the existing subscription directly, we'll create a checkout session
        // with the current plan and the new add-ons

        // Get the current plan
        const currentPlan = subscription.plan;

        // Create line items for the checkout session - for add-on updates, we don't include the base plan
        const lineItems: any[] = [];

        // We'll store the current plan ID in metadata to keep track of it
        const checkoutMetadata: any = {
          tenantId,
          planId: subscription.planId,
          additionalUsers:
            additionalUsers?.toString() ||
            subscription.additionalUsers.toString(),
          additionalStorageGB: (
            additionalStorageGB ||
            subscription.additionalStorageGB ||
            0
          ).toString(),
          userId: token.sub as string,
          isAddonUpdate: "true",
          currentSubscriptionId: subscription.stripeSubscriptionId,
        };

        // For add-on updates, we'll directly update the subscription in Stripe
        // This avoids creating a new checkout session that would charge for the base plan again

        try {
          // Get the current subscription from Stripe
          const stripeSubscription = await stripe.subscriptions.retrieve(
            subscription.stripeSubscriptionId as string
          );

          // Get the subscription items
          const subscriptionItems = stripeSubscription.items.data;

          // Create an array to store the updated items
          const updatedItems: any[] = [];

          // Keep the main subscription item (the plan)
          const mainItem = subscriptionItems[0];
          updatedItems.push({
            id: mainItem.id,
            price: mainItem.price.id,
            quantity: 1,
          });

          // Remove any existing add-on items
          for (let i = 1; i < subscriptionItems.length; i++) {
            updatedItems.push({
              id: subscriptionItems[i].id,
              deleted: true,
            });
          }

          // Add additional users if any
          if (additionalUsers && additionalUsers > 0) {
            // Create a product for additional users
            const additionalUsersProduct = await stripe.products.create({
              name: "Additional Users",
              description: `Additional users beyond the ${currentPlan.includedUsers} included in your plan`,
              active: true,
            });

            // Create a price for the additional users product
            const additionalUsersPrice = await stripe.prices.create({
              currency: "chf",
              product: additionalUsersProduct.id,
              unit_amount: Math.round(currentPlan.additionalUserFee * 100), // Convert to cents
              recurring: {
                interval: "month",
              },
              active: true,
            });

            updatedItems.push({
              price: additionalUsersPrice.id,
              quantity: additionalUsers,
            });
          }

          // Add additional storage if any
          if (additionalStorageGB && additionalStorageGB > 0) {
            // Determine storage tier and price
            let storagePrice = 0;
            let tierName = "";

            if (additionalStorageGB <= 10) {
              storagePrice = 29;
              tierName = "10GB";
            } else if (additionalStorageGB <= 50) {
              storagePrice = 79;
              tierName = "50GB";
            } else {
              storagePrice = 149;
              tierName = "100GB";
            }

            // Create a product for additional storage
            const additionalStorageProduct = await stripe.products.create({
              name: `Additional Vector Storage (${tierName})`,
              description: `Additional vector storage beyond the ${currentPlan.vectorStoreGB} GB included in your plan`,
              active: true,
            });

            // Create a price for the additional storage product
            const additionalStoragePrice = await stripe.prices.create({
              currency: "chf",
              product: additionalStorageProduct.id,
              unit_amount: Math.round(storagePrice * 100), // Convert to cents
              recurring: {
                interval: "month",
              },
              active: true,
            });

            updatedItems.push({
              price: additionalStoragePrice.id,
              quantity: 1,
            });
          }

          // Update the subscription in Stripe
          const updatedStripeSubscription = await stripe.subscriptions.update(
            subscription.stripeSubscriptionId as string,
            {
              items: updatedItems,
              metadata: {
                additionalUsers:
                  additionalUsers?.toString() ||
                  subscription.additionalUsers.toString(),
                additionalStorageGB: (
                  additionalStorageGB ||
                  subscription.additionalStorageGB ||
                  0
                ).toString(),
              },
              proration_behavior: "always_invoice",
            }
          );

          console.log(
            `Stripe subscription updated successfully:`,
            updatedStripeSubscription.id
          );

          return NextResponse.json({
            subscription: updatedSubscription,
          });
        } catch (error) {
          console.error("Error updating subscription in Stripe:", error);

          // If we can't update the subscription directly, fall back to creating a checkout session
          // but only for the add-ons, not the base plan

          // Add additional users if any
          if (additionalUsers && additionalUsers > 0) {
            // Create a product for additional users
            const additionalUsersProduct = await stripe.products.create({
              name: "Additional Users",
              description: `Additional users beyond the ${currentPlan.includedUsers} included in your plan`,
              active: true,
            });

            // Create a price for the additional users product
            const additionalUsersPrice = await stripe.prices.create({
              currency: "chf",
              product: additionalUsersProduct.id,
              unit_amount: Math.round(currentPlan.additionalUserFee * 100), // Convert to cents
              recurring: {
                interval: "month",
              },
              active: true,
            });

            lineItems.push({
              price: additionalUsersPrice.id,
              quantity: additionalUsers,
            });
          }

          // Add additional storage if any
          if (additionalStorageGB && additionalStorageGB > 0) {
            // Determine storage tier and price
            let storagePrice = 0;
            let tierName = "";

            if (additionalStorageGB <= 10) {
              storagePrice = 29;
              tierName = "10GB";
            } else if (additionalStorageGB <= 50) {
              storagePrice = 79;
              tierName = "50GB";
            } else {
              storagePrice = 149;
              tierName = "100GB";
            }

            // Create a product for additional storage
            const additionalStorageProduct = await stripe.products.create({
              name: `Additional Vector Storage (${tierName})`,
              description: `Additional vector storage beyond the ${currentPlan.vectorStoreGB} GB included in your plan`,
              active: true,
            });

            // Create a price for the additional storage product
            const additionalStoragePrice = await stripe.prices.create({
              currency: "chf",
              product: additionalStorageProduct.id,
              unit_amount: Math.round(storagePrice * 100), // Convert to cents
              recurring: {
                interval: "month",
              },
              active: true,
            });

            lineItems.push({
              price: additionalStoragePrice.id,
              quantity: 1,
            });
          }

          // Create a checkout session for just the add-ons
          const checkoutSession = await stripe.checkout.sessions.create({
            customer: subscription.stripeCustomerId as string,
            payment_method_types: ["card"],
            line_items: lineItems,
            mode: "subscription",
            success_url: `${request.headers.get(
              "origin"
            )}/billing?success=true`,
            cancel_url: `${request.headers.get(
              "origin"
            )}/billing?canceled=true`,
            allow_promotion_codes: true, // Enable promotion code field in checkout
            metadata: checkoutMetadata,
            subscription_data: {
              trial_period_days: 14,
            },
          });

          console.log(
            `Checkout session created successfully:`,
            checkoutSession.id
          );

          return NextResponse.json({
            subscription: updatedSubscription,
            portalUrl: checkoutSession.url,
          });
        }
      } catch (error) {
        console.error("Error updating subscription add-ons:", error);
        return NextResponse.json(
          { error: "Failed to update subscription add-ons" },
          { status: 500 }
        );
      }
    } else {
      // If nothing is changing, just create a Stripe customer portal session
      const session = await stripe.billingPortal.sessions.create({
        customer: subscription.stripeCustomerId as string,
        return_url: `${request.headers.get("origin")}/billing`,
      });

      return NextResponse.json({
        portalUrl: session.url,
      });
    }
  } catch (error) {
    console.error("Error updating subscription:", error);
    return NextResponse.json(
      { error: "Failed to update subscription" },
      { status: 500 }
    );
  }
}
