import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/next-auth";
import dbPromise from "@/lib/shared-db";
import { revalidatePath } from "next/cache";

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  let body: any = null;

  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    const db = await dbPromise;
    const tenantId = params.id;

    // Parse request body to get the new tenant name
    body = await request.json();
    const { name } = body;

    console.log(`Processing tenant name update request for tenant ${tenantId}:`, { name });

    // Validate payload
    if (!name || typeof name !== 'string') {
      return NextResponse.json(
        { error: "Tenant name is required and must be a string" },
        { status: 400 }
      );
    }

    // Validate name length and format
    const trimmedName = name.trim();
    if (trimmedName.length === 0) {
      return NextResponse.json(
        { error: "Tenant name cannot be empty" },
        { status: 400 }
      );
    }

    if (trimmedName.length < 2) {
      return NextResponse.json(
        { error: "Tenant name must be at least 2 characters long" },
        { status: 400 }
      );
    }

    if (trimmedName.length > 100) {
      return NextResponse.json(
        { error: "Tenant name cannot exceed 100 characters" },
        { status: 400 }
      );
    }

    // Check for invalid characters (basic validation)
    const nameRegex = /^[a-zA-Z0-9\s\-_.,&()]+$/;
    if (!nameRegex.test(trimmedName)) {
      return NextResponse.json(
        { error: "Tenant name contains invalid characters. Only letters, numbers, spaces, and basic punctuation are allowed." },
        { status: 400 }
      );
    }

    // Verify tenant exists
    const tenant = await db.tenant.findUnique({
      where: { id: tenantId },
      select: {
        id: true,
        name: true,
        slug: true
      }
    });

    if (!tenant) {
      return NextResponse.json(
        { error: "Tenant not found" },
        { status: 404 }
      );
    }

    const currentName = tenant.name;

    // Check if name is actually changing
    if (trimmedName === currentName) {
      return NextResponse.json({
        message: "Tenant name is already up to date",
        tenant: {
          id: tenant.id,
          name: tenant.name,
          slug: tenant.slug
        }
      });
    }

    // Check if another tenant already has this name (optional uniqueness check)
    const existingTenant = await db.tenant.findFirst({
      where: {
        name: trimmedName,
        id: { not: tenantId } // Exclude current tenant
      },
      select: { id: true }
    });

    if (existingTenant) {
      return NextResponse.json(
        { error: "A tenant with this name already exists" },
        { status: 409 }
      );
    }

    // Update tenant name in database
    const updatedTenant = await db.tenant.update({
      where: { id: tenantId },
      data: {
        name: trimmedName,
        updatedAt: new Date()
      },
      select: {
        id: true,
        name: true,
        slug: true,
        updatedAt: true
      }
    });

    // Log the name change for audit purposes
    console.log(`Tenant name updated for tenant ${tenantId}: "${currentName}" -> "${trimmedName}"`);

    // ✅ Revalidate the tenant list page and tenant detail page to ensure fresh data
    revalidatePath('/tenants');
    revalidatePath(`/tenants/${tenantId}`);

    return NextResponse.json({
      message: "Tenant name updated successfully",
      tenant: {
        id: updatedTenant.id,
        name: updatedTenant.name,
        slug: updatedTenant.slug,
        updatedAt: updatedTenant.updatedAt
      }
    });

  } catch (error) {
    console.error("Error updating tenant name:", error);
    
    return NextResponse.json(
      { 
        error: "Failed to update tenant name",
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
