import { authOptions } from "@/lib/next-auth";
import db from "@/lib/shared-db";
import { getServerSession } from "next-auth/next";
import { NextResponse } from "next/server";

export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  const session = await getServerSession(authOptions);

  if (!session) {
    return NextResponse.json(
      {
        error: "Unauthorized",
      },
      { status: 401 }
    );
  }

  const { id } = params;

  // Validate ObjectId format (24 character hex string)
  if (!id || id.length !== 24 || !/^[0-9a-f]{24}$/i.test(id)) {
    return NextResponse.json(
      {
        error: "Invalid tenant ID format",
      },
      { status: 400 }
    );
  }

  try {
    

    // Fetch tenant with related data
    const tenant = await db.tenant.findUnique({
      where: {
        id,
      },
      include: {
        Membership: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
                createdAt: true,
              },
            },
          },
        },
        Subscription: {
          where: {
            isActive: true,
          },
          include: {
            plan: {
              select: {
                name: true,
                price: true,
              },
            },
          },
        },
        TokenUsage: {
          orderBy: {
            timestamp: "desc",
          },
          take: 7, // Last 7 records for chart
          select: {
            id: true,
            inputTokens: true,
            outputTokens: true,
            timestamp: true,
            cost: true,
          },
        },
      },
    });

    if (!tenant) {
      return NextResponse.json(
        {
          error: "Tenant not found",
        },
        { status: 404 }
      );
    }

    // Calculate additional metrics
    const userCount = tenant.Membership.length;
    const activeSubscription = tenant.Subscription[0];
    const planName = activeSubscription?.plan?.name || "Free";
    const monthlyRevenue = activeSubscription?.plan?.price || 0;

    // Determine status
    let status: "active" | "inactive" | "trial" = "inactive";
    if (activeSubscription) {
      status = userCount > 0 ? "active" : "trial";
    }

    // Mock storage calculation (in a real app, this would come from actual usage data)
    const storageUsed = Math.floor(Math.random() * 5000) + 100; // Mock 100MB - 5GB
    const storageFormatted = storageUsed > 1000
      ? `${(storageUsed / 1000).toFixed(1)}GB`
      : `${storageUsed}MB`;

    // Format response with calculated fields
    const response = {
      ...tenant,
      users: userCount,
      plan: planName,
      status,
      storage: storageFormatted,
      monthlyRevenue,
      hasActiveSubscription: !!activeSubscription,
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error fetching tenant:", error);
    return NextResponse.json(
      {
        error: "Failed to fetch tenant data",
      },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: Request,
  { params }: { params: { id: string } }
) {
  const session = await getServerSession(authOptions);

  if (!session) {
    return NextResponse.json(
      {
        error: "Unauthorized",
      },
      { status: 401 }
    );
  }

  const { id } = params;

  try {
    // Check if tenant exists
    const tenant = await db.tenant.findUnique({
      where: { id },
    });

    if (!tenant) {
      return NextResponse.json(
        {
          error: "Tenant not found",
        },
        { status: 404 }
      );
    }

    // Delete related records first (cascade delete)
    // Delete memberships
    await db.membership.deleteMany({
      where: { tenantId: id },
    });

    // Delete subscriptions
    await db.subscription.deleteMany({
      where: { tenantId: id },
    });

    // Delete API requests
    await db.aPIRequest.deleteMany({
      where: { tenantId: id },
    });

    // Finally delete the tenant
    await db.tenant.delete({
      where: { id },
    });

    return NextResponse.json(
      {
        message: "Tenant deleted successfully",
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error deleting tenant:", error);
    return NextResponse.json(
      {
        error: "Failed to delete tenant",
      },
      { status: 500 }
    );
  }
}
