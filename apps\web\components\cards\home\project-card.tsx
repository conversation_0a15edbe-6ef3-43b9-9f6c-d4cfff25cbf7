"use client";

import React from "react";
import { MoreVertical, UserPlus } from "lucide-react";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import {
  Card,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import { useRouter } from "next/navigation";
import { useLanguage } from "@/lib/language-context";

interface ProjectCardProps {
  initials: string;
  title: string;
  description: string;
  workspace: any;
}

const ProjectCard: React.FC<ProjectCardProps> = ({
  initials,
  title,
  description,
  workspace,
}) => {
  const router = useRouter();
  const { t } = useLanguage();

  const workspaceSlug = workspace?.slug;

  const handleCardClick = () => {
    router.push(`/workspace/${workspaceSlug}`);
  };

  const handleMembersClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    router.push(`/workspace/${workspaceSlug}/members`);
  };

  const handleViewDetailsClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    router.push(`/workspace/${workspaceSlug}/details`);
  };

  const handleMenuClick = (e: React.MouseEvent) => {
    e.stopPropagation();
  };

  return (
    <>
      <Card
        className="w-full max-w-64 overflow-hidden rounded-lg shadow-lg transition-all hover:shadow-xl cursor-pointer"
        onClick={handleCardClick}
      >
        <CardHeader className="relative mb-6 bg-sidebar h-14 flex flex-row items-center justify-between p-3">
          <Avatar className="h-10 w-10 rounded-lg absolute -bottom-5 left-4 border-2 border-background">
            <AvatarFallback className="rounded-lg text-sm font-medium">
              {initials}
            </AvatarFallback>
          </Avatar>
          <div className="absolute right-2 flex items-center gap-2">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className=" h-8 w-8 hover:bg-sidebar-foreground/10"
                  onClick={handleMenuClick}
                >
                  <MoreVertical size={16} />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-48">
                {/* <DropdownMenuItem
                  onClick={handleMembersClick}
                  className="cursor-pointer"
                >
                  <UserPlus className="mr-2 h-4 w-4" />
                  <span>{t("workspace.addRemoveMembers")}</span>
                </DropdownMenuItem> */}
                {/* <DropdownMenuSeparator /> */}
                <DropdownMenuItem
                  onClick={handleViewDetailsClick}
                  className="cursor-pointer"
                >
                  <span>{t("workspace.viewDetails")}</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </CardHeader>
        <div className="px-4 pb-4 pt-4 sm:px-6">
          <CardTitle className="mb-2 text-lg font-semibold tracking-tight">
            {title}
          </CardTitle>
          <CardDescription className="text-sm font-normal line-clamp-2">
            {description}
          </CardDescription>
        </div>
      </Card>
    </>
  );
};

export default ProjectCard;
