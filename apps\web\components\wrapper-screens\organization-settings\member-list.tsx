"use client";

import { useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { MoreHorizontal, Shield, User, UserCircle } from "lucide-react";
import { updateMemberRole, removeMember } from "@/services";
import { formatDistanceToNow } from "date-fns";
import toast from "react-hot-toast";
import { useLanguage } from "@/lib/language-context";
import { getLocale } from "@/utils/date-locale";

interface MemberListProps {
  members: any[];
  currentUserId: string;
  tenantId: string;
  isAdmin: boolean;
  updateMembers: (members: any[]) => void;
}

export function MemberList({
  members,
  currentUserId,
  isAdmin,
  updateMembers,
}: MemberListProps) {
  const [loadingMemberId, setLoadingMemberId] = useState<string | null>(null);
  const { t, language } = useLanguage();
  const [confirmRemove, setConfirmRemove] = useState<{
    open: boolean;
    memberId: string | null;
    memberName: string;
  }>({
    open: false,
    memberId: null,
    memberName: "",
  });

  const handleRoleChange = async (memberId: string, newRole: string) => {
    setLoadingMemberId(memberId);
    try {
      const result = await updateMemberRole(memberId, newRole);
      if (result.error) {
        toast.error(result.error);
      } else {
        // Update the member in the list
        const updatedMembers = members.map((member) => {
          if (member.id === memberId) {
            return { ...member, role: newRole };
          }
          return member;
        });
        updateMembers(updatedMembers);
        toast.success(t("roles.roleUpdatedSuccess", { role: newRole }));
      }
    } catch (error) {
      console.error("Failed to update role:", error);
      toast.error(t("roles.updateRoleFailed"));
    } finally {
      setLoadingMemberId(null);
    }
  };

  const handleRemoveMember = async () => {
    if (!confirmRemove.memberId) return;

    setLoadingMemberId(confirmRemove.memberId);
    try {
      const result = await removeMember(confirmRemove.memberId);
      if (result.error) {
        toast.error(result.error);
      } else {
        // Remove the member from the list
        const updatedMembers = members.filter(
          (member) => member.id !== confirmRemove.memberId
        );
        updateMembers(updatedMembers);
        toast.success(t("roles.memberRemovedSuccess"));
      }
    } catch (error) {
      console.error("Failed to remove member:", error);
      toast.error(t("roles.removeMemberFailed"));
    } finally {
      setLoadingMemberId(null);
      setConfirmRemove({ open: false, memberId: null, memberName: "" });
    }
  };

  const sortedMembers = [...members].sort((a, b) => {
    // Sort by role: OWNER first, then ADMIN, then MEMBER
    const roleOrder = { OWNER: 0, ADMIN: 1, MEMBER: 2 };
    return roleOrder[a.role] - roleOrder[b.role];
  });

  return (
    <>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>{t("common.user")}</TableHead>
            <TableHead>{t("roles.role")}</TableHead>
            <TableHead>{t("roles.joined")}</TableHead>
            {isAdmin && (
              <TableHead className="text-right">
                {t("common.actions")}
              </TableHead>
            )}
          </TableRow>
        </TableHeader>
        <TableBody>
          {sortedMembers.map((member) => {
            const isCurrentUser = member.user.id === currentUserId;
            const isOwner = member.role === "OWNER";
            const canModify = isAdmin && !isCurrentUser && !isOwner;

            return (
              <TableRow key={member.id}>
                <TableCell>
                  <div className="flex items-center gap-3">
                    <Avatar>
                      <AvatarImage
                        src={member.user.image}
                        alt={member.user.name}
                      />
                      <AvatarFallback>
                        {member.user.name
                          ?.split(" ")
                          .map((n: string) => n[0])
                          .join("")
                          .toUpperCase() || "U"}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <div className="font-medium">{member.user.name}</div>
                      <div className="text-sm text-muted-foreground">
                        {member.user.email}
                      </div>
                    </div>
                    {isCurrentUser && (
                      <span className="ml-2 text-xs bg-primary/10 text-primary px-2 py-1 rounded-full">
                        {t("common.you")}
                      </span>
                    )}
                  </div>
                </TableCell>
                <TableCell>
                  <div className="flex items-center">
                    {member.role === "OWNER" ? (
                      <Shield className="h-4 w-4 mr-1 text-primary" />
                    ) : member.role === "ADMIN" ? (
                      <Shield className="h-4 w-4 mr-1 text-amber-500" />
                    ) : (
                      <User className="h-4 w-4 mr-1 text-muted-foreground" />
                    )}
                    <span
                      className={`font-medium ${
                        member.role === "OWNER"
                          ? "text-primary"
                          : member.role === "ADMIN"
                            ? "text-amber-500"
                            : ""
                      }`}
                    >
                      {member.role.charAt(0) +
                        member.role.slice(1).toLowerCase()}
                    </span>
                  </div>
                </TableCell>
                <TableCell>
                  {formatDistanceToNow(new Date(member.createdAt), {
                    addSuffix: true,
                    locale: getLocale(language),
                  })}
                </TableCell>
                {isAdmin && (
                  <TableCell className="text-right">
                    {canModify ? (
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button
                            variant="ghost"
                            size="sm"
                            disabled={loadingMemberId === member.id}
                          >
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>
                            {t("common.actions")}
                          </DropdownMenuLabel>
                          <DropdownMenuItem
                            onClick={() => handleRoleChange(member.id, "ADMIN")}
                            disabled={member.role === "ADMIN"}
                          >
                            <Shield className="h-4 w-4 mr-2" />
                            {t("roles.makeAdmin")}
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() =>
                              handleRoleChange(member.id, "MEMBER")
                            }
                            disabled={member.role === "MEMBER"}
                          >
                            <User className="h-4 w-4 mr-2" />
                            {t("roles.makeMember")}
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem
                            onClick={() =>
                              setConfirmRemove({
                                open: true,
                                memberId: member.id,
                                memberName: member.user.name,
                              })
                            }
                            className="text-destructive focus:text-destructive"
                          >
                            {t("roles.removeMember")}
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    ) : (
                      <span className="text-sm text-muted-foreground">
                        {isCurrentUser
                          ? t("roles.currentUser")
                          : t("roles.owner")}
                      </span>
                    )}
                  </TableCell>
                )}
              </TableRow>
            );
          })}
        </TableBody>
      </Table>

      <AlertDialog
        open={confirmRemove.open}
        onOpenChange={(open) => setConfirmRemove({ ...confirmRemove, open })}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{t("roles.removeTeamMember")}</AlertDialogTitle>
            <AlertDialogDescription>
              {t("roles.confirmRemove", { name: confirmRemove.memberName })}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>{t("common.cancel")}</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleRemoveMember}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {t("common.remove")}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
