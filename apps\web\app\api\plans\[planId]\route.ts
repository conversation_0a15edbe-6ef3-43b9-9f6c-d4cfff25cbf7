import { NextResponse } from "next/server";
import { getToken } from "next-auth/jwt";
import db from "@/lib/shared-db";

// Get a specific plan
export async function GET(
  request: Request,
  { params }: { params: { planId: string } }
) {
  try {
    const planId = params.planId;

    const plan = await db.plan.findUnique({
      where: {
        id: planId,
      },
    });

    if (!plan) {
      return NextResponse.json({ error: "Plan not found" }, { status: 404 });
    }

    return NextResponse.json({ plan });
  } catch (error) {
    console.error("Error fetching plan:", error);
    return NextResponse.json(
      { error: "Failed to fetch plan" },
      { status: 500 }
    );
  }
}

// Update a plan (admin only)
export async function PUT(
  request: Request,
  { params }: { params: { planId: string } }
) {
  try {
    const token = await getToken({ req: request as any });
    if (!token) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const planId = params.planId;
    const body = await request.json();
    const { name, description, includedUsers, additionalUserFee, vectorStoreGB, isActive } = body;

    // Check if plan exists
    const existingPlan = await db.plan.findUnique({
      where: {
        id: planId,
      },
    });

    if (!existingPlan) {
      return NextResponse.json({ error: "Plan not found" }, { status: 404 });
    }

    // Update the plan
    const updateData: any = {};
    if (name !== undefined) updateData.name = name;
    if (description !== undefined) updateData.description = description;
    if (includedUsers !== undefined) updateData.includedUsers = includedUsers;
    if (additionalUserFee !== undefined) updateData.additionalUserFee = additionalUserFee;
    if (vectorStoreGB !== undefined) updateData.vectorStoreGB = vectorStoreGB;
    if (isActive !== undefined) updateData.isActive = isActive;

    const updatedPlan = await db.plan.update({
      where: {
        id: planId,
      },
      data: updateData,
    });

    return NextResponse.json({ plan: updatedPlan });
  } catch (error) {
    console.error("Error updating plan:", error);
    return NextResponse.json(
      { error: "Failed to update plan" },
      { status: 500 }
    );
  }
}

// Delete a plan (admin only)
export async function DELETE(
  request: Request,
  { params }: { params: { planId: string } }
) {
  try {
    const token = await getToken({ req: request as any });
    if (!token) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const planId = params.planId;

    // Check if plan exists
    const existingPlan = await db.plan.findUnique({
      where: {
        id: planId,
      },
    });

    if (!existingPlan) {
      return NextResponse.json({ error: "Plan not found" }, { status: 404 });
    }

    // Check if plan has active subscriptions
    const activeSubscriptions = await db.subscription.findMany({
      where: {
        planId,
        isActive: true,
      },
    });

    if (activeSubscriptions.length > 0) {
      return NextResponse.json(
        { error: "Cannot delete plan with active subscriptions" },
        { status: 400 }
      );
    }

    // Soft delete by setting isActive to false
    const updatedPlan = await db.plan.update({
      where: {
        id: planId,
      },
      data: {
        isActive: false,
      },
    });

    return NextResponse.json({ 
      message: "Plan successfully deleted",
      plan: updatedPlan 
    });
  } catch (error) {
    console.error("Error deleting plan:", error);
    return NextResponse.json(
      { error: "Failed to delete plan" },
      { status: 500 }
    );
  }
}
