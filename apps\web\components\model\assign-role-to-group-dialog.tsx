"use client";

import { useState, useEffect, use } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { useLanguage } from "@/lib/language-context";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  <PERSON><PERSON>Title,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { useRouter } from "next/navigation";
import toast from "react-hot-toast";
import { assignRoleToGroup } from "@/services/src/group";
import { Check, ChevronsUpDown } from "lucide-react";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import { fetchJson } from "@/services";
import { getCookie } from "@/utils/cookies";

interface CustomRole {
  id: string;
  name: string;
  description?: string;
}

interface AssignRoleToGroupDialogProps {
  groupId: string;
  tenantId?: string;
  trigger: React.ReactNode;
  onRoleAssigned?: () => void; // Callback to refresh parent component
}

export function AssignRoleToGroupDialog({
  groupId,
  tenantId,
  trigger,
  onRoleAssigned,
}: AssignRoleToGroupDialogProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [roles, setRoles] = useState<CustomRole[]>([]);
  const [selectedRole, setSelectedRole] = useState<CustomRole | null>(null);
  const [open, setOpen] = useState(false);
  const router = useRouter();
  const { t } = useLanguage();
  const userId = getCookie("userId");

  useEffect(() => {
    if (isOpen && tenantId) {
      fetchRoles();
    }
  }, [isOpen, tenantId]);

  const fetchRoles = async () => {
    try {
      const response = await fetchJson(
        `/api/roles?tenantId=${tenantId}`,
        userId,
        tenantId
      );

      setRoles(response.customRoles || []);
    } catch (error) {
      console.error("Error fetching roles:", error);
    }
  };

  const onSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedRole || !tenantId) {
      toast.error(t("groups.selectRole"));
      return;
    }

    try {
      setIsLoading(true);
      const result = await assignRoleToGroup(
        {
          groupId,
          customRoleId: selectedRole.id,
          tenantId,
        },
        userId
      );
      if (result?.error) {
        toast.error(result.error || t("common.error"));
        return;
      }
      toast.success(t("groups.roleAssignedToGroup"));
      setIsOpen(false);
      setSelectedRole(null);

      // Trigger parent component refresh
      if (onRoleAssigned) {
        onRoleAssigned();
      } else {
        // Fallback to router refresh if no callback provided
        router.refresh();
      }
    } catch (error) {
      toast.error(t("groups.failedToAssignRole"));
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>{trigger}</DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{t("groups.assignRoleToGroup")}</DialogTitle>
          <DialogDescription>
            {t("groups.assignRoleDescription")}
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={onSubmit}>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label>{t("groups.selectRole")}</Label>
              <Popover open={open} modal={true} onOpenChange={setOpen}>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    role="combobox"
                    aria-expanded={open}
                    className="w-full justify-between"
                  >
                    {selectedRole ? selectedRole.name : t("groups.selectRole")}
                    <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent
                  className="w-full p-0 max-h-[300px] overflow-hidden"
                  side="bottom"
                  sideOffset={4}
                >
                  <Command className="max-h-[300px]">
                    <CommandInput
                      placeholder={t("roles.searchRoles")}
                      className="h-9 border-none focus:ring-0"
                    />
                    <CommandEmpty className="py-6 text-center text-sm text-muted-foreground">
                      {t("roles.noRolesFound")}
                    </CommandEmpty>
                    <CommandGroup className="max-h-[240px] overflow-y-auto">
                      {roles.map((role) => (
                        <CommandItem
                          key={role?.id}
                          value={role?.name}
                          onSelect={() => {
                            setSelectedRole(role);
                            setOpen(false);
                          }}
                          className="cursor-pointer hover:bg-accent"
                        >
                          <span className="truncate">{role?.name}</span>
                          <Check
                            className={cn(
                              "ml-auto h-4 w-4 flex-shrink-0",
                              selectedRole?.id === role?.id
                                ? "opacity-100"
                                : "opacity-0"
                            )}
                          />
                        </CommandItem>
                      ))}
                    </CommandGroup>
                  </Command>
                </PopoverContent>
              </Popover>
            </div>
          </div>
          <DialogFooter>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? t("common.assigning") : t("common.assign")}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
