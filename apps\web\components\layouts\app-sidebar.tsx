"use client";

import * as React from "react";
import {
  BotIcon,
  Gem,
  HardDrive,
  History,
  Home,
  FolderOpen,
  ChevronRight,
  File,
  FileText,
  Users,
} from "lucide-react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useLanguage } from "@/lib/language-context";

// Import removed since we're implementing custom scrolling
import { NavProjects } from "@/components/layouts/nav-projects";
import { NavSecondary } from "@/components/layouts/nav-secondary";
import { NavUser } from "@/components/layouts/nav-user";
import {
  Sidebar,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuItem,
} from "@/components/layouts/sidebar";
import { OrganizationSwitcher } from "@/components/organizations/organization-switcher";
import { CreateGuard } from "../permission-guard";

// Default workspaces - adding more to test scrolling

const data = {
  user: {
    name: "<PERSON><PERSON> ",
    role: "Admin",
    avatar: "/avatars/shadcn.jpg",
  },
  navMain: [], // This will be updated with sessionStorage data
  navSecondary: [
    // Help URL will be dynamically generated in the component based on language
    // {
    //   title: "Support",
    //   url: "/",
    //   icon: LifeBuoy,
    //   translationKey: "sidebar.support",
    // },
    {
      title: "Upgrade",
      url: "/billing",
      icon: Gem,
      translationKey: "sidebar.upgrade",
    },
  ],
  projects: [
    {
      name: "Dashboard",
      url: "/dashboard",
      icon: Home,
      translationKey: "sidebar.dashboard",
    },
    {
      name: "My Hub",
      url: "/my-hub",
      icon: HardDrive,
      translationKey: "sidebar.myHub",
    },
    {
      name: "Ask AI",
      url: "/ask-ai",
      icon: BotIcon,
      translationKey: "sidebar.askAi",
    },
    {
      name: "Chat History",
      url: "/chat-history",
      icon: History,
      translationKey: "sidebar.chatHistory",
    },
    {
      name: "Shared Threads",
      url: "/shared-threads",
      icon: Users,
      translationKey: "sidebar.sharedThreads",
    },
  ],
};

export function AppSidebar({
  session,
  permission,
  workspace,
  role,
  ...props
}: any) {
  const user = { ...session?.user, role: role };
  const pathname = usePathname() || "";
  const { t, language } = useLanguage();

  // Create language-aware navigation items
  const languageAwareNavSecondary = React.useMemo(() => [
    {
      title: "Help",
      url: `${process.env.NEXT_PUBLIC_API_BASE_URL}/docs/${language}`,
      icon: FileText,
      translationKey: "sidebar.help",
    },
    ...data.navSecondary,
  ], [language]);

  // Initialize workspaces with the provided workspace prop if available
  const [workspaces, setWorkspaces] = React.useState(() => {
    // Fall back to workspace prop if no temporary data
    return (
      workspace?.map((w: any) => ({
        title: w.name,
        slug: w.slug,
        url: `/workspace/${w.slug}`,
        icon: FolderOpen,
        isActive: false,
        items: w?.pages?.map((p: any) => ({
          title: p.name,
          url: `/workspace/${w.slug}/page/${p.id}`,
        })),
      })) || []
    );
  });

  // State to track expanded workspaces
  const [expandedWorkspaces, setExpandedWorkspaces] = React.useState<
    Record<string, boolean>
  >({});

  // State to track workspace pages
  const [workspacePages, setWorkspacePages] = React.useState<
    Record<string, any[]>
  >({});

  // Toggle workspace expansion
  const toggleWorkspaceExpansion = (workspaceSlug: string) => {
    setExpandedWorkspaces((prev) => ({
      ...prev,
      [workspaceSlug]: !prev[workspaceSlug],
    }));
  };

  // Check if a workspace or its page is active
  const isWorkspaceActive = (workspaceSlug: string) => {
    return pathname.includes(`/workspace/${workspaceSlug}`);
  };

  // Check if a specific page is active
  const isPageActive = (workspaceSlug: string, pageId: string) => {
    return pathname === `/workspace/${workspaceSlug}/page/${pageId}`;
  };

  // Check if the pages view is active
  const isPagesViewActive = (workspaceSlug: string) => {
    return pathname === `/workspace/${workspaceSlug}/pages`;
  };

  // Initialize workspace pages from the workspace prop
  React.useEffect(() => {
    if (workspace) {
      const newWorkspacePages: Record<string, any[]> = {};

      workspace.forEach((w: any) => {
        if (w.slug && w.pages) {
          newWorkspacePages[w.slug] = w.pages.map((page: any) => ({
            id: page.id,
            name: page.name,
            type: "page",
            createdAt: page.createdAt,
          }));

          // Auto-expand workspace if it's active
          if (isWorkspaceActive(w.slug)) {
            setExpandedWorkspaces((prev) => ({
              ...prev,
              [w.slug]: true,
            }));
          }
        }
      });

      setWorkspacePages(newWorkspacePages);
    }
  }, [workspace, pathname]); // Added pathname as dependency

  return (
    <Sidebar
      className="top-[--header-height] !h-[calc(100svh-var(--header-height))]"
      {...props}
    >
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <div className="px-2">
              <OrganizationSwitcher
                isAdmin={role === "owner" || role === "admin" ? true : false}
              />
            </div>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      {/* Override SidebarContent's default behavior to provide custom scrolling */}
      <div className="flex min-h-0 flex-1 flex-col gap-2 p-2 overflow-hidden">
        {/* Fixed section at the top - not part of scrollable area */}
        <div className="shrink-0">
          <NavProjects projects={data.projects} />
        </div>

        {/* Add separator between fixed and scrollable areas */}
        <div className="mx-2 h-px bg-sidebar-border shrink-0" />

        {/* Label for the workspace section */}
        <div className="flex items-center justify-between px-2 py-1 shrink-0">
          <div className="text-xs font-medium text-sidebar-foreground/70">
            {t("common.workspaces")}
          </div>
          <CreateGuard
            hasPermission={permission?.workspace?.create}
            resource="WORKSPACE"
            fallback={null}
          >
            <a
              href="/workspace/create"
              className="flex items-center justify-center text-sm font-medium text-primary bg-primary/10 hover:bg-primary/20 rounded-md px-2 py-1"
            >
              <span className="mr-1">{t("sidebar.addWorkspace")}</span>
            </a>
          </CreateGuard>
        </div>

        {/* Scrollable area for workspaces - will take remaining space */}
        <div className="overflow-y-auto pr-1 flex-1 min-h-0">
          <div className="h-full">
            <div className="relative flex w-full min-w-0 flex-col">
              {workspaces.length === 0 ? (
                <div className="flex flex-col items-center justify-center py-8 px-4 text-center">
                  <FolderOpen className="size-8 text-muted-foreground mb-2" />
                  <p className="text-sm text-muted-foreground mb-1">
                    {t("sidebar.noWorkspaces")}
                  </p>
                  <p className="text-xs text-muted-foreground/70">
                    {permission?.workspace?.read === false
                      ? t("sidebar.noWorkspacePermission")
                      : t("sidebar.createFirstWorkspace")}
                  </p>
                </div>
              ) : (
                <ul className="flex w-full min-w-0 flex-col gap-1">
                  {workspaces.map((item: any) => {
                    const workspaceSlug =
                      "slug" in item
                        ? (item.slug as string)
                        : item.title.toLowerCase().replace(/\s+/g, "-");
                    const isActive = isWorkspaceActive(workspaceSlug);
                    const isExpanded = expandedWorkspaces[workspaceSlug];
                    // Get pages from workspace pages state
                    const pages = workspacePages[workspaceSlug] || [];

                    return (
                      <li key={item.title} className="group/menu-item relative">
                        <div className="flex items-center">
                          <div className="flex w-full">
                            <Link
                              href={`/workspace/${workspaceSlug}/pages`}
                              className={`flex flex-1 items-center gap-2 overflow-hidden rounded-l-md p-2 text-left text-sm hover:bg-sidebar-accent hover:text-sidebar-accent-foreground ${
                                isActive
                                  ? "bg-sidebar-accent/50 text-sidebar-accent-foreground"
                                  : ""
                              }`}
                            >
                              <item.icon className="size-4 shrink-0" />
                              <div className="flex-1 overflow-hidden">
                                <span
                                  className="overflow-hidden text-ellipsis whitespace-nowrap w-full inline-block"
                                  title={item.title}
                                >
                                  {item.title}
                                </span>
                              </div>
                            </Link>
                            <button
                              onClick={() =>
                                toggleWorkspaceExpansion(workspaceSlug)
                              }
                              className={`flex items-center overflow-hidden rounded-r-md p-2 text-left text-sm hover:bg-sidebar-accent hover:text-sidebar-accent-foreground ${
                                isActive
                                  ? "bg-sidebar-accent/50 text-sidebar-accent-foreground"
                                  : ""
                              }`}
                            >
                              <ChevronRight
                                className={`size-4 shrink-0 transition-transform ${
                                  isExpanded ? "rotate-90" : ""
                                }`}
                              />
                            </button>
                          </div>
                        </div>

                        {isExpanded && (
                          <ul className="ml-6 mt-1 space-y-1">
                            {/* Individual Page Items - Show These First */}
                            {pages.length > 0 &&
                              pages.map((page) => {
                                const pageUrl = `/workspace/${workspaceSlug}/page/${page.id}`;
                                const pageIsActive = isPageActive(
                                  workspaceSlug,
                                  page.id
                                );

                                return (
                                  <li key={page.id}>
                                    <Link
                                      href={page.url || pageUrl}
                                      className={`flex items-center gap-2 rounded-md py-1.5 px-2 text-sm hover:bg-sidebar-accent hover:text-sidebar-accent-foreground ${
                                        pageIsActive
                                          ? "bg-sidebar-accent/50 text-sidebar-accent-foreground"
                                          : ""
                                      } w-full`}
                                    >
                                      <FileText className="size-3.5 shrink-0" />
                                      <div className="w-full overflow-hidden">
                                        <span
                                          className="overflow-hidden text-ellipsis whitespace-nowrap w-full inline-block"
                                          title={page.name}
                                        >
                                          {page.name}
                                        </span>
                                      </div>
                                    </Link>
                                  </li>
                                );
                              })}

                            {/* Pages Management Link At Bottom */}
                            <li key="pages-submenu">
                              <Link
                                href={`/workspace/${workspaceSlug}/pages`}
                                className={`flex items-center gap-2 rounded-md py-1.5 px-2 text-sm hover:bg-sidebar-accent hover:text-sidebar-accent-foreground ${
                                  isPagesViewActive(workspaceSlug)
                                    ? "bg-sidebar-accent/50 text-sidebar-accent-foreground"
                                    : ""
                                } w-full mt-2 border-t border-sidebar-border pt-2`}
                              >
                                <File className="size-3.5 shrink-0" />
                                <div className="w-full overflow-hidden">
                                  <span className="overflow-hidden text-ellipsis whitespace-nowrap w-full inline-block">
                                    {t("sidebar.managePages")}
                                  </span>
                                </div>
                              </Link>
                            </li>
                          </ul>
                        )}
                      </li>
                    );
                  })}
                </ul>
              )}
            </div>
          </div>
        </div>

        {/* Add separator between scrollable and fixed areas */}
        <div className="mx-2 h-px bg-sidebar-border shrink-0" />

        {/* Fixed section at the bottom - not part of scrollable area */}
        <div className="shrink-0 mt-auto">
          <NavSecondary items={languageAwareNavSecondary} />
        </div>
      </div>
      <SidebarFooter>
        <NavUser
          user={{
            name: user?.name ?? user?.email,
            role: user?.role,
            avatar: user?.image,
          }}
        />
      </SidebarFooter>
    </Sidebar>
  );
}
