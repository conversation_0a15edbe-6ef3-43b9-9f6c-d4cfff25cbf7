

resource "azurerm_communication_service" "communication_service" {
  name                = "${var.prefix}-ceruniq-communication-service"
  resource_group_name = var.resource_group_name
  data_location       = var.data_location
}
resource "azurerm_email_communication_service" "email_service" {
  name                = "${var.prefix}-ceruniq-email-communication-service"
  resource_group_name = var.resource_group_name
  data_location       = var.data_location
}
# resource "azurerm_email_communication_service_domain" "custom_domain" {
#   name              = "swissknowledgehub.ch"
#   email_service_id  = azurerm_email_communication_service.email_service.id
#   domain_management = "CustomerManaged"
# }
