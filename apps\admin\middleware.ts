import { NextResponse } from "next/server";
import { getToken } from "next-auth/jwt";
import { NextRequest } from "next/server";

export async function middleware(request: NextRequest) {
  const token = await getToken({
    req: request,
    secret: process.env.NEXTAUTH_SECRET,
  });

  // Allow access to auth-related pages
  if (request.nextUrl.pathname.startsWith("/auth")) {
    // If user is already logged in and trying to access login page, redirect to dashboard
    if (token && request.nextUrl.pathname === "/auth/login") {
      return NextResponse.redirect(new URL("/dashboard", request.url));
    }
    return NextResponse.next();
  }

  // Allow access to API routes
  if (
    request.nextUrl.pathname.startsWith("/api") ||
    request.nextUrl.pathname.startsWith("/swa") ||
    request.nextUrl.pathname.startsWith("/.swa")
  ) {
    return NextResponse.next();
  }

  // Redirect to login if not authenticated
  if (!token) {
    const loginUrl = new URL("/auth/login", request.url);
    // Only set callback URL for valid paths (not webpack chunks or static files)
    const pathname = request.nextUrl.pathname;
    if (!pathname.includes("_next") && !pathname.includes("webpack") && pathname !== "/") {
      loginUrl.searchParams.set("callbackUrl", pathname);
    }
    return NextResponse.redirect(loginUrl);
  }

  // Allow access to all other routes if authenticated
  return NextResponse.next();
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - webpack (webpack chunks)
     * - .swa (Azure Static Web Apps)
     */
    "/((?!api|_next/static|_next/image|favicon.ico|webpack|.swa).*)",
  ],
};
