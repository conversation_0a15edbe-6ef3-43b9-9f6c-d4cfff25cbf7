"""
High-Performance Web Search Tool for CopilotKit Agent

This tool provides optimized web search capabilities with result caching,
intelligent query processing, and source formatting for citations.
"""

import asyncio
import logging
from typing import List, Dict, Any, Optional, Type
from langchain_core.tools import BaseTool
from langchain_core.callbacks import CallbackManagerFor<PERSON><PERSON><PERSON>un
from pydantic import BaseModel, Field
from src.agents.web_search import WebSearchClient, format_web_sources
from src.utils.intelligent_query_interpreter import query_interpreter
import time

logger = logging.getLogger(__name__)

class WebSearchInput(BaseModel):
    """Input schema for web search tool."""
    query: str = Field(description="The search query for web search")
    max_results: int = Field(default=5, description="Maximum number of search results to return")
    include_snippets: bool = Field(default=True, description="Whether to include content snippets")

class WebSearchTool(BaseTool):
    """
    High-performance web search tool that performs intelligent web searches
    with query optimization and result formatting for citations.
    """
    
    name: str = "web_search_tool"
    description: str = """
    Search the web for current information, news, and general knowledge.
    Use this tool when users ask about recent events, current affairs, general knowledge,
    or when the query requires information not available in the knowledge base.
    This tool automatically optimizes search queries and formats results with proper citations.
    """
    args_schema: Type[BaseModel] = WebSearchInput

    # Pydantic v2 requires explicit field definitions
    web_search_client: Any = Field(default=None, exclude=True)
    tenant_id: Optional[str] = Field(default=None, exclude=True)
    user_id: Optional[str] = Field(default=None, exclude=True)
    query_interpreter: Any = Field(default=None, exclude=True)

    def __init__(self, tenant_id: str = None, user_id: str = None, **kwargs):
        super().__init__(**kwargs)
        self.web_search_client = WebSearchClient()
        self.tenant_id = tenant_id
        self.user_id = user_id
        self.query_interpreter = query_interpreter
    
    def _should_use_web_search(self, query: str) -> bool:
        """Determine if the query requires web search."""
        # Keywords that indicate web search is needed
        web_indicators = [
            "current", "recent", "latest", "today", "news", "update",
            "what's happening", "breaking", "trending", "now",
            "2024", "2025", "this year", "this month", "this week"
        ]
        
        query_lower = query.lower()
        return any(indicator in query_lower for indicator in web_indicators)
    
    async def _optimize_search_query(self, query: str, conversation_history: str = None) -> str:
        """Optimize the search query for better web search results."""
        try:
            if self.query_interpreter:
                optimized_query = await self.query_interpreter.interpret_query_for_web_search(
                    query, conversation_history
                )
                if optimized_query and optimized_query != query:
                    logger.info(f"Optimized query: '{query}' -> '{optimized_query}'")
                    return optimized_query
        except Exception as e:
            logger.warning(f"Query optimization failed: {e}")
        
        return query
    
    async def _perform_web_search(self, query: str, max_results: int) -> List[Dict[str, Any]]:
        """Perform the actual web search."""
        try:
            # Use the existing web search client
            results = await self.web_search_client.search(
                query=query,
                tenant_id=self.tenant_id,
                user_id=self.user_id
            )
            
            # Limit results
            if isinstance(results, list):
                return results[:max_results]
            elif isinstance(results, dict) and "results" in results:
                return results["results"][:max_results]
            else:
                logger.warning(f"Unexpected web search result format: {type(results)}")
                return []
                
        except Exception as e:
            logger.error(f"Web search failed: {e}")
            return []
    
    def _format_search_results(self, results: List[Dict[str, Any]], 
                             include_snippets: bool = True) -> str:
        """Format search results for the agent response."""
        if not results:
            return "No web search results found."
        
        # Use the existing format_web_sources function
        formatted_sources = format_web_sources(results)
        
        response_parts = [f"Found {len(results)} web search results:"]
        
        for i, result in enumerate(results, 1):
            title = result.get("title", "Unknown Title")
            url = result.get("link", "") or result.get("url", "")  # Try 'link' first, then 'url'
            snippet = result.get("snippet", "")

            result_text = f"\n[W{i}] {title}"
            if url:
                result_text += f"\nURL: {url}"
            
            if include_snippets and snippet:
                # Limit snippet length
                snippet_preview = snippet[:200] + "..." if len(snippet) > 200 else snippet
                result_text += f"\nSnippet: {snippet_preview}"
            
            response_parts.append(result_text)
        
        return "\n".join(response_parts)
    
    def _extract_key_information(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Extract key information from search results for structured response."""
        if not results:
            return {"summary": "No information found", "sources": []}
        
        # Extract key information
        sources = []
        all_content = []
        
        for result in results:
            source_info = {
                "title": result.get("title", ""),
                "url": result.get("link", "") or result.get("url", ""),  # Try 'link' first, then 'url'
                "snippet": result.get("snippet", "")
            }
            sources.append(source_info)
            
            # Collect content for summary
            if result.get("snippet"):
                all_content.append(result["snippet"])
        
        # Create a brief summary
        summary = " ".join(all_content[:3])  # Use first 3 snippets
        if len(summary) > 300:
            summary = summary[:297] + "..."
        
        return {
            "summary": summary,
            "sources": sources,
            "total_results": len(results)
        }
    
    async def _arun(
        self,
        query: str,
        max_results: int = 5,
        include_snippets: bool = True,
        run_manager: Optional[CallbackManagerForToolRun] = None,
    ) -> str:
        """Async implementation of the web search tool."""
        start_time = time.time()
        
        try:
            logger.info(f"Starting web search for query: '{query}'")
            
            # Check if web search is appropriate for this query
            if not self._should_use_web_search(query):
                logger.info("Query doesn't seem to require web search")
                # Still perform search but with lower priority indication
            
            # Optimize the search query
            optimized_query = await self._optimize_search_query(query)
            
            # Perform web search
            search_results = await self._perform_web_search(optimized_query, max_results)
            
            if not search_results:
                logger.info("No web search results found")
                return "No relevant web search results found for the query."
            
            # Format results for response
            formatted_results = self._format_search_results(search_results, include_snippets)
            
            # Extract key information
            key_info = self._extract_key_information(search_results)
            
            # Create comprehensive response
            response_parts = [
                f"Web search completed for: '{optimized_query}'",
                f"Found {key_info['total_results']} results",
                "",
                formatted_results
            ]
            
            if key_info["summary"]:
                response_parts.extend([
                    "",
                    "Key Information Summary:",
                    key_info["summary"]
                ])
            
            elapsed_time = time.time() - start_time
            logger.info(f"Web search completed in {elapsed_time:.2f} seconds")
            
            return {"content": "\n".join(response_parts), "documents": search_results}
            
        except Exception as e:
            logger.error(f"Error in web search tool: {e}")
            return {
                "content": f"Error performing web search: {str(e)}",
                "documents": []
            }
    
    def _run(
        self,
        query: str,
        max_results: int = 5,
        include_snippets: bool = True,
        run_manager: Optional[CallbackManagerForToolRun] = None,
    ) -> str:
        """Sync wrapper for async implementation."""
        return asyncio.run(self._arun(query, max_results, include_snippets, run_manager))
