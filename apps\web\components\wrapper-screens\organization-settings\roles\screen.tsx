"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useLanguage } from "@/lib/language-context";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  Plus,
  Pencil,
  Trash2,
  Shield,
  Building,
  Users,
  Crown,
  Settings,
  HelpCircle,
  Info,
} from "lucide-react";
import { toast } from "react-hot-toast";
import { getCookie } from "@/utils/cookies";
import { fetchJson } from "@/services";
import { Badge } from "@/components/ui/badge";
import {
  Toolt<PERSON>,
  TooltipContent,
  Too<PERSON><PERSON><PERSON><PERSON><PERSON>,
  TooltipTrigger,
} from "@/components/ui/tooltip";

// Define interfaces for our data structures
interface Permission {
  id: string;
  action: string;
  resource: string;
  description?: string;
}

interface RolePermission {
  permissionId: string;
  permission?: Permission;
}

interface CustomRole {
  id: string;
  name: string;
  description?: string;
  permissions: RolePermission[];
  tenantId: string;
  createdAt?: string;
  updatedAt?: string;
}

interface Organization {
  id: string;
  name: string;
  [key: string]: any;
}

interface RolesSettingsPageProps {
  organization?: Organization;
  userRole?: string;
}

export default function RolesSettingsPage({
  organization,
  userRole,
}: RolesSettingsPageProps) {
  const router = useRouter();
  const { t } = useLanguage();
  const [roles, setRoles] = useState<CustomRole[]>([]);

  const [isLoading, setIsLoading] = useState(true);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [roleToDelete, setRoleToDelete] = useState<CustomRole | null>(null);
  const [builtInRolePermissions, setBuiltInRolePermissions] = useState<Record<string, Permission[]>>({});
  const [allPermissions, setAllPermissions] = useState<Permission[]>([]);
  const [loadingPermissions, setLoadingPermissions] = useState(false);

  const tenantId = organization?.id || getCookie("currentOrganizationId");
  const userId = getCookie("userId");

  // Helper functions for role styling
  const getBuiltInRoleIcon = (role: string) => {
    switch (role) {
      case "OWNER":
        return <Crown className="h-4 w-4" />;
      case "ADMIN":
        return <Shield className="h-4 w-4" />;
      case "MEMBER":
        return <Users className="h-4 w-4" />;
      default:
        return <Settings className="h-4 w-4" />;
    }
  };

  const getBuiltInRoleVariant = (role: string) => {
    switch (role) {
      case "OWNER":
        return "destructive";
      case "ADMIN":
        return "default";
      case "MEMBER":
        return "secondary";
      default:
        return "outline";
    }
  };

  // Fetch built-in role permissions
  const fetchBuiltInRolePermissions = async (roleType: string, availablePermissions: Permission[]) => {
    try {
      const response = await fetchJson(
        `/api/roles/builtin/${roleType.toLowerCase()}?tenantId=${tenantId}`,
        userId,
        tenantId
      );

      console.log(`${roleType} API response:`, response);

      if (response && response.permissions) {
        // Get permission details using the passed availablePermissions
        const permissionDetails = availablePermissions.filter(p =>
          response.permissions.includes(p.id)
        );
        setBuiltInRolePermissions(prev => ({
          ...prev,
          [roleType]: permissionDetails
        }));

        return permissionDetails;
      } else if (response === null) {
        // fetchJson returned null, likely due to an error (403, 404, etc.)
        console.error(`${roleType} API error - likely permission denied`);
        setBuiltInRolePermissions(prev => ({
          ...prev,
          [roleType]: []
        }));
        return [];
      } else {
        setBuiltInRolePermissions(prev => ({
          ...prev,
          [roleType]: []
        }));
        return [];
      }
    } catch (error) {
      console.error(`Error fetching ${roleType} permissions:`, error);

      // Set empty array on error to prevent infinite loading
      setBuiltInRolePermissions(prev => ({
        ...prev,
        [roleType]: []
      }));
      return [];
    }
  };

  // Get informative tooltip content for built-in roles
  const getRoleTooltipContent = (roleType: string) => {
    if (roleType === "OWNER") {
      return t("roles.tooltipMessages.ownersHaveAllPermissions");
    }

    // Check if we're still loading
    if (loadingPermissions || allPermissions.length === 0) {
      return t("roles.tooltipMessages.loadingPermissionDetails");
    }

    const rolePermissions = builtInRolePermissions[roleType];

    // If rolePermissions is undefined, it means we haven't fetched it yet or it failed
    if (rolePermissions === undefined) {
      if (!loadingPermissions) {
        return t("roles.tooltipMessages.permissionDetailsUnavailable");
      }
      return t("roles.tooltipMessages.loadingPermissionDetails");
    }

    const totalPermissions = allPermissions.length;
    const assignedPermissions = rolePermissions.length;

    if (assignedPermissions === 0) {
      return t("roles.tooltipMessages.roleHasNoPermissions", { role: roleType.toLowerCase() });
    }

    // Get key permission examples - prioritize important ones
    const importantPermissions = rolePermissions
      .filter(p => {
        // Prioritize key actions that users care about
        const keyActions = ['CREATE', 'READ', 'UPDATE', 'DELETE', 'MANAGE'];
        const keyResources = ['WORKSPACE', 'FILE', 'MEMBER', 'FOLDER', 'PAGE'];
        return keyActions.includes(p.action) && keyResources.includes(p.resource);
      })
      .slice(0, 3);

    // If we don't have enough important ones, fill with others
    const remainingSlots = 3 - importantPermissions.length;
    const otherPermissions = rolePermissions
      .filter(p => !importantPermissions.includes(p))
      .slice(0, remainingSlots);

    const keyPermissions = [...importantPermissions, ...otherPermissions]
      .map(p => `${p.action.toLowerCase()} ${p.resource.toLowerCase()}`)
      .join(", ");

    const moreCount = assignedPermissions - (importantPermissions.length + otherPermissions.length);
    const moreText = moreCount > 0 ? ` and ${moreCount} more` : "";

    if (assignedPermissions === totalPermissions) {
      return t("roles.tooltipMessages.roleHasAllPermissions", {
        role: roleType.toLowerCase(),
        count: assignedPermissions
      });
    } else {
      return t("roles.tooltipMessages.permissionsIncluding", {
        count: assignedPermissions,
        permissions: keyPermissions,
        more: moreText
      });
    }
  };

  // Get permission summary for a built-in role
  const getPermissionSummary = (roleType: string) => {
    if (roleType === "OWNER") {
      return t("roles.permissionSummary.allPermissions");
    }

    // Check if we're still loading
    if (loadingPermissions || allPermissions.length === 0) {
      return t("roles.permissionSummary.loadingPermissions");
    }

    const rolePermissions = builtInRolePermissions[roleType];

    // If rolePermissions is undefined, it means we haven't fetched it yet or it failed
    if (rolePermissions === undefined) {
      // If we're not loading anymore but still undefined, it failed to load
      if (!loadingPermissions) {
        // Add specific debugging for admin users
        if (userRole === "ADMIN") {
          console.warn(`[${roleType}] Admin user cannot see permissions - this should not happen after the fix`);
          console.warn(`[${roleType}] Debug info:`, {
            userRole,
            loadingPermissions,
            allPermissionsCount: allPermissions.length,
            builtInRolePermissions: Object.keys(builtInRolePermissions),
            rolePermissions
          });
        }
        return t("roles.permissionSummary.failedToLoad");
      }
      return t("roles.permissionSummary.loadingPermissions");
    }

    const totalPermissions = allPermissions.length;
    const assignedPermissions = rolePermissions.length;

    if (assignedPermissions === 0) {
      return t("roles.permissionSummary.noPermissionsAssigned");
    } else if (assignedPermissions === totalPermissions) {
      return t("roles.permissionSummary.allPermissions");
    } else {
      return `${assignedPermissions} ${t("roles.permissionsCount")}`;
    }
  };

  // Define all possible built-in roles
  const allBuiltInRoles = [
    {
      name: t("roles.builtInRoleNames.owner"),
      description: t("roles.builtInRoleDescriptions.owner"),
      role: "OWNER",
    },
    {
      name: t("roles.builtInRoleNames.admin"),
      description: t("roles.builtInRoleDescriptions.admin"),
      role: "ADMIN",
    },
    {
      name: t("roles.builtInRoleNames.member"),
      description: t("roles.builtInRoleDescriptions.member"),
      role: "MEMBER",
    },
  ];

  // Filter built-in roles based on user role and what's configured
  const builtInRoles = allBuiltInRoles.filter(role => {
    if (role.role === "OWNER") return true; // Always show Owner
    if (role.role === "ADMIN") return true; // Always show Admin
    if (role.role === "MEMBER") {
      // For OWNER and ADMIN users, always show Member role
      if (userRole === "OWNER" || userRole === "ADMIN") {
        return true;
      }
      // For other users, show Member only if it has permissions configured or if we're still loading
      return builtInRolePermissions[role.role] !== undefined || loadingPermissions;
    }
    return false;
  });

  // Fetch roles and permissions
  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      setLoadingPermissions(true);
      try {
        // Fetch all permissions first
        console.log("Fetching all permissions...");
        const permissionsResponse = await fetch('/api/permissions');
        let availablePermissions: Permission[] = [];

        if (permissionsResponse.ok) {
          const permData = await permissionsResponse.json();
          availablePermissions = permData.permissions || [];

          // Remove duplicates if any exist
          availablePermissions = availablePermissions.filter((permission, index, self) =>
            index === self.findIndex(p => p.action === permission.action && p.resource === permission.resource)
          );

          setAllPermissions(availablePermissions);
          console.log("All permissions fetched:", availablePermissions);
          console.log("Total unique permissions:", availablePermissions.length);
          console.log("Permission breakdown:", availablePermissions.map(p => `${p.action} ${p.resource}`));
        }

        // Fetch roles
        const rolesResponse = await fetchJson(
          `/api/roles?tenantId=${tenantId}`,
          userId,
          tenantId
        );

        if (rolesResponse && rolesResponse.customRoles) {
          setRoles(rolesResponse.customRoles);
        } else {
          setRoles([]);
        }

        // Fetch built-in role permissions for ADMIN and MEMBER (for both OWNER and ADMIN users)
        if ((userRole === "OWNER" || userRole === "ADMIN") && availablePermissions.length > 0) {
          console.log("Fetching built-in role permissions...");
          await Promise.all([
            fetchBuiltInRolePermissions("ADMIN", availablePermissions),
            fetchBuiltInRolePermissions("MEMBER", availablePermissions)
          ]);
        }
      } catch (error) {
        console.error("Error fetching data:", error);
        toast.error(t("roles.errorMessages.failedToLoadRolesAndPermissions"));
      } finally {
        setIsLoading(false);
        setLoadingPermissions(false);
      }
    };

    if (tenantId) {
      fetchData();
    }
  }, [tenantId, userRole]);

  // Handle role deletion
  const handleDeleteRole = async () => {
    try {
      if (!roleToDelete) {
        toast.error(t("roles.errorMessages.noRoleSelectedForDeletion"));
        return;
      }

      const response = await fetch(`/api/roles/${roleToDelete.id}`, {
        method: "DELETE",
      });

      if (response.ok) {
        setRoles(roles.filter((role) => role.id !== roleToDelete.id));
        setDeleteDialogOpen(false);
        toast.success(t("roles.errorMessages.roleDeletedSuccessfully"));
      } else {
        const error = await response.json();
        toast.error(error.error || t("roles.errorMessages.failedToDeleteRole"));
      }
    } catch (error) {
      console.error("Error deleting role:", error);
      toast.error(t("roles.errorMessages.failedToDeleteRole"));
    }
  };

  // Only owners can manage roles
  if (userRole !== "OWNER" && userRole !== "ADMIN") {
    return (
      <div className="container py-6">
        <h1 className="text-2xl font-bold mb-6">{t("roles.title")}</h1>
        <p>{t("roles.ownerOnly")}</p>
      </div>
    );
  }

  return (
    <TooltipProvider>
      <div className="space-y-6">
        {/* Header Section */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-primary/10 rounded-lg">
              <Shield className="h-6 w-6 text-primary" />
            </div>
            <div>
              <h1 className="text-2xl font-bold">{t("roles.title")}</h1>
              <p className="text-muted-foreground">
                {t("roles.rolesDescription")}
              </p>
            </div>
          </div>
          <Button onClick={() => router.push("/settings/roles/create")}>
            <Plus className="mr-2 h-4 w-4" />
            {t("roles.createRole")}
          </Button>
        </div>

        {/* Built-in Roles Section */}
        <Card>
          <CardHeader>
            <div className="flex items-center gap-2">
              <Building className="h-5 w-5" />
              <CardTitle>{t("roles.builtInRoles")}</CardTitle>
              <Tooltip>
                <TooltipTrigger>
                  <HelpCircle className="h-4 w-4 text-muted-foreground" />
                </TooltipTrigger>
                <TooltipContent>
                  <p className="max-w-xs">{t("roles.builtInRolesTooltip")}</p>
                </TooltipContent>
              </Tooltip>
            </div>
            <CardDescription>
              {t("roles.builtInRolesDescription")}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 sm:grid-cols-1 md:grid-cols-1 lg:grid-cols-3">
              {builtInRoles.map((role) => (
                <Card key={role.role} className="border-2 group hover:border-primary/50 hover:shadow-sm transition-all duration-200">
                  <CardContent className="p-4">
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex items-start gap-2 flex-1 min-w-0">
                        <div className="flex-shrink-0 mt-0.5">
                          {getBuiltInRoleIcon(role.role)}
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-1 flex-wrap">
                            <h3 className="font-semibold text-sm">{role.name}</h3>
                            <Badge variant={getBuiltInRoleVariant(role.role)} className="text-xs">
                              {t("roles.builtIn")}
                            </Badge>
                          </div>
                        </div>
                      </div>
                      {/* Only show edit button for ADMIN and MEMBER roles, and only for owners */}
                      {userRole === "OWNER" && (role.role === "ADMIN" || role.role === "MEMBER") && (
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => router.push(`/settings/roles/edit-builtin/${role.role.toLowerCase()}`)}
                              className="flex-shrink-0"
                            >
                              <Pencil className="h-3 w-3" />
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>{t("roles.editRolePermissions", { role: role.name })}</p>
                          </TooltipContent>
                        </Tooltip>
                      )}
                    </div>
                    <p className="text-sm text-muted-foreground mb-3">
                      {role.description}
                    </p>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <span className="text-xs text-muted-foreground">
                          <strong>{t("roles.permissions")}:</strong>
                        </span>
                        <span className="text-xs font-medium">
                          {getPermissionSummary(role.role)}
                        </span>
                      </div>
                      {/* Consistent info tooltip for all roles */}
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button variant="ghost" size="sm" className="h-6 w-6 p-0 hover:bg-muted">
                            <Info className="h-3 w-3" />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent className="max-w-sm">
                          <p className="text-sm">{getRoleTooltipContent(role.role)}</p>
                        </TooltipContent>
                      </Tooltip>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Custom Roles Section */}
        <Card>
          <CardHeader>
            <div className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              <CardTitle>{t("roles.customRoles")}</CardTitle>
              <Tooltip>
                <TooltipTrigger>
                  <HelpCircle className="h-4 w-4 text-muted-foreground" />
                </TooltipTrigger>
                <TooltipContent>
                  <p className="max-w-xs">{t("roles.customRolesTooltip")}</p>
                </TooltipContent>
              </Tooltip>
            </div>
            <CardDescription>
              {t("roles.customRolesDescription")}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="flex items-center justify-center py-12">
                <div className="text-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
                  <p className="text-muted-foreground">{t("common.loading")}</p>
                </div>
              </div>
            ) : roles.length === 0 ? (
              <div className="flex flex-col items-center justify-center py-12">
                <div className="bg-muted/50 p-3 rounded-full mb-4">
                  <Settings className="h-8 w-8 text-muted-foreground" />
                </div>
                <h3 className="text-lg font-medium mb-2">
                  {t("roles.noRoles")}
                </h3>
                <p className="text-muted-foreground text-center mb-6 max-w-md">
                  {t("roles.noRolesDescription")}
                </p>
                <Button onClick={() => router.push("/settings/roles/create")}>
                  <Plus className="mr-2 h-4 w-4" />
                  {t("roles.createRole")}
                </Button>
              </div>
            ) : (
              <div className="grid gap-4 sm:grid-cols-1 md:grid-cols-1 lg:grid-cols-3">
                {roles.map((role) => (
                  <Card
                    key={role.id}
                    className="group border hover:border-primary/50 hover:shadow-sm transition-all duration-200 cursor-pointer"
                    onClick={() =>
                      router.push(`/settings/roles/edit/${role.id}`)
                    }
                  >
                    <CardContent className="p-4">
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex items-start gap-3 flex-1 min-w-0">
                          <div className="p-2 bg-gradient-to-br from-primary/10 to-primary/5 rounded-lg border border-primary/20 group-hover:border-primary/30 transition-colors flex-shrink-0">
                            <Settings className="h-4 w-4 text-primary" />
                          </div>
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-2 mb-1 flex-wrap">
                              <h3 className="font-semibold text-sm text-foreground group-hover:text-primary transition-colors truncate">
                                {role.name}
                              </h3>
                            </div>
                            <Badge
                              variant="secondary"
                              className="bg-primary/10 text-primary border-primary/20 hover:bg-primary/15 text-xs px-1.5 py-0.5 mb-2"
                            >
                              {t("roles.custom")}
                            </Badge>
                          </div>
                        </div>
                        <div className="flex items-center gap-1 flex-shrink-0">
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  router.push(
                                    `/settings/roles/edit/${role.id}`
                                  );
                                }}
                                className="h-8 w-8 p-0"
                              >
                                <Pencil className="h-3 w-3" />
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>{t("roles.editRole")}</p>
                            </TooltipContent>
                          </Tooltip>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  setRoleToDelete(role);
                                  setDeleteDialogOpen(true);
                                }}
                                className="h-8 w-8 p-0"
                              >
                                <Trash2 className="h-3 w-3" />
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>{t("roles.deleteRole")}</p>
                            </TooltipContent>
                          </Tooltip>
                        </div>
                      </div>

                      <p className="text-xs text-muted-foreground mb-3 line-clamp-2">
                        {role.description || t("roles.noDescription")}
                      </p>

                      <div className="flex items-center justify-between text-xs text-muted-foreground mb-3 flex-wrap gap-2">
                        <div className="flex items-center gap-1">
                          <div className="w-1.5 h-1.5 rounded-full bg-primary/60"></div>
                          <span>
                            {role.permissions.length}{" "}
                            {role.permissions.length === 1
                              ? t("roles.permission")
                              : t("roles.permissionsCount")}
                          </span>
                        </div>
                        {role.createdAt && (
                          <span className="text-xs">
                            {new Date(role.createdAt).toLocaleDateString()}
                          </span>
                        )}
                      </div>

                      {/* Permission Preview - Compact */}
                      {role.permissions.length > 0 && (
                        <div className="pt-3 border-t border-border/50">
                          <div className="flex flex-wrap gap-1">
                            {role.permissions
                              .filter((perm) => perm.permission?.action && perm.permission?.resource)
                              .slice(0, 2)
                              .map((perm, index) => (
                              <Badge
                                key={index}
                                variant="outline"
                                className="text-xs px-1.5 py-0.5 bg-muted/50 border-muted-foreground/20 h-5"
                              >
                                {t(`roles.actions.${perm.permission?.action}`)}{" "}
                                {t(`roles.resources.${perm.permission?.resource}`)}
                              </Badge>
                            ))}
                            {role.permissions.filter((perm) => perm.permission?.action && perm.permission?.resource).length > 2 && (
                              <Badge
                                variant="outline"
                                className="text-xs px-1.5 py-0.5 bg-muted/30 border-muted-foreground/20 h-5"
                              >
                                +{role.permissions.filter((perm) => perm.permission?.action && perm.permission?.resource).length - 2}
                              </Badge>
                            )}
                          </div>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Delete Role Confirmation */}
        <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>{t("roles.deleteRoleTitle")}</AlertDialogTitle>
              <AlertDialogDescription>
                {t("roles.deleteRoleDescription")}
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>{t("common.cancel")}</AlertDialogCancel>
              <AlertDialogAction onClick={handleDeleteRole}>
                {t("common.delete")}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>
    </TooltipProvider>
  );
}
