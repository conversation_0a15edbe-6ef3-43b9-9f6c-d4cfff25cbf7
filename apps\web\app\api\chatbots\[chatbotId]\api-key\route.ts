import { NextRequest, NextResponse } from "next/server";
import db from "@/lib/shared-db";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/next-auth";
import crypto from "crypto";

// Generate a secure API key
function generateApiKey(): string {
  const prefix = 'skh_';
  const randomBytes = crypto.randomBytes(32).toString('hex');
  return prefix + randomBytes;
}

// POST - Generate new API key
export async function POST(
  req: Request,
  { params }: { params: { chatbotId: string } }
) {
  try {
    const session: any = await getServerSession(authOptions);
    if (!session) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const { tenantId } = await req.json();

    if (!tenantId) {
      return NextResponse.json(
        { error: "Tenant ID is required" },
        { status: 400 }
      );
    }

    // Check if chatbot exists and user has access
    const existingChatbot = await db.chatbot.findUnique({
      where: {
        id: params.chatbotId,
      },
    });

    if (!existingChatbot) {
      return NextResponse.json({ error: "Chatbot not found" }, { status: 404 });
    }

    // Verify user has access to this chatbot
    if (existingChatbot.userId !== session.userId || existingChatbot.tenantId !== tenantId) {
      return NextResponse.json({ error: "Access denied" }, { status: 403 });
    }

    // Check if API key already exists
    if (existingChatbot.apiKey) {
      return NextResponse.json(
        { error: "API key already exists. Use PUT to regenerate." },
        { status: 409 }
      );
    }

    // Generate new API key
    const apiKey = generateApiKey();

    // Update chatbot with new API key
    const updatedChatbot = await db.chatbot.update({
      where: {
        id: params.chatbotId,
      },
      data: {
        apiKey: apiKey,
      },
    });

    return NextResponse.json({ apiKey: updatedChatbot.apiKey });

  } catch (error) {
    console.error("[CHATBOT_API_KEY_POST]", error);
    return NextResponse.json({ error: "Internal Error" }, { status: 500 });
  }
}

// PUT - Regenerate existing API key
export async function PUT(
  req: Request,
  { params }: { params: { chatbotId: string } }
) {
  try {
    const session: any = await getServerSession(authOptions);
    if (!session) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const { tenantId } = await req.json();

    if (!tenantId) {
      return NextResponse.json(
        { error: "Tenant ID is required" },
        { status: 400 }
      );
    }

    // Check if chatbot exists and user has access
    const existingChatbot = await db.chatbot.findUnique({
      where: {
        id: params.chatbotId,
      },
    });

    if (!existingChatbot) {
      return NextResponse.json({ error: "Chatbot not found" }, { status: 404 });
    }

    // Verify user has access to this chatbot
    if (existingChatbot.userId !== session.userId || existingChatbot.tenantId !== tenantId) {
      return NextResponse.json({ error: "Access denied" }, { status: 403 });
    }

    // Generate new API key
    const apiKey = generateApiKey();

    // Update chatbot with new API key
    const updatedChatbot = await db.chatbot.update({
      where: {
        id: params.chatbotId,
      },
      data: {
        apiKey: apiKey,
      },
    });

    return NextResponse.json({ apiKey: updatedChatbot.apiKey });

  } catch (error) {
    console.error("[CHATBOT_API_KEY_PUT]", error);
    return NextResponse.json({ error: "Internal Error" }, { status: 500 });
  }
}

// DELETE - Remove API key (disable SDK access)
export async function DELETE(
  req: Request,
  { params }: { params: { chatbotId: string } }
) {
  try {
    const session: any = await getServerSession(authOptions);
    if (!session) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const url = new URL(req.url);
    const tenantId = url.searchParams.get('tenantId');

    if (!tenantId) {
      return NextResponse.json(
        { error: "Tenant ID is required" },
        { status: 400 }
      );
    }

    // Check if chatbot exists and user has access
    const existingChatbot = await db.chatbot.findUnique({
      where: {
        id: params.chatbotId,
      },
    });

    if (!existingChatbot) {
      return NextResponse.json({ error: "Chatbot not found" }, { status: 404 });
    }

    // Verify user has access to this chatbot
    if (existingChatbot.userId !== session.userId || existingChatbot.tenantId !== tenantId) {
      return NextResponse.json({ error: "Access denied" }, { status: 403 });
    }

    // Remove API key
    await db.chatbot.update({
      where: {
        id: params.chatbotId,
      },
      data: {
        apiKey: null,
      },
    });

    return NextResponse.json({ message: "API key removed successfully" });

  } catch (error) {
    console.error("[CHATBOT_API_KEY_DELETE]", error);
    return NextResponse.json({ error: "Internal Error" }, { status: 500 });
  }
}
