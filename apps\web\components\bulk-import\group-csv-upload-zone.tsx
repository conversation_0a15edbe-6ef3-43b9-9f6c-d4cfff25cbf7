"use client";

import React, { useCallback, useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Upload, FileText, Download, AlertCircle, X, Info } from "lucide-react";
import { cn } from "@/lib/utils";
import { useLanguage } from "@/lib/language-context";
import { generateGroupCSVTemplate } from "@/lib/utils/group-csv-parser";

interface Group {
  id: string;
  name: string;
  description?: string;
}

interface GroupCSVUploadZoneProps {
  onFileSelect: (file: File) => void;
  tenantId: string;
  isLoading?: boolean;
  error?: string;
  className?: string;
}

export function GroupCSVUploadZone({
  onFileSelect,
  tenantId,
  isLoading = false,
  error,
  className
}: GroupCSVUploadZoneProps) {
  const { t } = useLanguage();
  const [isDragOver, setIsDragOver] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [groups, setGroups] = useState<Group[]>([]);
  const [loadingGroups, setLoadingGroups] = useState(false);

  // Fetch groups on component mount
  useEffect(() => {
    const fetchGroups = async () => {
      setLoadingGroups(true);
      try {
        const response = await fetch(`/api/groups?tenantId=${tenantId}`);
        if (response.ok) {
          const data = await response.json();
          setGroups(data.data || []);
        }
      } catch (error) {
        console.error('Failed to fetch groups:', error);
      } finally {
        setLoadingGroups(false);
      }
    };

    fetchGroups();
  }, [tenantId]);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(false);
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(false);

    const files = Array.from(e.dataTransfer.files);
    const csvFile = files.find(file => 
      file.type === 'text/csv' || 
      file.name.toLowerCase().endsWith('.csv')
    );

    if (csvFile) {
      setSelectedFile(csvFile);
      onFileSelect(csvFile);
    }
  }, [onFileSelect]);

  const handleFileInput = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setSelectedFile(file);
      onFileSelect(file);
    }
  }, [onFileSelect]);

  const handleDownloadTemplate = useCallback(() => {
    const csvContent = generateGroupCSVTemplate(groups);
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = 'group-member-import-template.csv';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }, [groups]);

  const handleRemoveFile = useCallback(() => {
    setSelectedFile(null);
  }, []);

  return (
    <div className={cn("space-y-4", className)}>
      {/* Upload Zone */}
      <Card 
        className={cn(
          "border-2 border-dashed transition-all duration-200",
          isDragOver && "border-primary bg-primary/5",
          error && "border-destructive",
          "hover:border-primary/50"
        )}
      >
        <CardContent className="p-8">
          <div
            className="text-center space-y-4"
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
          >
            {selectedFile ? (
              // File Selected State
              <div className="space-y-4">
                <div className="flex items-center justify-center gap-3">
                  <FileText className="h-8 w-8 text-primary" />
                  <div className="text-left">
                    <p className="font-medium text-sm">{selectedFile.name}</p>
                    <p className="text-xs text-muted-foreground">
                      {(selectedFile.size / 1024).toFixed(1)} KB
                    </p>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleRemoveFile}
                    className="ml-2"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
                
                {error && (
                  <div className="flex items-center gap-2 text-destructive text-sm">
                    <AlertCircle className="h-4 w-4" />
                    <span>{error}</span>
                  </div>
                )}
              </div>
            ) : (
              // Upload State
              <div className="space-y-4">
                <div className="flex justify-center">
                  <div className="rounded-full bg-primary/10 p-4">
                    <Upload className="h-8 w-8 text-primary" />
                  </div>
                </div>
                
                <div className="space-y-2">
                  <h3 className="font-semibold text-lg">
                    {t("bulkImport.upload.title")}
                  </h3>
                  <p className="text-muted-foreground text-sm">
                    {t("bulkImport.upload.description")}
                  </p>
                </div>

                <div className="space-y-3">
                  <input
                    type="file"
                    accept=".csv,text/csv"
                    onChange={handleFileInput}
                    className="hidden"
                    id="group-csv-upload"
                    disabled={isLoading}
                  />
                  <label htmlFor="group-csv-upload">
                    <Button 
                      variant="outline" 
                      className="cursor-pointer"
                      disabled={isLoading}
                      asChild
                    >
                      <span>
                        <FileText className="h-4 w-4 mr-2" />
                        {t("bulkImport.upload.chooseFile")}
                      </span>
                    </Button>
                  </label>

                  <div className="text-xs text-muted-foreground">
                    {t("bulkImport.upload.supportedFormat")}
                  </div>
                </div>

                {error && (
                  <div className="flex items-center gap-2 text-destructive text-sm">
                    <AlertCircle className="h-4 w-4" />
                    <span>{error}</span>
                  </div>
                )}
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Template Download */}
      <div className="flex items-center justify-between p-4 bg-muted/50 rounded-lg">
        <div className="space-y-1">
          <p className="text-sm font-medium">{t("bulkImport.upload.template.title")}</p>
          <p className="text-xs text-muted-foreground">
            {t("bulkImport.upload.template.description")}
          </p>
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={handleDownloadTemplate}
          className="gap-2"
        >
          <Download className="h-4 w-4" />
          {t("bulkImport.upload.template.downloadButton")}
        </Button>
      </div>

      {/* Format Instructions */}
      <Card>
        <CardContent className="p-4">
          <h4 className="font-medium text-sm mb-3">{t("bulkImport.upload.formatRequirements.title")}</h4>
          <div className="space-y-2 text-xs text-muted-foreground">
            <div className="flex items-start gap-2">
              <span className="font-medium text-foreground">
                {t("bulkImport.upload.formatRequirements.requiredColumns")}
              </span>
              <span>email</span>
            </div>
            <div className="flex items-start gap-2">
              <span className="font-medium text-foreground">
                {t("bulkImport.upload.formatRequirements.groupIdentifier")}
              </span>
              <span>{t("bulkImport.upload.formatRequirements.groupIdentifierValue")}</span>
            </div>
            <div className="flex items-start gap-2">
              <span className="font-medium text-foreground">{t("bulkImport.upload.formatRequirements.note")}</span>
              <span>
                {t("bulkImport.upload.formatRequirements.noteValue")}
              </span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Groups Reference */}
      {groups.length > 0 && (
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2 mb-3">
              <Info className="h-4 w-4 text-blue-600" />
              <h4 className="font-medium text-sm">Available Groups:</h4>
            </div>
            <div className="space-y-2">
              {groups.map((group) => (
                <div
                  key={group.id}
                  className="flex items-center justify-between p-2 bg-muted/50 rounded text-xs"
                >
                  <div>
                    <span className="font-medium">{group.name}</span>
                    {group.description && (
                      <span className="text-muted-foreground ml-2">
                        - {group.description}
                      </span>
                    )}
                  </div>
                  <code className="bg-background px-2 py-1 rounded text-xs font-mono">
                    {group.id}
                  </code>
                </div>
              ))}
            </div>
            <p className="text-xs text-muted-foreground mt-3">
              Use either the group name or ID in your CSV file.
            </p>
          </CardContent>
        </Card>
      )}

      {loadingGroups && (
        <Card>
          <CardContent className="p-4 text-center">
            <div className="flex items-center justify-center gap-2 text-sm text-muted-foreground">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
              Loading groups...
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
