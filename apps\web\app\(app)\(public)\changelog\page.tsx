"use client";

import React, { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import Image from "next/image";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import {
  ChevronLeft,
  ChevronRight,
  ExternalLink,
  GitCommit,
} from "lucide-react";
import { useLanguage } from "@/lib/language-context";
import { useThemeConfig } from "@/lib/theme-config-context";
import ReactMarkdown from "react-markdown";
import { CodeBlock } from "@/components/wrapper-screens/chat/components/CodeBlock";

interface Changelog {
  id: string;
  title: string;
  content: string;
  version?: string;
  type: "RELEASE" | "HOTFIX" | "MAINTENANCE" | "ANNOUNCEMENT";
  priority: "LOW" | "MEDIUM" | "HIGH" | "CRITICAL";
  publishedAt: string;
  isViewed?: boolean;
  authorName?: string;
  githubCommitSha?: string;
  deploymentId?: string;
}

interface PaginationInfo {
  page: number;
  limit: number;
  totalCount: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

const ChangelogPage: React.FC = () => {
  const { data: session } = useSession();
  const { t } = useLanguage();
  const { themeConfig } = useThemeConfig();

  const [changelogs, setChangelogs] = useState<Changelog[]>([]);
  const [pagination, setPagination] = useState<PaginationInfo | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedFilter, setSelectedFilter] = useState<string>("");
  const [searchQuery, setSearchQuery] = useState("");

  // Initialize filter with translated default on first render
  React.useEffect(() => {
    if (selectedFilter === "") {
      setSelectedFilter(t("changelog.page.allPublications"));
    }
  }, [selectedFilter, t]);

  const filterCategories = [
    { name: t("changelog.page.allPublications"), count: changelogs.length },
    {
      name: t("changelog.announcement"),
      count: changelogs.filter((c) => c.type === "ANNOUNCEMENT").length,
    },
    {
      name: t("changelog.page.bugFix"),
      count: changelogs.filter((c) => c.type === "HOTFIX").length,
    },
    {
      name: t("changelog.page.improvement"),
      count: changelogs.filter((c) => c.type === "MAINTENANCE").length,
    },
    {
      name: t("changelog.page.newFeature"),
      count: changelogs.filter((c) => c.type === "RELEASE").length,
    },
  ];

  const filteredChangelogs = changelogs.filter((changelog) => {
    const matchesFilter =
      selectedFilter === t("changelog.page.allPublications") ||
      selectedFilter === "" ||
      (selectedFilter === t("changelog.announcement") &&
        changelog.type === "ANNOUNCEMENT") ||
      (selectedFilter === t("changelog.page.bugFix") &&
        changelog.type === "HOTFIX") ||
      (selectedFilter === t("changelog.page.improvement") &&
        changelog.type === "MAINTENANCE") ||
      (selectedFilter === t("changelog.page.newFeature") &&
        changelog.type === "RELEASE");

    const matchesSearch =
      searchQuery === "" ||
      changelog.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      changelog.content.toLowerCase().includes(searchQuery.toLowerCase());

    return matchesFilter && matchesSearch;
  });

  const fetchChangelogs = async (page: number = 1) => {
    setIsLoading(true);
    setError(null);

    try {
      // Get tenant ID from session using the correct structure
      const tenantId =
        (session as any)?.memberships?.[0]?.tenant?.id || "default";
      const environment = process.env.NODE_ENV;

      const response = await fetch(
        `/api/changelog/public?tenantId=${tenantId}&environment=${environment}&page=${page}&limit=10`
      );

      if (!response.ok) {
        throw new Error(`Failed to fetch changelogs: ${response.statusText}`);
      }

      const data = await response.json();
      setChangelogs(data.data || []);
      setPagination(data.pagination);
      setCurrentPage(page);
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "Failed to fetch changelogs";
      setError(errorMessage);
      console.error("Error fetching changelogs:", err);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchChangelogs(1);
  }, [session]);

  useEffect(() => {
    // Enable scrolling for this page by overriding global styles
    document.body.style.overflow = "auto";
    document.documentElement.style.overflow = "auto";
    document.body.style.height = "auto";
    document.documentElement.style.height = "auto";

    // Cleanup function to restore original styles when leaving the page
    return () => {
      document.body.style.overflow = "";
      document.documentElement.style.overflow = "";
      document.body.style.height = "";
      document.documentElement.style.height = "";
    };
  }, []);

  const handlePageChange = (page: number) => {
    fetchChangelogs(page);
  };

  const getTypeBadgeStyle = (type: string) => {
    switch (type) {
      case "RELEASE":
        return {
          backgroundColor: "hsl(var(--primary) / 0.1)",
          color: "hsl(var(--primary))",
          borderColor: "hsl(var(--primary) / 0.2)",
        };
      case "HOTFIX":
        return {
          backgroundColor: "hsl(var(--destructive) / 0.1)",
          color: "hsl(var(--destructive))",
          borderColor: "hsl(var(--destructive) / 0.2)",
        };
      case "MAINTENANCE":
        return {
          backgroundColor: "hsl(var(--accent) / 0.1)",
          color: "hsl(var(--accent))",
          borderColor: "hsl(var(--accent) / 0.2)",
        };
      case "ANNOUNCEMENT":
        return {
          backgroundColor: "hsl(var(--secondary) / 0.1)",
          color: "hsl(var(--secondary))",
          borderColor: "hsl(var(--secondary) / 0.2)",
        };
      default:
        return {
          backgroundColor: "hsl(var(--muted))",
          color: "hsl(var(--muted-foreground))",
          borderColor: "hsl(var(--border))",
        };
    }
  };

  const getTypeLabel = (type: string) => {
    switch (type) {
      case "RELEASE":
        return t("changelog.page.newFeatureLabel");
      case "HOTFIX":
        return t("changelog.page.bugFixLabel");
      case "MAINTENANCE":
        return t("changelog.page.improvementLabel");
      case "ANNOUNCEMENT":
        return t("changelog.page.announcementLabel");
      default:
        return type;
    }
  };

  const getRelativeTime = (date: string) => {
    const now = new Date();
    const publishedDate = new Date(date);
    const diffInDays = Math.floor(
      (now.getTime() - publishedDate.getTime()) / (1000 * 60 * 60 * 24)
    );

    if (diffInDays === 0) return t("changelog.page.today");
    if (diffInDays === 1) return t("changelog.page.dayAgo");
    if (diffInDays < 7)
      return t("changelog.page.daysAgo", { count: diffInDays });
    if (diffInDays < 30) {
      const weeks = Math.floor(diffInDays / 7);
      return weeks === 1
        ? t("changelog.page.weekAgo")
        : t("changelog.page.weeksAgo", { count: weeks });
    }
    if (diffInDays < 365) {
      const months = Math.floor(diffInDays / 30);
      return months === 1
        ? t("changelog.page.monthAgo")
        : t("changelog.page.monthsAgo", { count: months });
    }
    const years = Math.floor(diffInDays / 365);
    return years === 1
      ? t("changelog.page.yearAgo")
      : t("changelog.page.yearsAgo", { count: years });
  };

  if (error) {
    return (
      <div
        className="min-h-screen"
        style={{ backgroundColor: "hsl(var(--background))" }}
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center py-20">
            <div
              className="rounded-lg p-8 max-w-md mx-auto"
              style={{
                backgroundColor: "hsl(var(--card))",
                borderColor: "hsl(var(--border))",
                border: "1px solid",
              }}
            >
              <ExternalLink className="h-12 w-12 text-red-500 dark:text-red-400 mx-auto mb-4" />
              <h1
                className="text-2xl font-bold mb-2"
                style={{ color: "hsl(var(--card-foreground))" }}
              >
                {t("changelog.page.somethingWentWrong")}
              </h1>
              <p
                className="mb-6"
                style={{ color: "hsl(var(--muted-foreground))" }}
              >
                {error}
              </p>
              <Button
                onClick={() => fetchChangelogs(currentPage)}
                className="bg-blue-600 hover:bg-blue-700 text-white"
              >
                {t("changelog.page.tryAgain")}
              </Button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div
      className="min-h-screen overflow-x-hidden overflow-y-auto"
      style={{
        height: "auto",
        minHeight: "100vh",
        backgroundColor: "hsl(var(--background))",
        color: "hsl(var(--foreground))",
      }}
    >
      {/* Header */}
      <div
        className="border-b"
        style={{
          backgroundColor: "hsl(var(--navigation-background, var(--card)))",
          borderColor: "hsl(var(--border))",
        }}
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <div className="inline-flex items-center justify-center rounded-lg mb-4">
              <Image
                src={
                  themeConfig?.fullAppLogoUrl ||
                  `https://framerusercontent.com/images/ZW6c6IiU3fbnGPQPlBTKbBZomHk.svg`
                }
                alt={t("workspace.appName")}
                width={themeConfig?.fullAppLogoUrl ? 100 : 18}
                height={themeConfig?.fullAppLogoUrl ? 100 : 18}
                className="h-full w-full"
              />
            </div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
              {t("changelog.page.title")}
            </h1>
            <p className="text-lg text-gray-600 dark:text-gray-400 mb-4">
              {t("changelog.page.subtitle")}
            </p>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              <a
                href="https://www.swissknowledgehub.ch"
                target="_blank"
                rel="noopener noreferrer"
              >
                swissknowledgehub.ch
              </a>
            </p>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex flex-col lg:flex-row gap-8">
          {/* Sidebar */}
          <div className="w-full lg:w-80 flex-shrink-0">
            <div
              className="rounded-lg p-6"
              style={{
                backgroundColor: "hsl(var(--card))",
                borderColor: "hsl(var(--border))",
                border: "1px solid",
              }}
            >
              {/* Search */}
              <div className="mb-6">
                <div className="relative">
                  <input
                    type="text"
                    placeholder={t("changelog.page.search")}
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="w-full pl-4 pr-10 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                  <button className="absolute right-3 top-1/2 transform -translate-y-1/2">
                    <svg
                      className="h-4 w-4 text-gray-400"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                      />
                    </svg>
                  </button>
                </div>
              </div>

              {/* Filters */}
              <div>
                <h3 className="text-sm font-medium text-gray-900 dark:text-white mb-3">
                  {t("changelog.page.filterByCategory")}
                </h3>
                <div className="space-y-2">
                  {filterCategories.map((category) => (
                    <button
                      key={category.name}
                      onClick={() => setSelectedFilter(category.name)}
                      className={`w-full flex items-center justify-between px-3 py-2 text-sm rounded-lg transition-colors ${
                        selectedFilter === category.name
                          ? "bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 border border-blue-200 dark:border-blue-800"
                          : "text-gray-600 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700"
                      }`}
                    >
                      <span>{category.name}</span>
                      <span className="text-xs bg-gray-100 dark:bg-gray-600 text-gray-600 dark:text-gray-300 px-2 py-1 rounded-full">
                        {category.count}
                      </span>
                    </button>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Content Area */}
          <div className="flex-1 min-w-0 w-full">
            {/* Loading State */}
            {isLoading && (
              <div className="space-y-6">
                {[...Array(3)].map((_, index) => (
                  <div
                    key={index}
                    className="rounded-lg p-6"
                    style={{
                      backgroundColor: "hsl(var(--card))",
                      borderColor: "hsl(var(--border))",
                      border: "1px solid",
                    }}
                  >
                    <div className="flex items-start gap-4">
                      <div className="w-2 h-2 bg-gray-300 dark:bg-gray-600 rounded-full animate-pulse mt-2"></div>
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-3">
                          <Skeleton className="h-5 w-20" />
                          <Skeleton className="h-4 w-16" />
                        </div>
                        <Skeleton className="h-6 w-3/4 mb-2" />
                        <Skeleton className="h-4 w-24 mb-4" />
                        <div className="space-y-2">
                          <Skeleton className="h-4 w-full" />
                          <Skeleton className="h-4 w-full" />
                          <Skeleton className="h-4 w-2/3" />
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {/* Changelogs */}
            {!isLoading && filteredChangelogs.length > 0 && (
              <div className="space-y-6">
                {filteredChangelogs.map((changelog) => (
                  <div
                    key={changelog.id}
                    className="rounded-lg p-6 hover:shadow-lg transition-shadow"
                    style={{
                      backgroundColor: "hsl(var(--card))",
                      borderColor: "hsl(var(--border))",
                      border: "1px solid",
                    }}
                  >
                    <div className="flex items-start gap-4">
                      {/* Timeline Dot */}
                      <div className="w-2 h-2 bg-gray-400 dark:bg-gray-500 rounded-full mt-2 flex-shrink-0"></div>

                      {/* Content */}
                      <div className="flex-1 min-w-0">
                        {/* Header */}
                        <div className="flex items-center gap-2 mb-3">
                          <span className="text-sm text-gray-500 dark:text-gray-400">
                            {getRelativeTime(changelog.publishedAt)}
                          </span>
                          <Badge
                            className={`text-xs font-medium px-2 py-1 ${getTypeBadgeStyle(
                              changelog.type
                            )}`}
                          >
                            {getTypeLabel(changelog.type)}
                          </Badge>
                        </div>

                        {/* Title */}
                        <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2 leading-tight">
                          {changelog.title}
                        </h2>

                        {/* Author */}
                        <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                          {t("changelog.page.author", {
                            author:
                              changelog?.authorName ||
                              t("changelog.page.systemAuthor"),
                          })}
                        </p>

                        {/* Content */}
                        <div className="prose prose-sm max-w-none dark:prose-invert">
                          <ReactMarkdown
                            className="markdown break-words"
                            components={{
                              code: ({
                                inline,
                                children,
                                className,
                                ...props
                              }: any) => (
                                <CodeBlock
                                  inline={inline}
                                  className={className}
                                  {...props}
                                >
                                  {children}
                                </CodeBlock>
                              ),
                              pre: ({ children }: any) => <>{children}</>, // Let CodeBlock handle the pre wrapper
                            }}
                          >
                            {changelog.content}
                          </ReactMarkdown>
                        </div>

                        {/* Metadata */}
                        {(changelog.githubCommitSha ||
                          changelog.deploymentId) && (
                          <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                            <div className="flex flex-wrap gap-4 text-xs text-gray-500 dark:text-gray-400">
                              {changelog.githubCommitSha && (
                                <div className="flex items-center gap-1">
                                  <GitCommit className="h-3 w-3" />
                                  <span>
                                    {t("changelog.page.commitLabel")}{" "}
                                    {changelog.githubCommitSha.substring(0, 8)}
                                  </span>
                                </div>
                              )}
                              {changelog.deploymentId && (
                                <div className="flex items-center gap-1">
                                  <ExternalLink className="h-3 w-3" />
                                  <span>
                                    {t("changelog.page.deploymentLabel")}{" "}
                                    {changelog.deploymentId}
                                  </span>
                                </div>
                              )}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {/* Empty State */}
            {!isLoading && filteredChangelogs.length === 0 && (
              <div className="text-center py-12">
                <div
                  className="rounded-lg p-8"
                  style={{
                    backgroundColor: "hsl(var(--card))",
                    borderColor: "hsl(var(--border))",
                    border: "1px solid",
                  }}
                >
                  <GitCommit className="h-12 w-12 text-gray-400 dark:text-gray-500 mx-auto mb-4" />
                  <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                    {searchQuery ||
                    selectedFilter !== t("changelog.page.allPublications")
                      ? t("changelog.page.noMatchingUpdates")
                      : t("changelog.page.noUpdatesAvailable")}
                  </h2>
                  <p className="text-gray-600 dark:text-gray-400">
                    {searchQuery ||
                    selectedFilter !== t("changelog.page.allPublications")
                      ? t("changelog.page.tryAdjustingFilters")
                      : t("changelog.page.checkBackLater")}
                  </p>
                </div>
              </div>
            )}

            {/* Pagination */}
            {pagination && pagination.totalPages > 1 && (
              <div className="mt-8 flex items-center justify-center">
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handlePageChange(pagination.page - 1)}
                    disabled={!pagination.hasPreviousPage}
                  >
                    <ChevronLeft className="h-4 w-4 mr-1" />
                    {t("changelog.page.previous")}
                  </Button>
                  <span className="text-sm text-gray-600 dark:text-gray-400 px-4">
                    {t("changelog.page.pageOf", {
                      current: pagination.page,
                      total: pagination.totalPages,
                    })}
                  </span>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handlePageChange(pagination.page + 1)}
                    disabled={!pagination.hasNextPage}
                  >
                    {t("changelog.page.next")}
                    <ChevronRight className="h-4 w-4 ml-1" />
                  </Button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ChangelogPage;
