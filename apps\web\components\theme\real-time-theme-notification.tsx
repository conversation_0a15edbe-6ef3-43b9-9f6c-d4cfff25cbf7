"use client";

import { useEffect, useState } from "react";
import { toast } from "react-hot-toast";
import { Bell, Palette, User } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";

interface ThemeUpdateNotificationProps {
  children: React.ReactNode;
}

interface ThemeUpdateData {
  type: "THEME_UPDATED";
  tenantId: string;
  themeConfig: any;
  updatedBy: string;
  timestamp: string;
}

export function RealTimeThemeNotification({ children }: ThemeUpdateNotificationProps) {
  const [lastThemeUpdate, setLastThemeUpdate] = useState<ThemeUpdateData | null>(null);
  const [showNotification, setShowNotification] = useState(false);
  const isPartnerConsole = process.env.NEXT_PUBLIC_IS_PARTNER_CONSOLE === "1";

  useEffect(() => {
    // Only activate real-time notifications if partner console is enabled
    if (!isPartnerConsole) {
      return;
    }

    const handleRealtimeThemeUpdate = (event: CustomEvent) => {
      const themeData = event.detail as ThemeUpdateData;
      console.log("Real-time theme notification received:", themeData);

      // Get current user ID from cookies to avoid showing notification for own changes
      const currentUserId = document.cookie
        .split("; ")
        .find((row) => row.startsWith("userId="))
        ?.split("=")[1];

      // Only show notification if the update was made by another user
      if (themeData?.updatedBy && themeData.updatedBy !== currentUserId) {
        setLastThemeUpdate(themeData);
        setShowNotification(true);

        // Show toast notification
        toast.success("Theme updated by team member", {
          duration: 4000,
          icon: <Palette className="h-4 w-4" style={{ color: 'hsl(var(--primary))' }} />,
          style: {
            background: "hsl(var(--background))",
            color: "hsl(var(--foreground))",
            border: "1px solid hsl(var(--primary) / 0.2)",
            boxShadow: "0 4px 12px hsl(var(--primary) / 0.1)",
          },
        });

        // Auto-hide the persistent notification after 8 seconds
        setTimeout(() => {
          setShowNotification(false);
        }, 8000);
      }
    };

    // Listen for real-time theme updates
    window.addEventListener("realtimeThemeUpdate", handleRealtimeThemeUpdate as EventListener);

    return () => {
      window.removeEventListener("realtimeThemeUpdate", handleRealtimeThemeUpdate as EventListener);
    };
  }, [isPartnerConsole]);

  const formatTimestamp = (timestamp: string) => {
    try {
      const date = new Date(timestamp);
      return date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" });
    } catch {
      return "now";
    }
  };

  const getThemeChangeSummary = (themeConfig: any) => {
    const changes: string[] = [];
    
    if (themeConfig?.brandName) {
      changes.push("brand name");
    }
    if (themeConfig?.logoUrl) {
      changes.push("logo");
    }
    if (themeConfig?.faviconUrl) {
      changes.push("favicon");
    }
    if (themeConfig?.themePreset) {
      changes.push(`${themeConfig.themePreset} mode`);
    }
    if (themeConfig?.primaryColor || themeConfig?.secondaryColor || themeConfig?.accentColor) {
      changes.push("colors");
    }

    if (changes.length === 0) {
      return "theme settings";
    }

    if (changes.length === 1) {
      return changes[0];
    }

    if (changes.length === 2) {
      return `${changes[0]} and ${changes[1]}`;
    }

    return `${changes.slice(0, -1).join(", ")}, and ${changes[changes.length - 1]}`;
  };

  return (
    <>
      {children}
      
      {/* Persistent notification banner */}
      {showNotification && lastThemeUpdate && isPartnerConsole && (
        <div className="fixed top-4 right-4 z-50 max-w-sm">
          <Alert
            className="border-primary/20 bg-primary/5 backdrop-blur-sm shadow-lg"
            style={{
              borderColor: 'hsl(var(--primary) / 0.2)',
              backgroundColor: 'hsl(var(--primary) / 0.05)',
            }}
          >
            <div className="flex items-start gap-3">
              <div className="flex-shrink-0">
                <Bell
                  className="h-4 w-4"
                  style={{ color: 'hsl(var(--primary))' }}
                />
              </div>
              <div className="flex-1 min-w-0">
                <AlertDescription className="text-sm">
                  <div className="flex items-center gap-2 mb-1">
                    <User
                      className="h-3 w-3"
                      style={{ color: 'hsl(var(--primary))' }}
                    />
                    <span className="font-medium text-foreground">Team member updated theme</span>
                    <Badge
                      variant="secondary"
                      className="text-xs"
                      style={{
                        backgroundColor: 'hsl(var(--secondary))',
                        color: 'hsl(var(--secondary-foreground))',
                      }}
                    >
                      {formatTimestamp(lastThemeUpdate.timestamp)}
                    </Badge>
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Updated: {getThemeChangeSummary(lastThemeUpdate.themeConfig)}
                  </p>
                </AlertDescription>
              </div>
              <button
                onClick={() => setShowNotification(false)}
                className="flex-shrink-0 transition-colors hover:opacity-80"
                style={{
                  color: 'hsl(var(--primary))',
                }}
                aria-label="Dismiss notification"
              >
                <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
          </Alert>
        </div>
      )}
    </>
  );
}
