import { NextResponse } from "next/server";
import { getToken } from "next-auth/jwt";
import { NextRequest } from "next/server";
import db from "@/lib/shared-db";

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const membershipId = params.id;
    if (!membershipId) {
      return NextResponse.json(
        { error: "Membership ID is required" },
        { status: 400 }
      );
    }

    const token = await getToken({ req: request as any });
    if (!token) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const { role } = body;

    // Get the membership we're trying to update
    const membership = await db.membership.findUnique({
      where: { id: membershipId },
      include: { tenant: true },
    });

    if (!membership) {
      return NextResponse.json(
        { error: "Membership not found" },
        { status: 404 }
      );
    }

    // Verify the current user has admin access to this tenant
    const adminMembership = await db.membership.findFirst({
      where: {
        userId: token.sub,
        tenantId: membership.tenantId,
        role: { in: ["ADMIN", "OWNER"] },
      },
    });

    if (!adminMembership) {
      return NextResponse.json(
        { error: "You don't have admin permissions for this tenant" },
        { status: 403 }
      );
    }

    // Prevent changing role of the tenant owner
    if (membership.role === "OWNER") {
      return NextResponse.json(
        { error: "Cannot change the role of the tenant owner" },
        { status: 403 }
      );
    }

    // Update the role
    const updatedMembership = await db.membership.update({
      where: { id: membershipId },
      data: { role: role },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            image: true,
          },
        },
      },
    });

    return NextResponse.json({
      data: updatedMembership,
      message: "Member role updated successfully",
    });
  } catch (error) {
    console.error("Update membership error:", error);
    return NextResponse.json(
      { error: "Internal Server Error: Failed to update membership" },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const membershipId = params.id;
    if (!membershipId) {
      return NextResponse.json(
        { error: "Membership ID is required" },
        { status: 400 }
      );
    }

    const token = await getToken({ req: request as any });
    if (!token) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get the membership we're trying to delete
    const membership = await db.membership.findUnique({
      where: { id: membershipId },
    });

    if (!membership) {
      return NextResponse.json(
        { error: "Membership not found" },
        { status: 404 }
      );
    }

    // Verify the current user has admin access to this tenant
    const adminMembership = await db.membership.findFirst({
      where: {
        userId: token.sub,
        tenantId: membership.tenantId,
        role: { in: ["ADMIN", "OWNER"] },
      },
    });

    if (!adminMembership) {
      return NextResponse.json(
        { error: "You don't have admin permissions for this tenant" },
        { status: 403 }
      );
    }

    // Prevent removing the tenant owner
    if (membership.role === "OWNER") {
      return NextResponse.json(
        { error: "Cannot remove the tenant owner" },
        { status: 403 }
      );
    }

    // Prevent removing yourself
    if (membership.userId === token.sub) {
      return NextResponse.json(
        { error: "Cannot remove yourself from the tenant" },
        { status: 403 }
      );
    }

    // Delete the membership
    await db.membership.delete({
      where: { id: membershipId },
    });

    return NextResponse.json({
      message: "Member removed successfully",
    });
  } catch (error) {
    console.error("Delete membership error:", error);
    return NextResponse.json(
      { error: "Internal Server Error: Failed to delete membership" },
      { status: 500 }
    );
  }
}
