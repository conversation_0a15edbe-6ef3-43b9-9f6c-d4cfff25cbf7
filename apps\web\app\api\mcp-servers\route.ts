import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/next-auth";
import db from "@/lib/shared-db";

interface MCPServerCreate {
  name: string;
  description?: string;
  server_type: "STDIO" | "HTTP";
  command?: string; // Required for stdio servers
  args?: string[];
  env?: Record<string, string>;
  url?: string; // Required for http servers
  headers?: Record<string, string>; // For http servers
  timeout?: number;
  auto_restart?: boolean;
  is_public?: boolean;
}

interface MCPServerResponse {
  id: string;
  name: string;
  description?: string;
  server_type: "STDIO" | "HTTP";
  command?: string;
  args?: string[];
  env?: Record<string, string>;
  url?: string;
  headers?: Record<string, string>;
  status: string;
  last_error?: string;
  timeout: number;
  auto_restart: boolean;
  is_public: boolean;
  user_id: string;
  tenant_id: string;
  created_at: string;
  updated_at: string;
}

/**
 * GET /api/mcp-servers - List all MCP servers
 */
export async function GET(req: NextRequest) {
  try {
    const session: any = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(req.url);
    const includePublic = searchParams.get("include_public") !== "false";
    const tenantId =
      searchParams.get("tenantId") || session.currentOrganization?.id;

    // Build query filter
    const whereClause: any = {
      tenantId,
      OR: [
        { userId: session.userId }, // User's own servers
      ],
    };

    if (includePublic) {
      whereClause.OR.push({ isPublic: true }); // Public servers in tenant
    }

    // Fetch servers from database
    const servers = await db.mCPServer.findMany({
      where: whereClause,
      orderBy: { createdAt: "desc" },
    });

    // Convert to response format
    const serverResponses = servers.map((server: any) => ({
      id: server.id,
      name: server.name,
      description: server.description,
      server_type: server.serverType,
      command: server.command,
      args: server.args,
      env: server.env,
      url: server.url,
      headers: server.headers,
      status: server.status,
      last_error: server.lastError,
      timeout: server.timeout,
      auto_restart: server.autoRestart,
      is_public: server.isPublic,
      user_id: server.userId,
      tenant_id: server.tenantId,
      created_at: server.createdAt.toISOString(),
      updated_at: server.updatedAt.toISOString(),
    }));

    return NextResponse.json(serverResponses);
  } catch (error: any) {
    console.error("[MCP_SERVERS_GET]", error);
    return NextResponse.json(
      { error: error.message || "Failed to fetch MCP servers" },
      { status: 500 }
    );
  }
}

/**
 * POST /api/mcp-servers - Create a new MCP server
 */
export async function POST(req: NextRequest) {
  try {
    const session: any = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body: MCPServerCreate = await req.json();

    // Validate required fields
    if (!body.name || !body.server_type) {
      return NextResponse.json(
        { error: "Name and server_type are required" },
        { status: 400 }
      );
    }

    // Validate server type specific requirements
    if (body.server_type === "STDIO" && !body.command) {
      return NextResponse.json(
        { error: "Command is required for STDIO servers" },
        { status: 400 }
      );
    }

    if (body.server_type === "HTTP" && !body.url) {
      return NextResponse.json(
        { error: "URL is required for HTTP servers" },
        { status: 400 }
      );
    }

    // Prevent NPX-based servers as they're not supported in the server environment
    if (body.server_type === "STDIO" && body.command) {
      if (body.command.toLowerCase().includes("npx")) {
        return NextResponse.json(
          {
            error:
              "NPX-based servers are not supported in this environment. Please use Python scripts or other direct executables.",
          },
          { status: 400 }
        );
      }
    }

    // Check if server name already exists for this tenant
    const existingServer = await db.mCPServer.findFirst({
      where: {
        name: body.name,
        tenantId: session.currentOrganization?.id,
      },
    });

    if (existingServer) {
      return NextResponse.json(
        { error: `MCP server with name '${body.name}' already exists` },
        { status: 400 }
      );
    }

    // Create server document
    const serverData = {
      name: body.name,
      description: body.description || null,
      serverType: body.server_type,
      command: body.command || null,
      args: body.args || null,
      env: body.env || null,
      url: body.url || null,
      headers: body.headers || null,
      status: "INACTIVE",
      lastError: null,
      timeout: body.timeout || 30000,
      autoRestart: body.auto_restart ?? true,
      isPublic: body.is_public ?? false,
      userId: session.userId,
      tenantId: session.currentOrganization?.id,
    };

    // Insert into database
    const createdServer = await db.mCPServer.create({
      data: serverData,
    });

    // Convert to response format
    const response: MCPServerResponse = {
      id: createdServer.id,
      name: createdServer.name,
      description: createdServer.description,
      server_type: createdServer.serverType as "STDIO" | "HTTP",
      command: createdServer.command,
      args: createdServer.args as string[],
      env: createdServer.env as Record<string, string>,
      url: createdServer.url,
      headers: createdServer.headers as Record<string, string>,
      status: createdServer.status,
      last_error: createdServer.lastError,
      timeout: createdServer.timeout,
      auto_restart: createdServer.autoRestart,
      is_public: createdServer.isPublic,
      user_id: createdServer.userId,
      tenant_id: createdServer.tenantId,
      created_at: createdServer.createdAt.toISOString(),
      updated_at: createdServer.updatedAt.toISOString(),
    };

    return NextResponse.json(response, { status: 201 });
  } catch (error: any) {
    console.error("[MCP_SERVERS_POST]", error);
    return NextResponse.json(
      { error: error.message || "Failed to create MCP server" },
      { status: 500 }
    );
  }
}
