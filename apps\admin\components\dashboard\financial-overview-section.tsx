"use client";

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card";
import { Enhanced<PERSON>hart } from "./enhanced-chart";
import { TrendingUp, TrendingDown, DollarSign, PieChart } from "lucide-react";

interface FinancialData {
  mrrVsCostData: Array<{ name: string; mrr: number; cost: number }>;
  tenantProfitabilityData: Array<{ 
    name: string; 
    profit: number; 
    plan: string; 
    margin: number 
  }>;
}

interface FinancialOverviewSectionProps {
  data: FinancialData;
  kpiMetrics?: {
    mrr: number;
    infrastructureCost: number;
  };
  loading?: boolean;
}

export function FinancialOverviewSection({ data, kpiMetrics, loading }: FinancialOverviewSectionProps) {
  if (loading) {
    return (
      <div className="space-y-6">
        <h2 className="text-2xl font-bold">Financial Overview</h2>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <DollarSign className="h-5 w-5" />
                Loading...
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-64 bg-muted animate-pulse rounded" />
            </CardContent>
          </Card>
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <PieChart className="h-5 w-5" />
                Loading...
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-64 bg-muted animate-pulse rounded" />
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  // Calculate summary metrics using KPI data for consistency
  const totalMrr = kpiMetrics?.mrr || 0;
  const totalCost = kpiMetrics?.infrastructureCost || 0;
  const grossMargin = totalMrr > 0 ? ((totalMrr - totalCost) / totalMrr) * 100 : 0;

  const avgProfitMargin = data.tenantProfitabilityData.length > 0
    ? data.tenantProfitabilityData.reduce((sum, tenant) => sum + tenant.margin, 0) / data.tenantProfitabilityData.length
    : 0;

  // Calculate total profit from all tenants
  const totalProfit = data.tenantProfitabilityData.reduce((sum, tenant) => sum + tenant.profit, 0);

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">Financial Overview</h2>
        <div className="flex items-center gap-4 text-sm text-muted-foreground">
          <div className="flex items-center gap-1">
            <TrendingUp className="h-4 w-4 text-green-500" />
            <span>Gross Margin: {grossMargin.toFixed(1)}%</span>
          </div>
          <div className="flex items-center gap-1">
            <PieChart className="h-4 w-4 text-blue-500" />
            <span>Avg Profit Margin: {avgProfitMargin.toFixed(1)}%</span>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* MRR vs Cost Trend */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <DollarSign className="h-5 w-5" />
              MRR vs Infrastructure Cost
            </CardTitle>
          </CardHeader>
          <CardContent>
            <EnhancedChart
              data={data.mrrVsCostData}
              type="line"
              height={300}
              formatterType="currency"
              showLegend={true}
              yAxisLabel="CHF"
              config={{
                mrr: {
                  label: "MRR (CHF)",
                  color: "#3B82F6",
                },
                cost: {
                  label: "Infrastructure Cost (CHF)",
                  color: "#EF4444",
                },
              }}
            />
          </CardContent>
        </Card>

        {/* Tenant Profitability */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <PieChart className="h-5 w-5" />
              Tenant Profitability
            </CardTitle>
          </CardHeader>
          <CardContent>
            <EnhancedChart
              data={data.tenantProfitabilityData}
              type="bar"
              height={300}
              formatterType="currency"
              showLegend={true}
              yAxisLabel="CHF / %"
              config={{
                profit: {
                  label: "Profit (CHF)",
                  color: "#10B981",
                },
                margin: {
                  label: "Margin %",
                  color: "#F59E0B",
                },
              }}
            />
          </CardContent>
        </Card>
      </div>

      {/* Financial Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Total MRR</p>
                <p className="text-2xl font-bold">CHF {totalMrr.toLocaleString()}</p>
              </div>
              <TrendingUp className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Infrastructure Cost</p>
                <p className="text-2xl font-bold">CHF {totalCost.toLocaleString()}</p>
              </div>
              <TrendingDown className="h-8 w-8 text-red-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Net Profit</p>
                <p className="text-2xl font-bold">CHF {(totalMrr - totalCost).toLocaleString()}</p>
              </div>
              <DollarSign className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
