"use client";

import React, { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Building,
  ArrowLeft,
  Copy,
  ExternalLink,
  Users,
  CheckCircle,
  AlertCircle,
  Crown,
  Shield,
  User,
  Mail,
  Plus,
  Loader2,
  Trash2,
  Link,
  Share2,
} from "lucide-react";
import toast from "react-hot-toast";
import { useLanguage } from "@/lib/language-context";
import {
  getCompany,
  getCompanyMembers,
  addCompanyMember,
  removeCompanyMember,
  updateCompanyMemberRole,
  type Company,
  type CompanyMember,
} from "@/services";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";

// Remove duplicate interfaces and mock data - using types from services

const InviteLinkCard = ({ company }: { company: Company }) => {
  const { t } = useLanguage();
  const [copied, setCopied] = useState(false);

  const inviteUrl = `${window.location.origin}/invite/${company.slug}`;

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(inviteUrl);
      setCopied(true);
      toast.success(t("company.inviteLinkCopiedToClipboard"));
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      toast.error(t("company.failedToCopyInviteLink"));
    }
  };

  const openInNewTab = () => {
    window.open(inviteUrl, "_blank");
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Link className="h-5 w-5" />
          {t("company.inviteLink")}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <Label htmlFor="invite-link">{t("company.companyInviteLink")}</Label>
          <div className="flex gap-2 mt-2">
            <Input
              id="invite-link"
              value={inviteUrl}
              readOnly
              className="flex-1"
              style={{ backgroundColor: "hsl(var(--muted))" }}
            />
            <Button
              onClick={copyToClipboard}
              variant="outline"
              size="sm"
              className="shrink-0"
            >
              {copied ? (
                <CheckCircle
                  className="h-4 w-4"
                  style={{ color: "hsl(var(--primary))" }}
                />
              ) : (
                <Copy className="h-4 w-4" />
              )}
            </Button>
            <Button
              onClick={openInNewTab}
              variant="outline"
              size="sm"
              className="shrink-0"
            >
              <ExternalLink className="h-4 w-4" />
            </Button>
          </div>
        </div>

        <div
          className="p-4 rounded-lg"
          style={{ backgroundColor: "hsl(var(--accent) / 0.1)" }}
        >
          <div className="flex items-start gap-3">
            <Share2
              className="h-5 w-5 mt-0.5"
              style={{ color: "hsl(var(--accent))" }}
            />
            <div>
              <h4
                className="font-medium mb-1"
                style={{ color: "hsl(var(--accent-foreground))" }}
              >
                {t("company.shareThisLink")}
              </h4>
              <p
                className="text-sm"
                style={{ color: "hsl(var(--muted-foreground))" }}
              >
                {t("company.shareThisLinkDescription")}
              </p>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-2 gap-4 text-sm">
          <div
            className="p-3 rounded-lg"
            style={{ backgroundColor: "hsl(var(--muted))" }}
          >
            <p
              className="font-medium"
              style={{ color: "hsl(var(--card-foreground))" }}
            >
              {t("company.currentMembers")}
            </p>
            <p
              className="text-2xl font-bold"
              style={{ color: "hsl(var(--card-foreground))" }}
            >
              {company.currentSignups}
            </p>
          </div>
          <div
            className="p-3 rounded-lg"
            style={{ backgroundColor: "hsl(var(--muted))" }}
          >
            <p
              className="font-medium"
              style={{ color: "hsl(var(--card-foreground))" }}
            >
              {t("company.availableSpots")}
            </p>
            <p
              className="text-2xl font-bold"
              style={{ color: "hsl(var(--card-foreground))" }}
            >
              {company.maxSignups - company.currentSignups}
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

const AddMemberDialog = ({
  companyId,
  onMemberAdded,
}: {
  companyId: string;
  onMemberAdded: (member: CompanyMember) => void;
}) => {
  const { t } = useLanguage();
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    email: "",
    role: "MEMBER",
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.email.trim()) {
      toast.error(t("company.emailRequired"));
      return;
    }

    setLoading(true);
    try {
      const result = await addCompanyMember(companyId, {
        email: formData.email,
        role: formData.role as any,
      });

      if ("error" in result) {
        toast.error(result.error);
        return;
      }

      onMemberAdded(result.member);
      setFormData({ email: "", role: "MEMBER" });
      setOpen(false);
      toast.success(t("company.memberAddedSuccess"));
    } catch (error) {
      toast.error(t("company.failedToAddMember"));
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button>
          <Plus className="mr-2 h-4 w-4" />
          {t("company.addMember")}
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[400px]">
        <DialogHeader>
          <DialogTitle>{t("company.addNewMember")}</DialogTitle>
          <DialogDescription>
            {t("company.addNewMemberDescription")}
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="email">{t("company.emailAddress")} *</Label>
            <Input
              id="email"
              type="email"
              placeholder={t("company.enterEmailAddress")}
              value={formData.email}
              onChange={(e) =>
                setFormData((prev) => ({ ...prev, email: e.target.value }))
              }
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="role">{t("company.role")}</Label>
            <Select
              value={formData.role}
              onValueChange={(value) =>
                setFormData((prev) => ({ ...prev, role: value }))
              }
            >
              <SelectTrigger>
                <SelectValue placeholder={t("company.selectRole")} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="MEMBER">{t("company.member")}</SelectItem>
                <SelectItem value="ADMIN">{t("company.admin")}</SelectItem>
                <SelectItem value="VIEWER">{t("company.viewer")}</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="flex justify-end space-x-2 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => setOpen(false)}
              disabled={loading}
            >
              {t("company.cancel")}
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  {t("company.adding")}
                </>
              ) : (
                t("company.addMember")
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

const getRoleIcon = (role: CompanyMember["role"]) => {
  switch (role) {
    case "owner":
      return (
        <Crown className="h-4 w-4" style={{ color: "hsl(var(--primary))" }} />
      );
    case "admin":
      return (
        <Shield className="h-4 w-4" style={{ color: "hsl(var(--accent))" }} />
      );
    case "member":
      return (
        <User className="h-4 w-4" style={{ color: "hsl(var(--secondary))" }} />
      );
    case "viewer":
      return (
        <User
          className="h-4 w-4"
          style={{ color: "hsl(var(--muted-foreground))" }}
        />
      );
    default:
      return <User className="h-4 w-4" />;
  }
};

const getStatusBadge = (status: CompanyMember["status"]) => {
  const { t } = useLanguage();

  switch (status) {
    case "active":
      return (
        <Badge
          variant="default"
          style={{
            backgroundColor: "hsl(var(--primary) / 0.1)",
            color: "hsl(var(--primary))",
            borderColor: "hsl(var(--primary) / 0.2)",
          }}
        >
          {t("company.active")}
        </Badge>
      );
    case "pending":
      return (
        <Badge
          variant="secondary"
          style={{
            backgroundColor: "hsl(var(--secondary) / 0.1)",
            color: "hsl(var(--secondary))",
            borderColor: "hsl(var(--secondary) / 0.2)",
          }}
        >
          {t("company.pending")}
        </Badge>
      );
    case "inactive":
      return (
        <Badge
          variant="outline"
          style={{
            backgroundColor: "hsl(var(--muted))",
            color: "hsl(var(--muted-foreground))",
            borderColor: "hsl(var(--border))",
          }}
        >
          {t("company.inactive")}
        </Badge>
      );
    default:
      return <Badge variant="outline">{status}</Badge>;
  }
};

export default function CompanyDetailsPage() {
  const { t } = useLanguage();
  const params = useParams() as { id: string };
  const router = useRouter();
  const companyId = params?.id as string;

  const [company, setCompany] = useState<Company | null>(null);
  const [companyMembers, setCompanyMembers] = useState<CompanyMember[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const inviteUrl = `${window.location.origin}/invite/${company?.slug}`;

  // Load company data on component mount
  useEffect(() => {
    loadCompanyData();
  }, [companyId]);

  const loadCompanyData = async () => {
    setLoading(true);
    setError(null);
    try {
      // Load company details and members in parallel
      const [companyResult, membersResult] = await Promise.all([
        getCompany(companyId),
        getCompanyMembers(companyId),
      ]);

      if ("error" in companyResult) {
        setError(companyResult.error);
        return;
      }

      if ("error" in membersResult) {
        setError(membersResult.error);
        return;
      }

      setCompany(companyResult.company);
      setCompanyMembers(membersResult.members);
    } catch (error) {
      setError(t("company.failedToLoadCompanyData"));
    } finally {
      setLoading(false);
    }
  };

  const handleMemberAdded = (newMember: CompanyMember) => {
    setCompanyMembers((prev) => [...prev, newMember]);
  };

  const handleRemoveMember = async (memberId: string) => {
    try {
      const result = await removeCompanyMember(companyId, memberId);
      if ("error" in result) {
        toast.error(result.error);
        return;
      }
      setCompanyMembers((prev) => prev.filter((m) => m.id !== memberId));
      toast.success(t("company.memberRemovedSuccess"));
    } catch (error) {
      toast.error(t("company.failedToRemoveMember"));
    }
  };

  const handleRoleChange = async (memberId: string, newRole: string) => {
    try {
      const result = await updateCompanyMemberRole(
        companyId,
        memberId,
        newRole
      );
      if ("error" in result) {
        toast.error(result.error);
        return;
      }
      setCompanyMembers((prev) =>
        prev.map((m) =>
          m.id === memberId ? { ...m, role: newRole as any } : m
        )
      );
      toast.success(t("company.memberRoleUpdatedSuccess"));
    } catch (error) {
      toast.error(t("company.failedToUpdateMemberRole"));
    }
  };

  if (loading) {
    return (
      <div className="flex flex-col items-center justify-center py-12">
        <Loader2 className="h-12 w-12 text-muted-foreground mb-4 animate-spin" />
        <h3 className="text-lg font-semibold mb-2">
          {t("company.loadingCompany")}
        </h3>
        <p className="text-muted-foreground">
          {t("company.pleaseWaitCompanyDetails")}
        </p>
      </div>
    );
  }

  if (error || !company) {
    return (
      <div className="flex flex-col items-center justify-center py-12">
        <Building className="h-12 w-12 text-muted-foreground mb-4" />
        <h3 className="text-lg font-semibold mb-2">
          {error || t("company.companyNotFound")}
        </h3>
        <p className="text-muted-foreground mb-4">
          {error || t("company.companyNotFoundDescription")}
        </p>
        <Button onClick={() => router.back()}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          {t("company.goBack")}
        </Button>
      </div>
    );
  }

  const copyInviteLink = () => {
    navigator.clipboard.writeText(inviteUrl);
    toast.success(t("company.inviteLinkCopied"));
  };

  const getUsageStatus = (current: number, max: number) => {
    const percentage = (current / max) * 100;
    if (percentage >= 100)
      return {
        color: "hsl(var(--destructive))",
        icon: AlertCircle,
        bg: "hsl(var(--destructive) / 0.1)",
      };
    if (percentage >= 80)
      return {
        color: "hsl(var(--secondary))",
        icon: AlertCircle,
        bg: "hsl(var(--secondary) / 0.1)",
      };
    return {
      color: "hsl(var(--primary))",
      icon: CheckCircle,
      bg: "hsl(var(--primary) / 0.1)",
    };
  };

  const status = getUsageStatus(company.currentSignups, company.maxSignups);
  const StatusIcon = status.icon;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button variant="outline" onClick={() => router.back()}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          {t("common.back")}
        </Button>
        <div>
          <h1 className="text-2xl font-bold tracking-tight flex items-center gap-2">
            <Building className="h-6 w-6" />
            {company.name}
          </h1>
          <p className="text-muted-foreground">{company.description}</p>
        </div>
      </div>

      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList>
          <TabsTrigger value="overview">{t("company.overview")}</TabsTrigger>
          <TabsTrigger value="members">
            {t("company.members")} ({companyMembers.length})
          </TabsTrigger>
          <TabsTrigger value="invite">{t("company.invite")}</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Company Info Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  {t("company.totalMembers")}
                </CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {companyMembers.length}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  {t("company.activeMembers")}
                </CardTitle>
                <Users
                  className="h-4 w-4"
                  style={{ color: "hsl(var(--primary))" }}
                />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {companyMembers.filter((m) => m.status === "active").length}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  {t("company.pendingInvites")}
                </CardTitle>
                <Mail
                  className="h-4 w-4"
                  style={{ color: "hsl(var(--secondary))" }}
                />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {companyMembers.filter((m) => m.status === "pending").length}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Company Details */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>{t("company.companyInformation")}</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="text-sm font-medium">
                    {t("company.companyName")}
                  </label>
                  <p className="text-sm text-muted-foreground">
                    {company.name}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium">
                    {t("company.slug")}
                  </label>
                  <code className="text-sm bg-muted px-2 py-1 rounded">
                    {company.slug}
                  </code>
                </div>
                <div>
                  <label className="text-sm font-medium">
                    {t("common.description")}
                  </label>
                  <p className="text-sm text-muted-foreground">
                    {company.description}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium">
                    {t("company.created")}
                  </label>
                  <p className="text-sm text-muted-foreground">
                    {company.createdAt}
                  </p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>{t("company.usageAndAccess")}</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <label className="text-sm font-medium">
                      {t("company.signupLimit")}
                    </label>
                    <div className="flex items-center gap-1">
                      <StatusIcon
                        className="h-4 w-4"
                        style={{ color: status.color }}
                      />
                      <span
                        className="text-sm font-medium"
                        style={{ color: status.color }}
                      >
                        {company.currentSignups} / {company.maxSignups}
                      </span>
                    </div>
                  </div>
                  <div
                    className="w-full rounded-full h-2"
                    style={{ backgroundColor: "hsl(var(--muted))" }}
                  >
                    <div
                      className="h-2 rounded-full transition-all"
                      style={{
                        backgroundColor: status.color,
                        width: `${Math.min(
                          (company.currentSignups / company.maxSignups) * 100,
                          100
                        )}%`,
                      }}
                    />
                  </div>
                </div>

                <div>
                  <label className="text-sm font-medium">
                    {t("company.inviteLink")}
                  </label>
                  <div className="flex items-center gap-2 mt-1">
                    <Input value={inviteUrl} readOnly className="text-sm" />
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={copyInviteLink}
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                    <Button variant="outline" size="sm" asChild>
                      <a
                        href={inviteUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        <ExternalLink className="h-4 w-4" />
                      </a>
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="members" className="space-y-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle>{t("company.companyMembers")}</CardTitle>
              <AddMemberDialog
                companyId={companyId}
                onMemberAdded={handleMemberAdded}
              />
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>{t("company.member")}</TableHead>
                    <TableHead>{t("company.role")}</TableHead>
                    <TableHead>{t("company.status")}</TableHead>
                    <TableHead>{t("company.joined")}</TableHead>
                    <TableHead>{t("common.actions")}</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {companyMembers.map((member) => (
                    <TableRow key={member.id}>
                      <TableCell>
                        <div className="flex items-center gap-3">
                          <Avatar className="h-8 w-8">
                            <AvatarFallback>
                              {member.name
                                .split(" ")
                                .map((n) => n[0])
                                .join("")
                                .toUpperCase()}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <div className="font-medium">{member.name}</div>
                            <div className="text-sm text-muted-foreground">
                              {member.email}
                            </div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          {getRoleIcon(member.role)}
                          <Select
                            value={member.role.toUpperCase()}
                            onValueChange={(value) =>
                              handleRoleChange(member.id, value)
                            }
                          >
                            <SelectTrigger className="w-32">
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="OWNER">
                                {t("company.owner")}
                              </SelectItem>
                              <SelectItem value="ADMIN">
                                {t("company.admin")}
                              </SelectItem>
                              <SelectItem value="MEMBER">
                                {t("company.member")}
                              </SelectItem>
                              <SelectItem value="VIEWER">
                                {t("company.viewer")}
                              </SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      </TableCell>
                      <TableCell>{getStatusBadge(member.status)}</TableCell>
                      <TableCell>{member.joinedAt}</TableCell>
                      <TableCell>
                        <div className="flex gap-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleRemoveMember(member.id)}
                            style={{ color: "hsl(var(--destructive))" }}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              {companyMembers.length === 0 && (
                <div className="text-center py-8">
                  <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">
                    {t("company.noMembersYet")}
                  </h3>
                  <p className="text-muted-foreground mb-4">
                    {t("company.getStartedByAddingMember")}
                  </p>
                  <AddMemberDialog
                    companyId={companyId}
                    onMemberAdded={handleMemberAdded}
                  />
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="invite" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <InviteLinkCard company={company} />

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  {t("company.invitationSettings")}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label>{t("company.maximumMembers")}</Label>
                  <p
                    className="text-sm mb-2"
                    style={{ color: "hsl(var(--muted-foreground))" }}
                  >
                    {t("company.currentLimit", { limit: company.maxSignups })}
                  </p>
                  <div
                    className="w-full rounded-full h-2"
                    style={{ backgroundColor: "hsl(var(--muted))" }}
                  >
                    <div
                      className="h-2 rounded-full"
                      style={{
                        backgroundColor: "hsl(var(--primary))",
                        width: `${Math.min(
                          (company.currentSignups / company.maxSignups) * 100,
                          100
                        )}%`,
                      }}
                    ></div>
                  </div>
                  <p
                    className="text-xs mt-1"
                    style={{ color: "hsl(var(--muted-foreground))" }}
                  >
                    {t("company.spotsFilledOf", {
                      current: company.currentSignups,
                      max: company.maxSignups,
                    })}
                  </p>
                </div>

                <div
                  className="p-4 rounded-lg"
                  style={{ backgroundColor: "hsl(var(--secondary) / 0.1)" }}
                >
                  <div className="flex items-start gap-3">
                    <AlertCircle
                      className="h-5 w-5 mt-0.5"
                      style={{ color: "hsl(var(--secondary))" }}
                    />
                    <div>
                      <h4
                        className="font-medium mb-1"
                        style={{ color: "hsl(var(--secondary-foreground))" }}
                      >
                        {t("company.securityNotice")}
                      </h4>
                      <p
                        className="text-sm"
                        style={{ color: "hsl(var(--muted-foreground))" }}
                      >
                        {t("company.securityNoticeDescription")}
                      </p>
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>{t("company.defaultRoleForNewMembers")}</Label>
                  <div className="flex items-center gap-2">
                    <User
                      className="h-4 w-4"
                      style={{ color: "hsl(var(--muted-foreground))" }}
                    />
                    <span className="text-sm">{t("company.member")}</span>
                    <Badge variant="outline">{t("company.default")}</Badge>
                  </div>
                  <p
                    className="text-xs"
                    style={{ color: "hsl(var(--muted-foreground))" }}
                  >
                    {t("company.newMembersRoleDescription")}
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
