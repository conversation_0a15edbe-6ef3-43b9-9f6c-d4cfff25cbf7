resource "azurerm_resource_group" "resource_group" {
  name     = var.resource_group_name
  location = var.location
}

module "networking" {
  source              = "./modules/networking"
  vnet_name           = var.vnet_name
  location            = var.location
  resource_group_name = var.resource_group_name
}

module "cosmosdb" {
  source              = "./modules/cosmosdb"
  cosmosdb_name       = var.cosmosdb_name
  database_name       = var.database_name
  location            = var.location
  cosmosdb_password   = var.cosmosdb_password
  cosmosdb_username   = var.cosmosdb_username
  resource_group_name = var.resource_group_name
  static_app_location = var.static_app_location
  cosmosdb_location   = var.cosmosdb_location
  cosmosdb_tire       = var.cosmosdb_tire
  cosmosdb_size       = var.cosmosdb_size
}

module "app_service" {
  source                = "./modules/app_service"
  app_service_plan_name = var.backend_app_service_plan_name
  app_service_name      = var.backend_app_service_name
  location              = var.location
  resource_group_name   = var.resource_group_name
  app_service_sku_name  = var.app_service_sku_name
}

module "static_web_app" {
  source              = "./modules/static-web-app"
  swa_name            = var.swa_name
  resource_group_name = var.resource_group_name
  location            = var.static_app_location
}
module "admin_web_app" {
  source              = "./modules/static-web-app"
  swa_name            = var.admin_swa_name
  resource_group_name = var.resource_group_name
  location            = var.static_app_location
}

module "storage" {
  source               = "./modules/storage"
  storage_account_name = var.storage_account_name
  environment          = var.environment
  location             = var.location
  resource_group_name  = var.resource_group_name
}

module "email" {
  source              = "./modules/email_communication"
  resource_group_name = var.resource_group_name
  data_location       = var.data_location
  prefix              = var.environment
}
