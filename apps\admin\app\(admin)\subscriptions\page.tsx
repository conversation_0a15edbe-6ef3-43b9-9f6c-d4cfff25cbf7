import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/next-auth";
import { redirect } from "next/navigation";
import { SubscriptionsTableClient } from "./subscriptions-table-client";
import db from "@/lib/shared-db";

// Types for our data structures
interface Subscription {
  id: string;
  tenantName: string;
  tenantId: string;
  plan: string;
  startDate: string;
  endDate: string | null;
  status: string;
  billingInterval: string;
  amount: number;
  additionalUsers: number;
  additionalStorage: number;
}

interface PlanDistribution {
  name: string;
  value: number;
}

interface BillingDistribution {
  name: string;
  value: number;
}

interface SubscriptionsData {
  subscriptions: Subscription[];
  planDistribution: PlanDistribution[];
  billingDistribution: BillingDistribution[];
  allTenants: { id: string; name: string }[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  searchQuery: string;
}

// Function to fetch subscriptions using manual joins to avoid Prisma relationship issues
async function fetchSubscriptionsWithJoins(db: any) {
  try {
    // First, get all subscriptions without joins
    const subscriptions = await db.subscription.findMany({
      orderBy: {
        createdAt: "desc",
      },
    });


    if (subscriptions.length === 0) {
      return [];
    }

    // Get unique tenant and plan IDs, filtering out null values
    const tenantIds = [...new Set(subscriptions.map((s: any) => s.tenantId).filter(Boolean))];
    const planIds = [...new Set(subscriptions.map((s: any) => s.planId).filter(Boolean))];

    // First, let's see what tenants and plans exist in the database
    const [allTenants, allPlans] = await Promise.all([
      db.tenant.findMany({
        select: { id: true, name: true },
      }),
      db.plan.findMany({
        select: { id: true, name: true, price: true },
      }),
    ]);

    // Now fetch tenants and plans that match our subscription references
    const [tenants, plans] = await Promise.all([
      tenantIds.length > 0 ? db.tenant.findMany({
        where: { id: { in: tenantIds } },
        select: { id: true, name: true },
      }) : [],
      planIds.length > 0 ? db.plan.findMany({
        where: { id: { in: planIds } },
        select: { id: true, name: true, price: true },
      }) : [],
    ]);

    // Create lookup maps for efficient joining
    const tenantMap = new Map(tenants.map((t: any) => [t.id, t]));
    const planMap = new Map(plans.map((p: any) => [p.id, p]));

    // Process all subscriptions and provide placeholder data for missing relationships
    const allSubscriptions = subscriptions
      .map((subscription: any) => {
        const tenant = tenantMap.get(subscription.tenantId);
        const plan = planMap.get(subscription.planId);

        if (!tenant || !plan) {
          // Provide placeholder data for missing relationships
          const placeholderTenant = tenant || {
            id: subscription.tenantId,
            name: `Unknown Tenant (${subscription.tenantId.slice(-8)})`
          };
          const placeholderPlan = plan || {
            id: subscription.planId,
            name: `Unknown Plan (${subscription.planId.slice(-8)})`,
            price: 0
          };

          return {
            ...subscription,
            tenant: placeholderTenant,
            plan: placeholderPlan,
          };
        }

        return {
          ...subscription,
          tenant,
          plan,
        };
      });
    return allSubscriptions;
  } catch (error) {
    console.error('Error in fetchSubscriptionsWithJoins:', error);
    return [];
  }
}

// Server-side data fetching function
async function fetchSubscriptionsData(
  searchQuery: string = "",
  page: number = 1,
  limit: number = 5,
  tenantId: string = ""
): Promise<SubscriptionsData> {
  try {
    const allTenants = await db.tenant.findMany({
      select: { id: true, name: true },
    });
    // Fetch subscriptions with manual joins to handle relationship issues
    const allSubscriptions = await fetchSubscriptionsWithJoins(db);

    // Transform the data to match the expected structure
    let transformedSubscriptions = allSubscriptions.map((subscription: any) => {
      // Determine status based on subscription properties
      let status = "inactive";
      if (subscription.isActive) {
        if (subscription.isOnTrial) {
          status = "trial";
        } else {
          status = "active";
        }
      }

      let totalAmount = subscription.plan.price;
      return {
        id: subscription.id,
        tenantName: subscription.tenant.name,
        tenantId: subscription.tenantId,
        plan: subscription.plan.name,
        startDate: subscription.startDate.toISOString(),
        endDate: subscription.endDate ? subscription.endDate.toISOString() : null,
        status,
        billingInterval: subscription.billingInterval || "month",
        allTenants,
        amount: totalAmount,
        additionalUsers: subscription.additionalUsers,
        additionalStorage: subscription.additionalStorageGB,
      };
    });

    // Apply search filter if search query is provided
    if (searchQuery && searchQuery.trim()) {
      const searchTerm = searchQuery.toLowerCase().trim();

      transformedSubscriptions = transformedSubscriptions.filter((subscription: any) => {
        const tenantMatch = subscription.tenantName.toLowerCase().includes(searchTerm);
        const planMatch = subscription.plan.toLowerCase().includes(searchTerm);
        const statusMatch = subscription.status.toLowerCase().includes(searchTerm);
        const billingMatch = subscription.billingInterval.toLowerCase().includes(searchTerm);

        return tenantMatch || planMatch || statusMatch || billingMatch;
      });
    }

    if (tenantId) {
      transformedSubscriptions = transformedSubscriptions.filter(
        (sub: any) => sub.tenantId === tenantId
      );
    }

    // Get total count for pagination
    const total = transformedSubscriptions.length;

    // Apply pagination
    const startIndex = (page - 1) * limit;
    const paginatedSubscriptions = transformedSubscriptions.slice(startIndex, startIndex + limit);

    // Get aggregation data for charts from real subscription data
    const planCounts = transformedSubscriptions
      .filter((sub: any) => sub.status === "active")
      .reduce((acc: any, sub: any) => {
        acc[sub.plan] = (acc[sub.plan] || 0) + 1;
        return acc;
      }, {});

    const billingCounts = transformedSubscriptions
      .filter((sub: any) => sub.status === "active")
      .reduce((acc: any, sub: any) => {
        const billing = sub.billingInterval === "year" ? "Yearly" : "Monthly";
        acc[billing] = (acc[billing] || 0) + 1;
        return acc;
      }, {});

    const planStats = Object.entries(planCounts).map(([name, value]) => ({
      name,
      value: value as number,
    }));

    const billingStats = Object.entries(billingCounts).map(([name, value]) => ({
      name,
      value: value as number,
    }));

    // Calculate pagination info
    const totalPages = Math.ceil(total / limit);

    return {
      subscriptions: paginatedSubscriptions,
      planDistribution: planStats,
      billingDistribution: billingStats,
      allTenants,
      pagination: {
        page,
        limit,
        total,
        totalPages,
      },
      searchQuery,
    };
  } catch (error) {
    console.error('Error fetching subscriptions data:', error);
    // Return fallback data
    return {
      subscriptions: [],
      planDistribution: [],
      billingDistribution: [],
      allTenants: [],
      pagination: {
        page: 1,
        limit: 5,
        total: 0,
        totalPages: 0,
      },
      searchQuery: "",
    };
  }
}

// Main server component
export default async function SubscriptionsPage({
  searchParams,
}: {
  searchParams: { search?: string; page?: string; limit?: string; tenant?: string };
}) {
  const session = await getServerSession(authOptions);
  if (!session) {
    redirect("/auth/signin");
  }

  // Parse search params with defaults
  const searchQuery = searchParams.search || "";
  const page = parseInt(searchParams.page || "1", 10);
  const limit = parseInt(searchParams.limit || "5", 10);
  const tenantId = searchParams.tenant || "";

  // Validate pagination parameters
  const validPage = Math.max(1, page);
  const validLimit = Math.min(Math.max(1, limit), 100); // Max 100 items per page

  // Fetch subscriptions data server-side
  const subscriptionsData = await fetchSubscriptionsData(searchQuery, validPage, validLimit, tenantId);

  return <SubscriptionsTableClient data={subscriptionsData} />;
}
