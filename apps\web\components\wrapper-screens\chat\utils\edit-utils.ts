import { Message } from '../types';

/**
 * Determines if a message can be edited based on its position in the chat
 * Only the last 5 user messages can be edited, assistant messages are never editable
 * @param message The message to check
 * @param allMessages All messages in the conversation
 * @returns Whether the message can be edited
 */
export const canEditMessage = (message: Message, allMessages: Message[]): boolean => {
  // Only user messages can be edited
  if (message.role !== "user") {
    return false;
  }

  // Only count ORIGINAL user messages (exclude edited and regenerated versions)
  // A message is considered original if:
  // 1. It has no originalMessageId (not a child of another message)
  // 2. It doesn't have metadata.editedResponse === true
  // 3. It doesn't have metadata.regeneratedResponse === true
  const isOriginalUserMessage = (msg: Message): boolean => {
    return (
      msg.role === "user" &&
      !msg.originalMessageId &&
      !msg.metadata?.editedResponse &&
      !msg.metadata?.regeneratedResponse
    );
  };

  // Check if the current message is an original user message
  if (!isOriginalUserMessage(message)) {
    return false;
  }

  // Find all ORIGINAL user messages in the entire conversation (exclude edited/regenerated versions)
  const allOriginalUserMessages = allMessages.filter(isOriginalUserMessage);

  // Sort original user messages by their position in the conversation (chronological order)
  // Since messages are already in chronological order in the array, we can use their indices
  const originalUserMessagesWithIndices = allOriginalUserMessages.map(msg => {
    const index = allMessages.findIndex(m => m.id === msg.id);
    return { message: msg, index };
  }).sort((a, b) => a.index - b.index);

  // Find the position of the current message among all original user messages
  const currentMessagePosition = originalUserMessagesWithIndices.findIndex(
    item => item.message.id === message.id
  );

  if (currentMessagePosition === -1) {
    return false; // Message not found in original user messages
  }

  // Only allow editing if this user message is among the last 5 ORIGINAL user messages
  const totalOriginalUserMessages = originalUserMessagesWithIndices.length;
  const lastFiveUserThreshold = Math.max(0, totalOriginalUserMessages - 5);

  return currentMessagePosition >= lastFiveUserThreshold;
};

/**
 * Determines if a specific message is currently being edited
 * @param message The message to check
 * @returns Whether the message is being edited
 */
export const isMessageBeingEdited = (message: Message): boolean => {
  return message.metadata?.isEditing === true;
};

/**
 * Creates a temporary edited message for immediate UI updates
 * @param originalMessage The original message being edited
 * @param newContent The new content for the edited message
 * @returns A temporary edited message object
 */
export const createTemporaryEditedMessage = (
  originalMessage: Message,
  newContent: string
): Message => {
  return {
    role: originalMessage.role,
    content: newContent,
    metadata: {
      editedResponse: true,
      isSelected: true,
      isEditing: false,
      // Preserve image metadata
      hasImages: originalMessage.metadata?.hasImages,
      images: originalMessage.metadata?.images,
    },
    originalMessageId: originalMessage.id,
    sources: originalMessage.sources || [],
    images: originalMessage.images,
  };
};

/**
 * Adds a temporary edited message to the messages array
 * @param messages Current messages array
 * @param originalMessage The original message being edited
 * @param editedMessage The edited message to add
 * @returns Updated messages array
 */
export const addTemporaryEditedMessage = (
  messages: Message[],
  originalMessage: Message,
  editedMessage: Message
): Message[] => {
  return messages.map((msg) => {
    if (msg.id === originalMessage.id) {
      // Get existing edited messages or initialize empty array
      const existingEditedMessages = msg.editedMessages || [];

      // Create a new array with the edited message added
      const updatedEditedMessages = [
        ...existingEditedMessages,
        editedMessage,
      ];

      return {
        ...msg,
        editedMessages: updatedEditedMessages,
        metadata: {
          ...msg.metadata,
          originalResponse: true,
        },
      };
    }
    return msg;
  });
};

/**
 * Updates an existing edited message with new content
 * @param messages Current messages array
 * @param originalMessage The original message
 * @param newContent New content to update with
 * @returns Updated messages array
 */
export const updateEditedMessage = (
  messages: Message[],
  originalMessage: Message,
  newContent: string
): Message[] => {
  return messages.map((msg) => {
    // Find the original message
    if (msg.id === originalMessage.id && msg.editedMessages) {
      // Find and update the most recent edited message
      const updatedEditedMessages = msg.editedMessages.map((editedMsg, index) => {
        // Update the last edited message (most recent)
        if (index === msg.editedMessages!.length - 1) {
          return {
            ...editedMsg,
            content: newContent,
          };
        }
        return editedMsg;
      });

      return {
        ...msg,
        editedMessages: updatedEditedMessages,
      };
    }
    return msg;
  });
};

/**
 * Removes the temporary edited message (used when canceling edit)
 * @param messages Current messages array
 * @param originalMessage The original message
 * @returns Updated messages array
 */
export const removeTemporaryEditedMessage = (
  messages: Message[],
  originalMessage: Message
): Message[] => {
  return messages.map((msg) => {
    if (msg.id === originalMessage.id && msg.editedMessages) {
      // Remove the last (temporary) edited message
      const updatedEditedMessages = msg.editedMessages.slice(0, -1);

      return {
        ...msg,
        editedMessages: updatedEditedMessages.length > 0 ? updatedEditedMessages : undefined,
        metadata: {
          ...msg.metadata,
          originalResponse: updatedEditedMessages.length > 0,
        },
      };
    }
    return msg;
  });
};

/**
 * Gets the total number of versions for a message (original + edited)
 * @param message The message to check
 * @returns Total number of versions
 */
export const getEditVersionCount = (message: Message): number => {
  const editedCount = message.editedMessages?.length || 0;
  return 1 + editedCount; // 1 for original + edited versions
};

/**
 * Gets the currently displayed edited message based on display index
 * @param message The original message
 * @param displayIndex The current display index (0 = original, 1+ = edited versions)
 * @returns The message to display
 */
export const getDisplayedEditedMessage = (
  message: Message,
  displayIndex: number
): Message => {
  if (displayIndex === 0 || !message.editedMessages || message.editedMessages.length === 0) {
    return message; // Return original message
  }

  const editedIndex = displayIndex - 1;
  if (editedIndex < message.editedMessages.length) {
    return message.editedMessages[editedIndex];
  }

  // Fallback to original if index is out of bounds
  return message;
};

/**
 * Checks if a message has edited versions
 * @param message The message to check
 * @returns Whether the message has edited versions
 */
export const hasEditedVersions = (message: Message): boolean => {
  return !!(message.editedMessages && message.editedMessages.length > 0);
};

/**
 * Finds the assistant message that follows a user message and has edit-triggered responses
 * @param messages All messages in the chat
 * @param userMessageIndex Index of the user message
 * @returns The assistant message with edit-triggered responses, or null if none found
 */
export const findAssistantMessageWithEditTriggeredResponse = (
  messages: Message[],
  userMessageIndex: number
): Message | null => {
  // Look for the next assistant message after the user message
  for (let i = userMessageIndex + 1; i < messages.length; i++) {
    const message = messages[i];
    if (message.role === "assistant") {
      // Check if this assistant message has edited versions that were triggered by user edits
      if (message.editedMessages && message.editedMessages.length > 0) {
        // For now, assume any assistant message with edited versions after a user message
        // with edited versions indicates an edit-triggered response
        return message;
      }
      // If we found an assistant message but it doesn't have edit-triggered responses, stop looking
      break;
    }
  }
  return null;
};

/**
 * Checks if an assistant message was triggered by a user message edit
 * @param messages All messages in the chat
 * @param assistantMessage The assistant message to check
 * @param assistantMessageIndex Index of the assistant message
 * @returns Whether this assistant message was triggered by a user edit
 */
export const isAssistantMessageTriggeredByUserEdit = (
  messages: Message[],
  assistantMessage: Message,
  assistantMessageIndex: number
): boolean => {
  // Only consider assistant messages with edited versions
  if (!assistantMessage.editedMessages || assistantMessage.editedMessages.length === 0) {
    return false;
  }

  // Look for the previous user message
  for (let i = assistantMessageIndex - 1; i >= 0; i--) {
    const message = messages[i];
    if (message.role === "user") {
      // Check if this user message has edited versions
      if (message.editedMessages && message.editedMessages.length > 0) {
        // Check if the assistant message that follows this user message is our target
        const followingAssistant = findAssistantMessageWithEditTriggeredResponse(messages, i);
        return followingAssistant?.id === assistantMessage.id;
      }
      // If we found a user message without edits, this assistant wasn't triggered by user edit
      break;
    }
  }
  return false;
};

/**
 * Checks if a user message should hide its carousel because it has a corresponding AI response with edit-triggered versions
 * @param messages All messages in the chat
 * @param userMessage The user message to check
 * @param userMessageIndex Index of the user message
 * @returns Whether the user message should hide its carousel
 */
export const shouldHideUserMessageCarousel = (
  messages: Message[],
  userMessage: Message,
  userMessageIndex: number
): boolean => {
  // Only hide carousel for user messages that have edited versions
  if (!hasEditedVersions(userMessage)) {
    return false;
  }

  // Check if there's a corresponding assistant message with edit-triggered responses
  const assistantMessage = findAssistantMessageWithEditTriggeredResponse(messages, userMessageIndex);
  return assistantMessage !== null;
};

/**
 * Gets the total versions count for a unified carousel (user message versions + AI response versions)
 * @param userMessage The user message
 * @param _assistantMessage The corresponding assistant message (kept for future extensibility)
 * @returns Total number of versions for unified navigation
 */
export const getUnifiedCarouselVersionCount = (
  userMessage: Message,
  _assistantMessage: Message
): number => {
  const userEditedCount = userMessage.editedMessages?.length || 0;

  // Each user version corresponds to one conversation thread
  // Original user + edited user versions = conversation threads
  // Each thread has: 1 user message + 1 assistant response (original or edited/regenerated)
  return 1 + userEditedCount; // Number of conversation threads
};

/**
 * Gets the current conversation thread index for unified carousel navigation
 * @param _userMessage The user message (kept for future extensibility)
 * @param _assistantMessage The corresponding assistant message (kept for future extensibility)
 * @param userDisplayIndex Current display index for user message
 * @param _assistantDisplayIndex Current display index for assistant message (kept for future extensibility)
 * @returns The current conversation thread index
 */
export const getCurrentConversationThreadIndex = (
  _userMessage: Message,
  _assistantMessage: Message,
  userDisplayIndex: number,
  _assistantDisplayIndex: number
): number => {
  // The conversation thread index is determined by which user message version is being displayed
  // We keep the parameters for future extensibility even if not all are currently used
  return userDisplayIndex;
};

/**
 * Creates a temporary message for streaming AI response after edit
 * @param originalMessage The original message that triggered the response
 * @returns A temporary message object for streaming
 */
export const createTemporaryAIResponseMessage = (): Message => {
  return {
    role: "assistant",
    content: "",
    metadata: {
      editedResponse: true,
      isSelected: true,
      isStreaming: true,
    },
    sources: [],
  };
};

/**
 * Finds the assistant message that should be regenerated after a user message edit
 * @param messages All messages in the chat
 * @param editedUserMessageIndex Index of the edited user message
 * @returns The assistant message to regenerate, or null if none found
 */
export const findAssistantMessageToRegenerate = (
  messages: Message[],
  editedUserMessageIndex: number
): Message | null => {
  // Look for the next assistant message after the edited user message
  for (let i = editedUserMessageIndex + 1; i < messages.length; i++) {
    if (messages[i].role === "assistant") {
      return messages[i];
    }
  }
  return null;
};

/**
 * Builds the conversation context up to the edited message for AI generation
 * @param messages All messages in the chat
 * @param editedMessageIndex Index of the edited message
 * @param editedContent The new content of the edited message
 * @returns Object containing context messages and images from the edited message
 */
export const buildContextForEditedMessage = (
  messages: Message[],
  editedMessageIndex: number,
  editedContent: string
): {
  contextMessages: Array<{
    id: string;
    role: "user" | "assistant";
    content: string;
    createdAt: Date;
  }>;
  images: any[];
} => {
  const contextMessages: Array<{
    id: string;
    role: "user" | "assistant";
    content: string;
    createdAt: Date;
  }> = [];

  // Include all messages up to (but not including) the edited message
  for (let i = 0; i < editedMessageIndex; i++) {
    const msg = messages[i];
    contextMessages.push({
      id: msg.id || Math.random().toString(),
      role: msg.role,
      content: msg.content,
      createdAt: new Date(),
    });
  }

  // Add the edited message with new content
  const editedMessage = messages[editedMessageIndex];
  contextMessages.push({
    id: editedMessage.id || Math.random().toString(),
    role: editedMessage.role,
    content: editedContent,
    createdAt: new Date(),
  });

  // Extract images from the edited message if it's a user message
  const images = editedMessage.role === "user"
    ? (editedMessage.images || editedMessage.metadata?.images || [])
    : [];

  return {
    contextMessages,
    images,
  };
};
