"use client";

import { DeleteConfirmationDialog } from "./delete-confirmation-dialog";
import { useLanguage } from "@/lib/language-context";

interface PageDeleteConfirmationProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  pageName: string;
  isLoading?: boolean;
}

export function PageDeleteConfirmation({
  isOpen,
  onClose,
  onConfirm,
  pageName,
  isLoading = false,
}: PageDeleteConfirmationProps) {
  const { t } = useLanguage();

  return (
    <DeleteConfirmationDialog
      isOpen={isOpen}
      onClose={onClose}
      onConfirm={onConfirm}
      title={t("workspace.deletePageConfirm")}
      description={t("workspace.deletePageWarning", { name: pageName })}
      isLoading={isLoading}
      loadingText={t("workspace.deletingPage")}
      destructiveActionText={t("common.delete")}
    />
  );
}
