import React from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { Message } from '../types';
import { useLanguage } from "@/lib/language-context";

interface MessageCarouselProps {
  message: Message;
  currentVersionIndex: number;
  totalVersions: number;
  updateDisplayIndex: (messageId: string, newIndex: number, isManuallySet: boolean) => void;
}

export const MessageCarousel: React.FC<MessageCarouselProps> = ({
  message,
  currentVersionIndex,
  totalVersions,
  updateDisplayIndex,
}) => {
  const { t } = useLanguage();

  return (
    <div className="flex items-center gap-1">
      <Button
        variant="ghost"
        size="icon"
        className="h-7 w-7 rounded-full"
        onClick={() => {
          // Navigate to previous version
          if (message.id && currentVersionIndex > 0) {
            // Use the combined update function to update both states in a single batch
            updateDisplayIndex(
              message.id as string,
              currentVersionIndex - 1,
              true // Mark as manually set
            );
          }
        }}
        // Disable prev button when viewing original
        disabled={currentVersionIndex === 0}
        title={t("chat.previousResponse")}
      >
        <ChevronLeft className="h-4 w-4" />
      </Button>

      {/* Carousel indicator dots */}
      <div className="flex items-center gap-1 mx-1">
        {Array.from({ length: totalVersions }).map((_, i) => (
          <div
            key={i}
            className={`h-1.5 w-1.5 rounded-full transition-all duration-200 ${
              i === currentVersionIndex
                ? "bg-primary scale-125"
                : "bg-gray-300 dark:bg-gray-600"
            }`}
          />
        ))}
      </div>

      <Button
        variant="ghost"
        size="icon"
        className="h-7 w-7 rounded-full"
        onClick={() => {
          // Navigate to next version
          if (
            message.id &&
            currentVersionIndex < totalVersions - 1
          ) {
            // Use the combined update function to update both states in a single batch
            updateDisplayIndex(
              message.id as string,
              currentVersionIndex + 1,
              true // Mark as manually set
            );
          }
        }}
        // Disable next button when viewing the last regenerated message
        disabled={currentVersionIndex === totalVersions - 1}
        title={t("chat.nextResponse")}
      >
        <ChevronRight className="h-4 w-4" />
      </Button>
    </div>
  );
};
