"use client";

import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  MCPServer,
  MCPServerCreate,
  MCPServerUpdate,
  MCPServerType,
} from "@/types/mcp-server";
import { Plus, Minus, Save, X, Info } from "lucide-react";
import { useLanguage } from "@/lib/language-context";

interface MCPServerFormProps {
  server?: MCPServer;
  onSubmit: (data: MCPServerCreate | MCPServerUpdate) => Promise<void>;
  onCancel: () => void;
  loading?: boolean;
}

interface FormData {
  name: string;
  description: string;
  server_type: MCPServerType;
  url: string;
  headers: Array<{ key: string; value: string }>;
  timeout: number;
  auto_restart: boolean;
  is_public: boolean;
}

export function MCPServerForm({
  server,
  onSubmit,
  onCancel,
  loading = false,
}: MCPServerFormProps) {
  const { t } = useLanguage();
  const isEditing = !!server;

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors, isSubmitting },
  } = useForm<FormData>({
    defaultValues: {
      name: server?.name || "",
      description: server?.description || "",
      server_type: "HTTP", // Always HTTP
      url: server?.url || "",
      headers: server?.headers
        ? Object.entries(server.headers).map(([key, value]) => ({ key, value }))
        : [],
      timeout: server?.timeout || 30000,
      auto_restart: server?.auto_restart ?? true,
      is_public: server?.is_public ?? false,
    },
  });

  const [headers, setHeaders] = useState<Array<{ key: string; value: string }>>(
    server?.headers
      ? Object.entries(server.headers).map(([key, value]) => ({ key, value }))
      : [{ key: "", value: "" }]
  );

  // Set server type to HTTP and sync with form
  useEffect(() => {
    setValue("server_type", "HTTP");
  }, [setValue]);

  const addHeader = () => {
    setHeaders([...headers, { key: "", value: "" }]);
  };

  const removeHeader = (index: number) => {
    const newHeaders = headers.filter((_, i) => i !== index);
    setHeaders(newHeaders);
    setValue("headers", newHeaders);
  };

  const updateHeader = (
    index: number,
    field: "key" | "value",
    value: string
  ) => {
    const newHeaders = [...headers];
    newHeaders[index][field] = value;
    setHeaders(newHeaders);
    setValue("headers", newHeaders);
  };

  const onFormSubmit = async (data: FormData) => {
    // Convert headers array to object
    const headersObject: Record<string, string> = {};
    data.headers.forEach(({ key, value }) => {
      if (key.trim() && value.trim()) {
        headersObject[key.trim()] = value.trim();
      }
    });

    const submitData: MCPServerCreate | MCPServerUpdate = {
      name: data.name.trim(),
      description: data.description.trim() || undefined,
      server_type: "HTTP", // Always HTTP
      url: data.url.trim(),
      headers: Object.keys(headersObject).length > 0 ? headersObject : undefined,
      timeout: data.timeout,
      auto_restart: data.auto_restart,
      is_public: data.is_public,
    };

    await onSubmit(submitData);
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          {isEditing ? t("mcpServers.form.editServer") : t("mcpServers.form.addServer")}
          {server && (
            <Badge variant="outline" className="ml-auto">
              {server.status}
            </Badge>
          )}
        </CardTitle>
      </CardHeader>

      <CardContent>
        <form onSubmit={handleSubmit(onFormSubmit)} className="space-y-6">
          {/* Hidden field to set server_type to HTTP */}
          <input type="hidden" {...register("server_type")} value="HTTP" />

          {/* Basic Information */}
          <div className="space-y-4">
            <div>
              <Label htmlFor="name">{t("mcpServers.form.name")} *</Label>
              <Input
                id="name"
                {...register("name", {
                  required: t("mcpServers.form.nameRequired"),
                  minLength: {
                    value: 2,
                    message: t("mcpServers.form.nameMinLength"),
                  },
                })}
                placeholder={t("mcpServers.form.namePlaceholder")}
                className={errors.name ? "border-red-500" : ""}
              />
              {errors.name && (
                <p className="text-sm text-red-500 mt-1">
                  {errors.name.message}
                </p>
              )}
            </div>

            <div>
              <Label htmlFor="description">{t("mcpServers.form.description")}</Label>
              <Textarea
                id="description"
                {...register("description")}
                placeholder={t("mcpServers.form.descriptionPlaceholder")}
                rows={2}
              />
            </div>

            {/* HTTP Only Information */}
            <Alert>
              <Info className="h-4 w-4" />
              <AlertDescription>
                {t("mcpServers.form.httpOnlyMessage")}
              </AlertDescription>
            </Alert>
          </div>

          {/* HTTP Server Configuration - Always shown since we only support HTTP */}
          <Separator />

          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Badge variant="secondary">{t("mcpServers.form.httpType")}</Badge>
              <span className="text-sm text-muted-foreground">
                {t("mcpServers.form.httpDescription")}
              </span>
            </div>

            <div>
              <Label htmlFor="url">{t("mcpServers.form.url")} *</Label>
              <Input
                id="url"
                {...register("url", {
                  required: t("mcpServers.form.urlRequired"),
                  pattern: {
                    value: /^https?:\/\/.+/,
                    message: t("mcpServers.form.invalidUrl"),
                  },
                })}
                placeholder={t("mcpServers.form.urlPlaceholder")}
                className={errors.url ? "border-red-500" : ""}
              />
              {errors.url && (
                <p className="text-sm text-red-500 mt-1">
                  {errors.url.message}
                </p>
              )}
            </div>
          </div>

          <Separator />

          {/* HTTP Headers */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Label>{t("mcpServers.form.httpHeaders")}</Label>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={addHeader}
                className="flex items-center gap-1"
              >
                <Plus className="h-4 w-4" />
                {t("mcpServers.form.addHeader")}
              </Button>
            </div>

            <div className="text-sm text-muted-foreground">
              {t("mcpServers.form.httpHeadersDescription")}
            </div>

            {headers.map((header, index) => (
              <div key={index} className="flex items-center gap-2">
                <Input
                  value={header.key}
                  onChange={(e) =>
                    updateHeader(index, "key", e.target.value)
                  }
                  placeholder={t("mcpServers.form.headerNamePlaceholder")}
                  className="flex-1"
                />
                <Input
                  value={header.value}
                  onChange={(e) =>
                    updateHeader(index, "value", e.target.value)
                  }
                  placeholder={t("mcpServers.form.headerValuePlaceholder")}
                  className="flex-1"
                  type={
                    header.key.toLowerCase().includes("authorization")
                      ? "password"
                      : "text"
                  }
                />
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => removeHeader(index)}
                  disabled={headers.length === 1}
                >
                  <Minus className="h-4 w-4" />
                </Button>
              </div>
            ))}
          </div>
          <Separator />

          {/* Server Configuration */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Label className="text-base font-medium">
                {t("mcpServers.form.serverConfiguration")}
              </Label>
            </div>

            <div>
              <Label htmlFor="timeout">{t("mcpServers.form.timeout")}</Label>
              <Input
                id="timeout"
                type="number"
                {...register("timeout", {
                  required: t("mcpServers.form.timeoutRequired"),
                  min: {
                    value: 1000,
                    message: t("mcpServers.form.timeoutMinError"),
                  },
                  max: {
                    value: 300000,
                    message: t("mcpServers.form.timeoutMaxError"),
                  },
                })}
                placeholder={t("mcpServers.form.timeoutPlaceholder")}
                className={errors.timeout ? "border-red-500" : ""}
              />
              <p className="text-sm text-muted-foreground mt-1">
                {t("mcpServers.form.timeoutDescription")}
              </p>
              {errors.timeout && (
                <p className="text-sm text-red-500 mt-1">
                  {errors.timeout.message}
                </p>
              )}
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="auto_restart">{t("mcpServers.form.autoRestart")}</Label>
                <p className="text-sm text-muted-foreground">
                  {t("mcpServers.form.autoRestartDescription")}
                </p>
              </div>
              <Switch
                id="auto_restart"
                {...register("auto_restart")}
                checked={watch("auto_restart")}
                onCheckedChange={(checked) => setValue("auto_restart", checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="is_public">{t("mcpServers.form.isPublic")}</Label>
                <p className="text-sm text-muted-foreground">
                  {t("mcpServers.form.isPublicDescription")}
                </p>
              </div>
              <Switch
                id="is_public"
                {...register("is_public")}
                checked={watch("is_public")}
                onCheckedChange={(checked) => setValue("is_public", checked)}
              />
            </div>
          </div>

          {/* Form Actions */}
          <div className="flex items-center gap-3 pt-4">
            <Button
              type="submit"
              disabled={isSubmitting || loading}
              className="flex items-center gap-2"
            >
              <Save className="h-4 w-4" />
              {isEditing ? t("mcpServers.form.updating") : t("mcpServers.form.creating")}
            </Button>

            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={isSubmitting || loading}
              className="flex items-center gap-2"
            >
              <X className="h-4 w-4" />
              {t("mcpServers.form.cancel")}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
