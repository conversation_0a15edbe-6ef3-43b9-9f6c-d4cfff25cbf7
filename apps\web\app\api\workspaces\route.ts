import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import db from "@/lib/shared-db";
import { authOptions } from "@/lib/next-auth/src/auth-options";
import { checkActiveSubscription } from "@/lib/subscription-check";
import {
  withPermission,
  withActiveSubscription,
} from "@/lib/permission-middleware";
import { hasPermission } from "@/lib/permissions";
import { getTranslations } from "@/lib/server-i18n";
import { BlobServiceClient } from "@azure/storage-blob";
import jwt from "jsonwebtoken";

// Get workspaces for the current user
const getWorkspaces = async (request: Request) => {
  try {
    // Get userId and tenantId from headers
    const userId = request.headers.get("x-user-id");
    if (!userId) {
      return NextResponse.json({ error: "x-user-id header required" }, { status: 400 });
    }
    const tenantId = request.headers.get("x-tenant-id");
    if (!tenantId) {
      return NextResponse.json({ error: "x-tenant-id header required" }, { status: 400 });
    }

    const url = request.url;
    const urlParams = new URLSearchParams(url.split("?")[1]);

    const rawSlug = urlParams.get("slug") ?? "";
    const slug = decodeURI(rawSlug);

    // Check if user is an owner or admin
    const membership = await db.membership.findFirst({
      where: {
        userId,
        tenantId,
      },
      include: {
        customRole: {
          include: {
            permissions: {
              include: {
                permission: true,
              },
            },
          },
        },
      },
    });

    const isOwnerOrAdmin =
      membership?.role === "OWNER" || membership?.role === "ADMIN";

    if (slug) {
      // For owners and admins, show any workspace in the tenant
      // For regular users, show workspaces they have access to (direct or through groups)
      let workspaceQuery;

      if (isOwnerOrAdmin) {
        workspaceQuery = {
          slug,
          tenantId,
        };
      } else {
        // For regular users, check both direct membership and group-based access
        workspaceQuery = {
          slug,
          tenantId,
          OR: [
            // Direct workspace membership
            {
              workspaceMember: {
                some: {
                  userId,
                },
              },
            },
            // Group-based workspace access
            {
              groupWorkspaces: {
                some: {
                  group: {
                    groupMembers: {
                      some: {
                        userId,
                      },
                    },
                  },
                },
              },
            },
          ],
        };
      }

      const workspace = await Promise.all([
        db.workspace.findFirst({
          where: workspaceQuery,
          include: {
            pages: true,
          },
        }),
        db.workspaceMember.findMany({
          where: {
            userId,
            workspace: {
              slug,
            },
          },
          include: {
            customRole: {
              include: {
                permissions: {
                  include: {
                    permission: true,
                  },
                },
              },
            },
          },
        }),
      ]);
      // Use the updated hasPermission function that respects built-in role configurations
      const singleWorkspacePermissions = {
        workspace: {
          create: await hasPermission(userId, tenantId, "CREATE", "WORKSPACE", slug),
          read: await hasPermission(userId, tenantId, "READ", "WORKSPACE", slug),
          update: await hasPermission(userId, tenantId, "UPDATE", "WORKSPACE", slug),
          delete: await hasPermission(userId, tenantId, "DELETE", "WORKSPACE", slug),
        },
      };

      return NextResponse.json(
        {
          workspace: workspace[0],
          permission: singleWorkspacePermissions,
        },
        { status: 200 }
      );
    }

    // Check if user has READ permission for WORKSPACE resource using built-in role configurations
    const hasWorkspaceReadPermission = await hasPermission(userId, tenantId, "READ", "WORKSPACE");

    // If user doesn't have READ permission for WORKSPACE, return empty list
    if (!hasWorkspaceReadPermission) {
      return NextResponse.json(
        {
          workspaces: [],
          permission: {
            workspace: {
              create: await hasPermission(userId, tenantId, "CREATE", "WORKSPACE"),
              read: false,
              update: await hasPermission(userId, tenantId, "UPDATE", "WORKSPACE"),
              delete: await hasPermission(userId, tenantId, "DELETE", "WORKSPACE"),
            },
          },
        },
        { status: 200 }
      );
    }

    // For users with READ permission, show workspaces they are members of (direct or through groups)
    let filteredIds;
    if (!isOwnerOrAdmin) {
      // Get direct workspace memberships
      const directWorkspaceMembers = await db.workspaceMember.findMany({
        where: {
          userId,
          workspace: {
            tenantId,
          },
        },
        include: {
          workspace: true,
          customRole: {
            include: {
              permissions: {
                include: {
                  permission: true,
                },
              },
            },
          },
        },
      });

      // Get group-based workspace access
      const userGroups = await db.groupMember.findMany({
        where: {
          userId,
        },
        include: {
          group: {
            include: {
              groupWorkspaces: {
                where: {
                  workspace: {
                    tenantId,
                  },
                },
                include: {
                  workspace: true,
                  customRole: {
                    include: {
                      permissions: {
                        include: {
                          permission: true,
                        },
                      },
                    },
                  },
                },
              },
              customRole: {
                include: {
                  permissions: {
                    include: {
                      permission: true,
                    },
                  },
                },
              },
            },
          },
        },
      });

      const accessibleWorkspaceIds = new Set();

      // Add direct workspace access
      directWorkspaceMembers.forEach((member: any) => {
        // Workspace owners and admins have automatic access
        if (member.role === "OWNER" || member.role === "ADMIN") {
          accessibleWorkspaceIds.add(member.workspace.id);
        }
        // Check custom role permissions for other roles
        else if (member.customRole) {
          const hasRequiredPermission = member.customRole.permissions.some(
            (p: any) =>
              p.permission.action === "READ" &&
              p.permission.resource === "WORKSPACE"
          );
          if (hasRequiredPermission) {
            accessibleWorkspaceIds.add(member.workspace.id);
          }
        }
      });

      // Add group-based workspace access
      userGroups.forEach((groupMember: any) => {
        groupMember.group.groupWorkspaces.forEach((groupWorkspace: any) => {
          // Check workspace-specific role first
          if (groupWorkspace.customRole) {
            const hasRequiredPermission = groupWorkspace.customRole.permissions.some(
              (p: any) =>
                p.permission.action === "READ" &&
                p.permission.resource === "WORKSPACE"
            );
            if (hasRequiredPermission) {
              accessibleWorkspaceIds.add(groupWorkspace.workspace.id);
            }
          }
          // If no workspace-specific role, check group's general role
          else if (groupMember.group.customRole) {
            const hasRequiredPermission = groupMember.group.customRole.permissions.some(
              (p: any) =>
                p.permission.action === "READ" &&
                p.permission.resource === "WORKSPACE"
            );
            if (hasRequiredPermission) {
              accessibleWorkspaceIds.add(groupWorkspace.workspace.id);
            }
          }
        });
      });

      filteredIds = Array.from(accessibleWorkspaceIds);
    }
    const workspacesQuery = isOwnerOrAdmin
      ? {
          tenantId,
        }
      : {
          tenantId,
          id: {
            in: filteredIds,
          },
        };

    const workspaces = await db.workspace.findMany({
      where: workspacesQuery,
      include: {
        pages: true,
      },
    });

    // Get comprehensive permissions including group-based permissions
    const { getUserPermissions } = await import("@/lib/permissions");

    // Ensure userId and tenantId are not null before calling getUserPermissions
    if (!userId || !tenantId) {
      return NextResponse.json(
        { error: "User ID and Tenant ID are required" },
        { status: 400 }
      );
    }

    const userPermissions = await getUserPermissions(userId, tenantId);

    // Use the updated hasPermission function that respects built-in role configurations
    const workspaceListPermissions = {
      workspace: {
        create: await hasPermission(userId, tenantId, "CREATE", "WORKSPACE"),
        read: await hasPermission(userId, tenantId, "READ", "WORKSPACE"),
        update: await hasPermission(userId, tenantId, "UPDATE", "WORKSPACE"),
        delete: await hasPermission(userId, tenantId, "DELETE", "WORKSPACE"),
      },
    };

    return NextResponse.json(
      {
        workspaces,
        permission: workspaceListPermissions,
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error fetching workspaces:", error);
    return NextResponse.json(
      { error: "Failed to fetch workspaces" },
      { status: 500 }
    );
  }
};

// Create a new workspace
const createWorkspace = async (request: Request) => {
  try {
    const session: any = await getServerSession(authOptions);

    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { title, slug, description, initials, tenantId } =
      await request.json();

    // Check if tenant has an active subscription
    const { hasActiveSubscription } = await checkActiveSubscription(tenantId);

    if (!hasActiveSubscription) {
      return NextResponse.json(
        { error: "You need an active subscription to create workspaces" },
        { status: 403 }
      );
    }

    // Permission checking is handled by the withPermission middleware
    // No need for additional role-based checks here since the middleware
    // already validates CREATE WORKSPACE permission using the RBAC system

    // Create workspace and membership in a transaction
    const result = await db.$transaction(async (tx: any) => {
      // Create the workspace
      const newWorkspace = await tx.workspace.create({
        data: {
          name: title,
          slug: `${slug}-${Date.now()}`,
          description,
          tenantId,
          initials,
        },
      });
      // Get the user's membership in the tenant to create workspace membership
      const userMembership = await tx.membership.findFirst({
        where: {
          userId: session.userId,
          tenantId,
        },
      });

      if (!userMembership) {
        throw new Error("User membership not found");
      }

      // Create workspace membership for the creator with OWNER role
      const membership = await tx.workspaceMember.create({
        data: {
          userId: session.userId,
          workspaceId: newWorkspace.id,
          membershipId: userMembership.id,
          role: "OWNER", // Creator becomes owner of the workspace
        },
      });
      await tx.page.create({
        data: {
          name: "General",
          workspaceId: newWorkspace.id,
        },
      });

      return {
        ...newWorkspace,
        role: membership.role,
      };
    });

    return NextResponse.json(
      {
        message: "Workspace created successfully",
        data: result,
      },
      { status: 201 }
    );
  } catch (error) {
    console.error("Error creating Workspace:", error);
    return NextResponse.json(
      { error: "Failed to create Workspace" },
      { status: 500 }
    );
  }
};

// Delete a workspace
const deleteWorkspace = async (request: Request) => {
  try {
    const session: any = await getServerSession(authOptions);
    if (!session?.userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    const tenantId = request.headers.get("x-tenant-id");
    const url = request.url;
    const urlParams = new URLSearchParams(url.split("?")[1]);
    const slug = decodeURI(urlParams.get("slug") || "");
    const workspace = await db.workspace.findFirst({
      where: {
        slug: decodeURI(slug),
      },
      include: {
        pages: true,
        files: true,
      },
    });
    const files = workspace?.files;
    const pages = workspace?.pages?.map((item: any) => item.id);
    const workspaceId = workspace?.id;
    const payload = {
      userId: session.userId,
      email: session.user?.email,
      name: session.user?.name,
      // Include the current organization if available
      organizationId: session.currentOrganization?.id,
      // Add any other information you need
      iat: Math.floor(Date.now() / 1000),
    };

    // Generate a token directly
    const secret = process.env.NEXTAUTH_SECRET;
    if (!secret) {
      throw new Error("NEXTAUTH_SECRET is not defined");
    }

    // Create a token that expires in 1 hour
    const token = jwt.sign(payload, secret, { expiresIn: "1h" });

    await Promise.all(
      files?.map(async (file) => {
        // Delete from multiple storage platforms
        try {
          const vectorDeleteResponse = await fetch(
            `${
              process.env.NEXT_PUBLIC_API_BASE_URL || "http://localhost:8000"
            }/api/v1/workspace-chat/delete-file?file_id=${
              file.id
            }&workspaceSlug=${encodeURIComponent(
              slug || ""
            )}&tenant_id=${tenantId}`,
            {
              method: "GET",
              headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${token}`,
              },
            }
          );

          if (!vectorDeleteResponse.ok) {
            console.error(`Failed to delete vectors for file ${file.id}`);
          }

          // Delete from Azure Blob Storage
          const blobServiceClient = BlobServiceClient.fromConnectionString(
            process.env.AZURE_STORAGE_CONNECTION_STRING || ""
          );
          const containerClient = blobServiceClient.getContainerClient(
            process.env.AZURE_STORAGE_CONTAINER_NAME || ""
          );
          const parts = decodeURIComponent(
            file?.url?.split("/").slice(-3)?.join("/")
          );
          const blobClient = containerClient.getBlobClient(parts || "");
          await blobClient.delete();
        } catch (storageError) {
          console.error("Error deleting file from storage:", storageError);
          const { t } = getTranslations();
          return NextResponse.json(
            { error: t("api.errors.failedToDeleteFile") },
            { status: 500 }
          );
        }
      }) || []
    );
    const deletedItem = await db.$transaction(async (tx) => {
      const page = await tx.page.deleteMany({
        where: { id: { in: pages } },
      });
      await tx.pageMember.deleteMany({
        where: { pageId: { in: pages } },
      });
      await tx.folder.deleteMany({
        where: { workspaceId },
      });
      await tx.file.deleteMany({
        where: { workspaceId },
      });
      await tx.workspaceMember.deleteMany({
        where: { workspaceId },
      });
      await tx.workspace.delete({
        where: {
          id: workspaceId,
        },
      });
      return { page };
    });
    if (!deletedItem) {
      console.error("Error deleting page:", deletedItem);
      const { t } = getTranslations();
      return NextResponse.json(
        { error: t("api.errors.failedToDeletePage") },
        { status: 500 }
      );
    }

    return NextResponse.json(
      {
        message: "Workspace deleted successfully",
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error deleting workspace:", error);
    return NextResponse.json(
      { error: "Failed to delete workspace" },
      { status: 500 }
    );
  }
};

// Update a workspace
const updateWorkspace = async (request: Request) => {
  try {
    const session: any = await getServerSession(authOptions);
    if (!session?.userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    const url = request.url;
    const urlParams = new URLSearchParams(url.split("?")[1]);
    const id = urlParams.get("id") ?? "";

    const { data } = await request.json();
    const updatedWorkspace = await db.workspace.update({
      where: {
        id,
      },
      data,
    });
    return NextResponse.json(
      {
        workspace: updatedWorkspace,
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error updating workspace:", error);
    return NextResponse.json(
      { error: "Failed to update workspace" },
      { status: 500 }
    );
  }
};

// Export the handlers with permission checks
// Note: GET doesn't use withPermission middleware because it implements its own access control
// that includes both direct and group-based workspace access
export const GET = getWorkspaces;
export const POST = withPermission(
  withActiveSubscription(createWorkspace),
  "CREATE",
  "WORKSPACE"
);
export const DELETE = withPermission(deleteWorkspace, "DELETE", "WORKSPACE");
export const PUT = withPermission(updateWorkspace, "UPDATE", "WORKSPACE");
