import { headers } from "next/headers";
import { v4 as uuidv4 } from "uuid";

// Configuration
const DATADOG_API_KEY = process.env.DATADOG_API_KEY || "";
const DATADOG_SITE = process.env.NEXT_PUBLIC_DATADOG_SITE || "datadoghq.com";
const SERVICE_NAME = "apex-ai-web";
const ENV = process.env.NODE_ENV || "development";
const VERSION = process.env.NEXT_PUBLIC_APP_VERSION || "1.0.0";

// Check if Datadog is enabled
const isEnabled =
  process.env.NEXT_PUBLIC_DATADOG_ENABLED === "true" && DATADOG_API_KEY !== "";

/**
 * Send logs to Datadog
 */
async function sendToDatadog(logs: any[]) {
  if (!isEnabled || logs.length === 0) {
    return;
  }

  try {
    // Add common fields to all logs
    const enhancedLogs = logs.map((log) => ({
      ...log,
      ddsource: "nodejs",
      service: SERVICE_NAME,
      env: ENV,
      version: VERSION,
      timestamp: log.timestamp || new Date().getTime(),
    }));

    // Send logs to Datadog HTTP API
    const response = await fetch(
      `https://http-intake.logs.${DATADOG_SITE}/api/v2/logs`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "DD-API-KEY": DATADOG_API_KEY,
        },
        body: JSON.stringify(enhancedLogs),
      }
    );

    if (!response.ok) {
      console.error(
        `Failed to send logs to Datadog: ${response.status} ${response.statusText}`
      );
    }
  } catch (error) {
    console.error("Error sending logs to Datadog:", error);
  }
}

/**
 * Log API request details to Datadog
 */
export async function logApiRequest({
  method,
  url,
  status,
  duration,
  requestBody,
  responseBody,
  error,
}: {
  method: string;
  url: string;
  status: number;
  duration: number;
  requestBody?: any;
  responseBody?: any;
  error?: Error;
}) {
  // Generate a unique request ID
  const requestId = uuidv4();

  // Get headers to extract user and tenant information
  const headersList = headers();
  const userAgent = headersList.get("user-agent") || "";
  const tenantId = headersList.get("x-tenant-id") || "";
  const userId = headersList.get("x-user-id") || "";

  // Create the log entry
  const logEntry = {
    requestId,
    method,
    url,
    status,
    duration_ms: duration,
    tenant_id: tenantId,
    user_id: userId,
    user_agent: userAgent,
    timestamp: new Date().getTime(),
    level: status >= 400 ? "error" : "info",
    message: `${method} ${url} ${status} ${duration}ms`,
  };

  // Add request/response bodies if provided (be careful with sensitive data)
  if (requestBody) {
    // Sanitize request body to remove sensitive information
    const sanitizedRequestBody = sanitizeData(requestBody);
    Object.assign(logEntry, { request_body: sanitizedRequestBody });
  }

  if (responseBody && status < 400) {
    // Sanitize response body to remove sensitive information
    const sanitizedResponseBody = sanitizeData(responseBody);
    Object.assign(logEntry, { response_body: sanitizedResponseBody });
  }

  // Add error details if present
  if (error) {
    Object.assign(logEntry, {
      error_message: error.message,
      error_stack: error.stack,
      error_name: error.name,
    });
  }

  // Send the log to Datadog
  await sendToDatadog([logEntry]);

  return requestId;
}

/**
 * Create a middleware wrapper for API routes to log requests
 */
export function withApiLogger(handler: Function) {
  return async (req: Request, ...args: any[]) => {
    const startTime = performance.now();
    const method = req.method;
    const url = req.url;

    let status = 200;
    let responseBody;
    let requestBody;
    let error;

    try {
      // Try to parse request body if it's JSON
      if (["POST", "PUT", "PATCH"].includes(method)) {
        try {
          const clonedReq = req.clone();
          const contentType = req.headers.get("content-type");
          if (contentType && contentType.includes("application/json")) {
            requestBody = await clonedReq.json();
          }
        } catch (e) {
          // Ignore body parsing errors
        }
      }

      // Execute the handler
      const response = await handler(req, ...args);

      // Get status code from response
      status = response.status;

      // Try to parse response body if it's JSON
      try {
        const clonedRes = response.clone();
        const contentType = response.headers.get("content-type");
        if (contentType && contentType.includes("application/json")) {
          responseBody = await clonedRes.json();
        }
      } catch (e) {
        // Ignore body parsing errors
      }

      return response;
    } catch (e) {
      error = e as Error;
      status = 500;
      throw e;
    } finally {
      const endTime = performance.now();
      const duration = Math.round(endTime - startTime);

      // Log the API request
      await logApiRequest({
        method,
        url,
        status,
        duration,
        requestBody,
        responseBody,
        error,
      });
    }
  };
}

/**
 * Sanitize data to remove sensitive information
 */
function sanitizeData(data: any): any {
  if (!data) return data;

  // If it's not an object, return as is
  if (typeof data !== "object") return data;

  // Clone the data to avoid modifying the original
  const sanitized = Array.isArray(data) ? [...data] : { ...data };

  // List of sensitive fields to redact
  const sensitiveFields = [
    "password",
    "token",
    "secret",
    "key",
    "auth",
    "credential",
    "credit_card",
    "cardNumber",
    "cvv",
    "ssn",
    "social",
    "accessToken",
    "refreshToken",
  ];

  // Redact sensitive fields
  Object.keys(sanitized).forEach((key) => {
    // Check if this is a sensitive field
    const isLowerKey = key.toLowerCase();
    if (sensitiveFields.some((field) => isLowerKey.includes(field))) {
      sanitized[key] = "[REDACTED]";
    }
    // Recursively sanitize nested objects
    else if (typeof sanitized[key] === "object" && sanitized[key] !== null) {
      sanitized[key] = sanitizeData(sanitized[key]);
    }
  });

  return sanitized;
}

/**
 * Log a custom event to Datadog
 */
export async function logEvent(
  message: string,
  level: "info" | "warning" | "error" = "info",
  data?: Record<string, any>
) {
  if (!isEnabled) {
    return;
  }

  const logEntry = {
    message,
    level,
    timestamp: new Date().getTime(),
    ...data,
  };

  await sendToDatadog([logEntry]);
}
