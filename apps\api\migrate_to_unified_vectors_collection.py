#!/usr/bin/env python3
"""
Unified Vectors Collection Migration Script

This script creates a new 'vectors' collection and migrates all vector documents
from existing collections into it with proper sharded DiskANN indexing.

Features:
- Creates unified 'vectors' collection
- Migrates all documents from existing vector collections
- Adds proper tenantId, workspaceId, pageId fields
- Creates sharded DiskANN indexes with vectorIndexShardKey
- Maintains data integrity during migration
"""

import os
import sys
import asyncio
import logging
import json
import dotenv
from typing import Dict, Any, List
from datetime import datetime, timezone
import re

dotenv.load_dotenv()

# Add the src directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from pymongo import MongoClient
from bson import ObjectId

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(f'vectors_migration_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
    ]
)
logger = logging.getLogger(__name__)

class VectorsMigrationManager:
    """Manager for migrating all vector collections to a unified 'vectors' collection."""
    
    def __init__(self):
        self.vector_client = None
        self.vector_db = None
        self.target_collection_name = "vectors"
        
    async def connect_database(self):
        """Connect to the vector database."""
        try:
            vector_connection = os.getenv("VECTOR_DATABASE_URL")
            vector_db_name = os.getenv("VECTOR_DATABASE_NAME")
            
            if not vector_connection or not vector_db_name:
                raise ValueError("VECTOR_DATABASE_URL and VECTOR_DATABASE_NAME must be set")
            
            self.vector_client = MongoClient(vector_connection)
            self.vector_db = self.vector_client[vector_db_name]
            logger.info(f"Connected to vector database: {vector_db_name}")
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to connect to database: {e}")
            return False
    
    def close_connections(self):
        """Close database connections."""
        try:
            if self.vector_client:
                self.vector_client.close()
        except:
            pass
    
    async def discover_vector_collections(self) -> List[Dict[str, Any]]:
        """Discover all collections with vector data (excluding target collection)."""
        try:
            collection_names = await asyncio.to_thread(self.vector_db.list_collection_names)
            logger.info(f"Found {len(collection_names)} collections in database")
            
            vector_collections = []
            
            for collection_name in collection_names:
                # Skip the target collection if it already exists
                if collection_name == self.target_collection_name:
                    logger.info(f"  ⏭️ Skipping target collection: {collection_name}")
                    continue
                
                logger.info(f"\n🔍 Analyzing collection: {collection_name}")
                collection = self.vector_db[collection_name]
                
                # Get basic collection stats
                doc_count = await asyncio.to_thread(collection.count_documents, {})
                
                if doc_count == 0:
                    logger.info(f"  ⏭️ Skipping empty collection: {collection_name}")
                    continue
                
                # Sample documents to check for vector data
                sample_docs = list(collection.find().limit(5))
                has_vectors = False
                vector_dimensions = None
                
                for doc in sample_docs:
                    embedding = doc.get("embedding")
                    if embedding and isinstance(embedding, list) and len(embedding) > 0:
                        has_vectors = True
                        vector_dimensions = len(embedding)
                        break
                
                if not has_vectors:
                    logger.info(f"  ⏭️ Skipping collection without vector data: {collection_name}")
                    continue
                
                # Extract tenant info from collection name or documents
                workspace_id = collection_name.split("_")[-1]
                tenant_id = self._extract_tenant_id(workspace_id)
                
                collection_info = {
                    "name": collection_name,
                    "doc_count": doc_count,
                    "vector_dimensions": vector_dimensions,
                    "tenant_id": tenant_id,
                    "workspace_id": workspace_id
                }
                
                # Analyze document structure
                sample_structure = self._analyze_document_structure(sample_docs)

                logger.info(f"  📊 Collection stats:")
                logger.info(f"    - Documents: {doc_count}")
                logger.info(f"    - Vector dimensions: {vector_dimensions}D")
                logger.info(f"    - Extracted tenantId: {tenant_id}")
                logger.info(f"    - Extracted workspaceId: {workspace_id}")
                logger.info(f"    - Document structure: {sample_structure}")
                
                vector_collections.append(collection_info)
            
            logger.info(f"\n✅ Found {len(vector_collections)} collections with vector data to migrate")
            return vector_collections
            
        except Exception as e:
            logger.error(f"Failed to discover vector collections: {e}")
            return []
    
    def _extract_tenant_id(self, workspace_id: str) -> str:
        """Extract or generate tenant ID from collection name or documents."""
        workspaceTenat=[
  { '67d9855198e2fd22d93e56e4': '67cfc0e7b792fd28f77b750a'},
  { '67cfd4ffdec013cc8dc42202': '67cfc0e7b792fd28f77b750a'},
  { '67dbb5034569bae19caf9cac': '67dbb4834569bae19caf9caa'},
  { '67dbb56f4569bae19caf9caf': '67dbb4834569bae19caf9caa'},
  { '684704809ea0185e9b805e59': '6806140b1f953f78e2641a30'},
  { '68764d4b5f4120fc3d0d18a4': '67ca81b2343cdb510ed600b8'},
  { '684a99f87960f756950de6fe': '67ca81b2343cdb510ed600b8'},
  { '6877886d86385a258cf365a3': '6877875186385a258cf3659a'},
  { '684ab0cfa322ef6d5bb63a78': '67ca81b2343cdb510ed600b8'},
  { '6807397654d3146ddc243f4a': '67ca81b2343cdb510ed600b8'},
  { '684a98f67960f756950de6f8': '67ca81b2343cdb510ed600b8'},
  { '684aafc87960f756950de74b': '67ca81b2343cdb510ed600b8'},
  { '6800a515f478e34e176cfa2f': '67f799f6bf3bfd43a01f9e71'},
  { '6801114c2baf9adcc3d00971': '67f799f6bf3bfd43a01f9e71'},
  { '680889d4b518f1677ce1583e': '67cfbfd5b792fd28f77b7503'},
  { '68088a00b518f1677ce15841': '67cfbfd5b792fd28f77b7503'},
  { '67cac89d6a7f74bc794c44e9': '67ca81b2343cdb510ed600b8'},
  { '67cad98391776d0ee34c2e4f': '67ca81b2343cdb510ed600b8'},
  { '6810f2ffb8f9d9d5e41575db': '6806140b1f953f78e2641a30'},
  { '67fca4920e2d5a2e87707260': '67ca81b2343cdb510ed600b8'},
  { '67d014c5b9321de7c54db7d5': '67ca81b2343cdb510ed600b8'},
  { '6822f37b2bd5db666ff82e05': '6822efd42bd5db666ff82e01'},
  { '681f3e3f5c6e91c99d1e7640': '67f799f6bf3bfd43a01f9e71'},
  { '681f39625c6e91c99d1e7636': '67f799f6bf3bfd43a01f9e71'},
  { '682de4b6f0f177af66984ce7': '67dbb76a4569bae19caf9cb5'},
  { '68300d2620bdc4cabab29402': '67ca81b2343cdb510ed600b8'},
  { '67ffa0e518c1245eadd12e8a': '67f799f6bf3bfd43a01f9e71'},
  { '681da7aea3cb0ab906a664ae': '67f799f6bf3bfd43a01f9e71'},
  { '683451b2a790b7a4a6c2fedb': '67f799f6bf3bfd43a01f9e71'},
  { '683455f1a790b7a4a6c2feee': '67f799f6bf3bfd43a01f9e71'},
  { '6875e35a2a50ffb2cc429ecf': '67ca81b2343cdb510ed600b8'},
  { '6877884b86385a258cf3659d': '6877875186385a258cf3659a'},
  { '684144a89b24432fe83e5058': '684142659b24432fe83e5051'},
  { '684144219b24432fe83e5054': '684142659b24432fe83e5051'},
  { '684183fd9b24432fe83e50b0': '684142659b24432fe83e5051'},
  { '68495da63bac9295394782f3': '67f799f6bf3bfd43a01f9e71'},
  { '684a998b7960f756950de6fb': '67ca81b2343cdb510ed600b8'},
  { '6877885b86385a258cf365a0': '6877875186385a258cf3659a'},
  { '6877888086385a258cf365a6': '6877875186385a258cf3659a'},
  { '684ab0647960f756950de74e': '67ca81b2343cdb510ed600b8'},
  { '684bb633fabdaaaf9c74752d': '67ca81b2343cdb510ed600b8'},
  { '684bd31c60efe680e433e96e': '67ca81b2343cdb510ed600b8'},
  { '6877a09a9fa60a8b7a0e02df': '67e4383ca08402fb743e23f0'},
  { '67dbb8f04569bae19caf9cb7': '67dbb76a4569bae19caf9cb5'},
  { '68515a901508d65c1298120a': '67dbb76a4569bae19caf9cb5'},
  { '67dbb9064569bae19caf9cba': '67dbb76a4569bae19caf9cb5'},
  { '68823c2433d366b9dc57e8c9': '68823ae333d366b9dc57e8c7'},
  { '68835931713195a2b04303f9': '67ca81b2343cdb510ed600b8'},
  { '6875e30d2a50ffb2cc429ecc': '67ca81b2343cdb510ed600b8'},
  { '6847d7c95702d7f8bd45ac99': '67ca81b2343cdb510ed600b8'},
  { '687632b81400a3d4c82102f8': '67ca81b2343cdb510ed600b8'}
]

        return  next((d[workspace_id] for d in workspaceTenat if workspace_id in d), None)

    
    def _extract_workspace_id(self, collection_name: str, sample_docs: List[Dict]) -> str:
        """Extract or generate workspace ID from collection name or documents."""
        # Check if documents already have workspaceId or workspace_id
        for doc in sample_docs:
            if doc.get("workspaceId"):
                return doc["workspaceId"]
            if doc.get("workspace_id"):
                return str(doc["workspace_id"])  # Convert ObjectId to string if needed

        # Extract from collection name patterns
        if collection_name.startswith("workspace_"):
            return collection_name.replace("workspace_", "")

        # Use collection name as workspace ID
        return collection_name
    
    def _generate_page_id(self, file_id: str) -> str:
        """Generate page ID for document."""
        filePage=[
  { '67dba7ebcd30b2c87c8613a7': '67d014c6b9321de7c54db7d7'},
  { '67dba877cd30b2c87c8613a8': '67d014c6b9321de7c54db7d7'},
  { '683b1483fdb8ba5a11c18040': '683455f2a790b7a4a6c2fef2'},
  { '67dba7ebcd30b2c87c8613a6': '67d014c6b9321de7c54db7d7'},
  { '67cd7c38e7b7819dadea5821': '67cae9a411a629df39ff8777'},
  { '67cfcdfb6bb69d87c7117a26': '67cae9a411a629df39ff8777'},
  { '67d0041545614d444a41572f': '67cfd500dec013cc8dc42204'},
  { '67d1223735727388a35f096a': '67cac89d6a7f74bc794c44eb'},
  { '67d698250cb08bd91517d814': '67cac89d6a7f74bc794c44eb'},
  { '67d985a798e2fd22d93e56e8': '67cfd500dec013cc8dc42204'},
  { '67da75c1e60024e843caf0ed': '67cac89d6a7f74bc794c44eb'},
  { '67e18c0226344f55051ba8bf': '67d014c6b9321de7c54db7d7'},
  { '67f405d4eb40124009088e2d': '67d014c6b9321de7c54db7d7'},
  { '67f40358eb40124009088e2a': '67d014c6b9321de7c54db7d7'},
  { '67f403d3eb40124009088e2b': '67d014c6b9321de7c54db7d7'},
  { '681f55ba485ffeb749353361': '67dbb8f04569bae19caf9cb9'},
  { '68304b5333e6b5228971680c': '67cac89d6a7f74bc794c44eb'},
  { '68304b7333e6b5228971680d': '67cac89d6a7f74bc794c44eb'},
  { '686249c78df9d00486bbd7cd': '684aafc87960f756950de74d'},
  { '686249c88df9d00486bbd7ce': '684aafc87960f756950de74d'},
  { '683456c8a790b7a4a6c2ff06': '68345602a790b7a4a6c2fef3'},
  { '686249e18df9d00486bbd7cf': '684aafc87960f756950de74d'},
  { '67f406a8eb40124009088e2f': '67d014c6b9321de7c54db7d7'},
  { '687763f586385a258cf3657e': '684183fd9b24432fe83e50b3'},
  { '686249e28df9d00486bbd7d1': '684aafc87960f756950de74d'},
  { '684bbe3a3b3d65bb6260a073': '684bb634fabdaaaf9c74752f'},
  { '685cfc564a084ed3aac6ed78': '68515a911508d65c1298120c'},
  { '680f346c069ba79998b7800c': '6807397754d3146ddc243f4c'},
  { '67f75ab783d320158813ca61': '67cad98391776d0ee34c2e51'},
  { '67ffa21ff478e34e176cfa05': '67ffa1e4f478e34e176cfa01'},
  { '68009fa7f478e34e176cfa24': '67ffa0e618c1245eadd12e8c'},
  { '6800a9e3f478e34e176cfa43': '6800a568f478e34e176cfa32'},
  { '680111b22baf9adcc3d00976': '6801114c2baf9adcc3d00974'},
  { '6807393d54d3146ddc243f48': '680738f354d3146ddc243f45'},
  { '6808913ab518f1677ce15849': '68088cc5b518f1677ce15844'},
  { '6808913ab518f1677ce1584a': '68088cc5b518f1677ce15844'},
  { '68089184b518f1677ce1584c': '68088cc5b518f1677ce15844'},
  { '67f75abc83d320158813ca62': '67cad98391776d0ee34c2e51'},
  { '686249c78df9d00486bbd7cc': '684aafc87960f756950de74d'},
  { '681f55c1485ffeb74935336a': '67dbb8f04569bae19caf9cb9'},
  { '682f0f5e4d38f68531424c4d': '67cac89d6a7f74bc794c44eb'},
  { '68345749a790b7a4a6c2ff11': '68345602a790b7a4a6c2fef3'},
  { '686249e18df9d00486bbd7d0': '684aafc87960f756950de74d'},
  { '680f4e870e033cddf30351bf': '67fca4930e2d5a2e87707262'},
  { '6853b72b5fb20503deb560d9': '6853b6185fb20503deb560d3'},
  { '67dbc01b4569bae19caf9cbf': '67dbb9064569bae19caf9cbc'},
  { '681f3f915c6e91c99d1e7649': '67dbb9064569bae19caf9cbc'},
  { '6811b410ead69a1e4cf8f6f9': '6807397754d3146ddc243f4c'},
  { '68760354b0e8f969482dc009': '6875e35b2a50ffb2cc429ed1'},
  { '68306531d1e67f1cb9c45f9e': '683064fed1e67f1cb9c45f9c'},
  { '68219b06485ffeb749353387': '68219aa6485ffeb749353376'},
  { '681f55cb485ffeb749353371': '67dbb8f04569bae19caf9cb9'},
  { '681f55c3485ffeb74935336d': '67dbb8f04569bae19caf9cb9'},
  { '68219b06485ffeb749353388': '68219aa6485ffeb749353376'},
  { '68219b07485ffeb749353389': '68219aa6485ffeb749353376'},
  { '684bd35660efe680e433e973': '684bd31c60efe680e433e970'},
  { '68219b08485ffeb74935338a': '68219aa6485ffeb749353376'},
  { '68219b09485ffeb74935338b': '68219aa6485ffeb749353376'},
  { '687763f586385a258cf36580': '684183fd9b24432fe83e50b3'},
  { '68219b0f485ffeb74935338d': '68219aa6485ffeb749353376'},
  { '681f55c3485ffeb74935336e': '67dbb8f04569bae19caf9cb9'},
  { '681f55c6485ffeb74935336f': '67dbb8f04569bae19caf9cb9'},
  { '683458b2a790b7a4a6c2ff1a': '68345602a790b7a4a6c2fef3'},
  { '680f3739069ba79998b78012': '6807397754d3146ddc243f4c'},
  { '681f3f935c6e91c99d1e764a': '67dbb9064569bae19caf9cbc'},
  { '6821c038ef157f14f2b583ab': '6821b2dfef157f14f2b5839f'},
  { '68306533d1e67f1cb9c45f9f': '683064fed1e67f1cb9c45f9c'},
  { '68514bb71508d65c129811fe': '68514a4a1508d65c129811fb'},
  { '68760358b0e8f969482dc00a': '6875e35b2a50ffb2cc429ed1'},
  { '681f55b9485ffeb74935335f': '67dbb8f04569bae19caf9cb9'},
  { '681f19645c6e91c99d1e762b': '67dbb8f04569bae19caf9cb9'},
  { '681f196c5c6e91c99d1e762c': '67dbb8f04569bae19caf9cb9'},
  { '681f19e55c6e91c99d1e7630': '67dbb8f04569bae19caf9cb9'},
  { '681f55be485ffeb749353366': '67dbb8f04569bae19caf9cb9'},
  { '68219b10485ffeb74935338e': '68219aa6485ffeb749353376'},
  { '6834526da790b7a4a6c2fee6': '683451b2a790b7a4a6c2fedf'},
  { '6821dbba18aaeb4e85a8464c': '67cad98391776d0ee34c2e51'},
  { '6821b618ef157f14f2b583a7': '6821b2dfef157f14f2b5839f'},
  { '6821dd3e18aaeb4e85a84656': '6821dba318aaeb4e85a8464b'},
  { '6821dd4418aaeb4e85a84657': '6821dba318aaeb4e85a8464b'},
  { '6821e8a17fcc11a678677536': '6810fc2cb8f9d9d5e41575e2'},
  { '6821e8ab7fcc11a678677537': '6810fc2cb8f9d9d5e41575e2'},
  { '681f55bc485ffeb749353364': '67dbb8f04569bae19caf9cb9'},
  { '681f55bd485ffeb749353365': '67dbb8f04569bae19caf9cb9'},
  { '6821b2bbef157f14f2b5839e': '681f3e485c6e91c99d1e7647'},
  { '681f55b9485ffeb749353360': '67dbb8f04569bae19caf9cb9'},
  { '67dbbcac4569bae19caf9cbd': '67dbb8f04569bae19caf9cb9'},
  { '681f55c1485ffeb74935336b': '67dbb8f04569bae19caf9cb9'},
  { '681f55c2485ffeb74935336c': '67dbb8f04569bae19caf9cb9'},
  { '681f55c0485ffeb749353369': '67dbb8f04569bae19caf9cb9'},
  { '681f55ca485ffeb749353370': '67dbb8f04569bae19caf9cb9'},
  { '6876035cb0e8f969482dc00b': '6875e35b2a50ffb2cc429ed1'},
  { '681f19e45c6e91c99d1e762e': '67dbb8f04569bae19caf9cb9'},
  { '681f19e45c6e91c99d1e762f': '67dbb8f04569bae19caf9cb9'},
  { '67f795292b9c17f9aa810b44': '67dbb8f04569bae19caf9cb9'},
  { '681f2cf65c6e91c99d1e7635': '67dbb8f04569bae19caf9cb9'},
  { '6875f0e68851d75265ca725b': '6875e35b2a50ffb2cc429ed1'},
  { '6830652ed1e67f1cb9c45f9d': '683064fed1e67f1cb9c45f9c'},
  { '681f4b71ca8120641e1e219c': '681f3e485c6e91c99d1e7647'},
  { '681f4b7fca8120641e1e219d': '681f3e485c6e91c99d1e7647'}
]
  
        return next((d[file_id] for d in filePage if file_id in d), None)

    def _analyze_document_structure(self, sample_docs: List[Dict]) -> str:
        """Analyze the structure of sample documents."""
        if not sample_docs:
            return "No documents to analyze"

        # Get common fields across documents
        all_fields = set()
        for doc in sample_docs:
            all_fields.update(doc.keys())

        # Categorize fields
        key_fields = []
        if "workspace_id" in all_fields:
            key_fields.append("workspace_id")
        if "fileId" in all_fields:
            key_fields.append("fileId")
        if "page" in all_fields:
            key_fields.append("page")
        if "slug" in all_fields:
            key_fields.append("slug")

        return f"Fields found: {len(all_fields)}, Key fields: {', '.join(key_fields)}"

    async def create_target_collection(self) -> bool:
        """Create the target 'vectors' collection if it doesn't exist."""
        try:
            collection_names = await asyncio.to_thread(self.vector_db.list_collection_names)

            if self.target_collection_name in collection_names:
                logger.warning(f"⚠️ Target collection '{self.target_collection_name}' already exists")

                # Check if it has documents
                target_collection = self.vector_db[self.target_collection_name]
                doc_count = await asyncio.to_thread(target_collection.count_documents, {})

                if doc_count > 0:
                    logger.error(f"❌ Target collection already has {doc_count} documents")
                    logger.error("Please backup and drop the existing collection or choose a different name")
                    return False
                else:
                    logger.info(f"✅ Target collection exists but is empty, proceeding...")
            else:
                logger.info(f"📁 Creating target collection: {self.target_collection_name}")
                # Collection will be created automatically when we insert documents

            return True

        except Exception as e:
            logger.error(f"Failed to create target collection: {e}")
            return False

    async def migrate_collection_documents(self, source_collection_info: Dict[str, Any]) -> int:
        """Migrate documents from source collection to target collection."""
        try:
            source_name = source_collection_info["name"]
            tenant_id = source_collection_info["tenant_id"]
            workspace_id = source_collection_info["workspace_id"]

            source_collection = self.vector_db[source_name]
            target_collection = self.vector_db[self.target_collection_name]

            logger.info(f"  📄 Migrating documents from {source_name}...")

            # Get all documents from source collection
            documents = list(source_collection.find())
            migrated_count = 0

            # Process documents in batches
            batch_size = 100
            for i in range(0, len(documents), batch_size):
                batch = documents[i:i + batch_size]
                processed_batch = []

                for doc_index, doc in enumerate(batch):
                    try:
                        # Create new document with proper fields
                        new_doc = {
                            "_id": doc.get("_id", ObjectId()),
                            **doc,
                            "source_collection": source_name,  # Track original source
                            "migrated_at": datetime.utcnow().isoformat()
                        }

                        # Add/override required fields for unified collection
                        new_doc["tenantId"] = doc.get("tenantId") or tenant_id
                        new_doc["workspaceId"] = doc.get("workspaceId") or str(doc.get("workspace_id", workspace_id))
                        new_doc["pageId"] =  self._generate_page_id(doc.get("fileId", "")) or " "
                        new_doc["fileId"] = doc.get("fileId") or str(ObjectId())
                        new_doc["metadata"] = {
                            "file_name": doc.get("file_name", ""),
                            "source": doc.get("source", ""),
                            "title": doc.get("title", "Unknown"),
                            "file_type": doc.get("file_type", "Unknown"),
                            "page_number": doc.get("page_number", 1),
                            "page": doc.get("page", 1),
                            "slug": doc.get("slug", "Unknown"),
                            "fileId": doc.get("fileId", ""),
                            "pageId":self._generate_page_id(doc.get("fileId", "")) or " ",
                            "workspace_id": doc.get("workspace_id", ""),
                            "page_label": doc.get("page_label", ""),
                            "total_pages": doc.get("total_pages", 1)
                        }

                        # Ensure required fields exist
                        if "content" not in new_doc:
                            new_doc["content"] = ""
                        if "embedding" not in new_doc:
                            new_doc["embedding"] = []

                        processed_batch.append(new_doc)

                    except Exception as doc_error:
                        logger.warning(f"    Failed to process document {doc.get('_id', 'unknown')}: {doc_error}")
                        continue

                # Insert batch if we have documents to insert
                if processed_batch:
                    try:
                        await asyncio.to_thread(target_collection.insert_many, processed_batch)
                        migrated_count += len(processed_batch)
                        logger.info(f"    Migrated {migrated_count}/{len(documents)} documents...")
                    except Exception as batch_error:
                        logger.error(f"    Failed to insert batch: {batch_error}")
                        # Try inserting documents one by one
                        for single_doc in processed_batch:
                            try:
                                await asyncio.to_thread(target_collection.insert_one, single_doc)
                                migrated_count += 1
                            except Exception as single_error:
                                logger.warning(f"    Failed to insert document {single_doc.get('_id', 'unknown')}: {single_error}")
                        logger.info(f"    Migrated {migrated_count}/{len(documents)} documents (with some failures)...")
                else:
                    logger.warning(f"    No valid documents in batch {i//batch_size + 1}")

            logger.info(f"  ✅ Successfully migrated {migrated_count} documents from {source_name}")
            return migrated_count

        except Exception as e:
            logger.error(f"  ❌ Failed to migrate documents from {source_collection_info['name']}: {e}")
            return 0

    async def create_sharded_diskann_indexes(self) -> bool:
        """Create sharded DiskANN and filter indexes on the target collection."""
        try:
            target_collection = self.vector_db[self.target_collection_name]

            logger.info(f"\n🔨 Creating sharded DiskANN indexes on '{self.target_collection_name}'...")

            # Get collection stats for optimal parameters
            doc_count = await asyncio.to_thread(target_collection.count_documents, {})

            # Sample document to get vector dimensions
            sample_doc = target_collection.find_one({"embedding": {"$exists": True}})
            if not sample_doc or not sample_doc.get("embedding"):
                logger.error("❌ No documents with embeddings found in target collection")
                return False

            vector_dimensions = len(sample_doc["embedding"])

            # Calculate optimal DiskANN parameters
            max_degree = min(128, max(32, vector_dimensions // 24))
            l_build = min(300, max(50, int(doc_count ** 0.3)))

            logger.info(f"  📊 Collection stats: {doc_count} documents, {vector_dimensions}D vectors")
            logger.info(f"  🔧 DiskANN parameters: maxDegree={max_degree}, lBuild={l_build}")

            # Create sharded DiskANN vector index
            logger.info("  🔑 Creating sharded DiskANN vector index...")
            cosmos_search_options = {
                "kind": "vector-diskann",
                "similarity": "COS",
                "dimensions": vector_dimensions,
                "maxDegree": max_degree,
                "lBuild": l_build,
                "vectorIndexShardKey": ["/tenantId"]  # Shard by tenantId
            }

            await asyncio.to_thread(
                target_collection.create_index,
                [("embedding", "cosmosSearch")],
                name="sharded_diskann_vector_index",
                cosmosSearchOptions=cosmos_search_options
            )
            logger.info("  ✅ Sharded DiskANN vector index created")

            # Create filter indexes
            logger.info("  🔍 Creating filter indexes...")

            # TenantId index
            await asyncio.to_thread(
                target_collection.create_index,
                [("tenantId", 1)],
                name="tenantId_filter_index"
            )

            # WorkspaceId index
            await asyncio.to_thread(
                target_collection.create_index,
                [("workspaceId", 1)],
                name="workspaceId_filter_index"
            )

            # PageId index
            await asyncio.to_thread(
                target_collection.create_index,
                [("pageId", 1)],
                name="pageId_filter_index"
            )

            # Compound indexes
            await asyncio.to_thread(
                target_collection.create_index,
                [("tenantId", 1), ("workspaceId", 1)],
                name="tenant_workspace_filter_index"
            )

            await asyncio.to_thread(
                target_collection.create_index,
                [("tenantId", 1), ("workspaceId", 1), ("pageId", 1)],
                name="tenant_workspace_page_filter_index"
            )

            logger.info("  ✅ All filter indexes created")

            return True

        except Exception as e:
            error_msg = str(e)
            if "IndexKeySpecsConflict" in error_msg or "existing index" in error_msg:
                logger.warning("  ⚠️ Some indexes already exist, skipping...")
                return True
            else:
                logger.error(f"  ❌ Failed to create indexes: {e}")
                return False

async def main():
    """Main function to migrate all vector collections to unified 'vectors' collection."""
    
    logger.info("🚀 Starting Vector Collections Migration to Unified 'vectors' Collection")
    logger.info("=" * 90)
    
    # Check environment variables
    required_vars = ["VECTOR_DATABASE_URL", "VECTOR_DATABASE_NAME"]
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    
    if missing_vars:
        logger.error(f"❌ Missing required environment variables: {missing_vars}")
        logger.info("Please set the following environment variables:")
        for var in missing_vars:
            logger.info(f"  {var}=your_value_here")
        return False
    
    # Initialize manager
    manager = VectorsMigrationManager()
    
    try:
        # Connect to database
        if not await manager.connect_database():
            logger.error("❌ Failed to connect to database")
            return False
        
        # Discover all vector collections
        logger.info("\n🔍 Discovering collections with vector data...")
        vector_collections = await manager.discover_vector_collections()
        
        if not vector_collections:
            logger.error("❌ No vector collections found to migrate")
            return False
        
        # Create target collection
        logger.info(f"\n📁 Preparing target collection '{manager.target_collection_name}'...")
        if not await manager.create_target_collection():
            logger.error("❌ Failed to prepare target collection")
            return False

        # Migrate all collections
        total_migrated = 0
        successful_migrations = 0


        for collection_info in vector_collections:
            collection_name = collection_info["name"]

            logger.info(f"\n{'='*60}")
            logger.info(f"📦 Migrating collection: {collection_name}")
            logger.info(f"{'='*60}")

            migrated_count = await manager.migrate_collection_documents(collection_info)

            if migrated_count > 0:
                total_migrated += migrated_count
                successful_migrations += 1
                logger.info(f"✅ Successfully migrated {migrated_count} documents from {collection_name}")
            else:
                logger.error(f"❌ Failed to migrate documents from {collection_name}")

        # Create indexes on the unified collection
        if total_migrated > 0:
            logger.info(f"\n� Creating sharded DiskANN indexes on unified collection...")
            if await manager.create_sharded_diskann_indexes():
                logger.info("✅ Successfully created all indexes")
            else:
                logger.error("❌ Failed to create some indexes")

        # Final summary
        logger.info(f"\n{'='*90}")
        logger.info("📈 MIGRATION SUMMARY")
        logger.info(f"{'='*90}")
        logger.info(f"Source collections found: {len(vector_collections)}")
        logger.info(f"Successful migrations: {successful_migrations}")
        logger.info(f"Total documents migrated: {total_migrated}")
        logger.info(f"Target collection: {manager.target_collection_name}")

        if successful_migrations == len(vector_collections) and total_migrated > 0:
            logger.info("\n🎉 SUCCESS! All collections migrated successfully!")
            logger.info("\nYour unified 'vectors' collection now provides:")
            logger.info("  🚀 Single collection for all vector data")
            logger.info("  🔑 Sharded DiskANN with tenantId-based partitioning")
            logger.info("  💰 Reduced RU costs for tenant-specific queries")
            logger.info("  🎯 Improved recall and accuracy per tenant")
            logger.info("  📁 Efficient workspace and page filtering")
            logger.info("  ⚡ Optimal performance for multi-tenant vector search")
            logger.info("\nNext steps:")
            logger.info("  1. Test vector search queries on the new 'vectors' collection")
            logger.info("  2. Update your application to use the unified collection")
            logger.info("  3. Backup and remove old collections once verified")
            return True
        elif total_migrated > 0:
            logger.warning(f"⚠️ Partial success: {successful_migrations}/{len(vector_collections)} collections migrated")
            logger.warning(f"Total {total_migrated} documents migrated to '{manager.target_collection_name}'")
            return True
        else:
            logger.error("❌ No documents were migrated successfully")
            return False
        
    except Exception as e:
        logger.error(f"❌ Migration failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        manager.close_connections()

if __name__ == "__main__":
    success = asyncio.run(main())
    
    if success:
        logger.info("=" * 90)
        logger.info("🎉 Vector collections migration completed successfully!")
        sys.exit(0)
    else:
        logger.error("=" * 90)
        logger.error("💥 Vector collections migration failed!")
        sys.exit(1)
