import { NextResponse } from "next/server";
import <PERSON><PERSON> from "stripe";
import db from "@/lib/shared-db";

// Initialize Stripe with the secret key
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY as string, {
  apiVersion: "2023-10-16" as any,
});

// This is your Stripe webhook secret for testing your endpoint locally
const endpointSecret = process.env.STRIPE_WEBHOOK_SECRET;

// Helper function to find a product by name
async function findProductByName(name: string): Promise<Stripe.Product | null> {
  try {
    const products = await stripe.products.list({
      active: true,
      limit: 100,
    });

    const product = products.data.find((p) => p.name === name);
    return product || null;
  } catch (error) {
    console.error(`Error finding product by name ${name}:`, error);
    return null;
  }
}

// Helper function to find a price for a product
async function findPriceForProduct(
  productId: string
): Promise<Stripe.Price | null> {
  try {
    const prices = await stripe.prices.list({
      product: productId,
      active: true,
      limit: 1,
    });

    return prices.data.length > 0 ? prices.data[0] : null;
  } catch (error) {
    console.error(`Error finding price for product ${productId}:`, error);
    return null;
  }
}

export async function POST(request: Request) {
  console.log(`Stripe webhook received`);
  const payload = await request.text();
  const signature = request.headers.get("stripe-signature") as string;

  let event: Stripe.Event;

  try {
    console.log(
      `Constructing Stripe event with signature: ${signature?.substring(
        0,
        10
      )}...`
    );
    event = stripe.webhooks.constructEvent(
      payload,
      signature,
      endpointSecret as string
    );
    console.log(`Stripe event constructed successfully: ${event.type}`);
  } catch (err: any) {
    console.error(`Webhook signature verification failed: ${err.message}`);
    return NextResponse.json(
      { error: `Webhook signature verification failed` },
      { status: 400 }
    );
  }

  // Handle the event
  console.log(`Processing Stripe event: ${event.type}`);

  // Declare variables outside the switch statement
  let checkoutSession: Stripe.Checkout.Session;
  let subscription: Stripe.Subscription;
  let deletedSubscription: Stripe.Subscription;

  switch (event.type) {
    case "checkout.session.completed":
      console.log(`Handling checkout.session.completed event`);
      checkoutSession = event.data.object as Stripe.Checkout.Session;
      await handleCheckoutSessionCompleted(checkoutSession);
      break;

    case "customer.subscription.updated":
      console.log(`Handling customer.subscription.updated event`);
      subscription = event.data.object as Stripe.Subscription;
      await handleSubscriptionUpdated(subscription);
      break;

    case "customer.subscription.deleted":
      console.log(`Handling customer.subscription.deleted event`);
      deletedSubscription = event.data.object as Stripe.Subscription;
      await handleSubscriptionDeleted(deletedSubscription);
      break;

    case "price.created":
      console.log(`Handling price.created event`);
      const newPrice = event.data.object as Stripe.Price;
      await handlePriceCreated(newPrice);
      break;

    case "price.updated":
      console.log(`Handling price.updated event`);
      const updatedPrice = event.data.object as Stripe.Price;
      await handlePriceUpdated(updatedPrice);
      break;

    default:
      console.log(`Unhandled event type: ${event.type}`);
  }

  return NextResponse.json({ received: true });
}

async function handleCheckoutSessionCompleted(
  session: Stripe.Checkout.Session
) {
  try {
    // Extract metadata from the session
    console.log(`Checkout session metadata:`, session.metadata);
    const {
      tenantId,
      planId,
      additionalUsers,
      additionalStorageGB,
      userId,
      isAddonUpdate,
      currentSubscriptionId,
    } = session.metadata || {};

    console.log(
      `Extracted values: tenantId=${tenantId}, planId=${planId}, additionalUsers=${additionalUsers},  userId=${userId}, isAddonUpdate=${isAddonUpdate}, currentSubscriptionId=${currentSubscriptionId}`
    );

    if (!tenantId || !planId) {
      console.error("Missing required metadata in checkout session");
      return;
    }

    // Get the Stripe subscription
    const subscriptionId = session.subscription as string;
    const stripeSubscription: Stripe.Subscription =
      await stripe.subscriptions.retrieve(subscriptionId);

    // Get additional information about the subscription
    const isTrialSubscription = stripeSubscription.status === "trialing";
    const trialEnd = stripeSubscription.trial_end
      ? new Date(stripeSubscription.trial_end * 1000)
      : null;

    // Check if this is a unified subscription
    const isUnifiedSubscription =
      stripeSubscription.metadata?.unified === "true" ||
      session.metadata?.unified === "true";

    console.log(
      `Subscription status: ${stripeSubscription.status}, Trial: ${isTrialSubscription}, Trial end: ${trialEnd}, Unified: ${isUnifiedSubscription}`
    );

    // Create or update the subscription in our database
    const existingSubscription = await db.subscription.findFirst({
      where: {
        tenantId,
        isActive: true,
      },
    });

    if (existingSubscription) {
      // Update existing subscription
      console.log(`Updating existing subscription ${existingSubscription.id}`);
      console.log(
        `additionalUsers=${parseInt(
          additionalUsers || "0"
        )}, isAddonUpdate=${isAddonUpdate}`
      );

      try {
        // If this is an add-on update, we need to handle it differently
        if (isAddonUpdate === "true") {
          console.log(
            `This is an add-on update for subscription ${currentSubscriptionId}`
          );

          // For add-on updates, we don't create a new subscription in Stripe
          // Instead, we update the existing subscription with the new add-ons

          // First, get the current subscription from Stripe
          const currentStripeSubscription = await stripe.subscriptions.retrieve(
            currentSubscriptionId as string
          );

          // Get the subscription items
          const subscriptionItems = currentStripeSubscription.items.data;

          // Create an array to store the updated items
          const updatedItems: any[] = [];

          // Keep the main subscription item (the plan)
          const mainItem = subscriptionItems[0];
          updatedItems.push({
            id: mainItem.id,
            price: mainItem.price.id,
            quantity: 1,
          });

          // Remove any existing add-on items
          for (let i = 1; i < subscriptionItems.length; i++) {
            updatedItems.push({
              id: subscriptionItems[i].id,
              deleted: true,
            });
          }

          // Add the new add-ons
          // First, find the additional users product
          if (parseInt(additionalUsers || "0") > 0) {
            // Find or create the additional users product
            let additionalUsersProduct =
              await findProductByName("Additional Users");
            if (!additionalUsersProduct) {
              additionalUsersProduct = await stripe.products.create({
                name: "Additional Users",
                description: "Additional users beyond the base plan limit",
                active: true,
              });
            }

            // Find or create the price for the additional users product
            let additionalUsersPrice = await findPriceForProduct(
              additionalUsersProduct.id
            );
            if (!additionalUsersPrice) {
              // Get the plan to determine the price
              const plan = await db.plan.findUnique({
                where: { id: planId },
              });

              if (!plan) {
                throw new Error(`Plan not found: ${planId}`);
              }

              additionalUsersPrice = await stripe.prices.create({
                product: additionalUsersProduct.id,
                currency: "chf",
                unit_amount: Math.round(plan.additionalUserFee * 100),
                recurring: { interval: "month" },
                active: true,
              });
            }

            // Add the additional users item
            updatedItems.push({
              price: additionalUsersPrice.id,
              quantity: parseInt(additionalUsers || "0"),
            });
          }

          // Handle additional storage
          if (parseInt(additionalStorageGB || "0") > 0) {
            // Determine the storage tier
            const storageGB = parseInt(additionalStorageGB || "0");
            let tierName = "";
            let storagePriceAmount = 0;

            if (storageGB <= 10) {
              tierName = "10GB";
              storagePriceAmount = 29;
            } else if (storageGB <= 50) {
              tierName = "50GB";
              storagePriceAmount = 79;
            } else {
              tierName = "100GB";
              storagePriceAmount = 149;
            }

            // Find or create the storage product
            let storageProduct = await findProductByName(
              `Additional Vector Storage (${tierName})`
            );
            if (!storageProduct) {
              storageProduct = await stripe.products.create({
                name: `Additional Vector Storage (${tierName})`,
                description: `Additional vector storage (${tierName})`,
                active: true,
              });
            }

            // Find or create the price for the storage product
            let storagePriceObj = await findPriceForProduct(storageProduct.id);
            if (!storagePriceObj) {
              storagePriceObj = await stripe.prices.create({
                product: storageProduct.id,
                currency: "chf",
                unit_amount: Math.round(storagePriceAmount * 100),
                recurring: { interval: "month" },
                active: true,
              });
            }

            // Add the storage item
            updatedItems.push({
              price: storagePriceObj.id,
              quantity: 1,
            });
          }

          // Update the subscription in Stripe
          await stripe.subscriptions.update(currentSubscriptionId as string, {
            items: updatedItems,
            metadata: {
              additionalUsers: additionalUsers || "0",
              additionalStorageGB: additionalStorageGB || "0",
            },
            proration_behavior: "always_invoice",
          });

          console.log(`Stripe subscription updated with new add-ons`);
        }

        // Determine what type of subscription this is from the metadata
        const subscriptionType = session.metadata?.type || "unknown";
        console.log(`Checkout session subscription type: ${subscriptionType}`);

        // Prepare the update data
        const updateData: any = {
          planId,
          additionalUsers: parseInt(additionalUsers || "0"),
          additionalStorageGB: parseInt(additionalStorageGB || "0"),
          stripeCustomerId: session.customer as string,
        };

        // Add trial information if applicable
        if (isTrialSubscription && trialEnd) {
          updateData.isOnTrial = true;
          updateData.trialEndDate = trialEnd;
        }

        // Set the subscription ID and billing interval
        updateData.stripeSubscriptionId = subscriptionId;

        // Get the billing interval from the Stripe subscription
        const stripeSubscription: Stripe.Subscription =
          await stripe.subscriptions.retrieve(subscriptionId);
        updateData.billingInterval =
          stripeSubscription.items.data[0]?.price.recurring?.interval ||
          "month";

        // Update the subscription in our database
        const updatedSubscription = await db.subscription.update({
          where: {
            id: existingSubscription.id,
          },
          data: updateData,
        });

        console.log(`Subscription updated successfully:`, {
          id: updatedSubscription.id,
          additionalUsers: updatedSubscription.additionalUsers,
          additionalStorageGB: updatedSubscription.additionalStorageGB,
          planId: updatedSubscription.planId,
        });
      } catch (updateError) {
        console.error(`Error updating subscription in database:`, updateError);
        throw updateError;
      }
    } else {
      // Create new subscription
      console.log(`Creating new subscription for tenant ${tenantId}`);
      console.log(`additionalUsers=${parseInt(additionalUsers || "0")}`);

      try {
        // Determine what type of subscription this is from the metadata
        const subscriptionType = session.metadata?.type || "unknown";
        console.log(
          `New checkout session subscription type: ${subscriptionType}`
        );

        // Prepare the create data
        const createData: any = {
          tenantId,
          planId,
          additionalUsers: parseInt(additionalUsers || "0"),
          isActive: true,
          startDate: new Date(stripeSubscription.start_date * 1000),
          stripeCustomerId: session.customer as string,
        };

        // Add trial information if applicable
        if (isTrialSubscription && trialEnd) {
          createData.isOnTrial = true;
          createData.trialEndDate = trialEnd;
        }

        // Set the subscription ID and billing interval
        createData.stripeSubscriptionId = subscriptionId;
        createData.billingInterval =
          stripeSubscription.items.data[0]?.price.recurring?.interval ||
          "month";

        const newSubscription = await db.subscription.create({
          data: createData,
        });

        console.log(`Subscription created successfully:`, {
          id: newSubscription.id,
          additionalUsers: newSubscription.additionalUsers,
          planId: newSubscription.planId,
        });
      } catch (createError) {
        console.error(`Error creating subscription in database:`, createError);
        throw createError;
      }
    }

    console.log(`Subscription created/updated for tenant ${tenantId}`);
  } catch (error) {
    console.error("Error handling checkout session completed:", error);
  }
}

async function handleSubscriptionUpdated(subscription: Stripe.Subscription) {
  try {
    // Check if this is a unified subscription
    const isUnifiedSubscription = subscription.metadata?.unified === "true";
    console.log(`Is unified subscription: ${isUnifiedSubscription}`);

    // Find the subscription in our database by the Stripe subscription ID
    const dbSubscription = await db.subscription.findFirst({
      where: {
        stripeSubscriptionId: subscription.id,
      },
    });

    if (!dbSubscription) {
      console.error(`Subscription not found: ${subscription.id}`);
      return;
    }

    // Determine which type of subscription was updated
    const subscriptionType = subscription.metadata?.type || "unknown";
    console.log(`Subscription type from metadata: ${subscriptionType}`);

    // Check if there are any line items for additional users or storage
    let additionalUsers: any = dbSubscription.additionalUsers;
    let additionalStorageGB: any = dbSubscription.additionalStorageGB || 0;
    let planId = dbSubscription.planId;

    // Process based on subscription type
    if (subscriptionType === "base_plan") {
      // This is a base plan subscription update
      if (subscription.metadata?.planId) {
        planId = subscription.metadata.planId;
        console.log(`Updating plan ID to: ${planId}`);
      }
    } else if (subscriptionType === "additional_users") {
      // This is an additional users subscription update
      for (const item of subscription.items.data) {
        const product = await stripe.products.retrieve(
          item.price.product as string
        );

        if (product.name === "Additional Users") {
          additionalUsers = item.quantity;
          console.log(`Updating additional users to: ${additionalUsers}`);
        }
      }
    } else if (subscriptionType === "additional_storage") {
      // This is an additional storage subscription update
      for (const item of subscription.items.data) {
        const product = await stripe.products.retrieve(
          item.price.product as string
        );

        if (product.name.includes("Additional Vector Storage")) {
          console.log(`Found storage product: ${product.name}`);
          // Extract the storage tier from the product name
          if (product.name.includes("10GB")) {
            additionalStorageGB = 10;
          } else if (product.name.includes("50GB")) {
            additionalStorageGB = 50;
          } else if (product.name.includes("100GB")) {
            additionalStorageGB = 100;
          }
          console.log(
            `Updating additional storage to: ${additionalStorageGB} GB`
          );
        }
      }
    } else {
      // Legacy subscription or unknown type - process all items
      console.log(
        `Processing legacy subscription with ${subscription.items.data.length} items`
      );

      for (const item of subscription.items.data) {
        const product = await stripe.products.retrieve(
          item.price.product as string
        );

        console.log(`Processing product: ${product.id}, name: ${product.name}`);

        // If this is the additional users product
        if (product.name === "Additional Users") {
          additionalUsers = item.quantity;
          console.log(`Found additional users: ${additionalUsers}`);
        }

        // If this is an additional storage product
        if (product.name.includes("Additional Vector Storage")) {
          console.log(`Found storage product: ${product.name}`);
          // Extract the storage tier from the product name
          if (product.name.includes("10GB")) {
            additionalStorageGB = 10;
          } else if (product.name.includes("50GB")) {
            additionalStorageGB = 50;
          } else if (product.name.includes("100GB")) {
            additionalStorageGB = 100;
          }
          console.log(`Setting storage to ${additionalStorageGB} GB`);
        }
      }
    }

    // If we didn't find a specific additional users product, try to calculate from metadata
    if (
      additionalUsers === 0 &&
      subscription.metadata &&
      subscription.metadata.additionalUsers
    ) {
      additionalUsers = parseInt(subscription.metadata.additionalUsers) || 0;
      console.log(`Using additionalUsers from metadata: ${additionalUsers}`);
    }

    // If we didn't find a specific additional storage product, try to calculate from metadata
    if (
      additionalStorageGB === 0 &&
      subscription.metadata &&
      subscription.metadata.additionalStorageGB
    ) {
      additionalStorageGB =
        parseInt(subscription.metadata.additionalStorageGB) || 0;
      console.log(
        `Using additionalStorageGB from metadata: ${additionalStorageGB}`
      );
    }

    // Check if there's a plan change in the metadata from the legacy approach
    if (
      subscription.metadata &&
      subscription.metadata.planId &&
      subscriptionType === "unknown"
    ) {
      planId = subscription.metadata.planId;
      console.log(`Plan change detected in metadata (legacy): ${planId}`);
    }

    // Update the subscription status, plan, and additional users/storage
    console.log(
      `Updating subscription ${dbSubscription.id} with planId=${planId}, additionalUsers=${additionalUsers}, additionalStorageGB=${additionalStorageGB}`
    );

    try {
      // Check if this is a trial subscription
      const isTrialSubscription = subscription.status === "trialing";
      const trialEnd = subscription.trial_end
        ? new Date(subscription.trial_end * 1000)
        : null;

      console.log(
        `Subscription status: ${subscription.status}, Trial: ${isTrialSubscription}, Trial end: ${trialEnd}`
      );

      // Prepare the update data
      const updateData: any = {
        planId,
        isActive:
          subscription.status === "active" ||
          subscription.status === "trialing",
        additionalUsers,
        additionalStorageGB,
        endDate:
          subscription.status !== "active" && subscription.status !== "trialing"
            ? new Date(subscription.start_date * 1000)
            : null,
      };

      // Update trial information
      if (isTrialSubscription && trialEnd) {
        updateData.isOnTrial = true;
        updateData.trialEndDate = trialEnd;
      } else {
        updateData.isOnTrial = false;
        updateData.trialEndDate = null;
      }

      // Update the subscription ID and billing interval
      updateData.stripeSubscriptionId = subscription.id;
      updateData.billingInterval =
        subscription.items.data[0]?.price.recurring?.interval || "month";

      const updatedSubscription = await db.subscription.update({
        where: {
          id: dbSubscription.id,
        },
        data: updateData,
      });

      console.log(`Subscription updated successfully:`, {
        id: updatedSubscription.id,
        additionalUsers: updatedSubscription.additionalUsers,
        additionalStorageGB: updatedSubscription.additionalStorageGB,
        isActive: updatedSubscription.isActive,
      });
    } catch (updateError) {
      console.error(`Error updating subscription in database:`, updateError);
      throw updateError;
    }

    console.log(
      `Subscription updated: ${subscription.id}, Additional users: ${additionalUsers}, Additional storage: ${additionalStorageGB} GB`
    );
  } catch (error) {
    console.error("Error handling subscription updated:", error);
  }
}

async function handleSubscriptionDeleted(subscription: Stripe.Subscription) {
  try {
    // Check if this is a unified subscription
    const isUnifiedSubscription = subscription.metadata?.unified === "true";
    console.log(`Is unified subscription: ${isUnifiedSubscription}`);

    // Find the subscription in our database by the Stripe subscription ID
    const dbSubscription = await db.subscription.findFirst({
      where: {
        stripeSubscriptionId: subscription.id,
      },
    });

    if (!dbSubscription) {
      console.error(`Subscription not found: ${subscription.id}`);
      return;
    }

    // Determine which type of subscription was deleted
    const subscriptionType = subscription.metadata?.type || "unknown";
    console.log(`Deleted subscription type from metadata: ${subscriptionType}`);

    // Prepare the update data
    const updateData: any = {};

    // Mark the subscription as inactive
    updateData.isActive = false;
    updateData.endDate = new Date();
    updateData.stripeSubscriptionId = null;

    // Update the subscription in the database
    await db.subscription.update({
      where: {
        id: dbSubscription.id,
      },
      data: updateData,
    });

    console.log(
      `Subscription updated after deletion: ${subscription.id}, type: ${subscriptionType}`
    );

    console.log(`Subscription deleted: ${subscription.id}`);
  } catch (error) {
    console.error("Error handling subscription deleted:", error);
  }
}

/**
 * Handle price.created event
 */
async function handlePriceCreated(price: Stripe.Price) {
  try {
    // Skip if price is not active
    if (!price.active) {
      console.log(`Price ${price.id} is not active. Skipping.`);
      return;
    }

    // Get the product to access metadata
    const product = await stripe.products.retrieve(
      typeof price.product === "string" ? price.product : price.product.id
    );

    // Extract metadata
    const metadata = product.metadata || {};
    const type = metadata.type;
    const planName = metadata.plan_name;
    const sizeGB = metadata.size_gb ? parseInt(metadata.size_gb) : null;

    // Determine if this is a monthly or yearly price
    const isYearly = price.recurring?.interval === "year";

    console.log(`Price details:
      - ID: ${price.id}
      - Type: ${type}
      - Plan: ${planName}
      - Size: ${sizeGB}
      - Interval: ${isYearly ? "yearly" : "monthly"}
    `);

    // Update the appropriate record based on metadata
    if (type === "base_plan" && planName) {
      await updatePlanPrice(planName, price.id, isYearly);
    } else if (type === "additional_user" && planName) {
      await updatePlanUserPrice(planName, price.id, isYearly);
    } else if (type === "storage_tier" && sizeGB) {
      await updateStorageTierPrice(sizeGB, price.id, isYearly);
    } else {
      console.log(
        `Unknown price type or missing metadata. Type: ${type}, Plan: ${planName}, Size: ${sizeGB}`
      );
    }
  } catch (error) {
    console.error(`Error handling price created: ${error}`);
  }
}

/**
 * Handle price.updated event
 */
async function handlePriceUpdated(price: Stripe.Price) {
  // For price updates, we'll just reuse the same logic as price creation
  // This ensures our database stays in sync even if prices are updated
  await handlePriceCreated(price);
}

/**
 * Update plan price
 */
async function updatePlanPrice(
  planName: string,
  priceId: string,
  isYearly: boolean
) {
  try {
    // Find the plan by name
    const plan = await db.plan.findFirst({
      where: {
        name: planName,
      },
    });

    if (!plan) {
      console.log(`Plan with name ${planName} not found.`);
      return;
    }

    // Update the appropriate price ID
    if (isYearly) {
      await db.plan.update({
        where: { id: plan.id },
        data: { stripeYearlyPriceId: priceId },
      });
      console.log(`Updated yearly price ID for plan ${planName} to ${priceId}`);
    } else {
      await db.plan.update({
        where: { id: plan.id },
        data: { stripePriceId: priceId },
      });
      console.log(
        `Updated monthly price ID for plan ${planName} to ${priceId}`
      );
    }
  } catch (error) {
    console.error(`Error updating plan price: ${error}`);
    throw error;
  }
}

/**
 * Update plan user price
 */
async function updatePlanUserPrice(
  planName: string,
  priceId: string,
  isYearly: boolean
) {
  try {
    // Find the plan by name
    const plan = await db.plan.findFirst({
      where: {
        name: planName,
      },
    });

    if (!plan) {
      console.log(`Plan with name ${planName} not found.`);
      return;
    }

    // Update the appropriate user price ID
    if (isYearly) {
      await db.plan.update({
        where: { id: plan.id },
        data: { stripeUserYearlyPriceId: priceId },
      });
      console.log(
        `Updated yearly user price ID for plan ${planName} to ${priceId}`
      );
    } else {
      await db.plan.update({
        where: { id: plan.id },
        data: { stripeUserPriceId: priceId },
      });
      console.log(
        `Updated monthly user price ID for plan ${planName} to ${priceId}`
      );
    }
  } catch (error) {
    console.error(`Error updating plan user price: ${error}`);
    throw error;
  }
}

/**
 * Update storage tier price
 */
async function updateStorageTierPrice(
  sizeGB: number,
  priceId: string,
  isYearly: boolean
) {
  try {
    // Find the storage tier by size
    const storageTier = await db.storageTier.findFirst({
      where: {
        sizeGB: sizeGB,
      },
    });

    if (!storageTier) {
      console.log(`Storage tier with size ${sizeGB}GB not found.`);

      // Create a new storage tier if it doesn't exist
      const name = `${sizeGB}GB`;
      const price = 0; // Default price, will be updated from Stripe

      await db.storageTier.create({
        data: {
          name,
          sizeGB,
          price,
          stripePriceId: isYearly ? "" : priceId,
          stripeYearlyPriceId: isYearly ? priceId : "",
          isActive: true,
        },
      });

      console.log(
        `Created new storage tier ${name} with ${
          isYearly ? "yearly" : "monthly"
        } price ID ${priceId}`
      );
      return;
    }

    // Update the appropriate price ID
    if (isYearly) {
      await db.storageTier.update({
        where: { id: storageTier.id },
        data: { stripeYearlyPriceId: priceId },
      });
      console.log(
        `Updated yearly price ID for storage tier ${sizeGB}GB to ${priceId}`
      );
    } else {
      await db.storageTier.update({
        where: { id: storageTier.id },
        data: { stripePriceId: priceId },
      });
      console.log(
        `Updated monthly price ID for storage tier ${sizeGB}GB to ${priceId}`
      );
    }
  } catch (error) {
    console.error(`Error updating storage tier price: ${error}`);
    throw error;
  }
}
