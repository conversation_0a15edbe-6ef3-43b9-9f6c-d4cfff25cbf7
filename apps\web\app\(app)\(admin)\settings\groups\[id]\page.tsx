"use client";

import { useEffect, useState } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import { useLanguage } from "@/lib/language-context";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  getGroup,
  removeUserFromGroup,
  removeRoleFromGroup,
} from "@/services/src/group";
import { AddUserToGroupDialog } from "@/components/model/add-user-to-group-dialog";
import { AssignRoleToGroupDialog } from "@/components/model/assign-role-to-group-dialog";
import { UserPlus, FolderPlus, Trash2, Users, ArrowLeft } from "lucide-react";
import toast from "react-hot-toast";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { getCookie } from "@/utils/cookies";
import { Badge } from "@/components/ui/badge";

// Define types for our components
interface Group {
  id: string;
  name: string;
  description?: string;
  tenantId: string;
  groupMembers: GroupMember[];
  customRoleId?: string;
  customRole?: CustomRole;
  createdAt: string;
  updatedAt: string;
}

interface CustomRole {
  id: string;
  name: string;
  description?: string;
  permissions?: RolePermission[];
}

interface RolePermission {
  id: string;
  permission?: {
    action: string;
    resource: string;
  };
}

interface GroupMember {
  id: string;
  userId: string;
  groupId: string;
  user: {
    id: string;
    name: string;
    email: string;
    image?: string;
  };
}

export default function GroupDetailsPage() {
  const params = useParams();
  const router = useRouter();
  const { data: session } = useSession();
  const { t } = useLanguage();
  const [group, setGroup] = useState<Group | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState("members");
  const tenantId = getCookie("currentOrganizationId") as string;
  const userId = getCookie("userId") as string;
  const groupId = params?.id as string;

  const fetchGroup = async () => {
    if (userId && tenantId && groupId) {
      try {
        setLoading(true);
        const result = await getGroup(groupId, userId, tenantId);
        if (result?.data) {
          setGroup(result.data);
        } else if (result?.error) {
          toast.error(result.error);
          router.push("/settings/groups");
        }
      } catch (error) {
        console.error("Error fetching group:", error);
        toast.error(t("groups.failedToFetchGroup"));
        router.push("/settings/groups");
      } finally {
        setLoading(false);
      }
    }
  };

  useEffect(() => {
    fetchGroup();
  }, [session, groupId]);

  // Callback functions for real-time UI updates
  const handleUserAdded = async (newUser?: any, isOptimistic = true) => {
    // If we have the new user data, update the state optimistically
    if (newUser && group && isOptimistic) {
      // Check if user is already in the group to avoid duplicates
      const userExists = group.groupMembers.some(member => member.userId === newUser.id);
      if (!userExists) {
        const newMember = {
          id: `temp-${Date.now()}`, // Temporary ID
          userId: newUser.id,
          groupId: group.id,
          user: newUser,
        };

        // Update the group immediately
        const updatedGroup = {
          ...group,
          groupMembers: [...group.groupMembers, newMember],
        };
        setGroup(updatedGroup);
      }
    }

    // If this is not an optimistic update, fetch fresh data
    if (!isOptimistic) {
      // Delay the fetch slightly to allow server to process the addition
      setTimeout(() => {
        fetchGroup();
      }, 500);
    }

    // User added successfully - no cross-page notification needed
  };

  const handleRoleAssigned = () => {
    fetchGroup(); // Refresh the group to update role information
  };

  const handleRemoveUser = async (userIdToRemove: string) => {
    if (!group || !tenantId) return;

    try {
      const result = await removeUserFromGroup(
        group.id,
        userIdToRemove,
        tenantId,
        userId
      );
      if (result?.error) {
        toast.error(result.error || t("common.error"));
        return;
      }
      toast.success(t("groups.userRemovedFromGroup"));

      // Update the local state
      setGroup({
        ...group,
        groupMembers: group.groupMembers.filter(
          (member) => member.userId !== userIdToRemove
        ),
      });

      // User removed successfully
    } catch (error) {
      toast.error(t("groups.failedToRemoveUser"));
    }
  };

  const handleRemoveRole = async () => {
    if (!group || !tenantId) return;

    try {
      const result = await removeRoleFromGroup(group.id, tenantId, userId);
      if (result?.error) {
        toast.error(result.error || t("common.error"));
        return;
      }
      toast.success(t("groups.roleRemovedFromGroup"));

      // Update the local state
      setGroup({
        ...group,
        customRole: undefined,
        customRoleId: undefined,
      });
    } catch (error) {
      toast.error(t("groups.failedToRemoveRole"));
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <h2 className="text-xl font-semibold mb-2">{t("common.loading")}</h2>
          <p className="text-muted-foreground">{t("groups.loadingGroup")}</p>
        </div>
      </div>
    );
  }

  if (!group) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <h2 className="text-xl font-semibold mb-2">
            {t("groups.groupNotFound")}
          </h2>
          <p className="text-muted-foreground mb-4">
            {t("groups.groupNotFoundDescription")}
          </p>
          <Button onClick={() => router.push("/settings/groups")}>
            {t("groups.backToGroups")}
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header Section */}
      <Card>
        <CardHeader className="p-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Button
                variant="ghost"
                size="icon"
                onClick={() => router.push("/settings/groups")}
                className="mr-2"
              >
                <ArrowLeft className="h-4 w-4" />
              </Button>
              <div className="p-2 bg-primary/10 rounded-lg">
                <Users className="h-6 w-6 text-primary" />
              </div>
              <div>
                <div className="flex items-center justify-between gap-4 mt-2">
                  <div>
                    <CardTitle className="text-2xl">{group.name}</CardTitle>
                    <p className="text-muted-foreground mt-1">
                      {group.description || t("common.noDescription")}
                    </p>
                  </div>
                  <Badge variant="secondary">
                    {group.groupMembers.length}{" "}
                    {group.groupMembers.length === 1
                      ? t("common.member")
                      : t("common.members")}
                  </Badge>
                  {group.customRole && (
                    <Badge variant="outline">
                      {t("groups.role")}: {group.customRole.name}
                    </Badge>
                  )}
                </div>
              </div>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Group Details Tabs */}
      <Card className="border shadow-sm hover:shadow-md transition-all duration-200">
        <CardContent className="p-0">
          <Tabs
            defaultValue="members"
            value={activeTab}
            onValueChange={setActiveTab}
            className="w-full"
          >
            <div className="border-b">
              <div className="container">
                <TabsList className="h-12 w-full justify-start rounded-none border-b-0 bg-transparent p-0">
                  <TabsTrigger
                    value="members"
                    className="h-12 rounded-none border-b-2 border-b-transparent data-[state=active]:border-b-primary data-[state=active]:bg-transparent"
                  >
                    {t("groups.members")}
                  </TabsTrigger>
                  <TabsTrigger
                    value="roles"
                    className="h-12 rounded-none border-b-2 border-b-transparent data-[state=active]:border-b-primary data-[state=active]:bg-transparent"
                  >
                    {t("groups.role")}
                  </TabsTrigger>
                </TabsList>
              </div>
            </div>

            <div className="p-6">
              <TabsContent value="members" className="mt-0 p-0">
                <div className="flex justify-between items-center mb-6">
                  <h3 className="text-lg font-medium">
                    {t("groups.groupMembers")}
                  </h3>
                  <AddUserToGroupDialog
                    groupId={group.id}
                    tenantId={tenantId}
                    trigger={
                      <Button size="sm" variant="outline" className="gap-2">
                        <UserPlus className="h-4 w-4" />
                        {t("groups.addUser")}
                      </Button>
                    }
                    onUserAdded={handleUserAdded}
                  />
                </div>

                {group.groupMembers.length === 0 ? (
                  <div className="flex flex-col items-center justify-center py-12 px-4">
                    <div className="bg-muted/50 p-3 rounded-full mb-4">
                      <UserPlus className="h-8 w-8 text-muted-foreground" />
                    </div>
                    <h3 className="text-lg font-medium mb-2">
                      {t("groups.noMembersYet")}
                    </h3>
                    <p className="text-muted-foreground text-center mb-6">
                      {t("groups.addMembersDescription")}
                    </p>
                    <AddUserToGroupDialog
                      groupId={group.id}
                      tenantId={tenantId}
                      trigger={
                        <Button size="sm">
                          <UserPlus className="mr-2 h-4 w-4" />
                          {t("groups.addUser")}
                        </Button>
                      }
                      onUserAdded={handleUserAdded}
                    />
                  </div>
                ) : (
                  <div className="border rounded-lg overflow-hidden">
                    <Table>
                      <TableHeader>
                        <TableRow className="bg-muted/50 hover:bg-muted/50">
                          <TableHead>{t("common.user")}</TableHead>
                          <TableHead>{t("common.email")}</TableHead>
                          <TableHead className="w-[100px] text-right">
                            {t("common.actions")}
                          </TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {group.groupMembers.map((member) => (
                          <TableRow key={member.id}>
                            <TableCell>
                              <div className="flex items-center gap-3">
                                <Avatar className="h-8 w-8">
                                  <AvatarImage
                                    src={member.user.image}
                                    alt={member.user.name}
                                  />
                                  <AvatarFallback className="bg-primary/10 text-primary">
                                    {member.user.name
                                      ?.split(" ")
                                      .map((n: string) => n[0])
                                      .join("")}
                                  </AvatarFallback>
                                </Avatar>
                                <span className="font-medium">
                                  {member.user.name}
                                </span>
                              </div>
                            </TableCell>
                            <TableCell className="text-muted-foreground">
                              {member.user.email}
                            </TableCell>
                            <TableCell className="text-right">
                              <AlertDialog>
                                <AlertDialogTrigger asChild>
                                  <Button
                                    variant="ghost"
                                    size="icon"
                                    className="h-8 w-8 text-muted-foreground hover:text-destructive"
                                  >
                                    <Trash2 className="h-4 w-4" />
                                  </Button>
                                </AlertDialogTrigger>
                                <AlertDialogContent>
                                  <AlertDialogHeader>
                                    <AlertDialogTitle>
                                      {t("groups.removeUserConfirm")}
                                    </AlertDialogTitle>
                                    <AlertDialogDescription>
                                      {t("groups.removeUserDescription")}
                                    </AlertDialogDescription>
                                  </AlertDialogHeader>
                                  <AlertDialogFooter>
                                    <AlertDialogCancel>
                                      {t("common.cancel")}
                                    </AlertDialogCancel>
                                    <AlertDialogAction
                                      onClick={() => {
                                        handleRemoveUser(member.user.id);
                                      }}
                                      className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                                    >
                                      {t("common.remove")}
                                    </AlertDialogAction>
                                  </AlertDialogFooter>
                                </AlertDialogContent>
                              </AlertDialog>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                )}
              </TabsContent>

              <TabsContent value="roles" className="mt-0 p-0">
                <div className="flex justify-between items-center mb-6">
                  <h3 className="text-lg font-medium">
                    {t("groups.workspaceAccess")}
                  </h3>
                  <AssignRoleToGroupDialog
                    groupId={group.id}
                    tenantId={tenantId}
                    trigger={
                      <Button size="sm" variant="outline" className="gap-2">
                        <FolderPlus className="h-4 w-4" />
                        {t("groups.assignRole")}
                      </Button>
                    }
                    onRoleAssigned={handleRoleAssigned}
                  />
                </div>

                {!group.customRole ? (
                  <div className="flex flex-col items-center justify-center py-12 px-4">
                    <div className="bg-muted/50 p-3 rounded-full mb-4">
                      <FolderPlus className="h-8 w-8 text-muted-foreground" />
                    </div>
                    <h3 className="text-lg font-medium mb-2">
                      {t("groups.noRoleAssigned")}
                    </h3>
                    <p className="text-muted-foreground text-center mb-6">
                      {t("groups.assignRoleDescription")}
                    </p>
                    <AssignRoleToGroupDialog
                      groupId={group.id}
                      tenantId={tenantId}
                      trigger={
                        <Button size="sm">
                          <FolderPlus className="mr-2 h-4 w-4" />
                          {t("groups.assignRole")}
                        </Button>
                      }
                      onRoleAssigned={handleRoleAssigned}
                    />
                  </div>
                ) : (
                  <Card className="overflow-hidden">
                    <CardHeader className="p-4 pb-2 flex flex-row justify-between items-start">
                      <div>
                        <CardTitle className="text-base">
                          {group.customRole.name}
                        </CardTitle>
                        <p className="text-sm text-muted-foreground mt-1">
                          {group.customRole.description ||
                            t("groups.noDescription")}
                        </p>
                        <div className="mt-4">
                          <h4 className="text-sm font-medium mb-2">
                            {t("roles.permissions")}:
                          </h4>
                          <div className="flex flex-wrap gap-2">
                            {group.customRole.permissions?.map((p) => (
                              <div
                                key={p.id}
                                className="bg-muted px-2 py-1 rounded-md text-xs"
                              >
                                {p.permission?.action} {p.permission?.resource}
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>
                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-8 w-8 text-muted-foreground hover:text-destructive"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </AlertDialogTrigger>
                        <AlertDialogContent>
                          <AlertDialogHeader>
                            <AlertDialogTitle>
                              {t("groups.removeRoleConfirm")}
                            </AlertDialogTitle>
                            <AlertDialogDescription>
                              {t("groups.removeRoleDescription")}
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel>
                              {t("common.cancel")}
                            </AlertDialogCancel>
                            <AlertDialogAction
                              onClick={handleRemoveRole}
                              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                            >
                              {t("common.remove")}
                            </AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                    </CardHeader>
                  </Card>
                )}
              </TabsContent>
            </div>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}
