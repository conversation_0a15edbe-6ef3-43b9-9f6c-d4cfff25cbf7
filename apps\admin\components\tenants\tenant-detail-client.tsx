"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Switch } from "../ui/switch";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { formatDate } from "@/lib/utils";
import {
  ArrowLeft,
  ArrowUpDown,
  Edit,
  Save,
  Ban,
  CheckCircle,
  Users,
  Activity,
  DollarSign,
  Settings,
  UserCheck,
  Zap,
  TrendingUp,
  TrendingDown,
  MessageSquare,
  Database,
  FileText,
  Star,
  Calendar,
  ArrowUp,
  ArrowDown,
  Minus,
  Search,
  X,
  ChevronLeft,
  ChevronRight,
  Info,
  Heart,
  Shield,
  AlertTriangle,
  Loader2,
  Check,
} from "lucide-react";
import {
  LineChart,
  Line,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Cell,
} from "recharts";
import { useRouter } from "next/navigation";
import React, { useState, useMemo, useEffect } from "react";
import Link from "next/link";
import { ChangePlanModal } from "@/components/tenants/change-plan-modal";
import { useToast } from "@/components/ui/use-toast";

// Enhanced types for tenant detail view
interface TenantDetail {
  id: string;
  name: string;
  slug: string;
  createdAt: Date;
  plan: {
    id: string;
    name: string;
    type: string;
    price: number;
    features?: any;
  } | null;
  status: "active" | "inactive" | "suspended";
  llmScope?: string[];
  fileUploadLimitDuringChat?: number;
  users: Array<{
    id: string;
    name: string;
    email: string;
    role: string;
    lastSeen: Date | null;
    status: string;
  }>;
  usage: {
    tokenUsage: {
      current: number;
      inputTokens: number;
      outputTokens: number;
    };
    storage: {
      current: number;
    };
    apiRequests: {
      current: number;
    };
  };
  tokenAnalytics: {
    dailyUsage: Array<{
      date: string;
      totalTokens: number;
    }>;
    featureBreakdown: Array<{
      requestType: string;
      totalTokens: number;
      percentage: number;
      count: number;
    }>;
  };
  financial: {
    infraCost: number;
    revenue: number;
    margin: number;
    costBreakdown: {
      tokenCost: number;
      storageCost: number;
      apiCost: number;
    };
    dailyTrends?: Array<{
      date: string;
      revenue: number;
      infraCost: number;
      margin: number;
      marginPercent: number;
    }>;
  };
  healthScore: {
    score: number;
    breakdown: {
      lastActiveScore: number;
      quotaUsageScore: number;
      featureAdoptionScore: number;
      supportTicketPenalty: number;
      churnPredictorPenalty: number;
    };
  };
  lastActivity: Date | null;
}

interface Plan {
  id: string;
  name: string;
  type: string;
  description?: string;
  price?: number;
  includedUsers: number;
  vectorStoreGB: number;
  webSearchLimit: number;
  additionalUserFee?: number;
}

interface TenantDetailClientProps {
  tenant: TenantDetail;
  availablePlans: Plan[];
}

// Helper functions
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat("de-CH", {
    style: "currency",
    currency: "CHF",
    minimumFractionDigits: 2,
  }).format(amount);
};

const formatNumber = (num: number) => {
  return new Intl.NumberFormat().format(num);
};

// Chart colors for consistency
const CHART_COLORS = [
  "#3B82F6", // Blue
  "#10B981", // Green
  "#F59E0B", // Amber
  "#EF4444", // Red
  "#8B5CF6", // Purple
  "#06B6D4", // Cyan
  "#F97316", // Orange
  "#84CC16", // Lime
];

// Helper function to get icon for feature type
const getFeatureIcon = (requestType: string) => {
  const type = requestType.toLowerCase();
  switch (type) {
    case "chat":
      return MessageSquare;
    case "embedding":
      return Database;
    case "completion":
      return FileText;
    default:
      return Activity;
  }
};

// Helper function to get current month name
const getCurrentMonthYear = () => {
  const now = new Date();
  return now.toLocaleDateString("en-US", { month: "long", year: "numeric" });
};

// Helper function to format date range
const getDateRange = () => {
  const now = new Date();
  const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
  const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0);

  return `${startOfMonth.toLocaleDateString("en-US", {
    month: "short",
    day: "numeric",
  })} - ${endOfMonth.toLocaleDateString("en-US", {
    month: "short",
    day: "numeric",
    year: "numeric",
  })}`;
};

const getStatusBadge = (status: string) => {
  const variants = {
    active: "bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100",
    inactive: "bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100",
    suspended:
      "bg-orange-100 text-orange-800 dark:bg-orange-800 dark:text-orange-100",
  };

  return (
    <Badge
      className={variants[status as keyof typeof variants] || variants.inactive}
    >
      {status.charAt(0).toUpperCase() + status.slice(1)}
    </Badge>
  );
};

// Helper function to get LLM scope label from array
const getLlmScopeLabel = (scopeArray: string[] | undefined) => {
  if (!scopeArray || scopeArray.length === 0) {
    return "Not Set";
  }

  if (scopeArray.length === 1 && scopeArray[0] === "INTERNAL_ONLY") {
    return "Internal Only";
  }

  const displayParts: string[] = [];
  if (scopeArray.includes("web")) displayParts.push("Web");
  if (scopeArray.includes("mcp")) displayParts.push("MCP");
  if (scopeArray.includes("hybrid")) displayParts.push("Hybrid");
  if (scopeArray.includes("deep_search")) displayParts.push("Deep Search");

  return displayParts.length > 0
    ? displayParts.join(" + ")
    : "Custom Configuration";
};

// Health Score Component with Breakdown Modal
const HealthScoreComponent = ({
  healthScore,
}: {
  healthScore: {
    score: number;
    breakdown: {
      lastActiveScore: number;
      quotaUsageScore: number;
      featureAdoptionScore: number;
      supportTicketPenalty: number;
      churnPredictorPenalty: number;
      featureUsageDetails?: {
        totalFeaturesAvailable: number;
        featuresUsed: number;
        featureUsageStats: Array<{
          featureName: string;
          usageCount: number;
          percentage: number;
        }>;
      };
    };
  };
}) => {
  const score = healthScore.score;
  const breakdown = healthScore.breakdown;

  const getScoreColor = (score: number) => {
    if (score >= 80) return "text-green-600";
    if (score >= 60) return "text-yellow-600";
    return "text-red-600";
  };

  const getScoreIcon = (score: number) => {
    if (score >= 80) return Heart;
    if (score >= 60) return Shield;
    return AlertTriangle;
  };

  const getScoreLabel = (score: number) => {
    if (score >= 80) return "Healthy";
    if (score >= 60) return "Watchlist";
    return "At Risk";
  };

  const getBadgeClasses = (score: number) => {
    if (score >= 80)
      return "bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100";
    if (score >= 60)
      return "bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100";
    return "bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100";
  };

  const ScoreIcon = getScoreIcon(score);

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Badge
          className={`${getBadgeClasses(
            score
          )} cursor-pointer hover:opacity-80 transition-opacity`}
        >
          <ScoreIcon className="w-3 h-3 mr-1" />
          Tenant Health Score
          <Info className="w-3 h-3 ml-1" />
        </Badge>
      </DialogTrigger>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <ScoreIcon className={`w-5 h-5 ${getScoreColor(score)}`} />
            Tenant Health Score Breakdown
          </DialogTitle>
          <DialogDescription>
            Comprehensive analysis of tenant health metrics and risk factors
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Overall Score */}
          <div className="text-center p-6 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <div className={`text-4xl font-bold ${getScoreColor(score)} mb-2`}>
              {score}/100
            </div>
            <div className="text-lg font-medium text-gray-700 dark:text-gray-300">
              {getScoreLabel(score)}
            </div>
            <div className="text-sm text-gray-500 mt-1">
              Overall tenant health assessment
            </div>
          </div>

          {/* Score Components */}
          <div className="space-y-4">
            <h4 className="font-semibold text-lg">Score Components</h4>

            {/* Last Active Days */}
            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div className="flex items-center gap-3">
                <Activity className="w-5 h-5 text-blue-600" />
                <div>
                  <div className="font-medium">Last Active Days</div>
                </div>
              </div>
              <div className="text-right">
                <div className="font-bold text-lg">
                  {breakdown.lastActiveScore}/100
                </div>
                <div className="text-xs text-gray-500">
                  {breakdown.lastActiveScore >= 100
                    ? "0-7 days"
                    : breakdown.lastActiveScore >= 75
                      ? "8-30 days"
                      : breakdown.lastActiveScore >= 50
                        ? "31-90 days"
                        : "90+ days"}
                </div>
              </div>
            </div>

            {/* Quota Usage */}
            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div className="flex items-center gap-3">
                <Database className="w-5 h-5 text-purple-600" />
                <div>
                  <div className="font-medium">Quota Usage Sustained</div>
                </div>
              </div>
              <div className="text-right">
                <div className="font-bold text-lg">
                  {breakdown.quotaUsageScore}/100
                </div>
                <div className="text-xs text-gray-500">
                  {breakdown.quotaUsageScore >= 100
                    ? "0-50% usage"
                    : breakdown.quotaUsageScore >= 75
                      ? "51-80% usage"
                      : breakdown.quotaUsageScore >= 50
                        ? "81-95% usage"
                        : "96-100% usage"}
                </div>
              </div>
            </div>

            {/* Feature Adoption */}
            <div className="p-4 border rounded-lg">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center gap-3">
                  <Star className="w-5 h-5 text-amber-600" />
                  <div>
                    <div className="font-medium">Feature Adoption</div>
                  </div>
                </div>
                <div className="text-right">
                  <div className="font-bold text-lg">
                    {breakdown.featureAdoptionScore}/100
                  </div>
                  {breakdown.featureUsageDetails && (
                    <div className="text-xs text-gray-500">
                      {breakdown.featureUsageDetails.featuresUsed} /{" "}
                      {breakdown.featureUsageDetails.totalFeaturesAvailable}{" "}
                      features used
                    </div>
                  )}
                </div>
              </div>

              {/* Feature Usage Statistics */}
              {breakdown.featureUsageDetails &&
                breakdown.featureUsageDetails.featureUsageStats.length > 0 && (
                  <div className="mt-3 pt-3 border-t">
                    <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Feature Usage Breakdown:
                    </div>
                    <div className="space-y-2">
                      {breakdown.featureUsageDetails.featureUsageStats.map(
                        (feature, index) => (
                          <div
                            key={index}
                            className="flex items-center justify-between text-sm"
                          >
                            <div className="flex items-center gap-2">
                              <div className="w-2 h-2 bg-amber-500 rounded-full"></div>
                              <span className="font-medium text-gray-700 dark:text-gray-300">
                                {feature.featureName}
                              </span>
                            </div>
                            <div className="text-right">
                              <div className="font-semibold text-gray-900 dark:text-gray-100">
                                {feature.usageCount.toLocaleString()} times
                              </div>
                              <div className="text-xs text-gray-500">
                                {feature.percentage}% of usage
                              </div>
                            </div>
                          </div>
                        )
                      )}
                    </div>
                  </div>
                )}

              {/* No Usage State */}
              {breakdown.featureUsageDetails &&
                breakdown.featureUsageDetails.featureUsageStats.length ===
                  0 && (
                  <div className="mt-3 pt-3 border-t">
                    <div className="text-sm text-gray-500 text-center py-2">
                      No feature usage detected for this tenant
                    </div>
                  </div>
                )}
            </div>

            {/* Support Ticket Penalty */}
            {breakdown.supportTicketPenalty < 0 && (
              <div className="flex items-center justify-between p-4 border rounded-lg bg-red-50 dark:bg-red-900/20">
                <div className="flex items-center gap-3">
                  <AlertTriangle className="w-5 h-5 text-red-600" />
                  <div>
                    <div className="font-medium">Support Ticket Volume</div>
                    <div className="text-sm text-gray-500">Penalty applied</div>
                  </div>
                </div>
                <div className="text-right">
                  <div className="font-bold text-lg text-red-600">
                    {breakdown.supportTicketPenalty}
                  </div>
                  <div className="text-xs text-gray-500">
                    {breakdown.supportTicketPenalty <= -15
                      ? "6+ tickets"
                      : breakdown.supportTicketPenalty <= -10
                        ? "3-5 tickets"
                        : breakdown.supportTicketPenalty <= -5
                          ? "1-2 tickets"
                          : "0 tickets"}
                  </div>
                </div>
              </div>
            )}

            {/* Churn Predictor Penalty */}
            {breakdown.churnPredictorPenalty < 0 && (
              <div className="flex items-center justify-between p-4 border rounded-lg bg-orange-50 dark:bg-orange-900/20">
                <div className="flex items-center gap-3">
                  <TrendingDown className="w-5 h-5 text-orange-600" />
                  <div>
                    <div className="font-medium">Churn Predictors</div>
                    <div className="text-sm text-gray-500">Risk indicators</div>
                  </div>
                </div>
                <div className="text-right">
                  <div className="font-bold text-lg text-orange-600">
                    {breakdown.churnPredictorPenalty}
                  </div>
                  <div className="text-xs text-gray-500">
                    {breakdown.churnPredictorPenalty <= -10
                      ? "Major issues"
                      : breakdown.churnPredictorPenalty <= -5
                        ? "Minor issues"
                        : "No issues"}
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Recommendations */}
          <div className="space-y-3">
            <h4 className="font-semibold text-lg">Recommendations</h4>
            <div className="space-y-2 text-sm">
              {score < 80 && (
                <div className="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                  <div className="font-medium text-blue-800 dark:text-blue-200">
                    💡 Improvement Opportunities
                  </div>
                  <ul className="mt-2 space-y-1 text-blue-700 dark:text-blue-300">
                    {breakdown.lastActiveScore < 75 && (
                      <li>• Encourage more frequent platform usage</li>
                    )}
                    {breakdown.quotaUsageScore < 75 && (
                      <li>
                        • Monitor quota usage and consider plan optimization
                      </li>
                    )}
                    {breakdown.featureAdoptionScore < 75 && (
                      <li>• Promote feature discovery and adoption</li>
                    )}
                    {breakdown.supportTicketPenalty < 0 && (
                      <li>• Address support issues to improve satisfaction</li>
                    )}
                    {breakdown.churnPredictorPenalty < 0 && (
                      <li>• Proactive engagement to prevent churn</li>
                    )}
                  </ul>
                </div>
              )}
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

const getHealthScoreBadge = (healthScore: {
  score: number;
  breakdown: {
    lastActiveScore: number;
    quotaUsageScore: number;
    featureAdoptionScore: number;
    supportTicketPenalty: number;
    churnPredictorPenalty: number;
    featureUsageDetails?: {
      totalFeaturesAvailable: number;
      featuresUsed: number;
      featureUsageStats: Array<{
        featureName: string;
        usageCount: number;
        percentage: number;
      }>;
    };
  };
}) => {
  return <HealthScoreComponent healthScore={healthScore} />;
};

// NUCLEAR DATA SANITIZATION - ZERO TOLERANCE FOR NaN
const sanitizeNumber = (value: any): number => {
  // Handle all possible edge cases
  if (value === null || value === undefined || value === "") return 0;
  if (typeof value === "string" && value.trim() === "") return 0;
  if (typeof value === "boolean") return value ? 1 : 0;

  // Convert to number and validate
  let num: number;
  if (typeof value === "number") {
    num = value;
  } else {
    num = parseFloat(String(value));
  }

  // Triple check for any invalid values
  if (isNaN(num) || !isFinite(num) || !Number.isFinite(num)) return 0;
  if (num === Infinity || num === -Infinity) return 0;
  if (Object.is(num, NaN)) return 0;

  // Ensure non-negative and reasonable bounds
  return Math.max(0, Math.min(num, Number.MAX_SAFE_INTEGER));
};

const deepSanitizeObject = (obj: any): any => {
  if (obj === null || obj === undefined) return {};
  if (typeof obj !== "object") return {};

  const sanitized: any = {};
  for (const [key, value] of Object.entries(obj)) {
    if (
      typeof value === "number" ||
      (typeof value === "string" && !isNaN(Number(value)))
    ) {
      sanitized[key] = sanitizeNumber(value);
    } else if (typeof value === "string") {
      sanitized[key] = value;
    } else if (typeof value === "boolean") {
      sanitized[key] = value;
    } else {
      sanitized[key] = value;
    }
  }
  return sanitized;
};

const sanitizeChartData = (data: any[]): any[] => {
  if (!Array.isArray(data)) {
    return [];
  }

  const sanitized = data
    .filter((item) => item !== null && item !== undefined)
    .map((item) => {
      const sanitizedItem = deepSanitizeObject(item);

      // Force sanitize critical fields
      return {
        requestType: String(sanitizedItem.requestType || "Unknown"),
        totalTokens: sanitizeNumber(sanitizedItem.totalTokens),
        percentage: sanitizeNumber(sanitizedItem.percentage),
        count: sanitizeNumber(sanitizedItem.count),
        inputTokens: sanitizeNumber(sanitizedItem.inputTokens),
        outputTokens: sanitizeNumber(sanitizedItem.outputTokens),
      };
    })
    .filter((item) => {
      return (
        item.totalTokens > 0 &&
        Number.isFinite(item.totalTokens) &&
        Number.isFinite(item.percentage) &&
        Number.isFinite(item.count)
      );
    });

  return sanitized;
};

const sanitizeDailyUsageData = (data: any[]): any[] => {
  if (!Array.isArray(data)) {
    return [];
  }

  return data
    .filter((item) => item !== null && item !== undefined)
    .map((item) => ({
      date: String(item.date || "Unknown"),
      totalTokens: sanitizeNumber(item.totalTokens),
    }))
    .filter((item) => Number.isFinite(item.totalTokens));
};

const sanitizeFinancialTrendData = (data: any[]): any[] => {
  if (!Array.isArray(data)) {
    return [];
  }

  return data
    .filter((item) => item !== null && item !== undefined)
    .map((item) => {
      const revenue = sanitizeNumber(item.revenue);
      const infraCost = sanitizeNumber(item.infraCost);
      const margin = revenue - infraCost;
      const marginPercent = revenue > 0 ? (margin / revenue) * 100 : 0;

      return {
        date: String(item.date || "Unknown"),
        revenue: Math.max(0, revenue),
        infraCost: Math.max(0, infraCost),
        margin: margin,
        marginPercent: sanitizeNumber(marginPercent),
      };
    })
    .filter(
      (item) =>
        Number.isFinite(item.revenue) &&
        Number.isFinite(item.infraCost) &&
        Number.isFinite(item.margin) &&
        Number.isFinite(item.marginPercent)
    );
};

// Legacy function for backward compatibility
const validateChartData = sanitizeChartData;

// ULTRA-DEFENSIVE CHART WRAPPER WITH ERROR BOUNDARY
const SafeChart = ({
  children,
  data,
  type,
}: {
  children: React.ReactNode;
  data: any[];
  type: string;
}) => {
  // Ultra-defensive validation
  const isDataSafe = useMemo(() => {
    if (!Array.isArray(data) || data.length === 0) return false;

    return data.every((item) => {
      if (!item || typeof item !== "object") return false;

      // Check ALL properties, not just numeric ones
      for (const [key, value] of Object.entries(item)) {
        // Skip non-numeric properties that should be strings
        if (key === "requestType" || key === "date") continue;

        if (typeof value === "number") {
          // Ultra-strict validation
          if (value === null || value === undefined) return false;
          if (isNaN(value)) return false;
          if (!isFinite(value)) return false;
          if (!Number.isFinite(value)) return false;
          if (value === Infinity || value === -Infinity) return false;
          if (Object.is(value, NaN)) return false;
          // Allow negative values for financial data (margins can be negative)
          if (key !== "margin" && key !== "marginPercent" && value < 0)
            return false;
          if (value > Number.MAX_SAFE_INTEGER) return false;
        }
      }
      return true;
    });
  }, [data]);

  // Error boundary wrapper
  const [hasError, setHasError] = useState(false);

  useEffect(() => {
    setHasError(false);
  }, [data]);

  if (hasError || !isDataSafe) {
    return (
      <div className="flex items-center justify-center h-full text-muted-foreground">
        <div className="text-center">
          <Activity className="mx-auto h-12 w-12 mb-4 opacity-50" />
          <p>Chart data validation failed</p>
          <p className="text-xs mt-2">Unable to render {type} chart safely</p>
        </div>
      </div>
    );
  }

  // Wrap in error boundary
  try {
    return <>{children}</>;
  } catch (error) {
    console.error(`Chart rendering error for ${type}:`, error);
    setHasError(true);
    return (
      <div className="flex items-center justify-center h-full text-muted-foreground">
        <div className="text-center">
          <Activity className="mx-auto h-12 w-12 mb-4 opacity-50" />
          <p>Chart rendering failed</p>
        </div>
      </div>
    );
  }
};

// Fallback data generators
const generateFallbackDailyUsage = (): Array<{
  date: string;
  totalTokens: number;
}> => {
  const result: Array<{ date: string; totalTokens: number }> = [];
  const now = new Date();
  for (let i = 29; i >= 0; i--) {
    const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000);
    result.push({
      date: date.toLocaleDateString("en-US", {
        month: "short",
        day: "numeric",
      }),
      totalTokens: 0,
    });
  }
  return result;
};

const generateFallbackFeatureBreakdown = (): Array<{
  requestType: string;
  totalTokens: number;
  percentage: number;
  count: number;
}> => {
  return [];
};

const EmptyState = ({
  message = "No data available",
}: {
  message?: string;
}) => (
  <div className="flex items-center justify-center h-[300px] text-muted-foreground">
    <div className="text-center">
      <div className="text-lg font-medium mb-2">{message}</div>
      <div className="text-sm">Data will appear here once available</div>
    </div>
  </div>
);

const FinancialEmptyState = () => (
  <div className="flex items-center justify-center h-[400px] text-muted-foreground">
    <div className="text-center max-w-md">
      <div className="text-lg font-medium mb-2">
        No Financial Trend Data Available
      </div>
      <div className="text-sm mb-4">
        Historical financial data will appear here once the tenant has
        sufficient usage history and cost tracking data in the database.
      </div>
      <div className="text-xs text-muted-foreground">
        Financial trends require at least 7 days of usage data including token
        costs, storage costs, and subscription information.
      </div>
    </div>
  </div>
);

export function TenantDetailClient({
  tenant: initialTenant,
  availablePlans,
}: TenantDetailClientProps) {
  const router = useRouter();
  const { toast } = useToast();

  // ✅ Local state for tenant to enable real-time updates
  const [tenant, setTenant] = useState<TenantDetail>(initialTenant);

  // ✅ Sync local state when props change (e.g., on page refresh)
  useEffect(() => {
    setTenant(initialTenant);
  }, [initialTenant]);

  const [isEditing, setIsEditing] = useState(false);
  const [editedName, setEditedName] = useState(tenant.name);
  const [editedStatus, setEditedStatus] = useState(tenant.status);

  // Change Plan state
  const [changePlanModalOpen, setChangePlanModalOpen] = useState(false);

  // Feature Configuration state
  const [isUpdatingLlmScope, setIsUpdatingLlmScope] = useState(false);
  const [isUpdatingFileUploadLimit, setIsUpdatingFileUploadLimit] =
    useState(false);
  const [fileUploadLimit, setFileUploadLimit] = useState<string>(
    (tenant.fileUploadLimitDuringChat || 5).toString()
  );
  const [fileUploadLimitError, setFileUploadLimitError] = useState<string>("");

  // ✅ Suspension state management
  const [isSuspending, setIsSuspending] = useState(false);

  // ✅ Name editing state management
  const [isSaving, setIsSaving] = useState(false);

  // Change Plan functionality
  const handleChangePlanSuccess = (message: string) => {
    // Success is already handled by the modal with toast
    console.log("Plan change success:", message);
  };

  const handleChangePlanError = (error: string) => {
    // Error is already handled by the modal with toast
    console.error("Plan change error:", error);
  };

  // Feature Configuration functionality
  const handleLlmScopeChange = async (newScope: string) => {
    if (!newScope) return;

    // Convert single scope value to array format
    const scopeArray = [newScope];

    setIsUpdatingLlmScope(true);
    try {
      // Use API endpoint to update tenant llmScope
      const response = await fetch(
        `/api/tenants/${tenant.id}/update-llm-scope`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            llmScope: scopeArray,
          }),
        }
      );

      if (response.ok) {
        const result = await response.json();
        console.log("LLM Scope updated successfully:", result);

        // Show success toast
        toast({
          title: "Success",
          description: `Feature configuration updated to ${getLlmScopeLabel(
            scopeArray
          )}`,
        });

        // Refresh the page to show updated data
        router.refresh();
      } else {
        const errorData = await response.json();
        console.error("Failed to update LLM scope:", errorData.error);
        toast({
          title: "Error",
          description:
            errorData.error || "Failed to update feature configuration",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error updating LLM scope:", error);
      toast({
        title: "Error",
        description: "An error occurred while updating feature configuration",
        variant: "destructive",
      });
    } finally {
      setIsUpdatingLlmScope(false);
    }
  };

  // File Upload Limit validation functions
  const validateFileUploadLimit = (
    value: string
  ): { isValid: boolean; error: string; numericValue?: number } => {
    if (value === "") {
      return { isValid: false, error: "Field cannot be empty" };
    }

    // Check if value contains only digits
    if (!/^\d+$/.test(value)) {
      return { isValid: false, error: "Only numeric values are allowed" };
    }

    const numericValue = parseInt(value, 10);

    if (numericValue === 0) {
      return { isValid: false, error: "Value cannot be 0" };
    }

    if (numericValue < 1 || numericValue > 10) {
      return { isValid: false, error: "Value must be between 1 and 10" };
    }

    return { isValid: true, error: "", numericValue };
  };

  const isFileUploadLimitUpdateDisabled = (): boolean => {
    const validation = validateFileUploadLimit(fileUploadLimit);
    if (!validation.isValid) return true;

    // Disable if value hasn't changed
    if (validation.numericValue === tenant.fileUploadLimitDuringChat)
      return true;

    return false;
  };

  // File Upload Limit Configuration functionality
  const handleFileUploadLimitChange = async (inputValue: string) => {
    const validation = validateFileUploadLimit(inputValue);
    if (!validation.isValid || !validation.numericValue) return;

    const newLimit = validation.numericValue;
    if (newLimit === tenant.fileUploadLimitDuringChat) return;

    setIsUpdatingFileUploadLimit(true);
    try {
      // Use API endpoint to update tenant fileUploadLimitDuringChat
      const response = await fetch(
        `/api/tenants/${tenant.id}/update-file-upload-limit`,
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            fileUploadLimitDuringChat: newLimit,
          }),
        }
      );

      if (response.ok) {
        const result = await response.json();
        console.log("File upload limit updated successfully:", result);

        // Show success toast
        toast({
          title: "Success",
          description: `File upload limit updated to ${newLimit} files`,
        });

        // Refresh the page to show updated data
        router.refresh();
      } else {
        const errorData = await response.json();
        console.error("Failed to update file upload limit:", errorData.error);
        toast({
          title: "Error",
          description: errorData.error || "Failed to update file upload limit",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error updating file upload limit:", error);
      toast({
        title: "Error",
        description: "An error occurred while updating file upload limit",
        variant: "destructive",
      });
    } finally {
      setIsUpdatingFileUploadLimit(false);
    }
  };

  // Users tab pagination and search state
  const [usersSearchQuery, setUsersSearchQuery] = useState("");
  const [usersCurrentPage, setUsersCurrentPage] = useState(1);
  const usersPerPage = 5;

  // Filter and paginate users
  const filteredUsers = useMemo(() => {
    if (!usersSearchQuery.trim()) {
      return tenant.users;
    }

    const query = usersSearchQuery.toLowerCase().trim();
    return tenant.users.filter(
      (user) =>
        user.name.toLowerCase().includes(query) ||
        user.email.toLowerCase().includes(query) ||
        user.role.toLowerCase().includes(query)
    );
  }, [tenant.users, usersSearchQuery]);

  const paginatedUsers = useMemo(() => {
    const startIndex = (usersCurrentPage - 1) * usersPerPage;
    const endIndex = startIndex + usersPerPage;
    return filteredUsers.slice(startIndex, endIndex);
  }, [filteredUsers, usersCurrentPage, usersPerPage]);

  const totalUsersPages = Math.ceil(filteredUsers.length / usersPerPage);

  // Reset to first page when search changes
  useEffect(() => {
    setUsersCurrentPage(1);
  }, [usersSearchQuery]);

  // Pagination handlers
  const handleUsersPageChange = (page: number) => {
    setUsersCurrentPage(page);
  };

  const handleUsersPrevious = () => {
    setUsersCurrentPage((prev) => Math.max(1, prev - 1));
  };

  const handleUsersNext = () => {
    setUsersCurrentPage((prev) => Math.min(totalUsersPages, prev + 1));
  };

  const clearUsersSearch = () => {
    setUsersSearchQuery("");
  };

  // Aggressively sanitize ALL chart data to prevent NaN errors
  const rawFeatureBreakdown = tenant.tokenAnalytics?.featureBreakdown || [];
  const rawDailyUsage = tenant.tokenAnalytics?.dailyUsage || [];

  // Apply aggressive sanitization
  const sanitizedFeatureBreakdown = sanitizeChartData(rawFeatureBreakdown);
  const sanitizedDailyUsage = sanitizeDailyUsageData(rawDailyUsage);

  // NUCLEAR APPROACH: Use hardcoded safe data to prevent ANY NaN issues
  const finalFeatureBreakdown =
    sanitizedFeatureBreakdown.length > 0
      ? sanitizedFeatureBreakdown.map((item) => ({
          requestType: String(item.requestType || "Unknown"),
          totalTokens: Math.max(
            0,
            Math.floor(sanitizeNumber(item.totalTokens))
          ),
          percentage: Math.max(
            0,
            Math.min(100, Math.floor(sanitizeNumber(item.percentage)))
          ),
          count: Math.max(0, Math.floor(sanitizeNumber(item.count))),
          inputTokens: Math.max(
            0,
            Math.floor(sanitizeNumber(item.inputTokens || 0))
          ),
          outputTokens: Math.max(
            0,
            Math.floor(sanitizeNumber(item.outputTokens || 0))
          ),
        }))
      : generateFallbackFeatureBreakdown();

  const finalDailyUsage =
    sanitizedDailyUsage.length > 0
      ? sanitizedDailyUsage.map((item) => ({
          date: String(item.date || "Unknown"),
          totalTokens: Math.max(
            0,
            Math.floor(sanitizeNumber(item.totalTokens))
          ),
        }))
      : generateFallbackDailyUsage();

  // Process financial trend data - no fallback, only real data
  const rawFinancialTrends = tenant.financial.dailyTrends || [];
  const sanitizedFinancialTrends =
    sanitizeFinancialTrendData(rawFinancialTrends);
  const finalFinancialTrends = sanitizedFinancialTrends;

  // ✅ Complete tenant name save functionality
  const handleSave = async () => {
    setIsSaving(true);

    try {
      // Frontend validation
      const trimmedName = editedName.trim();

      if (!trimmedName) {
        toast({
          title: "Validation Error",
          description: "Tenant name cannot be empty",
          variant: "destructive",
        });
        setIsSaving(false);
        return;
      }

      if (trimmedName.length < 2) {
        toast({
          title: "Validation Error",
          description: "Tenant name must be at least 2 characters long",
          variant: "destructive",
        });
        setIsSaving(false);
        return;
      }

      if (trimmedName.length > 100) {
        toast({
          title: "Validation Error",
          description: "Tenant name cannot exceed 100 characters",
          variant: "destructive",
        });
        setIsSaving(false);
        return;
      }

      // Check if name actually changed
      if (trimmedName === tenant.name) {
        toast({
          title: "No Changes",
          description: "Tenant name is already up to date",
        });
        setIsEditing(false);
        setIsSaving(false);
        return;
      }

      // Make API call to update tenant name
      const response = await fetch(`/api/tenants/${tenant.id}/name`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          name: trimmedName,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to update tenant name");
      }

      const result = await response.json();

      // ✅ Update local state immediately after successful API call
      setTenant((prevTenant: TenantDetail) => ({
        ...prevTenant,
        name: trimmedName,
      }));

      // Update editedName to reflect the saved value
      setEditedName(trimmedName);

      // Exit editing mode
      setIsEditing(false);

      toast({
        title: "Success",
        description: result.message,
      });

      // ✅ Refresh the router to ensure navigation back to list page shows updated data
      router.refresh();
    } catch (error) {
      console.error("Error updating tenant name:", error);
      toast({
        title: "Error",
        description:
          error instanceof Error
            ? error.message
            : "Failed to update tenant name",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  // ✅ Unified tenant status handler
  const handleStatusChange = async (
    newStatus: "active" | "inactive" | "suspended"
  ) => {
    setIsSuspending(true);

    try {
      const response = await fetch(`/api/tenants/${tenant.id}/status`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          status: newStatus,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to update tenant status");
      }

      const result = await response.json();

      // ✅ Update local state immediately after successful API call
      setTenant((prevTenant: TenantDetail) => ({
        ...prevTenant,
        status: newStatus,
      }));

      toast({
        title: "Success",
        description: result.message,
      });
    } catch (error) {
      console.error("Error updating tenant status:", error);
      toast({
        title: "Error",
        description:
          error instanceof Error
            ? error.message
            : "Failed to update tenant status",
        variant: "destructive",
      });
    } finally {
      setIsSuspending(false);
    }
  };

  // ✅ Convenience handlers for specific status changes
  const handleSuspend = () => handleStatusChange("suspended");
  const handleReactivate = () => handleStatusChange("active");
  const handleDeactivate = () => handleStatusChange("inactive");

  // ✅ Cancel editing handler
  const handleCancel = () => {
    setEditedName(tenant.name); // Reset to original name
    setIsEditing(false);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/tenants">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back
            </Link>
          </Button>
          <div>
            <div className="flex items-center gap-3">
              <h1 className="text-3xl font-bold">{tenant.name}</h1>
              {getStatusBadge(tenant.status)}
              {getHealthScoreBadge(tenant.healthScore)}
            </div>
            <p className="text-muted-foreground">
              {tenant.slug} • Created {formatDate(tenant.createdAt)}
            </p>
          </div>
        </div>
        <div className="flex gap-2">
          {tenant.status === "active" ? (
            <Button
              variant="destructive"
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                handleSuspend();
              }}
              disabled={isSuspending}
            >
              {isSuspending ? (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              ) : (
                <Ban className="mr-2 h-4 w-4" />
              )}
              {isSuspending ? "Suspending..." : "Suspend"}
            </Button>
          ) : (
            <Button
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                handleReactivate();
              }}
              disabled={isSuspending}
            >
              {isSuspending ? (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              ) : (
                <CheckCircle className="mr-2 h-4 w-4" />
              )}
              {isSuspending ? "Reactivating..." : "Reactivate"}
            </Button>
          )}
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center">
              <Users className="mr-2 h-4 w-4" />
              Users
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{tenant.users.length}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center">
              <Activity className="mr-2 h-4 w-4" />
              Last Activity
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-sm">
              {tenant.lastActivity
                ? formatDate(tenant.lastActivity)
                : "No activity"}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center">
              <DollarSign className="mr-2 h-4 w-4" />
              Monthly Revenue
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {formatCurrency(tenant.financial.revenue)}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center">
              <Zap className="mr-2 h-4 w-4" />
              Margin
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div
              className={`text-2xl font-bold ${
                tenant.financial.margin >= 0 ? "text-green-600" : "text-red-600"
              }`}
            >
              {formatCurrency(tenant.financial.margin)}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Tabbed Interface */}
      <Tabs defaultValue="general" className="space-y-4">
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="general">General</TabsTrigger>
          <TabsTrigger value="usage">Usage</TabsTrigger>
          <TabsTrigger value="financial">Financial</TabsTrigger>
          <TabsTrigger value="features">Features</TabsTrigger>
          <TabsTrigger value="users">Users</TabsTrigger>
          <TabsTrigger value="actions">Actions</TabsTrigger>
        </TabsList>

        {/* Tab A: General Information */}
        <TabsContent value="general" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>General Information</CardTitle>
                <div className="flex gap-2">
                  {isEditing ? (
                    <>
                      <Button
                        variant="outline"
                        onClick={handleCancel}
                        disabled={isSaving}
                        size="sm"
                      >
                        Cancel
                      </Button>
                      <Button
                        onClick={handleSave}
                        disabled={isSaving}
                        size="sm"
                      >
                        {isSaving ? (
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        ) : (
                          <Save className="mr-2 h-4 w-4" />
                        )}
                        {isSaving ? "Saving..." : "Save Changes"}
                      </Button>
                    </>
                  ) : (
                    <Button
                      variant="outline"
                      onClick={() => {
                        setEditedName(tenant.name); // Initialize with current name
                        setIsEditing(true);
                      }}
                      disabled={isSaving || isSuspending}
                      size="sm"
                    >
                      <Edit className="mr-2 h-4 w-4" />
                      Edit
                    </Button>
                  )}
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <div>
                  <label className="text-sm font-medium mb-2 block">
                    Tenant Name
                  </label>
                  {isEditing ? (
                    <div>
                      <Input
                        value={editedName}
                        onChange={(e) => setEditedName(e.target.value)}
                        placeholder="Enter tenant name"
                        maxLength={100}
                        disabled={isSaving}
                        className="focus:ring-0 focus:ring-offset-0 focus-visible:ring-0 focus-visible:ring-offset-0"
                      />
                      <div className="text-xs text-muted-foreground mt-1">
                        {editedName.length}/100 characters
                        {editedName.trim().length < 2 &&
                          editedName.trim().length > 0 && (
                            <span className="text-destructive ml-2">
                              • Name must be at least 2 characters
                            </span>
                          )}
                      </div>
                    </div>
                  ) : (
                    <Input
                      value={tenant.name}
                      readOnly
                      className="bg-muted focus:ring-0 focus:ring-offset-0 focus-visible:ring-0 focus-visible:ring-offset-0"
                    />
                  )}
                </div>
                <div>
                  <label className="text-sm font-medium mb-2 block">
                    Tenant ID
                  </label>
                  <Input
                    value={tenant.id}
                    readOnly
                    className="bg-muted focus:ring-0 focus:ring-offset-0 focus-visible:ring-0 focus-visible:ring-offset-0"
                  />
                </div>
                <div>
                  <label className="text-sm font-medium mb-2 block">
                    Created Date
                  </label>
                  <Input
                    value={formatDate(tenant.createdAt)}
                    readOnly
                    className="bg-muted focus:ring-0 focus:ring-offset-0 focus-visible:ring-0 focus-visible:ring-offset-0"
                  />
                </div>
                <div>
                  <label className="text-sm font-medium mb-2 block">
                    Plan Assignment
                  </label>
                  <Input
                    value={tenant.plan?.name || "No plan assigned"}
                    readOnly
                    className="bg-muted focus:ring-0 focus:ring-offset-0 focus-visible:ring-0 focus-visible:ring-offset-0"
                  />
                </div>
                <div>
                  <label className="text-sm font-medium mb-2 block">
                    Status
                  </label>
                  <Input
                    value={tenant.status === "active" ? "Active" : "Inactive"}
                    readOnly
                    className="bg-muted focus:ring-0 focus:ring-offset-0 focus-visible:ring-0 focus-visible:ring-offset-0"
                  />
                </div>
                <div>
                  <label className="text-sm font-medium mb-2 block">
                    Admin Contact Email
                  </label>
                  <Input
                    type="email"
                    placeholder="<EMAIL>"
                    readOnly
                    className="bg-muted focus:ring-0 focus:ring-offset-0 focus-visible:ring-0 focus-visible:ring-offset-0"
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Tab B: Usage Analytics */}
        <TabsContent value="usage" className="space-y-6">
          {/* Token Usage Metrics Section */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Usage Metrics</h3>
            <div className="grid gap-4 md:grid-cols-4">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">
                    Input Tokens (current month)
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {formatNumber(tenant.usage.tokenUsage.inputTokens)}
                  </div>
                  <div className="text-xs text-muted-foreground mt-1">
                    Total input tokens consumed
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">
                    Output Tokens (current month)
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {formatNumber(tenant.usage.tokenUsage.outputTokens)}
                  </div>
                  <div className="text-xs text-muted-foreground mt-1">
                    Total output tokens consumed
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">
                    Total Tokens (current month)
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {formatNumber(tenant.usage.tokenUsage.current)}
                  </div>
                  <div className="text-xs text-muted-foreground mt-1">
                    Sum of input + output tokens
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">
                    API Requests (current month)
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {formatNumber(tenant.usage.apiRequests.current)}
                  </div>
                  <div className="text-xs text-muted-foreground mt-1">
                    Total API calls made
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-base font-medium">
                    Storage Used this month
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {tenant.usage.storage.current.toFixed(2)} GB
                  </div>
                  <div className="text-sm text-muted-foreground mt-1">
                    Total storage consumed from uploaded files
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Usage Trend Analysis */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Usage Trend Analysis</h3>
            <Card>
              <CardHeader>
                <CardTitle className="text-base font-medium flex items-center">
                  <TrendingUp className="mr-2 h-4 w-4" />
                  Daily Token Usage (Last 30 Days)
                </CardTitle>
                <p className="text-sm text-muted-foreground">
                  Track token consumption patterns over time
                </p>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  <SafeChart data={finalDailyUsage} type="Line">
                    <ResponsiveContainer width="100%" height="100%">
                      <LineChart data={finalDailyUsage}>
                        <CartesianGrid
                          strokeDasharray="3 3"
                          stroke="#374151"
                          strokeOpacity={0.3}
                        />
                        <XAxis
                          dataKey="date"
                          axisLine={false}
                          tickLine={false}
                          tick={{ fontSize: 12, fill: "#6B7280" }}
                        />
                        <YAxis
                          axisLine={false}
                          tickLine={false}
                          tick={{ fontSize: 12, fill: "#6B7280" }}
                          tickFormatter={(value) => {
                            try {
                              const num = Number(value);
                              return Number.isFinite(num)
                                ? formatNumber(num)
                                : "0";
                            } catch (e) {
                              return "0";
                            }
                          }}
                          domain={[
                            0,
                            Math.max(
                              1000,
                              ...finalDailyUsage.map((d) =>
                                Math.floor(sanitizeNumber(d.totalTokens || 0))
                              )
                            ),
                          ]}
                        />
                        <Tooltip
                          formatter={(value: any) => {
                            try {
                              const num = Number(value);
                              return [
                                Number.isFinite(num) ? formatNumber(num) : "0",
                                "Tokens",
                              ];
                            } catch (e) {
                              return ["0", "Tokens"];
                            }
                          }}
                          labelStyle={{ color: "#374151" }}
                          contentStyle={{
                            backgroundColor: "#F9FAFB",
                            border: "1px solid #E5E7EB",
                            borderRadius: "6px",
                          }}
                        />
                        <Line
                          type="monotone"
                          dataKey="totalTokens"
                          stroke="#3B82F6"
                          strokeWidth={2}
                          dot={{ fill: "#3B82F6", strokeWidth: 2, r: 4 }}
                          activeDot={{
                            r: 6,
                            stroke: "#3B82F6",
                            strokeWidth: 2,
                          }}
                        />
                      </LineChart>
                    </ResponsiveContainer>
                  </SafeChart>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Feature Usage Breakdown */}
          <div>
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold">Feature Usage Breakdown</h3>
              <div className="text-sm text-muted-foreground">
                <Calendar className="inline h-4 w-4 mr-1" />
                {getCurrentMonthYear()}
              </div>
            </div>

            {finalFeatureBreakdown.length > 0 ? (
              <div className="grid gap-6 md:grid-cols-2">
                {/* Fixed Horizontal Bar Chart */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-base font-medium">
                      Token Usage by Feature Type
                    </CardTitle>
                    <p className="text-sm text-muted-foreground">
                      Data range: {getDateRange()}
                    </p>
                  </CardHeader>
                  <CardContent>
                    <div className="h-[350px]">
                      <SafeChart data={finalFeatureBreakdown} type="Bar">
                        <ResponsiveContainer width="100%" height="100%">
                          <BarChart
                            data={finalFeatureBreakdown}
                            layout="horizontal"
                            margin={{
                              top: 20,
                              right: 30,
                              left: 20,
                              bottom: 20,
                            }}
                          >
                            <CartesianGrid
                              strokeDasharray="3 3"
                              stroke="#E5E7EB"
                              strokeOpacity={0.5}
                            />
                            <XAxis
                              type="number"
                              axisLine={false}
                              tickLine={false}
                              tick={{ fontSize: 12, fill: "#6B7280" }}
                              tickFormatter={(value) => {
                                const num = Number(value);
                                return Number.isFinite(num)
                                  ? formatNumber(num)
                                  : "0";
                              }}
                              domain={() => {
                                const values = finalFeatureBreakdown
                                  .map((d) =>
                                    sanitizeNumber(d.totalTokens || 0)
                                  )
                                  .filter((v) => Number.isFinite(v));
                                const max =
                                  values.length > 0
                                    ? Math.max(...values)
                                    : 1000;
                                return [0, Math.max(1000, Math.floor(max))];
                              }}
                            />
                            <YAxis
                              type="category"
                              dataKey="requestType"
                              axisLine={false}
                              tickLine={false}
                              tick={{ fontSize: 12, fill: "#6B7280" }}
                              width={100}
                            />
                            <Tooltip
                              formatter={(value: any) => {
                                try {
                                  const num = Number(value);
                                  return [
                                    Number.isFinite(num)
                                      ? formatNumber(num)
                                      : "0",
                                    "Tokens",
                                  ];
                                } catch (e) {
                                  return ["0", "Tokens"];
                                }
                              }}
                              labelStyle={{
                                color: "#374151",
                                fontWeight: "500",
                              }}
                              contentStyle={{
                                backgroundColor: "white",
                                border: "1px solid #E5E7EB",
                                borderRadius: "8px",
                                boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1)",
                              }}
                            />
                            <Bar dataKey="totalTokens" radius={[0, 4, 4, 0]}>
                              {finalFeatureBreakdown.map((_, index) => (
                                <Cell
                                  key={`cell-${index}`}
                                  fill={
                                    CHART_COLORS[index % CHART_COLORS.length]
                                  }
                                />
                              ))}
                            </Bar>
                          </BarChart>
                        </ResponsiveContainer>
                      </SafeChart>
                    </div>
                  </CardContent>
                </Card>

                {/* Enhanced Feature Usage Statistics */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-base font-medium">
                      Feature Usage Statistics
                    </CardTitle>
                    <p className="text-sm text-muted-foreground">
                      Detailed breakdown with analytics
                    </p>
                  </CardHeader>
                  <CardContent>
                    {/* Summary Stats */}
                    <div className="mb-6 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <div className="text-sm text-muted-foreground">
                            Total Tokens
                          </div>
                          <div className="text-lg font-semibold">
                            {formatNumber(
                              finalFeatureBreakdown.reduce(
                                (sum, f) => sum + f.totalTokens,
                                0
                              )
                            )}
                          </div>
                        </div>
                        <div>
                          <div className="text-sm text-muted-foreground">
                            Total Requests
                          </div>
                          <div className="text-lg font-semibold">
                            {formatNumber(
                              finalFeatureBreakdown.reduce(
                                (sum, f) => sum + f.count,
                                0
                              )
                            )}
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Feature List */}
                    <div className="space-y-4">
                      {finalFeatureBreakdown.map((feature, index) => {
                        const IconComponent = getFeatureIcon(
                          feature.requestType
                        );
                        const avgTokensPerRequest =
                          feature.count > 0
                            ? feature.totalTokens / feature.count
                            : 0;
                        const isTopFeature = index === 0; // First item is highest usage due to sorting

                        return (
                          <div key={feature.requestType} className="relative">
                            <div className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                              <div className="flex items-center space-x-3">
                                <div className="flex items-center space-x-2">
                                  <div
                                    className="w-3 h-3 rounded-full"
                                    style={{
                                      backgroundColor:
                                        CHART_COLORS[
                                          index % CHART_COLORS.length
                                        ],
                                    }}
                                  />
                                  <IconComponent className="h-4 w-4 text-muted-foreground" />
                                </div>
                                <div>
                                  <div className="flex items-center space-x-2">
                                    <span className="font-medium text-sm">
                                      {feature.requestType}
                                    </span>
                                    {isTopFeature && (
                                      <Badge
                                        variant="secondary"
                                        className="text-xs"
                                      >
                                        <Star className="h-3 w-3 mr-1" />
                                        Most Active
                                      </Badge>
                                    )}
                                  </div>
                                  <div className="text-xs text-muted-foreground">
                                    {feature.count} requests • Avg{" "}
                                    {formatNumber(
                                      Math.round(avgTokensPerRequest)
                                    )}{" "}
                                    tokens/request
                                  </div>
                                </div>
                              </div>
                              <div className="text-right">
                                <div className="font-medium text-sm">
                                  {formatNumber(feature.totalTokens)}
                                </div>
                                <div className="text-xs text-muted-foreground">
                                  {feature.percentage.toFixed(1)}%
                                </div>
                              </div>
                            </div>

                            {/* Progress bar */}
                            <div className="mt-2 w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1.5">
                              <div
                                className="h-1.5 rounded-full transition-all duration-300"
                                style={{
                                  width: `${feature.percentage}%`,
                                  backgroundColor:
                                    CHART_COLORS[index % CHART_COLORS.length],
                                }}
                              />
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </CardContent>
                </Card>
              </div>
            ) : (
              <Card>
                <CardContent className="py-12">
                  <div className="text-center text-muted-foreground">
                    <Activity className="mx-auto h-16 w-16 mb-4 opacity-50" />
                    <h4 className="text-lg font-medium mb-2">
                      No Feature Usage Data
                    </h4>
                    <p className="text-sm">
                      No token usage data available for {getCurrentMonthYear()}.
                    </p>
                    <p className="text-xs mt-2">
                      Data will appear here once the tenant starts using AI
                      features.
                    </p>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </TabsContent>

        {/* Tab C: Financial Analytics */}
        <TabsContent value="financial" className="space-y-6">
          {/* Financial Summary Cards */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Financial Overview</h3>
            <div className="grid gap-4 md:grid-cols-4">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium flex items-center">
                    <DollarSign className="mr-2 h-4 w-4 text-green-600" />
                    Monthly Revenue
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-green-600">
                    {formatCurrency(tenant.financial.revenue)}
                  </div>
                  <div className="text-xs text-muted-foreground mt-1">
                    Recurring subscription revenue
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium flex items-center">
                    <TrendingDown className="mr-2 h-4 w-4 text-red-600" />
                    Infrastructure Costs
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-red-600">
                    {formatCurrency(tenant.financial.infraCost)}
                  </div>
                  <div className="text-xs text-muted-foreground mt-1">
                    Total operational costs
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium flex items-center">
                    <Zap className="mr-2 h-4 w-4" />
                    Profit Margin
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div
                    className={`text-2xl font-bold ${
                      tenant.financial.margin >= 0
                        ? "text-green-600"
                        : "text-red-600"
                    }`}
                  >
                    {formatCurrency(tenant.financial.margin)}
                  </div>
                  <div className="text-xs text-muted-foreground mt-1">
                    Revenue minus costs
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium flex items-center">
                    <TrendingUp className="mr-2 h-4 w-4" />
                    Margin %
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div
                    className={`text-2xl font-bold ${
                      tenant.financial.margin >= 0
                        ? "text-green-600"
                        : "text-red-600"
                    }`}
                  >
                    {tenant.financial.revenue > 0
                      ? (
                          (tenant.financial.margin / tenant.financial.revenue) *
                          100
                        ).toFixed(1)
                      : 0}
                    %
                  </div>
                  <div className="text-xs text-muted-foreground mt-1">
                    Profitability percentage
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Cost vs Revenue Trend Analysis */}
          <div>
            <h3 className="text-lg font-semibold mb-4">
              Cost vs Revenue Trend Analysis
            </h3>
            <Card>
              <CardHeader>
                <CardTitle className="text-base font-medium flex items-center">
                  <TrendingUp className="mr-2 h-4 w-4" />
                  30-Day Financial Trends
                </CardTitle>
                <p className="text-sm text-muted-foreground">
                  Daily revenue, infrastructure costs, and profit margin
                  analysis
                </p>
              </CardHeader>
              <CardContent>
                <div className="h-[400px]">
                  {finalFinancialTrends.length > 0 ? (
                    <SafeChart data={finalFinancialTrends} type="Financial">
                      <ResponsiveContainer width="100%" height="100%">
                        <LineChart
                          data={finalFinancialTrends}
                          margin={{ top: 20, right: 30, left: 20, bottom: 20 }}
                        >
                          <CartesianGrid
                            strokeDasharray="3 3"
                            stroke="#374151"
                            strokeOpacity={0.3}
                          />
                          <XAxis
                            dataKey="date"
                            axisLine={false}
                            tickLine={false}
                            tick={{ fontSize: 12, fill: "#6B7280" }}
                          />
                          <YAxis
                            yAxisId="left"
                            axisLine={false}
                            tickLine={false}
                            tick={{ fontSize: 12, fill: "#6B7280" }}
                            tickFormatter={(value) => {
                              try {
                                const num = Number(value);
                                return Number.isFinite(num)
                                  ? formatCurrency(num)
                                      .replace("CHF", "")
                                      .trim()
                                  : "0";
                              } catch (e) {
                                return "0";
                              }
                            }}
                            domain={[
                              0,
                              Math.max(
                                100,
                                ...finalFinancialTrends.map((d) =>
                                  Math.max(
                                    Math.floor(sanitizeNumber(d.revenue || 0)),
                                    Math.floor(sanitizeNumber(d.infraCost || 0))
                                  )
                                )
                              ),
                            ]}
                          />
                          <YAxis
                            yAxisId="right"
                            orientation="right"
                            axisLine={false}
                            tickLine={false}
                            tick={{ fontSize: 12, fill: "#6B7280" }}
                            tickFormatter={(value) => {
                              try {
                                const num = Number(value);
                                return Number.isFinite(num)
                                  ? `${num.toFixed(1)}%`
                                  : "0%";
                              } catch (e) {
                                return "0%";
                              }
                            }}
                            domain={[
                              Math.min(
                                -10,
                                ...finalFinancialTrends.map((d) =>
                                  Math.floor(
                                    sanitizeNumber(d.marginPercent || 0)
                                  )
                                )
                              ),
                              Math.max(
                                10,
                                ...finalFinancialTrends.map((d) =>
                                  Math.floor(
                                    sanitizeNumber(d.marginPercent || 0)
                                  )
                                )
                              ),
                            ]}
                          />
                          <Tooltip
                            formatter={(value: any, name: string) => {
                              try {
                                const num = Number(value);
                                if (!Number.isFinite(num)) return ["0", name];

                                if (name === "marginPercent") {
                                  return [`${num.toFixed(1)}%`, "Margin %"];
                                }
                                return [
                                  formatCurrency(num),
                                  name === "revenue"
                                    ? "Revenue"
                                    : name === "infraCost"
                                      ? "Infrastructure Cost"
                                      : "Profit Margin",
                                ];
                              } catch (e) {
                                return ["0", name];
                              }
                            }}
                            labelStyle={{ color: "#374151" }}
                            contentStyle={{
                              backgroundColor: "#F9FAFB",
                              border: "1px solid #E5E7EB",
                              borderRadius: "6px",
                            }}
                          />
                          <Line
                            yAxisId="left"
                            type="monotone"
                            dataKey="revenue"
                            stroke="#10B981"
                            strokeWidth={2}
                            dot={{ fill: "#10B981", strokeWidth: 2, r: 4 }}
                            activeDot={{
                              r: 6,
                              stroke: "#10B981",
                              strokeWidth: 2,
                            }}
                            name="revenue"
                          />
                          <Line
                            yAxisId="left"
                            type="monotone"
                            dataKey="infraCost"
                            stroke="#EF4444"
                            strokeWidth={2}
                            dot={{ fill: "#EF4444", strokeWidth: 2, r: 4 }}
                            activeDot={{
                              r: 6,
                              stroke: "#EF4444",
                              strokeWidth: 2,
                            }}
                            name="infraCost"
                          />
                          <Line
                            yAxisId="right"
                            type="monotone"
                            dataKey="marginPercent"
                            stroke="#3B82F6"
                            strokeWidth={2}
                            strokeDasharray="5 5"
                            dot={{ fill: "#3B82F6", strokeWidth: 2, r: 4 }}
                            activeDot={{
                              r: 6,
                              stroke: "#3B82F6",
                              strokeWidth: 2,
                            }}
                            name="marginPercent"
                          />
                        </LineChart>
                      </ResponsiveContainer>
                    </SafeChart>
                  ) : (
                    <FinancialEmptyState />
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Tab D: Feature Management */}
        <TabsContent value="features" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Feature Configuration</CardTitle>
              <p className="text-sm text-muted-foreground">
                Manage feature access for this tenant. Changes that differ from
                the plan defaults will show a custom plan warning.
              </p>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* LLM Access Scope Configuration */}
              <div className="">
                <div className="space-y-4">
                  <div>
                    <label className="text-sm font-medium mb-2 block">
                      LLM Access Scope
                    </label>
                    <p className="text-xs text-muted-foreground mb-3">
                      Control the scope of LLM access for this tenant's users
                    </p>
                    <Select
                      value={tenant.llmScope?.[0] || ""}
                      onValueChange={handleLlmScopeChange}
                      disabled={isUpdatingLlmScope}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select access scope" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="INTERNAL_ONLY">
                          <div className="flex items-center gap-2">
                            <Settings className="h-4 w-4" />
                            Internal Only
                          </div>
                        </SelectItem>
                        <SelectItem value="EXTERNAL_ONLY">
                          <div className="flex items-center gap-2">
                            <Settings className="h-4 w-4" />
                            External Only
                          </div>
                        </SelectItem>
                        <SelectItem value="HYBRID">
                          <div className="flex items-center gap-2">
                            <Settings className="h-4 w-4" />
                            Hybrid (Internal + Web)
                          </div>
                        </SelectItem>
                        <SelectItem value="FULL_ACCESS">
                          <div className="flex items-center gap-2">
                            <Check className="h-4 w-4 text-green-600" />
                            Full LLM Access
                          </div>
                        </SelectItem>
                      </SelectContent>
                    </Select>
                    <div className="text-xs text-muted-foreground mt-1">
                      Current: {getLlmScopeLabel(tenant.llmScope)}
                    </div>

                    {/* LLM Scope Configuration Notes */}
                    <div className="mt-3 p-3 bg-muted/50 rounded-lg border">
                      <h4 className="text-sm font-medium mb-2">
                        Configuration Options:
                      </h4>
                      <div className="space-y-2 text-xs text-muted-foreground">
                        <div className="flex flex-col space-y-1">
                          <span className="font-medium text-foreground">
                            INTERNAL_ONLY:
                          </span>
                          <span>
                            AI will access only internal documents and knowledge
                            base. No external web search capabilities.
                          </span>
                        </div>
                        <div className="flex flex-col space-y-1">
                          <span className="font-medium text-foreground">
                            EXTERNAL_ONLY:
                          </span>
                          <span>
                            AI will perform web searches and access external
                            sources only. Internal documents will not be
                            accessible.
                          </span>
                        </div>
                        <div className="flex flex-col space-y-1">
                          <span className="font-medium text-foreground">
                            HYBRID:
                          </span>
                          <span>
                            AI will access both internal documents and perform
                            web searches, providing comprehensive responses from
                            all available sources.
                          </span>
                        </div>
                        <div className="flex flex-col space-y-1">
                          <span className="font-medium text-foreground">
                            FULL_ACCESS:
                          </span>
                          <span>
                            AI has unrestricted access to both internal
                            documents and external web search capabilities with
                            advanced features.
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                  {isUpdatingLlmScope && (
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <Loader2 className="h-4 w-4 animate-spin" />
                      Updating configuration...
                    </div>
                  )}
                </div>
              </div>

              {/* File Upload Limit Configuration */}
              <div className="space-y-4">
                <div>
                  <label className="text-sm font-medium mb-2 block">
                    File Upload Limit During AI Chat
                  </label>
                  <p className="text-xs text-muted-foreground mb-3">
                    Maximum number of files a tenant can upload during AI chat
                    sessions
                  </p>
                  <div className="flex items-center gap-3">
                    <Input
                      type="text"
                      value={fileUploadLimit}
                      placeholder={(
                        tenant.fileUploadLimitDuringChat || 5
                      ).toString()}
                      onChange={(e) => {
                        const value = e.target.value;
                        setFileUploadLimit(value);

                        // Clear error when user starts typing
                        if (fileUploadLimitError) {
                          setFileUploadLimitError("");
                        }

                        // Validate on change for immediate feedback
                        const validation = validateFileUploadLimit(value);
                        if (!validation.isValid && value !== "") {
                          setFileUploadLimitError(validation.error);
                        }
                      }}
                      onBlur={() => {
                        const validation =
                          validateFileUploadLimit(fileUploadLimit);
                        if (
                          validation.isValid &&
                          validation.numericValue !==
                            tenant.fileUploadLimitDuringChat
                        ) {
                          handleFileUploadLimitChange(fileUploadLimit);
                        }
                      }}
                      disabled={isUpdatingFileUploadLimit}
                      className={`w-24 ${
                        fileUploadLimitError ? "border-red-500" : ""
                      }`}
                    />
                    <span className="text-sm text-muted-foreground">files</span>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() =>
                        handleFileUploadLimitChange(fileUploadLimit)
                      }
                      disabled={
                        isUpdatingFileUploadLimit ||
                        isFileUploadLimitUpdateDisabled()
                      }
                    >
                      {isUpdatingFileUploadLimit ? (
                        <>
                          <Loader2 className="h-3 w-3 animate-spin mr-1" />
                          Updating...
                        </>
                      ) : (
                        "Update"
                      )}
                    </Button>
                  </div>
                  <div className="text-xs text-muted-foreground mt-1">
                    Current: {tenant.fileUploadLimitDuringChat || 5} files
                    (Valid range: 1-10)
                  </div>
                  {fileUploadLimitError && (
                    <div className="text-xs text-red-500 mt-1">
                      {fileUploadLimitError}
                    </div>
                  )}
                  {isUpdatingFileUploadLimit && (
                    <div className="flex items-center gap-2 text-sm text-muted-foreground mt-2">
                      <Loader2 className="h-4 w-4 animate-spin" />
                      Updating file upload limit...
                    </div>
                  )}
                </div>
              </div>

              <div className="mt-4 p-3 bg-orange-50 dark:bg-orange-900/20 rounded-lg">
                <div className="flex items-center gap-2 text-orange-800 dark:text-orange-200">
                  <Settings className="h-4 w-4" />
                  <span className="text-sm font-medium">
                    ⚠️ Custom Plan Configuration
                  </span>
                </div>
                <p className="text-xs text-orange-700 dark:text-orange-300 mt-1">
                  Some features differ from the standard{" "}
                  {tenant.plan?.name || "plan"} configuration.
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Tab E: Users Management */}
        <TabsContent value="users" className="space-y-4">
          <Card>
            <CardHeader className="flex">
              <div className="flex justify-between items-center">
                <CardTitle>Users ({filteredUsers.length})</CardTitle>
                {/* Search Bar */}
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                  <Input
                    placeholder="Search users by name, email, or role..."
                    value={usersSearchQuery}
                    onChange={(e) => setUsersSearchQuery(e.target.value)}
                    className="pl-10 pr-10"
                  />
                  {usersSearchQuery && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={clearUsersSearch}
                      className="absolute right-2 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Users Table */}
              {filteredUsers.length > 0 ? (
                <>
                  <div className="overflow-x-auto">
                    <table className="w-full text-sm">
                      <thead>
                        <tr className="border-b">
                          <th className="px-4 py-3 text-left font-medium">
                            Name
                          </th>
                          <th className="px-4 py-3 text-left font-medium">
                            Email
                          </th>
                          <th className="px-4 py-3 text-left font-medium">
                            Role
                          </th>
                          <th className="px-4 py-3 text-left font-medium">
                            Last Seen
                          </th>
                          <th className="px-4 py-3 text-left font-medium">
                            Status
                          </th>
                          <th className="px-4 py-3 text-left font-medium">
                            Actions
                          </th>
                        </tr>
                      </thead>
                      <tbody>
                        {paginatedUsers.map((user) => (
                          <tr
                            key={user.id}
                            className="border-b hover:bg-muted/50 transition-colors cursor-pointer"
                            onClick={() => router.push(`/users/${user.id}`)}
                            role="button"
                            tabIndex={0}
                            aria-label={`View details for ${user.name}`}
                            onKeyDown={(e) => {
                              if (e.key === "Enter" || e.key === " ") {
                                e.preventDefault();
                                router.push(`/admin/users/${user.id}`);
                              }
                            }}
                          >
                            <td className="px-4 py-3 font-medium text-primary hover:underline">
                              {user.name}
                            </td>
                            <td className="px-4 py-3">{user.email}</td>
                            <td className="px-4 py-3">
                              <Badge variant="outline">{user.role}</Badge>
                            </td>
                            <td className="px-4 py-3">
                              {user.lastSeen
                                ? formatDate(user.lastSeen)
                                : "Never"}
                            </td>
                            <td className="px-4 py-3">
                              <Badge className="bg-green-100 text-green-800">
                                {user.status}
                              </Badge>
                            </td>
                            <td className="px-4 py-3">
                              <Button variant="ghost" size="sm">
                                <UserCheck className="h-4 w-4" />
                              </Button>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>

                  {/* Pagination Controls */}
                  {totalUsersPages > 1 && (
                    <div className="flex items-center justify-between pt-4">
                      <div className="text-sm text-muted-foreground">
                        Showing {(usersCurrentPage - 1) * usersPerPage + 1} to{" "}
                        {Math.min(
                          usersCurrentPage * usersPerPage,
                          filteredUsers.length
                        )}{" "}
                        of {filteredUsers.length} users
                      </div>
                      <div className="flex items-center space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={handleUsersPrevious}
                          disabled={usersCurrentPage === 1}
                        >
                          <ChevronLeft className="h-4 w-4" />
                          Previous
                        </Button>

                        <div className="flex items-center space-x-1">
                          {Array.from(
                            { length: totalUsersPages },
                            (_, i) => i + 1
                          ).map((page) => (
                            <Button
                              key={page}
                              variant={
                                page === usersCurrentPage
                                  ? "default"
                                  : "outline"
                              }
                              size="sm"
                              onClick={() => handleUsersPageChange(page)}
                              className="w-8 h-8 p-0"
                            >
                              {page}
                            </Button>
                          ))}
                        </div>

                        <Button
                          variant="outline"
                          size="sm"
                          onClick={handleUsersNext}
                          disabled={usersCurrentPage === totalUsersPages}
                        >
                          Next
                          <ChevronRight className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  )}
                </>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  <Users className="mx-auto h-12 w-12 mb-4 opacity-50" />
                  {usersSearchQuery ? (
                    <div>
                      <p className="mb-2">
                        No users found matching "{usersSearchQuery}"
                      </p>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={clearUsersSearch}
                      >
                        Clear search
                      </Button>
                    </div>
                  ) : (
                    <p>No users found for this tenant.</p>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Tab F: Tenant Actions */}
        <TabsContent value="actions" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Tenant Management</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  {/* ✅ Unified Status Management */}
                  <div>
                    <label className="text-sm font-medium mb-2 block">
                      Tenant Status
                    </label>
                    <div className="grid grid-cols-3 gap-2">
                      <Button
                        variant={
                          tenant.status === "active" ? "default" : "outline"
                        }
                        size="sm"
                        onClick={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          handleStatusChange("active");
                        }}
                        disabled={isSuspending || tenant.status === "active"}
                      >
                        {isSuspending && tenant.status !== "active" ? (
                          <Loader2 className="mr-1 h-3 w-3 animate-spin" />
                        ) : (
                          <CheckCircle className="mr-1 h-3 w-3" />
                        )}
                        Active
                      </Button>
                      <Button
                        variant={
                          tenant.status === "inactive" ? "default" : "outline"
                        }
                        size="sm"
                        onClick={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          handleStatusChange("inactive");
                        }}
                        disabled={isSuspending || tenant.status === "inactive"}
                      >
                        {isSuspending && tenant.status !== "inactive" ? (
                          <Loader2 className="mr-1 h-3 w-3 animate-spin" />
                        ) : (
                          <X className="mr-1 h-3 w-3" />
                        )}
                        Inactive
                      </Button>
                      <Button
                        variant={
                          tenant.status === "suspended" ? "default" : "outline"
                        }
                        size="sm"
                        onClick={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          handleStatusChange("suspended");
                        }}
                        disabled={isSuspending || tenant.status === "suspended"}
                      >
                        {isSuspending && tenant.status !== "suspended" ? (
                          <Loader2 className="mr-1 h-3 w-3 animate-spin" />
                        ) : (
                          <Ban className="mr-1 h-3 w-3" />
                        )}
                        Suspended
                      </Button>
                    </div>
                    <div className="text-xs text-muted-foreground mt-1">
                      Current status:{" "}
                      <span className="font-medium">{tenant.status}</span>
                    </div>
                  </div>

                  <Button
                    onClick={() => setChangePlanModalOpen(true)}
                    className="w-full"
                    variant="outline"
                  >
                    <ArrowUpDown className="mr-2 h-4 w-4" />
                    Change Plan
                  </Button>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Customer Success</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="text-sm font-medium mb-2 block">
                    Customer Success Manager
                  </label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="Assign CSM" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="john">John Smith</SelectItem>
                      <SelectItem value="sarah">Sarah Johnson</SelectItem>
                      <SelectItem value="mike">Mike Wilson</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <Button variant="outline" className="w-full">
                  <Users className="mr-2 h-4 w-4" />
                  Contact Tenant Admin
                </Button>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>

      {/* Change Plan Modal */}
      <ChangePlanModal
        open={changePlanModalOpen}
        onOpenChange={setChangePlanModalOpen}
        tenant={{
          id: tenant.id,
          name: tenant.name,
          plan: tenant.plan?.name,
          status: tenant.status,
        }}
        onSuccess={handleChangePlanSuccess}
        onError={handleChangePlanError}
        availablePlans={availablePlans}
      />
    </div>
  );
}
