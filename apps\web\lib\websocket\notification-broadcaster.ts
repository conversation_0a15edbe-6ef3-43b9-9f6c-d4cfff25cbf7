/**
 * WebSocket notification broadcaster for real-time notifications
 */

interface NotificationData {
  type: "MENTION" | "COMMENT_REPLY" | "THREAD_SHARED";
  title: string;
  content: string;
  userId: string;
  triggeredBy: string;
  chatId?: string;
  messageId?: string;
  commentId?: string;
  tenantId: string;
  metadata?: any;
}

interface ThreadUpdateData {
  type: "THREAD_SHARED" | "THREAD_UPDATED" | "COMMENT_ADDED";
  chatId: string;
  tenantId: string;
  shareToken?: string;
  metadata?: any;
}

interface ThemeUpdateData {
  type: "THEME_UPDATED";
  tenantId: string;
  themeConfig: any;
  updatedBy: string;
  timestamp: string;
}

/**
 * Broadcast a notification via WebSocket to the FastAPI backend
 */
export async function broadcastNotification(notificationData: NotificationData): Promise<void> {
  try {
    const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || "http://localhost:8000";
    
    // Send notification to FastAPI WebSocket broadcaster
    const response = await fetch(`${API_BASE_URL}/api/v1/notifications/broadcast`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        type: "notification",
        data: notificationData,
      }),
    });

    if (!response.ok) {
      console.error("Failed to broadcast notification:", response.statusText);
    }
  } catch (error) {
    console.error("Error broadcasting notification:", error);
    // Don't throw error to avoid breaking the main flow
  }
}

/**
 * Broadcast a thread update via WebSocket
 */
export async function broadcastThreadUpdate(threadData: ThreadUpdateData): Promise<void> {
  try {
    const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || "http://localhost:8000";
    
    // Send thread update to FastAPI WebSocket broadcaster
    const response = await fetch(`${API_BASE_URL}/api/v1/notifications/broadcast`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        type: "thread_update",
        data: threadData,
      }),
    });

    if (!response.ok) {
      console.error("Failed to broadcast thread update:", response.statusText);
    }
  } catch (error) {
    console.error("Error broadcasting thread update:", error);
    // Don't throw error to avoid breaking the main flow
  }
}

/**
 * Broadcast multiple mention notifications
 */
export async function broadcastMentionNotifications(
  mentions: string[],
  triggerUser: { id: string; name: string },
  context: {
    chatId: string;
    messageId: string;
    commentId: string;
    tenantId: string;
  }
): Promise<void> {
  const notifications = mentions.map((userId) => ({
    type: "MENTION" as const,
    title: "You were mentioned in a comment",
    content: `${triggerUser.name} mentioned you in a comment`,
    userId,
    triggeredBy: triggerUser.id,
    chatId: context.chatId,
    messageId: context.messageId,
    commentId: context.commentId,
    tenantId: context.tenantId,
  }));

  // Broadcast each notification
  await Promise.all(
    notifications.map((notification) => broadcastNotification(notification))
  );
}

/**
 * Broadcast a comment reply notification
 */
export async function broadcastCommentReplyNotification(
  parentAuthorId: string,
  triggerUser: { id: string; name: string },
  context: {
    chatId: string;
    messageId: string;
    commentId: string;
    tenantId: string;
  }
): Promise<void> {
  const notification: NotificationData = {
    type: "COMMENT_REPLY",
    title: "Someone replied to your comment",
    content: `${triggerUser.name} replied to your comment`,
    userId: parentAuthorId,
    triggeredBy: triggerUser.id,
    chatId: context.chatId,
    messageId: context.messageId,
    commentId: context.commentId,
    tenantId: context.tenantId,
  };

  await broadcastNotification(notification);
}

/**
 * Broadcast a theme update via WebSocket
 */
export async function broadcastThemeUpdate(themeData: ThemeUpdateData): Promise<void> {
  try {
    const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || "http://localhost:8000";

    // Send theme update to FastAPI WebSocket broadcaster
    const response = await fetch(`${API_BASE_URL}/api/v1/notifications/broadcast`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        type: "theme_update",
        data: themeData,
      }),
    });

    if (!response.ok) {
      console.error("Failed to broadcast theme update:", response.statusText);
    }
  } catch (error) {
    console.error("Error broadcasting theme update:", error);
    // Don't throw error to avoid breaking the main flow
  }
}
