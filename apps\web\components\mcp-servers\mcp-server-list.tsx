"use client";

import { useState } from "react";
import { MCPServer } from "@/types/mcp-server";
import { useLanguage } from "@/lib/language-context";
import { MCPServerStatusBadge } from "./mcp-server-status";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  MoreVertical,
  TestTube,
  Edit,
  Trash2,
  Eye,
  Globe,
  Lock,
  Clock,
  Terminal,
  Wrench,
} from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import { de, enUS } from "date-fns/locale";

interface MCPServerListProps {
  servers: MCPServer[];
  onEdit: (server: MCPServer) => void;
  onDelete: (serverId: string) => void;
  onTest: (serverId: string) => void;
  onViewTools: (serverId: string) => void;
  onToggleStatus: (serverId: string) => void;
  loading?: boolean;
}

export function MCPServerList({
  servers,
  onEdit,
  onDelete,
  onTest,
  onViewTools,
  onToggleStatus,
  loading = false,
}: MCPServerListProps) {
  const { t, language } = useLanguage();
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [serverToDelete, setServerToDelete] = useState<MCPServer | null>(null);

  const handleDeleteClick = (server: MCPServer) => {
    setServerToDelete(server);
    setDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = () => {
    if (serverToDelete) {
      onDelete(serverToDelete.id);
      setDeleteDialogOpen(false);
      setServerToDelete(null);
    }
  };

  const getActionButtons = (server: MCPServer) => {
    const canTest = server.status !== "TESTING";

    return (
      <div className="flex items-center gap-2">
        {canTest && (
          <Button
            size="sm"
            variant="outline"
            onClick={() => onTest(server.id)}
            className="flex items-center gap-1"
          >
            <TestTube className="h-3 w-3" />
            <span className="hidden sm:inline">
              {t("mcpServers.testConnection")}
            </span>
            <span className="sm:hidden">{t("mcpServers.testConnection")}</span>
          </Button>
        )}
      </div>
    );
  };

  if (loading) {
    return (
      <div className="space-y-4">
        {[1, 2, 3].map((i) => (
          <Card key={i} className="animate-pulse">
            <CardContent className="p-6">
              <div className="space-y-3">
                <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                <div className="h-3 bg-gray-200 rounded w-3/4"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (servers.length === 0) {
    return (
      <Card>
        <CardContent className="p-12 text-center">
          <Terminal className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            {t("mcpServers.noServersConfigured")}
          </h3>
          <p className="text-muted-foreground mb-4">
            {t("mcpServers.noServersDescription")}
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <div className="space-y-4">
        {servers.map((server) => (
          <Card key={server.id} className="hover:shadow-md transition-shadow">
            <CardHeader className="pb-3">
              <div className="flex flex-col gap-3">
                {/* Title, Test Button, and 3-dot menu in one line */}
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg">{server.name}</CardTitle>
                  <div className="flex items-center gap-2">
                    {getActionButtons(server)}
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreVertical className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem
                          onClick={() => onViewTools(server.id)}
                        >
                          <Eye className="h-4 w-4 mr-2" />
                          {t("mcpServers.viewTools")}
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => onEdit(server)}>
                          <Edit className="h-4 w-4 mr-2" />
                          {t("common.edit")}
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem
                          onClick={() => handleDeleteClick(server)}
                          className="text-red-600"
                        >
                          <Trash2 className="h-4 w-4 mr-2" />
                          {t("common.delete")}
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>

                {/* Badges and Toggle */}
                <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4">
                  <div className="flex items-center gap-2 flex-wrap">
                    <MCPServerStatusBadge
                      status={server.status}
                      lastError={server.last_error}
                      size="sm"
                    />
                    {server.is_public ? (
                      <Badge
                        variant="outline"
                        className="flex items-center gap-1 w-fit"
                      >
                        <Globe className="h-3 w-3" />
                        {t("mcpServers.public")}
                      </Badge>
                    ) : (
                      <Badge
                        variant="outline"
                        className="flex items-center gap-1 w-fit"
                      >
                        <Lock className="h-3 w-3" />
                        {t("mcpServers.private")}
                      </Badge>
                    )}
                  </div>

                  <div className="flex items-center gap-2">
                    <Label
                      htmlFor={`toggle-${server.id}`}
                      className="text-sm font-medium"
                      title={
                        t("settings.mcp.toggleStatus") || "Toggle server status"
                      }
                    >
                      {server.status === "ACTIVE"
                        ? t("settings.mcp.active") || "Active"
                        : t("settings.mcp.inactive") || "Inactive"}
                    </Label>
                    <Switch
                      id={`toggle-${server.id}`}
                      checked={server.status === "ACTIVE"}
                      onCheckedChange={() => onToggleStatus(server.id)}
                      disabled={server.status === "TESTING"}
                      className="data-[state=checked]:bg-green-600"
                    />
                  </div>
                </div>

                {/* Description */}
                {server.description && (
                  <p className="text-sm text-muted-foreground">
                    {server.description ===
                    "GitHub MCP Server for repository management and code analysis"
                      ? t("mcpServers.serverDescriptions.githubMcp")
                      : server.description}
                  </p>
                )}

                {/* Server Details */}
                <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4 text-sm text-muted-foreground">
                  <span className="flex items-center gap-1">
                    <Terminal className="h-3 w-3 flex-shrink-0" />
                    <span className="truncate">
                      {server.server_type === "HTTP"
                        ? server.url || t("mcpServers.form.httpType")
                        : server.command || t("mcpServers.form.stdioType")}
                    </span>
                  </span>
                  <span className="flex items-center gap-1">
                    <Badge variant="outline" className="text-xs">
                      {server.server_type}
                    </Badge>
                  </span>
                  <span className="flex items-center gap-1">
                    <Clock className="h-3 w-3 flex-shrink-0" />
                    {formatDistanceToNow(new Date(server.updated_at), {
                      addSuffix: true,
                      locale: language === "de" ? de : enUS,
                    })}
                  </span>
                </div>
              </div>
            </CardHeader>

            <CardContent className="pt-0">
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
                <div>
                  <span className="text-muted-foreground">
                    {t("mcpServers.timeout")}:
                  </span>
                  <p className="font-medium">{server.timeout}ms</p>
                </div>
                <div>
                  <span className="text-muted-foreground">
                    {t("mcpServers.autoRestart")}:
                  </span>
                  <p className="font-medium">
                    {server.auto_restart ? t("common.yes") : t("common.no")}
                  </p>
                </div>
                <div>
                  <span className="text-muted-foreground">
                    {t("mcpServers.arguments")}:
                  </span>
                  <p className="font-medium">{server.args?.length || 0}</p>
                </div>
                <div>
                  <span className="text-muted-foreground">
                    {t("mcpServers.envVariables")}:
                  </span>
                  <p className="font-medium">
                    {server.env ? Object.keys(server.env).length : 0}
                  </p>
                </div>
              </div>

              {server.last_error && (
                <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-md">
                  <p className="text-sm text-red-700 break-words">
                    <strong>{t("mcpServers.errorMessage")}:</strong>{" "}
                    {server.last_error}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              {t("mcpServers.deleteServerTitle")}
            </AlertDialogTitle>
            <AlertDialogDescription>
              {t("mcpServers.deleteServerDescription", {
                name: serverToDelete?.name,
              })}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>{t("common.cancel")}</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteConfirm}
              className="bg-red-600 hover:bg-red-700"
            >
              {t("common.delete")}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
