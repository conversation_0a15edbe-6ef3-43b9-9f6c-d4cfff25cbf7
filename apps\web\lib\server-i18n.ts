import { cookies, headers } from 'next/headers';
import enTranslations from '../i18n/locales/en.json';
import deTranslations from '../i18n/locales/de.json';

// Define the languages we support
export type Language = 'en' | 'de';

// Translations holder
const translationsMap: Record<Language, Record<string, any>> = {
  en: enTranslations,
  de: deTranslations,
};

/**
 * Get translations for server components
 * @returns A translation function and the current language
 */
export function getTranslations() {
  // Get language from cookie or header
  const cookieStore = cookies();
  const headersList = headers();
  
  // Try to get language from cookie first
  let language: Language = 'en'; // Default to English
  
  const languageCookie = cookieStore.get('language')?.value as Language;
  const languageHeader = headersList.get('x-language') as Language;
  
  if (languageCookie && (languageCookie === 'en' || languageCookie === 'de')) {
    language = languageCookie;
  } else if (languageHeader && (languageHeader === 'en' || languageHeader === 'de')) {
    language = languageHeader;
  }
  
  const translations = translationsMap[language];
  
  // Helper function to get nested translation
  const t = (key: string, replacements: Record<string, string | number> = {}): string => {
    const keys = key.split('.');
    let result: any = translations;

    for (const k of keys) {
      if (result && typeof result === 'object' && k in result) {
        result = result[k];
      } else {
        // Fallback to key if translation not found
        return key;
      }
    }

    if (typeof result !== 'string') {
      return key;
    }

    // Replace placeholders with values
    let translatedText = result;
    Object.entries(replacements).forEach(([placeholder, value]) => {
      translatedText = translatedText.replace(new RegExp(`{${placeholder}}`, 'g'), String(value));
    });

    return translatedText;
  };

  return { t, language };
}
