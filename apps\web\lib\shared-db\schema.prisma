generator client {
    provider      = "prisma-client-js"
    binaryTargets = ["native", "debian-openssl-1.1.x"]
}

datasource db {
    provider = "mongodb"
    url      = env("DATABASE_URL")
}

enum MembershipRole {
    OWNER
    MEMBER
    ADMIN
    CUSTOM
}

enum CompanyMemberRole {
    OWNER
    ADMIN
    MEMBER
    VIEWER
}

enum CompanyMemberStatus {
    ACTIVE
    PENDING
    INACTIVE
}

enum LLMScope {
    INTERNAL_ONLY // Only internal documents/knowledge base
    EXTERNAL_ONLY // Only external web search
    MCP_ONLY // Only MCP (Model Context Protocol) tools and external services
    HYBRID // Internal documents + web search + MCP servers
    FULL_ACCESS // Full LLM capabilities including deep research and advanced features
}

enum LLMProvider {
    AZURE_OPENAI
    AZURE_DEEPSEEK
    OPENAI
    DEEPSEEK
}

enum VectorDBProvider {
    LANCEDB
    PINECONE
    CHROMADB
}

enum EmbeddingProvider {
    OPENAI
}

enum VectorizationStatus {
    PENDING
    PROCESSING
    COMPLETED
    FAILED
}

model Account {
    id                 String  @id @default(auto()) @map("_id") @db.ObjectId
    userId             String  @db.ObjectId
    type               String
    provider           String
    providerAccountId  String
    refresh_token      String?
    access_token       String?
    expires_at         Int?
    token_type         String?
    scope              String?
    id_token           String?
    session_state      String?
    oauth_token_secret String?
    oauth_token        String?
    user               User    @relation(fields: [userId], references: [id])

    @@unique([provider, providerAccountId])
    @@index([userId])
}

model Session {
    id           String   @id @default(auto()) @map("_id") @db.ObjectId
    sessionToken String   @unique
    userId       String   @db.ObjectId
    expires      DateTime
    user         User     @relation(fields: [userId], references: [id])
    createdAt    DateTime @default(now())
    updatedAt    DateTime @updatedAt

    @@index([userId])
}

model User {
    id                    String    @id @default(auto()) @map("_id") @db.ObjectId
    name                  String? /// @encrypted <- annotate fields to encrypt
    email                 String    @unique /// @encrypted <- annotate fields to encrypt
    emailHash             String    @unique
    password              String?
    emailVerified         DateTime?
    image                 String?
    passwordResetRequired Boolean   @default(false)

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    accounts               Account[]
    sessions               Session[]
    membership             Membership[]
    sentInvitations        Invitation[]            @relation("SentInvitations")
    workspaceMember        WorkspaceMember[]
    Chat                   Chat[]
    Message                Message[]
    ChatGroup              ChatGroup[]
    TokenUsage             TokenUsage[]
    APIRequest             APIRequest[]
    Integration            Integration[]
    GroupMember            GroupMember[]
    ThreadShare            ThreadShare[]
    Comment                Comment[]
    notifications          Notification[]
    triggeredNotifications Notification[]          @relation("NotificationTrigger")
    PageMember             PageMember[]
    UserChangelogView      UserChangelogView[]
    MCPServer              MCPServer[]
    DocumentProcessingJob  DocumentProcessingJob[]
    Chatbot                Chatbot[]
    CompanyMember          CompanyMember[]
    ChatbotSession         ChatbotSession[]
    BlockedUser            BlockedUser[]
}

model VerificationToken {
    id         String   @id @default(auto()) @map("_id") @db.ObjectId
    identifier String
    token      String   @unique
    expires    DateTime
    type       String?

    @@unique([identifier, token])
}

model Tenant {
    id                        String                  @id @default(auto()) @map("_id") @db.ObjectId
    name                      String
    slug                      String
    image                     String?
    description               String?
    url                       String?
    llmScope                  String[]                @default(["INTERNAL_ONLY"]) // Organization-level LLM scope control as array
    fileUploadLimitDuringChat Int                     @default(5) // Maximum number of files that can be uploaded during AI chat sessions
    createdAt                 DateTime                @default(now())
    updatedAt                 DateTime                @updatedAt
    isOnboarded               Boolean                 @default(false)
    stripeCustomerId          String? // Stripe customer ID
    Membership                Membership[]
    workspaces                Workspace[]
    llmSettings               LLMSettings[]
    vectorDbSettings          VectorDBSettings[]
    embeddingSettings         EmbeddingSettings[]
    Invitation                Invitation[]
    Integration               Integration[]
    TokenUsage                TokenUsage[]
    APIRequest                APIRequest[]
    Subscription              Subscription[]
    VectorStoreUsage          VectorStoreUsage[]
    ChatGroup                 ChatGroup[]
    Chat                      Chat[]
    customRoles               CustomRole[]
    groups                    Group[]
    WebSearchUsage            WebSearchUsage[]
    ThreadShare               ThreadShare[]
    Comment                   Comment[]
    Notification              Notification[]
    builtInRoleConfigs        BuiltInRoleConfig[]
    MCPServer                 MCPServer[]
    DocumentProcessingJob     DocumentProcessingJob[]
    Chatbot                   Chatbot[]
    ChatbotMessage            ChatbotMessage[]
    companies                 Company[] // Companies managed by this tenant
    BlockedUser               BlockedUser[]
    themeConfiguration        ThemeConfiguration?
    ChatbotSession            ChatbotSession[]

    @@unique([slug, id])
    @@index([slug])
}

model ThemeConfiguration {
  id               String   @id @default(auto()) @map("_id") @db.ObjectId
  tenantId         String   @unique @db.ObjectId
  tenant           Tenant   @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  // Brand Identity
  brandName        String?  // Custom brand name for white-label
  logoUrl          String?  // URL to custom logo
  faviconUrl       String?  // URL to custom favicon
  fullAppLogoUrl   String?  // URL to full app logo for changelog and other pages



  // Light Theme Color Customization
  lightPrimaryColor              String?  // Light theme primary brand color (hex format)
  lightSecondaryColor            String?  // Light theme secondary brand color (hex format)
  lightAccentColor               String?  // Light theme accent color (hex format)
  lightNavigationBackgroundColor String?  // Light theme navigation background color for sidebar and header (hex format)
  lightContentBackgroundColor    String?  // Light theme content background color for main content areas (hex format)
  lightForegroundColor           String?  // Light theme foreground/text color (hex format)

  // Dark Theme Color Customization
  darkPrimaryColor              String?  // Dark theme primary brand color (hex format)
  darkSecondaryColor            String?  // Dark theme secondary brand color (hex format)
  darkAccentColor               String?  // Dark theme accent color (hex format)
  darkNavigationBackgroundColor String?  // Dark theme navigation background color for sidebar and header (hex format)
  darkContentBackgroundColor    String?  // Dark theme content background color for main content areas (hex format)
  darkForegroundColor           String?  // Dark theme foreground/text color (hex format)

  // Theme Settings
  themePreset         String?  @default("light") // Theme preset: light, dark

  // Metadata
  isActive         Boolean  @default(true)      // Whether theme configuration is active
  version          Int      @default(1)         // Version for theme configuration tracking
  createdAt        DateTime @default(now())
  updatedAt        DateTime @updatedAt

  // Indexes
  @@index([isActive])
}


model Membership {
    id           String         @id @default(auto()) @map("_id") @db.ObjectId
    role         MembershipRole
    tenantId     String         @db.ObjectId
    userId       String         @db.ObjectId
    user         User           @relation(fields: [userId], references: [id])
    tenant       Tenant         @relation(fields: [tenantId], references: [id])
    customRoleId String?        @db.ObjectId
    customRole   CustomRole?    @relation(fields: [customRoleId], references: [id])

    createdAt       DateTime          @default(now())
    updatedAt       DateTime          @updatedAt
    workspaceMember WorkspaceMember[]

    @@unique([tenantId, userId]) // Prevents duplicate memberships
    @@index([tenantId])
    @@index([userId])
    @@index([customRoleId])
}

model Company {
    id          String   @id @default(auto()) @map("_id") @db.ObjectId
    name        String
    slug        String
    description String?
    maxSignups  Int      @default(50) // Maximum number of signups allowed
    inviteLink  String? // Custom invite link for the company
    status      String   @default("active") // active, inactive, suspended
    createdAt   DateTime @default(now())
    updatedAt   DateTime @updatedAt

    // Relationship to tenant (organization that manages this company)
    tenantId String @db.ObjectId
    tenant   Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)

    // Company members
    members CompanyMember[]

    @@unique([slug, tenantId]) // Unique slug per tenant
    @@index([tenantId])
    @@index([slug])
    @@index([status])
}

model CompanyMember {
    id        String              @id @default(auto()) @map("_id") @db.ObjectId
    role      CompanyMemberRole   @default(MEMBER)
    status    CompanyMemberStatus @default(ACTIVE)
    joinedAt  DateTime            @default(now())
    createdAt DateTime            @default(now())
    updatedAt DateTime            @updatedAt

    // Relationships
    companyId String  @db.ObjectId
    company   Company @relation(fields: [companyId], references: [id], onDelete: Cascade)
    userId    String  @db.ObjectId
    user      User    @relation(fields: [userId], references: [id], onDelete: Cascade)

    @@unique([companyId, userId]) // Prevents duplicate memberships
    @@index([companyId])
    @@index([userId])
    @@index([role])
    @@index([status])
}

model Workspace {
    id                    String                  @id @default(auto()) @map("_id") @db.ObjectId
    name                  String
    slug                  String
    description           String?
    initials              String?
    oneDriveFolderId      String?
    gDriveFolderId        String?
    createdAt             DateTime                @default(now())
    updatedAt             DateTime                @updatedAt
    tenantId              String                  @db.ObjectId
    tenant                Tenant                  @relation(fields: [tenantId], references: [id])
    pages                 Page[]
    folders               Folder[]
    files                 File[]
    LLMSettings           LLMSettings[]
    VectorDBSettings      VectorDBSettings[]
    EmbeddingSettings     EmbeddingSettings[]
    workspaceMember       WorkspaceMember[]
    customRoles           CustomRoleWorkspace[]
    groupWorkspaces       GroupWorkspace[]
    DocumentProcessingJob DocumentProcessingJob[]

    @@unique([slug, tenantId])
    @@index([tenantId])
}

model Page {
    id                   String  @id @default(auto()) @map("_id") @db.ObjectId
    name                 String
    content              String?
    oneDriveFolderId     String?
    gDriveFolderId       String?
    sharePointSiteId     String?
    sharePointDriveId    String?
    sharePointFolderPath String? // Path to the synced SharePoint folder

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    workspaceId String       @db.ObjectId
    workspace   Workspace    @relation(fields: [workspaceId], references: [id])
    folders     Folder[]
    files       File[]
    pageMembers PageMember[]

    @@index([workspaceId])
    @@index([sharePointSiteId, sharePointDriveId])
}

model Folder {
    id               String  @id @default(auto()) @map("_id") @db.ObjectId
    name             String
    oneDriveFolderId String?
    gDriveFolderId   String?
    parentId         String? @db.ObjectId

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    workspaceId String    @db.ObjectId
    pageId      String?   @db.ObjectId
    workspace   Workspace @relation(fields: [workspaceId], references: [id])
    page        Page?     @relation(fields: [pageId], references: [id])

    parentRelations FolderHierarchy[] @relation("FolderParents")
    childRelations  FolderHierarchy[] @relation("FolderChildren")
    files           File[]

    @@index([workspaceId])
}

model FolderHierarchy {
    id       String  @id @default(auto()) @map("_id") @db.ObjectId
    parentId String  @db.ObjectId
    childId  String  @db.ObjectId
    pageId   String? @db.ObjectId

    parent Folder @relation("FolderParents", fields: [parentId], references: [id], onDelete: Cascade)
    child  Folder @relation("FolderChildren", fields: [childId], references: [id], onDelete: Cascade)

    @@unique([parentId, childId]) // Prevents duplicate relationships
}

model File {
    id                  String              @id @default(auto()) @map("_id") @db.ObjectId
    name                String
    type                String
    extension           String?
    gDriveFileId        String?
    oneDriveFileId      String?
    size                String?
    url                 String?
    metadata            Json?
    content             String?
    vectorizationStatus VectorizationStatus @default(PENDING)
    vectorizedAt        DateTime?
    createdAt           DateTime            @default(now())
    updatedAt           DateTime            @updatedAt
    workspaceId         String              @db.ObjectId
    parentId            String?             @db.ObjectId
    pageId              String?             @db.ObjectId
    folderId            String?             @db.ObjectId
    workspace           Workspace           @relation(fields: [workspaceId], references: [id])
    page                Page?               @relation(fields: [pageId], references: [id])
    folder              Folder?             @relation(fields: [folderId], references: [id])

    @@index([workspaceId])
}

model LLMSettings {
    id                    String      @id @default(auto()) @map("_id") @db.ObjectId
    provider              LLMProvider
    apiKey                String?
    // Azure OpenAI fields
    azureOpenAIModel      String?
    azureOpenAIApiVersion String?
    azureOpenAIDeployment String?
    azureOpenAIEndpoint   String?
    // OpenAI fields
    baseUrl               String?

    tenantId    String    @db.ObjectId
    tenant      Tenant    @relation(fields: [tenantId], references: [id])
    workspaceId String    @db.ObjectId
    workspace   Workspace @relation(fields: [workspaceId], references: [id])

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt
}

model VectorDBSettings {
    id          String           @id @default(auto()) @map("_id") @db.ObjectId
    provider    VectorDBProvider
    // LanceDB fields
    localUri    String?
    // Pinecone fields
    apiKey      String?
    environment String?

    tenantId    String    @db.ObjectId
    tenant      Tenant    @relation(fields: [tenantId], references: [id])
    workspaceId String    @db.ObjectId
    workspace   Workspace @relation(fields: [workspaceId], references: [id])

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt
}

model EmbeddingSettings {
    id            String            @id @default(auto()) @map("_id") @db.ObjectId
    provider      EmbeddingProvider
    // OpenAI Embeddings fields
    apiKey        String?
    embedderModel String?

    tenantId    String    @db.ObjectId
    tenant      Tenant    @relation(fields: [tenantId], references: [id])
    workspaceId String    @db.ObjectId
    workspace   Workspace @relation(fields: [workspaceId], references: [id])

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt
}

model Invitation {
    id           String         @id @default(auto()) @map("_id") @db.ObjectId
    email        String /// @encrypted <- annotate fields to encrypt
    emailHash    String
    role         MembershipRole
    customRoleId String?        @db.ObjectId
    tenantId     String         @db.ObjectId
    inviterId    String         @db.ObjectId
    token        String         @unique
    expires      DateTime
    accepted     Boolean        @default(false)

    tenant     Tenant      @relation(fields: [tenantId], references: [id])
    inviter    User        @relation("SentInvitations", fields: [inviterId], references: [id])
    customRole CustomRole? @relation(fields: [customRoleId], references: [id])

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    @@index([tenantId])
    @@index([inviterId])
    @@index([email])
    @@index([customRoleId])
}

model WorkspaceMember {
    id   String         @id @default(auto()) @map("_id") @db.ObjectId
    role MembershipRole

    workspaceId  String      @db.ObjectId
    userId       String      @db.ObjectId
    membershipId String      @db.ObjectId
    customRoleId String?     @db.ObjectId
    workspace    Workspace   @relation(fields: [workspaceId], references: [id])
    user         User        @relation(fields: [userId], references: [id])
    membership   Membership? @relation(fields: [membershipId], references: [id], onDelete: Cascade)
    customRole   CustomRole? @relation(fields: [customRoleId], references: [id])

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    @@unique([workspaceId, userId])
    @@index([workspaceId])
    @@index([userId])
    @@index([customRoleId])
}

enum PageMemberRole {
    MEMBER // Read access
    EDITOR // Read/Write access
    ADMIN // Full access including member management
}

enum PageMemberSource {
    MANUAL // Manually added by page admin
    SHAREPOINT // Automatically added via SharePoint access
    WORKSPACE // Inherited from workspace membership
}

model PageMember {
    id     String           @id @default(auto()) @map("_id") @db.ObjectId
    role   PageMemberRole
    source PageMemberSource @default(MANUAL)

    pageId   String @db.ObjectId
    userId   String @db.ObjectId
    tenantId String @db.ObjectId

    // Relations
    page Page @relation(fields: [pageId], references: [id], onDelete: Cascade)
    user User @relation(fields: [userId], references: [id])

    // SharePoint metadata (when source is SHAREPOINT)
    sharePointPermissionLevel String? // Original SharePoint permission level
    lastSharePointCheck       DateTime?

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    @@unique([pageId, userId])
    @@index([pageId])
    @@index([userId])
    @@index([tenantId])
    @@index([source])
}

model ChatGroup {
    id          String  @id @default(auto()) @map("_id") @db.ObjectId
    name        String
    description String?
    isArchived  Boolean @default(false)

    userId   String @db.ObjectId
    user     User   @relation(fields: [userId], references: [id])
    tenantId String @db.ObjectId
    teant    Tenant @relation(fields: [tenantId], references: [id])

    chats     Chat[]
    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    @@index([userId])
}

model Chat {
    id          String  @id @default(auto()) @map("_id") @db.ObjectId
    title       String?
    description String?
    isArchived  Boolean @default(false)

    userId   String     @db.ObjectId
    user     User       @relation(fields: [userId], references: [id])
    groupId  String?    @db.ObjectId
    group    ChatGroup? @relation(fields: [groupId], references: [id])
    tenantId String     @db.ObjectId
    teant    Tenant     @relation(fields: [tenantId], references: [id])

    messages       Message[]
    createdAt      DateTime         @default(now())
    updatedAt      DateTime         @updatedAt
    ThreadShare    ThreadShare[]
    Notification   Notification[]
    MCPChatSession MCPChatSession[]

    @@index([userId])
    @@index([groupId])
}

model Message {
    id       String @id @default(auto()) @map("_id") @db.ObjectId
    content  String /// @encrypted <- annotate fields to encrypt
    role     String // 'user' or 'assistant'
    metadata Json? // For storing additional message-specific data
    sources  Json? // For storing document sources/citations

    // New field for tracking regenerated messages
    originalMessageId   String?   @db.ObjectId // If this is a regenerated message, points to the original message
    regeneratedMessages Message[] @relation("RegeneratedMessages") // Messages that are regenerations of this one
    originalMessage     Message?  @relation("RegeneratedMessages", fields: [originalMessageId], references: [id], onDelete: NoAction, onUpdate: NoAction)

    chatId String @db.ObjectId
    chat   Chat   @relation(fields: [chatId], references: [id], onDelete: Cascade)
    userId String @db.ObjectId
    user   User   @relation(fields: [userId], references: [id])

    createdAt    DateTime       @default(now())
    updatedAt    DateTime       @updatedAt
    Comment      Comment[]
    Notification Notification[]

    @@index([chatId])
    @@index([userId])
}

enum IntegrationType {
    OUTLOOK
    GOOGLE
}

model Integration {
    id           String          @id @default(auto()) @map("_id") @db.ObjectId
    platform     IntegrationType
    accountId    String
    accountName  String?
    accessToken  String
    page         Json?
    refreshToken String?
    config       Json?
    tenantId     String          @db.ObjectId
    tenant       Tenant          @relation(fields: [tenantId], references: [id])
    userId       String          @db.ObjectId
    user         User            @relation(fields: [userId], references: [id])

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    @@index([userId])
    @@index([tenantId])
    @@map("integration")
}

model TokenUsage {
    id           String   @id @default(auto()) @map("_id") @db.ObjectId
    userId       String?  @db.ObjectId
    user         User?    @relation(fields: [userId], references: [id])
    inputTokens  Int
    outputTokens Int
    timestamp    DateTime @default(now())
    requestType  String // e.g., "chat", "embedding", "completion"
    modelUsed    String // The specific model used for this request
    cost         Float // Estimated cost in USD
    tenantId     String   @db.ObjectId
    tenant       Tenant   @relation(fields: [tenantId], references: [id])
    createdAt    DateTime @default(now())
    updatedAt    DateTime @updatedAt

    @@index([userId])
    @@index([timestamp])
}

model APIRequest {
    id           String   @id @default(auto()) @map("_id") @db.ObjectId
    userId       String   @db.ObjectId
    user         User     @relation(fields: [userId], references: [id])
    endpoint     String // The API endpoint called
    method       String // HTTP method used
    statusCode   Int // Response status code
    timestamp    DateTime @default(now())
    duration     Int // Request duration in milliseconds
    success      Boolean // Whether the request was successful
    errorMessage String? // Error message if request failed
    tenantId     String   @db.ObjectId
    tenant       Tenant   @relation(fields: [tenantId], references: [id])
    createdAt    DateTime @default(now())
    updatedAt    DateTime @updatedAt

    @@index([userId])
    @@index([timestamp])
}

enum PlanType {
    STARTER
    BUSINESS
    CUSTOM
    ENTERPRISE
}

model Plan {
    id                      String         @id @default(auto()) @map("_id") @db.ObjectId
    name                    String
    type                    PlanType
    description             String?
    stripePriceId           String? // Monthly price ID for the base plan
    stripeYearlyPriceId     String? // Yearly price ID for the base plan
    webSearchLimit          Int            @default(50) // Daily web search limit
    includedUsers           Int
    additionalUserFee       Float // For display purposes only
    stripeUserPriceId       String? // Monthly price ID for additional users
    stripeUserYearlyPriceId String? // Yearly price ID for additional users
    vectorStoreGB           Int
    price                   Float? // Base price of the plan (for display purposes only)
    isActive                Boolean        @default(true)
    createdAt               DateTime       @default(now())
    updatedAt               DateTime       @updatedAt
    subscriptions           Subscription[]
}

// Define a type for storage tier items in the subscription
type StorageTierItem {
    id            String
    size          Int
    quantity      Int
    price         Float
    stripePriceId String?
}

model Subscription {
    id                   String            @id @default(auto()) @map("_id") @db.ObjectId
    tenantId             String            @db.ObjectId
    tenant               Tenant            @relation(fields: [tenantId], references: [id])
    planId               String            @db.ObjectId
    plan                 Plan              @relation(fields: [planId], references: [id])
    startDate            DateTime          @default(now())
    endDate              DateTime?
    isActive             Boolean           @default(true)
    isOnTrial            Boolean           @default(false) // Whether the subscription is on a trial period
    trialEndDate         DateTime? // When the trial period ends
    additionalUsers      Int               @default(0)
    additionalStorageGB  Int               @default(0) // Additional vector storage in GB (for backward compatibility)
    storageTierItems     StorageTierItem[] // Array of storage tier objects
    stripeSubscriptionId String? // Main Stripe subscription ID
    stripeCustomerId     String? // Stripe customer ID
    billingInterval      String? // "month" or "year"
    createdAt            DateTime          @default(now())
    updatedAt            DateTime          @updatedAt

    @@index([tenantId])
    @@index([planId])
}

model StorageTier {
    id                  String   @id @default(auto()) @map("_id") @db.ObjectId
    name                String // e.g., "10GB", "50GB", "100GB"
    sizeGB              Int // Size in GB
    price               Float // Price for display purposes
    stripePriceId       String // Monthly price ID
    stripeYearlyPriceId String // Yearly price ID
    isActive            Boolean  @default(true)
    createdAt           DateTime @default(now())
    updatedAt           DateTime @updatedAt
}

model VectorStoreUsage {
    id        String   @id @default(auto()) @map("_id") @db.ObjectId
    tenantId  String   @db.ObjectId
    tenant    Tenant   @relation(fields: [tenantId], references: [id])
    usageGB   Float
    timestamp DateTime @default(now())
    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    @@index([tenantId])
    @@index([timestamp])
}

enum PermissionAction {
    CREATE
    READ
    UPDATE
    DELETE
}

enum PermissionResource {
    WORKSPACE
    PAGE
    FOLDER
    FILE
    MEMBER
}

model Permission {
    id          String             @id @default(auto()) @map("_id") @db.ObjectId
    action      PermissionAction
    resource    PermissionResource
    description String?
    createdAt   DateTime           @default(now())
    updatedAt   DateTime           @updatedAt

    // Relations
    customRoles  CustomRolePermission[]
    builtInRoles BuiltInRolePermission[]

    @@unique([action, resource])
}

model CustomRole {
    id          String   @id @default(auto()) @map("_id") @db.ObjectId
    name        String
    description String?
    isDefault   Boolean  @default(false)
    tenantId    String   @db.ObjectId
    tenant      Tenant   @relation(fields: [tenantId], references: [id])
    createdAt   DateTime @default(now())
    updatedAt   DateTime @updatedAt

    // Relations
    permissions     CustomRolePermission[]
    memberships     Membership[]
    WorkspaceMember WorkspaceMember[]
    Invitation      Invitation[]
    groups          Group[]
    workspaces      CustomRoleWorkspace[]
    groupWorkspaces GroupWorkspace[]

    @@unique([name, tenantId])
    @@index([tenantId])
}

model CustomRoleWorkspace {
    id           String     @id @default(auto()) @map("_id") @db.ObjectId
    customRoleId String     @db.ObjectId
    workspaceId  String     @db.ObjectId
    customRole   CustomRole @relation(fields: [customRoleId], references: [id], onDelete: Cascade)
    workspace    Workspace  @relation(fields: [workspaceId], references: [id], onDelete: Cascade)
    createdAt    DateTime   @default(now())
    updatedAt    DateTime   @updatedAt

    @@unique([customRoleId, workspaceId])
    @@index([customRoleId])
    @@index([workspaceId])
}

model CustomRolePermission {
    id           String     @id @default(auto()) @map("_id") @db.ObjectId
    customRoleId String     @db.ObjectId
    permissionId String     @db.ObjectId
    customRole   CustomRole @relation(fields: [customRoleId], references: [id], onDelete: Cascade)
    permission   Permission @relation(fields: [permissionId], references: [id], onDelete: Cascade)
    createdAt    DateTime   @default(now())
    updatedAt    DateTime   @updatedAt

    @@unique([customRoleId, permissionId])
    @@index([customRoleId])
    @@index([permissionId])
}

model BuiltInRoleConfig {
    id        String   @id @default(auto()) @map("_id") @db.ObjectId
    roleType  String // ADMIN, MEMBER
    tenantId  String   @db.ObjectId
    tenant    Tenant   @relation(fields: [tenantId], references: [id])
    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    // Relations
    permissions BuiltInRolePermission[]

    @@unique([roleType, tenantId])
    @@index([tenantId])
}

model BuiltInRolePermission {
    id                  String            @id @default(auto()) @map("_id") @db.ObjectId
    builtInRoleConfigId String            @db.ObjectId
    permissionId        String            @db.ObjectId
    builtInRoleConfig   BuiltInRoleConfig @relation(fields: [builtInRoleConfigId], references: [id], onDelete: Cascade)
    permission          Permission        @relation(fields: [permissionId], references: [id], onDelete: Cascade)
    createdAt           DateTime          @default(now())
    updatedAt           DateTime          @updatedAt

    @@unique([builtInRoleConfigId, permissionId])
    @@index([builtInRoleConfigId])
    @@index([permissionId])
}

model Group {
    id          String   @id @default(auto()) @map("_id") @db.ObjectId
    name        String
    description String?
    createdAt   DateTime @default(now())
    updatedAt   DateTime @updatedAt

    // Relations
    tenantId        String           @db.ObjectId
    tenant          Tenant           @relation(fields: [tenantId], references: [id])
    groupMembers    GroupMember[]
    groupWorkspaces GroupWorkspace[]

    // Custom role relation for workspace access
    customRoleId String?     @db.ObjectId
    customRole   CustomRole? @relation(fields: [customRoleId], references: [id])

    @@unique([name, tenantId])
    @@index([tenantId])
    @@index([customRoleId])
}

model GroupMember {
    id        String   @id @default(auto()) @map("_id") @db.ObjectId
    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    // Relations
    groupId String @db.ObjectId
    userId  String @db.ObjectId
    group   Group  @relation(fields: [groupId], references: [id], onDelete: Cascade)
    user    User   @relation(fields: [userId], references: [id])

    @@unique([groupId, userId])
    @@index([groupId])
    @@index([userId])
}

model GroupWorkspace {
    id        String   @id @default(auto()) @map("_id") @db.ObjectId
    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    // Relations
    groupId     String    @db.ObjectId
    workspaceId String    @db.ObjectId
    group       Group     @relation(fields: [groupId], references: [id], onDelete: Cascade)
    workspace   Workspace @relation(fields: [workspaceId], references: [id], onDelete: Cascade)

    // Optional custom role for this specific group-workspace association
    customRoleId String?     @db.ObjectId
    customRole   CustomRole? @relation(fields: [customRoleId], references: [id])

    @@unique([groupId, workspaceId])
    @@index([groupId])
    @@index([workspaceId])
    @@index([customRoleId])
}

model WebSearchUsage {
    id        String   @id @default(auto()) @map("_id") @db.ObjectId
    tenantId  String   @db.ObjectId
    tenant    Tenant   @relation(fields: [tenantId], references: [id])
    userId    String   @db.ObjectId
    query     String
    cached    Boolean  @default(false)
    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    @@index([tenantId])
    @@index([userId])
    @@index([createdAt])
}

// Collaborative Features Models

model ThreadShare {
    id          String    @id @default(auto()) @map("_id") @db.ObjectId
    chatId      String    @db.ObjectId
    chat        Chat      @relation(fields: [chatId], references: [id], onDelete: Cascade)
    shareToken  String    @unique // URL-safe token for sharing
    isPublic    Boolean   @default(false) // If true, anyone with link can view
    expiresAt   DateTime? // Optional expiration date
    createdById String    @db.ObjectId
    createdBy   User      @relation(fields: [createdById], references: [id])
    tenantId    String    @db.ObjectId
    tenant      Tenant    @relation(fields: [tenantId], references: [id])
    createdAt   DateTime  @default(now())
    updatedAt   DateTime  @updatedAt

    @@index([chatId])
    @@index([tenantId])
}

enum CommentStatus {
    ACTIVE
    RESOLVED
    DELETED
}

model Comment {
    id           String         @id @default(auto()) @map("_id") @db.ObjectId
    content      String
    messageId    String         @db.ObjectId
    message      Message        @relation(fields: [messageId], references: [id], onDelete: Cascade)
    authorId     String         @db.ObjectId
    author       User           @relation(fields: [authorId], references: [id])
    parentId     String?        @db.ObjectId // For threaded comments (1-level only)
    parent       Comment?       @relation("CommentThread", fields: [parentId], references: [id], onDelete: NoAction, onUpdate: NoAction)
    replies      Comment[]      @relation("CommentThread")
    status       CommentStatus  @default(ACTIVE)
    mentions     Json? // Array of mentioned user IDs
    tenantId     String         @db.ObjectId
    tenant       Tenant         @relation(fields: [tenantId], references: [id])
    createdAt    DateTime       @default(now())
    updatedAt    DateTime       @updatedAt
    Notification Notification[]

    @@index([messageId])
    @@index([authorId])
    @@index([parentId])
    @@index([tenantId])
    @@index([status])
}

enum NotificationType {
    MENTION
    COMMENT_REPLY
    THREAD_SHARED
}

enum NotificationStatus {
    UNREAD
    READ
    DISMISSED
}

model Notification {
    id          String             @id @default(auto()) @map("_id") @db.ObjectId
    type        NotificationType
    title       String
    content     String // Changed from 'message' to avoid conflict
    status      NotificationStatus @default(UNREAD)
    userId      String             @db.ObjectId
    user        User               @relation(fields: [userId], references: [id])
    triggeredBy String?            @db.ObjectId // User who triggered the notification
    triggerUser User?              @relation("NotificationTrigger", fields: [triggeredBy], references: [id])

    // Related entities
    chatId         String?  @db.ObjectId
    chat           Chat?    @relation(fields: [chatId], references: [id], onDelete: Cascade)
    messageId      String?  @db.ObjectId
    relatedMessage Message? @relation(fields: [messageId], references: [id], onDelete: Cascade)
    commentId      String?  @db.ObjectId
    comment        Comment? @relation(fields: [commentId], references: [id], onDelete: Cascade)

    metadata  Json? // Additional notification data
    tenantId  String   @db.ObjectId
    tenant    Tenant   @relation(fields: [tenantId], references: [id])
    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    @@index([userId])
    @@index([status])
    @@index([type])
    @@index([tenantId])
    @@index([createdAt])
}

model Changelog {
    id       String            @id @default(auto()) @map("_id") @db.ObjectId
    title    String
    content  String // Markdown content
    version  String? // Optional version number
    type     ChangelogType     @default(RELEASE)
    priority ChangelogPriority @default(MEDIUM)
    isActive Boolean           @default(true)

    // Targeting options
    targetTenants     String[] @db.ObjectId // Empty array means all tenants
    targetEnvironment String? // "dev", "qa", "prod", null means all

    // Scheduling
    publishedAt DateTime  @default(now())
    expiresAt   DateTime? // Optional expiration date

    // Metadata
    githubCommitSha String? // If triggered by GitHub
    deploymentId    String? // If triggered by deployment
    authorId        String? // Admin user who created it

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    // Track which users have viewed this changelog
    userViews UserChangelogView[]

    @@index([isActive])
    @@index([publishedAt])
    @@index([targetEnvironment])
}

model UserChangelogView {
    id          String   @id @default(auto()) @map("_id") @db.ObjectId
    userId      String   @db.ObjectId
    changelogId String   @db.ObjectId
    viewedAt    DateTime @default(now())
    dismissed   Boolean  @default(false) // User explicitly dismissed

    user      User      @relation(fields: [userId], references: [id], onDelete: Cascade)
    changelog Changelog @relation(fields: [changelogId], references: [id], onDelete: Cascade)

    @@unique([userId, changelogId])
    @@index([userId])
    @@index([changelogId])
}

enum ChangelogType {
    RELEASE // New features, major updates
    HOTFIX // Bug fixes, security patches
    MAINTENANCE // Maintenance, performance improvements
    ANNOUNCEMENT // General announcements
}

enum ChangelogPriority {
    LOW // Optional viewing
    MEDIUM // Standard notification
    HIGH // Important, should be seen
    CRITICAL // Must be acknowledged
}

// MCP (Model Context Protocol) Server Integration Models

enum MCPServerStatus {
    ACTIVE
    INACTIVE
    ERROR
    TESTING
}

enum MCPServerType {
    STDIO // Command-line based MCP servers (npx, python scripts, etc.)
    HTTP // HTTP-based MCP servers with SSE support
}

model MCPServer {
    id          String          @id @default(auto()) @map("_id") @db.ObjectId
    name        String
    description String?
    serverType  MCPServerType   @default(STDIO) // Type of MCP server (stdio or http)
    command     String? // MCP server command (e.g., "npx -y @upstash/context7-mcp@latest") - required for stdio
    args        Json? // Additional command arguments as JSON array - for stdio servers
    env         Json? // Environment variables as JSON object - for stdio servers
    url         String? // HTTP URL for HTTP-based servers - required for http
    headers     Json? // HTTP headers as JSON object - for http servers (e.g., Authorization)
    status      MCPServerStatus @default(INACTIVE)
    lastError   String? // Last error message if status is ERROR

    // Ownership and access control
    userId   String @db.ObjectId
    user     User   @relation(fields: [userId], references: [id])
    tenantId String @db.ObjectId
    tenant   Tenant @relation(fields: [tenantId], references: [id])

    // Configuration
    timeout     Int     @default(30000) // Timeout in milliseconds
    autoRestart Boolean @default(true) // Auto-restart on failure
    isPublic    Boolean @default(false) // If true, available to all tenant users

    // Relationships
    mcpChatSessions MCPChatSession[]

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    @@unique([name, tenantId]) // Unique name per tenant
    @@index([userId])
    @@index([tenantId])
    @@index([status])
}

model MCPChatSession {
    id String @id @default(auto()) @map("_id") @db.ObjectId

    // Relationships
    chatId      String    @db.ObjectId
    chat        Chat      @relation(fields: [chatId], references: [id], onDelete: Cascade)
    mcpServerId String    @db.ObjectId
    mcpServer   MCPServer @relation(fields: [mcpServerId], references: [id], onDelete: Cascade)

    // Session data
    sessionData Json? // Store MCP session-specific data
    isActive    Boolean @default(true)

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    @@unique([chatId, mcpServerId]) // One session per MCP server per chat
    @@index([chatId])
    @@index([mcpServerId])
}

model DocumentProcessingJob {
    id     String @id @default(auto()) @map("_id") @db.ObjectId
    jobId  String @unique // Unique job identifier for tracking
    status String @default("pending") // "pending", "processing", "completed", "failed"

    // Document information
    documentPath String // Path or URL to the document
    documentType String // Type of document (pdf, text, etc.)
    metadata     Json? // Additional metadata for the document

    // Processing results
    documentCount Int? // Number of documents/chunks created
    errorMessage  String? // Error message if processing failed

    // Context information
    workspaceId   String    @db.ObjectId
    workspace     Workspace @relation(fields: [workspaceId], references: [id])
    fileId        String // File ID for status updates
    workspaceSlug String // Workspace slug for indexing

    // User and tenant context
    userId      String    @db.ObjectId
    user        User      @relation(fields: [userId], references: [id])
    tenantId    String    @db.ObjectId
    tenant      Tenant    @relation(fields: [tenantId], references: [id])
    // Timestamps
    createdAt   DateTime  @default(now())
    updatedAt   DateTime  @updatedAt
    completedAt DateTime?

    @@index([status])
    @@index([userId])
    @@index([tenantId])
    @@index([workspaceId])
    @@index([createdAt])
}

model Chatbot {
    id           String   @id @default(auto()) @map("_id") @db.ObjectId
    name         String
    description  String
    type         String // 'web-snippet', 'inline-embedding', 'dedicated-page'
    access       String // 'public', 'private'
    allowedUsers String[] // Array of company names for private access
    isActive     Boolean  @default(true)

    // SDK-specific fields
    apiKey         String?   @unique // API key for public access
    allowedDomains String[]  @default([]) // Domains allowed to embed this chatbot
    customization  Json? // Theme and UI customization settings
    usageCount     Int       @default(0) // Track total usage
    monthlyUsage   Int       @default(0) // Track monthly usage for billing
    lastUsedAt     DateTime? // Last time chatbot was used

    // LLM Configuration for this chatbot
    llmScope    String[] @default(["INTERNAL_ONLY"]) // Chatbot-specific LLM scope
    searchModes String[] @default(["internal"]) // Allowed search modes
    maxTokens   Int?     @default(4000) // Max tokens per response
    temperature Float?   @default(0.7) // LLM temperature setting

    // Rate limiting settings
    rateLimitPerMinute Int @default(60) // Requests per minute limit
    rateLimitPerHour   Int @default(1000) // Requests per hour limit
    rateLimitPerDay    Int @default(10000) // Requests per day limit

    userId     String   @db.ObjectId
    user       User     @relation(fields: [userId], references: [id])
    tenantId   String   @db.ObjectId
    tenant     Tenant   @relation(fields: [tenantId], references: [id])
    companyIds String[] @db.ObjectId

    // Relationships
    sessions  ChatbotSession[]
    messages  ChatbotMessage[]   @relation("ChatbotMessages")
    analytics ChatbotAnalytics[]

    createdAt      DateTime         @default(now())
    updatedAt      DateTime         @updatedAt
    ChatbotMessage ChatbotMessage[]

    @@index([userId])
    @@index([tenantId])
    @@index([isActive])
    @@index([lastUsedAt])
}

model ChatbotSession {
    id           String  @id @default(auto()) @map("_id") @db.ObjectId
    sessionToken String  @unique // Unique session identifier
    chatbotId    String  @db.ObjectId
    chatbot      Chatbot @relation(fields: [chatbotId], references: [id], onDelete: Cascade)

    // Tenant association for multi-tenant isolation
    tenantId String @db.ObjectId
    tenant   Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)

    // User information
    userId String? @db.ObjectId
    user   User?   @relation(fields: [userId], references: [id], onDelete: Cascade)

    // Basic session metadata
    domain    String? // Domain where the chatbot is embedded
    userAgent String? // User's browser information
    ipAddress String? // User's IP address (hashed for privacy)
    country   String? // User's country (from IP geolocation)

    // Enhanced user tracking
    city        String? // User's city (from IP geolocation)
    region      String? // User's state/region (from IP geolocation)
    timezone    String? // User's timezone
    language    String? // User's preferred language
    referrer    String? // Referring URL
    utmSource   String? // UTM source parameter
    utmMedium   String? // UTM medium parameter
    utmCampaign String? // UTM campaign parameter
    utmTerm     String? // UTM term parameter
    utmContent  String? // UTM content parameter

    // Device and browser information
    deviceType      String? // 'desktop', 'mobile', 'tablet'
    operatingSystem String? // 'Windows', 'macOS', 'iOS', 'Android', etc.
    browserName     String? // 'Chrome', 'Firefox', 'Safari', etc.
    browserVersion  String? // Browser version
    screenWidth     Int? // Screen width in pixels
    screenHeight    Int? // Screen height in pixels
    viewportWidth   Int? // Viewport width in pixels
    viewportHeight  Int? // Viewport height in pixels

    // Network and connection info
    connectionType String? // 'wifi', '4g', '3g', 'ethernet', etc.
    isp            String? // Internet Service Provider
    organization   String? // Organization (if corporate network)

    // User behavior tracking
    pageUrl        String? // Current page URL where chat was initiated
    pageTitle      String? // Page title
    sessionDepth   Int     @default(1) // Number of pages visited in session
    isReturning    Boolean @default(false) // Is this a returning visitor
    previousVisits Int     @default(0) // Number of previous visits

    // Session tracking
    messagesCount  Int       @default(0) // Number of messages in this session
    startedAt      DateTime  @default(now())
    lastActivityAt DateTime  @default(now())
    endedAt        DateTime? // When session ended
    duration       Int?      @default(0) // Session duration in seconds

    // User satisfaction
    satisfactionRating Int? // 1-5 rating if provided
    feedback           String? // Optional user feedback

    // Performance metrics
    averageResponseTime Float? // Average API response time for this session
    errorCount          Int    @default(0) // Number of errors in this session

    // Additional metadata (flexible JSON field for future extensions)
    metadata Json? // Store any additional tracking data

    // Relationships
    messages ChatbotMessage[]

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    @@index([chatbotId])
    @@index([tenantId])
    @@index([startedAt])
    @@index([domain])
    @@index([country])
    @@index([deviceType])
    @@index([isReturning])
}

model BlockedUser {
    id       String @id @default(auto()) @map("_id") @db.ObjectId
    tenantId String @db.ObjectId
    tenant   Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)

    // Who blocked this user
    blockedBy String @db.ObjectId
    blocker   User   @relation(fields: [blockedBy], references: [id], onDelete: Cascade)

    // Block details
    blockType String // 'user', 'email', 'ip'
    reason    String // Reason for blocking

    // User identification (one of these will be set based on blockType)
    userId    String? @db.ObjectId // For authenticated users
    email     String? // For email-based blocking
    ipAddress String? // For IP-based blocking (hashed)

    // Block status and timing
    isActive  Boolean   @default(true)
    expiresAt DateTime? // null for permanent blocks

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    @@index([tenantId])
    @@index([userId])
    @@index([email])
    @@index([ipAddress])
    @@index([isActive])
    @@index([expiresAt])
}

model ChatbotMessage {
    id      String @id @default(auto()) @map("_id") @db.ObjectId
    content String // Message content
    role    String // 'user' or 'assistant' or 'system'

    // Relationships
    sessionId String         @db.ObjectId
    session   ChatbotSession @relation(fields: [sessionId], references: [id], onDelete: Cascade)
    chatbotId String         @db.ObjectId
    chatbot   Chatbot        @relation("ChatbotMessages", fields: [chatbotId], references: [id], onDelete: Cascade)
    tenantId  String         @db.ObjectId
    tenant    Tenant         @relation(fields: [tenantId], references: [id])

    // Message metadata
    metadata Json? // Additional message data (sources, tools_used, etc.)

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt
    Chatbot   Chatbot  @relation(fields: [chatbotId], references: [id])

    @@index([sessionId])
    @@index([chatbotId])
    @@index([tenantId])
    @@index([createdAt])
}

model ChatbotAnalytics {
    id        String  @id @default(auto()) @map("_id") @db.ObjectId
    chatbotId String  @db.ObjectId
    chatbot   Chatbot @relation(fields: [chatbotId], references: [id], onDelete: Cascade)

    // Time period for this analytics record
    date DateTime // Date for daily analytics
    hour Int? // Hour (0-23) for hourly analytics

    // Usage metrics
    totalSessions      Int   @default(0)
    totalMessages      Int   @default(0)
    uniqueUsers        Int   @default(0) // Estimated based on IP/fingerprinting
    averageSessionTime Float @default(0)

    // Performance metrics
    averageResponseTime Float @default(0)
    errorRate           Float @default(0)
    successRate         Float @default(0)

    // User engagement
    satisfactionScore Float? // Average satisfaction rating
    conversionRate    Float  @default(0) // If conversion tracking is enabled
    bounceRate        Float  @default(0) // Sessions with only 1 message

    // API usage
    apiCallsCount Int @default(0)
    tokensUsed    Int @default(0)

    // Popular content
    topQuestions Json? // Array of most asked questions
    topDocuments Json? // Array of most retrieved documents

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    @@unique([chatbotId, date, hour]) // Unique analytics per chatbot per hour
    @@index([chatbotId])
    @@index([date])
}
