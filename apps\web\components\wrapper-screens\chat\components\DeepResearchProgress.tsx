"use client";

import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import {
  Brain,
  ChevronDown,
  ChevronUp,
  Search,
  Target,
  CheckCircle,
  AlertCircle,
  Clock,
  BarChart3,
} from "lucide-react";
import { useLanguage } from "@/lib/language-context";

interface ResearchProgress {
  phase?: string;
  message?: string;
  progress?: number;
  iteration?: number;
  confidence?: number;
  gaps_identified?: string[];
  research_plan?: {
    main_topic?: string;
    subtopics?: string[];
    research_questions?: string[];
  };
  iterations?: Array<{
    iteration: number;
    phase: string;
    confidence: number;
    gaps_identified: string[];
    findings_preview: string;
  }>;
}

interface ResearchSummary {
  iterations_conducted?: number;
  final_confidence?: number;
  quality_score?: number;
  subtopics_covered?: number;
  sources_consulted?: number;
}

interface DeepResearchProgressProps {
  researchProgress?: ResearchProgress;
  researchSummary?: ResearchSummary;
  iterations?: Array<{
    iteration: number;
    phase: string;
    confidence: number;
    gaps_identified: string[];
    findings_preview: string;
  }>;
  isComplete?: boolean;
}

const phaseIcons = {
  planning: <Target className="h-4 w-4" />,
  planning_complete: <CheckCircle className="h-4 w-4" />,
  research_iteration: <Search className="h-4 w-4" />,
  iteration_complete: <CheckCircle className="h-4 w-4" />,
  source_validation: <AlertCircle className="h-4 w-4" />,
  synthesis: <Brain className="h-4 w-4" />,
  complete: <CheckCircle className="h-4 w-4" />,
  confidence_achieved: <Target className="h-4 w-4" />,
};

export const DeepResearchProgress: React.FC<DeepResearchProgressProps> = ({
  researchProgress,
  researchSummary,
  iterations,
  isComplete = false,
}) => {
  const { t } = useLanguage();
  const [isExpanded, setIsExpanded] = useState(true);
  const [showIterations, setShowIterations] = useState(false);

  if (!researchProgress && !researchSummary && !isComplete) {
    return null;
  }

  const getConfidenceBadgeVariant = (confidence: number) => {
    if (confidence >= 0.8) return "default";
    if (confidence >= 0.6) return "secondary";
    return "destructive";
  };

  return (
    <Card className="mb-4 border border-blue-100 bg-gradient-to-r from-blue-50/50 to-indigo-50/50 shadow-sm">
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-100 rounded-full">
              <Search className="h-4 w-4 text-blue-600" />
            </div>
            <div>
              <CardTitle className="text-base font-medium text-gray-900">
                {isComplete
                  ? t("deepResearch.researchComplete")
                  : t("deepResearch.title")}
              </CardTitle>
              {!isComplete && (
                <p className="text-sm text-gray-500 mt-0.5">
                  {t("deepResearch.description")}
                </p>
              )}
            </div>
          </div>
          <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
            <CollapsibleTrigger asChild>
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                {isExpanded ? (
                  <ChevronUp className="h-4 w-4 text-gray-500" />
                ) : (
                  <ChevronDown className="h-4 w-4 text-gray-500" />
                )}
              </Button>
            </CollapsibleTrigger>
          </Collapsible>
        </div>
      </CardHeader>

      <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
        <CollapsibleContent>
          <CardContent className="pt-0 space-y-6">
            {/* Current Progress */}
            {researchProgress && !isComplete && (
              <div className="space-y-4">
                {/* Phase and Iteration */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="flex items-center gap-2 text-gray-700">
                      {phaseIcons[
                        researchProgress.phase as keyof typeof phaseIcons
                      ] || <Clock className="h-4 w-4" />}
                      <span className="font-medium capitalize text-sm">
                        {researchProgress.phase?.replace("_", " ")}
                      </span>
                    </div>
                    {researchProgress.iteration && (
                      <Badge variant="secondary" className="text-xs px-2 py-1">
                        {t("deepResearch.step", {
                          step: researchProgress.iteration,
                        })}
                      </Badge>
                    )}
                  </div>
                  {researchProgress.confidence !== undefined && (
                    <Badge
                      variant={getConfidenceBadgeVariant(
                        researchProgress.confidence
                      )}
                      className="text-xs"
                    >
                      {Math.round(researchProgress.confidence * 100)}%
                    </Badge>
                  )}
                </div>

                {/* Progress Bar */}
                {researchProgress.progress !== undefined && (
                  <div className="space-y-2">
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-600">
                        {t("deepResearch.progress")}
                      </span>
                      <span className="text-sm font-medium text-gray-900">
                        {Math.round(researchProgress.progress * 100)}%
                      </span>
                    </div>
                    <Progress
                      value={researchProgress.progress * 100}
                      className="h-2 bg-gray-100"
                    />
                  </div>
                )}

                {/* Current Message */}
                {researchProgress.message && (
                  <div className="bg-white/60 rounded-lg p-3 border border-gray-100">
                    <p className="text-sm text-gray-700 leading-relaxed">
                      {researchProgress.message}
                    </p>
                  </div>
                )}

                {/* Knowledge Gaps */}
                {researchProgress.gaps_identified &&
                  researchProgress.gaps_identified.length > 0 && (
                    <div className="bg-amber-50/50 rounded-lg p-3 border border-amber-100">
                      <div className="flex items-center gap-2 mb-2">
                        <AlertCircle className="h-4 w-4 text-amber-600" />
                        <span className="text-sm font-medium text-amber-800">
                          {t("deepResearch.areasForFurtherResearch")}
                        </span>
                      </div>
                      <ul className="text-sm text-amber-700 space-y-1">
                        {researchProgress.gaps_identified
                          .slice(0, 3)
                          .map((gap, index) => (
                            <li key={index} className="flex items-start gap-2">
                              <span className="text-amber-500 mt-1">•</span>
                              <span>{gap}</span>
                            </li>
                          ))}
                      </ul>
                    </div>
                  )}
              </div>
            )}

            {/* Research Plan */}
            {researchProgress?.research_plan && (
              <div className="bg-blue-50/50 rounded-lg p-4 border border-blue-100">
                <div className="flex items-center gap-2 mb-3">
                  <Target className="h-4 w-4 text-blue-600" />
                  <h4 className="font-medium text-blue-900">
                    {t("deepResearch.researchPlan")}
                  </h4>
                </div>
                <div className="space-y-3 text-sm">
                  <div className="flex items-start gap-2">
                    <span className="font-medium text-blue-800 min-w-fit">
                      {t("deepResearch.topic")}:
                    </span>
                    <span className="text-blue-700">
                      {researchProgress.research_plan.main_topic}
                    </span>
                  </div>
                  {researchProgress.research_plan.subtopics && (
                    <div className="flex items-center gap-2">
                      <span className="font-medium text-blue-800">
                        {t("deepResearch.subtopics")}:
                      </span>
                      <Badge
                        variant="outline"
                        className="text-blue-700 border-blue-200"
                      >
                        {t("deepResearch.identified", {
                          count:
                            researchProgress.research_plan.subtopics.length,
                        })}
                      </Badge>
                    </div>
                  )}
                  {researchProgress.research_plan.research_questions && (
                    <div className="flex items-center gap-2">
                      <span className="font-medium text-blue-800">
                        {t("deepResearch.questions")}:
                      </span>
                      <Badge
                        variant="outline"
                        className="text-blue-700 border-blue-200"
                      >
                        {t("deepResearch.planned", {
                          count:
                            researchProgress.research_plan.research_questions
                              .length,
                        })}
                      </Badge>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Research Summary */}
            {researchSummary && (
              <div className="bg-green-50/50 rounded-lg p-4 border border-green-100">
                <div className="flex items-center gap-2 mb-4">
                  <BarChart3 className="h-4 w-4 text-green-600" />
                  <h4 className="font-medium text-green-900">
                    {t("deepResearch.researchSummary")}
                  </h4>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-green-700">
                        {t("deepResearch.iterations")}
                      </span>
                      <Badge
                        variant="outline"
                        className="text-green-700 border-green-200"
                      >
                        {researchSummary.iterations_conducted || 0}
                      </Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-green-700">
                        {t("deepResearch.sources")}
                      </span>
                      <Badge
                        variant="outline"
                        className="text-green-700 border-green-200"
                      >
                        {researchSummary.sources_consulted || 0}
                      </Badge>
                    </div>
                  </div>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-green-700">
                        {t("deepResearch.confidence")}
                      </span>
                      <Badge
                        variant={getConfidenceBadgeVariant(
                          researchSummary.final_confidence || 0
                        )}
                        className="text-xs"
                      >
                        {Math.round(
                          (researchSummary.final_confidence || 0) * 100
                        )}
                        %
                      </Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-green-700">
                        {t("deepResearch.quality")}
                      </span>
                      <Badge
                        variant={getConfidenceBadgeVariant(
                          researchSummary.quality_score || 0
                        )}
                        className="text-xs"
                      >
                        {Math.round((researchSummary.quality_score || 0) * 100)}
                        %
                      </Badge>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Iterations Detail */}
            {iterations && iterations.length > 0 && (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Search className="h-4 w-4 text-gray-600" />
                    <h4 className="font-medium text-gray-900">
                      {t("deepResearch.researchSteps", {
                        count: iterations.length,
                      })}
                    </h4>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowIterations(!showIterations)}
                    className="text-gray-500 hover:text-gray-700 h-8 px-3"
                  >
                    {showIterations
                      ? t("deepResearch.hide")
                      : t("deepResearch.show")}
                    {showIterations ? (
                      <ChevronUp className="h-3 w-3 ml-1" />
                    ) : (
                      <ChevronDown className="h-3 w-3 ml-1" />
                    )}
                  </Button>
                </div>

                {showIterations && (
                  <div className="space-y-3">
                    {iterations.map((iteration, index) => (
                      <div
                        key={index}
                        className="bg-gray-50/50 rounded-lg p-4 border border-gray-100 space-y-3"
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-3">
                            <Badge
                              variant="secondary"
                              className="text-xs px-2 py-1"
                            >
                              {t("deepResearch.step", {
                                step: iteration.iteration,
                              })}
                            </Badge>
                            <span className="text-sm text-gray-700 capitalize font-medium">
                              {iteration.phase.replace("_", " ")}
                            </span>
                          </div>
                          <Badge
                            variant={getConfidenceBadgeVariant(
                              iteration.confidence
                            )}
                            className="text-xs"
                          >
                            {Math.round(iteration.confidence * 100)}%
                          </Badge>
                        </div>
                        {iteration.findings_preview && (
                          <div className="bg-white/60 rounded p-3 border border-gray-100">
                            <p className="text-sm text-gray-700 leading-relaxed">
                              {iteration.findings_preview}
                            </p>
                          </div>
                        )}
                        {iteration.gaps_identified.length > 0 && (
                          <div className="flex items-start gap-2 text-sm">
                            <span className="font-medium text-gray-600 min-w-fit">
                              {t("deepResearch.areasToExplore")}:
                            </span>
                            <span className="text-gray-600">
                              {iteration.gaps_identified.slice(0, 2).join(", ")}
                              {iteration.gaps_identified.length > 2 && "..."}
                            </span>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </CollapsibleContent>
      </Collapsible>
    </Card>
  );
};
