export const SupportedExtensions = [
  "pdf",
  "txt",
  "csv",
  "md",
  "mdx",
  "docx",
  "doc",
  "xlsx",
  "xls",
  "pptx",
  "ppt",
  "markdown",
  "jpg",
  "jpeg",
  "png",
  "gif",
  "bmp",
  "webp",
  // Audio formats
  "mp3",
  "wav",
  "m4a",
  "ogg",
  "flac",
  // Video formats
  "mp4",
  "avi",
  "mov",
  "webm",
  "mkv",
  "flv",
  "wmv",
  "m4v",
  "3gp",
];

// MIME type mappings for file upload validation
export const SupportedMimeTypes = [
  // Images
  "image/jpeg",
  "image/jpg",
  "image/png",
  "image/webp",
  "image/gif",
  "image/bmp",
  // Documents
  "application/pdf",
  "text/plain",
  "text/csv",
  "application/csv",
  "text/markdown",
  "text/x-markdown", // .mdx and other markdown variants
  "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", // .xlsx
  "application/vnd.ms-excel", // .xls
  "application/vnd.openxmlformats-officedocument.wordprocessingml.document", // .docx
  "application/vnd.ms-word", // .doc
  "application/vnd.openxmlformats-officedocument.presentationml.presentation", // .pptx
  "application/vnd.ms-powerpoint", // .ppt
  // Audio formats
  "audio/mpeg", // .mp3
  "audio/wav", // .wav
  "audio/wave", // .wav alternative
  "audio/x-wav", // .wav alternative
  "audio/mp4", // .m4a
  "audio/x-m4a", // .m4a alternative
  "audio/ogg", // .ogg
  "audio/flac", // .flac
  "audio/x-flac", // .flac alternative
  // Video formats
  "video/mp4", // .mp4
  "video/x-msvideo", // .avi
  "video/quicktime", // .mov
  "video/webm", // .webm
  "video/x-matroska", // .mkv
  "video/x-flv", // .flv
  "video/x-ms-wmv", // .wmv
  "video/3gpp", // .3gp
]; // MIME type mappings for file upload validation
export const SupportedChatMimeTypes = [
  // Images
  "image/jpeg",
  "image/jpg",
  "image/png",
  "image/webp",
  "image/gif",
  "image/bmp",
  // Documents
  "application/pdf",
  "text/plain",
  "text/csv",
  "application/csv",
  "text/markdown",
  "text/x-markdown", // .mdx and other markdown variants
  "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", // .xlsx
  "application/vnd.ms-excel", // .xls
  "application/vnd.openxmlformats-officedocument.wordprocessingml.document", // .docx
  "application/vnd.ms-word", // .doc
  "application/vnd.openxmlformats-officedocument.presentationml.presentation", // .pptx
  "application/vnd.ms-powerpoint", // .ppt
  // Audio formats
  "audio/mpeg", // .mp3
  "audio/wav", // .wav
  "audio/wave", // .wav alternative
  "audio/x-wav", // .wav alternative
  "audio/x-m4a", // .m4a alternative
  "audio/ogg", // .ogg
  "audio/flac", // .flac
  "audio/x-flac", // .flac alternative
];

// File size limits by category (in bytes)
export const FileSizeLimits = {
  image: 10 * 1024 * 1024, // 10MB for images
  document: 25 * 1024 * 1024, // 25MB for documents
  audio: 50 * 1024 * 1024, // 50MB for audio files
  video: 100 * 1024 * 1024, // 100MB for video files
  default: 10 * 1024 * 1024, // 10MB default
};

// File extensions for accept attribute (to ensure browser file picker shows the right files)
export const SupportedFileExtensions = [
  // Images
  ".jpg",
  ".jpeg",
  ".png",
  ".webp",
  ".gif",
  ".bmp",
  // Documents
  ".pdf",
  ".txt",
  ".csv",
  ".md",
  ".mdx",
  ".markdown",
  ".xlsx",
  ".xls",
  ".docx",
  ".doc",
  ".pptx",
  ".ppt",
  // Audio formats
  ".mp3",
  ".wav",
  ".m4a",
  ".ogg",
  ".flac",
  // Video formats
  ".mp4",
  ".avi",
  ".mov",
  ".webm",
  ".mkv",
  ".flv",
  ".wmv",
  ".m4v",
  ".3gp",
];
// File extensions for accept attribute (to ensure browser file picker shows the right files)
export const SupportedChatFileExtensions = [
  // Images
  ".jpg",
  ".jpeg",
  ".png",
  ".webp",
  ".gif",
  ".bmp",
  // Documents
  ".pdf",
  ".txt",
  ".csv",
  ".md",
  ".mdx",
  ".markdown",
  ".xlsx",
  ".xls",
  ".docx",
  ".doc",
  ".pptx",
  ".ppt",
  // Audio formats
  ".mp3",
  ".wav",
  ".m4a",
  ".ogg",
  ".flac",
];

// Combined accept attribute for file inputs (MIME types + extensions for better browser support)
export const getFileAcceptAttribute = (): string => {
  return [...SupportedMimeTypes, ...SupportedFileExtensions].join(",");
};
// Combined accept attribute for file inputs (MIME types + extensions for better browser support)
export const getChatAcceptAttribute = (): string => {
  return [...SupportedChatMimeTypes, ...SupportedChatFileExtensions].join(",");
};

// Get file category based on MIME type
export const getFileCategory = (
  mimeType: string
): keyof typeof FileSizeLimits => {
  if (mimeType.startsWith("image/")) return "image";
  if (mimeType.startsWith("audio/")) return "audio";
  if (mimeType.startsWith("video/")) return "video";
  if (
    mimeType.includes("pdf") ||
    mimeType.includes("document") ||
    mimeType.includes("spreadsheet") ||
    mimeType.includes("text") ||
    mimeType.includes("csv") ||
    mimeType.includes("word") ||
    mimeType.includes("powerpoint") ||
    mimeType.includes("presentation") ||
    mimeType.includes("markdown")
  )
    return "document";
  return "default";
};
