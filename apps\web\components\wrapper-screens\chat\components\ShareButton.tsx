"use client";

import React, { useState, useEffect } from "react";
import {
  Share2,
  Co<PERSON>,
  CheckCircle,
  Globe,
  Lock,
  Calendar,
  Trash2,
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";

import toast from "react-hot-toast";

interface ShareInfo {
  shared: boolean;
  shareToken?: string;
  shareUrl?: string;
  isPublic?: boolean;
  expiresAt?: string;
  createdBy?: {
    id: string;
    name: string;
    email: string;
  };
  createdAt?: string;
}

interface ShareButtonProps {
  chatId: string;
  chatTitle?: string;
}

export const ShareButton: React.FC<ShareButtonProps> = ({
  chatId,
  chatTitle,
}) => {

  const [isOpen, setIsOpen] = useState(false);
  const [shareInfo, setShareInfo] = useState<ShareInfo>({ shared: false });
  const [isPublic, setIsPublic] = useState(false);
  const [expiresAt, setExpiresAt] = useState("");
  const [loading, setLoading] = useState(false);
  const [copied, setCopied] = useState(false);
  const [dateError, setDateError] = useState("");

  // Validate expiration date
  const validateExpirationDate = (dateValue: string): string => {
    if (!dateValue) return ""; // Empty is valid (no expiration)

    // Check for invalid date format or parsing issues
    const selectedDate = new Date(dateValue);
    if (isNaN(selectedDate.getTime())) {
      return "Please enter a valid date and time";
    }

    const now = new Date();

    // Check if date is in the past
    if (selectedDate <= now) {
      return "Expiration date must be in the future";
    }

    // Check if year is valid (4 digits, reasonable range)
    const year = selectedDate.getFullYear();
    if (year < 2024 || year > 2100) {
      return "Please enter a valid year (2024-2100)";
    }

    return "";
  };

  // Validate and format datetime input to ensure proper format
  const validateDateTimeInput = (value: string): string => {
    if (!value) return "";

    // Remove any extra characters and ensure proper format
    // datetime-local expects YYYY-MM-DDTHH:MM format
    const cleanValue = value.replace(/[^\d\-T:]/g, '');

    // Check if the year part has more than 4 digits
    const yearMatch = cleanValue.match(/^(\d{4,})/);
    if (yearMatch && yearMatch[1].length > 4) {
      // Truncate year to 4 digits
      return cleanValue.replace(/^(\d{4})\d*/, '$1');
    }

    // Ensure the format is correct (YYYY-MM-DDTHH:MM)
    const dateTimeRegex = /^(\d{4})-(\d{2})-(\d{2})T(\d{2}):(\d{2})$/;
    if (cleanValue && !dateTimeRegex.test(cleanValue)) {
      // If it's not in the correct format, try to parse and reformat
      try {
        const date = new Date(cleanValue);
        if (!isNaN(date.getTime())) {
          return formatDateTimeForInput(date.toISOString());
        }
      } catch (error) {
        // If parsing fails, return the cleaned value
      }
    }

    return cleanValue;
  };

  // Handle expiration date change with validation
  const handleExpirationDateChange = (value: string) => {
    // First, validate and clean the input format
    const cleanedValue = validateDateTimeInput(value);

    // Only set the value if it's valid or empty
    if (cleanedValue === "" || /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}$/.test(cleanedValue)) {
      setExpiresAt(cleanedValue);
    }

    // Then validate the date logic
    const error = validateExpirationDate(cleanedValue);
    setDateError(error);
  };

  // Handle input events to prevent invalid typing
  const handleDateTimeInput = (e: React.FormEvent<HTMLInputElement>) => {
    const input = e.currentTarget;
    const value = input.value;

    // Prevent year from exceeding 4 digits
    const yearMatch = value.match(/^(\d{4,})/);
    if (yearMatch && yearMatch[1].length > 4) {
      const correctedValue = value.replace(/^(\d{4})\d*/, '$1');
      input.value = correctedValue;
      setExpiresAt(correctedValue);
    }
  };

  // Format datetime for input (ensures proper YYYY-MM-DDTHH:MM format)
  const formatDateTimeForInput = (dateString: string): string => {
    if (!dateString) return "";

    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return "";

      // Ensure we get the local timezone formatted string
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');

      return `${year}-${month}-${day}T${hours}:${minutes}`;
    } catch (error) {
      console.error("Error formatting date for input:", error);
      return "";
    }
  };

  // Load existing share info when dialog opens
  useEffect(() => {
    if (isOpen) {
      loadShareInfo();
    }
  }, [isOpen, chatId]);

  const loadShareInfo = async () => {
    try {
      const response = await fetch(`/api/chat/${chatId}/share`);
      if (response.ok) {
        const data = await response.json();
        setShareInfo(data);
        setIsPublic(data.isPublic || false);
        setExpiresAt(
          data.expiresAt ? formatDateTimeForInput(data.expiresAt) : ""
        );
      }
    } catch (error) {
      console.error("Error loading share info:", error);
    }
  };

  const handleCreateShare = async () => {
    // Validate expiration date before submitting
    if (expiresAt) {
      const error = validateExpirationDate(expiresAt);
      if (error) {
        setDateError(error);
        toast.error(error);
        return;
      }
    }

    try {
      setLoading(true);
      const response = await fetch(`/api/chat/${chatId}/share`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          isPublic,
          expiresAt: expiresAt ? new Date(expiresAt).toISOString() : null,
        }),
      });

      if (response.ok) {
        const data = await response.json();
        setShareInfo({
          shared: true,
          shareToken: data.shareToken,
          shareUrl: data.shareUrl,
          isPublic: data.isPublic,
          expiresAt: data.expiresAt,
        });
        toast.success("Thread shared successfully");
      } else {
        toast.error("Failed to share thread");
      }
    } catch (error) {
      console.error("Error creating share:", error);
      toast.error("Failed to share thread");
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateShare = async () => {
    // Validate expiration date before submitting
    if (expiresAt) {
      const error = validateExpirationDate(expiresAt);
      if (error) {
        setDateError(error);
        toast.error(error);
        return;
      }
    }

    try {
      setLoading(true);
      const response = await fetch(`/api/chat/${chatId}/share`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          isPublic,
          expiresAt: expiresAt ? new Date(expiresAt).toISOString() : null,
        }),
      });

      if (response.ok) {
        const data = await response.json();
        setShareInfo({
          ...shareInfo,
          isPublic: data.isPublic,
          expiresAt: data.expiresAt,
        });
        toast.success("Share settings updated");
      } else {
        toast.error("Failed to update share settings");
      }
    } catch (error) {
      console.error("Error updating share:", error);
      toast.error("Failed to update share settings");
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteShare = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/chat/${chatId}/share`, {
        method: "DELETE",
      });

      if (response.ok) {
        setShareInfo({ shared: false });
        setIsPublic(false);
        setExpiresAt("");
        toast.success("Share link removed");
      } else {
        toast.error("Failed to remove share link");
      }
    } catch (error) {
      console.error("Error deleting share:", error);
      toast.error("Failed to remove share link");
    } finally {
      setLoading(false);
    }
  };

  const handleCopyLink = async () => {
    if (shareInfo.shareUrl) {
      try {
        await navigator.clipboard.writeText(shareInfo.shareUrl);
        setCopied(true);
        toast.success("Link copied to clipboard");
        setTimeout(() => setCopied(false), 2000);
      } catch (error) {
        toast.error("Failed to copy link");
      }
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString(undefined, {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm" className="flex items-center gap-2">
          <Share2 className="h-4 w-4" />
          Share
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Share2 className="h-5 w-5" />
            Share Thread
          </DialogTitle>
          <DialogDescription>
            {chatTitle ? `Share "${chatTitle}"` : "Share this conversation"}{" "}
            with others
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {shareInfo.shared ? (
            <>
              {/* Existing Share */}
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <Label className="text-sm font-medium">Share Link</Label>
                  <Badge variant={shareInfo.isPublic ? "default" : "secondary"}>
                    {shareInfo.isPublic ? (
                      <>
                        <Globe className="h-3 w-3 mr-1" />
                        Public
                      </>
                    ) : (
                      <>
                        <Lock className="h-3 w-3 mr-1" />
                        Private
                      </>
                    )}
                  </Badge>
                </div>

                <div className="flex gap-2">
                  <Input
                    value={shareInfo.shareUrl || ""}
                    readOnly
                    className="flex-1"
                  />
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleCopyLink}
                    className="flex items-center gap-1"
                  >
                    {copied ? (
                      <CheckCircle className="h-4 w-4 text-green-500" />
                    ) : (
                      <Copy className="h-4 w-4" />
                    )}
                  </Button>
                </div>

                {shareInfo.expiresAt && (
                  <div className="text-xs text-gray-500 flex items-center gap-1">
                    <Calendar className="h-3 w-3" />
                    Expires: {formatDate(shareInfo.expiresAt)}
                  </div>
                )}
              </div>

              {/* Share Settings */}
              <div className="space-y-3 pt-3 border-t">
                <div className="flex items-center justify-between">
                  <div>
                    <Label
                      htmlFor="public-toggle"
                      className="text-sm font-medium"
                    >
                      Public Access
                    </Label>
                    <p className="text-xs text-gray-500">
                      Anyone with the link can view
                    </p>
                  </div>
                  <Switch
                    id="public-toggle"
                    checked={isPublic}
                    onCheckedChange={setIsPublic}
                  />
                </div>

                <div>
                  <Label htmlFor="expires-at-update" className="text-sm font-medium">
                    Expiration (optional)
                  </Label>
                  <Input
                    id="expires-at-update"
                    type="datetime-local"
                    value={expiresAt}
                    onChange={(e) => handleExpirationDateChange(e.target.value)}
                    onInput={handleDateTimeInput}
                    className={`mt-1 ${dateError ? "border-red-500" : ""}`}
                    max={(() => {
                      const maxDate = new Date();
                      maxDate.setFullYear(maxDate.getFullYear() + 1);
                      return maxDate.toISOString().slice(0, 16);
                    })()}
                    min={new Date().toISOString().slice(0, 16)} // Prevent past dates
                    step="60" // Only allow minute precision (removes seconds)
                  />
                  {dateError && (
                    <p className="text-xs text-red-500 mt-1">{dateError}</p>
                  )}
                </div>
              </div>

              {/* Actions */}
              <div className="flex gap-2 pt-3 border-t">
                <Button
                  onClick={handleUpdateShare}
                  disabled={loading}
                  className="flex-1"
                >
                  Update Settings
                </Button>
                <Button
                  variant="outline"
                  onClick={handleDeleteShare}
                  disabled={loading}
                  className="flex items-center gap-1"
                >
                  <Trash2 className="h-4 w-4" />
                  Remove
                </Button>
              </div>
            </>
          ) : (
            <>
              {/* Create New Share */}
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div>
                    <Label
                      htmlFor="public-toggle"
                      className="text-sm font-medium"
                    >
                      Public Access
                    </Label>
                    <p className="text-xs text-gray-500">
                      Anyone with the link can view
                    </p>
                  </div>
                  <Switch
                    id="public-toggle"
                    checked={isPublic}
                    onCheckedChange={setIsPublic}
                  />
                </div>

                <div>
                  <Label htmlFor="expires-at" className="text-sm font-medium">
                    Expiration (optional)
                  </Label>
                  <Input
                    id="expires-at"
                    type="datetime-local"
                    value={expiresAt}
                    onChange={(e) => handleExpirationDateChange(e.target.value)}
                    onInput={handleDateTimeInput}
                    className={`mt-1 ${dateError ? "border-red-500" : ""}`}
                    max={(() => {
                      const maxDate = new Date();
                      maxDate.setFullYear(maxDate.getFullYear() + 1);
                      return maxDate.toISOString().slice(0, 16);
                    })()}
                    min={new Date().toISOString().slice(0, 16)} // Prevent past dates
                    step="60" // Only allow minute precision (removes seconds)
                  />
                  {dateError && (
                    <p className="text-xs text-red-500 mt-1">{dateError}</p>
                  )}
                </div>
              </div>

              <Button
                onClick={handleCreateShare}
                disabled={loading}
                className="w-full"
              >
                Create Share Link
              </Button>
            </>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};
