"use client";

import React from "react";
import { AlertCircle, Shield } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useLanguage } from "@/lib/language-context";

interface PartnerConsoleGuardProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  showStatus?: boolean;
}

export function PartnerConsoleGuard({
  children,
  fallback,
  showStatus = false,
}: PartnerConsoleGuardProps) {
  const { t } = useLanguage();
  const isPartnerConsole = process.env.NEXT_PUBLIC_IS_PARTNER_CONSOLE === "1";

  if (!isPartnerConsole) {
    if (fallback) {
      return <>{fallback}</>;
    }

    return (
      <Alert>
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>
          {t("settings.partnerConsole.partnerConsoleRequired")}
        </AlertTitle>
        <AlertDescription>
          {t("settings.partnerConsole.partnerConsoleRequiredDescription")}
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <>
      {showStatus && (
        <div className="mb-4">
          <Badge
            variant="outline"
            className="bg-green-50 text-green-700 border-green-200"
          >
            <Shield className="h-3 w-3 mr-1" />
            {t("settings.partnerConsole.enabled")}
          </Badge>
        </div>
      )}
      {children}
    </>
  );
}

export function PartnerConsoleStatus() {
  const { t } = useLanguage();
  const isPartnerConsole = process.env.NEXT_PUBLIC_IS_PARTNER_CONSOLE === "1";

  return (
    <Card className="mb-6">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Shield className="h-5 w-5" />
          {t("settings.partnerConsole.partnerConsoleStatus")}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="flex items-center gap-3">
          <div
            className={`h-3 w-3 rounded-full ${
              isPartnerConsole ? "bg-green-500" : "bg-red-500"
            }`}
          />
          <div>
            <p className="font-medium">
              {isPartnerConsole
                ? t("settings.partnerConsole.enabled")
                : t("settings.partnerConsole.disabled")}
            </p>
            <p className="text-sm text-muted-foreground">
              {isPartnerConsole
                ? t("settings.partnerConsole.whitelabelFeaturesAvailable")
                : t("settings.partnerConsole.enableWhitelabelFeatures")}
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

// Hook to check partner console status
export function usePartnerConsole() {
  const isPartnerConsole = process.env.NEXT_PUBLIC_IS_PARTNER_CONSOLE === "1";

  return {
    isPartnerConsole,
    isEnabled: isPartnerConsole,
    isDisabled: !isPartnerConsole,
  };
}

// Higher-order component for partner console protection
export function withPartnerConsole<T extends object>(
  Component: React.ComponentType<T>,
  fallback?: React.ReactNode
) {
  return function PartnerConsoleProtectedComponent(props: T) {
    return (
      <PartnerConsoleGuard fallback={fallback}>
        <Component {...props} />
      </PartnerConsoleGuard>
    );
  };
}
