import { NextResponse } from "next/server";
import { getToken } from "next-auth/jwt";
import db from "@/lib/shared-db";

/**
 * GET /api/roles/[id]
 * Get a specific custom role
 */
export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const roleId = params.id;
    if (!roleId) {
      return NextResponse.json(
        { error: "Role ID is required" },
        { status: 400 }
      );
    }

    const token = await getToken({ req: request as any });
    if (!token) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get the role
    const customRole = await db.customRole.findUnique({
      where: { id: roleId },
      include: {
        permissions: {
          include: {
            permission: true,
          },
        },
        workspaces: {
          include: {
            workspace: true,
          },
        },
      },
    });

    if (!customRole) {
      return NextResponse.json({ error: "Role not found" }, { status: 404 });
    }

    // Verify user has access to this tenant
    const membership = await db.membership.findFirst({
      where: {
        userId: token.sub,
        tenantId: customRole.tenantId,
      },
    });

    if (!membership) {
      return NextResponse.json(
        { error: "You don't have access to this tenant" },
        { status: 403 }
      );
    }

    return NextResponse.json({ customRole });
  } catch (error) {
    console.error("Error fetching custom role:", error);
    return NextResponse.json(
      { error: "Failed to fetch custom role" },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/roles/[id]
 * Update a custom role
 */
export async function PUT(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const roleId = params.id;
    if (!roleId) {
      return NextResponse.json(
        { error: "Role ID is required" },
        { status: 400 }
      );
    }

    const token = await getToken({ req: request as any });
    if (!token) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const { name, description, permissionIds, workspaceIds } = body;

    // Get the role
    const customRole = await db.customRole.findUnique({
      where: { id: roleId },
    });

    if (!customRole) {
      return NextResponse.json({ error: "Role not found" }, { status: 404 });
    }

    // Verify user has owner access to this tenant
    const membership = await db.membership.findFirst({
      where: {
        userId: token.sub,
        tenantId: customRole?.tenantId,
        role: "OWNER",
      },
    });

    if (!membership) {
      return NextResponse.json(
        { error: "Only owners can update custom roles" },
        { status: 403 }
      );
    }

    // Check if a different role with the same name already exists
    if (name) {
      const existingRole = await db.customRole.findFirst({
        where: {
          name,
          tenantId: customRole?.tenantId,
          id: { not: roleId },
        },
      });

      if (existingRole) {
        return NextResponse.json(
          { error: "A role with this name already exists" },
          { status: 400 }
        );
      }
    }

    // Update the role basic info
    const updateData: any = {};
    if (name !== undefined) updateData.name = name;
    if (description !== undefined) updateData.description = description;

    if (Object.keys(updateData).length > 0) {
      await db.customRole.update({
        where: { id: roleId },
        data: updateData,
      });
    }

    // Update permissions if provided
    if (permissionIds) {
      // Delete existing permissions
      await db.customRolePermission.deleteMany({
        where: { customRoleId: roleId },
      });

      // Add new permissions
      if (permissionIds.length > 0) {
        const permissionConnections = permissionIds.map(
          (permissionId: string) => ({
            permissionId,
            customRoleId: roleId,
          })
        );

        await db.customRolePermission.createMany({
          data: permissionConnections,
        });
      }
    }

    // Update workspace assignments if provided
    if (workspaceIds !== undefined) {
      // Get all workspaces to verify they exist and belong to the tenant
      const workspaces = await db.workspace.findMany({
        where: {
          id: { in: workspaceIds },
          tenantId: customRole.tenantId,
        },
      });

      if (workspaces.length !== workspaceIds.length) {
        return NextResponse.json(
          {
            error:
              "One or more workspaces not found or do not belong to this tenant",
          },
          { status: 400 }
        );
      }

      // Wrap all workspace updates in a transaction
      await db.$transaction(async (tx) => {
        // First, delete existing workspace associations
        await tx.customRoleWorkspace.deleteMany({
          where: {
            customRoleId: roleId,
          },
        });

        // Create new workspace associations
        for (const workspace of workspaces) {
          await tx.customRoleWorkspace.create({
            data: {
              customRoleId: roleId,
              workspaceId: workspace.id,
            },
          });
        }

        // Now, update all workspace members that use this role
        // Get all members with this role (both direct members and group members)
        const membersWithRole = await tx.membership.findMany({
          where: {
            customRoleId: roleId,
          },
        });

        // Get all groups with this role
        const groupsWithRole = await tx.group.findMany({
          where: {
            customRoleId: roleId,
          },
          include: {
            groupMembers: true,
            groupWorkspaces: {
              include: {
                workspace: true,
              },
            },
          },
        });

        // For groups with this role, we need to update their workspace assignments
        for (const group of groupsWithRole) {
          // Remove existing group workspace assignments
          await tx.groupWorkspace.deleteMany({
            where: {
              groupId: group.id,
            },
          });

          // Create new group workspace assignments based on the role's workspaces
          for (const workspace of workspaces) {
            await tx.groupWorkspace.create({
              data: {
                groupId: group.id,
                workspaceId: workspace.id,
                customRoleId: roleId,
              },
            });
          }

          // Update workspace access for all group members
          for (const groupMember of group.groupMembers) {
            const membership = await tx.membership.findFirst({
              where: {
                userId: groupMember.userId,
                tenantId: customRole.tenantId,
              },
            });

            if (membership) {
              // Remove existing workspace memberships for this user that were created through this group
              await tx.workspaceMember.deleteMany({
                where: {
                  userId: groupMember.userId,
                  customRoleId: roleId,
                  role: "CUSTOM",
                },
              });

              // Add new workspace memberships for the updated workspaces
              for (const workspace of workspaces) {
                const existingMember = await tx.workspaceMember.findFirst({
                  where: {
                    userId: groupMember.userId,
                    workspaceId: workspace.id,
                  },
                });

                if (!existingMember) {
                  await tx.workspaceMember.create({
                    data: {
                      userId: groupMember.userId,
                      workspaceId: workspace.id,
                      membershipId: membership.id,
                      customRoleId: roleId,
                      role: "CUSTOM",
                    },
                  });
                } else if (existingMember.customRoleId !== roleId) {
                  await tx.workspaceMember.update({
                    where: {
                      id: existingMember.id,
                    },
                    data: {
                      customRoleId: roleId,
                    },
                  });
                }
              }
            }
          }
        }

        // Handle direct members with this role
        for (const member of membersWithRole) {
          // Remove existing workspace memberships for this user that were created through this role
          await tx.workspaceMember.deleteMany({
            where: {
              userId: member.userId,
              customRoleId: roleId,
              NOT: {
                role: "CUSTOM", // Don't remove group-based access
              },
            },
          });

          // Add new workspace memberships for the updated workspaces
          for (const workspace of workspaces) {
            const existingMember = await tx.workspaceMember.findFirst({
              where: {
                userId: member.userId,
                workspaceId: workspace.id,
              },
            });

            if (!existingMember) {
              await tx.workspaceMember.create({
                data: {
                  userId: member.userId,
                  workspaceId: workspace.id,
                  membershipId: member.id,
                  customRoleId: roleId,
                  role: member.role,
                },
              });
            } else if (
              existingMember.customRoleId !== roleId &&
              existingMember.role !== "CUSTOM"
            ) {
              await tx.workspaceMember.update({
                where: {
                  id: existingMember.id,
                },
                data: {
                  customRoleId: roleId,
                },
              });
            }
          }
        }
      });
    }

    // Get the updated role with permissions and workspaces
    const finalRole = await db.customRole.findUnique({
      where: { id: roleId },
      include: {
        permissions: {
          include: {
            permission: true,
          },
        },
        workspaces: {
          include: {
            workspace: true,
          },
        },
      },
    });

    return NextResponse.json({ customRole: finalRole });
  } catch (error) {
    console.error("Error updating custom role:", error);
    return NextResponse.json(
      { error: "Failed to update custom role" },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/roles/[id]
 * Delete a custom role
 */
export async function DELETE(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const roleId = params.id;
    if (!roleId) {
      return NextResponse.json(
        { error: "Role ID is required" },
        { status: 400 }
      );
    }

    const token = await getToken({ req: request as any });
    if (!token) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get the role
    const customRole = await db.customRole.findUnique({
      where: { id: roleId },
    });

    if (!customRole) {
      return NextResponse.json({ error: "Role not found" }, { status: 404 });
    }

    // Verify user has owner access to this tenant
    const membership = await db.membership.findFirst({
      where: {
        userId: token.sub,
        tenantId: customRole.tenantId,
        role: "OWNER",
      },
    });

    if (!membership) {
      return NextResponse.json(
        { error: "Only owners can delete custom roles" },
        { status: 403 }
      );
    }

    // Check if the role is in use
    const membersUsingRole = await db.membership.count({
      where: { customRoleId: roleId },
    });

    if (membersUsingRole > 0) {
      return NextResponse.json(
        {
          error:
            "This role is currently assigned to members and cannot be deleted",
        },
        { status: 400 }
      );
    }

    // Delete the role and its permissions
    await db.customRolePermission.deleteMany({
      where: { customRoleId: roleId },
    });

    await db.customRole.delete({
      where: { id: roleId },
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error deleting custom role:", error);
    return NextResponse.json(
      { error: "Failed to delete custom role" },
      { status: 500 }
    );
  }
}
