/**
 * Comprehensive User Tracking Library for Chatbot SDK
 * Automatically collects user information during chat sessions
 */

class UserTracker {
  constructor() {
    this.trackingData = {};
    this.isInitialized = false;
  }

  // Initialize tracking and collect all available data
  async initialize() {
    if (this.isInitialized) return this.trackingData;

    try {
      // Collect basic browser and device information
      this.collectBasicInfo();
      
      // Collect screen and viewport information
      this.collectScreenInfo();
      
      // Collect page information
      this.collectPageInfo();
      
      // Collect UTM parameters
      this.collectUTMParameters();
      
      // Collect connection information
      await this.collectConnectionInfo();
      
      // Collect timezone information
      this.collectTimezoneInfo();
      
      // Collect user behavior data
      this.collectBehaviorData();
      
      this.isInitialized = true;
      return this.trackingData;
    } catch (error) {
      console.warn('Error initializing user tracking:', error);
      return this.trackingData;
    }
  }

  // Collect basic browser and device information
  collectBasicInfo() {
    const userAgent = navigator.userAgent;
    
    // Device type detection
    let deviceType = 'desktop';
    if (/Mobile|Android|iPhone|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent)) {
      deviceType = 'mobile';
    } else if (/Tablet|iPad|PlayBook|Silk/i.test(userAgent)) {
      deviceType = 'tablet';
    }

    // Operating system detection
    let operatingSystem = 'unknown';
    if (/Windows NT/i.test(userAgent)) {
      operatingSystem = 'Windows';
    } else if (/Mac OS X/i.test(userAgent)) {
      operatingSystem = 'macOS';
    } else if (/Linux/i.test(userAgent)) {
      operatingSystem = 'Linux';
    } else if (/Android/i.test(userAgent)) {
      operatingSystem = 'Android';
    } else if (/iPhone|iPad|iPod/i.test(userAgent)) {
      operatingSystem = 'iOS';
    }

    // Browser detection
    let browserName = 'unknown';
    let browserVersion = 'unknown';
    
    if (/Chrome/i.test(userAgent) && !/Edge|Edg/i.test(userAgent)) {
      browserName = 'Chrome';
      const match = userAgent.match(/Chrome\/([0-9.]+)/);
      browserVersion = match ? match[1] : 'unknown';
    } else if (/Firefox/i.test(userAgent)) {
      browserName = 'Firefox';
      const match = userAgent.match(/Firefox\/([0-9.]+)/);
      browserVersion = match ? match[1] : 'unknown';
    } else if (/Safari/i.test(userAgent) && !/Chrome/i.test(userAgent)) {
      browserName = 'Safari';
      const match = userAgent.match(/Version\/([0-9.]+)/);
      browserVersion = match ? match[1] : 'unknown';
    } else if (/Edge|Edg/i.test(userAgent)) {
      browserName = 'Edge';
      const match = userAgent.match(/Edge?\/([0-9.]+)/);
      browserVersion = match ? match[1] : 'unknown';
    }

    this.trackingData = {
      ...this.trackingData,
      userAgent,
      deviceType,
      operatingSystem,
      browserName,
      browserVersion,
      language: navigator.language || navigator.userLanguage,
    };
  }

  // Collect screen and viewport information
  collectScreenInfo() {
    this.trackingData = {
      ...this.trackingData,
      screenWidth: screen.width,
      screenHeight: screen.height,
      viewportWidth: window.innerWidth || document.documentElement.clientWidth,
      viewportHeight: window.innerHeight || document.documentElement.clientHeight,
    };
  }

  // Collect page information
  collectPageInfo() {
    this.trackingData = {
      ...this.trackingData,
      pageUrl: window.location.href,
      pageTitle: document.title,
      domain: window.location.hostname,
      referrer: document.referrer || undefined,
    };
  }

  // Collect UTM parameters from URL
  collectUTMParameters() {
    const urlParams = new URLSearchParams(window.location.search);
    
    const utmParams = {
      utmSource: urlParams.get('utm_source'),
      utmMedium: urlParams.get('utm_medium'),
      utmCampaign: urlParams.get('utm_campaign'),
      utmTerm: urlParams.get('utm_term'),
      utmContent: urlParams.get('utm_content'),
    };

    // Only add UTM parameters if they exist
    Object.keys(utmParams).forEach(key => {
      if (utmParams[key]) {
        this.trackingData[key] = utmParams[key];
      }
    });
  }

  // Collect connection information
  async collectConnectionInfo() {
    try {
      // Network Information API (if available)
      if ('connection' in navigator) {
        const connection = navigator.connection || navigator.mozConnection || navigator.webkitConnection;
        if (connection) {
          this.trackingData.connectionType = connection.effectiveType || connection.type;
        }
      }
    } catch (error) {
      console.warn('Could not collect connection info:', error);
    }
  }

  // Collect timezone information
  collectTimezoneInfo() {
    try {
      this.trackingData.timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
    } catch (error) {
      console.warn('Could not collect timezone info:', error);
    }
  }

  // Collect user behavior data
  collectBehaviorData() {
    try {
      // Check if user is returning (using localStorage)
      const visitCount = localStorage.getItem('chatbot_visit_count');
      const isReturning = visitCount !== null;
      const previousVisits = visitCount ? parseInt(visitCount, 10) : 0;
      
      // Update visit count
      localStorage.setItem('chatbot_visit_count', (previousVisits + 1).toString());
      
      // Session depth (simplified - could be enhanced with more sophisticated tracking)
      const sessionDepth = sessionStorage.getItem('chatbot_session_depth') || '1';
      sessionStorage.setItem('chatbot_session_depth', (parseInt(sessionDepth, 10) + 1).toString());

      this.trackingData = {
        ...this.trackingData,
        isReturning,
        previousVisits,
        sessionDepth: parseInt(sessionDepth, 10),
      };
    } catch (error) {
      console.warn('Could not collect behavior data:', error);
    }
  }

  // Get all collected tracking data
  getTrackingData() {
    return this.trackingData;
  }

  // Update tracking data with additional information
  updateTrackingData(additionalData) {
    this.trackingData = {
      ...this.trackingData,
      ...additionalData,
    };
  }

  // Track page view (call when user navigates to new page)
  trackPageView() {
    this.collectPageInfo();
    
    // Update session depth
    const currentDepth = parseInt(sessionStorage.getItem('chatbot_session_depth') || '1', 10);
    sessionStorage.setItem('chatbot_session_depth', (currentDepth + 1).toString());
    this.trackingData.sessionDepth = currentDepth + 1;
  }

  // Get fingerprint-like identifier (for anonymous user tracking)
  generateUserFingerprint() {
    const components = [
      this.trackingData.userAgent,
      this.trackingData.language,
      this.trackingData.timezone,
      this.trackingData.screenWidth,
      this.trackingData.screenHeight,
      navigator.platform,
      navigator.cookieEnabled,
    ];

    // Simple hash function
    let hash = 0;
    const str = components.join('|');
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    
    return Math.abs(hash).toString(36);
  }
}

// Global instance
window.UserTracker = new UserTracker();

// Auto-initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    window.UserTracker.initialize();
  });
} else {
  window.UserTracker.initialize();
}

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
  module.exports = UserTracker;
}
