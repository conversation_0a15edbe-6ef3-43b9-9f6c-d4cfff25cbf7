"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Loader2, Mail } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import Link from "next/link";
import { sendPasswordResetEmail } from "@/services/src/auth";
import { useTranslatedToast } from "@/hooks/use-translated-toast";
import { useLanguage } from "@/lib/language-context";

export default function ForgotPasswordPage() {
  const [isLoading, setIsLoading] = useState(false);
  const [emailSent, setEmailSent] = useState(false);
  const { t } = useLanguage();
  const toast = useTranslatedToast();

  // Form schema with translated error messages
  const formSchema = z.object({
    email: z.string().email({
      message: t("auth.validEmail"),
    }),
  });

  type FormValues = z.infer<typeof formSchema>;

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema as any),
    defaultValues: {
      email: "",
    },
  });

  const onSubmit = async (data: FormValues) => {
    setIsLoading(true);
    toast.loading(t("common.loading"));

    try {
      const response = await sendPasswordResetEmail(data.email);

      toast.dismiss();
      setIsLoading(false);

      if (response?.error) {
        toast.apiError(response.error);
        return;
      }

      // Show success message regardless of whether email exists (security best practice)
      setEmailSent(true);
      toast.success(t("common.emailSent"));
    } catch (error) {
      toast.dismiss();
      setIsLoading(false);
      console.error("Password reset error:", error);
      toast.error(t("common.tryAgain"));
    }
  };

  return (
    <div className="flex min-h-svh w-full items-center justify-center p-6 md:p-10">
      <div className="w-full max-w-sm">
        <Card>
          <CardHeader>
            <CardTitle className="text-2xl">
              {t("auth.resetPassword")}
            </CardTitle>
            <CardDescription>
              {emailSent
                ? t("auth.resetEmailSent")
                : t("auth.resetPasswordDescription")}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {emailSent ? (
              <div className="flex flex-col items-center text-center gap-4">
                <div className="bg-primary/10 p-3 rounded-full">
                  <Mail className="h-6 w-6 text-primary" />
                </div>
                <p className="text-sm text-muted-foreground">
                  {t("auth.resetEmailMessage", {
                    email: form.getValues().email,
                  })}
                </p>
                <div className="flex flex-col gap-2 w-full mt-2">
                  <Button
                    variant="outline"
                    className="w-full"
                    onClick={() => setEmailSent(false)}
                  >
                    {t("common.tryAnotherEmail")}
                  </Button>
                  <Button asChild className="w-full mt-2">
                    <Link href="/sign-in">{t("common.returnToSignIn")}</Link>
                  </Button>
                </div>
              </div>
            ) : (
              <Form {...form}>
                <form
                  onSubmit={form.handleSubmit(onSubmit)}
                  className="space-y-6"
                >
                  <FormField
                    control={form.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t("common.email")}</FormLabel>
                        <FormControl>
                          <Input
                            placeholder={t("auth.emailPlaceholder")}
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <Button type="submit" disabled={isLoading} className="w-full">
                    {isLoading ? (
                      <Loader2 className="animate-spin mr-2" />
                    ) : null}
                    {t("auth.sendResetInstructions")}
                  </Button>

                  <div className="mt-4 text-center text-sm">
                    {t("auth.alreadyHaveAccount")}{" "}
                    <Link
                      href="/sign-in"
                      className="underline underline-offset-4"
                    >
                      {t("common.signIn")}
                    </Link>
                  </div>
                </form>
              </Form>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
