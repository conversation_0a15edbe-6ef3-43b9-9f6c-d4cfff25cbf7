from fastapi import APIRouter, Depends, HTTPException
from datetime import datetime, timedelta
from typing import Optional
from src.services.usage_tracker import UsageTracker
from src.api.deps import get_db


router = APIRouter()

@router.get("/user/{user_id}")
async def get_user_stats(
    user_id: str,
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None,
    db_client = Depends(get_db)
):
    """Get usage statistics for a specific user."""
    if not start_date:
        start_date = datetime.utcnow() - timedelta(days=30)  # Default to last 30 days
    if not end_date:
        end_date = datetime.utcnow()

    tracker = UsageTracker(db_client)
    stats = await tracker.get_user_usage_stats(user_id, start_date, end_date)
    if not stats:
        raise HTTPException(status_code=404, detail="No usage data found")
    return stats

@router.get("/tenant/{tenant_id}")
async def get_tenant_stats(
    tenant_id: str,
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None,
    db_client = Depends(get_db)
):
    """Get usage statistics for a specific tenant."""
    if not start_date:
        start_date = datetime.utcnow() - timedelta(days=30)
    if not end_date:
        end_date = datetime.utcnow()
        
    tracker = UsageTracker(db_client)
    stats = await tracker.get_tenant_usage_stats(tenant_id, start_date, end_date)
    if not stats:
        raise HTTPException(status_code=404, detail="No usage data found")
    return stats