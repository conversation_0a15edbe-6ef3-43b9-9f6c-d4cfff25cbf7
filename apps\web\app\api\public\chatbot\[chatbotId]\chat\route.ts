import { NextRequest, NextResponse } from "next/server";
import db from "@/lib/shared-db";
import { validateChatbotSecurity } from "@/lib/security/chatbot-security";
import {
  applyCORSHeaders,
  handleCORSPreflight,
  createChatbotCORSConfig,
} from "@/lib/security/cors";
import { hashIdentifier } from "@/lib/rate-limiter";
import crypto from "crypto";
import { publicChatService } from "@/services/public-chat";
import { collectUserTrackingData } from "@/lib/services/user-tracking";
import { checkUserBlockedInTenant } from "@/lib/services/user-blocking";

// Use shared utility function for user blocking checks
// This ensures consistency across all endpoints

interface ChatbotChatRequest {
  message: string;
  sessionToken?: string;
  images?: string[];
  stream?: boolean;
  searchModes?: string[];
  userTrackingData?: any;
  metadata?: {
    domain?: string;
    userAgent?: string;
    referrer?: string;
    // Page information
    pageUrl?: string;
    pageTitle?: string;
    // Screen and viewport
    screenWidth?: number;
    screenHeight?: number;
    viewportWidth?: number;
    viewportHeight?: number;
    // Connection info
    connectionType?: string;
    // Behavior tracking
    sessionDepth?: number;
    isReturning?: boolean;
    previousVisits?: number;
    // UTM parameters
    utmSource?: string;
    utmMedium?: string;
    utmCampaign?: string;
    utmTerm?: string;
    utmContent?: string;
  };
}

export async function POST(
  request: NextRequest,
  { params }: { params: { chatbotId: string } }
) {
  let sessionToken: string | undefined;
  const { chatbotId } = params;

  try {
    const body: ChatbotChatRequest = await request.json();
    const {
      message,
      sessionToken: bodySessionToken,
      images,
      metadata,
      searchModes = [],
      userTrackingData,
      stream = false,
    } = body;

    sessionToken = bodySessionToken;

    // Validate required fields
    if (!message || !chatbotId) {
      const response = NextResponse.json(
        { error: "Missing required fields: message, chatbotId" },
        { status: 400 }
      );
      return applyCORSHeaders(response, request, createChatbotCORSConfig({}));
    }

    // Get API key from Authorization header
    const authHeader = request.headers.get("authorization");
    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      const response = NextResponse.json(
        { error: "Missing or invalid API key" },
        { status: 401 }
      );
      return applyCORSHeaders(response, request, createChatbotCORSConfig({}));
    }

    const apiKey = authHeader.substring(7);

    // Validate security and authenticate the request
    const securityResult = await validateChatbotSecurity(
      request,
      chatbotId,
      apiKey
    );

    if (!securityResult.success) {
      const response = NextResponse.json(
        { error: securityResult.error },
        { status: securityResult.statusCode || 500 }
      );

      // Apply CORS headers
      return applyCORSHeaders(response, request, createChatbotCORSConfig({}));
    }

    const { chatbot } = securityResult;

    // Get client IP and hash it for privacy
    const clientIP =
      request.headers.get("x-forwarded-for")?.split(",")[0].trim() ||
      request.headers.get("x-real-ip") ||
      "unknown";

    const hashedIP = hashIdentifier(clientIP);

    // Check if user is blocked before proceeding
    const isUserBlocked = await checkUserBlockedInTenant(
      chatbot.tenantId,
      securityResult?.user?.userId,
      securityResult?.user?.email,
      hashedIP
    );

    if (isUserBlocked.blocked) {
      // Try to increment error count in existing session if available
      if (sessionToken) {
        try {
          await db.chatbotSession.updateMany({
            where: {
              sessionToken: sessionToken,
              chatbotId: chatbot.id,
            },
            data: {
              errorCount: {
                increment: 1,
              },
              lastActivityAt: new Date(),
              metadata: {
                lastError: {
                  type: "blocked_access_attempt",
                  blockType: isUserBlocked.blockType,
                  blockReason: isUserBlocked.reason,
                  timestamp: new Date().toISOString(),
                },
              },
            },
          });
        } catch (sessionError) {
          console.error(
            "Failed to update session error count for blocked access:",
            sessionError
          );
        }
      }

      const response = NextResponse.json(
        {
          error: "Access denied",
          message:
            isUserBlocked.reason ||
            "You have been blocked from using this chatbot",
          blockInfo: {
            type: isUserBlocked.blockType,
            expiresAt: isUserBlocked.expiresAt,
          },
        },
        { status: 403 }
      );
      return applyCORSHeaders(
        response,
        request,
        createChatbotCORSConfig(chatbot)
      );
    }

    // Generate or validate session token
    let currentSessionToken = sessionToken;
    if (!currentSessionToken) {
      currentSessionToken = crypto.randomUUID();
    }

    // Find or create session (with tenant validation)
    let session = await db.chatbotSession.findFirst({
      where: {
        sessionToken: currentSessionToken,
        tenantId: chatbot.tenantId, // Ensure tenant isolation
      },
    });

    if (!session) {
      const trackingData = await collectUserTrackingData(request, {
        ...userTrackingData,
        ...metadata,
      });

      session = await db.chatbotSession.create({
        data: {
          sessionToken: currentSessionToken,
          userId: securityResult?.user?.userId ?? securityResult?.user?.id,
          chatbotId: chatbot.id,
          tenantId: chatbot.tenantId, // Add tenantId for multi-tenant isolation

          // Basic session metadata
          domain:
            trackingData.domain || request.headers.get("origin") || "unknown",
          userAgent:
            trackingData.userAgent ||
            request.headers.get("user-agent") ||
            "unknown",
          ipAddress: trackingData.ipAddress || hashedIP,
          referrer: trackingData.referrer,

          // Geographic information
          country: trackingData.country ?? request.geo?.country,
          city: trackingData.city ?? request.geo?.city,
          region: trackingData.region ?? request.geo?.region,
          timezone: trackingData.timezone,

          // Device and browser information
          deviceType: trackingData.deviceType,
          operatingSystem: trackingData.operatingSystem,
          browserName: trackingData.browserName,
          browserVersion: trackingData.browserVersion,
          screenWidth: trackingData.screenWidth,
          screenHeight: trackingData.screenHeight,
          viewportWidth: trackingData.viewportWidth,
          viewportHeight: trackingData.viewportHeight,

          // Network information
          connectionType: trackingData.connectionType,
          isp: trackingData.isp,
          organization: trackingData.organization,

          // Page and language information
          pageUrl: trackingData.pageUrl,
          pageTitle: trackingData.pageTitle,
          language: trackingData.language,

          // UTM parameters
          utmSource: trackingData.utmSource,
          utmMedium: trackingData.utmMedium,
          utmCampaign: trackingData.utmCampaign,
          utmTerm: trackingData.utmTerm,
          utmContent: trackingData.utmContent,

          // User behavior tracking
          sessionDepth: trackingData.sessionDepth || 1,
          isReturning: trackingData.isReturning || false,
          previousVisits: trackingData.previousVisits || 0,

          // Session tracking
          messagesCount: 1,
          startedAt: new Date(),
          lastActivityAt: new Date(),

          // Additional metadata
          metadata: trackingData.metadata,
        },
      });
    } else {
      // Update existing session with new tracking data
      const trackingData = await collectUserTrackingData(request, {
        ...userTrackingData,
        ...metadata,
      });

      await db.chatbotSession.update({
        where: { id: session.id },
        data: {
          messagesCount: { increment: 1 },
          lastActivityAt: new Date(),

          // Update page information if provided
          pageUrl: trackingData.pageUrl || session.pageUrl,
          pageTitle: trackingData.pageTitle || session.pageTitle,
          sessionDepth: trackingData.sessionDepth || session.sessionDepth,

          // Update viewport if changed
          viewportWidth: trackingData.viewportWidth || session.viewportWidth,
          viewportHeight: trackingData.viewportHeight || session.viewportHeight,

          // Update connection type if changed
          connectionType: trackingData.connectionType || session.connectionType,

          // Merge additional metadata
          metadata: {
            ...(session.metadata as any),
            ...trackingData.metadata,
            lastMessageAt: new Date().toISOString(),
          },
        },
      });
    }

    // Prepare chat query for CopilotKit
    const chatQuery = {
      question: message.trim(),
      message,
      stream,
      search_mode:
        searchModes?.length > 2
          ? "hybrid"
          : (searchModes?.[0] as any) ?? "internal", // Use first allowed search mode
      include_web_results: searchModes?.includes("web"),
      max_tokens: chatbot.maxTokens,
      temperature: chatbot.temperature,
      images: images,
    };

    // Call CopilotKit chat service
    const startTime = Date.now();
    // Check if streaming is requested
    const isStreaming = chatQuery.stream;

    if (isStreaming) {
      // Handle streaming response
      const stream = publicChatService.streamChat(
        chatQuery,
        chatbot.id,
        chatbot.userId,
        chatbot.tenantId,
        chatbot.user.name || "Anonymous",
        chatbot.apiKey
      );

      // Create a readable stream for the response
      const encoder = new TextEncoder();

      const readable = new ReadableStream({
        async start(controller) {
          let fullResponse = "";
          let isClosed = false;

          // Helper function to safely enqueue data
          const safeEnqueue = (data: Uint8Array) => {
            if (!isClosed) {
              try {
                // Check if controller is still valid before enqueueing
                if (controller && typeof controller.enqueue === "function") {
                  controller.enqueue(data);
                } else {
                  console.warn(
                    "Controller is invalid or missing enqueue method"
                  );
                  isClosed = true;
                }
              } catch (error) {
                console.error("Error enqueueing data:", error);
                isClosed = true;
              }
            }
          };

          // Helper function to safely close controller
          const safeClose = () => {
            if (!isClosed) {
              try {
                // Check if controller is still valid before closing
                if (controller && typeof controller.close === "function") {
                  controller.close();
                  isClosed = true;
                } else {
                  console.warn("Controller is invalid or missing close method");
                  isClosed = true;
                }
              } catch (error) {
                console.error("Error closing controller:", error);
                isClosed = true;
              }
            }
          };

          // Handle client disconnection
          const abortHandler = () => {
            console.log("Client disconnected, closing stream");
            safeClose();
          };

          try {
            // Add abort listener if request has signal
            if (request.signal) {
              request.signal.addEventListener("abort", abortHandler);
            }

            for await (const chunk of stream) {
              // Check if controller is closed before processing
              if (isClosed) {
                console.log("Stream controller is closed, breaking from loop");
                break;
              }

              try {
                // Handle content chunks
                if (chunk?.answer_chunk || chunk?.answer) {
                  fullResponse += chunk?.answer_chunk || chunk?.answer;

                  // Send chunk to client
                  const data =
                    JSON.stringify({
                      type: "content",
                      content: chunk?.answer_chunk || chunk?.answer,
                      sessionToken: session.sessionToken,
                    }) + "\n";

                  safeEnqueue(encoder.encode(`data: ${data}\n\n`));
                }

                // Handle sources
                if (chunk.sources && chunk.sources.length > 0) {
                  const data =
                    JSON.stringify({
                      type: "sources",
                      sources: chunk.sources,
                      sessionToken: session.sessionToken,
                    }) + "\n";

                  safeEnqueue(encoder.encode(`data: ${data}\n\n`));
                }
              } catch (chunkError) {
                console.error("Error processing chunk:", chunkError);
                // If there's an error processing a chunk, close the stream
                safeClose();
                break;
              }

              // Handle completion
              if (chunk.done) {
                try {
                  // Save the complete response to database
                  await db.chatbotMessage.create({
                    data: {
                      content: chatQuery.question || "No question provided",
                      role: "user",
                      sessionId: session.id,
                      chatbotId,
                      tenantId: chatbot.tenantId,
                      metadata: {
                        responseTime: Date.now() - startTime,
                      },
                    },
                  });
                  await db.chatbotMessage.create({
                    data: {
                      content:
                        fullResponse ||
                        chunk?.answer ||
                        "No response generated",
                      role: "assistant",
                      sessionId: session.id,
                      chatbotId,
                      tenantId: chatbot.tenantId,
                      metadata: {
                        responseTime: Date.now() - startTime,
                        sources: chunk.sources || [],
                        tools_used: chunk.tools_used || [],
                      },
                    },
                  });

                  // Update session and chatbot usage statistics
                  await db.chatbotSession.update({
                    where: { id: session.id },
                    data: {
                      averageResponseTime: Date.now() - startTime,
                    },
                  });

                  await db.chatbot.update({
                    where: { id: chatbot.id },
                    data: {
                      usageCount: { increment: 1 },
                      monthlyUsage: { increment: 1 },
                      lastUsedAt: new Date(),
                    },
                  });

                  // Send completion signal
                  const data =
                    JSON.stringify({
                      type: "done",
                      sessionToken: session.sessionToken,
                      responseTime: Date.now() - startTime,
                      fullResponse: fullResponse || chunk?.answer,
                    }) + "\n";

                  safeEnqueue(encoder.encode(`data: ${data}\n\n`));
                } catch (dbError) {
                  console.error("Error saving to database:", dbError);
                  // Still send completion signal even if DB save fails
                  const data =
                    JSON.stringify({
                      type: "done",
                      sessionToken: session.sessionToken,
                      responseTime: Date.now() - startTime,
                      fullResponse: fullResponse || chunk?.answer,
                    }) + "\n";

                  safeEnqueue(encoder.encode(`data: ${data}\n\n`));
                }

                safeClose();
                break;
              }

              // Handle errors
              if (chunk.error) {
                try {
                  const errorData =
                    JSON.stringify({
                      type: "error",
                      error: chunk.error,
                      sessionToken: session.sessionToken,
                    }) + "\n";
                  safeEnqueue(encoder.encode(`data: ${errorData}\n\n`));
                } catch (errorHandlingError) {
                  console.error(
                    "Error handling chunk error:",
                    errorHandlingError
                  );
                }

                safeClose();
                break;
              }
            }
          } catch (error) {
            console.error("Streaming error:", error);
            const errorData =
              JSON.stringify({
                type: "error",
                error: "Streaming failed",
              }) + "\n";
            safeEnqueue(encoder.encode(`data: ${errorData}\n\n`));
            safeClose();
          } finally {
            // Clean up abort listener
            if (request.signal) {
              request.signal.removeEventListener("abort", abortHandler);
            }
          }
        },
      });

      return new Response(readable, {
        headers: {
          "Content-Type": "text/event-stream",
          "Cache-Control": "no-cache",
          Connection: "keep-alive",
          "Access-Control-Allow-Origin": "*",
          "Access-Control-Allow-Headers": "Content-Type, Authorization",
        },
      });
    } else {
      // Handle non-streaming response
      try {
        const chatResponse = await publicChatService.chat(
          chatQuery,
          chatbot.id,
          chatbot.userId,
          chatbot.tenantId,
          chatbot.user.name || "Anonymous",
          chatbot.apiKey
        );

        // Save assistant message to database
        await db.chatbotMessage.create({
          data: {
            content:
              chatResponse.answer ||
              "I apologize, but I couldn't generate a response.",
            role: "assistant",
            sessionId: session.id,
            chatbotId,
            tenantId: chatbot.tenantId,
            metadata: {
              responseTime: Date.now() - startTime,
              sources: chatResponse.sources || [],
            },
          },
        });

        // Update session and chatbot usage statistics
        await db.chatbotSession.update({
          where: { id: session.id },
          data: {
            averageResponseTime: Date.now() - startTime,
          },
        });

        await db.chatbot.update({
          where: { id: chatbot.id },
          data: {
            usageCount: { increment: 1 },
            monthlyUsage: { increment: 1 },
            lastUsedAt: new Date(),
          },
        });

        const response = NextResponse.json({
          response:
            chatResponse.answer ||
            "I apologize, but I couldn't generate a response.",
          sessionToken: session.sessionToken,
          sources: chatResponse.sources || [],
          responseTime: Date.now() - startTime,
        });

        return applyCORSHeaders(
          response,
          request,
          createChatbotCORSConfig(chatbot)
        );
      } catch (chatError) {
        console.error("Error in chat service:", chatError);

        // Increment error count for chat service failures
        await db.chatbotSession.updateMany({
          where: {
            sessionToken: session.sessionToken,
            chatbotId: chatbotId,
          },
          data: {
            errorCount: {
              increment: 1,
            },
            lastActivityAt: new Date(),
            metadata: {
              lastError: {
                message:
                  chatError instanceof Error
                    ? chatError.message
                    : String(chatError),
                timestamp: new Date().toISOString(),
                type: "chat_service_error",
              },
            },
          },
        });

        // Return error response
        const response = NextResponse.json(
          { error: "Failed to generate response" },
          { status: 500 }
        );
        return applyCORSHeaders(
          response,
          request,
          createChatbotCORSConfig(chatbot)
        );
      }
    }

    // Update session with response time (moved inside the if/else blocks above)
    // Update chatbot usage statistics (moved inside the if/else blocks above)
  } catch (error) {
    console.error("Error in public chatbot chat:", error);

    // Increment error count for the session if we have a session token
    if (sessionToken) {
      try {
        await db.chatbotSession.updateMany({
          where: {
            sessionToken: sessionToken,
            chatbotId: chatbotId,
          },
          data: {
            errorCount: {
              increment: 1,
            },
            lastActivityAt: new Date(),
            metadata: {
              lastError: {
                message: error instanceof Error ? error.message : String(error),
                timestamp: new Date().toISOString(),
                type: "chat_api_error",
              },
            },
          },
        });
      } catch (dbError) {
        console.error("Failed to update error count:", dbError);
      }
    }

    const response = NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
    return applyCORSHeaders(response, request, createChatbotCORSConfig({}));
  }
}

// GET endpoint to retrieve chat history
export async function GET(
  request: NextRequest,
  { params }: { params: { chatbotId: string } }
) {
  try {
    const { chatbotId } = params;
    const { searchParams } = new URL(request.url);
    const sessionToken = searchParams.get("sessionToken");

    // Get API key from Authorization header
    const authHeader = request.headers.get("authorization");
    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      const response = NextResponse.json(
        { error: "Missing or invalid API key" },
        { status: 401 }
      );
      return applyCORSHeaders(response, request, createChatbotCORSConfig({}));
    }

    const apiKey = authHeader.substring(7);

    // Validate chatbot and API key
    const chatbot = await db.chatbot.findUnique({
      where: { id: chatbotId },
    });

    if (!chatbot || chatbot.apiKey !== apiKey) {
      const response = NextResponse.json(
        { error: "Invalid chatbot or API key" },
        { status: 401 }
      );
      return applyCORSHeaders(response, request, createChatbotCORSConfig({}));
    }

    if (!sessionToken) {
      const response = NextResponse.json(
        { messages: [], sessionToken: null },
        { status: 200 }
      );
      return applyCORSHeaders(
        response,
        request,
        createChatbotCORSConfig(chatbot)
      );
    }

    // Get chat session with messages (with tenant validation)
    const session = await db.chatbotSession.findFirst({
      where: {
        sessionToken,
        tenantId: chatbot.tenantId, // Ensure tenant isolation
      },
      include: {
        messages: {
          orderBy: { createdAt: "asc" },
          select: {
            id: true,
            content: true,
            role: true,
            createdAt: true,
            metadata: true,
          },
        },
      },
    });

    if (!session) {
      const response = NextResponse.json(
        { messages: [], sessionToken },
        { status: 200 }
      );
      return applyCORSHeaders(
        response,
        request,
        createChatbotCORSConfig(chatbot)
      );
    }

    const response = NextResponse.json({
      messages: session.messages,
      sessionToken: session.sessionToken,
      messagesCount: session.messagesCount,
    });

    return applyCORSHeaders(
      response,
      request,
      createChatbotCORSConfig(chatbot)
    );
  } catch (error) {
    console.error("Error retrieving chat history:", error);
    const response = NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
    return applyCORSHeaders(response, request, createChatbotCORSConfig({}));
  }
}

// Handle CORS preflight requests
export async function OPTIONS(request: NextRequest) {
  return handleCORSPreflight(request, {
    allowedMethods: ["GET", "POST", "OPTIONS"],
    allowedHeaders: [
      "Content-Type",
      "Authorization",
      "X-Requested-With",
      "X-User-Token",
    ],
    maxAge: 86400,
  });
}
