"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { useLanguage } from "@/lib/language-context";
import { LLMScopeSettings } from "@/types/llm-scope";
import {
  updateLLMScopeSettings,
  getLLMScopeSettings,
} from "@/services/src/llm-scope";
import toast from "react-hot-toast";
import {
  Loader2,
  Shield,
  Globe,
  Database,
  Zap,
  Info,
  RefreshCw,
  Server,
  Brain,
} from "lucide-react";

const formSchema = z.object({
  llmScope: z.array(z.string()).default(["INTERNAL_ONLY"]),
  scopeLevel: z.enum(["internal_only", "full_access"]),
  fullAccessOptions: z
    .array(z.enum(["web", "mcp", "hybrid", "deep_search"]))
    .default([]),
});

interface LLMScopeSettingsPageProps {
  settings: LLMScopeSettings | null;
  tenantId: string;
  userRole: string;
}

export default function LLMScopeSettingsPage({
  settings,
  tenantId,
  userRole,
}: LLMScopeSettingsPageProps) {
  const { t } = useLanguage();
  const [isLoading, setIsLoading] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [isInitialLoading, setIsInitialLoading] = useState(!settings);
  const [currentSettings, setCurrentSettings] = useState(settings);

  const isOwner = userRole === "OWNER" || userRole === "ADMIN";
  const canModify = isOwner;

  // Define available options as a constant list
  const AVAILABLE_OPTIONS = ["web", "mcp", "hybrid", "deep_search"] as const;

  type OptionValue = (typeof AVAILABLE_OPTIONS)[number];

  // Helper function to convert scope array to display string
  const getScopeDisplayString = (scopeArray: string[]): string => {
    if (scopeArray.length === 1 && scopeArray[0] === "INTERNAL_ONLY") {
      return "Internal Only";
    }

    const displayParts: string[] = [];
    if (scopeArray.includes("web")) displayParts.push("Web");
    if (scopeArray.includes("mcp")) displayParts.push("MCP");
    if (scopeArray.includes("hybrid")) displayParts.push("Hybrid");
    if (scopeArray.includes("deep_search")) displayParts.push("Deep Search");

    return displayParts.length > 0
      ? displayParts.join(" + ")
      : "Custom Configuration";
  };

  // Helper function to determine scope level and options from array format
  const getScopeLevelAndOptions = (scopeArray: string[]) => {
    // If only INTERNAL_ONLY is present, it's internal only mode
    if (scopeArray.length === 1 && scopeArray[0] === "INTERNAL_ONLY") {
      return {
        scopeLevel: "internal_only" as const,
        fullAccessOptions: [] as OptionValue[],
      };
    } else {
      // For any other combination, it's full access mode
      const options: OptionValue[] = [];

      // Map the scope array to our UI options
      if (scopeArray.includes("EXTERNAL_ONLY") || scopeArray.includes("web")) {
        options.push("web");
      }
      if (scopeArray.includes("MCP_ONLY") || scopeArray.includes("mcp")) {
        options.push("mcp");
      }
      if (scopeArray.includes("HYBRID") || scopeArray.includes("hybrid")) {
        options.push("hybrid");
      }
      if (
        scopeArray.includes("FULL_ACCESS") ||
        scopeArray.includes("deep_search")
      ) {
        options.push("deep_search");
      }

      return { scopeLevel: "full_access" as const, fullAccessOptions: options };
    }
  };

  // Helper function to convert form values to array format
  const convertToLLMScopeArray = (
    scopeLevel: string,
    fullAccessOptions: OptionValue[] = []
  ): string[] => {
    if (scopeLevel === "internal_only") {
      return ["INTERNAL_ONLY"];
    }

    // For full access, convert UI options to database format
    const scopeArray: string[] = [];

    // If no options are selected, default to INTERNAL_ONLY
    if (fullAccessOptions.length === 0) {
      return ["INTERNAL_ONLY"];
    }

    // Add each selected option to the array
    fullAccessOptions.forEach((option) => {
      switch (option) {
        case "web":
          scopeArray.push("web");
          break;
        case "mcp":
          scopeArray.push("mcp");
          break;
        case "hybrid":
          scopeArray.push("hybrid");
          break;
        case "deep_search":
          scopeArray.push("deep_search");
          break;
      }
    });

    return scopeArray;
  };

  // Create schema with translations
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema as any),
    defaultValues: {
      llmScope: settings?.llmScope || ["INTERNAL_ONLY"],
      ...getScopeLevelAndOptions(settings?.llmScope || ["INTERNAL_ONLY"]),
    },
  });

  // Client-side fallback: fetch settings if not provided by server
  useEffect(() => {
    async function fetchSettingsClientSide() {
      if (!settings && tenantId && isInitialLoading) {
        setIsInitialLoading(true);
        try {
          const clientSettings = await getLLMScopeSettings(tenantId);

          if (clientSettings) {
            setCurrentSettings(clientSettings);
            form.reset({
              llmScope: clientSettings.llmScope,
              ...getScopeLevelAndOptions(clientSettings.llmScope),
            });
          }
        } catch (error) {
          console.error("Error fetching settings client-side:", error);
        } finally {
          setIsInitialLoading(false);
        }
      }
    }

    fetchSettingsClientSide();
  }, [settings, tenantId, isInitialLoading, form]);

  // Update form when settings change - only on initial load
  useEffect(() => {
    if (settings && isInitialLoading) {
      const newLlmScope = settings.llmScope || ["INTERNAL_ONLY"];
      form.reset({
        llmScope: newLlmScope,
        ...getScopeLevelAndOptions(newLlmScope),
      });

      setCurrentSettings(settings);
      setIsInitialLoading(false);
    }
  }, [settings, form, isInitialLoading]);

  // Manual refresh function
  async function refreshSettings() {
    setIsRefreshing(true);
    try {
      const freshSettings = await getLLMScopeSettings(tenantId);

      if (freshSettings) {
        setCurrentSettings(freshSettings);
        form.reset({
          llmScope: freshSettings.llmScope,
          ...getScopeLevelAndOptions(freshSettings.llmScope),
        });
        toast.success("Settings refreshed successfully");
      } else {
        toast.error("Failed to refresh settings");
      }
    } catch (error) {
      console.error("Error refreshing settings:", error);
      toast.error("Error refreshing settings");
    } finally {
      setIsRefreshing(false);
    }
  }

  const watchedScopeLevel = form.watch("scopeLevel");

  // Remove this useEffect - it's causing conflicts with user selections

  async function onSubmit(values: z.infer<typeof formSchema>) {
    if (!canModify) {
      toast.error(t("llmScope.ownerOnlyError"));
      return;
    }
    setIsLoading(true);
    try {
      // Convert form values to array format
      const finalLLMScopeArray = convertToLLMScopeArray(
        values.scopeLevel,
        values.fullAccessOptions
      );

      const result = await updateLLMScopeSettings(tenantId, {
        llmScope: finalLLMScopeArray,
      });

      if (result.success) {
        toast.success(result.message || t("llmScope.updateSuccess"));
        setCurrentSettings(result.settings || null);

        // Update the llmScope field but preserve user's checkbox selections
        if (result.settings) {
          form.setValue("llmScope", result.settings.llmScope);
        }
      } else {
        toast.error(result.message || t("llmScope.updateFailed"));
      }
    } catch (error) {
      toast.error(t("llmScope.updateError"));
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Shield className="h-5 w-5" />
              {t("llmScope.configuration")}
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={refreshSettings}
              disabled={isRefreshing}
              className="flex items-center gap-2"
            >
              <RefreshCw
                className={`h-4 w-4 ${isRefreshing ? "animate-spin" : ""}`}
              />
              {t("llmScope.refresh")}
            </Button>
          </CardTitle>
          <CardDescription>{t("llmScope.description")}</CardDescription>
        </CardHeader>
        <CardContent>
          {!canModify && (
            <Alert className="mb-6">
              <Info className="h-4 w-4" />
              <AlertDescription>
                {t("llmScope.ownerOnlyMessage")}{" "}
                <Badge variant="outline">
                  {getScopeDisplayString(
                    currentSettings?.llmScope || ["INTERNAL_ONLY"]
                  )}
                </Badge>
              </AlertDescription>
            </Alert>
          )}
          {/* Loading indicator */}
          {isInitialLoading && (
            <Alert className="mb-6">
              <Loader2 className="h-4 w-4 animate-spin" />
              <AlertDescription>
                {t("llmScope.loadingSettings")}
              </AlertDescription>
            </Alert>
          )}

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              {/* Scope Level Selection */}
              <FormField
                control={form.control}
                name="scopeLevel"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t("llmScope.configuration")}</FormLabel>
                    <Select
                      disabled={!canModify || isLoading}
                      onValueChange={field.onChange}
                      value={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select scope level" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="internal_only">
                          <div className="flex items-center gap-2">
                            <Database className="h-4 w-4" />
                            {t("llmScope.scopeOptions.internalOnly")}
                          </div>
                        </SelectItem>
                        <SelectItem value="full_access">
                          <div className="flex items-center gap-2">
                            <Zap className="h-4 w-4" />
                            {t("llmScope.scopeOptions.fullLLMScope")}
                          </div>
                        </SelectItem>
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      {field.value === "internal_only"
                        ? t("llmScope.scopeOptions.internalOnlyDescription")
                        : t("llmScope.scopeOptions.fullAccessDescription")}
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Full Access Options */}
              {watchedScopeLevel === "full_access" && (
                <FormField
                  control={form.control}
                  name="fullAccessOptions"
                  render={({ field }) => {
                    const optionDefinitions = [
                      {
                        value: "web" as const,
                        label: t("llmScope.features.webSearch"),
                        icon: Globe,
                      },
                      {
                        value: "mcp" as const,
                        label: t("llmScope.features.mcpTools"),
                        icon: Server,
                      },
                      {
                        value: "deep_search" as const,
                        label: t("llmScope.features.deepSearchResearch"),
                        icon: Brain,
                      },
                    ];

                    const handleOptionChange = (
                      optionValue: string,
                      checked: boolean
                    ) => {
                      const currentOptions = field.value || [];
                      if (checked) {
                        field.onChange([...currentOptions, optionValue]);
                      } else {
                        field.onChange(
                          currentOptions.filter((opt) => opt !== optionValue)
                        );
                      }
                    };

                    return (
                      <FormItem>
                        <FormLabel>{t("llmScope.availableFeatures")}</FormLabel>
                        <FormControl>
                          <div className="space-y-3">
                            {optionDefinitions.map((option) => {
                              const Icon = option.icon;
                              const isChecked =
                                field.value?.includes(option.value) || false;

                              return (
                                <div
                                  key={option.value}
                                  className="flex items-center space-x-2"
                                >
                                  <input
                                    type="checkbox"
                                    id={option.value}
                                    checked={isChecked}
                                    onChange={(e) =>
                                      handleOptionChange(
                                        option.value,
                                        e.target.checked
                                      )
                                    }
                                    disabled={!canModify || isLoading}
                                    className="rounded border-gray-300"
                                  />
                                  <label
                                    htmlFor={option.value}
                                    className="flex items-center gap-2 text-sm cursor-pointer"
                                  >
                                    <Icon className="h-4 w-4" />
                                    {option.label}
                                  </label>
                                </div>
                              );
                            })}
                          </div>
                        </FormControl>
                        <FormDescription>
                          {t("llmScope.features.selectFeaturesDescription")}
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    );
                  }}
                />
              )}

              {/* Capabilities Preview */}
              {/* <div className="space-y-3">
                <h4 className="text-sm font-medium">
                  {t("llmScope.capabilities.title")}
                </h4>
                <div className="grid grid-cols-2 gap-3">
                  <div className="flex items-center gap-2">
                    <Badge
                      variant={
                        capabilities.canAccessInternalDocs
                          ? "default"
                          : "secondary"
                      }
                    >
                      {capabilities.canAccessInternalDocs ? "✓" : "✗"}
                    </Badge>
                    <span className="text-sm">
                      {t("llmScope.capabilities.internalDocuments")}
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge
                      variant={
                        capabilities.canAccessWebSearch
                          ? "default"
                          : "secondary"
                      }
                    >
                      {capabilities.canAccessWebSearch ? "✓" : "✗"}
                    </Badge>
                    <span className="text-sm">
                      {t("llmScope.capabilities.webSearch")}
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge
                      variant={
                        capabilities.canUseHybridSearch
                          ? "default"
                          : "secondary"
                      }
                    >
                      {capabilities.canUseHybridSearch ? "✓" : "✗"}
                    </Badge>
                    <span className="text-sm">
                      {t("llmScope.capabilities.hybridSearch")}
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge
                      variant={
                        capabilities.canAccessFullLLM ? "default" : "secondary"
                      }
                    >
                      {capabilities.canAccessFullLLM ? "✓" : "✗"}
                    </Badge>
                    <span className="text-sm">
                      {t("llmScope.capabilities.fullLLMAccess")}
                    </span>
                  </div>
                </div>
              </div> */}

              {canModify && (
                <Button type="submit" disabled={isLoading}>
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    t("llmScope.saveSettings")
                  )}
                </Button>
              )}
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
}
