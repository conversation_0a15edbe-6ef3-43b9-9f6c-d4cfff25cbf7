// app/(dashboard)/admin/metrics/finance/page.tsx
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/next-auth";
import { redirect } from "next/navigation";
import db from "@/lib/shared-db";
import { addDays } from "date-fns";
import { FinanceMetricsClient } from "@/components/metrics/finance-metrics-client";

interface RevenueMetrics {
  mrr: number;
  arr: number;
  activeSubscriptions: number;
}

interface RevenueByPlan {
  name: string;
  value: number;
  revenue: number;
}

interface RevenueOverTime {
  month: string;
  revenue: number;
}

interface FinanceMetricsData {
  revenueMetrics: RevenueMetrics;
  revenueByPlan: RevenueByPlan[];
  revenueOverTime: RevenueOverTime[];
  period: string;
}

function dateRangeToPeriod(from: Date | null, to: Date | null): string {
  if (!from || !to) return "lifetime";
  const diffTime = Math.abs(to.getTime() - from.getTime());
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  if (diffDays <= 7) return "7days";
  if (diffDays <= 30) return "30days";
  if (diffDays <= 90) return "90days";
  if (diffDays <= 180) return "6months";
  if (diffDays <= 365) return "12months";
  return "12months";
}

async function fetchRevenueMetrics(fromDate: Date | null, toDate: Date | null): Promise<RevenueMetrics> {
  const isLifetime = !fromDate || !toDate;

  const activeSubscriptions = await db.subscription.findMany({
    where: {
      isActive: true,
      ...(isLifetime ? {} : {
        createdAt: {
          gte: fromDate!,
          lte: toDate!
        }
      })
    },
    include: { plan: true }
  });

  let mrr = 0;

  activeSubscriptions.forEach((subscription) => {
    const basePrice =
      subscription.billingInterval === "month"
        ? subscription.plan.price || 0
        : (subscription.plan.price || 0) / 12;

    const additionalUsersCost =
      subscription.additionalUsers *
      (subscription.billingInterval === "month"
        ? subscription.plan.additionalUserFee || 0
        : (subscription.plan.additionalUserFee || 0) / 12);

    const additionalStorageCost = subscription.additionalStorageGB * 10;

    const subscriptionMrr = basePrice + additionalUsersCost + additionalStorageCost;
    mrr += subscriptionMrr;
  });

  const arr = mrr * 12;

  return {
    mrr: Math.round(mrr * 100) / 100,
    arr: Math.round(arr * 100) / 100,
    activeSubscriptions: activeSubscriptions.length,
  };
}

async function fetchRevenueByPlan(fromDate: Date | null, toDate: Date | null): Promise<RevenueByPlan[]> {
  const isLifetime = !fromDate || !toDate;

  const revenueByPlan = await db.subscription.groupBy({
    by: ["planId"],
    where: {
      isActive: true,
      ...(isLifetime ? {} : {
        createdAt: {
          gte: fromDate!,
          lte: toDate!
        }
      })
    },
    _count: { _all: true },
  });

  const planIds = revenueByPlan.map((item) => item.planId);
  const plans = await db.plan.findMany({
    where: {
      id: { in: planIds },
      ...(isLifetime ? {} : {
        createdAt: {
          gte: fromDate!,
          lte: toDate!
        }
      })
    },
    select: { id: true, name: true, type: true, price: true },
  });

  // Map plan names to the results and calculate revenue
  const revenueByPlanWithNames = revenueByPlan.map(item => {
    const plan = plans.find(p => p.id === item.planId);
    const subscriptionCount = item._count._all;
    const planPrice = plan?.price || 0;
    const revenue = subscriptionCount * planPrice;
    
    return {
      name: plan?.name || 'Unknown',
      value: subscriptionCount,
      revenue: revenue,
    };
  });

  return revenueByPlanWithNames;
}

async function fetchRevenueOverTime(fromDate: Date | null, toDate: Date | null, period: string): Promise<RevenueOverTime[]> {
  const isLifetime = !fromDate || !toDate;

  const subscriptions = await db.subscription.findMany({
    where: {
      isActive: true,
      ...(isLifetime ? {} : {
        createdAt: {
          gte: fromDate!,
          lte: toDate!
        }
      }),
    },
    include: { plan: true },
    orderBy: { createdAt: "asc" },
  });

  const monthNames = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];
  const groupedData: Record<string, number> = {};

  subscriptions.forEach((subscription) => {
    const date = subscription.createdAt;
    const monthYear = `${monthNames[date.getMonth()]} ${date.getFullYear()}`;

    const basePrice =
      subscription.billingInterval === "month"
        ? subscription.plan.price || 0
        : (subscription.plan.price || 0) / 12;

    const additionalUsersCost =
      subscription.additionalUsers *
      (subscription.billingInterval === "month"
        ? subscription.plan.additionalUserFee || 0
        : (subscription.plan.additionalUserFee || 0) / 12);

    const additionalStorageCost = subscription.additionalStorageGB * 10;

    const subscriptionRevenue = basePrice + additionalUsersCost + additionalStorageCost;
    groupedData[monthYear] = (groupedData[monthYear] || 0) + subscriptionRevenue;
  });

  return Object.entries(groupedData)
    .map(([month, revenue]) => ({ month, revenue }))
    .sort((a, b) => {
      const [aMonth, aYear] = a.month.split(" ");
      const [bMonth, bYear] = b.month.split(" ");
      const yearDiff = parseInt(aYear) - parseInt(bYear);
      if (yearDiff !== 0) return yearDiff;
      return monthNames.indexOf(aMonth) - monthNames.indexOf(bMonth);
    });
}

export default async function FinanceMetricsPage({
  searchParams,
}: {
  searchParams: { from?: string; to?: string };
}) {
  const session = await getServerSession(authOptions);
  if (!session) redirect("/auth/signin");

  const isLifetimeQuery = !searchParams.from && !searchParams.to;
  const fromDate = isLifetimeQuery
    ? null
    : searchParams.from
    ? new Date(searchParams.from)
    : addDays(new Date(), -180);
  const toDate = isLifetimeQuery
    ? null
    : searchParams.to
    ? new Date(searchParams.to)
    : new Date();
  const period = dateRangeToPeriod(fromDate, toDate);

  const [revenueMetrics, revenueByPlan, revenueOverTime] = await Promise.all([
    fetchRevenueMetrics(fromDate, toDate),
    fetchRevenueByPlan(fromDate, toDate),
    fetchRevenueOverTime(fromDate, toDate, period),
  ]);

  const metricsData: FinanceMetricsData = {
    revenueMetrics,
    revenueByPlan,
    revenueOverTime,
    period,
  };

  return <FinanceMetricsClient data={metricsData} />;
}
