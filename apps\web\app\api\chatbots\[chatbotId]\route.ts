import { NextResponse } from "next/server";
import db from "@/lib/shared-db";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/next-auth";

export async function GET(
  req: Request,
  { params }: { params: { chatbotId: string } }
) {
  try {
    const session: any = await getServerSession(authOptions);
    if (!session) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const { searchParams } = new URL(req.url);
    const tenantId = searchParams.get("tenantId");

    if (!tenantId) {
      return NextResponse.json(
        { error: "Tenant ID is required" },
        { status: 400 }
      );
    }

    // Find chatbot in database
    const chatbot = await db.chatbot.findUnique({
      where: {
        id: params.chatbotId,
      },
    });

    if (!chatbot) {
      return NextResponse.json({ error: "Chatbot not found" }, { status: 404 });
    }

    // Check if user has access to this chatbot
    if (chatbot.userId !== session.userId || chatbot.tenantId !== tenantId) {
      return NextResponse.json({ error: "Access denied" }, { status: 403 });
    }

    return NextResponse.json({ chatbot }, { status: 200 });
  } catch (error) {
    console.error("[CHATBOT_GET]", error);
    return NextResponse.json({ error: "Internal Error" }, { status: 500 });
  }
}

export async function PUT(
  req: Request,
  { params }: { params: { chatbotId: string } }
) {
  try {
    const session: any = await getServerSession(authOptions);
    if (!session) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    let requestBody;
    try {
      requestBody = await req.json();
    } catch (parseError) {
      console.error("Failed to parse request body:", parseError);
      return NextResponse.json(
        { error: "Invalid JSON in request body" },
        { status: 400 }
      );
    }

    const {
      name,
      description,
      type,
      access,
      companyIds,
      isActive,
      tenantId,
      allowedDomains,
      customization,
      llmScope,
      searchModes,
      maxTokens,
      temperature,
      rateLimitPerMinute,
      rateLimitPerHour,
      rateLimitPerDay,
    } = requestBody;

    if (!tenantId) {
      return NextResponse.json(
        { error: "Tenant ID is required" },
        { status: 400 }
      );
    }

    // Basic field validation
    if (name !== undefined && (typeof name !== 'string' || name.trim().length === 0)) {
      return NextResponse.json(
        { error: "Name must be a non-empty string" },
        { status: 400 }
      );
    }

    if (description !== undefined && typeof description !== 'string') {
      return NextResponse.json(
        { error: "Description must be a string" },
        { status: 400 }
      );
    }

    // Check if chatbot exists and user has access
    const existingChatbot = await db.chatbot.findUnique({
      where: {
        id: params.chatbotId,
      },
    });

    if (!existingChatbot) {
      return NextResponse.json({ error: "Chatbot not found" }, { status: 404 });
    }

    // Check if user has access to this chatbot
    if (
      existingChatbot.userId !== session.userId ||
      existingChatbot.tenantId !== tenantId
    ) {
      return NextResponse.json({ error: "Access denied" }, { status: 403 });
    }

    // Validate fields if provided
    if (type) {
      const validTypes = ["web-snippet", "inline-embedding", "dedicated-page"];
      if (!validTypes.includes(type)) {
        return NextResponse.json(
          { error: "Invalid type. Must be one of: " + validTypes.join(", ") },
          { status: 400 }
        );
      }
    }

    if (access) {
      const validAccess = ["public", "private"];
      if (!validAccess.includes(access)) {
        return NextResponse.json(
          {
            error: "Invalid access. Must be one of: " + validAccess.join(", "),
          },
          { status: 400 }
        );
      }
    }

    // Validate private access has allowed users (only if access is being changed to private)
    if (access === "private" && (!companyIds || companyIds.length === 0)) {
      // Check if the existing chatbot already has companies
      if (!existingChatbot.companyIds || existingChatbot.companyIds.length === 0) {
        return NextResponse.json(
          { error: "Private chatbots must have at least one company" },
          { status: 400 }
        );
      }
    }

    // Prepare update data
    const updateData: any = {};
    if (name !== undefined) updateData.name = name;
    if (description !== undefined) updateData.description = description;
    if (type !== undefined) updateData.type = type;
    if (access !== undefined) updateData.access = access;
    if (companyIds !== undefined) updateData.companyIds = companyIds;
    if (isActive !== undefined) updateData.isActive = isActive;
    if (companyIds !== undefined) updateData.companyIds = companyIds;

    // SDK-specific fields
    if (allowedDomains !== undefined)
      updateData.allowedDomains = allowedDomains;
    if (customization !== undefined) updateData.customization = customization;

    // LLM configuration
    if (llmScope !== undefined) updateData.llmScope = llmScope;
    if (searchModes !== undefined) updateData.searchModes = searchModes;
    if (maxTokens !== undefined) updateData.maxTokens = maxTokens;
    if (temperature !== undefined) updateData.temperature = temperature;

    // Rate limiting
    if (rateLimitPerMinute !== undefined)
      updateData.rateLimitPerMinute = rateLimitPerMinute;
    if (rateLimitPerHour !== undefined)
      updateData.rateLimitPerHour = rateLimitPerHour;
    if (rateLimitPerDay !== undefined)
      updateData.rateLimitPerDay = rateLimitPerDay;

    // Update chatbot in database
    const chatbot = await db.chatbot.update({
      where: {
        id: params.chatbotId,
        userId: session.userId,
        tenantId: tenantId,
      },
      data: updateData,
    });

    console.log("Chatbot updated successfully:", chatbot.id);
    return NextResponse.json({ chatbot }, { status: 200 });
  } catch (error) {
    console.error("[CHATBOT_PUT] Error updating chatbot:", error);

    // Provide more specific error messages
    if (error instanceof Error) {
      // Check for common Prisma errors
      if (error.message.includes("Record to update not found")) {
        return NextResponse.json(
          { error: "Chatbot not found or access denied" },
          { status: 404 }
        );
      }

      if (error.message.includes("Unique constraint")) {
        return NextResponse.json(
          { error: "A chatbot with this name already exists" },
          { status: 400 }
        );
      }

      // Return the actual error message for debugging (in development)
      if (process.env.NODE_ENV === "development") {
        return NextResponse.json(
          { error: `Update failed: ${error.message}` },
          { status: 500 }
        );
      }
    }

    return NextResponse.json({ error: "Internal Error" }, { status: 500 });
  }
}

export async function DELETE(
  req: Request,
  { params }: { params: { chatbotId: string } }
) {
  try {
    const session: any = await getServerSession(authOptions);
    if (!session) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const { searchParams } = new URL(req.url);
    const tenantId = searchParams.get("tenantId");

    if (!tenantId) {
      return NextResponse.json(
        { error: "Tenant ID is required" },
        { status: 400 }
      );
    }

    // Check if chatbot exists and user has access
    const existingChatbot = await db.chatbot.findUnique({
      where: {
        id: params.chatbotId,
      },
    });

    if (!existingChatbot) {
      return NextResponse.json({ error: "Chatbot not found" }, { status: 404 });
    }

    // Check if user has access to this chatbot
    if (
      existingChatbot.userId !== session.userId ||
      existingChatbot.tenantId !== tenantId
    ) {
      return NextResponse.json({ error: "Access denied" }, { status: 403 });
    }

    // Delete chatbot from database
    await db.chatbot.delete({
      where: {
        id: params.chatbotId,
        userId: session.userId,
        tenantId: tenantId,
      },
    });

    return NextResponse.json(
      { message: "Chatbot deleted successfully" },
      { status: 200 }
    );
  } catch (error) {
    console.error("[CHATBOT_DELETE]", error);
    return NextResponse.json({ error: "Internal Error" }, { status: 500 });
  }
}
