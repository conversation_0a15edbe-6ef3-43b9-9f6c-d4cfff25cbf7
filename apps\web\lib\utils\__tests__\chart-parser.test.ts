/**
 * Tests for chart parser utilities
 */

import { 
  detectCharts, 
  parseChartContent, 
  parseCSVLikeData, 
  validateChartData,
  getDefaultChartConfig 
} from '../chart-parser';

describe('Chart Parser', () => {
  describe('detectCharts', () => {
    it('should detect line chart syntax', () => {
      const content = `
Some text before

\`\`\`chart:line
{"data": [{"x": 1, "y": 2}]}
\`\`\`

Some text after
      `;
      
      const charts = detectCharts(content);
      expect(charts).toHaveLength(1);
      expect(charts[0].chart.type).toBe('line');
    });

    it('should detect mermaid diagrams', () => {
      const content = `
\`\`\`mermaid
graph TD
    A --> B
\`\`\`
      `;
      
      const charts = detectCharts(content);
      expect(charts).toHaveLength(1);
      expect(charts[0].chart.type).toBe('mermaid');
    });

    it('should detect multiple charts', () => {
      const content = `
\`\`\`chart:bar
data here
\`\`\`

Some text

\`\`\`mermaid
diagram here
\`\`\`
      `;
      
      const charts = detectCharts(content);
      expect(charts).toHaveLength(2);
    });

    it('should handle malformed chart blocks', () => {
      const content = `
\`\`\`chart:line
{"data": [{"x": 1, "y": 2}]}
// Missing closing backticks
      `;
      
      const charts = detectCharts(content);
      expect(charts).toHaveLength(0);
    });
  });

  describe('parseChartContent', () => {
    it('should parse JSON format', () => {
      const content = '{"data": [{"month": "Jan", "sales": 100}]}';
      const result = parseChartContent(content, 'line');
      
      expect(result).toBeTruthy();
      expect(result?.type).toBe('line');
      expect(result?.data).toEqual([{"month": "Jan", "sales": 100}]);
    });

    it('should parse array format', () => {
      const content = '[{"month": "Jan", "sales": 100}]';
      const result = parseChartContent(content, 'bar');
      
      expect(result).toBeTruthy();
      expect(result?.data).toEqual([{"month": "Jan", "sales": 100}]);
    });

    it('should handle invalid JSON', () => {
      const content = 'invalid json';
      const result = parseChartContent(content, 'line');
      
      expect(result).toBeNull();
    });
  });

  describe('parseCSVLikeData', () => {
    it('should parse CSV format', () => {
      const content = `month,sales,profit
Jan,100,20
Feb,150,30`;
      
      const result = parseCSVLikeData(content);
      expect(result).toEqual([
        {"month": "Jan", "sales": 100, "profit": 20},
        {"month": "Feb", "sales": 150, "profit": 30}
      ]);
    });

    it('should handle mixed data types', () => {
      const content = `name,value,active
Product A,100,true
Product B,200,false`;
      
      const result = parseCSVLikeData(content);
      expect(result).toEqual([
        {"name": "Product A", "value": 100, "active": "true"},
        {"name": "Product B", "value": 200, "active": "false"}
      ]);
    });

    it('should handle empty or invalid CSV', () => {
      expect(parseCSVLikeData('')).toEqual([]);
      expect(parseCSVLikeData('single line')).toEqual([]);
    });
  });

  describe('validateChartData', () => {
    it('should validate correct chart data', () => {
      const chartData = {
        type: 'line' as const,
        data: [
          {"month": "Jan", "sales": 100},
          {"month": "Feb", "sales": 150}
        ]
      };
      
      expect(validateChartData(chartData)).toBe(true);
    });

    it('should reject empty data', () => {
      const chartData = {
        type: 'line' as const,
        data: []
      };
      
      expect(validateChartData(chartData)).toBe(false);
    });

    it('should reject inconsistent data structure', () => {
      const chartData = {
        type: 'line' as const,
        data: [
          {"month": "Jan", "sales": 100},
          {"quarter": "Q1", "revenue": 1000} // Different keys
        ]
      };
      
      expect(validateChartData(chartData)).toBe(false);
    });
  });

  describe('getDefaultChartConfig', () => {
    it('should detect string key as x-axis', () => {
      const data = [
        {"month": "Jan", "sales": 100},
        {"month": "Feb", "sales": 150}
      ];
      
      const config = getDefaultChartConfig('line', data);
      expect(config.xKey).toBe('month');
      expect(config.yKey).toBe('sales');
    });

    it('should set appropriate defaults for pie charts', () => {
      const data = [{"category": "A", "value": 100}];
      const config = getDefaultChartConfig('pie', data);
      
      expect(config.showLegend).toBe(true);
    });

    it('should handle empty data gracefully', () => {
      const config = getDefaultChartConfig('line', []);
      expect(config).toEqual({});
    });
  });
});
