import { cookies } from "next/headers";
import db from "@/lib/shared-db";
import { ThemeInitializer } from "@/components/theme/theme-initializer";

interface ThemeSynchronizerProps {
  children: React.ReactNode;
}

export async function ThemeSynchronizer({ children }: ThemeSynchronizerProps) {
  const isPartnerConsole = process.env.NEXT_PUBLIC_IS_PARTNER_CONSOLE === "1";

  let tenantThemePreset: string | null = null;
  let themeConfig = null;

  if (isPartnerConsole) {
    try {
      const tenantId = cookies().get("currentOrganizationId")?.value;

      if (tenantId) {
        themeConfig = await db.themeConfiguration.findUnique({
          where: { tenantId: tenantId },
        });
        tenantThemePreset = (themeConfig as any)?.themePreset || null;
      }
    } catch (error) {
      console.error("Failed to fetch tenant theme config:", error);
      // Don't throw, just continue with default theme
    }
  }

  return (
    <>
      <ThemeInitializer
        tenantThemePreset={tenantThemePreset}
        themeConfig={isPartnerConsole ? themeConfig : null}
      />
      {children}
    </>
  );
}
