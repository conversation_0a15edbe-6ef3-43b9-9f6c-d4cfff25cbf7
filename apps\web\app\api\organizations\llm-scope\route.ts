import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import db from "@/lib/shared-db";
import { authOptions } from "@/lib/next-auth/src/auth-options";
import crypto from "crypto";

// Get LLM scope settings for an organization
export async function GET(request: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const tenantId = searchParams.get("tenantId");

    if (!tenantId) {
      return NextResponse.json(
        { error: "Tenant ID is required" },
        { status: 400 }
      );
    }

    const emailHash = crypto
      .createHash("sha256")
      .update(session.user.email)
      .digest("hex");

    // Check if user has access to this tenant
    const membership = await db.membership.findFirst({
      where: {
        user: {
          emailHash,
        },
        tenantId,
      },
      include: {
        tenant: true,
      },
    });

    if (!membership) {
      return NextResponse.json({ error: "Access denied" }, { status: 403 });
    }

    // Get tenant with LLM scope settings
    const tenant = await db.tenant.findUnique({
      where: {
        id: tenantId,
      },
      select: {
        id: true,
        llmScope: true,
        updatedAt: true,
      },
    });

    if (!tenant) {
      return NextResponse.json(
        { error: "Organization not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({
      settings: {
        id: tenant.id,
        llmScope: tenant.llmScope || ["INTERNAL_ONLY"],
        updatedAt: tenant.updatedAt.toISOString(),
      },
    });
  } catch (error) {
    console.error("Error fetching LLM scope settings:", error);
    return NextResponse.json(
      { error: "Failed to fetch LLM scope settings" },
      { status: 500 }
    );
  }
}

// Update LLM scope settings for an organization (Owner only)
export async function PUT(request: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { tenantId, llmScope } = await request.json();

    if (!tenantId || !llmScope) {
      return NextResponse.json(
        { error: "Tenant ID and LLM scope are required" },
        { status: 400 }
      );
    }

    // Validate LLM scope array
    if (!Array.isArray(llmScope) || llmScope.length === 0) {
      return NextResponse.json(
        { error: "Invalid LLM scope value - must be a non-empty array" },
        { status: 400 }
      );
    }

    const emailHash = crypto
      .createHash("sha256")
      .update(session.user.email)
      .digest("hex");

    // Check if user is an Owner of this tenant
    const membership = await db.membership.findFirst({
      where: {
        user: {
          emailHash,
        },
        tenantId,
        role: {
          in: ["OWNER", "ADMIN"],
        }, // Only owners can modify LLM scope
      },
    });

    if (!membership) {
      return NextResponse.json(
        { error: "Only organization owners can modify LLM scope settings" },
        { status: 403 }
      );
    }

    // Update tenant's LLM scope
    const updatedTenant = await db.tenant.update({
      where: {
        id: tenantId,
      },
      data: {
        llmScope,
        updatedAt: new Date(),
      },
      select: {
        id: true,
        llmScope: true,
        updatedAt: true,
      },
    });

    return NextResponse.json({
      message: "LLM scope settings updated successfully",
      settings: {
        id: updatedTenant.id,
        llmScope: updatedTenant.llmScope,
        updatedAt: updatedTenant.updatedAt.toISOString(),
      },
    });
  } catch (error) {
    console.error("Error updating LLM scope settings:", error);
    return NextResponse.json(
      { error: "Failed to update LLM scope settings" },
      { status: 500 }
    );
  }
}
