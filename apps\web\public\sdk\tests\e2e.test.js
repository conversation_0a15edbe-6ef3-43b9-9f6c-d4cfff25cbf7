/**
 * Swiss Knowledge Hub Chatbot SDK - End-to-End Tests
 * 
 * E2E tests for the chatbot SDK using Cypress or Playwright
 * These tests simulate real user interactions with the chatbot
 */

// Cypress E2E Tests
if (typeof cy !== 'undefined') {
  describe('Chatbot SDK E2E Tests', () => {
    const TEST_CONFIG = {
      chatbotId: 'test-chatbot-id',
      apiKey: 'skh_test_key_1234567890abcdef',
      baseUrl: 'http://localhost:3000'
    };

    beforeEach(() => {
      // Visit test page
      cy.visit('/sdk/test.html');
      
      // Configure test chatbot
      cy.get('#chatbotId').type(TEST_CONFIG.chatbotId);
      cy.get('#apiKey').type(TEST_CONFIG.apiKey);
    });

    describe('Widget Mode Tests', () => {
      it('should initialize widget chatbot successfully', () => {
        cy.get('button').contains('Initialize Chatbot').click();
        
        // Check for success message
        cy.get('#status').should('contain', 'Chatbot initialized successfully');
        
        // Check that chat button appears
        cy.get('.skh-chat-button').should('be.visible');
        
        // Check logs
        cy.get('#logs').should('contain', 'Chatbot initialized successfully');
      });

      it('should open and close chat window', () => {
        // Initialize chatbot
        cy.get('button').contains('Initialize Chatbot').click();
        
        // Click chat button to open
        cy.get('.skh-chat-button').click();
        cy.get('.skh-chat-window').should('have.class', 'open');
        cy.get('.skh-chat-window').should('be.visible');
        
        // Click close button
        cy.get('.skh-close-button').click();
        cy.get('.skh-chat-window').should('not.have.class', 'open');
      });

      it('should send and receive messages', () => {
        // Initialize and open chatbot
        cy.get('button').contains('Initialize Chatbot').click();
        cy.get('.skh-chat-button').click();
        
        // Type and send message
        const testMessage = 'Hello, this is a test message';
        cy.get('.skh-chat-input').type(testMessage);
        cy.get('.skh-send-button').click();
        
        // Check user message appears
        cy.get('.skh-message.user').should('contain', testMessage);
        
        // Check for typing indicator
        cy.get('.skh-message.typing').should('be.visible');
        
        // Wait for response (mock or real)
        cy.get('.skh-message.bot', { timeout: 10000 }).should('be.visible');
        
        // Check typing indicator is removed
        cy.get('.skh-message.typing').should('not.exist');
      });

      it('should handle multiple messages in conversation', () => {
        // Initialize and open chatbot
        cy.get('button').contains('Initialize Chatbot').click();
        cy.get('.skh-chat-button').click();
        
        // Send first message
        cy.get('.skh-chat-input').type('First message');
        cy.get('.skh-send-button').click();
        cy.get('.skh-message.user').should('contain', 'First message');
        
        // Wait for response
        cy.get('.skh-message.bot', { timeout: 10000 }).should('be.visible');
        
        // Send second message
        cy.get('.skh-chat-input').type('Second message');
        cy.get('.skh-send-button').click();
        
        // Check both messages exist
        cy.get('.skh-message.user').should('have.length', 2);
        cy.get('.skh-message.user').eq(0).should('contain', 'First message');
        cy.get('.skh-message.user').eq(1).should('contain', 'Second message');
      });

      it('should handle Enter key for sending messages', () => {
        // Initialize and open chatbot
        cy.get('button').contains('Initialize Chatbot').click();
        cy.get('.skh-chat-button').click();
        
        // Type message and press Enter
        cy.get('.skh-chat-input').type('Message sent with Enter{enter}');
        
        // Check message was sent
        cy.get('.skh-message.user').should('contain', 'Message sent with Enter');
      });

      it('should prevent sending empty messages', () => {
        // Initialize and open chatbot
        cy.get('button').contains('Initialize Chatbot').click();
        cy.get('.skh-chat-button').click();
        
        // Try to send empty message
        cy.get('.skh-send-button').click();
        
        // Should not create any user messages
        cy.get('.skh-message.user').should('not.exist');
      });
    });

    describe('Inline Mode Tests', () => {
      it('should initialize inline chatbot successfully', () => {
        // Select inline mode
        cy.get('#mode').select('inline');
        
        // Initialize chatbot
        cy.get('button').contains('Initialize Chatbot').click();
        
        // Check for success message
        cy.get('#status').should('contain', 'Chatbot initialized successfully');
        
        // Check that inline container is visible
        cy.get('#inlineSection').should('be.visible');
        cy.get('#inline-chatbot-container .skh-chat-header').should('be.visible');
      });

      it('should not show close button in inline mode', () => {
        // Select inline mode and initialize
        cy.get('#mode').select('inline');
        cy.get('button').contains('Initialize Chatbot').click();
        
        // Close button should not exist in inline mode
        cy.get('.skh-close-button').should('not.exist');
      });
    });

    describe('Configuration Tests', () => {
      it('should apply custom theme colors', () => {
        // Set custom primary color
        cy.get('#primaryColor').invoke('val', '#ff0000').trigger('change');
        
        // Initialize chatbot
        cy.get('button').contains('Initialize Chatbot').click();
        
        // Check that CSS custom property is set
        cy.window().then((win) => {
          const primaryColor = win.getComputedStyle(win.document.documentElement)
            .getPropertyValue('--skh-primary-color');
          expect(primaryColor.trim()).to.equal('#ff0000');
        });
      });

      it('should apply custom greeting message', () => {
        // Set custom greeting
        const customGreeting = 'Welcome to our custom chatbot!';
        cy.get('#greeting').type(customGreeting);
        
        // Initialize and open chatbot
        cy.get('button').contains('Initialize Chatbot').click();
        cy.get('.skh-chat-button').click();
        
        // Check greeting message appears
        cy.get('.skh-message.bot').should('contain', customGreeting);
      });

      it('should test configuration endpoint', () => {
        // Click test configuration button
        cy.get('button').contains('Test Configuration').click();
        
        // Should show testing status
        cy.get('#status').should('contain', 'Testing configuration');
        
        // Check logs for test results
        cy.get('#logs').should('contain', 'Testing configuration');
      });
    });

    describe('Error Handling Tests', () => {
      it('should handle invalid API key gracefully', () => {
        // Set invalid API key
        cy.get('#apiKey').clear().type('invalid-key');
        
        // Try to initialize
        cy.get('button').contains('Initialize Chatbot').click();
        
        // Should show error
        cy.get('#status').should('contain', 'failed');
        cy.get('#logs').should('contain', 'error');
      });

      it('should handle network errors gracefully', () => {
        // Mock network failure
        cy.intercept('GET', '/api/public/chatbot/*/config', {
          statusCode: 500,
          body: { error: 'Server error' }
        });
        
        // Try to test configuration
        cy.get('button').contains('Test Configuration').click();
        
        // Should show error
        cy.get('#status').should('contain', 'failed');
      });
    });

    describe('Cleanup Tests', () => {
      it('should destroy chatbot successfully', () => {
        // Initialize chatbot first
        cy.get('button').contains('Initialize Chatbot').click();
        cy.get('#status').should('contain', 'initialized successfully');
        
        // Destroy chatbot
        cy.get('button').contains('Destroy Chatbot').click();
        
        // Check for success message
        cy.get('#status').should('contain', 'destroyed successfully');
        
        // Chat button should be removed
        cy.get('.skh-chat-button').should('not.exist');
      });
    });

    describe('Responsive Design Tests', () => {
      it('should work on mobile viewport', () => {
        // Set mobile viewport
        cy.viewport(375, 667);
        
        // Initialize chatbot
        cy.get('button').contains('Initialize Chatbot').click();
        cy.get('.skh-chat-button').click();
        
        // Chat window should be responsive
        cy.get('.skh-chat-window').should('be.visible');
        cy.get('.skh-chat-window').should('have.css', 'width');
      });

      it('should work on tablet viewport', () => {
        // Set tablet viewport
        cy.viewport(768, 1024);
        
        // Initialize chatbot
        cy.get('button').contains('Initialize Chatbot').click();
        cy.get('.skh-chat-button').click();
        
        // Should work normally
        cy.get('.skh-chat-window').should('be.visible');
      });
    });
  });
}

// Playwright E2E Tests (alternative to Cypress)
if (typeof test !== 'undefined') {
  const { test, expect } = require('@playwright/test');

  test.describe('Chatbot SDK Playwright Tests', () => {
    test.beforeEach(async ({ page }) => {
      await page.goto('/sdk/test.html');
      
      // Configure test chatbot
      await page.fill('#chatbotId', 'test-chatbot-id');
      await page.fill('#apiKey', 'skh_test_key_1234567890abcdef');
    });

    test('should initialize and interact with chatbot', async ({ page }) => {
      // Initialize chatbot
      await page.click('button:has-text("Initialize Chatbot")');
      
      // Wait for initialization
      await expect(page.locator('#status')).toContainText('initialized successfully');
      
      // Open chat
      await page.click('.skh-chat-button');
      await expect(page.locator('.skh-chat-window')).toBeVisible();
      
      // Send message
      await page.fill('.skh-chat-input', 'Hello chatbot');
      await page.click('.skh-send-button');
      
      // Check message appears
      await expect(page.locator('.skh-message.user')).toContainText('Hello chatbot');
    });

    test('should handle errors gracefully', async ({ page }) => {
      // Set invalid API key
      await page.fill('#apiKey', 'invalid-key');
      
      // Try to initialize
      await page.click('button:has-text("Initialize Chatbot")');
      
      // Should show error
      await expect(page.locator('#status')).toContainText('failed');
    });
  });
}

// Manual Test Checklist
const MANUAL_TEST_CHECKLIST = `
Manual Testing Checklist for Chatbot SDK:

□ Widget Mode
  □ Chat button appears in correct position
  □ Chat window opens/closes properly
  □ Messages send and receive correctly
  □ Typing indicator works
  □ Citations are clickable
  □ Scroll behavior works
  □ Mobile responsive design

□ Inline Mode
  □ Chatbot renders in container
  □ No close button present
  □ Full functionality works
  □ Container sizing correct

□ Customization
  □ Theme colors apply correctly
  □ Font family changes work
  □ Position settings work
  □ Size settings work
  □ Greeting message displays
  □ Placeholder text correct

□ Error Handling
  □ Invalid API key shows error
  □ Network errors handled gracefully
  □ Rate limiting shows appropriate message
  □ Domain restrictions work

□ Performance
  □ SDK loads quickly
  □ Chat responses are fast
  □ No memory leaks
  □ Smooth animations

□ Security
  □ API key not exposed in DOM
  □ CORS headers correct
  □ Domain validation works
  □ Rate limiting enforced

□ Browser Compatibility
  □ Chrome (latest)
  □ Firefox (latest)
  □ Safari (latest)
  □ Edge (latest)
  □ Mobile browsers
`;

console.log('Swiss Knowledge Hub Chatbot SDK - E2E Test Suite Loaded');
console.log('Manual Test Checklist:', MANUAL_TEST_CHECKLIST);
