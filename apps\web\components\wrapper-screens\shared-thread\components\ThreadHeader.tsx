"use client";

import React, { useState } from "react";
import {
  Share2,
  <PERSON><PERSON>,
  Clock,
  User,
  Building2,
  CheckCircle,
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { useLanguage } from "@/lib/language-context";
import { useTranslatedToast } from "@/hooks/use-translated-toast";

interface User {
  id: string;
  name: string;
  email: string;
  image?: string;
}

interface ShareInfo {
  shareToken: string;
  isPublic: boolean;
  expiresAt?: string;
  createdBy: User;
  createdAt: string;
}

interface Tenant {
  id: string;
  name: string;
  slug: string;
}

interface Chat {
  id: string;
  title?: string;
  description?: string;
  user: User;
  createdAt: string;
  updatedAt: string;
}

interface ThreadHeaderProps {
  chat: Chat;
  shareInfo: ShareInfo;
  tenant: Tenant;
  currentUser: User | null;
}

export const ThreadHeader: React.FC<ThreadHeaderProps> = ({
  chat,
  shareInfo,
  tenant,
  currentUser,
}) => {
  const { t } = useLanguage();
  const toast = useTranslatedToast();
  const [copied, setCopied] = useState(false);

  const handleCopyLink = async () => {
    try {
      await navigator.clipboard.writeText(window.location.href);
      setCopied(true);
      toast.success("toast.linkCopied");
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      toast.error("toast.linkCopyFailed");
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString(undefined, {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase()
      .slice(0, 2);
  };

  return (
    <div className="border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800">
      <div className="max-w-4xl mx-auto px-4 py-4">
        {/* Main Header */}
        <div className="flex items-start justify-between mb-4">
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-2">
              <Share2 className="h-5 w-5 text-primary" />
              <h1 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
                {chat.title || t("sharedThread.title")}
              </h1>
              <Badge variant={shareInfo.isPublic ? "default" : "secondary"}>
                {shareInfo.isPublic
                  ? t("sharedThread.public")
                  : t("sharedThread.private")}
              </Badge>
            </div>

            {chat.description && (
              <p className="text-gray-600 dark:text-gray-400 text-sm mb-3">
                {chat.description}
              </p>
            )}

            {/* Thread Info */}
            <div className="flex flex-wrap items-center gap-4 text-sm text-gray-500 dark:text-gray-400">
              <div className="flex items-center gap-1">
                <Building2 className="h-4 w-4" />
                <span>{tenant.name}</span>
              </div>

              <div className="flex items-center gap-1">
                <User className="h-4 w-4" />
                <span>
                  {t("sharedThread.createdBy", { name: chat.user.name })}
                </span>
              </div>

              <div className="flex items-center gap-1">
                <Clock className="h-4 w-4" />
                <span>{formatDate(chat.createdAt)}</span>
              </div>
            </div>
          </div>

          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleCopyLink}
              className="flex items-center gap-2"
            >
              {copied ? (
                <CheckCircle className="h-4 w-4 text-green-500" />
              ) : (
                <Copy className="h-4 w-4" />
              )}
              {copied ? t("sharedThread.copied") : t("sharedThread.copyLink")}
            </Button>
          </div>
        </div>

        {/* Share Info */}
        <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
          <div className="flex items-center gap-3">
            <Avatar className="h-8 w-8">
              <AvatarImage src={shareInfo.createdBy.image} />
              <AvatarFallback className="text-xs">
                {getInitials(shareInfo.createdBy.name)}
              </AvatarFallback>
            </Avatar>
            <div>
              <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                ({shareInfo.createdBy.name})
              </p>
              <p className="text-xs text-gray-500 dark:text-gray-400">
                {formatDate(shareInfo.createdAt)}
                {shareInfo.expiresAt && (
                  <span className="ml-2">
                    •{" "}
                    {t("sharedThread.expires", {
                      date: formatDate(shareInfo.expiresAt),
                    })}
                  </span>
                )}
              </p>
            </div>
          </div>

          {!currentUser && (
            <div className="text-right">
              <p className="text-xs text-gray-500 dark:text-gray-400 mb-1">
                {t("sharedThread.wantToComment")}
              </p>
              <Button size="sm" asChild>
                <a href="/sign-in">{t("sharedThread.signIn")}</a>
              </Button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
