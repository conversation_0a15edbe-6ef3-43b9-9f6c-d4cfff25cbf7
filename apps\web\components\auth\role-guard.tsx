"use client";

import React from "react";
import { useSession } from "next-auth/react";
import { AlertCircle, Shield, Lock } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useRouter } from "next/navigation";

type UserRole = "OWNER" | "ADMIN" | "MEMBER" | "CUSTOM";

interface RoleGuardProps {
  children: React.ReactNode;
  allowedRoles: UserRole[];
  tenantId?: string;
  fallback?: React.ReactNode;
  redirectTo?: string;
  showUnauthorizedMessage?: boolean;
}

export function RoleGuard({
  children,
  allowedRoles,
  tenantId,
  fallback,
  redirectTo,
  showUnauthorizedMessage = true,
}: RoleGuardProps) {
  const { data: session, status } = useSession();
  const router = useRouter();

  // Show loading state while session is being fetched
  if (status === "loading") {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  // Redirect to sign-in if not authenticated
  if (status === "unauthenticated") {
    if (redirectTo) {
      router.push(redirectTo);
      return null;
    }
    return (
      <Alert>
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>Authentication Required</AlertTitle>
        <AlertDescription>
          You must be signed in to access this page.
        </AlertDescription>
      </Alert>
    );
  }

  const user = session?.user;
  const memberships = (session as any)?.memberships || [];

  // Find the user's role for the current tenant
  let userRole: UserRole | null = null;
  if (tenantId) {
    const membership = memberships.find((m: any) => m.tenant.id === tenantId);
    userRole = membership?.role || null;
  } else {
    // If no specific tenant, check if user has any of the allowed roles in any tenant
    const hasAllowedRole = memberships.some((m: any) =>
      allowedRoles.includes(m.role as UserRole)
    );
    if (hasAllowedRole) {
      userRole = memberships[0]?.role || null;
    }
  }

  // Check if user has required role
  const hasAccess = userRole && allowedRoles.includes(userRole);

  if (!hasAccess) {
    if (fallback) {
      return <>{fallback}</>;
    }

    if (!showUnauthorizedMessage) {
      return null;
    }

    return (
      <div className="flex items-center justify-center min-h-[400px] p-6">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="flex justify-center mb-4">
              <div className="rounded-full bg-red-100 p-3">
                <Lock className="h-8 w-8 text-red-600" />
              </div>
            </div>
            <CardTitle className="text-xl font-semibold text-red-900">
              Access Denied
            </CardTitle>
          </CardHeader>
          <CardContent className="text-center space-y-4">
            <p className="text-muted-foreground">
              You are not authorized to access this page.
            </p>
            <div className="space-y-2">
              <p className="text-sm text-muted-foreground">
                <strong>Required roles:</strong> {allowedRoles.join(", ")}
              </p>
              {userRole && (
                <p className="text-sm text-muted-foreground">
                  <strong>Your role:</strong> {userRole}
                </p>
              )}
            </div>
            <div className="pt-4">
              <Button
                variant="outline"
                onClick={() => router.back()}
                className="w-full"
              >
                Go Back
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return <>{children}</>;
}

// Specific guards for common role combinations
export function AdminOnlyGuard({
  children,
  tenantId,
  fallback,
  ...props
}: Omit<RoleGuardProps, "allowedRoles">) {
  return (
    <RoleGuard
      allowedRoles={["ADMIN", "OWNER"]}
      tenantId={tenantId}
      fallback={fallback}
      {...props}
    >
      {children}
    </RoleGuard>
  );
}

export function OwnerOnlyGuard({
  children,
  tenantId,
  fallback,
  ...props
}: Omit<RoleGuardProps, "allowedRoles">) {
  return (
    <RoleGuard
      allowedRoles={["OWNER"]}
      tenantId={tenantId}
      fallback={fallback}
      {...props}
    >
      {children}
    </RoleGuard>
  );
}

// Hook to check user role
export function useUserRole(tenantId?: string) {
  const { data: session, status } = useSession();

  if (status === "loading" || !session) {
    return {
      role: null,
      hasRole: (roles: UserRole[]) => false,
      isAdmin: false,
      isOwner: false,
      isMember: false,
      loading: status === "loading",
    };
  }

  const memberships = (session as any)?.memberships || [];
  let userRole: UserRole | null = null;

  if (tenantId) {
    const membership = memberships.find((m: any) => m.tenant.id === tenantId);
    userRole = membership?.role || null;
  } else if (memberships.length > 0) {
    userRole = memberships[0]?.role || null;
  }

  return {
    role: userRole,
    hasRole: (roles: UserRole[]) => userRole ? roles.includes(userRole) : false,
    isAdmin: userRole === "ADMIN" || userRole === "OWNER",
    isOwner: userRole === "OWNER",
    isMember: userRole === "MEMBER",
    loading: false,
  };
}

// Component to conditionally render based on role
export function RoleBasedComponent({
  children,
  allowedRoles,
  tenantId,
  fallback = null,
}: {
  children: React.ReactNode;
  allowedRoles: UserRole[];
  tenantId?: string;
  fallback?: React.ReactNode;
}) {
  const { hasRole } = useUserRole(tenantId);

  if (hasRole(allowedRoles)) {
    return <>{children}</>;
  }

  return <>{fallback}</>;
}

// Higher-order component for role protection
export function withRoleGuard<T extends object>(
  Component: React.ComponentType<T>,
  allowedRoles: UserRole[],
  options?: {
    tenantId?: string;
    fallback?: React.ReactNode;
    redirectTo?: string;
  }
) {
  return function RoleProtectedComponent(props: T) {
    return (
      <RoleGuard
        allowedRoles={allowedRoles}
        tenantId={options?.tenantId}
        fallback={options?.fallback}
        redirectTo={options?.redirectTo}
      >
        <Component {...props} />
      </RoleGuard>
    );
  };
}
