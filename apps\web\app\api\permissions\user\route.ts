import { NextResponse } from "next/server";
import { getToken } from "next-auth/jwt";
import { getUserPermissions } from "@/lib/permissions";

/**
 * GET /api/permissions/user
 * Get all permissions for the current user
 */
export async function GET(request: Request) {
  try {
    const url = new URL(request.url);
    const userIdFromParams = url.searchParams.get("userId");
    const tenantId =
      url.searchParams.get("tenantId") || request.headers.get("x-tenant-id");
    const workspaceId = url.searchParams.get("workspaceId");
    const rawWorkspaceSlug =
      url.searchParams.get("workspaceSlug") ||
      request.headers.get("x-workspace-slug");
    const workspaceSlug = rawWorkspaceSlug
      ? decodeURI(rawWorkspaceSlug)
      : undefined;

    // Get the user from the session for client-side calls
    const token = await getToken({ req: request as any });

    // Use userId from params if token is null (server-side call)
    // or use token.sub if available (client-side call)
    const userId =
      userIdFromParams || request.headers.get("x-user-id") || token?.sub;

    if (!userId) {
      return NextResponse.json(
        {
          error:
            "Unauthorized - userId is required either in token, headers, or as a query parameter",
        },
        { status: 401 }
      );
    }

    if (!tenantId) {
      return NextResponse.json(
        {
          error:
            "TenantId is required in query parameters or x-tenant-id header",
        },
        { status: 400 }
      );
    }

    // Prioritize workspaceSlug over workspaceId if both are provided
    const workspaceIdentifier = workspaceSlug || workspaceId;

    // Get all permissions for the user, including workspace-specific permissions if workspaceId or workspaceSlug is provided
    const permissions = await getUserPermissions(
      userId,
      tenantId,
      workspaceIdentifier || undefined
    );

    return NextResponse.json({ permissions });
  } catch (error: any) {
    console.error("Error fetching user permissions:", error);
    return NextResponse.json(
      { error: "Failed to fetch user permissions", details: error.message },
      { status: 500 }
    );
  }
}
