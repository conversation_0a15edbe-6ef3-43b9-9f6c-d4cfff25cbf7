import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";

export default async function middleware(request: NextRequest) {
  try {
    const requestHeaders = new Headers(request.headers);
    requestHeaders.set("x-next-pathname", request.nextUrl.pathname);
    requestHeaders.set("x-next-baseurl", request.nextUrl.origin);

    // Get the preferred language from a cookie
    const languageCookie = request.cookies.get("language")?.value;
    if (languageCookie) {
      requestHeaders.set("x-language", languageCookie);
    }
    const response = await NextResponse.next({
      request: { headers: requestHeaders },
    });

    return response;
  } catch (error) {
    return NextResponse.error();
  }
}

export const config = {
  matcher: [
    "/((?!_next/static|_next/image|.swa/health|favicon.ico).*)",
    "/((?!.swa).*)",
  ],
};
