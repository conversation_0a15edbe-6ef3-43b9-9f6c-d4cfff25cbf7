import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/next-auth";
import db from "@/lib/shared-db";

export async function GET(request: Request) {
  const session = await getServerSession(authOptions);

  if (!session) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const { searchParams } = new URL(request.url);
    const period = searchParams.get("period") || "7months"; // '7days', '30days', '7months', etc.

    // Get the latest storage usage for each tenant
    const latestUsageByTenant = await db.vectorStoreUsage.groupBy({
      by: ["tenantId"],
      where: {},
      _max: {
        timestamp: true,
      },
    });

    // Get the actual usage values for the latest timestamps
    const tenantIds = latestUsageByTenant.map((item) => item.tenantId);
    const latestTimestamps = latestUsageByTenant.map(
      (item) => item._max.timestamp
    );

    const latestUsages = await db.vectorStoreUsage.findMany({
      where: {
        OR: latestUsageByTenant.map((item) => ({
          tenantId: item.tenantId,
          timestamp: item._max.timestamp,
        })),
      },
    });

    // Calculate total storage used
    const totalStorageGB = latestUsages.reduce(
      (sum, item) => sum + item.usageGB,
      0
    );

    // Get tenant names
    const tenants = await db.tenant.findMany({
      where: {
        id: {
          in: tenantIds,
        },
      },
      select: {
        id: true,
        name: true,
      },
    });

    // Map tenant names to the results
    const storageByTenant = latestUsages.map((item) => {
      const tenant = tenants.find((t) => t.id === item.tenantId);
      return {
        tenantId: item.tenantId,
        tenantName: tenant?.name || "Unknown",
        usageGB: item.usageGB,
      };
    });

    // Sort by usage (descending)
    storageByTenant.sort((a, b) => b.usageGB - a.usageGB);

    // Calculate storage growth rate (last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    // Get the oldest usage in the last 30 days for each tenant
    const oldestUsageByTenant = await db.vectorStoreUsage.groupBy({
      by: ["tenantId"],
      where: {
        timestamp: {
          gte: thirtyDaysAgo,
        },
      },
      _min: {
        timestamp: true,
      },
    });

    // Get the actual usage values for the oldest timestamps
    const oldestUsages = await db.vectorStoreUsage.findMany({
      where: {
        OR: oldestUsageByTenant.map((item) => ({
          tenantId: item.tenantId,
          timestamp: item._min.timestamp,
        })),
      },
    });

    // Calculate total storage 30 days ago
    const totalStorageGB30DaysAgo = oldestUsages.reduce(
      (sum, item) => sum + item.usageGB,
      0
    );

    // Calculate growth rate
    const storageGrowthRate =
      totalStorageGB30DaysAgo > 0
        ? ((totalStorageGB - totalStorageGB30DaysAgo) /
            totalStorageGB30DaysAgo) *
          100
        : 0;

    // Handle period parameter for chart data
    if (period) {
      const match = period.match(/^(\d+)(days|months)$/);
      if (!match) {
        return NextResponse.json(
          {
            error:
              "Invalid period format. Use format like '7days' or '7months'",
          },
          { status: 400 }
        );
      }

      const amount = parseInt(match[1], 10);
      const unit = match[2];

      // Calculate the start date
      const startDate = new Date();
      if (unit === "days") {
        startDate.setDate(startDate.getDate() - amount);
      } else if (unit === "months") {
        startDate.setMonth(startDate.getMonth() - amount);
      }

      // Get monthly storage data
      const monthlyData: any = [];

      if (unit === "months") {
        // Get all storage usage records in the period
        const storageUsage = await db.vectorStoreUsage.findMany({
          where: {
            timestamp: {
              gte: startDate,
            },
          },
          orderBy: {
            timestamp: "asc",
          },
        });

        // Group by month manually
        const monthlyGroups: Record<string, any> = {};

        storageUsage.forEach((item: any) => {
          const monthYear = `${item.timestamp.getFullYear()}-${
            item.timestamp.getMonth() + 1
          }`;

          // We want the latest usage for each month
          if (
            !monthlyGroups[monthYear] ||
            item.timestamp > monthlyGroups[monthYear].timestamp
          ) {
            monthlyGroups[monthYear] = item;
          }
        });

        // Convert to array and format for chart
        const monthNames = [
          "Jan",
          "Feb",
          "Mar",
          "Apr",
          "May",
          "Jun",
          "Jul",
          "Aug",
          "Sep",
          "Oct",
          "Nov",
          "Dec",
        ];

        for (const [monthYear, data] of Object.entries(monthlyGroups)) {
          const [year, month] = monthYear.split("-").map(Number);
          const dataObj = data as any;
          monthlyData.push({
            name: monthNames[month - 1],
            value: dataObj?.usageGB,
          });
        }

        // Sort by date
        monthlyData.sort((a: any, b: any) => {
          const monthA = monthNames.indexOf(a.name);
          const monthB = monthNames.indexOf(b.name);
          return monthA - monthB;
        });
      }

      return NextResponse.json({
        totalStorageGB: Math.round(totalStorageGB * 100) / 100,
        storageGrowthRate: Math.round(storageGrowthRate * 100) / 100,
        storageByTenant,
        monthlyUsage: monthlyData,
      });
    }

    return NextResponse.json({
      totalStorageGB: Math.round(totalStorageGB * 100) / 100,
      storageGrowthRate: Math.round(storageGrowthRate * 100) / 100,
      storageByTenant,
    });
  } catch (error) {
    console.error("Error fetching storage usage metrics:", error);
    return NextResponse.json(
      { error: "Failed to fetch storage usage metrics" },
      { status: 500 }
    );
  }
}
