"use client";

import { DeleteConfirmationDialog } from "./delete-confirmation-dialog";
import { useLanguage } from "@/lib/language-context";

interface WorkspaceDeleteConfirmationProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  workspaceName: string;
  isLoading?: boolean;
}

export function WorkspaceDeleteConfirmation({
  isOpen,
  onClose,
  onConfirm,
  workspaceName,
  isLoading = false,
}: WorkspaceDeleteConfirmationProps) {
  const { t } = useLanguage();

  return (
    <DeleteConfirmationDialog
      isOpen={isOpen}
      onClose={onClose}
      onConfirm={onConfirm}
      title={t("workspace.deleteWorkspace")}
      description={t("workspace.deleteWorkspaceConfirm")}
      warningText={t("workspace.deleteWorkspaceWarning", { name: workspaceName })}
      requireTypeConfirmation={true}
      typeConfirmationText={workspaceName}
      typeConfirmationPlaceholder={workspaceName}
      confirmText={t("workspace.deleteWorkspaceTypeConfirm", { name: workspaceName })}
      isLoading={isLoading}
      loadingText={t("workspace.deletingWorkspace")}
      destructiveActionText={t("workspace.deleteWorkspacePermanently")}
    />
  );
}
