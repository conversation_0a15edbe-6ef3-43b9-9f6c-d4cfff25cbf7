"use client";

import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { formatDate } from "@/lib/utils";
import {
  Calendar,
  User,
  GitBranch,
  Package,
  Globe,
  Users,
  Clock,
  AlertCircle,
  CheckCircle,
  XCircle,
} from "lucide-react";

// TypeScript interfaces
interface Changelog {
  id: string;
  title: string;
  content: string;
  version?: string;
  type: "RELEASE" | "HOTFIX" | "MAINTENANCE" | "ANNOUNCEMENT";
  priority: "LOW" | "MEDIUM" | "HIGH" | "CRITICAL";
  isActive: boolean;
  targetTenants: string[];
  targetEnvironment?: string;
  publishedAt: Date;
  expiresAt?: Date;
  githubCommitSha?: string;
  deploymentId?: string;
  authorId?: string;
  createdAt: Date;
  updatedAt: Date;
}

interface ChangelogDetailModalProps {
  changelog: Changelog | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

// Helper functions for styling
const getTypeBadgeStyle = (type: string) => {
  switch (type) {
    case "RELEASE":
      return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";
    case "HOTFIX":
      return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200";
    case "MAINTENANCE":
      return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200";
    case "ANNOUNCEMENT":
      return "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200";
    default:
      return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200";
  }
};

const getPriorityBadgeStyle = (priority: string) => {
  switch (priority) {
    case "CRITICAL":
      return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200";
    case "HIGH":
      return "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200";
    case "MEDIUM":
      return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200";
    case "LOW":
      return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200";
    default:
      return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200";
  }
};

const getTypeLabel = (type: string) => {
  switch (type) {
    case "RELEASE":
      return "New Feature";
    case "HOTFIX":
      return "Bug Fix";
    case "MAINTENANCE":
      return "Improvement";
    case "ANNOUNCEMENT":
      return "Announcement";
    default:
      return type;
  }
};

export function ChangelogDetailModal({
  changelog,
  open,
  onOpenChange,
}: ChangelogDetailModalProps) {
  if (!changelog) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader className="space-y-4">
          <div className="flex flex-col space-y-3">
            {/* Title and Version */}
            <div className="flex flex-col space-y-2 md:flex-row md:items-start md:justify-between md:space-y-0">
              <div className="space-y-2">
                <DialogTitle className="text-2xl font-bold leading-tight">
                  {changelog.title}
                </DialogTitle>
                {changelog.version && (
                  <Badge variant="outline" className="text-sm w-fit">
                    Version {changelog.version}
                  </Badge>
                )}
              </div>
              <div className="flex items-center space-x-2">
                <Badge className={getTypeBadgeStyle(changelog.type)}>
                  {getTypeLabel(changelog.type)}
                </Badge>
                <Badge className={getPriorityBadgeStyle(changelog.priority)}>
                  {changelog.priority}
                </Badge>
                {changelog.isActive ? (
                  <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                    <CheckCircle className="h-3 w-3 mr-1" />
                    Active
                  </Badge>
                ) : (
                  <Badge variant="outline" className="text-muted-foreground">
                    <XCircle className="h-3 w-3 mr-1" />
                    Inactive
                  </Badge>
                )}
              </div>
            </div>

            {/* Basic Info */}
            <div className="flex flex-wrap items-center gap-4 text-sm text-muted-foreground">
              <div className="flex items-center space-x-1">
                <Calendar className="h-4 w-4" />
                <span>Published: {formatDate(changelog.publishedAt)}</span>
              </div>
              {changelog.authorId && (
                <div className="flex items-center space-x-1">
                  <User className="h-4 w-4" />
                  <span>Author: {changelog.authorId}</span>
                </div>
              )}
              {changelog.expiresAt && (
                <div className="flex items-center space-x-1">
                  <AlertCircle className="h-4 w-4" />
                  <span>Expires: {formatDate(changelog.expiresAt)}</span>
                </div>
              )}
            </div>
          </div>
        </DialogHeader>

        <Separator />

        {/* Content Section */}
        <div className="space-y-6">
          <div>
            <h3 className="text-lg font-semibold mb-3">Description</h3>
            <div className="prose prose-sm max-w-none dark:prose-invert">
              <div className="whitespace-pre-wrap text-sm leading-relaxed">
                {changelog.content}
              </div>
            </div>
          </div>

          <Separator />

          {/* Metadata Section */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Metadata</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Left Column */}
              <div className="space-y-4">
                {changelog.targetEnvironment && (
                  <div className="flex items-start space-x-3">
                    <Globe className="h-4 w-4 mt-0.5 text-muted-foreground" />
                    <div>
                      <p className="text-sm font-medium">Target Environment</p>
                      <Badge variant="outline" className="mt-1">
                        {changelog.targetEnvironment}
                      </Badge>
                    </div>
                  </div>
                )}

                {changelog.targetTenants.length > 0 && (
                  <div className="flex items-start space-x-3">
                    <Users className="h-4 w-4 mt-0.5 text-muted-foreground" />
                    <div>
                      <p className="text-sm font-medium">Target Tenants</p>
                      <p className="text-sm text-muted-foreground mt-1">
                        {changelog.targetTenants.length} tenant(s) targeted
                      </p>
                    </div>
                  </div>
                )}

                {changelog.githubCommitSha && (
                  <div className="flex items-start space-x-3">
                    <GitBranch className="h-4 w-4 mt-0.5 text-muted-foreground" />
                    <div>
                      <p className="text-sm font-medium">GitHub Commit</p>
                      <code className="text-xs bg-muted px-2 py-1 rounded mt-1 block w-fit">
                        {changelog.githubCommitSha}
                      </code>
                    </div>
                  </div>
                )}

                {changelog.deploymentId && (
                  <div className="flex items-start space-x-3">
                    <Package className="h-4 w-4 mt-0.5 text-muted-foreground" />
                    <div>
                      <p className="text-sm font-medium">Deployment ID</p>
                      <code className="text-xs bg-muted px-2 py-1 rounded mt-1 block w-fit">
                        {changelog.deploymentId}
                      </code>
                    </div>
                  </div>
                )}
              </div>

              {/* Right Column */}
              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <Clock className="h-4 w-4 mt-0.5 text-muted-foreground" />
                  <div>
                    <p className="text-sm font-medium">Created</p>
                    <p className="text-sm text-muted-foreground mt-1">
                      {formatDate(changelog.createdAt)}
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <Clock className="h-4 w-4 mt-0.5 text-muted-foreground" />
                  <div>
                    <p className="text-sm font-medium">Last Updated</p>
                    <p className="text-sm text-muted-foreground mt-1">
                      {formatDate(changelog.updatedAt)}
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <AlertCircle className="h-4 w-4 mt-0.5 text-muted-foreground" />
                  <div>
                    <p className="text-sm font-medium">Status</p>
                    <p className="text-sm text-muted-foreground mt-1">
                      {changelog.isActive ? "Active and visible" : "Inactive and hidden"}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
