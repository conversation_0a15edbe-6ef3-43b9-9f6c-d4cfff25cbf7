import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/next-auth";
import db from "@/lib/shared-db";
import { objectsToCSV } from "@/lib/export-utils";

export async function GET(request: Request) {
  const session = await getServerSession(authOptions);

  if (!session) {
    return NextResponse.json(
      { error: "Unauthorized" },
      { status: 401 }
    );
  }

  try {
    const { searchParams } = new URL(request.url);
    const fromDate = searchParams.get("from") ? new Date(searchParams.get("from") as string) : new Date(new Date().setDate(new Date().getDate() - 30));
    const toDate = searchParams.get("to") ? new Date(searchParams.get("to") as string) : new Date();
    
    // Get all tenants with their details
    const tenants = await db.tenant.findMany({
      where: {
        createdAt: {
          lte: toDate
        }
      },
      include: {
        Subscription: {
          where: {
            isActive: true
          },
          include: {
            plan: true
          }
        },
        _count: {
          select: {
            User: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });
    
    // Get API requests for each tenant
    const tenantIds = tenants.map(tenant => tenant.id);
    const apiRequests = await db.aPIRequest.groupBy({
      by: ['tenantId'],
      where: {
        tenantId: {
          in: tenantIds
        },
        timestamp: {
          gte: fromDate,
          lte: toDate
        }
      },
      _count: {
        _all: true
      }
    });
    
    // Get vector store usage for each tenant
    const vectorStoreUsage = await db.vectorStoreUsage.groupBy({
      by: ['tenantId'],
      where: {
        tenantId: {
          in: tenantIds
        },
        timestamp: {
          gte: fromDate,
          lte: toDate
        }
      },
      _max: {
        usageGB: true
      }
    });
    
    // Format the data for CSV export
    const exportData = tenants.map(tenant => {
      // Find API requests for this tenant
      const apiRequest = apiRequests.find(req => req.tenantId === tenant.id);
      
      // Find vector store usage for this tenant
      const vectorUsage = vectorStoreUsage.find(usage => usage.tenantId === tenant.id);
      
      // Get subscription details
      const subscription = tenant.Subscription[0];
      
      return {
        id: tenant.id,
        name: tenant.name,
        createdAt: tenant.createdAt.toISOString(),
        status: subscription ? 'Active' : 'Inactive',
        plan: subscription?.plan?.name || 'No Plan',
        planType: subscription?.plan?.type || 'None',
        billingInterval: subscription?.billingInterval || 'None',
        userCount: tenant._count.User,
        additionalUsers: subscription?.additionalUsers || 0,
        additionalStorageGB: subscription?.additionalStorageGB || 0,
        apiRequestCount: apiRequest?._count._all || 0,
        storageUsageGB: vectorUsage?._max.usageGB || 0,
        isOnboarded: tenant.isOnboarded ? 'Yes' : 'No'
      };
    });
    
    // Define CSV headers
    const headers = [
      'id',
      'name',
      'createdAt',
      'status',
      'plan',
      'planType',
      'billingInterval',
      'userCount',
      'additionalUsers',
      'additionalStorageGB',
      'apiRequestCount',
      'storageUsageGB',
      'isOnboarded'
    ];
    
    // Generate CSV content
    const csvContent = objectsToCSV(exportData, headers);
    
    // Return CSV content
    return new NextResponse(csvContent, {
      headers: {
        'Content-Type': 'text/csv',
        'Content-Disposition': `attachment; filename="tenant_metrics.csv"`
      }
    });
  } catch (error) {
    console.error("Error exporting tenant metrics:", error);
    return NextResponse.json(
      { error: "Failed to export tenant metrics" },
      { status: 500 }
    );
  }
}
