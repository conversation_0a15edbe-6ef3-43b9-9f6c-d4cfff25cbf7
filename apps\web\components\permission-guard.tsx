"use client";

import { ReactNode } from "react";
import { usePermissions } from "@/hooks/use-permissions";

interface PermissionGuardProps {
  action: "CREATE" | "READ" | "UPDATE" | "DELETE";
  resource: "WORKSPACE" | "PAGE" | "FOLDER" | "FILE" | "MEMBER";
  hasPermission;
  children: ReactNode;
  fallback?: ReactNode;
}

/**
 * A component that conditionally renders its children based on user permissions
 *
 * @example
 * <PermissionGuard action="CREATE" resource="WORKSPACE">
 *   <Button>Create Workspace</Button>
 * </PermissionGuard>
 */
export function PermissionGuard({
  action,
  resource,
  children,
  hasPermission,
  fallback = null,
}: PermissionGuardProps) {
  if (hasPermission) {
    return <>{children}</>;
  } else if (hasPermission === false) {
    return <>{fallback}</>;
  }
  const { hasPermission: hasPermissionHook, loading } = usePermissions();

  // While loading permissions, don't render anything
  if (loading) {
    return null;
  }

  // If the user has the permission, render the children
  if (hasPermissionHook(action, resource)) {
    return <>{children}</>;
  }

  // Otherwise, render the fallback or null
  return <>{fallback}</>;
}

/**
 * A component that conditionally renders its children if the user can create a resource
 */
export function CreateGuard({
  resource,
  children,
  hasPermission,
  fallback = null,
}: Omit<PermissionGuardProps, "action">) {
  return (
    <PermissionGuard
      action="CREATE"
      hasPermission={hasPermission}
      resource={resource}
      fallback={fallback}
    >
      {children}
    </PermissionGuard>
  );
}

/**
 * A component that conditionally renders its children if the user can read a resource
 */
export function ReadGuard({
  resource,
  children,
  hasPermission,
  fallback = null,
}: Omit<PermissionGuardProps, "action">) {
  return (
    <PermissionGuard
      action="READ"
      hasPermission={hasPermission}
      resource={resource}
      fallback={fallback}
    >
      {children}
    </PermissionGuard>
  );
}

/**
 * A component that conditionally renders its children if the user can update a resource
 */
export function UpdateGuard({
  resource,
  children,
  hasPermission,
  fallback = null,
}: Omit<PermissionGuardProps, "action">) {
  return (
    <PermissionGuard
      action="UPDATE"
      hasPermission={hasPermission}
      resource={resource}
      fallback={fallback}
    >
      {children}
    </PermissionGuard>
  );
}

/**
 * A component that conditionally renders its children if the user can delete a resource
 */
export function DeleteGuard({
  resource,
  children,
  hasPermission,
  fallback = null,
}: Omit<PermissionGuardProps, "action">) {
  return (
    <PermissionGuard
      action="DELETE"
      hasPermission={hasPermission}
      resource={resource}
      fallback={fallback}
    >
      {children}
    </PermissionGuard>
  );
}
