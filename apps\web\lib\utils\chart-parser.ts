/**
 * Chart detection and parsing utilities for markdown content
 */

export interface ChartData {
  type: 'line' | 'bar' | 'area' | 'pie' | 'scatter' | 'composed';
  data: any[];
  config?: {
    xKey?: string;
    yKey?: string;
    title?: string;
    description?: string;
    color?: string;
    showLegend?: boolean;
    height?: number;
    width?: number;
  };
}

export interface MermaidDiagram {
  type: 'mermaid';
  content: string;
  config?: {
    theme?: 'default' | 'dark' | 'forest' | 'neutral';
    title?: string;
  };
}

export interface ParsedChart {
  startIndex: number;
  endIndex: number;
  chart: ChartData | MermaidDiagram;
}

/**
 * Detects chart syntax in markdown content
 * Supports formats like:
 * ```chart:line
 * ```chart:bar
 * ```mermaid
 * ```json:chart
 */
export const detectCharts = (content: string): ParsedChart[] => {
  const lines = content.split('\n');
  const charts: ParsedChart[] = [];

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim();
    
    // Check for chart code blocks
    if (line.startsWith('```')) {
      const chartMatch = line.match(/^```(chart:(\w+)|mermaid|json:chart)(.*)$/);
      if (chartMatch) {
        const [, fullType, chartType] = chartMatch;
        
        // Find the closing ```
        let endIndex = -1;
        let chartContent = '';
        
        for (let j = i + 1; j < lines.length; j++) {
          if (lines[j].trim() === '```') {
            endIndex = j;
            break;
          }
          chartContent += lines[j] + '\n';
        }
        
        if (endIndex !== -1) {
          if (fullType === 'mermaid') {
            // Parse Mermaid diagram
            charts.push({
              startIndex: i,
              endIndex,
              chart: {
                type: 'mermaid',
                content: chartContent.trim(),
                config: {}
              }
            });
          } else {
            // Parse chart data
            const parsedChart = parseChartContent(chartContent.trim(), chartType || 'line');
            if (parsedChart) {
              charts.push({
                startIndex: i,
                endIndex,
                chart: parsedChart
              });
            }
          }
          
          i = endIndex; // Skip processed lines
        }
      }
    }
  }

  return charts;
};

/**
 * Parses chart content from various formats
 */
export const parseChartContent = (content: string, type: string): ChartData | null => {
  try {
    // Try to parse as JSON first
    let data: any;
    let config: any = {};
    
    if (content.trim().startsWith('{') || content.trim().startsWith('[')) {
      // JSON format
      const parsed = JSON.parse(content);
      if (Array.isArray(parsed)) {
        data = parsed;
      } else if (parsed.data) {
        data = parsed.data;
        config = parsed.config || {};
      } else {
        data = [parsed];
      }
    } else {
      // Try to parse as CSV-like format
      data = parseCSVLikeData(content);
    }

    if (!data || !Array.isArray(data) || data.length === 0) {
      return null;
    }

    return {
      type: type as ChartData['type'],
      data,
      config
    };
  } catch (error) {
    console.error('Error parsing chart content:', error);
    return null;
  }
};

/**
 * Parses CSV-like data format
 */
export const parseCSVLikeData = (content: string): any[] => {
  const lines = content.trim().split('\n');
  if (lines.length < 2) return [];

  const headers = lines[0].split(',').map(h => h.trim());
  const data: any[] = [];

  for (let i = 1; i < lines.length; i++) {
    const values = lines[i].split(',').map(v => v.trim());
    if (values.length === headers.length) {
      const row: any = {};
      headers.forEach((header, index) => {
        const value = values[index];
        // Try to parse as number, otherwise keep as string
        row[header] = isNaN(Number(value)) ? value : Number(value);
      });
      data.push(row);
    }
  }

  return data;
};

/**
 * Validates chart data structure
 */
export const validateChartData = (chart: ChartData): boolean => {
  if (!chart.data || !Array.isArray(chart.data) || chart.data.length === 0) {
    return false;
  }

  // Check if data has consistent structure
  const firstItem = chart.data[0];
  if (typeof firstItem !== 'object' || firstItem === null) {
    return false;
  }

  const keys = Object.keys(firstItem);
  return chart.data.every(item => 
    typeof item === 'object' && 
    item !== null && 
    keys.every(key => key in item)
  );
};

/**
 * Generates default configuration for chart types
 */
export const getDefaultChartConfig = (type: ChartData['type'], data: any[]): Partial<ChartData['config']> => {
  if (!data || data.length === 0) return {};

  const firstItem = data[0];
  const keys = Object.keys(firstItem);
  
  // Try to detect x and y keys
  const xKey = keys.find(key => 
    typeof firstItem[key] === 'string' || 
    key.toLowerCase().includes('name') ||
    key.toLowerCase().includes('label') ||
    key.toLowerCase().includes('date') ||
    key.toLowerCase().includes('time')
  ) || keys[0];
  
  const yKey = keys.find(key => 
    typeof firstItem[key] === 'number' && key !== xKey
  ) || keys[1];

  const config: Partial<ChartData['config']> = {
    xKey,
    yKey,
    height: 300,
    showLegend: keys.length > 2
  };

  // Type-specific defaults
  switch (type) {
    case 'pie':
      config.showLegend = true;
      break;
    case 'line':
    case 'area':
      config.color = 'blue';
      break;
    case 'bar':
      config.color = 'indigo';
      break;
  }

  return config;
};

/**
 * Extracts chart syntax examples for documentation
 */
export const getChartSyntaxExamples = () => {
  return {
    line: `\`\`\`chart:line
{
  "data": [
    {"month": "Jan", "sales": 100},
    {"month": "Feb", "sales": 150},
    {"month": "Mar", "sales": 200}
  ],
  "config": {
    "title": "Monthly Sales",
    "xKey": "month",
    "yKey": "sales"
  }
}
\`\`\``,
    
    bar: `\`\`\`chart:bar
month,sales,profit
Jan,100,20
Feb,150,30
Mar,200,40
\`\`\``,
    
    mermaid: `\`\`\`mermaid
graph TD
    A[Start] --> B{Decision}
    B -->|Yes| C[Action 1]
    B -->|No| D[Action 2]
    C --> E[End]
    D --> E
\`\`\``
  };
};
