import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/next-auth";
import dbPromise from "@/lib/shared-db";

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  let body: any = null;

  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    const db = await dbPromise;
    const tenantId = params.id;

    // Parse request body to get the desired suspension status
    body = await request.json();
    const { status } = body;

    console.log(`Processing tenant suspension update request for tenant ${tenantId}:`, { status });

    // Validate payload - status should be "active", "suspended", or "inactive"
    const validStatuses = ["active", "suspended", "inactive"];
    if (!validStatuses.includes(status)) {
      return NextResponse.json(
        { error: `Status must be one of: ${validStatuses.join(", ")}` },
        { status: 400 }
      );
    }

    // Verify tenant exists
    const tenant = await db.tenant.findUnique({
      where: { id: tenantId },
      select: {
        id: true,
        name: true,
        Subscription: {
          where: { isActive: true },
          select: {
            id: true,
            isActive: true
          }
        }
      }
    });

    if (!tenant) {
      return NextResponse.json(
        { error: "Tenant not found" },
        { status: 404 }
      );
    }

    // Get current status for logging
    const hasActiveSubscription = tenant.Subscription.length > 0;
    const currentStatus = hasActiveSubscription ? "active" : "inactive";

    // Update tenant subscription status based on the requested status
    let updatedTenant;
    
    if (status === "suspended") {
      // Suspend tenant by deactivating their subscription
      if (tenant.Subscription.length > 0) {
        await db.subscription.updateMany({
          where: {
            tenantId: tenantId,
            isActive: true
          },
          data: {
            isActive: false,
            updatedAt: new Date()
          }
        });
      }
      
      // Update tenant record
      updatedTenant = await db.tenant.update({
        where: { id: tenantId },
        data: {
          updatedAt: new Date()
        },
        select: {
          id: true,
          name: true,
          Subscription: {
            where: { tenantId: tenantId },
            select: {
              id: true,
              isActive: true
            }
          }
        }
      });
      
    } else if (status === "active") {
      // Reactivate tenant by activating their subscription
      if (tenant.Subscription.length > 0) {
        await db.subscription.updateMany({
          where: {
            tenantId: tenantId
          },
          data: {
            isActive: true,
            updatedAt: new Date()
          }
        });
      }
      
      // Update tenant record
      updatedTenant = await db.tenant.update({
        where: { id: tenantId },
        data: {
          updatedAt: new Date()
        },
        select: {
          id: true,
          name: true,
          Subscription: {
            where: { tenantId: tenantId },
            select: {
              id: true,
              isActive: true
            }
          }
        }
      });
      
    } else {
      // For "inactive" status, just update the tenant record
      updatedTenant = await db.tenant.update({
        where: { id: tenantId },
        data: {
          updatedAt: new Date()
        },
        select: {
          id: true,
          name: true,
          Subscription: {
            where: { tenantId: tenantId },
            select: {
              id: true,
              isActive: true
            }
          }
        }
      });
    }

    // Calculate the new status based on subscription state
    const newHasActiveSubscription = updatedTenant.Subscription.some(sub => sub.isActive);
    const newStatus = status === "suspended" ? "suspended" : 
                     newHasActiveSubscription ? "active" : "inactive";

    // Log the suspension change for audit purposes
    console.log(`Tenant status updated for tenant ${tenantId}: ${currentStatus} -> ${newStatus}`);

    return NextResponse.json({
      message: `Tenant ${newStatus === "suspended" ? "suspended" : newStatus === "active" ? "reactivated" : "updated"} successfully`,
      tenant: {
        id: updatedTenant.id,
        name: updatedTenant.name,
        status: newStatus,
        hasActiveSubscription: newHasActiveSubscription
      }
    });

  } catch (error) {
    console.error("Error updating tenant status:", error);
    
    return NextResponse.json(
      { 
        error: "Failed to update tenant status",
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
