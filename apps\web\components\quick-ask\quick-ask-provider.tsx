"use client";

import React, { createContext, useContext, useState, useEffect } from "react";
import { QuickAskDialog } from "./quick-ask-dialog";
import { useLanguage } from "@/lib/language-context";
import { Sparkles } from "lucide-react";
import { cn } from "@/lib/utils";

interface QuickAskContextType {
  openQuickAsk: () => void;
  closeQuickAsk: () => void;
}

const QuickAskContext = createContext<QuickAskContextType>({
  openQuickAsk: () => {},
  closeQuickAsk: () => {},
});

export const useQuickAsk = () => useContext(QuickAskContext);

interface QuickAskProviderProps {
  children: React.ReactNode;
  userId: string;
  tenantId: string;
  userName: string;
  isReady?: boolean;
}

export function QuickAskProvider({
  children,
  userId,
  tenantId,
  userName,
  isReady = true,
}: QuickAskProviderProps) {
  const [open, setOpen] = useState(false);

  // Handle keyboard shortcut (Cmd/Ctrl + Shift + K for Quick Ask)
  // Using Shift+K to differentiate from global search (Cmd/Ctrl + K)
  useEffect(() => {
    // Only enable keyboard shortcut if we have the required data
    if (!isReady) return;

    const handleKeyDown = (e: KeyboardEvent) => {
      if ((e.metaKey || e.ctrlKey) && e.shiftKey && e.key === "K") {
        e.preventDefault();
        setOpen((prev) => !prev);
      }
    };

    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [isReady]);

  const openQuickAsk = () => {
    if (isReady) {
      setOpen(true);
    }
  };

  const closeQuickAsk = () => setOpen(false);

  return (
    <QuickAskContext.Provider value={{ openQuickAsk, closeQuickAsk }}>
      {children}
      {isReady && (
        <QuickAskDialog
          userId={userId}
          userName={userName}
          open={open}
          onOpenChange={setOpen}
        />
      )}
    </QuickAskContext.Provider>
  );
}

// Client component wrapper for server components
export function QuickAskButton() {
  const { openQuickAsk } = useQuickAsk();
  const { t } = useLanguage();

  return (
    <button
      onClick={openQuickAsk}
      className={cn(
        "group relative inline-flex items-center justify-center gap-2",
        "rounded-full text-sm font-medium",
        "bg-gradient-to-r from-primary/10 via-primary/5 to-transparent",
        "border border-primary/20 shadow-sm ",
        "hover:shadow-md hover:border-primary/30 hover:from-primary/20",
        "transition-all duration-200 ease-in-out",
        "h-9 md:px-4 md:py-2",
        "px-2 py-1" // smaller padding for mobile
      )}
    >
      <Sparkles className="h-3.5 w-3.5 text-primary/70 group-hover:text-primary transition-colors" />
      <span className="hidden md:inline font-medium">
        {t("quickAsk.buttonLabel") || "Quick Ask"}
      </span>
    </button>
  );
}
