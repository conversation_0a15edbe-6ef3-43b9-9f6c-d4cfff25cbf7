"use client";

import { useState, useEffect } from "react";
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  AlertCircle,
  Clock,
  RefreshCw,
  ChevronLeft,
  ChevronRight,
  ExternalLink,
  Download,
  TrendingUp,
  BarChart3,
  Activity,
  AlertTriangle
} from "lucide-react";
import { formatDate } from "@/lib/utils";
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  BarChart,
  Bar
} from 'recharts';

interface SystemError {
  id: string;
  timestamp: Date;
  statusCode: number;
  endpoint: string;
  method: string;
  tenantId?: string;
  tenantName?: string;
  errorMessage?: string;
  userAgent?: string;
  ipAddress?: string;
}

interface AnalyticsData {
  totalErrors: number;
  errorRate: number;
  trendData: Array<{ date: string; count: number }>;
  statusCodeData: Array<{ statusCode: number; count: number }>;
  topEndpoints: Array<{ endpoint: string; count: number }>;
}

interface SystemErrorsModalProps {
  isOpen: boolean;
  onClose: () => void;
  timeRange: "7d" | "30d" | "90d";
}

export function SystemErrorsModal({ isOpen, onClose, timeRange }: SystemErrorsModalProps) {
  const [errors, setErrors] = useState<SystemError[]>([]);
  const [analytics, setAnalytics] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalErrors, setTotalErrors] = useState(0);

  const errorsPerPage = 10;

  useEffect(() => {
    if (isOpen) {
      fetchSystemErrors();
      fetchAnalytics();
    }
  }, [isOpen, currentPage, timeRange]);

  const fetchSystemErrors = async () => {
    setLoading(true);
    try {
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: errorsPerPage.toString(),
        timeRange,
      });

      const response = await fetch(`/api/dashboard/system-errors?${params}`);
      if (response.ok) {
        const data = await response.json();
        setErrors(data.errors || []);
        setTotalPages(data.totalPages || 1);
        setTotalErrors(data.totalErrors || 0);
      } else {
        console.error("Failed to fetch system errors");
        setErrors([]);
      }
    } catch (error) {
      console.error("Error fetching system errors:", error);
      setErrors([]);
    } finally {
      setLoading(false);
    }
  };

  const fetchAnalytics = async () => {
    try {
      const params = new URLSearchParams({
        timeRange,
        analytics: "true",
      });

      const response = await fetch(`/api/dashboard/system-errors?${params}`);
      if (response.ok) {
        const data = await response.json();
        setAnalytics(data);
      } else {
        console.error("Failed to fetch analytics");
        setAnalytics(null);
      }
    } catch (error) {
      console.error("Error fetching analytics:", error);
      setAnalytics(null);
    }
  };

  const handleRefresh = () => {
    setCurrentPage(1);
    fetchSystemErrors();
    fetchAnalytics();
  };

  const handleExportCSV = async () => {
    try {
      const params = new URLSearchParams({
        export: "csv",
        timeRange,
      });

      const response = await fetch(`/api/dashboard/system-errors?${params}`);
      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `system-client-errors-${timeRange}-${new Date().toISOString().split('T')[0]}.csv`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      }
    } catch (error) {
      console.error("Error exporting CSV:", error);
    }
  };

  const getStatusCodeBadge = (statusCode: number) => {
    if (statusCode >= 500 && statusCode < 600) {
      return <Badge variant="destructive">{statusCode}</Badge>; // Server errors - red
    } else if (statusCode >= 400 && statusCode < 500) {
      return <Badge variant="outline" className="border-orange-500 text-orange-600">{statusCode}</Badge>; // Client errors - orange
    }
    return <Badge variant="secondary">{statusCode}</Badge>; // Other errors - gray
  };

  const getTimePeriodLabel = () => {
    switch (timeRange) {
      case "7d": return "Last 7 Days";
      case "30d": return "Last 30 Days";
      case "90d": return "Last 90 Days";
      default: return "Current Period";
    }
  };



  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-7xl max-h-[95vh] overflow-y-auto">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="p-2 rounded-full bg-red-100 dark:bg-red-900/20">
                <AlertCircle className="h-5 w-5 text-red-600" />
              </div>
              <div>
                <DialogTitle>System & Client Errors ({getTimePeriodLabel()})</DialogTitle>
                <DialogDescription>
                  {analytics?.totalErrors || totalErrors} total errors found • Showing 4xx client errors and 5xx server errors
                </DialogDescription>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm" onClick={handleExportCSV}>
                <Download className="h-4 w-4 mr-2" />
                Export CSV
              </Button>
              <Button variant="outline" size="sm" onClick={handleRefresh}>
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh
              </Button>
            </div>
          </div>
        </DialogHeader>

        {/* Analytics Section */}
        {analytics && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            {/* Key Metrics Cards */}
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <AlertTriangle className="h-5 w-5 text-red-500" />
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Total Errors</p>
                    <p className="text-2xl font-bold text-red-600">{analytics.totalErrors}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <Activity className="h-5 w-5 text-orange-500" />
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Error Rate</p>
                    <p className="text-2xl font-bold text-orange-600">{analytics.errorRate}%</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <BarChart3 className="h-5 w-5 text-blue-500" />
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Status Codes</p>
                    <p className="text-2xl font-bold text-blue-600">{analytics.statusCodeData.length}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <TrendingUp className="h-5 w-5 text-green-500" />
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Endpoints</p>
                    <p className="text-2xl font-bold text-green-600">{analytics.topEndpoints.length}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Charts Section */}
        {analytics && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
            {/* Error Trend Chart */}
            <Card>
              <CardHeader>
                <CardTitle className="text-sm flex items-center">
                  <TrendingUp className="h-4 w-4 mr-2" />
                  Error Trend Over Time
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-64">
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart data={analytics.trendData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis
                        dataKey="date"
                        tick={{ fontSize: 12 }}
                        tickFormatter={(value) => new Date(value).toLocaleDateString()}
                      />
                      <YAxis tick={{ fontSize: 12 }} />
                      <Tooltip
                        labelFormatter={(value) => new Date(value).toLocaleDateString()}
                        formatter={(value) => [value, 'Errors']}
                      />
                      <Line
                        type="monotone"
                        dataKey="count"
                        stroke="#ef4444"
                        strokeWidth={2}
                        dot={{ fill: '#ef4444', strokeWidth: 2, r: 4 }}
                      />
                    </LineChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>

            {/* Status Code Distribution */}
            <Card>
              <CardHeader>
                <CardTitle className="text-sm flex items-center">
                  <BarChart3 className="h-4 w-4 mr-2" />
                  Status Code Distribution
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-64">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart data={analytics.statusCodeData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="statusCode" tick={{ fontSize: 12 }} />
                      <YAxis tick={{ fontSize: 12 }} />
                      <Tooltip formatter={(value) => [value, 'Count']} />
                      <Bar
                        dataKey="count"
                        fill="#f97316"
                        radius={[4, 4, 0, 0]}
                      />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Errors Table */}
        <Card>
          <CardContent className="p-0">
            {loading ? (
              <div className="flex items-center justify-center py-8">
                <RefreshCw className="h-6 w-6 animate-spin mr-2" />
                Loading errors...
              </div>
            ) : errors.length === 0 ? (
              <div className="text-center py-8">
                <AlertCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
                <p className="text-lg font-medium text-green-700 dark:text-green-400">
                  No system errors found!
                </p>
                <p className="text-sm text-muted-foreground mt-1">
                  All systems are running smoothly in the selected time period.
                </p>
              </div>
            ) : (
              <>
                <div className="overflow-x-auto">
                  <table className="w-full text-sm">
                    <thead className="border-b bg-muted/50">
                      <tr>
                        <th className="px-4 py-3 text-left font-medium">Timestamp</th>
                        <th className="px-4 py-3 text-left font-medium">Status</th>
                        <th className="px-4 py-3 text-left font-medium">Endpoint</th>
                        <th className="px-4 py-3 text-left font-medium">Method</th>
                        <th className="px-4 py-3 text-left font-medium">Tenant</th>
                        <th className="px-4 py-3 text-left font-medium">Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {errors.map((error) => (
                        <tr key={error.id} className="border-b hover:bg-muted/50">
                          <td className="px-4 py-3">
                            <div className="flex items-center space-x-2">
                              <Clock className="h-4 w-4 text-muted-foreground" />
                              <span className="font-mono text-xs">
                                {formatDate(error.timestamp)}
                              </span>
                            </div>
                          </td>
                          <td className="px-4 py-3">
                            {getStatusCodeBadge(error.statusCode)}
                          </td>
                          <td className="px-4 py-3">
                            <code className="text-xs bg-muted px-2 py-1 rounded">
                              {error.endpoint}
                            </code>
                          </td>
                          <td className="px-4 py-3">
                            <Badge variant="outline" className="text-xs">
                              {error.method}
                            </Badge>
                          </td>
                          <td className="px-4 py-3">
                            {error.tenantName ? (
                              <div className="flex items-center space-x-2">
                                <span className="text-sm">{error.tenantName}</span>
                                {error.tenantId && (
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="h-6 w-6 p-0"
                                    onClick={() => window.open(`/admin/tenants/${error.tenantId}`, '_blank')}
                                  >
                                    <ExternalLink className="h-3 w-3" />
                                  </Button>
                                )}
                              </div>
                            ) : (
                              <span className="text-muted-foreground text-sm">System</span>
                            )}
                          </td>
                          <td className="px-4 py-3">
                            <Button variant="ghost" size="sm">
                              View Details
                            </Button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>

                {/* Pagination */}
                {totalPages > 1 && (
                  <div className="flex items-center justify-between p-4 border-t">
                    <div className="text-sm text-muted-foreground">
                      Showing {((currentPage - 1) * errorsPerPage) + 1} to {Math.min(currentPage * errorsPerPage, totalErrors)} of {totalErrors} errors
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                        disabled={currentPage === 1}
                      >
                        <ChevronLeft className="h-4 w-4" />
                        Previous
                      </Button>

                      <div className="flex items-center space-x-1">
                        {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                          const page = i + 1;
                          return (
                            <Button
                              key={page}
                              variant={page === currentPage ? "default" : "outline"}
                              size="sm"
                              onClick={() => setCurrentPage(page)}
                              className="w-8 h-8 p-0"
                            >
                              {page}
                            </Button>
                          );
                        })}
                      </div>

                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                        disabled={currentPage === totalPages}
                      >
                        Next
                        <ChevronRight className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                )}
              </>
            )}
          </CardContent>
        </Card>
      </DialogContent>
    </Dialog>
  );
}
