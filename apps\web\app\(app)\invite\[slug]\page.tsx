"use client";

import React, { useState, useEffect } from "react";
import { use<PERSON>ara<PERSON>, useRouter } from "next/navigation";
import { useSession, signIn } from "next-auth/react";
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import {
  Building,
  CheckCircle,
  AlertCircle,
  Loader2,
  ArrowRight,
  UserPlus,
  LogIn,
} from "lucide-react";
import toast from "react-hot-toast";
import { ThemeConfigProvider } from "@/lib/theme-config-context";
import { WhiteLabelThemeProvider } from "@/components/theme/white-label-theme-provider";
import { DynamicFaviconManager } from "@/components/theme/dynamic-favicon-manager";
import { useBrandName } from "@/lib/theme-config-context";
import { ThemeConfig } from "@/types/theme-config";
import { generateThemeVariables } from "@/services/theme-config";

// Helper function to convert ThemeConfiguration to ThemeConfig
function convertToThemeConfig(
  themeConfiguration?: ThemeConfiguration
): ThemeConfig | undefined {
  if (!themeConfiguration) return undefined;

  return {
    brandName: themeConfiguration.brandName,
    logoUrl: themeConfiguration.logoUrl,
    faviconUrl: themeConfiguration.faviconUrl,
    fullAppLogoUrl: themeConfiguration.fullAppLogoUrl,
    primaryColor: themeConfiguration.primaryColor,
    secondaryColor: themeConfiguration.secondaryColor,
    accentColor: themeConfiguration.accentColor,
    navigationBackgroundColor: themeConfiguration.navigationBackgroundColor,
    contentBackgroundColor: themeConfiguration.contentBackgroundColor,
    foregroundColor: themeConfiguration.foregroundColor,
    themePreset: themeConfiguration.themePreset as "light" | "dark" | undefined,
  };
}

// Types
interface ThemeConfiguration {
  id: string;
  tenantId: string;
  brandName?: string;
  logoUrl?: string;
  faviconUrl?: string;
  fullAppLogoUrl?: string;
  primaryColor?: string;
  secondaryColor?: string;
  accentColor?: string;
  navigationBackgroundColor?: string;
  contentBackgroundColor?: string;
  foregroundColor?: string;
  themePreset?: string;
  isActive: boolean;
  version: number;
  updatedAt: string;
}

interface Company {
  id: string;
  name: string;
  slug: string;
  description: string;
  maxSignups: number;
  currentSignups: number;
  status: string;
  createdAt: string;
  tenant: {
    id: string;
    name: string;
    themeConfiguration?: ThemeConfiguration;
  };
}

// Main invite page content component
function InvitePageContent() {
  const params = useParams() ?? {};
  const router = useRouter();
  const { data: session } = useSession();
  const slug = params.slug as string;
  const brandName = useBrandName();

  const [company, setCompany] = useState<Company | null>(null);
  const [loading, setLoading] = useState(true);
  const [joining, setJoining] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [alreadyMember, setAlreadyMember] = useState(false);

  // Load company data
  useEffect(() => {
    if (slug) {
      loadCompanyData();
    }
  }, [slug, session]);

  // Apply theme configuration and update document title
  useEffect(() => {
    if (company?.tenant?.themeConfiguration) {
      const themeConfiguration = company.tenant.themeConfiguration;

      // Update document title
      if (themeConfiguration.brandName) {
        document.title = `Join Company - ${themeConfiguration.brandName}`;
      }

      // Apply theme configuration
      const isPartnerConsole =
        process.env.NEXT_PUBLIC_IS_PARTNER_CONSOLE === "1";
      if (isPartnerConsole) {
        // Convert to proper ThemeConfig format
        const themeConfig = convertToThemeConfig(themeConfiguration);
        if (themeConfig) {
          // Generate and apply CSS variables for custom colors using the proper function
          const themeVariables = generateThemeVariables(themeConfig);
          const root = document.documentElement;

          // Apply color variables to the root element
          Object.entries(themeVariables).forEach(([property, value]) => {
            root.style.setProperty(property, value);
          });

          // Apply theme mode
          if (themeConfig.themePreset) {
            root.classList.remove("light", "dark");
            root.classList.add(themeConfig.themePreset);
          }

          // Apply CSS overrides for hardcoded Tailwind classes on invite pages
          applyInvitePageOverrides();
        }
      }
    }
  }, [company]);

  // Function to apply CSS overrides for hardcoded classes on invite pages
  const applyInvitePageOverrides = () => {
    const styleId = "invite-page-theme-overrides";

    // Remove existing style element if it exists
    const existingStyle = document.getElementById(styleId);
    if (existingStyle) {
      existingStyle.remove();
    }

    const css = `
      /* Invite page background overrides */
      .min-h-screen.bg-gradient-to-br.from-blue-50.to-indigo-100,
      .min-h-screen.bg-gradient-to-br {
        background: hsl(var(--background)) !important;
      }

      /* Header background overrides */
      .bg-white.shadow-sm.border-b,
      .bg-white.border-b {
        background-color: hsl(var(--sidebar-background)) !important;
      }

      /* Card backgrounds */
      .bg-white.rounded-lg,
      .shadow-lg {
        background-color: hsl(var(--card)) !important;
      }

      /* Text color overrides */
      .text-gray-900 {
        color: hsl(var(--foreground)) !important;
      }

      .text-gray-600 {
        color: hsl(var(--muted-foreground)) !important;
      }

      .text-blue-600 {
        color: hsl(var(--primary)) !important;
      }

      /* Error page background */
      .bg-gradient-to-br.from-red-50.to-pink-100 {
        background: hsl(var(--background)) !important;
      }

      /* Loading spinner color */
      .text-blue-600.animate-spin {
        color: hsl(var(--primary)) !important;
      }

      /* Icon colors */
      .text-blue-600 {
        color: hsl(var(--primary)) !important;
      }

      /* Button overrides are handled by the button component using CSS custom properties */
    `;

    // Create and append style element
    const styleElement = document.createElement("style");
    styleElement.id = styleId;
    styleElement.textContent = css;
    document.head.appendChild(styleElement);
  };

  const loadCompanyData = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await fetch(`/api/companies/invite/${slug}`);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to load company");
      }

      setCompany(data.company);
      setAlreadyMember(data.alreadyMember || false);
    } catch (error: any) {
      setError(error.message || "Failed to load company");
    } finally {
      setLoading(false);
    }
  };

  const handleJoinCompany = async () => {
    if (!session?.user?.email || !company) return;

    setJoining(true);
    try {
      const response = await fetch(`/api/companies/invite/${slug}/join`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to join company");
      }

      // Show success message with additional context
      if (data.addedToOrganization) {
        toast.success("Successfully joined the company and organization!");
      } else {
        toast.success("Successfully joined the company!");
      }

      setAlreadyMember(true);

      // Redirect to success page
      setTimeout(() => {
        router.push(
          `/invite/success?company=${encodeURIComponent(
            company.name
          )}&newToOrg=${data.addedToOrganization}`
        );
      }, 1500);
    } catch (error: any) {
      toast.error(error.message || "Failed to join company");
    } finally {
      setJoining(false);
    }
  };

  const handleSignIn = () => {
    signIn(undefined, {
      callbackUrl: `/invite/${slug}`,
    });
  };

  const handleSignUp = () => {
    // Redirect to invite-specific signup page
    router.push(`/invite/${slug}/signup`);
  };

  // Show loading state
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
        <div className="text-center">
          <Loader2 className="h-12 w-12 text-blue-600 mx-auto mb-4 animate-spin" />
          <h3 className="text-lg font-semibold mb-2">Loading invitation...</h3>
          <p className="text-gray-600">
            Please wait while we fetch the company details.
          </p>
        </div>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-red-50 to-pink-100">
        <Card className="w-full max-w-md shadow-lg">
          <CardContent className="pt-6">
            <div className="text-center">
              <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">
                Invitation Not Found
              </h3>
              <p className="text-gray-600 mb-4">{error}</p>
              <Button onClick={() => router.push("/")} variant="outline">
                Go to Home
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!company) return null;

  // Check if company is at capacity
  const isAtCapacity = company.currentSignups >= company.maxSignups;

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center gap-3">
              {company.tenant.themeConfiguration?.logoUrl ? (
                <img
                  src={company.tenant.themeConfiguration.logoUrl}
                  alt={brandName}
                  className="h-8 w-8 object-contain"
                />
              ) : (
                <Building className="h-8 w-8 text-blue-600" />
              )}
              <span className="text-xl font-bold text-gray-900">
                {brandName}
              </span>
            </div>
            {!session && (
              <Button onClick={handleSignIn} variant="outline">
                <LogIn className="mr-2 h-4 w-4" />
                Sign In
              </Button>
            )}
          </div>
        </div>
      </div>

      <div className="py-12 overflow-scroll">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Hero Section */}
          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">
              You're Invited to Join
            </h1>
            <h2 className="text-2xl font-semibold text-blue-600 mb-2">
              {company.name}
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              {company.description ||
                "Join this company to collaborate and access shared knowledge resources."}
            </p>
          </div>

          <div className="flex justify-center">
            {/* Action Panel */}
            <div>
              <Card className="shadow-lg w-[400px] top-8">
                <CardContent className="pt-6">
                  {!session ? (
                    <div className="text-center">
                      <LogIn className="h-12 w-12 text-blue-600 mx-auto mb-4" />
                      <h3 className="text-lg font-semibold mb-2">
                        Join {company.name}
                      </h3>
                      <p className="text-gray-600 mb-6">
                        Create an account or sign in to join {company.name} and
                        start collaborating with the team.
                      </p>
                      <div className="space-y-3">
                        <Button
                          onClick={handleSignUp}
                          className="w-full"
                          size="lg"
                        >
                          <UserPlus className="mr-2 h-4 w-4" />
                          Create Account & Join
                        </Button>
                        <Button
                          onClick={handleSignIn}
                          variant="outline"
                          className="w-full"
                          size="lg"
                        >
                          <LogIn className="mr-2 h-4 w-4" />
                          Sign In
                        </Button>
                      </div>
                      <p className="text-xs text-gray-500 mt-3">
                        Free to join • No credit card required
                      </p>
                    </div>
                  ) : alreadyMember ? (
                    <div className="text-center">
                      <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
                      <h3 className="text-lg font-semibold mb-2">
                        Welcome Back!
                      </h3>
                      <p className="text-gray-600 mb-6">
                        You're already a member of {company.name}.
                      </p>
                      <Button
                        onClick={() => router.push("/dashboard")}
                        className="w-full"
                        size="lg"
                      >
                        Go to Dashboard
                        <ArrowRight className="ml-2 h-4 w-4" />
                      </Button>
                    </div>
                  ) : isAtCapacity ? (
                    <div className="text-center">
                      <AlertCircle className="h-12 w-12 text-orange-500 mx-auto mb-4" />
                      <h3 className="text-lg font-semibold mb-2">
                        Company Full
                      </h3>
                      <p className="text-gray-600 mb-6">
                        {company.name} has reached its maximum capacity of{" "}
                        {company.maxSignups} members.
                      </p>
                      <Button
                        variant="outline"
                        onClick={() => router.push("/")}
                        className="w-full"
                      >
                        Explore Other Companies
                      </Button>
                    </div>
                  ) : (
                    <div className="text-center">
                      <UserPlus className="h-12 w-12 text-blue-600 mx-auto mb-4" />
                      <h3 className="text-lg font-semibold mb-2">
                        Ready to Join?
                      </h3>
                      <p className="text-gray-600 mb-6">
                        Join {company.name} and get access to shared knowledge,
                        collaborate with the team, and more.
                      </p>
                      <Button
                        onClick={handleJoinCompany}
                        disabled={joining}
                        className="w-full"
                        size="lg"
                      >
                        {joining ? (
                          <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            Joining...
                          </>
                        ) : (
                          <>
                            <UserPlus className="mr-2 h-4 w-4" />
                            Join {company.name}
                          </>
                        )}
                      </Button>
                      <p className="text-xs text-gray-500 mt-3">
                        You'll be added as a member with immediate access
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// Main export component with theme providers
export default function PublicCompanyInvitePage() {
  const params = useParams() ?? {};
  const slug = params.slug as string;
  const [company, setCompany] = useState<Company | null>(null);
  const [loading, setLoading] = useState(true);

  // Load company data to get tenant info for theme configuration
  useEffect(() => {
    if (slug) {
      loadCompanyData();
    }
  }, [slug]);

  const loadCompanyData = async () => {
    setLoading(true);
    try {
      const response = await fetch(`/api/companies/invite/${slug}`);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to load company");
      }

      setCompany(data.company);
    } catch (error: any) {
      console.error("Error loading company data:", error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!company) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">
            Company Not Found
          </h1>
          <p className="text-gray-600">
            The invitation link is invalid or has expired.
          </p>
        </div>
      </div>
    );
  }

  const tenantId = company.tenant.id;
  const themeConfig = convertToThemeConfig(company.tenant.themeConfiguration);

  return (
    <ThemeConfigProvider tenantId={tenantId} initialConfig={themeConfig}>
      <WhiteLabelThemeProvider tenantId={tenantId} themeConfig={themeConfig}>
        <DynamicFaviconManager themeConfig={themeConfig || null} />
        <InvitePageContent />
      </WhiteLabelThemeProvider>
    </ThemeConfigProvider>
  );
}
