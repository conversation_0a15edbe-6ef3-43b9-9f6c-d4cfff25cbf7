"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardDescription } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import {
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  TooltipProps,
  Legend,
  Bar,
  BarChart,
  Line,
  LineChart,
} from "recharts";
import { Bar<PERSON>hart3, LineChart as LineChartIcon, TrendingUp } from "lucide-react";
import { useState } from "react";

interface EnhancedChartProps {
  className?: string;
  title?: string;
  data: any[];
  dataKey?: string;
  formatterType?: "number" | "currency" | "storage" | "percentage";
  description?: string;
  color?: "indigo" | "blue" | "green" | "amber" | "red";
  chartType?: "area" | "bar" | "line";
  type?: "area" | "bar" | "line";
  showLegend?: boolean;
  yAxisLabel?: string;
  xAxisLabel?: string;
  height?: number;
  config?: Record<string, { label: string; color: string }>;
}

const CustomTooltip = ({
  active,
  payload,
  label,
  valueFormatter,
}: TooltipProps<number, string> & {
  valueFormatter: (value: number) => string;
}) => {
  if (active && payload && payload.length) {
    return (
      <div className="rounded-lg border bg-background p-3 shadow-sm">
        <p className="text-xs font-medium text-muted-foreground mb-2">{label}</p>
        {payload.map((entry: any, index: number) => {
          // Custom formatting for different data types
          let formattedValue = "";
          if (entry.dataKey === "profit") {
            formattedValue = `CHF ${(entry.value as number).toLocaleString()}`;
          } else if (entry.dataKey === "margin") {
            formattedValue = `${entry.value}%`;
          } else if (entry.dataKey === "mrr" || entry.dataKey === "cost") {
            formattedValue = `CHF ${(entry.value as number).toLocaleString()}`;
          } else {
            formattedValue = valueFormatter(entry.value as number);
          }

          return (
            <div key={index} className="flex items-center justify-between gap-2 mb-1">
              <div className="flex items-center gap-2">
                <div
                  className="w-3 h-3 rounded-sm"
                  style={{ backgroundColor: entry.color }}
                />
                <span className="text-xs text-muted-foreground">
                  {entry.name || entry.dataKey}
                </span>
              </div>
              <span className="text-sm font-medium">
                {formattedValue}
              </span>
            </div>
          );
        })}
      </div>
    );
  }

  return null;
};

export function EnhancedChart({
  className,
  title,
  data,
  dataKey = "value",
  formatterType = "number",
  description,
  color = "indigo",
  chartType = "area",
  type,
  showLegend = false,
  yAxisLabel,
  xAxisLabel,
  height = 300,
  config,
}: EnhancedChartProps) {
  const [selectedChartType, setSelectedChartType] = useState<"area" | "bar" | "line">(type || chartType);

  // If no data, show empty state
  if (!data || data.length === 0) {
    return (
      <div className={cn("col-span-4 overflow-hidden", className)}>
        <div className="flex items-center justify-center h-[300px] text-muted-foreground">
          <div className="text-center">
            <div className="text-lg font-medium">No data available</div>
            <div className="text-sm">Data will appear here when available</div>
          </div>
        </div>
      </div>
    );
  }

  // Create formatter function based on type
  const getValueFormatter = (): (value: number) => string => {
    switch (formatterType) {
      case "number":
        return (value: number) => value.toLocaleString();
      case "currency":
        return (value: number) => `CHF ${value.toLocaleString()}`;
      case "storage":
        return (value: number) => `${value} GB`;
      case "percentage":
        return (value: number) => `${value}%`;
      default:
        return (value: number) => `${value}`;
    }
  };

  const formatValue = getValueFormatter();

  // Updated color map with better dark mode compatibility
  const colorMap: Record<"indigo" | "blue" | "green" | "amber" | "red", { stroke: string; fill: string }> = {
    indigo: {
      stroke: "#6366F1",
      fill: "#6366F1",
    },
    blue: {
      stroke: "#3B82F6",
      fill: "#3B82F6",
    },
    green: {
      stroke: "#10B981",
      fill: "#10B981",
    },
    amber: {
      stroke: "#F59E0B",
      fill: "#F59E0B",
    },
    red: {
      stroke: "#EF4444",
      fill: "#EF4444",
    },
  };

  const renderChart = () => {
    const commonProps = {
      data,
      margin: {
        top: 5,
        right: 5,
        left: 5,
        bottom: 5,
      },
    };

    // Get data keys from config or use default
    const dataKeys = config ? Object.keys(config) : [dataKey];

    switch (selectedChartType) {
      case "bar":
        return (
          <BarChart {...commonProps}>
            <CartesianGrid
              strokeDasharray="3 3"
              stroke="#374151"
              strokeOpacity={0.3}
              className="dark:stroke-gray-600"
            />
            <XAxis
              dataKey="name"
              axisLine={false}
              tickLine={false}
              tick={{ fontSize: 12, fill: "#6B7280" }}
              dy={10}
              label={xAxisLabel ? { value: xAxisLabel, position: 'insideBottom', offset: -5, style: { fill: "#6B7280" } } : undefined}
              className="dark:[&_text]:fill-gray-300"
            />
            <YAxis
              axisLine={false}
              tickLine={false}
              tick={{ fontSize: 12, fill: "#6B7280" }}
              dx={-10}
              width={90}
              tickFormatter={formatValue}
              label={yAxisLabel ? { value: yAxisLabel, angle: -90, position: 'insideLeft', style: { fill: "#6B7280" } } : undefined}
              className="dark:[&_text]:fill-gray-300"
            />
            <Tooltip
              content={({ active, payload, label }) => (
                <CustomTooltip
                  active={active}
                  payload={payload as any}
                  label={label}
                  valueFormatter={formatValue}
                />
              )}
            />
            {showLegend && <Legend />}
            {dataKeys.map((key, index) => (
              <Bar
                key={key}
                dataKey={key}
                fill={config?.[key]?.color || colorMap[color].fill}
                fillOpacity={0.8}
                radius={[4, 4, 0, 0]}
              />
            ))}
          </BarChart>
        );
      case "line":
        return (
          <LineChart {...commonProps}>
            <CartesianGrid
              strokeDasharray="3 3"
              stroke="#374151"
              strokeOpacity={0.3}
              className="dark:stroke-gray-600"
            />
            <XAxis
              dataKey="name"
              axisLine={false}
              tickLine={false}
              tick={{ fontSize: 12, fill: "#6B7280" }}
              dy={10}
              label={xAxisLabel ? { value: xAxisLabel, position: 'insideBottom', offset: -5, style: { fill: "#6B7280" } } : undefined}
              className="dark:[&_text]:fill-gray-300"
            />
            <YAxis
              axisLine={false}
              tickLine={false}
              width={90}
              tick={{ fontSize: 12, fill: "#6B7280" }}
              dx={-10}
              tickFormatter={formatValue}
              label={yAxisLabel ? { value: yAxisLabel, angle: -90, position: 'insideLeft', style: { fill: "#6B7280" } } : undefined}
              className="dark:[&_text]:fill-gray-300"
            />
            <Tooltip
              content={({ active, payload, label }) => (
                <CustomTooltip
                  active={active}
                  payload={payload as any}
                  label={label}
                  valueFormatter={formatValue}
                />
              )}
            />
            {showLegend && <Legend />}
            {dataKeys.map((key, index) => (
              <Line
                key={key}
                type="monotone"
                dataKey={key}
                stroke={config?.[key]?.color || colorMap[color].stroke}
                strokeWidth={2}
                dot={{ r: 4, fill: config?.[key]?.color || colorMap[color].stroke }}
                activeDot={{ r: 6 }}
              />
            ))}
          </LineChart>
        );
      case "area":
      default:
        return (
          <AreaChart {...commonProps}>
            <defs>
              {dataKeys.map((key, index) => (
                <linearGradient key={key} id={`color-${key}`} x1="0" y1="0" x2="0" y2="1">
                  <stop
                    offset="5%"
                    stopColor={config?.[key]?.color || colorMap[color].fill}
                    stopOpacity={0.2}
                  />
                  <stop
                    offset="95%"
                    stopColor={config?.[key]?.color || colorMap[color].fill}
                    stopOpacity={0}
                  />
                </linearGradient>
              ))}
            </defs>
            <CartesianGrid
              strokeDasharray="3 3"
              stroke="#374151"
              strokeOpacity={0.3}
              className="dark:stroke-gray-600"
            />
            <XAxis
              dataKey="name"
              axisLine={false}
              tickLine={false}
              tick={{ fontSize: 12, fill: "#6B7280" }}
              dy={10}
              label={xAxisLabel ? { value: xAxisLabel, position: 'insideBottom', offset: -5, style: { fill: "#6B7280" } } : undefined}
              className="dark:[&_text]:fill-gray-300"
            />
            <YAxis
              axisLine={false}
              tickLine={false}
              width={90}
              tick={{ fontSize: 12, fill: "#6B7280" }}
              dx={-10}
              tickFormatter={formatValue}
              label={yAxisLabel ? { value: yAxisLabel, angle: -90, position: 'insideLeft', style: { fill: "#6B7280" } } : undefined}
              className="dark:[&_text]:fill-gray-300"
            />
            <Tooltip
              content={({ active, payload, label }) => (
                <CustomTooltip
                  active={active}
                  payload={payload as any}
                  label={label}
                  valueFormatter={formatValue}
                />
              )}
            />
            {showLegend && <Legend />}
            {dataKeys.map((key, index) => (
              <Area
                key={key}
                type="monotone"
                dataKey={key}
                stroke={config?.[key]?.color || colorMap[color].stroke}
                strokeWidth={2}
                fill={`url(#color-${key})`}
                fillOpacity={1}
              />
            ))}
          </AreaChart>
        );
    }
  };

  return (
    <div className={cn("w-full", className)}>
      {title && (
        <div className="flex flex-row items-center justify-between pb-2 mb-4">
          <div>
            <h3 className="text-base font-medium">{title}</h3>
            {description && (
              <p className="text-xs text-muted-foreground">{description}</p>
            )}
          </div>
          <div className="flex items-center space-x-2">
            <div className="flex rounded-md border">
              <Button
                variant="ghost"
                size="icon"
                className={cn(
                  "h-8 w-8 rounded-none rounded-l-md",
                  selectedChartType === "area" && "bg-muted"
                )}
                onClick={() => setSelectedChartType("area")}
              >
                <TrendingUp className="h-4 w-4" />
                <span className="sr-only">Area Chart</span>
              </Button>
              <Button
                variant="ghost"
                size="icon"
                className={cn(
                  "h-8 w-8 rounded-none",
                  selectedChartType === "bar" && "bg-muted"
                )}
                onClick={() => setSelectedChartType("bar")}
              >
                <BarChart3 className="h-4 w-4" />
                <span className="sr-only">Bar Chart</span>
              </Button>
              <Button
                variant="ghost"
                size="icon"
                className={cn(
                  "h-8 w-8 rounded-none rounded-r-md",
                  selectedChartType === "line" && "bg-muted"
                )}
                onClick={() => setSelectedChartType("line")}
              >
                <LineChartIcon className="h-4 w-4" />
                <span className="sr-only">Line Chart</span>
              </Button>
            </div>
          </div>
        </div>
      )}
      <div className={`h-[${height}px] w-full`}>
        <ResponsiveContainer width="100%" height="100%">
          {renderChart()}
        </ResponsiveContainer>
      </div>
    </div>
  );
}
