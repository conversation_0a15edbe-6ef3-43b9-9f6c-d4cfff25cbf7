import asyncio
import logging
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any
from bson import ObjectId

logger = logging.getLogger(__name__)

class BackgroundWorker:
    """Background worker service that processes pending jobs with controlled concurrency."""

    def __init__(self):
        self.db_client = None  # Will be set by the application
        self._is_running = False
        self._worker_tasks: List[asyncio.Task] = []
        self._document_semaphore = asyncio.Semaphore(2)  # Only 2 concurrent document processing
        self._video_semaphore = asyncio.Semaphore(1)     # Only 1 concurrent video processing

    async def start_workers(self):
        """Start background worker tasks."""
        if self._is_running:
            logger.warning("Background workers are already running")
            return

        self._is_running = True
        logger.info("Starting background workers...")

        # Start document processing worker
        document_worker = asyncio.create_task(self._document_processing_worker())
        self._worker_tasks.append(document_worker)

        # Start video processing worker
        video_worker = asyncio.create_task(self._video_processing_worker())
        self._worker_tasks.append(video_worker)

        logger.info("Background workers started successfully")

    async def stop_workers(self):
        """Stop background worker tasks."""
        if not self._is_running:
            return

        self._is_running = False
        logger.info("Stopping background workers...")

        # Cancel all worker tasks
        for task in self._worker_tasks:
            task.cancel()

        # Wait for tasks to complete
        await asyncio.gather(*self._worker_tasks, return_exceptions=True)
        self._worker_tasks.clear()

        logger.info("Background workers stopped")

    async def _document_processing_worker(self):
        """Worker that processes document processing jobs."""
        logger.info("Document processing worker started")
        
        while self._is_running:
            try:
                # Get pending document processing jobs
                pending_jobs = await self.db_client.DocumentProcessingJob.find({
                    "status": "pending"
                }).sort("createdAt", 1).limit(10).to_list(10)

                if not pending_jobs:
                    # No pending jobs, wait before checking again
                    await asyncio.sleep(5)
                    continue

                # Process jobs with controlled concurrency
                tasks = []
                for job in pending_jobs:
                    task = asyncio.create_task(self._process_document_job(job))
                    tasks.append(task)

                # Wait for all tasks to complete
                if tasks:
                    await asyncio.gather(*tasks, return_exceptions=True)

            except Exception as e:
                logger.error(f"Error in document processing worker: {e}")
                await asyncio.sleep(10)  # Wait before retrying

    async def _video_processing_worker(self):
        """Worker that processes video processing jobs."""
        logger.info("Video processing worker started")
        
        while self._is_running:
            try:
                # Get pending video processing jobs
                pending_jobs = await self.db_client.VideoProcessingJob.find({
                    "status": "pending"
                }).sort("createdAt", 1).limit(5).to_list(5)

                if not pending_jobs:
                    # No pending jobs, wait before checking again
                    await asyncio.sleep(5)
                    continue

                # Process jobs with controlled concurrency
                tasks = []
                for job in pending_jobs:
                    task = asyncio.create_task(self._process_video_job(job))
                    tasks.append(task)

                # Wait for all tasks to complete
                if tasks:
                    await asyncio.gather(*tasks, return_exceptions=True)

            except Exception as e:
                logger.error(f"Error in video processing worker: {e}")
                await asyncio.sleep(10)  # Wait before retrying

    async def _process_document_job(self, job: Dict[str, Any]):
        """Process a single document job with concurrency control."""
        job_id = job["jobId"]
        
        # Use semaphore to limit concurrent processing
        async with self._document_semaphore:
            try:
                logger.info(f"Processing document job {job_id}")
                
                # Update status to processing
                await self.db_client.DocumentProcessingJob.update_one(
                    {"jobId": job_id},
                    {
                        "$set": {
                            "status": "processing",
                            "updatedAt": datetime.now(timezone.utc)
                        }
                    }
                )

                # Import and create workspace RAG manager
                from src.services.workspace_rag import WorkspaceRAGManager
                workspace_rag_manager = WorkspaceRAGManager()
                workspace_rag_manager.db_client = self.db_client
                
                # Process the document
                result = await workspace_rag_manager.index_document_with_access_check(
                    user_id=str(job["userId"]),
                    workspace_id=str(job["workspaceId"]),
                    document_path=job["documentPath"],
                    document_type=job["documentType"],
                    metadata=job.get("metadata", {}),
                    tenant_id=str(job["tenantId"]),
                    file_id=job["fileId"],
                    slug=job["workspaceSlug"],
                    page_id=job.get("pageId")
                )
                
                # Update job status based on result
                if result.get("status") == 200:
                    await self.db_client.DocumentProcessingJob.update_one(
                        {"jobId": job_id},
                        {
                            "$set": {
                                "status": "completed",
                                "completedAt": datetime.now(timezone.utc),
                                "updatedAt": datetime.now(timezone.utc),
                                "result": result
                            }
                        }
                    )
                    logger.info(f"Document job {job_id} completed successfully")
                else:
                    error_message = result.get("message", "Unknown error")
                    await self.db_client.DocumentProcessingJob.update_one(
                        {"jobId": job_id},
                        {
                            "$set": {
                                "status": "failed",
                                "errorMessage": error_message,
                                "updatedAt": datetime.now(timezone.utc)
                            }
                        }
                    )
                    logger.error(f"Document job {job_id} failed: {error_message}")

            except Exception as e:
                logger.error(f"Error processing document job {job_id}: {e}")
                await self.db_client.DocumentProcessingJob.update_one(
                    {"jobId": job_id},
                    {
                        "$set": {
                            "status": "failed",
                            "errorMessage": str(e),
                            "updatedAt": datetime.now(timezone.utc)
                        }
                    }
                )

    async def _process_video_job(self, job: Dict[str, Any]):
        """Process a single video job with concurrency control."""
        job_id = job["jobId"]
        
        # Use semaphore to limit concurrent processing
        async with self._video_semaphore:
            try:
                logger.info(f"Processing video job {job_id}")
                
                # Update status to processing
                await self.db_client.VideoProcessingJob.update_one(
                    {"jobId": job_id},
                    {
                        "$set": {
                            "status": "processing",
                            "updatedAt": datetime.now(timezone.utc)
                        }
                    }
                )

                # Import video processing service
                from src.services.video_processing_service import video_processing_service
                video_processing_service.db_client = self.db_client
                
                # Process the video (this will handle Azure Video Indexer integration)
                result = await video_processing_service._process_video_job(job_id)
                
                logger.info(f"Video job {job_id} processing completed")

            except Exception as e:
                logger.error(f"Error processing video job {job_id}: {e}")
                await self.db_client.VideoProcessingJob.update_one(
                    {"jobId": job_id},
                    {
                        "$set": {
                            "status": "failed",
                            "errorMessage": str(e),
                            "updatedAt": datetime.now(timezone.utc)
                        }
                    }
                )

# Global background worker instance
background_worker = BackgroundWorker()
