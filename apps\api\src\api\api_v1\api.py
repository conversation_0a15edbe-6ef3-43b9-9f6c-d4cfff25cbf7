from fastapi import APIRouter, Depends

from src.api.api_v1.endpoints import workspace_chat, usage_stats, web_search, global_search, notifications, url_import, copilotkit_chat, mcp_chat, webhooks, public_chatbot,bulk_index

from src.api.auth import validate_token

# Create the main API router
api_router = APIRouter()

# Add the workspace chat router with token validation
api_router.include_router(
    workspace_chat.router,
    prefix="/workspace-chat",
    tags=["workspace-chat"],
    dependencies=[Depends(validate_token)],  # Add token validation to all endpoints
    responses={404: {"description": "Not found"}, 401: {"description": "Unauthorized"}}
)

# Add the bulk index router with token validation
api_router.include_router(
    bulk_index.router,
    prefix="/bulk-index",
    tags=["bulk-index"],
    dependencies=[Depends(validate_token)],  # Add token validation to all endpoints
    responses={404: {"description": "Not found"}, 401: {"description": "Unauthorized"}}
)

# Add the CopilotKit chat router with token validation
api_router.include_router(
    copilotkit_chat.router,
    prefix="/copilotkit-chat",
    tags=["copilotkit-chat"],
    dependencies=[Depends(validate_token)],  # Add token validation to all endpoints
    responses={404: {"description": "Not found"}, 401: {"description": "Unauthorized"}}
)

# Add the MCP chat router with token validation
api_router.include_router(
    mcp_chat.router,
    prefix="/mcp-chat",
    tags=["mcp-chat"],
    dependencies=[Depends(validate_token)],  # Add token validation to all endpoints
    responses={404: {"description": "Not found"}, 401: {"description": "Unauthorized"}}
)

# Add the usage stats router with token validation
api_router.include_router(
    usage_stats.router,
    prefix="/usages",
    tags=["chat-usages"],
    dependencies=[Depends(validate_token)],  # Add token validation to all endpoints
    responses={404: {"description": "Not found"}, 401: {"description": "Unauthorized"}}
)

# Add the web search router with token validation
api_router.include_router(
    web_search.router,
    prefix="/web-search",
    tags=["web-search"],
    dependencies=[Depends(validate_token)],  # Add token validation to all endpoints
    responses={404: {"description": "Not found"}, 401: {"description": "Unauthorized"}, 429: {"description": "Too Many Requests"}}
)

# Add the global search router with token validation
api_router.include_router(
    global_search.router,
    prefix="/global-search",
    tags=["global-search"],
    dependencies=[Depends(validate_token)],  # Add token validation to all endpoints
    responses={404: {"description": "Not found"}, 401: {"description": "Unauthorized"}}
)

# Add the notifications router (no token validation needed for internal broadcasting)
api_router.include_router(
    notifications.router,
    prefix="/notifications",
    tags=["notifications"],
    responses={404: {"description": "Not found"}, 500: {"description": "Internal Server Error"}}
)

# Add the URL import router with token validation
api_router.include_router(
    url_import.router,
    prefix="/url-import",
    tags=["url-import"],
    dependencies=[Depends(validate_token)],  # Add token validation to all endpoints
    responses={404: {"description": "Not found"}, 401: {"description": "Unauthorized"}}
)

# Add the webhooks router (no token validation for external webhooks)
api_router.include_router(
    webhooks.router,
    prefix="/webhooks",
    tags=["webhooks"],
    responses={404: {"description": "Not found"}, 400: {"description": "Bad Request"}, 500: {"description": "Internal Server Error"}}
)

# Add the public chatbot router (no token validation, uses API key authentication)
api_router.include_router(
    public_chatbot.router,
    prefix="/public",
    tags=["public-chatbot"],
    responses={404: {"description": "Not found"}, 401: {"description": "Unauthorized"}, 403: {"description": "Forbidden"}, 429: {"description": "Too Many Requests"}}
)