"use client";

import * as React from "react";
import { CalendarIcon } from "lucide-react";
import { addDays, format } from "date-fns";
import { DateRange } from "react-day-picker";
import { useRouter, useSearchParams } from "next/navigation";

import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface DateRangeSelectorSSRProps {
  className?: string;
}

export function DateRangeSelectorSSR({ className }: DateRangeSelectorSSRProps) {
  const router = useRouter();
  const searchParams = useSearchParams();

  const fromParam = searchParams?.get("from");
  const toParam = searchParams?.get("to");

  const currentRange = fromParam && toParam
    ? { from: new Date(fromParam), to: new Date(toParam) }
    : undefined;

  const [date, setDate] = React.useState<DateRange | undefined>(currentRange);
  const [preset, setPreset] = React.useState<string>();

  const presets: Record<
    string,
    { label: string; range: DateRange | null }
  > = {
    lifetime: {
      label: "Lifetime",
      range: {
        from: undefined,
        to: undefined,
      },
    },
    last7Days: {
      label: "Last 7 Days",
      range: {
        from: addDays(new Date(), -7),
        to: new Date(),
      },
    },
    last30Days: {
      label: "Last 30 Days",
      range: {
        from: addDays(new Date(), -30),
        to: new Date(),
      },
    },
    last90Days: {
      label: "Last 90 Days",
      range: {
        from: addDays(new Date(), -90),
        to: new Date(),
      },
    },
    thisMonth: {
      label: "This Month",
      range: {
        from: new Date(new Date().getFullYear(), new Date().getMonth(), 1),
        to: new Date(),
      },
    },
    lastMonth: {
      label: "Last Month",
      range: {
        from: new Date(new Date().getFullYear(), new Date().getMonth() - 1, 1),
        to: new Date(new Date().getFullYear(), new Date().getMonth(), 0),
      },
    },
    thisYear: {
      label: "This Year",
      range: {
        from: new Date(new Date().getFullYear(), 0, 1),
        to: new Date(),
      },
    },
  };

  React.useEffect(() => {
if (!fromParam || !toParam) {
    setPreset("lifetime");
    setDate(undefined);
    return;
  }
  const currentFrom = new Date(fromParam);
  const currentTo = new Date(toParam);

  for (const [key, presetData] of Object.entries(presets)) {
    const presetFrom = presetData.range?.from;
    const presetTo = presetData.range?.to;

    if (
      presetFrom &&
      presetTo &&
      currentFrom.toDateString() === presetFrom.toDateString() &&
      currentTo.toDateString() === presetTo.toDateString()
    ) {
      setPreset(key);
      return;
    }
  }

  setPreset("custom");
}, [fromParam, toParam]);


  const updateURL = (newRange?: { from: Date; to: Date }) => {
    const params = new URLSearchParams(searchParams?.toString() || "");
    if (newRange?.from && newRange?.to) {
      params.set("from", newRange.from.toISOString());
      params.set("to", newRange.to.toISOString());
    } else {
      params.delete("from");
      params.delete("to");
    }
    router.push(`?${params.toString()}`);
  };

  const handlePresetChange = (value: string) => {
    setPreset(value);

    if (value === "lifetime") {
      setDate(undefined);
      updateURL(undefined);
      return;
    }

    if (value !== "custom") {
      const newRange = presets[value].range;
      if (newRange?.from && newRange?.to) {
        setDate(newRange);
        updateURL({ from: newRange.from, to: newRange.to });
      }
    }
  };

  const handleDateRangeChange = (range: DateRange | undefined) => {
    if (range?.from && range?.to) {
      setDate(range);
      setPreset("custom");
      updateURL({ from: range.from, to: range.to });
    }
  };

  return (
    <div className={cn("flex items-center space-x-2", className)}>
      <Select value={preset} onValueChange={handlePresetChange}>
        <SelectTrigger className="w-[180px]">
          <SelectValue placeholder="Select a preset" />
        </SelectTrigger>
        <SelectContent>
          {Object.entries(presets).map(([key, { label }]) => (
            <SelectItem key={key} value={key}>
              {label}
            </SelectItem>
          ))}
          {preset === "custom" && (
            <SelectItem value="custom">Custom Range</SelectItem>
          )}
        </SelectContent>
      </Select>

      {preset !== "lifetime" && (
        <Popover>
          <PopoverTrigger asChild>
            <Button
              id="date"
              variant={"outline"}
              className={cn(
                "w-[300px] justify-start text-left font-normal",
                !date && "text-muted-foreground"
              )}
            >
              <CalendarIcon className="mr-2 h-4 w-4" />
              {date?.from ? (
                date.to ? (
                  <>
                    {format(date.from, "LLL dd, y")} -{" "}
                    {format(date.to, "LLL dd, y")}
                  </>
                ) : (
                  format(date.from, "LLL dd, y")
                )
              ) : (
                <span>Pick a date</span>
              )}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-auto p-0" align="start">
            <Calendar
              initialFocus
              mode="range"
              defaultMonth={date?.from}
              selected={date}
              onSelect={handleDateRangeChange}
              numberOfMonths={2}
            />
          </PopoverContent>
        </Popover>
      )}
    </div>
  );
}
