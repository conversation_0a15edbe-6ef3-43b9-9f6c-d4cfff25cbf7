import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/next-auth";
import db from "@/lib/shared-db";

export async function GET(request: NextRequest) {
  const session = await getServerSession(authOptions);

  if (!session) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");
    const timeRange = searchParams.get("timeRange") || "7d";
    const exportFormat = searchParams.get("export");
    const analytics = searchParams.get("analytics"); // ✅ Added analytics endpoint

    // Calculate date range
    const now = new Date();
    const days = timeRange === "7d" ? 7 : timeRange === "30d" ? 30 : 90;
    const startDate = new Date(now.getTime() - (days * 24 * 60 * 60 * 1000));

    // ✅ Build where clause - Include both 4xx and 5xx errors to match alert logic
    const whereClause: any = {
      timestamp: { gte: startDate, lte: now },
      OR: [
        {
          statusCode: {
            gte: 500 // Server errors (5xx)
          }
        },
        {
          statusCode: {
            gte: 400,
            lt: 500 // Client errors (4xx) - matches dashboard alert logic
          }
        }
      ]
    };

    // ✅ No filters applied - show all errors for the time range

    // Handle analytics request for charts
    if (analytics === "true") {
      const allErrors = await db.aPIRequest.findMany({
        where: whereClause,
        select: {
          statusCode: true,
          endpoint: true,
          timestamp: true,
          tenantId: true,
          tenant: {
            select: {
              name: true
            }
          }
        },
        orderBy: {
          timestamp: 'desc'
        }
      });

      // Calculate analytics data
      const totalRequests = await db.aPIRequest.count({
        where: {
          timestamp: { gte: startDate, lte: now }
        }
      });

      const errorRate = totalRequests > 0 ? (allErrors.length / totalRequests) * 100 : 0;

      // Group errors by day for trend chart
      const errorsByDay = new Map<string, number>();
      const statusCodeCounts = new Map<number, number>();
      const endpointCounts = new Map<string, number>();

      allErrors.forEach(error => {
        // Daily trend
        const day = error.timestamp.toISOString().split('T')[0];
        errorsByDay.set(day, (errorsByDay.get(day) || 0) + 1);

        // Status code distribution
        const statusCode = error.statusCode || 500;
        statusCodeCounts.set(statusCode, (statusCodeCounts.get(statusCode) || 0) + 1);

        // Endpoint distribution
        const endpoint = error.endpoint || 'Unknown';
        endpointCounts.set(endpoint, (endpointCounts.get(endpoint) || 0) + 1);
      });

      // Convert to arrays for charts
      const trendData = Array.from(errorsByDay.entries())
        .map(([date, count]) => ({ date, count }))
        .sort((a, b) => a.date.localeCompare(b.date));

      const statusCodeData = Array.from(statusCodeCounts.entries())
        .map(([statusCode, count]) => ({ statusCode, count }))
        .sort((a, b) => a.statusCode - b.statusCode);

      const topEndpoints = Array.from(endpointCounts.entries())
        .map(([endpoint, count]) => ({
          endpoint: endpoint || 'Unknown', // ✅ Ensure endpoint is never null/undefined
          count: Number(count) || 0 // ✅ Ensure count is a valid number
        }))
        .filter(item => item.count > 0) // ✅ Filter out zero counts
        .sort((a, b) => b.count - a.count)
        .slice(0, 10); // Top 10 endpoints



      return NextResponse.json({
        totalErrors: allErrors.length,
        errorRate: Math.round(errorRate * 100) / 100,
        trendData,
        statusCodeData,
        topEndpoints
      });
    }

    // Handle CSV export
    if (exportFormat === "csv") {
      const allErrors = await db.aPIRequest.findMany({
        where: whereClause,
        include: {
          tenant: {
            select: {
              name: true
            }
          }
        },
        orderBy: {
          timestamp: 'desc'
        },
        take: 1000 // Limit for performance
      });

      // Generate CSV content
      const csvHeaders = [
        "Timestamp",
        "Status Code",
        "Method",
        "Endpoint",
        "Tenant Name",
        "Tenant ID",
        "Success",
        "Response Time"
      ];

      const csvRows = allErrors.map(error => [
        error.timestamp.toISOString(),
        error.statusCode?.toString() || "",
        error.method || "",
        error.endpoint || "",
        error.tenant?.name || "",
        error.tenantId || "",
        error.success ? "true" : "false",
        error.responseTime?.toString() || ""
      ]);

      const csvContent = [
        csvHeaders.join(","),
        ...csvRows.map(row => row.map(field => `"${field}"`).join(","))
      ].join("\n");

      return new NextResponse(csvContent, {
        headers: {
          "Content-Type": "text/csv",
          "Content-Disposition": `attachment; filename="system-client-errors-${timeRange}-${new Date().toISOString().split('T')[0]}.csv"`
        }
      });
    }

    // Get total count for pagination
    const totalErrors = await db.aPIRequest.count({
      where: whereClause
    });

    // Get paginated errors
    const errors = await db.aPIRequest.findMany({
      where: whereClause,
      include: {
        tenant: {
          select: {
            name: true
          }
        }
      },
      orderBy: {
        timestamp: 'desc'
      },
      skip: (page - 1) * limit,
      take: limit
    });

    // Transform data for frontend
    const transformedErrors = errors.map(error => ({
      id: error.id,
      timestamp: error.timestamp,
      statusCode: error.statusCode || 500,
      endpoint: error.endpoint || "Unknown",
      method: error.method || "GET",
      tenantId: error.tenantId,
      tenantName: error.tenant?.name,
      errorMessage: error.errorMessage,
      userAgent: error.userAgent,
      ipAddress: error.ipAddress,
      responseTime: error.responseTime,
      success: error.success
    }));

    const totalPages = Math.ceil(totalErrors / limit);

    return NextResponse.json({
      errors: transformedErrors,
      totalErrors,
      totalPages,
      currentPage: page,
      hasNextPage: page < totalPages,
      hasPreviousPage: page > 1
    });

  } catch (error) {
    console.error("Error fetching system errors:", error);
    return NextResponse.json(
      { error: "Failed to fetch system errors" },
      { status: 500 }
    );
  }
}
