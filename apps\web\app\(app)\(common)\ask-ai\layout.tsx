import { getTenantSubscription } from "@/services";
import { redirect } from "next/navigation";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/next-auth";
import { cookies } from "next/headers";

export default async function HomePage({ children }) {
  const session: any = await getServerSession(authOptions);
  const tenantId =
    cookies().get("currentOrganizationId")?.value ??
    session?.memberships[0]?.tenant?.id;
  const userId = session?.userId;
  if (!userId || !tenantId) {
    redirect("/sign-in");
  }
  const [subscription] = await Promise.all([
    getTenantSubscription(tenantId, session.userId as string),
  ]);
  if (!subscription?.isActive) {
    redirect("/dashboard");
  }
  return <>{children}</>;
}
