import React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Co<PERSON>, Check } from "lucide-react";
import { toast } from "react-hot-toast";

interface DownloadableContentProps {
  content: string;
}

/**
 * Component to handle downloadable content like vCard files with data URLs
 */
export const DownloadableContent: React.FC<DownloadableContentProps> = ({
  content,
}) => {
  const [copied, setCopied] = React.useState(false);

  // Extract download information from various formats
  const downloadInfo = React.useMemo(() => {
    // Try standard markdown link format first: [Download filename](data:...)
    const markdownLinkMatch = content.match(
      /\[Download ([^[\]]+)\]\((data:[^)]+)\)/
    );
    if (markdownLinkMatch) {
      const [fullMatch, filename, dataUrl] = markdownLinkMatch;
      let displayContent = content.replace(fullMatch, "").trim();

      // Clean up introductory text for standard markdown format too
      displayContent = displayContent
        .replace(
          /^Here is the [^.]*\.vcf[^.]*\. You can download[^.]*\.\s*/gi,
          ""
        )
        .replace(
          /^Here is the [^.]*file[^.]*\. You can download[^.]*\.\s*/gi,
          ""
        )
        .replace(/^###\s*\(?\s*/, "") // Remove leading ### and optional (
        .replace(/^\(\s*/, "") // Remove leading (
        .replace(/^#+\s*/, "") // Remove any leading # characters
        .trim();

      return {
        filename,
        dataUrl,
        hasDownload: true,
        displayContent,
      };
    }

    // Try flexible pattern: [Download filename] followed by data URL anywhere
    const filenameMatch = content.match(/\[Download ([^[\]]+)\]/);
    const dataUrlMatch = content.match(
      /(data:[^;\s]+;[^,\s]+,[A-Za-z0-9+/=]+)/
    );

    if (filenameMatch && dataUrlMatch) {
      const filename = filenameMatch[1];
      const dataUrl = dataUrlMatch[1];
      // Remove both the download text and data URL from display content
      let displayContent = content
        .replace(filenameMatch[0], "")
        .replace(dataUrlMatch[0], "")
        .trim();

      // Clean up only the introductory text, keep the actual content
      displayContent = displayContent
        .replace(
          /^Here is the [^.]*\.vcf[^.]*\. You can download[^.]*\.\s*/gi,
          ""
        )
        .replace(
          /^Here is the [^.]*file[^.]*\. You can download[^.]*\.\s*/gi,
          ""
        )
        .replace(/^###\s*\(?\s*/, "") // Remove leading ### and optional (
        .replace("(", "") // Remove leading (
        .replaceAll("#", "") // Remove any leading # characters
        .trim();

      // Don't hide content - show everything that remains after cleaning
      // This ensures vCard data and other meaningful content is always displayed

      return {
        filename,
        dataUrl,
        hasDownload: true,
        displayContent,
      };
    }

    return {
      filename: null,
      dataUrl: null,
      hasDownload: false,
      displayContent: content,
    };
  }, [content]);

  const handleDownload = () => {
    if (!downloadInfo.dataUrl || !downloadInfo.filename) return;

    try {
      const link = document.createElement("a");
      link.href = downloadInfo.dataUrl;
      link.download = downloadInfo.filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      toast.success(`Downloaded ${downloadInfo.filename}`);
    } catch (err) {
      toast.error("Failed to download file");
    }
  };

  const copyToClipboard = async () => {
    try {
      // For data URLs, copy the actual decoded content if possible
      if (downloadInfo.dataUrl && downloadInfo.dataUrl.includes("base64,")) {
        try {
          const base64Data = downloadInfo.dataUrl.split(",")[1];
          const decodedContent = atob(base64Data);
          await navigator.clipboard.writeText(decodedContent);
          toast.success("Decoded content copied to clipboard");
          setCopied(true);
          setTimeout(() => setCopied(false), 2000);
          return;
        } catch {
          // Fallback to copying the display content
        }
      }

      // Copy the raw content or display content
      const textToCopy = downloadInfo.displayContent || content;
      await navigator.clipboard.writeText(textToCopy);
      setCopied(true);
      toast.success("Content copied to clipboard");
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      toast.error("Failed to copy content");
    }
  };

  if (!downloadInfo.hasDownload) {
    // If no download link detected, render as regular content
    return (
      <div className="relative group">
        <pre className="bg-muted p-4 rounded-lg overflow-x-auto border">
          <code>{content}</code>
        </pre>
        <Button
          variant="ghost"
          size="icon"
          className="absolute top-2 right-2 h-8 w-8 opacity-0 group-hover:opacity-100 transition-opacity duration-200 bg-background/80 hover:bg-background"
          onClick={copyToClipboard}
          title="Copy content"
        >
          {copied ? (
            <Check className="h-4 w-4 text-green-600" />
          ) : (
            <Copy className="h-4 w-4" />
          )}
        </Button>
      </div>
    );
  }

  // Render as simple hyperlink text instead of prominent card
  return (
    <div className="space-y-3">
      {/* Additional content if any */}
      {downloadInfo.displayContent && (
        <div className="relative group">
          <div className=" rounded-lg overflow-x-auto text-sm">
            {downloadInfo.displayContent}
          </div>
        </div>
      )}
      {/* Simple download link */}
      <p className="text-sm">
        <a
          href={downloadInfo.dataUrl || "#"}
          download={downloadInfo.filename || "file"}
          onClick={(e) => {
            e.preventDefault();
            handleDownload();
          }}
          className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 underline cursor-pointer"
        >
          Download {downloadInfo.filename}
        </a>
      </p>
    </div>
  );
};
