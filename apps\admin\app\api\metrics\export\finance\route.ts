import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/next-auth";
import db from "@/lib/shared-db";
import { objectsToCSV } from "@/lib/export-utils";

export async function GET(request: Request) {
  const session = await getServerSession(authOptions);

  if (!session) {
    return NextResponse.json(
      { error: "Unauthorized" },
      { status: 401 }
    );
  }

  try {
    const { searchParams } = new URL(request.url);
    const fromDate = searchParams.get("from") ? new Date(searchParams.get("from") as string) : new Date(new Date().setDate(new Date().getDate() - 30));
    const toDate = searchParams.get("to") ? new Date(searchParams.get("to") as string) : new Date();
    
    // Get all active subscriptions with their details
    const subscriptions = await db.subscription.findMany({
      where: {
        createdAt: {
          lte: toDate
        }
      },
      include: {
        plan: true,
        tenant: true
      },
      orderBy: {
        createdAt: 'desc'
      }
    });
    
    // Function to calculate MRR for a subscription
    const calculateMRR = (subscription: any) => {
      // Get base plan price
      const basePrice = subscription.billingInterval === 'month' 
        ? subscription.plan.price || 0
        : (subscription.plan.price || 0) / 12;
      
      // Add additional users cost
      const additionalUsersCost = subscription.additionalUsers * 
        (subscription.billingInterval === 'month' 
          ? subscription.plan.additionalUserFee || 0
          : (subscription.plan.additionalUserFee || 0) / 12);
      
      // Add additional storage cost (simplified calculation)
      const additionalStorageCost = subscription.additionalStorageGB * 10; // Assuming $10 per GB
      
      // Calculate total monthly revenue for this subscription
      return basePrice + additionalUsersCost + additionalStorageCost;
    };
    
    // Format the data for CSV export
    const exportData = subscriptions.map(subscription => {
      // Calculate MRR for this subscription
      const mrr = calculateMRR(subscription);
      
      // Calculate ARR (MRR * 12)
      const arr = mrr * 12;
      
      return {
        id: subscription.id,
        tenantId: subscription.tenantId,
        tenantName: subscription.tenant.name,
        planId: subscription.planId,
        planName: subscription.plan.name,
        planType: subscription.plan.type,
        billingInterval: subscription.billingInterval,
        isActive: subscription.isActive ? 'Yes' : 'No',
        createdAt: subscription.createdAt.toISOString(),
        updatedAt: subscription.updatedAt.toISOString(),
        canceledAt: subscription.canceledAt ? subscription.canceledAt.toISOString() : null,
        additionalUsers: subscription.additionalUsers,
        additionalStorageGB: subscription.additionalStorageGB,
        basePrice: subscription.plan.price,
        additionalUserFee: subscription.plan.additionalUserFee,
        mrr: mrr,
        arr: arr
      };
    });
    
    // Define CSV headers
    const headers = [
      'id',
      'tenantId',
      'tenantName',
      'planId',
      'planName',
      'planType',
      'billingInterval',
      'isActive',
      'createdAt',
      'updatedAt',
      'canceledAt',
      'additionalUsers',
      'additionalStorageGB',
      'basePrice',
      'additionalUserFee',
      'mrr',
      'arr'
    ];
    
    // Generate CSV content
    const csvContent = objectsToCSV(exportData, headers);
    
    // Return CSV content
    return new NextResponse(csvContent, {
      headers: {
        'Content-Type': 'text/csv',
        'Content-Disposition': `attachment; filename="finance_metrics.csv"`
      }
    });
  } catch (error) {
    console.error("Error exporting finance metrics:", error);
    return NextResponse.json(
      { error: "Failed to export finance metrics" },
      { status: 500 }
    );
  }
}
