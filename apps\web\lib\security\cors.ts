/**
 * CORS Configuration for Chatbot SDK
 *
 * Handles Cross-Origin Resource Sharing for public chatbot endpoints
 */

import { NextRequest, NextResponse } from "next/server";

export interface CORSConfig {
  allowedOrigins?: string[];
  allowedMethods?: string[];
  allowedHeaders?: string[];
  exposedHeaders?: string[];
  credentials?: boolean;
  maxAge?: number;
  preflightContinue?: boolean;
}

const DEFAULT_CORS_CONFIG: CORSConfig = {
  allowedMethods: ["GET", "POST", "OPTIONS"],
  allowedHeaders: [
    "Content-Type",
    "Authorization",
    "X-Requested-With",
    "Accept",
    "Origin",
    "User-Agent",
    "DNT",
    "Cache-Control",
    "X-Mx-ReqToken",
    "Keep-Alive",
    "X-Requested-With",
    "If-Modified-Since",
    "X-User-Token",
  ],
  exposedHeaders: [
    "X-RateLimit-Remaining",
    "X-RateLimit-Reset",
    "Retry-After",
    "X-Response-Time",
  ],
  credentials: false,
  maxAge: 86400, // 24 hours
  preflightContinue: false,
};

/**
 * Apply CORS headers to a response
 */
export function applyCORSHeaders(
  response: NextResponse,
  request: NextRequest,
  config: CORSConfig = {}
): NextResponse {
  const corsConfig = { ...DEFAULT_CORS_CONFIG, ...config };
  const origin = request.headers.get("origin");

  // Determine allowed origin
  let allowedOrigin = "*";
  if (corsConfig.allowedOrigins && corsConfig.allowedOrigins.length > 0) {
    if (origin && isOriginAllowed(origin, corsConfig.allowedOrigins)) {
      allowedOrigin = origin;
    } else {
      allowedOrigin = "null"; // Explicitly deny
    }
  } else if (origin) {
    allowedOrigin = origin;
  }

  // Set CORS headers
  response.headers.set("Access-Control-Allow-Origin", allowedOrigin);

  if (corsConfig.allowedMethods) {
    response.headers.set(
      "Access-Control-Allow-Methods",
      corsConfig.allowedMethods.join(", ")
    );
  }

  if (corsConfig.allowedHeaders) {
    response.headers.set(
      "Access-Control-Allow-Headers",
      corsConfig.allowedHeaders.join(", ")
    );
  }

  if (corsConfig.exposedHeaders) {
    response.headers.set(
      "Access-Control-Expose-Headers",
      corsConfig.exposedHeaders.join(", ")
    );
  }

  if (corsConfig.credentials) {
    response.headers.set("Access-Control-Allow-Credentials", "true");
  }

  if (corsConfig.maxAge) {
    response.headers.set(
      "Access-Control-Max-Age",
      corsConfig.maxAge.toString()
    );
  }

  // Security headers
  response.headers.set("X-Content-Type-Options", "nosniff");
  response.headers.set("X-Frame-Options", "DENY");
  response.headers.set("X-XSS-Protection", "1; mode=block");
  response.headers.set("Referrer-Policy", "strict-origin-when-cross-origin");

  // Remove sensitive headers
  response.headers.delete("Server");
  response.headers.delete("X-Powered-By");

  return response;
}

/**
 * Handle CORS preflight requests
 */
export function handleCORSPreflight(
  request: NextRequest,
  config: CORSConfig = {}
): NextResponse {
  const corsConfig = { ...DEFAULT_CORS_CONFIG, ...config };
  const origin = request.headers.get("origin");
  const requestMethod = request.headers.get("access-control-request-method");
  const requestHeaders = request.headers.get("access-control-request-headers");

  // Validate preflight request
  if (!requestMethod) {
    return new NextResponse("Invalid preflight request", { status: 400 });
  }

  // Check if method is allowed
  if (
    corsConfig.allowedMethods &&
    !corsConfig.allowedMethods.includes(requestMethod)
  ) {
    return new NextResponse("Method not allowed", { status: 405 });
  }

  // Check if headers are allowed
  if (requestHeaders && corsConfig.allowedHeaders) {
    const requestedHeaders = requestHeaders
      .split(",")
      .map((h) => h.trim().toLowerCase());
    const allowedHeaders = corsConfig.allowedHeaders.map((h) =>
      h.toLowerCase()
    );

    const hasDisallowedHeaders = requestedHeaders.some(
      (header) => !allowedHeaders.includes(header)
    );

    if (hasDisallowedHeaders) {
      return new NextResponse("Headers not allowed", { status: 400 });
    }
  }

  // Create preflight response
  const response = new NextResponse(null, { status: 204 });

  return applyCORSHeaders(response, request, corsConfig);
}

/**
 * Check if origin is allowed
 */
function isOriginAllowed(origin: string, allowedOrigins: string[]): boolean {
  try {
    const originUrl = new URL(origin);
    const originHost = originUrl.hostname;

    return allowedOrigins.some((allowed) => {
      // Exact match
      if (allowed === origin) return true;

      // Wildcard subdomain match
      if (allowed.startsWith("*.")) {
        const baseDomain = allowed.slice(2);
        return originHost.endsWith(baseDomain);
      }

      // Protocol-less domain match
      if (!allowed.includes("://")) {
        return originHost === allowed;
      }

      // Full URL match
      try {
        const allowedUrl = new URL(allowed);
        return (
          originUrl.hostname === allowedUrl.hostname &&
          originUrl.port === allowedUrl.port &&
          originUrl.protocol === allowedUrl.protocol
        );
      } catch {
        return false;
      }
    });
  } catch {
    return false;
  }
}

/**
 * Create CORS configuration for a specific chatbot
 */
export function createChatbotCORSConfig(chatbot: any): CORSConfig {
  const config: CORSConfig = {
    ...DEFAULT_CORS_CONFIG,
  };

  // Set allowed origins based on chatbot configuration
  if (chatbot.allowedDomains && chatbot.allowedDomains.length > 0) {
    config.allowedOrigins = chatbot.allowedDomains
      .map((domain: string) => {
        // Convert domain patterns to full origins
        if (domain.startsWith("*.")) {
          return domain; // Keep wildcard as-is
        }

        if (!domain.includes("://")) {
          // Add both http and https for domains without protocol
          return [`https://${domain}`, `http://${domain}`];
        }

        return domain;
      })
      .flat();
  }

  return config;
}

/**
 * Middleware for applying security headers
 */
export function applySecurityHeaders(response: NextResponse): NextResponse {
  // Content Security Policy
  const csp = [
    "default-src 'self'",
    "script-src 'self' 'unsafe-inline' 'unsafe-eval'",
    "style-src 'self' 'unsafe-inline'",
    "img-src 'self' data: https:",
    "font-src 'self' data:",
    "connect-src 'self' https:",
    "frame-ancestors 'none'",
    "base-uri 'self'",
    "form-action 'self'",
  ].join("; ");

  response.headers.set("Content-Security-Policy", csp);

  // Additional security headers
  response.headers.set(
    "Strict-Transport-Security",
    "max-age=31536000; includeSubDomains"
  );
  response.headers.set("X-Content-Type-Options", "nosniff");
  response.headers.set("X-Frame-Options", "DENY");
  response.headers.set("X-XSS-Protection", "1; mode=block");
  response.headers.set("Referrer-Policy", "strict-origin-when-cross-origin");
  response.headers.set(
    "Permissions-Policy",
    "camera=(), microphone=(), geolocation=()"
  );

  return response;
}

/**
 * Validate request origin against chatbot configuration
 */
export function validateRequestOrigin(
  request: NextRequest,
  chatbot: any
): { valid: boolean; error?: string } {
  const origin = request.headers.get("origin");

  // Allow requests without origin (e.g., same-origin, server-to-server)
  if (!origin) {
    return { valid: true };
  }

  // If no domain restrictions, allow all
  if (!chatbot.allowedDomains || chatbot.allowedDomains.length === 0) {
    return { valid: true };
  }

  // Check against allowed domains
  const isAllowed = isOriginAllowed(origin, chatbot.allowedDomains);

  if (!isAllowed) {
    return {
      valid: false,
      error: `Origin '${origin}' is not allowed to access this chatbot`,
    };
  }

  return { valid: true };
}
