import { apiUrl, fetchJson } from "..";

export const createEmbedded = async (data) => {
  try {
    const response = await fetch(`${apiUrl}/embeddeds`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error("Network response was not ok");
    }
    return await response.json();
  } catch (error) {
    console.log({ "Create Embedded api calling error": error });
    return { error: "Error Creating The Embedded" };
  }
};

export const updateEmbedded = async (data) => {
  try {
    const response = await fetch(`${apiUrl}/embeddeds`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error("Network response was not ok");
    }
    return await response.json();
  } catch (error) {
    console.log({ "update embeddeds api calling error": error });
    return { error: "Error Update The embeddeds" };
  }
};

export const getEmbedded = async ({
  tenantId,
  workspaceSlug = "",
  embeddedId = "",
}) => {
  try {
    if (embeddedId) {
      const response = await fetchJson(
        `${apiUrl}/embeddeds?id=${embeddedId}&tenantId=${tenantId}`
      );
      return response;
    }
    if (workspaceSlug) {
      const response = await fetchJson(
        `${apiUrl}/embeddeds?slug=${workspaceSlug}&tenantId=${tenantId}`
      );
      return response;
    }
    const response = await fetchJson(
      `${apiUrl}/embeddeds?tenantId=${tenantId}`
    );

    return response;
  } catch (error) {
    console.error("Error in getEmbedded service:", error);
    throw error;
  }
};

export const deleteEmbedded = async (id) => {
  try {
    const url = `${apiUrl}/embeddeds?id=${id}`;
    const response = await fetch(url, {
      method: "DELETE",
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Network response was not ok");
    }
    return await response.json();
  } catch (error) {
    console.log({ "delete Embedded api error": error });
    return { error: error.message || "Error deleting Embedded" };
  }
};
