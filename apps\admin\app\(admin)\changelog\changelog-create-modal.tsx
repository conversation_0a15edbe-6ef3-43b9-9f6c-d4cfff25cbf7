"use client";

import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Footer,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { CalendarIcon, Loader2 } from "lucide-react";
import { format } from "date-fns";
import { cn } from "@/lib/utils";
import { useToast } from "@/components/ui/use-toast";

interface ChangelogCreateModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess: () => void;
}

interface FormData {
  title: string;
  content: string;
  version: string;
  type: "RELEASE" | "HOTFIX" | "MAINTENANCE" | "ANNOUNCEMENT";
  priority: "LOW" | "MEDIUM" | "HIGH" | "CRITICAL";
  targetEnvironment: string; // "all", "dev", "qa", "prod"
  targetTenants: string;
  publishedAt: Date;
  expiresAt: Date | null;
  githubCommitSha: string;
  deploymentId: string;
}

interface FormErrors {
  title?: string;
  content?: string;
  type?: string;
  priority?: string;
}

export function ChangelogCreateModal({
  open,
  onOpenChange,
  onSuccess,
}: ChangelogCreateModalProps) {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<FormErrors>({});
  const [publishedDateOpen, setPublishedDateOpen] = useState(false);
  const [expiresDateOpen, setExpiresDateOpen] = useState(false);

  const [formData, setFormData] = useState<FormData>({
    title: "",
    content: "",
    version: "",
    type: "RELEASE",
    priority: "MEDIUM",
    targetEnvironment: "all",
    targetTenants: "",
    publishedAt: new Date(),
    expiresAt: null,
    githubCommitSha: "",
    deploymentId: "",
  });

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    if (!formData.title.trim()) {
      newErrors.title = "Title is required";
    }

    if (!formData.content.trim()) {
      newErrors.content = "Content is required";
    }

    if (!formData.type) {
      newErrors.type = "Type is required";
    }

    if (!formData.priority) {
      newErrors.priority = "Priority is required";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsLoading(true);

    try {
      // Prepare the data for submission
      const submitData = {
        title: formData.title.trim(),
        content: formData.content.trim(),
        version: formData.version.trim() || undefined,
        type: formData.type,
        priority: formData.priority,
        targetEnvironment: formData.targetEnvironment === "all" ? undefined : formData.targetEnvironment,
        targetTenants: formData.targetTenants
          ? formData.targetTenants.split(",").map((id) => id.trim()).filter(Boolean)
          : [],
        publishedAt: formData.publishedAt.toISOString(),
        expiresAt: formData.expiresAt?.toISOString() || undefined,
        githubCommitSha: formData.githubCommitSha.trim() || undefined,
        deploymentId: formData.deploymentId.trim() || undefined,
      };

      const response = await fetch("/api/changelog", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(submitData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to create changelog");
      }

      // Reset form
      setFormData({
        title: "",
        content: "",
        version: "",
        type: "RELEASE",
        priority: "MEDIUM",
        targetEnvironment: "all",
        targetTenants: "",
        publishedAt: new Date(),
        expiresAt: null,
        githubCommitSha: "",
        deploymentId: "",
      });

      setErrors({});
      onOpenChange(false);

      // Show success toast
      toast({
        title: "Success",
        description: "Changelog entry created successfully",
      });

      onSuccess();
    } catch (error) {
      console.error("Error creating changelog:", error);

      // Show error toast
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to create changelog entry",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    setFormData({
      title: "",
      content: "",
      version: "",
      type: "RELEASE",
      priority: "MEDIUM",
      targetEnvironment: "all",
      targetTenants: "",
      publishedAt: new Date(),
      expiresAt: null,
      githubCommitSha: "",
      deploymentId: "",
    });
    setErrors({});
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold">
            Create New Changelog Entry
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Title */}
          <div className="space-y-2">
            <Label htmlFor="title">
              Title <span className="text-red-500">*</span>
            </Label>
            <Input
              id="title"
              value={formData.title}
              onChange={(e) =>
                setFormData((prev) => ({ ...prev, title: e.target.value }))
              }
              placeholder="Enter changelog title"
              className={errors.title ? "border-red-500" : ""}
            />
            {errors.title && (
              <p className="text-sm text-red-500">{errors.title}</p>
            )}
          </div>

          {/* Content */}
          <div className="space-y-2">
            <Label htmlFor="content">
              Content <span className="text-red-500">*</span>
            </Label>
            <Textarea
              id="content"
              value={formData.content}
              onChange={(e) =>
                setFormData((prev) => ({ ...prev, content: e.target.value }))
              }
              placeholder="Enter changelog content (Markdown supported)"
              rows={6}
              className={errors.content ? "border-red-500" : ""}
            />
            {errors.content && (
              <p className="text-sm text-red-500">{errors.content}</p>
            )}
          </div>

          {/* Version */}
          <div className="space-y-2">
            <Label htmlFor="version">Version</Label>
            <Input
              id="version"
              value={formData.version}
              onChange={(e) =>
                setFormData((prev) => ({ ...prev, version: e.target.value }))
              }
              placeholder="e.g., 1.2.0"
            />
          </div>

          {/* Type and Priority */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>
                Type <span className="text-red-500">*</span>
              </Label>
              <Select
                value={formData.type}
                onValueChange={(value: "RELEASE" | "HOTFIX" | "MAINTENANCE" | "ANNOUNCEMENT") =>
                  setFormData((prev) => ({ ...prev, type: value }))
                }
              >
                <SelectTrigger className={errors.type ? "border-red-500" : ""}>
                  <SelectValue placeholder="Select type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="RELEASE">New Feature</SelectItem>
                  <SelectItem value="HOTFIX">Bug Fix</SelectItem>
                  <SelectItem value="MAINTENANCE">Improvement</SelectItem>
                  <SelectItem value="ANNOUNCEMENT">Announcement</SelectItem>
                </SelectContent>
              </Select>
              {errors.type && (
                <p className="text-sm text-red-500">{errors.type}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label>
                Priority <span className="text-red-500">*</span>
              </Label>
              <Select
                value={formData.priority}
                onValueChange={(value: "LOW" | "MEDIUM" | "HIGH" | "CRITICAL") =>
                  setFormData((prev) => ({ ...prev, priority: value }))
                }
              >
                <SelectTrigger className={errors.priority ? "border-red-500" : ""}>
                  <SelectValue placeholder="Select priority" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="LOW">Low</SelectItem>
                  <SelectItem value="MEDIUM">Medium</SelectItem>
                  <SelectItem value="HIGH">High</SelectItem>
                  <SelectItem value="CRITICAL">Critical</SelectItem>
                </SelectContent>
              </Select>
              {errors.priority && (
                <p className="text-sm text-red-500">{errors.priority}</p>
              )}
            </div>
          </div>

          {/* Target Environment */}
          <div className="space-y-2">
            <Label htmlFor="targetEnvironment">Target Environment</Label>
            <Select
              value={formData.targetEnvironment || "all"}
              onValueChange={(value) =>
                setFormData((prev) => ({
                  ...prev,
                  targetEnvironment: value === "all" ? "" : value
                }))
              }
            >
              <SelectTrigger>
                <SelectValue placeholder="Select environment (optional)" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Environments</SelectItem>
                <SelectItem value="dev">Development</SelectItem>
                <SelectItem value="qa">QA/Testing</SelectItem>
                <SelectItem value="prod">Production</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Target Tenants */}
          <div className="space-y-2">
            <Label htmlFor="targetTenants">Target Tenants</Label>
            <Input
              id="targetTenants"
              value={formData.targetTenants}
              onChange={(e) =>
                setFormData((prev) => ({ ...prev, targetTenants: e.target.value }))
              }
              placeholder="Comma-separated tenant IDs (leave empty for all tenants)"
            />
            <p className="text-xs text-muted-foreground">
              Leave empty to target all tenants, or enter specific tenant IDs separated by commas
            </p>
          </div>

          {/* Published Date */}
          <div className="space-y-2">
            <Label>Published Date</Label>
            <Popover open={publishedDateOpen} onOpenChange={setPublishedDateOpen}>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={cn(
                    "w-full justify-start text-left font-normal",
                    !formData.publishedAt && "text-muted-foreground"
                  )}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {formData.publishedAt ? (
                    format(formData.publishedAt, "PPP")
                  ) : (
                    <span>Pick a date</span>
                  )}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0">
                <Calendar
                  mode="single"
                  selected={formData.publishedAt}
                  onSelect={(date) => {
                    if (date) {
                      setFormData((prev) => ({ ...prev, publishedAt: date }));
                      setPublishedDateOpen(false);
                    }
                  }}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
          </div>

          {/* Expiration Date */}
          <div className="space-y-2">
            <Label>Expiration Date (Optional)</Label>
            <Popover open={expiresDateOpen} onOpenChange={setExpiresDateOpen}>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={cn(
                    "w-full justify-start text-left font-normal",
                    !formData.expiresAt && "text-muted-foreground"
                  )}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {formData.expiresAt ? (
                    format(formData.expiresAt, "PPP")
                  ) : (
                    <span>Pick expiration date (optional)</span>
                  )}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0">
                <Calendar
                  mode="single"
                  selected={formData.expiresAt || undefined}
                  onSelect={(date) => {
                    setFormData((prev) => ({ ...prev, expiresAt: date || null }));
                    setExpiresDateOpen(false);
                  }}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
            {formData.expiresAt && (
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={() => setFormData((prev) => ({ ...prev, expiresAt: null }))}
                className="text-xs"
              >
                Clear expiration date
              </Button>
            )}
          </div>

          {/* GitHub Commit SHA */}
          <div className="space-y-2">
            <Label htmlFor="githubCommitSha">GitHub Commit SHA</Label>
            <Input
              id="githubCommitSha"
              value={formData.githubCommitSha}
              onChange={(e) =>
                setFormData((prev) => ({ ...prev, githubCommitSha: e.target.value }))
              }
              placeholder="e.g., abc123def456"
            />
          </div>

          {/* Deployment ID */}
          <div className="space-y-2">
            <Label htmlFor="deploymentId">Deployment ID</Label>
            <Input
              id="deploymentId"
              value={formData.deploymentId}
              onChange={(e) =>
                setFormData((prev) => ({ ...prev, deploymentId: e.target.value }))
              }
              placeholder="e.g., deploy-123456"
            />
          </div>

          <DialogFooter className="gap-2">
            <Button
              type="button"
              variant="outline"
              onClick={handleCancel}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Create Changelog
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
