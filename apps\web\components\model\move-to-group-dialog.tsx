"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  <PERSON><PERSON>Header,
  <PERSON>alog<PERSON><PERSON>le,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import toast from "react-hot-toast";
import { updateChat } from "@/services/src/chat";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { useLanguage } from "@/lib/language-context";

const formSchema = z.object({
  name: z.string().min(1, "Please enter a name"),
  groupId: z.string().min(1, "Please select a group"),
});

export function MoveToGroupDialog({
  groups,
  chatId,
  onMoveToGroup,
  trigger,
  currentGroupId,
}: {
  groups: { id: string; name: string }[];
  chatId: string;
  currentGroupId;
  onMoveToGroup: (chatId: string, groupId: string, name: string) => void;
  trigger: React.ReactNode;
}) {
  const [isLoading, setIsLoading] = useState(false);
  const { t } = useLanguage();
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema as any),
    defaultValues: {
      groupId: currentGroupId ?? "",
    },
  });

  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    try {
      setIsLoading(true);
      await onMoveToGroup(chatId, values.groupId, values.name);
    } catch (error) {
      toast.error("Failed to move chat");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog>
      <DialogTrigger asChild>{trigger}</DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{t("chat.moveToGroup")}</DialogTitle>
          <DialogDescription>{t("chat.moveChatDescription")}</DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form className="space-y-4 py-4">
            <FormField
              control={form.control}
              name="groupId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("chat.selectGroup")}</FormLabel>
                  <FormControl>
                    <RadioGroup
                      onValueChange={field.onChange}
                      value={field.value}
                      className="flex flex-col space-y-2"
                    >
                      {groups.map((group: any) => {
                        return (
                          <div
                            key={group?.id}
                            className="flex items-center space-x-2 rounded-md border p-2"
                          >
                            <RadioGroupItem value={group?.id} id={group?.id} />
                            <FormLabel
                              htmlFor={group?.id}
                              className="cursor-pointer"
                            >
                              {group?.name}
                            </FormLabel>
                          </div>
                        );
                      })}
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <DialogFooter>
              <Button
                onClick={() => onSubmit(form.getValues())}
                disabled={isLoading}
              >
                {isLoading ? t("common.moving") : t("common.move")}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
