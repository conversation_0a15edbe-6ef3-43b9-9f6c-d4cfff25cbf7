import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/next-auth";
import db from "@/lib/shared-db";
import crypto from "crypto";

// PUT /api/companies/[id]/members/[memberId] - Update member role
export async function PUT(
  request: Request,
  { params }: { params: { id: string; memberId: string } }
) {
  try {
    const session: any = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id, memberId } = params;
    const { role } = await request.json();

    if (!role) {
      return NextResponse.json({ error: "Role is required" }, { status: 400 });
    }

    // Get user
    const emailHash = crypto
      .createHash("sha256")
      .update(session.user.email)
      .digest("hex");

    const user = await db.user.findUnique({
      where: { emailHash },
      include: {
        membership: true,
      },
    });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Get company to check access
    const company = await db.company.findUnique({
      where: { id },
      include: {
        members: {
          where: { userId: user.id },
        },
      },
    });

    if (!company) {
      return NextResponse.json({ error: "Company not found" }, { status: 404 });
    }

    // Check if user has admin access to this company (either through tenant or company membership)
    const hasTenantAccess = user.membership.find(
      (m: any) => m.tenantId === company.tenantId
    );
    const companyMember = company.members[0];

    if (
      !hasTenantAccess &&
      (!companyMember || !["OWNER", "ADMIN"].includes(companyMember.role))
    ) {
      return NextResponse.json(
        { error: "Insufficient permissions" },
        { status: 403 }
      );
    }

    // Get the membership to update
    const targetMembership = await db.companyMember.findUnique({
      where: { id: memberId },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    if (!targetMembership || targetMembership.companyId !== id) {
      return NextResponse.json({ error: "Member not found" }, { status: 404 });
    }

    // Prevent changing owner role unless current user is owner
    const currentUserRole =
      companyMember?.role || (hasTenantAccess ? "ADMIN" : "MEMBER");
    if (targetMembership.role === "OWNER" && currentUserRole !== "OWNER") {
      return NextResponse.json(
        { error: "Only owners can change owner roles" },
        { status: 403 }
      );
    }

    // Update member role
    const updatedMembership = await db.companyMember.update({
      where: { id: memberId },
      data: { role: role.toUpperCase() as any },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    // Format response
    const formattedMember = {
      id: updatedMembership.id,
      name: updatedMembership.user.name,
      email: updatedMembership.user.email,
      role: updatedMembership.role.toLowerCase(),
      joinedAt: updatedMembership.joinedAt.toISOString().split("T")[0],
      status: updatedMembership.status.toLowerCase(),
    };

    return NextResponse.json({
      message: "Member role updated successfully",
      member: formattedMember,
    });
  } catch (error) {
    console.error("Error updating member role:", error);
    return NextResponse.json(
      { error: "Failed to update member role" },
      { status: 500 }
    );
  }
}

// DELETE /api/companies/[id]/members/[memberId] - Remove member from company
export async function DELETE(
  request: Request,
  { params }: { params: { id: string; memberId: string } }
) {
  try {
    const session: any = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id, memberId } = params;

    // Get user
    const emailHash = crypto
      .createHash("sha256")
      .update(session.user.email)
      .digest("hex");

    const user = await db.user.findUnique({
      where: { emailHash },
      include: {
        membership: true,
      },
    });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Get company to check access
    const company = await db.company.findUnique({
      where: { id },
      include: {
        members: {
          where: { userId: user.id },
        },
      },
    });

    if (!company) {
      return NextResponse.json({ error: "Company not found" }, { status: 404 });
    }

    // Check if user has admin access to this company (either through tenant or company membership)
    const hasTenantAccess = user.membership.find(
      (m: any) => m.tenantId === company.tenantId
    );
    const companyMember = company.members[0];

    if (
      !hasTenantAccess &&
      (!companyMember || !["OWNER", "ADMIN"].includes(companyMember.role))
    ) {
      return NextResponse.json(
        { error: "Insufficient permissions" },
        { status: 403 }
      );
    }

    // Get the membership to remove
    const targetMembership = await db.companyMember.findUnique({
      where: { id: memberId },
    });

    if (!targetMembership || targetMembership.companyId !== id) {
      return NextResponse.json({ error: "Member not found" }, { status: 404 });
    }

    // Prevent removing owner unless current user is owner
    const currentUserRole =
      companyMember?.role || (hasTenantAccess ? "ADMIN" : "MEMBER");
    if (targetMembership.role === "OWNER" && currentUserRole !== "OWNER") {
      return NextResponse.json(
        { error: "Only owners can remove other owners" },
        { status: 403 }
      );
    }

    // Prevent removing the last owner
    if (targetMembership.role === "OWNER") {
      const ownerCount = await db.companyMember.count({
        where: {
          companyId: id,
          role: "OWNER",
        },
      });

      if (ownerCount <= 1) {
        return NextResponse.json(
          { error: "Cannot remove the last owner" },
          { status: 400 }
        );
      }
    }

    // Remove member
    await db.companyMember.delete({
      where: { id: memberId },
    });

    return NextResponse.json({
      message: "Member removed successfully",
    });
  } catch (error) {
    console.error("Error removing member:", error);
    return NextResponse.json(
      { error: "Failed to remove member" },
      { status: 500 }
    );
  }
}
