/**
 * Permission Hierarchy Utilities
 * Shared logic for managing hierarchical permissions across role editors
 */

export interface Permission {
  id: string;
  action: string;
  resource: string;
  description?: string;
}

export interface PermissionState {
  checked: boolean;
  inherited: boolean;
  indeterminate: boolean;
}

// Define the resource hierarchy: WORKSPACE → PAGE → FOLDER → FILE
export const RESOURCE_HIERARCHY = {
  WORKSPACE: ["PAGE", "FOLDER", "FILE"],
  PAGE: ["FOLDER", "FILE"],
  FOLDER: ["FILE"],
  FILE: [],
} as const;

// Define parent relationships for reverse lookup
export const PARENT_RESOURCE_MAP = {
  FILE: "FOLDER",
  FOLDER: "PAGE",
  PAGE: "WORKSPACE",
} as const;

/**
 * Get child resources for a given resource
 */
export function getChildResources(resource: string): string[] {
  const children = RESOURCE_HIERARCHY[resource as keyof typeof RESOURCE_HIERARCHY];
  return children ? [...children] : [];
}

/**
 * Get parent resource for a given resource
 */
export function getParentResource(resource: string): string | null {
  return PARENT_RESOURCE_MAP[resource as keyof typeof PARENT_RESOURCE_MAP] || null;
}

/**
 * Get all permissions for a specific action and resource
 */
export function getPermissionsByActionAndResource(
  permissions: Permission[],
  action: string,
  resource: string
): Permission[] {
  return permissions.filter(
    (p) => p.action === action && p.resource === resource
  );
}

/**
 * Find permission by ID
 */
export function findPermissionById(
  permissions: Permission[],
  permissionId: string
): Permission | undefined {
  return permissions.find((p) => p.id === permissionId);
}

/**
 * Check if a parent resource permission is required
 */
export function requiresParentPermission(
  permissions: Permission[],
  permission: Permission
): {
  required: boolean;
  parentResource: string;
  parentPermissionId?: string;
} {
  const { action, resource } = permission;
  const parentResource = getParentResource(resource);
  
  if (!parentResource) {
    return { required: false, parentResource: "" };
  }

  // Find the corresponding parent permission with the same action
  const parentPermission = permissions.find(
    (p) => p.action === action && p.resource === parentResource
  );

  if (!parentPermission) {
    return { required: false, parentResource };
  }

  return {
    required: true,
    parentResource,
    parentPermissionId: parentPermission.id,
  };
}

/**
 * Check if all required parent permissions are selected
 */
export function hasAllRequiredParentPermissions(
  permissions: Permission[],
  selectedPermissions: string[],
  permission: Permission
): boolean {
  // If it's a workspace, no parent permissions are required
  if (permission.resource === "WORKSPACE") {
    return true;
  }

  // Check direct parent
  const { required, parentPermissionId } = requiresParentPermission(permissions, permission);

  // If direct parent permission is required but not selected, return false
  if (required && parentPermissionId && !selectedPermissions.includes(parentPermissionId)) {
    return false;
  }

  // If parent permission exists, recursively check if its parent permissions are selected
  if (parentPermissionId) {
    const parentPermission = findPermissionById(permissions, parentPermissionId);
    if (parentPermission) {
      return hasAllRequiredParentPermissions(permissions, selectedPermissions, parentPermission);
    }
  }

  return true;
}

/**
 * Check if a permission can be granted (has all required parent permissions)
 */
export function canGrantPermission(
  permissions: Permission[],
  selectedPermissions: string[],
  permission: Permission
): boolean {
  return hasAllRequiredParentPermissions(permissions, selectedPermissions, permission);
}

/**
 * Apply permission to child resources
 */
export function applyPermissionToChildren(
  permissions: Permission[],
  action: string,
  resource: string
): string[] {
  const childResources = getChildResources(resource);
  const childPermissionIds: string[] = [];

  childResources.forEach((childResource) => {
    const childPermissions = getPermissionsByActionAndResource(
      permissions,
      action,
      childResource
    );
    childPermissionIds.push(...childPermissions.map((p) => p.id));
  });

  return childPermissionIds;
}

/**
 * Get all affected child permissions when removing a permission
 */
export function getAffectedChildPermissions(
  permissions: Permission[],
  permission: Permission
): string[] {
  // Get direct child permissions
  const childPermissionIds = applyPermissionToChildren(
    permissions,
    permission.action,
    permission.resource
  );

  // Return all affected permission IDs
  return childPermissionIds;
}

/**
 * Check if all permissions in a resource group are selected
 */
export function areAllResourcePermissionsSelected(
  resourcePermissions: Permission[],
  selectedPermissions: string[]
): boolean {
  return resourcePermissions.every((p) => selectedPermissions.includes(p.id));
}

/**
 * Check if some permissions in a resource group are selected (for indeterminate state)
 */
export function areSomeResourcePermissionsSelected(
  resourcePermissions: Permission[],
  selectedPermissions: string[]
): boolean {
  return resourcePermissions.some((p) => selectedPermissions.includes(p.id));
}

/**
 * Get permission state (checked, inherited, indeterminate)
 */
export function getPermissionState(
  permissions: Permission[],
  selectedPermissions: string[],
  permission: Permission
): PermissionState {
  const isSelected = selectedPermissions.includes(permission.id);
  
  // Check if permission is inherited from parent
  const parentResource = getParentResource(permission.resource);
  let isInherited = false;
  
  if (parentResource) {
    const parentPermission = permissions.find(
      (p) => p.action === permission.action && p.resource === parentResource
    );
    if (parentPermission && selectedPermissions.includes(parentPermission.id)) {
      isInherited = true;
    }
  }

  return {
    checked: isSelected || isInherited,
    inherited: isInherited && !isSelected,
    indeterminate: false, // Will be calculated at resource level
  };
}

/**
 * Get resource group state for accordion headers
 */
export function getResourceGroupState(
  resourcePermissions: Permission[],
  selectedPermissions: string[]
): PermissionState {
  const allSelected = areAllResourcePermissionsSelected(resourcePermissions, selectedPermissions);
  const someSelected = areSomeResourcePermissionsSelected(resourcePermissions, selectedPermissions);
  
  return {
    checked: allSelected,
    inherited: false,
    indeterminate: someSelected && !allSelected,
  };
}
