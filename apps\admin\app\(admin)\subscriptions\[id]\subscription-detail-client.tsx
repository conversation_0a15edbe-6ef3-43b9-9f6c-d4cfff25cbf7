"use client";

import { useState } from "react";
import { useRouter, usePathname } from "next/navigation";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import {
  AreaChart,
  Area,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  TooltipProps,
} from "recharts";
import { TimePeriodFilter } from "@/app/(admin)/users/[id]/time-period-filter";
import { formatCurrency, formatDate, formatNumber } from "@/lib/utils";
import { ArrowLeft } from "lucide-react";

// Types for our data structures
interface Subscription {
  id: string;
  tenantName: string;
  tenantId: string;
  plan: string;
  planId: string;
  startDate: string;
  endDate: string | null;
  status: string;
  billingInterval: string;
  amount: number;
  additionalUsers: number;
  additionalStorage: number;
  storageTierItems: Array<{
    id: string;
    size: number;
    quantity: number;
    price: number;
    stripePriceId?: string;
  }>;
}

interface TokenUsageData {
  name: string;
  value: number;
  inputTokens: number;
  outputTokens: number;
  totalTokens: number;
  cost: number;
}

interface StorageUsageData {
  name: string;
  value: number;
}

interface SubscriptionDetailData {
  subscription: Subscription;
  tokenUsageData: TokenUsageData[];
  storageUsageData: StorageUsageData[];
  period: string;
}

// Status badge component
const StatusBadge = ({ status }: { status: string }) => {
  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "active":
        return "bg-green-100 text-green-800 hover:bg-green-100";
      case "trial":
        return "bg-blue-100 text-blue-800 hover:bg-blue-100";
      case "inactive":
      default:
        return "bg-gray-100 text-gray-800 hover:bg-gray-100";
    }
  };

  return (
    <Badge className={`${getStatusColor(status)} capitalize`}>{status}</Badge>
  );
};

// Billing interval badge component
const BillingBadge = ({ interval }: { interval: string }) => {
  const color =
    interval.toLowerCase() === "year"
      ? "bg-purple-100 text-purple-800 hover:bg-purple-100"
      : "bg-blue-100 text-blue-800 hover:bg-blue-100";

  return (
    <Badge className={`${color} capitalize`}>
      {interval.toLowerCase() === "year" ? "Yearly" : "Monthly"}
    </Badge>
  );
};

// Main client component
export function SubscriptionDetailClient({
  data,
}: {
  data: SubscriptionDetailData;
}) {
  const router = useRouter();
  const pathname = usePathname();
  const [period, setPeriod] = useState(data.period);

  // Handle period change
  const handlePeriodChange = (newPeriod: string) => {
    setPeriod(newPeriod);
    router.push(`${pathname}?period=${newPeriod}`);
  };

  // Calculate total additional costs
  const additionalUsersCost =
    data.subscription.additionalUsers * 10; // Assuming $10 per additional user
  const additionalStorageCost =
    data.subscription.additionalStorage * 5; // Assuming $5 per GB
  const storageTierCost = data.subscription.storageTierItems.reduce(
    (total, item) => total + item.price * item.quantity,
    0
  );

  // Calculate total cost
  const totalCost =
    data.subscription.amount +
    additionalUsersCost +
    additionalStorageCost +
    storageTierCost;

  // Calculate total token usage and cost
  const totalTokens = data.tokenUsageData.reduce(
    (sum, item) => sum + item.totalTokens,
    0
  );
  const totalTokenCost = data.tokenUsageData.reduce(
    (sum, item) => sum + item.cost,
    0
  );

  // Calculate total storage usage
  const totalStorage = data.storageUsageData.reduce(
    (sum, item) => sum + item.value,
    0
  );

  return (
    <div className="container mx-auto space-y-6">
      {/* Header with back button */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="icon"
            onClick={() => router.push("/subscriptions")}
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <h1 className="text-2xl font-bold tracking-tight">
            Subscription Details
          </h1>
        </div>
        <TimePeriodFilter period={period} onChange={handlePeriodChange} />
      </div>

      {/* Subscription info card */}
      <Card>
        <CardHeader>
          <CardTitle></CardTitle>
          <CardDescription>
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div>
                <h3 className="text-sm font-medium text-gray-500">Tenant</h3>
                <p className="text-lg font-medium">
                  {data.subscription.tenantName}
                </p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-500">Plan</h3>
                <p className="text-lg font-medium">{data.subscription.plan}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-500">Status</h3>
                <StatusBadge status={data.subscription.status} />
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-500">
                  Billing Interval
                </h3>
                <BillingBadge interval={data.subscription.billingInterval} />
              </div>
            </div>
            <div className="space-y-4">
              <div>
                <h3 className="text-sm font-medium text-gray-500">Start Date</h3>
                <p className="text-lg font-medium">
                  {formatDate(new Date(data.subscription.startDate))}
                </p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-500">End Date</h3>
                <p className="text-lg font-medium">
                  {data.subscription.endDate
                    ? formatDate(new Date(data.subscription.endDate))
                    : "No end date"}
                </p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-500">
                  Additional Users
                </h3>
                <p className="text-lg font-medium">
                  {data.subscription.additionalUsers}
                </p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-500">
                  Additional Storage
                </h3>
                <p className="text-lg font-medium">
                  {data.subscription.additionalStorage} GB
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Usage charts */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Token usage chart */}
        <Card>
          <CardHeader>
            <CardTitle>Token Usage</CardTitle>
            <CardDescription>
              Token consumption over the selected time period
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-80">
              {data.tokenUsageData.length > 0 ? (
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    data={data.tokenUsageData}
                    margin={{
                      top: 20,
                      right: 30,
                      left: 20,
                      bottom: 5,
                    }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <Tooltip 
                      formatter={(value) => [formatNumber(value as number), "Tokens"]}
                    />
                    <Legend />
                    <Bar dataKey="inputTokens" name="Input Tokens" fill="#3b82f6" />
                    <Bar dataKey="outputTokens" name="Output Tokens" fill="#8b5cf6" />
                  </BarChart>
                </ResponsiveContainer>
              ) : (
                <div className="flex h-full items-center justify-center text-gray-500">
                  No token usage data available for this period
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Storage usage chart */}
        <Card>
          <CardHeader>
            <CardTitle>Storage Usage</CardTitle>
            <CardDescription>
              Storage consumption over the selected time period
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-80">
              {data.storageUsageData.length > 0 ? (
                <ResponsiveContainer width="100%" height="100%">
                  <AreaChart
                    data={data.storageUsageData}
                    margin={{
                      top: 10,
                      right: 30,
                      left: 0,
                      bottom: 0,
                    }}
                  >
                    <defs>
                      <linearGradient id="colorStorage" x1="0" y1="0" x2="0" y2="1">
                        <stop offset="5%" stopColor="#10b981" stopOpacity={0.8} />
                        <stop offset="95%" stopColor="#10b981" stopOpacity={0.1} />
                      </linearGradient>
                    </defs>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <Tooltip 
                      formatter={(value) => [`${formatNumber(value as number)} GB`, "Storage"]}
                    />
                    <Area
                      type="monotone"
                      dataKey="value"
                      name="Storage (GB)"
                      stroke="#10b981"
                      fillOpacity={1}
                      fill="url(#colorStorage)"
                    />
                  </AreaChart>
                </ResponsiveContainer>
              ) : (
                <div className="flex h-full items-center justify-center text-gray-500">
                  No storage usage data available for this period
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Billing information */}
      <Card>
        <CardHeader>
          <CardTitle>Billing Information</CardTitle>
          <CardDescription>
            Cost breakdown for this subscription
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex justify-between">
              <span>Base Plan Cost</span>
              <span>{formatCurrency(data.subscription.amount)}</span>
            </div>
            <div className="flex justify-between">
              <span>Additional Users ({data.subscription.additionalUsers})</span>
              <span>{formatCurrency(additionalUsersCost)}</span>
            </div>
            <div className="flex justify-between">
              <span>
                Additional Storage ({data.subscription.additionalStorage} GB)
              </span>
              <span>{formatCurrency(additionalStorageCost)}</span>
            </div>
            {data.subscription.storageTierItems.length > 0 && (
              <div className="flex justify-between">
                <span>Storage Tier Items</span>
                <span>{formatCurrency(storageTierCost)}</span>
              </div>
            )}
            <Separator />
            <div className="flex justify-between font-bold">
              <span>Total Subscription Cost</span>
              <span>{formatCurrency(totalCost)}</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}