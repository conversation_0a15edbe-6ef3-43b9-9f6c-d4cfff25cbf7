import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import db from "@/lib/shared-db";
import { authOptions } from "@/lib/next-auth/src/auth-options";
import { withPermission } from "@/lib/permission-middleware";

// Get a specific group by ID
async function getGroup(request: Request, params?: any) {
  try {
    const session: any = await getServerSession(authOptions);

    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const url = new URL(request.url);
    const tenantId = url.searchParams.get("tenantId");
    const groupId = params?.params?.id ?? params?.id;
    if (!tenantId || !groupId) {
      return NextResponse.json(
        { error: "Tenant ID and Group ID are required" },
        { status: 400 }
      );
    }

    // Get the specific group for the tenant
    const group = await db.group.findFirst({
      where: {
        id: groupId,
        tenantId,
      },
      include: {
        groupMembers: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
                image: true,
              },
            },
          },
        },
        customRole: {
          include: {
            permissions: {
              include: {
                permission: true,
              },
            },
          },
        },
      },
    });

    if (!group) {
      return NextResponse.json({ error: "Group not found" }, { status: 404 });
    }

    return NextResponse.json({
      data: group,
      message: "Group retrieved successfully",
    });
  } catch (error) {
    console.error("Error fetching group:", error);
    return NextResponse.json(
      { error: "Failed to fetch group" },
      { status: 500 }
    );
  }
}

// Export the handler with permission checks
export const GET = withPermission(getGroup, "READ", "WORKSPACE");
