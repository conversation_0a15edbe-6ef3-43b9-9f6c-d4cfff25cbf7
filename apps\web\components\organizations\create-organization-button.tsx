"use client";

import { useState } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { PlusCircle, Building } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { createOrganization } from "@/services";
import toast from "react-hot-toast";
import { useLanguage } from "@/lib/language-context";

// Create schema with translations
const createFormSchema = (t: any) =>
  z.object({
    name: z.string().min(3, {
      message: t("organization.nameMinLength"),
    }),
    description: z.string().optional(),
    url: z
      .string()
      .url({
        message: t("organization.validUrl"),
      })
      .optional()
      .or(z.literal("")),
  });

type CreateOrganizationButtonProps = {
  variant?:
    | "default"
    | "outline"
    | "secondary"
    | "ghost"
    | "link"
    | "destructive";
  size?: "default" | "sm" | "lg" | "icon";
  className?: string;
  fullWidth?: boolean;
  iconOnly?: boolean;
};

export function CreateOrganizationButton({
  variant = "default",
  size = "default",
  className = "",
  fullWidth = false,
  iconOnly = false,
}: CreateOrganizationButtonProps) {
  const { data: session, update } = useSession();
  const router = useRouter();
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const { t } = useLanguage();

  // Create schema with translations
  const formSchema = createFormSchema(t);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema as any),
    defaultValues: {
      name: "",
      description: "",
      url: "",
    },
  });

  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    setLoading(true);
    try {
      // Generate slug from name
      const slug = values.name.toLowerCase().replace(/\s+/g, "-");

      const response = await createOrganization({
        name: values.name,
        slug,
        description: values.description,
        url: values.url,
      });

      if (response.error) {
        toast.error(response.error);
        return;
      }

      // Update session with new organization
      await update({
        memberships: [
          ...((session as any)?.memberships || []),
          {
            tenant: response.data,
            role: response.data.role,
          },
        ],
        currentOrganization: response.data,
      });

      // Reset form and close dialog
      form.reset();
      setOpen(false);

      // Refresh the page
      router.refresh();
      toast.success(t("organization.createSuccess"));
    } catch (error) {
      console.error("Error creating organization:", error);
      toast.error(t("organization.createFailed"));
    } finally {
      setLoading(false);
    }
  };

  if (!session) {
    return null;
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button
          variant={variant}
          size={size}
          className={className}
          style={fullWidth ? { width: "100%" } : undefined}
        >
          <PlusCircle className="h-4 w-4 mr-2" />
          {!iconOnly && t("organization.createOrganization")}
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{t("organization.createOrganization")}</DialogTitle>
          <DialogDescription>
            {t("organization.createDescription")}
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("common.name")}</FormLabel>
                  <FormControl>
                    <Input
                      placeholder={t("organization.namePlaceholder")}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("organization.descriptionOptional")}</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder={t("organization.descriptionPlaceholder")}
                      className="resize-none"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="url"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("organization.urlOptional")}</FormLabel>
                  <FormControl>
                    <Input
                      placeholder={t("organization.urlPlaceholder")}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => setOpen(false)}
                disabled={loading}
              >
                {t("common.cancel")}
              </Button>
              <Button type="submit" disabled={loading}>
                {loading ? t("organization.creating") : t("common.create")}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
