/**
 * CopilotKit Chat Service
 *
 * Service for interacting with the new high-performance CopilotKit agentic RAG system.
 * Provides both streaming and non-streaming chat capabilities with intelligent tool selection.
 */

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || "http://localhost:8000";
const API_VERSION = "/api/v1";

export interface CopilotKitChatQuery {
  question: string;
  stream?: boolean;
  previous_message?: string;
  search_mode?: "internal" | "web" | "hybrid" | "deep_research" | "";
  include_web_results?: boolean;
  selected_mcp_servers?: string[]; // Selected MCP server IDs
  max_tokens?: number;
  temperature?: number;
  images?: string[]; // Base64 encoded images
  image_context?: string; // Image context from CopilotKit
  audio_context?: string; // Audio transcription context
  video_context?: string; // Video analysis context (NEW)
  audio_files?: string[]; // Audio file URLs for transcription
  deep_answer?: boolean; // Use DeepSeek R1 for expanded research-grade answers
  language?: string; // User's preferred language for AI responses
}

export interface CopilotKitChatResponse {
  answer: string;
  title: string;
  sources: Array<{
    type: string;
    title: string;
    content: string;
    metadata: Record<string, any>;
  }>;
  tools_used: string[];
  elapsed_time: number;
  thinking?: string; // For DeepSeek R1 compatibility
  status: number;
}

export interface CopilotKitStreamChunk {
  status?: string;
  message?: string;
  tools_used?: string[];
  answer?: string;
  content?: string;
  answer_chunk?: string; // Individual content chunks for real-time streaming
  partial_answer?: string; // Accumulated partial response
  title?: string;
  sources?: Array<any>;
  elapsed_time?: number;
  thinking?: string;
  done?: boolean;
  error?: string;
  // Deep Research Agent fields
  research_data?: {
    phase?: string;
    message?: string;
    progress?: number;
    iteration?: number;
    confidence?: number;
    gaps_identified?: string[];
    research_plan?: {
      main_topic?: string;
      subtopics?: string[];
      research_questions?: string[];
    };
    iterations?: Array<{
      iteration: number;
      phase: string;
      confidence: number;
      gaps_identified: string[];
      findings_preview: string;
    }>;
  };
  research_summary?: {
    iterations_conducted?: number;
    final_confidence?: number;
    quality_score?: number;
    subtopics_covered?: number;
    sources_consulted?: number;
  };
  iterations?: Array<{
    iteration: number;
    phase: string;
    confidence: number;
    gaps_identified: string[];
    findings_preview: string;
  }>;
  progress?: number; // Overall progress for deep research
}

export interface CopilotKitToolsInfo {
  tools: Array<{
    name: string;
    description: string;
    capabilities: string[];
  }>;
  search_modes: string[];
  features: string[];
}

export interface CopilotKitBenchmarkResult {
  performance_metrics: {
    total_time_seconds: number;
    tools_used: string[];
    sources_found: number;
    answer_length: number;
    status: number;
  };
  result: CopilotKitChatResponse;
  benchmark_timestamp: string;
}

export const publicChatService = {
  /**
   * Send a chat query to the CopilotKit agentic RAG system
   */
  async chat(
    query: CopilotKitChatQuery,
    chatbotId: string,
    userId: string,
    tenantId: string,
    userName: string,
    apiKey: string
  ): Promise<CopilotKitChatResponse> {
    try {
      const response = await fetch(
        `${API_BASE_URL}${API_VERSION}/public/chatbot/${chatbotId}/chat?current_user=${userId}&tenant_id=${tenantId}&user_name=${encodeURIComponent(
          userName
        )}`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${apiKey}`,
          },
          body: JSON.stringify(query),
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error("Error in CopilotKit chat:", error);
      throw error;
    }
  },

  /**
   * Stream chat responses from the CopilotKit agentic RAG system
   */
  async *streamChat(
    query: CopilotKitChatQuery,
    chatbotId: string,
    userId: string,
    tenantId: string,
    userName: string,
    apiKey: string
  ): AsyncGenerator<CopilotKitStreamChunk, void, unknown> {
    try {
      const url = `${API_BASE_URL}${API_VERSION}/public/chatbot/${chatbotId}/chat?current_user=${userId}&tenant_id=${tenantId}&user_name=${encodeURIComponent(
        userName
      )}`;

      const response = await fetch(url, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${apiKey}`,
        },
        body: JSON.stringify({ ...query, stream: true }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(
          `HTTP error! status: ${response.status}, body: ${errorText}`
        );
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error("No response body reader available");
      }

      const decoder = new TextDecoder();
      let buffer = "";

      try {
        while (true) {
          const { done, value } = await reader.read();

          if (done) break;

          buffer += decoder.decode(value, { stream: true });
          const lines = buffer.split("\n");
          buffer = lines.pop() || "";

          for (const line of lines) {
            const trimmedLine = line.trim();

            if (trimmedLine.startsWith("data: ")) {
              try {
                const jsonData = trimmedLine.slice(6);
                if (jsonData) {
                  const data = JSON.parse(jsonData);
                  yield data;

                  if (data.done) {
                    return;
                  }
                }
              } catch (e) {
                console.warn("Failed to parse streaming data:", trimmedLine, e);
              }
            } else if (trimmedLine === "") {
              // Empty line, continue
              continue;
            } else if (
              trimmedLine.startsWith("event:") ||
              trimmedLine.startsWith("id:")
            ) {
              // SSE metadata, ignore
              continue;
            } else {
              // Might be plain text data without "data:" prefix
              try {
                const data = JSON.parse(trimmedLine);
                yield data;
                if (data.done) {
                  return;
                }
              } catch (e) {
                // Not JSON, might be plain text chunk
                if (trimmedLine.length > 0) {
                  yield {
                    answer_chunk: trimmedLine,
                    done: false,
                    status: "streaming",
                  };
                }
              }
            }
          }
        }
      } finally {
        reader.releaseLock();
      }
    } catch (error) {
      console.error("Error in CopilotKit streaming chat:", error);
      yield {
        error: `Streaming failed: ${
          error instanceof Error ? error.message : String(error)
        }`,
        done: true,
      };
    }
  },
};
