import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/next-auth/src/auth-options";
import db from "@/lib/shared-db";
import crypto from "crypto";
import { withPermission } from "@/lib/permission-middleware";
import {
  checkActiveSubscription,
  checkUserLimit,
} from "@/lib/subscription-check";

interface BulkImportRequest {
  email: string;
  name?: string;
  role: string;
  tenantId: string;
}

async function bulkImportMember(request: NextRequest) {
  try {
    const session: any = await getServerSession(authOptions);

    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body: BulkImportRequest = await request.json();
    const { email, name, role, tenantId } = body;

    // Validate required fields
    if (!email || !role || !tenantId) {
      return NextResponse.json(
        { error: "Email, role, and tenantId are required" },
        { status: 400 }
      );
    }

    // Validate role
    const validRoles = ["MEMBER", "ADMIN", "CUSTOM"];
    if (!validRoles.includes(role)) {
      return NextResponse.json(
        { error: `Invalid role. Must be one of: ${validRoles.join(", ")}` },
        { status: 400 }
      );
    }

    // Get tenant information
    const tenant = await db.tenant.findUnique({
      where: { id: tenantId },
    });

    if (!tenant) {
      return NextResponse.json({ error: "Tenant not found" }, { status: 404 });
    }

    // Check if tenant has an active subscription
    const { hasActiveSubscription } = await checkActiveSubscription(tenantId);
    if (!hasActiveSubscription) {
      return NextResponse.json(
        { error: "Active subscription required" },
        { status: 403 }
      );
    }

    // Check user limit
    const { hasReachedLimit, currentCount, limit } =
      await checkUserLimit(tenantId);
    if (hasReachedLimit) {
      return NextResponse.json(
        {
          error: "subscription.userLimitReached",
          data: { currentCount, limit },
        },
        { status: 403 }
      );
    }

    // Check if user already exists in the system
    const emailHash = crypto.createHash("sha256").update(email).digest("hex");
    let user = await db.user.findUnique({
      where: { emailHash },
      include: {
        membership: {
          where: { tenantId },
        },
      },
    });

    // If user doesn't exist, create them
    if (!user) {
      user = await db.user.create({
        data: {
          email,
          name: name || email.split("@")[0], // Use email prefix as fallback name
          emailHash,
          membership: {
            create: {
              tenantId,
              role,
            },
          },
        },
        include: {
          membership: {
            where: { tenantId },
          },
        },
      });
    } else {
      // Check if user is already a member of this tenant
      if (user.membership.length > 0) {
        return NextResponse.json(
          { error: `User ${email} is already a member of this organization` },
          { status: 400 }
        );
      }

      // Add membership to existing user
      await db.membership.create({
        data: {
          userId: user.id,
          tenantId,
          role,
        },
      });
    }

    // Create invitation record for tracking
    const invitation = await db.invitation.create({
      data: {
        email,
        role,
        tenantId,
        invitedBy: session.userId,
        status: "ACCEPTED", // Mark as accepted since user is directly added
      },
    });

    // TODO: Send welcome email to the user
    // This would typically be handled by a background job or email service

    return NextResponse.json({
      message: "Member imported successfully",
      data: {
        userId: user.id,
        email: user.email,
        name: user.name,
        role,
        invitationId: invitation.id,
      },
    });
  } catch (error) {
    console.error("Bulk import error:", error);

    // Handle specific database errors
    if (error instanceof Error) {
      if (error.message.includes("Unique constraint")) {
        return NextResponse.json(
          { error: "User already exists in the system" },
          { status: 400 }
        );
      }
    }

    return NextResponse.json(
      { error: "Failed to import member" },
      { status: 500 }
    );
  }
}

// Export with permission middleware
export const POST = withPermission(bulkImportMember, "CREATE", "MEMBER");
