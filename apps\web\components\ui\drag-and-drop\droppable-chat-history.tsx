"use client";

import React from "react";
import { useDroppable } from "@dnd-kit/core";
import { cn } from "@/lib/utils";
import { useLanguage } from "@/lib/language-context";

interface DroppableChatHistoryProps {
  children: React.ReactNode;
  className?: string;
}

export function DroppableChatHistory({
  children,
  className,
}: DroppableChatHistoryProps) {
  const { t } = useLanguage();

  const { isOver, setNodeRef } = useDroppable({
    id: "chat-history",
    data: {
      type: "chat-history",
    },
  });

  return (
    <div
      ref={setNodeRef}
      className={cn(
        "relative transition-all duration-200 min-h-[100px]",
        isOver && "bg-transparent rounded-md",
        className
      )}
      data-droppable-id="chat-history"
    >
      {children}

      {/* Drop indicator for chat history */}
      {isOver && (
        <div className="absolute inset-0 flex items-center justify-center bg-transparent rounded-md border pointer-events-none z-10">
          <div className="text-xs text-green-700 font-medium px-3 py-2 bg-transparent rounded shadow-sm">
            {t("chat.dropToUngroup") || "Drop here to remove from group"}
          </div>
        </div>
      )}
    </div>
  );
}
