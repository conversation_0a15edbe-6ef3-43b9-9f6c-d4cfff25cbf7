/**
 * CSV parsing utilities for bulk group member import
 */

export interface ParsedGroupMemberData {
  email: string;
  groupId?: string;
  groupName?: string;
}

export interface GroupCSVParseResult {
  data: ParsedGroupMemberData[];
  errors: Array<{
    row: number;
    field: string;
    message: string;
  }>;
  warnings: Array<{
    row: number;
    field: string;
    message: string;
  }>;
}

const REQUIRED_HEADERS = ["email"];
const OPTIONAL_HEADERS = ["groupId", "groupName"];

/**
 * Parses CSV content and validates group member data
 */
export function parseGroupCSV(csvContent: string): GroupCSVParseResult {
  const result: GroupCSVParseResult = {
    data: [],
    errors: [],
    warnings: [],
  };

  try {
    const lines = csvContent.trim().split("\n");

    if (lines.length < 2) {
      result.errors.push({
        row: 0,
        field: "file",
        message: "CSV file must contain at least a header row and one data row",
      });
      return result;
    }

    // Parse headers
    const headers = lines[0].split(",").map((h) => h.trim().toLowerCase());

    // Validate required headers
    const missingHeaders = REQUIRED_HEADERS.filter(
      (header) => !headers.includes(header.toLowerCase())
    );

    if (missingHeaders.length > 0) {
      result.errors.push({
        row: 0,
        field: "headers",
        message: `Missing required headers: ${missingHeaders.join(", ")}`,
      });
      return result;
    }

    // Check for group identifier (either groupId or groupName)
    const hasGroupId = headers.includes("groupid");
    const hasGroupName = headers.includes("groupname");

    if (!hasGroupId && !hasGroupName) {
      result.errors.push({
        row: 0,
        field: "headers",
        message: "Either 'groupId' or 'groupName' column is required",
      });
      return result;
    }

    // Parse data rows
    for (let i = 1; i < lines.length; i++) {
      const rowData = parseCSVRow(lines[i]);
      const rowNumber = i + 1;

      if (rowData.length === 0) continue; // Skip empty rows

      const memberData: Partial<ParsedGroupMemberData> = {};

      // Map CSV columns to member data
      headers.forEach((header, index) => {
        const value = rowData[index]?.trim() || "";

        switch (header) {
          case "email":
            memberData.email = value;
            break;
          case "groupid":
            memberData.groupId = value;
            break;
          case "groupname":
            memberData.groupName = value;
            break;
        }
      });

      // Validate member data
      const validationResult = validateGroupMemberData(memberData, rowNumber);
      result.errors.push(...validationResult.errors);
      result.warnings.push(...validationResult.warnings);

      if (validationResult.errors.length === 0) {
        result.data.push(memberData as ParsedGroupMemberData);
      }
    }
  } catch (error) {
    result.errors.push({
      row: 0,
      field: "file",
      message: `Failed to parse CSV: ${
        error instanceof Error ? error.message : "Unknown error"
      }`,
    });
  }

  return result;
}

/**
 * Parses a single CSV row, handling quoted values
 */
function parseCSVRow(row: string): string[] {
  const result: string[] = [];
  let current = "";
  let inQuotes = false;

  for (let i = 0; i < row.length; i++) {
    const char = row[i];

    if (char === '"') {
      if (inQuotes && row[i + 1] === '"') {
        // Escaped quote
        current += '"';
        i++; // Skip next quote
      } else {
        // Toggle quote state
        inQuotes = !inQuotes;
      }
    } else if (char === "," && !inQuotes) {
      // End of field
      result.push(current);
      current = "";
    } else {
      current += char;
    }
  }

  // Add the last field
  result.push(current);

  return result;
}

/**
 * Validates group member data for a single row
 */
function validateGroupMemberData(
  data: Partial<ParsedGroupMemberData>,
  rowNumber: number
): { errors: GroupCSVParseResult["errors"]; warnings: GroupCSVParseResult["warnings"] } {
  const errors: GroupCSVParseResult["errors"] = [];
  const warnings: GroupCSVParseResult["warnings"] = [];

  // Validate email
  if (!data.email) {
    errors.push({
      row: rowNumber,
      field: "email",
      message: "Email is required",
    });
  } else if (!isValidEmail(data.email)) {
    errors.push({
      row: rowNumber,
      field: "email",
      message: "Invalid email format",
    });
  }

  // Validate group identifier
  if (!data.groupId && !data.groupName) {
    errors.push({
      row: rowNumber,
      field: "group",
      message: "Either groupId or groupName is required",
    });
  }

  // Warning if both groupId and groupName are provided
  if (data.groupId && data.groupName) {
    warnings.push({
      row: rowNumber,
      field: "group",
      message: "Both groupId and groupName provided - groupId will take precedence",
    });
  }

  return { errors, warnings };
}

/**
 * Simple email validation
 */
function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * Generates a sample CSV template for group member import
 */
export function generateGroupCSVTemplate(
  groups?: Array<{ id: string; name: string }>
): string {
  const headers = ["email", "groupName", "groupId"];
  const sampleData = [
    ["<EMAIL>", "Development Team", ""],
    ["<EMAIL>", "Admin Group", ""],
  ];

  // Add group examples if available
  if (groups && groups.length > 0) {
    const firstGroup = groups[0];
    sampleData.push([
      "<EMAIL>",
      firstGroup.name,
      firstGroup.id,
    ]);
  } else {
    sampleData.push([
      "<EMAIL>",
      "Your Group Name",
      "your-group-id",
    ]);
  }

  let csv = headers.join(",") + "\n";
  sampleData.forEach((row) => {
    csv +=
      row.map((cell) => (cell.includes(",") ? `"${cell}"` : cell)).join(",") +
      "\n";
  });

  // Add groups reference as comments if available
  if (groups && groups.length > 0) {
    csv += "\n# Available Groups:\n";
    groups.forEach((group) => {
      csv += `# ${group.name}: ${group.id}\n`;
    });
  }

  return csv;
}
