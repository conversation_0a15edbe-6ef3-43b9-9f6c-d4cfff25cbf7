# Chat Drag and Drop Components

This directory contains the drag-and-drop functionality for chat elements in the Swiss Knowledge Hub application.

## Components

### `DraggableChat`
Makes chat items draggable. Wraps chat elements to enable drag functionality.

**Props:**
- `chat`: ChatItem object with id, title, url, and groupId
- `children`: React node to render as draggable content
- `className`: Optional CSS classes

### `DroppableGroup`
Makes group containers accept dropped chat items.

**Props:**
- `group`: GroupItem object with id, name, and chats array
- `children`: React node to render as droppable content
- `className`: Optional CSS classes
- `isExpanded`: Whether the group is expanded (affects drop indicator)

### `ChatDragOverlay`
Provides visual feedback during drag operations.

### `ChatDragProvider`
Context provider that manages drag state and operations.

**Props:**
- `children`: React nodes
- `onChatMoved`: Optional callback when a chat is moved

## Usage

```tsx
import { DndContext, closestCenter } from '@dnd-kit/core';
import { DraggableChat, DroppableGroup, ChatDragOverlay } from '@/components/ui/drag-and-drop';
import { useChatDragDrop } from '@/hooks/use-chat-drag-drop';

function ChatSidebar() {
  const { handleDragStart, handleDragOver, handleDragEnd } = useChatDragDrop();

  return (
    <DndContext
      collisionDetection={closestCenter}
      onDragStart={handleDragStart}
      onDragOver={handleDragOver}
      onDragEnd={handleDragEnd}
    >
      {/* Groups */}
      {groups.map(group => (
        <DroppableGroup key={group.id} group={group}>
          {/* Group content */}
          {group.chats.map(chat => (
            <DraggableChat key={chat.id} chat={chat}>
              {/* Chat item content */}
            </DraggableChat>
          ))}
        </DroppableGroup>
      ))}
      
      <ChatDragOverlay />
    </DndContext>
  );
}
```

## Features

- **Drag individual chats into groups**: Users can drag chat items from ungrouped chats or between groups
- **Move chats between different groups**: Supports moving chats from one group to another
- **Visual feedback**: Provides drag preview and drop zone highlighting
- **Accessibility**: Includes screen reader announcements and keyboard navigation support
- **Internationalization**: Supports English and German translations
- **Error handling**: Graceful handling of failed operations with user feedback

## API Integration

The components use the existing `updateChat` API to persist group changes:

```typescript
await updateChat({
  id: chatId,
  groupId: targetGroupId,
});
```

## Accessibility

- Drag operations announce start, progress, and completion
- Drop zones are clearly indicated
- Keyboard navigation support through @dnd-kit
- Screen reader compatible

## Translations

The following translation keys are used:

- `chat.moving`: "Moving chat..."
- `chat.moveSuccess`: "Chat moved successfully"
- `chat.moveFailed`: "Failed to move chat"
- `chat.dropHere`: "Drop here to add to this group"
- `accessibility.dragStart`: "Started dragging chat: {chatTitle}"
- `accessibility.dragOver`: "Dragging {chatTitle} over {groupName}"
- `accessibility.dragEnd`: "Moved {chatTitle} to {groupName}"
