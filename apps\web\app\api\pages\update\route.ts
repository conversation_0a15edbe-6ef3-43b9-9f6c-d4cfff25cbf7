import { NextRequest, NextResponse } from "next/server";
import db from "@/lib/shared-db";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/next-auth/src/auth-options";

export async function POST(req: NextRequest) {
  try {
    const session: any = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id, gDriveFolderId, oneDriveFolderId, workspaceId, tenantId } =
      await req.json();

    if (!id) {
      return NextResponse.json(
        { error: "Page ID is required" },
        { status: 400 }
      );
    }

    // Check if the user has access to the page
    const page = await db.page.findUnique({
      where: { id },
      include: {
        folders: true,
        files: true,
      },
    });

    if (!page) {
      return NextResponse.json({ error: "Page not found" }, { status: 404 });
    }

    // Update the page
    if (gDriveFolderId === null && oneDriveFolderId === null) {
      const response = await db.$transaction(async (prisma) => {
        await db.page.update({
          where: { id },
          data: {
            gDriveFolderId: gDriveFolderId === null ? null : gDriveFolderId,
            oneDriveFolderId:
              oneDriveFolderId === null ? null : oneDriveFolderId,
          },
        });
        await db.folder.updateMany({
          where: {
            pageId: id,
          },
          data: {
            oneDriveFolderId: null,
            gDriveFolderId: null,
          },
        });
        await db.file.updateMany({
          where: {
            pageId: id,
          },
          data: {
            oneDriveFileId: null,
            gDriveFileId: null,
          },
        });
      });
      return NextResponse.json({
        data: { id },
      });
    }
    const updatedPage = await db.page.update({
      where: { id },
      data: {
        gDriveFolderId: gDriveFolderId === null ? null : gDriveFolderId,
        oneDriveFolderId: oneDriveFolderId === null ? null : oneDriveFolderId,
      },
    });

    return NextResponse.json({
      data: updatedPage,
    });
  } catch (error) {
    console.error("Error updating page:", error);
    return NextResponse.json(
      { error: "Failed to update page" },
      { status: 500 }
    );
  }
}
