variable "vnet_name" {
  description = "Virtual Network Name"
  type        = string
}

variable "location" {
  description = "Azure Region"
  type        = string
  default     = "Switzerland North" # Default value (optional)
}
variable "static_app_location" {
  description = "Azure Region"
  type        = string
  default     = "westeurope" # Default value (optional)
}

variable "resource_group_name" {
  description = "Resource Group Name"
  type        = string
}


variable "swa_name" {
  description = "Frontend App Service Name"
  type        = string
}

variable "admin_swa_name" {
  description = "Admin Frontend App Service Name"
  type        = string
}

variable "backend_app_service_plan_name" {
  description = "Backend App Service Plan Name"
  type        = string
}

variable "backend_app_service_name" {
  description = "Backend App Service Name"
  type        = string
}

variable "app_service_sku_name" {
  description = "App Service SKU Name"
  type        = string
}

variable "cosmosdb_name" {
  description = "Cosmosdb Name"
  type        = string
}

variable "database_name" {
  description = "Database Name"
  type        = string
}
variable "cosmosdb_username" {
  description = "Database username"
  type        = string
}
variable "cosmosdb_password" {
  description = "Database password"
  type        = string
}

variable "cosmosdb_location" {
  description = "Cosmosdb Location"
  type        = string
}

variable "cosmosdb_tire" {
  description = "Cosmosdb Tire"
  type        = string
}

variable "cosmosdb_size" {
  description = "Cosmosdb Size"
  type        = string
}

variable "client_id" {}
variable "client_secret" {}
variable "tenant_id" {}
variable "subscription_id" {}

variable "storage_account_name" {
  description = "Name of the storage account"
  type        = string
}

variable "environment" {
  description = "Environment name"
  type        = string
}

variable "data_location" {
  description = "Location where data will be stored"
  type        = string
  default     = "United States"
}
