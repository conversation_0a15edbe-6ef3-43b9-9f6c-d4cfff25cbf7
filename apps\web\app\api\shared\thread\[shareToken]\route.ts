import { NextResponse } from "next/server";
import db from "@/lib/shared-db";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/next-auth";

// Get shared thread by token
export async function GET(
  req: Request,
  { params }: { params: { shareToken: string } }
) {
  try {
    const session: any = await getServerSession(authOptions);
    
    // Find the thread share
    const threadShare = await db.threadShare.findUnique({
      where: {
        shareToken: params.shareToken,
      },
      include: {
        chat: {
          include: {
            messages: {
              orderBy: {
                createdAt: "asc",
              },
              include: {
                user: {
                  select: {
                    id: true,
                    name: true,
                    email: true,
                  },
                },
              },
            },
            user: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
        },
        createdBy: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        tenant: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
      },
    });

    if (!threadShare) {
      return new NextResponse("Thread not found", { status: 404 });
    }

    // Check if share has expired
    if (threadShare.expiresAt && new Date() > threadShare.expiresAt) {
      return new NextResponse("Share link has expired", { status: 410 });
    }

    // Check access permissions
    let hasAccess = false;

    if (threadShare.isPublic) {
      // Public shares are accessible to anyone
      hasAccess = true;
    } else if (session) {
      // Private shares require workspace access
      // Check if user is member of the tenant
      const membership = await db.membership.findFirst({
        where: {
          userId: session.userId,
          tenantId: threadShare.tenantId,
        },
      });

      if (membership) {
        hasAccess = true;
      }
    }

    if (!hasAccess) {
      return new NextResponse("Access denied", { status: 403 });
    }

    // Process messages to include workspace information for sources
    const processedMessages = await Promise.all(
      threadShare.chat.messages.map(async (message: any) => {
        if (message.sources && message.sources.length > 0) {
          const workspaceIds = [
            ...new Set(
              message.sources
                .map((source: any) => source?.metadata?.workspace_id)
                .filter(Boolean)
            ),
          ];

          const workspaces = await db.workspace.findMany({
            where: {
              id: {
                in: workspaceIds,
              },
            },
          });

          return {
            ...message,
            sources: message.sources.map((source: any) => ({
              ...source,
              metadata: {
                ...source?.metadata,
                workspace: workspaces.find(
                  (workspace: any) =>
                    workspace.id === source?.metadata?.workspace_id
                ),
              },
            })),
          };
        }
        return message;
      })
    );

    return NextResponse.json({
      chat: {
        ...threadShare.chat,
        messages: processedMessages,
      },
      shareInfo: {
        shareToken: threadShare.shareToken,
        isPublic: threadShare.isPublic,
        expiresAt: threadShare.expiresAt,
        createdBy: threadShare.createdBy,
        createdAt: threadShare.createdAt,
      },
      tenant: threadShare.tenant,
      hasAccess: true,
      canComment: !!session, // Only authenticated users can comment
    });
  } catch (error) {
    console.error("[SHARED_THREAD_GET]", error);
    return NextResponse.json({ error: "Internal Error" }, { status: 500 });
  }
}
