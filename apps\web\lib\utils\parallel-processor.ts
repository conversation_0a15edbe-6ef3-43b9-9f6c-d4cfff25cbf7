/**
 * Parallel Processing Utility
 *
 * Provides utilities for processing items in parallel with configurable
 * concurrency, retry logic, and progress tracking.
 */

export interface ProcessorOptions<T, R> {
  /** Maximum number of concurrent operations */
  concurrency?: number;
  /** Maximum number of retry attempts */
  maxRetries?: number;
  /** Delay between retries in milliseconds */
  retryDelay?: number;
  /** Delay between batches in milliseconds */
  batchDelay?: number;
  /** Progress callback */
  onProgress?: (progress: ProcessorProgress<T, R>) => void;
  /** Error callback for individual items */
  onItemError?: (item: T, error: Error, attempt: number) => void;
}

export interface ProcessorProgress<T, R> {
  total: number;
  processed: number;
  successful: number;
  failed: number;
  currentBatch: T[];
  results: ProcessorResult<T, R>[];
}

export interface ProcessorResult<T, R> {
  item: T;
  success: boolean;
  result?: R;
  error?: Error;
  attempts: number;
}

/**
 * Process items in parallel with configurable concurrency and retry logic
 */
export class ParallelProcessor<T, R> {
  private options: Required<ProcessorOptions<T, R>>;

  constructor(options: ProcessorOptions<T, R> = {}) {
    this.options = {
      concurrency: 8,
      maxRetries: 3,
      retryDelay: 100,
      batchDelay: 50,
      onProgress: () => {},
      onItemError: () => {},
      ...options,
    };
  }

  /**
   * Process a single item with retry logic
   */
  private async processItemWithRetry(
    item: T,
    processor: (item: T) => Promise<R>,
    attempt = 0
  ): Promise<ProcessorResult<T, R>> {
    try {
      const result = await processor(item);
      return {
        item,
        success: true,
        result,
        attempts: attempt + 1,
      };
    } catch (error) {
      const errorObj =
        error instanceof Error ? error : new Error(String(error));
      this.options.onItemError(item, errorObj, attempt + 1);

      if (attempt < this.options.maxRetries) {
        // Exponential backoff
        const delay = this.options.retryDelay * Math.pow(2, attempt);
        await new Promise((resolve) => setTimeout(resolve, delay));
        return this.processItemWithRetry(item, processor, attempt + 1);
      }

      return {
        item,
        success: false,
        error: errorObj,
        attempts: attempt + 1,
      };
    }
  }

  /**
   * Process all items in parallel batches
   */
  async processAll(
    items: T[],
    processor: (item: T) => Promise<R>
  ): Promise<ProcessorResult<T, R>[]> {
    const results: ProcessorResult<T, R>[] = [];
    const total = items.length;
    let processed = 0;
    let successful = 0;
    let failed = 0;

    // Process items in batches
    for (let i = 0; i < total; i += this.options.concurrency) {
      const batch = items.slice(i, i + this.options.concurrency);

      // Update progress with current batch
      this.options.onProgress({
        total,
        processed,
        successful,
        failed,
        currentBatch: batch,
        results: [...results],
      });

      // Process batch in parallel using Promise.allSettled
      const batchPromises = batch.map((item) =>
        this.processItemWithRetry(item, processor)
      );
      const batchResults = await Promise.allSettled(batchPromises);

      // Process batch results
      batchResults.forEach((result) => {
        if (result.status === "fulfilled") {
          results.push(result.value);
          if (result.value.success) {
            successful++;
          } else {
            failed++;
          }
        } else {
          // This should rarely happen since processItemWithRetry handles errors
          const errorResult: ProcessorResult<T, R> = {
            item: batch[results.length % batch.length], // Fallback item
            success: false,
            error:
              result.reason instanceof Error
                ? result.reason
                : new Error(String(result.reason)),
            attempts: 1,
          };
          results.push(errorResult);
          failed++;
        }
        processed++;
      });

      // Update progress after batch completion
      this.options.onProgress({
        total,
        processed,
        successful,
        failed,
        currentBatch: [],
        results: [...results],
      });

      // Add delay between batches
      if (i + this.options.concurrency < total) {
        await new Promise((resolve) =>
          setTimeout(resolve, this.options.batchDelay)
        );
      }
    }

    return results;
  }
}

/**
 * Convenience function for simple parallel processing
 */
export async function processInParallel<T, R>(
  items: T[],
  processor: (item: T) => Promise<R>,
  options: ProcessorOptions<T, R> = {}
): Promise<ProcessorResult<T, R>[]> {
  const parallelProcessor = new ParallelProcessor(options);
  return parallelProcessor.processAll(items, processor);
}

/**
 * Utility for processing with progress tracking
 */
export function createProgressTracker<T, R>() {
  let currentProgress: ProcessorProgress<T, R> | null = null;

  return {
    getProgress: () => currentProgress,
    onProgress: (progress: ProcessorProgress<T, R>) => {
      currentProgress = progress;
    },
    reset: () => {
      currentProgress = null;
    },
  };
}
