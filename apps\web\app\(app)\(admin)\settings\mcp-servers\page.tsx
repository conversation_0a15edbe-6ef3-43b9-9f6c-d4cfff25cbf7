"use client";

import { useState } from "react";
import { MCPServer } from "@/types/mcp-server";
import { useMCPServers } from "@/hooks/use-mcp-servers";
import { MCPServerList } from "@/components/mcp-servers/mcp-server-list";
import { MCPServerForm } from "@/components/mcp-servers/mcp-server-form";
import { MCPServerTools } from "@/components/mcp-servers/mcp-server-tools";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Plus, Search, Server, RefreshCw, Filter } from "lucide-react";
import { useLanguage } from "@/lib/language-context";

export default function MCPServersPage() {
  const { t } = useLanguage();
  const {
    servers,
    loading,
    error,
    fetchServers,
    createServer,
    updateServer,
    deleteServer,
    toggleServerStatus,
    testServer,
  } = useMCPServers();

  const [showForm, setShowForm] = useState(false);
  const [editingServer, setEditingServer] = useState<MCPServer | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [toolsDialog, setToolsDialog] = useState<{
    open: boolean;
    serverId: string;
    serverName: string;
  }>({
    open: false,
    serverId: "",
    serverName: "",
  });

  // Filter servers based on search and status
  const filteredServers = servers.filter((server) => {
    const searchableText = [
      server.name,
      server.description,
      server.server_type,
      server.command,
      server.url,
    ]
      .filter(Boolean)
      .join(" ")
      .toLowerCase();

    const matchesSearch = searchableText.includes(searchQuery.toLowerCase());

    const matchesStatus =
      statusFilter === "all" ||
      server.status.toLowerCase() === statusFilter.toLowerCase();

    return matchesSearch && matchesStatus;
  });

  const handleCreateServer = async (data: any) => {
    const result = await createServer(data);
    if (result) {
      setShowForm(false);
    }
  };

  const handleUpdateServer = async (data: any) => {
    if (editingServer) {
      const result = await updateServer(editingServer.id, data);
      if (result) {
        setEditingServer(null);
        setShowForm(false);
      }
    }
  };

  const handleEdit = (server: MCPServer) => {
    setEditingServer(server);
    setShowForm(true);
  };

  const handleViewTools = (serverId: string) => {
    const server = servers.find((s) => s.id === serverId);
    if (server) {
      setToolsDialog({
        open: true,
        serverId,
        serverName: server.name,
      });
    }
  };

  const handleFormCancel = () => {
    setShowForm(false);
    setEditingServer(null);
  };

  const getStatusCounts = () => {
    const counts = {
      total: servers.length,
      ready: servers.filter((s) => s.status === "ACTIVE").length,
      configured: servers.filter((s) => s.status === "INACTIVE").length,
      error: servers.filter((s) => s.status === "ERROR").length,
    };
    return counts;
  };

  const statusCounts = getStatusCounts();

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col lg:flex-row gap-3 xl:items-center xl:justify-between">
        <div className="flex flex-col">
          <h1 className="text-3xl font-bold tracking-tight">MCP Servers</h1>
          <p className="text-gray-600 mt-1">
            {t("mcpServers.description")}
          </p>
        </div>

        <div className="flex items-center gap-3">
          <Button
            variant="outline"
            onClick={() => fetchServers()}
            disabled={loading}
            className="flex items-center gap-2"
          >
            <RefreshCw className={`h-4 w-4 ${loading ? "animate-spin" : ""}`} />
            {t("mcpServers.refresh")}
          </Button>

          <Button
            onClick={() => setShowForm(true)}
            className="flex items-center gap-2"
          >
            <Plus className="h-4 w-4" />
            {t("mcpServers.addServer")}
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 xl:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Server className="h-5 w-5 text-blue-500" />
              <div>
                <p className="text-muted-foreground">{t("mcpServers.totalServers")}</p>
                <p className="text-2xl font-bold">{statusCounts.total}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 rounded-full bg-blue-500"></div>
              <div>
                <p className="text-muted-foreground">{t("mcpServers.ready")}</p>
                <p className="text-2xl font-bold text-blue-600">
                  {statusCounts.ready}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 rounded-full bg-gray-500"></div>
              <div>
                <p className="text-muted-foreground">{t("mcpServers.configured")}</p>
                <p className="text-2xl font-bold text-gray-600">
                  {statusCounts.configured}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 rounded-full bg-red-500"></div>
              <div>
                <p className="text-muted-foreground">{t("mcpServers.errors")}</p>
                <p className="text-2xl font-bold text-red-600">
                  {statusCounts.error}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Search */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder={t("mcpServers.searchPlaceholder")}
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>

            <div className="flex items-center gap-2">
              <Filter className="h-4 w-4 text-gray-500" />
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="border border-gray-300 rounded-md px-3 py-2 text-sm"
              >
                <option value="all">{t("mcpServers.allStatus")}</option>
                <option value="active">{t("mcpServers.active")}</option>
                <option value="inactive">{t("mcpServers.inactive")}</option>
                <option value="error">{t("mcpServers.error")}</option>
                <option value="testing">{t("mcpServers.testing")}</option>
              </select>
            </div>
          </div>

          {searchQuery && (
            <div className="mt-3 flex items-center gap-2">
              <span className="text-sm text-gray-600">
                {t("mcpServers.showingResults", {
                  showing: filteredServers.length,
                  total: servers.length
                })}
              </span>
              {searchQuery && (
                <Badge variant="outline">{t("mcpServers.searchLabel")}: "{searchQuery}"</Badge>
              )}
              {statusFilter !== "all" && (
                <Badge variant="outline">{t("mcpServers.statusLabel")}: {statusFilter}</Badge>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Error State */}
      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="p-4">
            <p className="text-red-700">{t("mcpServers.errorMessage")}: {error}</p>
          </CardContent>
        </Card>
      )}

      {/* Server List */}
      <MCPServerList
        servers={filteredServers}
        onEdit={handleEdit}
        onDelete={deleteServer}
        onTest={testServer}
        onViewTools={handleViewTools}
        onToggleStatus={toggleServerStatus}
        loading={loading}
      />

      {/* Add/Edit Server Dialog */}
      <Dialog open={showForm} onOpenChange={setShowForm}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-auto">
          <MCPServerForm
            server={editingServer || undefined}
            onSubmit={editingServer ? handleUpdateServer : handleCreateServer}
            onCancel={handleFormCancel}
          />
        </DialogContent>
      </Dialog>

      {/* Tools Viewer Dialog */}
      <MCPServerTools
        serverId={toolsDialog.serverId}
        serverName={toolsDialog.serverName}
        open={toolsDialog.open}
        setOpen={setToolsDialog}
      />
    </div>
  );
}
