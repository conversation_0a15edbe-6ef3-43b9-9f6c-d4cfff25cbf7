import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/next-auth";
import db from "@/lib/shared-db";

export async function GET(request: Request) {
  const session = await getServerSession(authOptions);

  if (!session) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const { searchParams } = new URL(request.url);

    // Get tenant filter
    const tenantId = searchParams.get("tenantId");

    // Get date range from query parameters
    let startDate: Date;
    let endDate = new Date();
    let unit = "days"; // Default unit for grouping

    // Check if custom date range is provided
    const fromDate = searchParams.get("from");
    const toDate = searchParams.get("to");

    if (fromDate && toDate) {
      // Use custom date range
      startDate = new Date(fromDate);
      endDate = new Date(toDate);

      // Calculate appropriate grouping unit based on date range
      const daysDiff = Math.ceil(
        (endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)
      );
      unit = daysDiff > 60 ? "months" : "days";
    } else {
      // Use period-based date range
      const period = searchParams.get("period") || "7months";

      // Parse the period
      const match = period.match(/^(\d+)(days|months)$/);
      if (!match) {
        return NextResponse.json(
          {
            error:
              "Invalid period format. Use format like '30days' or '7months'",
          },
          { status: 400 }
        );
      }

      const amount = parseInt(match[1], 10);
      unit = match[2];

      // Calculate the start date
      startDate = new Date();
      if (unit === "days") {
        startDate.setDate(startDate.getDate() - amount);
      } else if (unit === "months") {
        startDate.setMonth(startDate.getMonth() - amount);
      }
    }

    // Base where clause for token usage queries
    const tokenUsageWhere: any = {
      timestamp: {
        gte: startDate,
        lte: endDate,
      },
    };

    // Add tenant filter if provided
    if (tenantId) {
      tokenUsageWhere.tenantId = tenantId;
    }

    // Get token usage data with error handling
    let tokenUsage = [];
    try {
      // First check if the TokenUsage model exists and has records
      const tokenUsageCount = await db.tokenUsage.count();

      if (tokenUsageCount > 0) {
        // Only try to fetch if there are records
        try {
          tokenUsage = await db.tokenUsage.findMany({
            where: tokenUsageWhere,
            orderBy: {
              timestamp: "asc",
            },
            // Select only fields we know are valid
            select: {
              id: true,
              userId: true,
              inputTokens: true,
              outputTokens: true,
              timestamp: true,
              requestType: true,
              modelUsed: true,
              cost: true,
              tenantId: true,
            },
          });
        } catch (innerError) {
          console.error("Error fetching token usage details:", innerError);
          tokenUsage = [];
        }
      } else {
        console.log("No token usage records found");
        tokenUsage = [];
      }
    } catch (error) {
      console.error("Error checking token usage:", error);
      // Provide fallback data
      tokenUsage = [];
    }

    // Group token usage by month or day
    const tokenUsageByPeriod: Record<
      string,
      {
        inputTokens: number;
        outputTokens: number;
        totalTokens: number;
        cost: number;
      }
    > = {};
    const monthNames = [
      "Jan",
      "Feb",
      "Mar",
      "Apr",
      "May",
      "Jun",
      "Jul",
      "Aug",
      "Sep",
      "Oct",
      "Nov",
      "Dec",
    ];

    tokenUsage.forEach((usage: any) => {
      const date = new Date(usage.timestamp);
      let key: string;

      if (unit === "days") {
        key = date.toISOString().split("T")[0]; // YYYY-MM-DD
      } else {
        key = `${monthNames[date.getMonth()]} ${date.getFullYear()}`;
      }

      if (!tokenUsageByPeriod[key]) {
        tokenUsageByPeriod[key] = {
          inputTokens: 0,
          outputTokens: 0,
          totalTokens: 0,
          cost: 0,
        };
      }

      tokenUsageByPeriod[key].inputTokens += usage.inputTokens;
      tokenUsageByPeriod[key].outputTokens += usage.outputTokens;
      tokenUsageByPeriod[key].totalTokens +=
        usage.inputTokens + usage.outputTokens;
      tokenUsageByPeriod[key].cost += usage.cost;
    });

    // Convert to array format for charts
    const tokenUsageData = Object.entries(tokenUsageByPeriod).map(
      ([name, data]) => ({
        name,
        inputTokens: (data as any).inputTokens,
        outputTokens: (data as any).outputTokens,
        totalTokens: (data as any).totalTokens,
        cost: (data as any).cost,
        value: (data as any).totalTokens, // For backward compatibility
      })
    );

    // Base where clause for storage usage queries
    const storageUsageWhere: any = {
      timestamp: {
        gte: startDate,
        lte: endDate,
      },
    };

    // Add tenant filter if provided
    if (tenantId) {
      storageUsageWhere.tenantId = tenantId;
    }

    // Get storage usage data with error handling
    let storageUsage = [];
    try {
      storageUsage = await db.vectorStoreUsage.findMany({
        where: storageUsageWhere,
        orderBy: {
          timestamp: "asc",
        },
      });
    } catch (error) {
      console.error("Error fetching storage usage:", error);
      // Provide fallback data
      storageUsage = [];
    }

    // Group storage usage by month or day
    const storageUsageByPeriod: Record<string, number> = {};

    storageUsage.forEach((usage: any) => {
      const date = new Date(usage.timestamp);
      let key: string;

      if (unit === "days") {
        key = date.toISOString().split("T")[0]; // YYYY-MM-DD
      } else {
        key = `${monthNames[date.getMonth()]} ${date.getFullYear()}`;
      }

      if (!storageUsageByPeriod[key]) {
        storageUsageByPeriod[key] = 0;
      }

      storageUsageByPeriod[key] += usage.usageGB;
    });

    // Convert to array format for charts
    const storageUsageData = Object.entries(storageUsageByPeriod).map(
      ([name, value]) => ({
        name,
        value,
      })
    );

    // Base where clause for API usage queries
    const apiUsageWhere: any = {
      timestamp: {
        gte: startDate,
        lte: endDate,
      },
    };

    // Add tenant filter if provided
    if (tenantId) {
      apiUsageWhere.tenantId = tenantId;
    }

    // Get API usage data with error handling
    let apiUsage = [];
    try {
      // First check if the APIRequest model exists and has records
      const apiUsageCount = await db.aPIRequest.count();

      if (apiUsageCount > 0) {
        // Only try to fetch if there are records
        try {
          apiUsage = await db.aPIRequest.findMany({
            where: apiUsageWhere,
            orderBy: {
              timestamp: "asc",
            },
            // Select only fields we know are valid
            select: {
              id: true,
              userId: true,
              endpoint: true,
              method: true,
              statusCode: true,
              timestamp: true,
              duration: true,
              success: true,
              errorMessage: true,
              tenantId: true,
            },
          });
        } catch (innerError) {
          console.error("Error fetching API usage details:", innerError);
          apiUsage = [];
        }
      } else {
        console.log("No API usage records found");
        apiUsage = [];
      }
    } catch (error) {
      console.error("Error checking API usage:", error);
      // Provide fallback data
      apiUsage = [];
    }

    // Group API usage by month or day
    const apiUsageByPeriod: Record<
      string,
      {
        total: number;
        success: number;
        failed: number;
        avgLatency: number;
        totalLatency: number;
      }
    > = {};

    apiUsage.forEach((usage: any) => {
      const date = new Date(usage.timestamp);
      let key: string;

      if (unit === "days") {
        key = date.toISOString().split("T")[0]; // YYYY-MM-DD
      } else {
        key = `${monthNames[date.getMonth()]} ${date.getFullYear()}`;
      }

      if (!apiUsageByPeriod[key]) {
        apiUsageByPeriod[key] = {
          total: 0,
          success: 0,
          failed: 0,
          avgLatency: 0,
          totalLatency: 0,
        };
      }

      apiUsageByPeriod[key].total += 1;
      apiUsageByPeriod[key].success += usage.success ? 1 : 0;
      apiUsageByPeriod[key].failed += usage.success ? 0 : 1;
      apiUsageByPeriod[key].totalLatency += usage.duration || 0; // Use duration instead of latencyMs
      apiUsageByPeriod[key].avgLatency =
        apiUsageByPeriod[key].totalLatency / apiUsageByPeriod[key].total;
    });

    // Convert to array format for charts
    const apiUsageData = Object.entries(apiUsageByPeriod).map(
      ([name, data]) => ({
        name,
        total: (data as any).total,
        success: (data as any).success,
        failed: (data as any).failed,
        avgLatency: (data as any).avgLatency,
        value: (data as any).total, // For backward compatibility
      })
    );

    return NextResponse.json({
      tokenUsage: tokenUsageData,
      storageUsage: storageUsageData,
      apiUsage: apiUsageData,
    });
  } catch (error) {
    console.error("Error fetching dashboard usage:", error);
    return NextResponse.json(
      { error: "Failed to fetch dashboard usage" },
      { status: 500 }
    );
  }
}
