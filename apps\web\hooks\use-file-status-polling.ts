import { useState, useEffect, useCallback, useRef } from 'react';

interface FileStatusPollingProps {
  files: any[];
  onFileStatusUpdate: (fileId: string, newStatus: string, vectorizedAt?: string) => void;
  pollingInterval?: number;
  enabled?: boolean;
}

interface FileStatusResponse {
  id: string;
  vectorizationStatus: string;
  vectorizedAt?: string;
}

export const useFileStatusPolling = ({
  files,
  onFileStatusUpdate,
  pollingInterval = 5000, // 5 seconds
  enabled = true,
}: FileStatusPollingProps) => {
  const [isPolling, setIsPolling] = useState(false);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const abortControllerRef = useRef<AbortController | null>(null);

  // Get files that need polling (PROCESSING status)
  const getProcessingFiles = useCallback(() => {
    return files.filter(file => file.vectorizationStatus === 'PROCESSING');
  }, [files]);

  // Check status for a single file
  const checkFileStatus = useCallback(async (fileId: string): Promise<FileStatusResponse | null> => {
    try {
      const response = await fetch(`/api/files/${fileId}/status`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        signal: abortControllerRef.current?.signal,
      });

      if (!response.ok) {
        console.warn(`Failed to fetch status for file ${fileId}: ${response.status}`);
        return null;
      }

      const data = await response.json();
      return {
        id: fileId,
        vectorizationStatus: data.vectorizationStatus,
        vectorizedAt: data.vectorizedAt,
      };
    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        // Request was aborted, this is expected
        return null;
      }
      console.error(`Error checking status for file ${fileId}:`, error);
      return null;
    }
  }, []);

  // Poll all processing files
  const pollFileStatuses = useCallback(async () => {
    const processingFiles = getProcessingFiles();
    
    if (processingFiles.length === 0) {
      setIsPolling(false);
      return;
    }

    setIsPolling(true);

    // Create new abort controller for this polling cycle
    abortControllerRef.current = new AbortController();

    try {
      const statusPromises = processingFiles.map(file => checkFileStatus(file.id));
      const results = await Promise.allSettled(statusPromises);

      results.forEach((result, index) => {
        if (result.status === 'fulfilled' && result.value) {
          const fileStatus = result.value;
          const originalFile = processingFiles[index];
          
          // Only update if status has changed
          if (originalFile.vectorizationStatus !== fileStatus.vectorizationStatus) {
            onFileStatusUpdate(
              fileStatus.id,
              fileStatus.vectorizationStatus,
              fileStatus.vectorizedAt
            );
          }
        }
      });
    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        // Request was aborted, this is expected
        return;
      }
      console.error('Error polling file statuses:', error);
    } finally {
      setIsPolling(false);
    }
  }, [getProcessingFiles, checkFileStatus, onFileStatusUpdate]);

  // Start/stop polling based on processing files
  useEffect(() => {
    if (!enabled) {
      // Clear any existing interval
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
      // Abort any ongoing requests
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      setIsPolling(false);
      return;
    }

    const processingFiles = getProcessingFiles();
    
    if (processingFiles.length > 0) {
      // Start polling immediately
      pollFileStatuses();
      
      // Set up interval for continued polling
      intervalRef.current = setInterval(pollFileStatuses, pollingInterval);
    } else {
      // No files to poll, clear interval
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
      setIsPolling(false);
    }

    // Cleanup function
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, [enabled, getProcessingFiles, pollFileStatuses, pollingInterval]);

  // Manual refresh function
  const refreshStatuses = useCallback(() => {
    if (enabled) {
      pollFileStatuses();
    }
  }, [enabled, pollFileStatuses]);

  return {
    isPolling,
    refreshStatuses,
    processingFilesCount: getProcessingFiles().length,
  };
};
