"use client";

import { useState, useEffect } from "react";
import { getCookie } from "@/utils/cookies";
import { useRouter } from "next/navigation";
import { createFile } from "@/services";
import toast from "react-hot-toast";

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Loader2, Globe, AlertCircle, Check, Shield } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { useLanguage } from "@/lib/language-context";
import { getAuthToken } from "@/lib/api/auth-token";
import { ParallelProcessor } from "@/lib/utils/parallel-processor";

interface UrlImportDialogProps {
  open: boolean; // Required by Dialog component
  // eslint-disable-next-line no-unused-vars
  onOpenChange: (open: boolean) => void; // Required by Dialog component
  workspaceSlug: string;
  workspaceId: string;
  pageId: string;
  folderId?: string;
  onSuccess?: () => void;
  // eslint-disable-next-line no-unused-vars
  onFileImported?: (file: any) => void; // Callback for when file is imported
}

export function UrlImportDialog({
  open,
  onOpenChange,
  workspaceSlug,
  workspaceId,
  pageId,
  folderId,
  onSuccess,
  onFileImported,
}: UrlImportDialogProps) {
  const { t } = useLanguage();
  const router = useRouter();
  const userId = getCookie("userId") || "";
  const tenantId = getCookie("currentOrganizationId") || "";

  const [url, setUrl] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState("");
  const [isRobotsError, setIsRobotsError] = useState(false);
  const [isDuplicate, setIsDuplicate] = useState(false);
  const [hasSitemap, setHasSitemap] = useState(false);
  const [sitemapUrls, setSitemapUrls] = useState<string[]>([]);
  const [selectedSitemapUrls, setSelectedSitemapUrls] = useState<string[]>([]);
  const [showSitemapSelector, setShowSitemapSelector] = useState(false);
  const [isBatchImporting, setIsBatchImporting] = useState(false);
  const [batchProgress, setBatchProgress] = useState<{
    total: number;
    processed: number;
    successful: number;
    failed: number;
    currentBatch: string[];
  } | null>(null);

  // Import options
  const [contentCleaningLevel, setContentCleaningLevel] = useState<
    "basic" | "medium" | "aggressive"
  >("basic");
  const [crawlDepth, setCrawlDepth] = useState(1);
  const [format, setFormat] = useState<"html" | "markdown">("html");
  const [batchConcurrency, setBatchConcurrency] = useState(3);

  // Content state
  const [title, setTitle] = useState("");
  const [description, setDescription] = useState("");
  const [content, setContent] = useState("");

  // Reset state when dialog opens/closes
  useEffect(() => {
    if (!open) {
      setUrl("");
      setTitle("");
      setDescription("");
      setContent("");
      setError("");
      setIsRobotsError(false);
      setIsLoading(false);
      setIsSubmitting(false);
      setIsDuplicate(false);
      setHasSitemap(false);
      setSitemapUrls([]);
      setSelectedSitemapUrls([]);
      setShowSitemapSelector(false);
      setIsBatchImporting(false);
      setContentCleaningLevel("basic");
      setCrawlDepth(1);
      setFormat("html");
      setBatchConcurrency(3);
    }
  }, [open]);

  // Check for duplicate URL
  useEffect(() => {
    const checkDuplicate = async () => {
      if (!url || !workspaceId) return;

      try {
        const response = await fetch("/api/url-import", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "x-user-id": userId,
            "x-tenant-id": tenantId,
          },
          body: JSON.stringify({
            url,
            action: "check-duplicate",
            workspaceId,
          }),
        });

        if (response.ok) {
          const data = await response.json();
          setIsDuplicate(data.isDuplicate);
        }
      } catch (error) {
        console.error("Error checking duplicate URL:", error);
      }
    };

    checkDuplicate();
  }, [url, workspaceId, userId, tenantId]);

  const handleFetchUrl = async () => {
    if (!url) {
      setError(t("common.urlRequired"));
      return;
    }

    setIsLoading(true);
    setError("");
    setIsRobotsError(false);

    try {
      // Fetch the URL content directly
      const response = await fetch("/api/url-import", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "x-user-id": userId,
          "x-tenant-id": tenantId,
        },
        body: JSON.stringify({
          url,
          action: "fetch",
          options: {
            contentCleaningLevel,
            format,
            crawlDepth,
          },
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to fetch URL");
      }

      const data = await response.json();

      setTitle(data.title);
      setDescription(data.description || "");
      setContent(data.content);
      setHasSitemap(data.hasSitemap || data.has_sitemap);

      // Handle additional URLs from crawl depth
      if (data.additional_urls && data.additional_urls.length > 0) {
        setSitemapUrls(data.additional_urls);
        setShowSitemapSelector(true);

        // Show success message for deep crawling
        if (crawlDepth > 1) {
          toast.success(
            `Found ${data.additional_urls.length} additional pages to import`,
            { duration: 3000 }
          );
        }
      } else if (data.hasSitemap || data.has_sitemap) {
        // Fallback to fetching sitemap if no additional URLs provided
        const sitemapUrl = data.sitemapUrl || data.sitemap_url;
        if (sitemapUrl) {
          fetchSitemap(sitemapUrl);

          // Show sitemap selector if sitemap is available and crawl depth > 1
          if (crawlDepth > 1) {
            setShowSitemapSelector(true);
          }
        }
      }
    } catch (error) {
      console.error("Error fetching URL:", error);
      const errorMessage = (error as Error).message || "Failed to fetch URL";

      // Check for specific robots.txt error
      if (errorMessage.includes("URL is blocked by robots.txt")) {
        const robotsErrorMessage = t("urlImport.robotsBlockedMessage");
        setError(robotsErrorMessage);
        setIsRobotsError(true);

        // Also show a toast notification for better visibility
        toast.error(t("urlImport.robotsBlocked"));
      } else {
        setError(errorMessage);
        setIsRobotsError(false);
      }
    } finally {
      setIsLoading(false);
    }
  };

  const fetchSitemap = async (sitemapUrl: string) => {
    try {
      const response = await fetch("/api/url-import", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "x-user-id": userId,
          "x-tenant-id": tenantId,
        },
        body: JSON.stringify({
          url: sitemapUrl,
          action: "sitemap",
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to fetch sitemap");
      }

      const data = await response.json();
      setSitemapUrls(data.urls || []);
    } catch (error) {
      console.error("Error fetching sitemap:", error);
    }
  };

  const handleSave = async () => {
    if (!title || !content) {
      setError(t("common.titleAndContentRequired"));
      return;
    }

    setIsSubmitting(true);

    try {
      // Determine file type and extension based on format
      const fileType = format === "markdown" ? "text/markdown" : "text/html";
      const fileExtension = format === "markdown" ? "md" : "html";

      const file = new Blob([content], { type: fileType });
      const formData = new FormData();
      formData.append("file", file);
      formData.append("tenantId", tenantId);
      formData.append("workspaceSlug", workspaceSlug);
      formData.append("pageId", pageId);

      const uploadResponse = await fetch("/api/upload", {
        method: "POST",
        body: formData,
        headers: {
          "x-user-id": userId,
          "x-tenant-id": tenantId,
          "x-workspace-slug": workspaceSlug,
        },
      });
      const uploadResponseData = await uploadResponse.json();

      // Create file with the imported content
      const fileData = {
        name: title,
        type: fileType,
        extension: fileExtension,
        content,
        workspaceSlug,
        pageId,
        parentId: pageId ?? folderId,
        folderId,
        url: uploadResponseData.url,
        metadata: {
          sourceUrl: url,
          description: description,
          importedAt: new Date().toISOString(),
          contentCleaningLevel,
          format,
        },
        vectorizationStatus: "PENDING", // Set initial status to PENDING
      };

      const result = await createFile(
        fileData,
        tenantId,
        userId,
        workspaceSlug
      );

      if (result.error) {
        throw new Error(result.error);
      }

      // Start vectorization process
      try {
        // Get the file ID from the result
        const fileId = result.data?.[0]?.id || result.file?.id;
        const fileUrl = result.data?.[0]?.url || result.file?.url;

        if (fileId && fileUrl) {
          // Try Python backend indexing first, fallback to existing service
          // try {
          //   const pythonApiUrl =
          //     process.env.NEXT_PUBLIC_API_URL || "http://localhost:8000";
          //   const indexResponse = await fetch(
          //     `${pythonApiUrl}/api/v1/url-import/index`,
          //     {
          //       method: "POST",
          //       headers: {
          //         "Content-Type": "application/json",
          //         Authorization: `Bearer ${await getAuthToken()}`,
          //         "X-Tenant-ID": tenantId,
          //       },
          //       body: JSON.stringify({
          //         file_id: fileId,
          //         workspace_slug: workspaceSlug,
          //         document_path: fileUrl,
          //         document_type: format === "markdown" ? "text" : "html",
          //         metadata: {
          //           sourceUrl: url,
          //           description: description,
          //           importedAt: new Date().toISOString(),
          //           contentCleaningLevel,
          //           format,
          //         },
          //       }),
          //     }
          //   );

          //   if (!indexResponse.ok) {
          //     throw new Error(
          //       `Python indexing failed: ${indexResponse.status}`
          //     );
          //   }
          // } catch (pythonError) {
          //   console.warn(
          //     "Python backend indexing failed, falling back to existing service:",
          //     pythonError
          //   );

          //   // Fallback to existing workspaceChatService
          //   const { workspaceChatService } = await import(
          //     "@/services/workspace-chat"
          //   );

          //   await workspaceChatService.uploadForIndexing({
          //     userId,
          //     document_path: fileUrl,
          //     workspaceSlug,
          //     tenantId,
          //     file_id: fileId,
          //     documentType: format === "markdown" ? "text" : "html",
          //     metadata: {
          //       sourceUrl: url,
          //       description: description,
          //       importedAt: new Date().toISOString(),
          //       contentCleaningLevel,
          //       format,
          //     },
          //   });
          // }

          // Show success message with vectorization info
          toast.success(t("workspace.urlImportAndVectorizationStarted"));
        } else {
          // Show regular success message if file ID is not available
          toast.success(t("workspace.urlImportSuccess"));
        }
      } catch (vectorizationError) {
        console.error("Error starting vectorization:", vectorizationError);
        // Still consider the import successful even if vectorization fails
        toast.success(t("workspace.urlImportSuccessButVectorizationFailed"));
      }

      onOpenChange(false);

      if (onSuccess) {
        onSuccess();
      }

      // Call the file imported callback to update UI
      if (onFileImported && result.data?.[0]) {
        onFileImported(result.data[0]);
      }

      // Fallback to page refresh if no callback provided
      if (!onFileImported) {
        router.refresh();
      }
    } catch (error) {
      console.error("Error saving imported content:", error);
      setError((error as Error).message || "Failed to save imported content");
      toast.error(t("workspace.urlImportError"));
    } finally {
      setIsSubmitting(false);
    }
  };

  // Helper function to process a single URL with retry logic
  const processSingleUrl = async (
    batchUrl: string,
    retryCount = 0
  ): Promise<{ success: boolean; url: string; error?: string }> => {
    const maxRetries = 2;

    try {
      // Fetch content for each URL
      const response = await fetch("/api/url-import", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "x-user-id": userId,
          "x-tenant-id": tenantId,
        },
        body: JSON.stringify({
          url: batchUrl,
          action: "fetch",
          options: {
            contentCleaningLevel,
            format,
            crawlDepth,
          },
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        const errorMessage = errorData.error || "Failed to fetch URL";

        // Log specific robots.txt errors for batch import
        if (errorMessage.includes("URL is blocked by robots.txt")) {
          console.warn(`Skipping ${batchUrl}: blocked by robots.txt`);
          return {
            success: false,
            url: batchUrl,
            error: "Blocked by robots.txt",
          };
        } else {
          throw new Error(errorMessage);
        }
      }

      const data = await response.json();

      // Determine file type and extension based on format
      const fileType = format === "markdown" ? "text/markdown" : "text/html";
      const fileExtension = format === "markdown" ? "md" : "html";

      const file = new Blob([data.content], { type: fileType });
      const formData = new FormData();
      formData.append("file", file);
      formData.append("tenantId", tenantId);
      formData.append("workspaceSlug", workspaceSlug);
      formData.append("pageId", pageId);

      const uploadResponse = await fetch("/api/upload", {
        method: "POST",
        body: formData,
        headers: {
          "x-user-id": userId,
          "x-tenant-id": tenantId,
          "x-workspace-slug": workspaceSlug,
        },
      });
      const uploadResponseData = await uploadResponse.json();

      // Create file for each URL
      const fileData = {
        name: data.title || `Imported from ${batchUrl}`,
        type: fileType,
        extension: fileExtension,
        content: data.content,
        workspaceSlug,
        pageId,
        parentId: pageId ?? folderId,
        folderId,
        url: uploadResponseData.url,
        metadata: {
          sourceUrl: batchUrl,
          description: data.description || "",
          importedAt: new Date().toISOString(),
          batchImported: true,
          parentUrl: url,
          contentCleaningLevel,
          format,
        },
        vectorizationStatus: "PENDING", // Set initial status to PENDING
      };

      const result = await createFile(
        fileData,
        tenantId,
        userId,
        workspaceSlug
      );

      if (result.error) {
        throw new Error(result.error);
      }

      // Start vectorization process for the file
      try {
        // Get the file ID from the result
        const fileId = result.data?.[0]?.id || result.file?.id;
        const fileUrl = result.data?.[0]?.url || result.file?.url;

        if (fileId && fileUrl) {
          // Try Python backend indexing first, fallback to existing service
          // try {
          //   const pythonApiUrl =
          //     process.env.NEXT_PUBLIC_API_URL || "http://localhost:8000";
          //   const indexResponse = await fetch(
          //     `${pythonApiUrl}/api/v1/url-import/index`,
          //     {
          //       method: "POST",
          //       headers: {
          //         "Content-Type": "application/json",
          //         Authorization: `Bearer ${await getAuthToken()}`,
          //         "X-Tenant-ID": tenantId,
          //       },
          //       body: JSON.stringify({
          //         file_id: fileId,
          //         workspace_slug: workspaceSlug,
          //         document_path: fileUrl,
          //         document_type: format === "markdown" ? "text" : "html",
          //         metadata: {
          //           sourceUrl: batchUrl,
          //           description: data.description || "",
          //           importedAt: new Date().toISOString(),
          //           batchImported: true,
          //           parentUrl: url,
          //           contentCleaningLevel,
          //           format,
          //         },
          //       }),
          //     }
          //   );
          //   if (!indexResponse.ok) {
          //     throw new Error(
          //       `Python indexing failed: ${indexResponse.status}`
          //     );
          //   }
          // } catch (pythonError) {
          //   console.warn(
          //     `Python backend indexing failed for ${batchUrl}, falling back to existing service:`,
          //     pythonError
          //   );
          //   // Fallback to existing workspaceChatService
          //   const { workspaceChatService } = await import(
          //     "@/services/workspace-chat"
          //   );
          //   await workspaceChatService.uploadForIndexing({
          //     userId,
          //     document_path: fileUrl,
          //     workspaceSlug,
          //     tenantId,
          //     file_id: fileId,
          //     documentType: format === "markdown" ? "text" : "html",
          //     metadata: {
          //       sourceUrl: batchUrl,
          //       description: data.description || "",
          //       importedAt: new Date().toISOString(),
          //       batchImported: true,
          //       parentUrl: url,
          //       contentCleaningLevel,
          //       format,
          //     },
          //   });
          // }
        }
      } catch (vectorizationError) {
        console.error(
          `Error starting vectorization for ${batchUrl}:`,
          vectorizationError
        );
        // Continue with the batch import even if vectorization fails for some files
      }

      return { success: true, url: batchUrl };
    } catch (error) {
      console.error(
        `Error importing ${batchUrl} (attempt ${retryCount + 1}):`,
        error
      );

      // Retry logic with exponential backoff
      if (retryCount < maxRetries) {
        const delay = Math.pow(2, retryCount) * 1000; // 1s, 2s, 4s
        await new Promise((resolve) => setTimeout(resolve, delay));
        return processSingleUrl(batchUrl, retryCount + 1);
      }

      return {
        success: false,
        url: batchUrl,
        error: error.message || "Unknown error",
      };
    }
  };

  const handleBatchImport = async () => {
    if (selectedSitemapUrls.length === 0) {
      setError(t("workspace.noUrlsSelected"));
      return;
    }

    setIsBatchImporting(true);
    setError("");

    try {
      // Initialize progress tracking
      setBatchProgress({
        total: selectedSitemapUrls.length,
        processed: 0,
        successful: 0,
        failed: 0,
        currentBatch: [],
      });

      // Create parallel processor with optimized settings
      const processor = new ParallelProcessor<
        string,
        { success: boolean; url: string; error?: string }
      >({
        concurrency: batchConcurrency, // Configurable concurrency to balance speed and server load
        maxRetries: 2,
        retryDelay: 1000,
        batchDelay: 500,
        onProgress: (progress) => {
          setBatchProgress({
            total: progress.total,
            processed: progress.processed,
            successful: progress.successful,
            failed: progress.failed,
            currentBatch: progress.currentBatch,
          });
        },
        onItemError: (url, error, attempt) => {
          console.warn(
            `Failed to import ${url} (attempt ${attempt}):`,
            error.message
          );
        },
      });

      // Process all URLs using the parallel processor
      const results = await processor.processAll(
        selectedSitemapUrls,
        async (batchUrl) => {
          const result = await processSingleUrl(batchUrl);
          if (!result.success) {
            throw new Error(result.error || "Unknown error");
          }
          return result;
        }
      );

      // Calculate final counts
      const successCount = results.filter((r) => r.success).length;
      const failureCount = results.filter((r) => !r.success).length;

      if (successCount > 0) {
        toast.success(
          t("workspace.batchImportAndVectorizationSuccess", {
            count: successCount,
          })
        );

        if (failureCount > 0) {
          toast.error(
            t("workspace.batchImportPartialFailure", {
              count: failureCount,
            })
          );
        }

        onOpenChange(false);

        if (onSuccess) {
          onSuccess();
        }

        // Fallback to page refresh for batch import (multiple files)
        router.refresh();
      } else {
        toast.error(t("workspace.batchImportFailure"));
      }
    } catch (error) {
      console.error("Error during batch import:", error);
      setError((error as Error).message || "Failed to perform batch import");
      toast.error(t("workspace.batchImportError"));
    } finally {
      setIsBatchImporting(false);
      setBatchProgress(null);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{t("workspace.importFromUrl")}</DialogTitle>
          <DialogDescription>
            {t("workspace.importFromUrlDescription")}
          </DialogDescription>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          <div className="flex flex-col gap-4">
            <div className="flex items-end gap-2">
              <div className="flex-1">
                <Input
                  placeholder="https://example.com"
                  value={url}
                  onChange={(e) => setUrl(e.target.value)}
                  disabled={isLoading}
                />
              </div>
              <Button onClick={handleFetchUrl} disabled={isLoading}>
                {isLoading ? (
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                ) : (
                  <Globe className="h-4 w-4 mr-2" />
                )}
                {isLoading
                  ? crawlDepth > 1
                    ? t("workspace.fetchingAndCrawling")
                    : t("common.fetching")
                  : t("common.fetch")}
              </Button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <label
                  htmlFor="contentCleaningLevel"
                  className="text-sm font-medium"
                >
                  {t("workspace.contentCleaningLevel")}
                </label>
                <select
                  id="contentCleaningLevel"
                  value={contentCleaningLevel}
                  onChange={(e) =>
                    setContentCleaningLevel(
                      e.target.value as "basic" | "medium" | "aggressive"
                    )
                  }
                  className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                >
                  <option value="basic">
                    {t("workspace.cleaningLevelBasic")}
                  </option>
                  <option value="medium">
                    {t("workspace.cleaningLevelMedium")}
                  </option>
                  <option value="aggressive">
                    {t("workspace.cleaningLevelAggressive")}
                  </option>
                </select>
              </div>

              <div className="space-y-2">
                <label htmlFor="format" className="text-sm font-medium">
                  {t("workspace.importFormat")}
                </label>
                <select
                  id="format"
                  value={format}
                  onChange={(e) =>
                    setFormat(e.target.value as "html" | "markdown")
                  }
                  className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                >
                  <option value="html">HTML</option>
                  <option value="markdown">Markdown</option>
                </select>
              </div>

              <div className="space-y-2">
                <label htmlFor="crawlDepth" className="text-sm font-medium">
                  {t("workspace.crawlDepth")}
                </label>
                <select
                  id="crawlDepth"
                  value={crawlDepth}
                  onChange={(e) => setCrawlDepth(parseInt(e.target.value))}
                  className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                >
                  <option value="1">{t("workspace.crawlDepth1")}</option>
                  <option value="2">{t("workspace.crawlDepth2")}</option>
                  <option value="3">{t("workspace.crawlDepth3")}</option>
                </select>
              </div>

              <div className="space-y-2">
                <label
                  htmlFor="batchConcurrency"
                  className="text-sm font-medium"
                >
                  Batch Concurrency
                </label>
                <select
                  id="batchConcurrency"
                  value={batchConcurrency}
                  onChange={(e) =>
                    setBatchConcurrency(parseInt(e.target.value))
                  }
                  className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                >
                  <option value="1">1 (Slow, Safe)</option>
                  <option value="2">2 (Balanced)</option>
                  <option value="3">3 (Default)</option>
                  <option value="5">5 (Fast)</option>
                  <option value="8">8 (Very Fast)</option>
                </select>
              </div>
            </div>
          </div>

          {error && (
            <Alert variant="destructive">
              {isRobotsError ? (
                <Shield className="h-4 w-4" />
              ) : (
                <AlertCircle className="h-4 w-4" />
              )}
              <AlertTitle>
                {isRobotsError ? t("urlImport.robotsBlockedTitle") : t("common.error")}
              </AlertTitle>
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {isDuplicate && (
            <Alert className="bg-yellow-50 border-yellow-200 dark:bg-yellow-900/20 dark:border-yellow-800">
              <AlertCircle className="h-4 w-4 text-yellow-600 dark:text-yellow-400" />
              <AlertTitle>{t("common.warning")}</AlertTitle>
              <AlertDescription>
                {t("workspace.urlAlreadyImported")}
              </AlertDescription>
            </Alert>
          )}

          {/* Sitemap selector */}
          {hasSitemap && showSitemapSelector && sitemapUrls.length > 0 && (
            <div className="border rounded-md p-4">
              <h3 className="text-lg font-medium mb-2">
                {t("workspace.sitemapUrls")}
              </h3>
              <p className="text-sm text-muted-foreground mb-4">
                {t("workspace.selectUrlsToImport")}
              </p>

              <div className="space-y-2 max-h-[200px] overflow-y-auto">
                {sitemapUrls.map((sitemapUrl, index) => (
                  <div key={index} className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      id={`sitemap-url-${index}`}
                      checked={selectedSitemapUrls.includes(sitemapUrl)}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setSelectedSitemapUrls([
                            ...selectedSitemapUrls,
                            sitemapUrl,
                          ]);
                        } else {
                          setSelectedSitemapUrls(
                            selectedSitemapUrls.filter(
                              (url) => url !== sitemapUrl
                            )
                          );
                        }
                      }}
                      className="rounded"
                    />
                    <label
                      htmlFor={`sitemap-url-${index}`}
                      className="text-sm truncate"
                    >
                      {sitemapUrl}
                    </label>
                  </div>
                ))}
              </div>

              <div className="mt-4 flex justify-between">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setSelectedSitemapUrls([])}
                >
                  {t("common.deselectAll")}
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setSelectedSitemapUrls([...sitemapUrls])}
                >
                  {t("common.selectAll")}
                </Button>
              </div>

              <div className="mt-4">
                <Button
                  onClick={handleBatchImport}
                  disabled={
                    isBatchImporting || selectedSitemapUrls.length === 0
                  }
                  className="w-full"
                >
                  {isBatchImporting ? (
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  ) : null}
                  {t("workspace.batchImport", {
                    count: selectedSitemapUrls.length,
                  })}
                </Button>
              </div>

              {/* Progress display */}
              {batchProgress && (
                <div className="mt-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-md">
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-sm font-medium">
                      {t("workspace.batchImportProgress")}
                    </span>
                    <span className="text-sm text-muted-foreground">
                      {batchProgress.processed} / {batchProgress.total}
                    </span>
                  </div>

                  {/* Progress bar */}
                  <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mb-3">
                    <div
                      className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                      style={{
                        width: `${
                          (batchProgress.processed / batchProgress.total) * 100
                        }%`,
                      }}
                    />
                  </div>

                  {/* Stats */}
                  <div className="flex justify-between text-xs text-muted-foreground">
                    <span className="text-green-600 dark:text-green-400">
                      ✓ {batchProgress.successful} successful
                    </span>
                    {batchProgress.failed > 0 && (
                      <span className="text-red-600 dark:text-red-400">
                        ✗ {batchProgress.failed} failed
                      </span>
                    )}
                  </div>

                  {/* Current batch */}
                  {batchProgress.currentBatch.length > 0 && (
                    <div className="mt-2">
                      <p className="text-xs text-muted-foreground mb-1">
                        Currently processing:
                      </p>
                      <div className="space-y-1">
                        {batchProgress.currentBatch.map((url, index) => (
                          <div key={index} className="flex items-center gap-2">
                            <Loader2 className="h-3 w-3 animate-spin" />
                            <span className="text-xs truncate">{url}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
          )}

          {content && (
            <div className="space-y-4">
              <div>
                <label
                  htmlFor="title"
                  className="block text-sm font-medium mb-1"
                >
                  {t("common.title")}
                </label>
                <Input
                  id="title"
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                />
              </div>
              {description && (
                <div>
                  <label
                    htmlFor="description"
                    className="block text-sm font-medium mb-1"
                  >
                    {t("common.description")}
                  </label>
                  <Input
                    id="description"
                    value={description}
                    onChange={(e) => setDescription(e.target.value)}
                    placeholder={t("workspace.descriptionPlaceholder", {
                      defaultValue: "Brief description of the content",
                    })}
                  />
                </div>
              )}
              <div className="text-sm text-muted-foreground">
                {format === "markdown"
                  ? t("workspace.contentImportedAsMarkdown")
                  : t("workspace.contentImportedAsHtml")}
              </div>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            {t("common.cancel")}
          </Button>

          <Button
            onClick={handleSave}
            disabled={isSubmitting || !content || !title}
          >
            {isSubmitting ? (
              <Loader2 className="h-4 w-4 animate-spin mr-2" />
            ) : (
              <Check className="h-4 w-4 mr-2" />
            )}
            {t("common.save")}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
