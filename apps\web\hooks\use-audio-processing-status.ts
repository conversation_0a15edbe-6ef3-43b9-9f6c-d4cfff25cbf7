import { useState, useEffect, useCallback, useRef } from 'react';
import { copilotKitChatService } from '@/services/copilotkit-chat';

interface AudioProcessingStatus {
  jobId: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  progress?: number;
  message?: string;
  results?: any;
  error?: string;
}

interface UseAudioProcessingStatusProps {
  jobId: string | null;
  enabled?: boolean;
  pollingInterval?: number;
  onComplete?: (results: any) => void;
  onError?: (error: string) => void;
}

export function useAudioProcessingStatus({
  jobId,
  enabled = true,
  pollingInterval = 60000, // 60 seconds - reduced from 30s to reduce server load
  onComplete,
  onError,
}: UseAudioProcessingStatusProps) {
  const [status, setStatus] = useState<AudioProcessingStatus | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Use refs to store callbacks to prevent unnecessary re-renders
  const onCompleteRef = useRef(onComplete);
  const onErrorRef = useRef(onError);

  // Update refs when callbacks change
  useEffect(() => {
    onCompleteRef.current = onComplete;
  }, [onComplete]);

  useEffect(() => {
    onErrorRef.current = onError;
  }, [onError]);

  const fetchStatus = useCallback(async () => {
    if (!jobId || !enabled) return;

    try {
      setIsLoading(true);
      setError(null);

      const response = await copilotKitChatService.getAudioAnalysisStatus(jobId);

      if (response.error_message) {
        setError(response.error_message);
        onErrorRef.current?.(response.error_message);
        return;
      }

      setStatus(response);

      // Handle completion
      if (response.status === 'completed') {
        onCompleteRef.current?.(response.audio_context);
      } else if (response.status === 'failed') {
        const errorMsg = response.error_message || 'Audio processing failed';
        setError(errorMsg);
        onErrorRef.current?.(errorMsg);
      }
    } catch (err) {
      const errorMsg = err instanceof Error ? err.message : 'Failed to fetch audio processing status';
      setError(errorMsg);
      onErrorRef.current?.(errorMsg);
    } finally {
      setIsLoading(false);
    }
  }, [jobId, enabled]); // Removed onComplete and onError from dependencies

  // Initial fetch
  useEffect(() => {
    if (jobId && enabled) {
      fetchStatus();
    }
  }, [jobId, enabled, fetchStatus]);

  // Polling
  useEffect(() => {
    if (!jobId || !enabled || !status) return;

    // Don't poll if already completed or failed
    if (status.status === 'completed' || status.status === 'failed') {
      return;
    }

    const interval = setInterval(fetchStatus, pollingInterval);
    return () => clearInterval(interval);
  }, [jobId, enabled, status, pollingInterval, fetchStatus]);

  return {
    status,
    isLoading,
    error,
    refetch: fetchStatus,
  };
}
