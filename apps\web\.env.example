# Database
DATABASE_URL="********************************:port/database?options"

# Next Auth
NEXTAUTH_SECRET="your-secret-key"
NEXTAUTH_URL="http://localhost:3000"

# Email (CampedMailer)
CAMPED_MAILER_API_KEY="your-campedmailer-api-key"
EMAIL_FROM="<EMAIL>"

# Stripe
STRIPE_PUBLISHABLE_KEY="pk_test_your_publishable_key"
STRIPE_SECRET_KEY="sk_test_your_secret_key"
STRIPE_WEBHOOK_SECRET="whsec_your_webhook_secret"

# Datadog
NEXT_PUBLIC_DATADOG_ENABLED="false"
NEXT_PUBLIC_DATADOG_APPLICATION_ID="your-datadog-application-id"
NEXT_PUBLIC_DATADOG_CLIENT_TOKEN="your-datadog-client-token"
NEXT_PUBLIC_DATADOG_SITE="datadoghq.com"
NEXT_PUBLIC_APP_VERSION="1.0.0"
DATADOG_API_KEY="your-datadog-api-key"

# WebSocket for real-time notifications
NEXT_PUBLIC_WS_URL="ws://localhost:8000/ws"

# Python API Backend for web scraping and content extraction
PYTHON_API_URL="http://localhost:8000"

# Partner Console Mode (set to 1 to enable partner console features)
NEXT_PUBLIC_IS_PARTNER_CONSOLE="0"
