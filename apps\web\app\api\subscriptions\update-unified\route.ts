import { NextResponse } from "next/server";
import { getToken } from "next-auth/jwt";
import db from "@/lib/shared-db";
import Strip<PERSON> from "stripe";
import { getTranslations } from "@/lib/server-i18n";

// Initialize Stripe with the secret key
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY as string, {
  apiVersion: "2023-10-16" as any,
});

export async function POST(request: Request) {
  try {
    const token = await getToken({ req: request as any });
    if (!token) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    const { t } = await getTranslations();

    const body = await request.json();
    const {
      tenantId,
      planId,
      additionalUsers,
      billingInterval = "month",
      stripePriceId,
      stripeUserPriceId,
      stripeStoragePriceId,
      storageIncrement, // Parameter for storage increment size
      storageTiers, // JSON string of storage tiers with quantities (for backward compatibility)
      storageTierItems, // Array of storage tier objects
    } = body;

    // Extract additionalStorageGB but allow it to be modified
    let additionalStorageGB = body.additionalStorageGB;

    console.log("Request body:", {
      ...body,
      storageTierItems: storageTierItems
        ? `${storageTierItems.length} items`
        : undefined,
    });

    // Parse storage tiers if provided
    let parsedStorageTiers = [];
    if (storageTiers) {
      try {
        parsedStorageTiers = JSON.parse(storageTiers);
      } catch (error) {
        console.error("Error parsing storage tiers:", error);
      }
    }

    // Validate required fields
    if (!tenantId) {
      return NextResponse.json(
        { error: "Tenant ID is required" },
        { status: 400 }
      );
    }

    // Verify user has admin access to this tenant
    const membership = await db.membership.findFirst({
      where: {
        userId: token.sub,
        tenantId,
        role: { in: ["ADMIN", "OWNER"] },
      },
    });

    if (!membership) {
      return NextResponse.json(
        { error: "You don't have admin permissions for this tenant" },
        { status: 403 }
      );
    }

    // Get the active subscription
    const subscription = await db.subscription.findFirst({
      where: {
        tenantId,
        isActive: true,
      },
      include: {
        plan: true,
      },
    });

    if (!subscription) {
      return NextResponse.json(
        { error: "No active subscription found" },
        { status: 404 }
      );
    }

    // Get the tenant's Stripe customer ID
    if (!subscription.stripeSubscriptionId) {
      return NextResponse.json(
        { error: "Subscription is not linked to Stripe" },
        { status: 400 }
      );
    }

    // Get the current plan
    const currentPlan = subscription.plan;
    const newPlan =
      planId && planId !== subscription.planId
        ? await db.plan.findUnique({ where: { id: planId } })
        : currentPlan;

    if (planId && !newPlan) {
      return NextResponse.json({ error: "Invalid plan ID" }, { status: 400 });
    }

    // Prevent downgrading
    if (planId && newPlan && currentPlan) {
      // Check if this is a downgrade based on plan price
      if (newPlan.price < currentPlan.price) {
        return NextResponse.json(
          { error: t("billing.downgradeNotAllowed") },
          { status: 400 }
        );
      }
    }

    // Prevent reducing additional users
    if (
      additionalUsers !== undefined &&
      additionalUsers < subscription.additionalUsers
    ) {
      return NextResponse.json(
        { error: "Reducing the number of additional users is not allowed" },
        { status: 400 }
      );
    }

    try {
      // Retrieve the current Stripe subscription
      const stripeSubscription = await stripe.subscriptions.retrieve(
        subscription.stripeSubscriptionId
      );

      // Get the current subscription items
      const subscriptionItems = stripeSubscription.items.data;

      // Prepare the items to update
      const updatedItems: Stripe.SubscriptionUpdateParams.Item[] = [];

      // Track which items we've processed
      const processedItems = new Set<string>();

      // Process base plan item (always the first item)
      if (planId && planId !== subscription.planId) {
        // If changing plans, we need to update the base plan item
        const basePlanItem = subscriptionItems[0];

        // Get the new plan's price ID based on billing interval
        let newPlanPriceId: string;

        if (stripePriceId) {
          // Use the provided Stripe price ID if available
          newPlanPriceId = stripePriceId;
        } else {
          // Otherwise, use the price ID from the plan based on billing interval
          const planPriceId =
            billingInterval === "year"
              ? newPlan.stripeYearlyPriceId
              : newPlan.stripePriceId;

          if (!planPriceId) {
            return NextResponse.json(
              { error: "No Stripe price ID found for the selected plan" },
              { status: 400 }
            );
          }

          newPlanPriceId = planPriceId;
        }

        // Add the updated base plan item
        updatedItems.push({
          id: basePlanItem.id,
          price: newPlanPriceId,
          quantity: 1,
        });

        processedItems.add(basePlanItem.id);
      }

      // Process additional users
      if (
        additionalUsers !== undefined &&
        additionalUsers !== subscription.additionalUsers
      ) {
        // Get the additional user price ID based on billing interval
        let additionalUserPriceId: string;

        if (stripeUserPriceId) {
          // Use the provided Stripe price ID if available
          additionalUserPriceId = stripeUserPriceId;
        } else {
          // Otherwise, use the price ID from the plan based on billing interval
          const userPriceId =
            billingInterval === "year"
              ? newPlan.stripeUserYearlyPriceId
              : newPlan.stripeUserPriceId;

          if (!userPriceId) {
            return NextResponse.json(
              { error: "No Stripe price ID found for additional users" },
              { status: 400 }
            );
          }

          additionalUserPriceId = userPriceId;
        }

        if (additionalUsers > 0) {
          // First, check if there's already an item with this price ID
          const existingItemWithPrice = subscriptionItems.find(
            (item) => item.price.id === additionalUserPriceId
          );

          // Find any item related to additional users by product type
          const additionalUsersItem = subscriptionItems.find((item) =>
            item.price.product.toString().includes("additional_user")
          );

          if (existingItemWithPrice) {
            // If there's already an item with this price ID, update its quantity
            updatedItems.push({
              id: existingItemWithPrice.id,
              quantity: additionalUsers,
            });
            processedItems.add(existingItemWithPrice.id);
          } else if (additionalUsersItem) {
            // If there's an additional users item but with a different price ID, update it
            updatedItems.push({
              id: additionalUsersItem.id,
              price: additionalUserPriceId,
              quantity: additionalUsers,
            });
            processedItems.add(additionalUsersItem.id);
          } else {
            // If there's no existing item, add a new one
            updatedItems.push({
              price: additionalUserPriceId,
              quantity: additionalUsers,
            });
          }
        } else if (additionalUsers === 0) {
          // If quantity is 0, remove any additional users items
          const additionalUsersItems = subscriptionItems.filter((item) =>
            item.price.product.toString().includes("additional_user")
          );

          for (const item of additionalUsersItems) {
            updatedItems.push({
              id: item.id,
              deleted: true,
            });
            processedItems.add(item.id);
          }
        }
      }

      // Process additional storage
      if (additionalStorageGB !== undefined || storageTierItems) {
        // If we're adding storage, we need to accumulate it with the existing storage
        if (additionalStorageGB > 0) {
          console.log(
            `Adding ${additionalStorageGB}GB to existing ${
              subscription.additionalStorageGB || 0
            }GB`
          );
          // Update the additionalStorageGB to include the existing storage
          additionalStorageGB += subscription.additionalStorageGB || 0;
          console.log(`Total storage after addition: ${additionalStorageGB}GB`);
        }

        // Only proceed if we have storage to add, storage tier items, or if we're explicitly setting it to 0
        if (
          additionalStorageGB > (subscription.additionalStorageGB || 0) ||
          additionalStorageGB === 0 ||
          (storageTierItems && storageTierItems.length > 0)
        ) {
          if (storageTierItems && storageTierItems.length > 0) {
            console.log("Using provided storageTierItems:", storageTierItems);

            // First, remove any existing storage items
            const existingStorageItems = subscriptionItems.filter((item) =>
              item.price.product.toString().includes("storage_tier")
            );

            for (const item of existingStorageItems) {
              updatedItems.push({
                id: item.id,
                deleted: true,
              });
              processedItems.add(item.id);
            }

            // Then add each storage tier
            for (const tierData of storageTierItems) {
              // Check if this tier has a valid Stripe price ID
              if (!tierData.stripePriceId) {
                console.error(
                  `No Stripe price ID provided for storage tier ${tierData.size}GB`
                );
                continue;
              }

              console.log(
                `Processing ${tierData.quantity} of ${tierData.size}GB storage tier with price ID ${tierData.stripePriceId}`
              );

              // Check if this price already exists in the subscription
              const existingItemWithPrice = subscriptionItems.find(
                (item) => item.price.id === tierData.stripePriceId
              );

              if (existingItemWithPrice) {
                // If there's already an item with this price ID, update its quantity
                console.log(
                  `Found existing item with ID ${existingItemWithPrice.id} for price ${tierData.stripePriceId}, updating quantity to ${tierData.quantity}`
                );
                updatedItems.push({
                  id: existingItemWithPrice.id,
                  quantity: tierData.quantity,
                });
                processedItems.add(existingItemWithPrice.id);
              } else {
                // If there's no existing item with this price, add a new one
                console.log(
                  `No existing item found for price ${tierData.stripePriceId}, adding new item with quantity ${tierData.quantity}`
                );
                updatedItems.push({
                  price: tierData.stripePriceId,
                  quantity: tierData.quantity,
                });
              }
            }

            // Update the subscription in the database with the new storage tier items
            await db.subscription.update({
              where: { id: subscription.id },
              data: {
                additionalStorageGB, // Keep this for backward compatibility
                storageTierItems: storageTierItems.map((item: any) => ({
                  id: item.id,
                  size: item.size,
                  quantity: item.quantity,
                  price: item.price,
                  stripePriceId: item.stripePriceId,
                })),
              },
            });

            // Skip the rest of the storage processing since we've handled it
            console.log("Storage tier items processed successfully");
          } else if (additionalStorageGB > 0) {
            // If we have parsed storage tiers, use them
            if (parsedStorageTiers && parsedStorageTiers.length > 0) {
              console.log("Using provided storage tiers:", parsedStorageTiers);

              // First, remove any existing storage items
              const existingStorageItems = subscriptionItems.filter((item) =>
                item.price.product.toString().includes("storage_tier")
              );

              for (const item of existingStorageItems) {
                updatedItems.push({
                  id: item.id,
                  deleted: true,
                });
                processedItems.add(item.id);
              }

              // Then add each storage tier
              for (const tierData of parsedStorageTiers as Array<{
                id: string;
                size: number;
                quantity: number;
                price: number;
              }>) {
                // Find the storage tier in the database
                const storageTier = await db.storageTier.findFirst({
                  where: {
                    name: `${tierData.size}GB`,
                    isActive: true,
                  },
                });

                if (!storageTier) {
                  console.error(`Storage tier ${tierData.size}GB not found`);
                  continue;
                }

                // Get the appropriate price ID based on billing interval
                const storagePriceId =
                  billingInterval === "year"
                    ? storageTier.stripeYearlyPriceId
                    : storageTier.stripePriceId;

                if (!storagePriceId) {
                  console.error(
                    `No price ID found for storage tier ${tierData.size}GB with billing interval ${billingInterval}`
                  );
                  continue;
                }

                // Check if this price already exists in the subscription
                const existingItemWithPrice = subscriptionItems.find(
                  (item) => item.price.id === storagePriceId
                );

                console.log(
                  `Processing ${tierData.quantity} of ${tierData.size}GB storage tier with price ID ${storagePriceId}`
                );

                if (existingItemWithPrice) {
                  // If there's already an item with this price ID, update its quantity
                  console.log(
                    `Found existing item with ID ${existingItemWithPrice.id} for price ${storagePriceId}, updating quantity to ${tierData.quantity}`
                  );
                  updatedItems.push({
                    id: existingItemWithPrice.id,
                    quantity: tierData.quantity,
                  });
                  processedItems.add(existingItemWithPrice.id);
                } else {
                  // If there's no existing item with this price, add a new one
                  console.log(
                    `No existing item found for price ${storagePriceId}, adding new item with quantity ${tierData.quantity}`
                  );
                  updatedItems.push({
                    price: storagePriceId,
                    quantity: tierData.quantity,
                  });
                }
              }
            }
          } else {
            // Fallback to the old approach
            // Determine storage tier based on the increment being added
            let storageTier: any;

            // If storageIncrement is provided, use it to determine the tier
            if (storageIncrement) {
              console.log(
                `Using provided storage increment: ${storageIncrement}GB`
              );
              // Find the storage tier that matches the increment
              if (storageIncrement === 10) {
                storageTier = await db.storageTier.findFirst({
                  where: { name: "10GB", isActive: true },
                });
              } else if (storageIncrement === 50) {
                storageTier = await db.storageTier.findFirst({
                  where: { name: "50GB", isActive: true },
                });
              } else if (storageIncrement === 100) {
                storageTier = await db.storageTier.findFirst({
                  where: { name: "100GB", isActive: true },
                });
              }
            } else {
              // Fallback to determining tier based on total storage
              console.log(
                `No storage increment provided, using total storage: ${additionalStorageGB}GB`
              );
              if (additionalStorageGB <= 10) {
                storageTier = await db.storageTier.findFirst({
                  where: { name: "10GB", isActive: true },
                });
              } else if (additionalStorageGB <= 50) {
                storageTier = await db.storageTier.findFirst({
                  where: { name: "50GB", isActive: true },
                });
              } else {
                storageTier = await db.storageTier.findFirst({
                  where: { name: "100GB", isActive: true },
                });
              }
            }

            if (!storageTier) {
              return NextResponse.json(
                { error: "No storage tier found for the requested size" },
                { status: 400 }
              );
            }

            // Get the storage tier price ID based on billing interval
            let storagePriceId: string;

            if (stripeStoragePriceId) {
              // Use the provided Stripe price ID if available
              storagePriceId = stripeStoragePriceId;
            } else {
              // Otherwise, use the price ID from the storage tier based on billing interval
              storagePriceId =
                billingInterval === "year"
                  ? storageTier.stripeYearlyPriceId
                  : storageTier.stripePriceId;

              if (!storagePriceId) {
                return NextResponse.json(
                  { error: "No Stripe price ID found for the storage tier" },
                  { status: 400 }
                );
              }
            }

            // First, check if there's already an item with this price ID
            const existingItemWithPrice = subscriptionItems.find(
              (item) => item.price.id === storagePriceId
            );

            // Find any item related to storage by product type
            const additionalStorageItem = subscriptionItems.find((item) =>
              item.price.product.toString().includes("storage_tier")
            );

            console.log(
              `Processing storage tier with price ID ${storagePriceId}`
            );

            if (existingItemWithPrice) {
              // If there's already an item with this price ID, update its quantity
              console.log(
                `Found existing item with ID ${existingItemWithPrice.id} for price ${storagePriceId}`
              );
              updatedItems.push({
                id: existingItemWithPrice.id,
                quantity: 1, // Storage tiers always have quantity 1
              });
              processedItems.add(existingItemWithPrice.id);
            } else if (additionalStorageItem) {
              // If there's a storage item but with a different price ID, update it
              console.log(
                `Found existing storage item with ID ${additionalStorageItem.id} with different price, updating to ${storagePriceId}`
              );
              updatedItems.push({
                id: additionalStorageItem.id,
                price: storagePriceId,
                quantity: 1,
              });
              processedItems.add(additionalStorageItem.id);
            } else {
              // If there's no existing item, add a new one
              console.log(
                `No existing storage item found, adding new item with price ${storagePriceId}`
              );
              updatedItems.push({
                price: storagePriceId,
                quantity: 1,
              });
            }
          }
        } else {
          // If storage is 0, remove any storage items
          const storageItems = subscriptionItems.filter((item) =>
            item.price.product.toString().includes("storage_tier")
          );

          for (const item of storageItems) {
            updatedItems.push({
              id: item.id,
              deleted: true,
            });
            processedItems.add(item.id);
          }
        }
      }

      // Add any remaining items that weren't processed
      for (const item of subscriptionItems) {
        if (!processedItems.has(item.id)) {
          updatedItems.push({
            id: item.id,
            price: item.price.id,
            quantity: item.quantity,
          });
        }
      }

      // Log the items we're going to update
      console.log(
        "Updating subscription with items:",
        JSON.stringify(updatedItems, null, 2)
      );

      try {
        // Update the subscription in Stripe
        await stripe.subscriptions.update(subscription.stripeSubscriptionId, {
          items: updatedItems,
          metadata: {
            tenantId,
            planId: planId || subscription.planId,
            additionalUsers: (additionalUsers !== undefined
              ? additionalUsers
              : subscription.additionalUsers
            ).toString(),
            additionalStorageGB: (additionalStorageGB !== undefined
              ? additionalStorageGB
              : subscription.additionalStorageGB || 0
            ).toString(),
            billingInterval,
            unified: "true",
          },
          proration_behavior: "always_invoice",
        });

        console.log("Successfully updated Stripe subscription");
      } catch (stripeError) {
        console.error("Stripe subscription update error:", stripeError);
        return NextResponse.json(
          { error: `Stripe error: ${stripeError.message}` },
          { status: 400 }
        );
      }

      // Update the subscription in our database
      const updatedSubscription = await db.subscription.update({
        where: {
          id: subscription.id,
        },
        data: {
          planId: planId || subscription.planId,
          additionalUsers:
            additionalUsers !== undefined
              ? additionalUsers
              : subscription.additionalUsers,
          additionalStorageGB:
            additionalStorageGB !== undefined
              ? additionalStorageGB
              : subscription.additionalStorageGB,
          // Update storage tier items if provided
          ...(storageTierItems && {
            storageTierItems: storageTierItems.map((item: any) => ({
              id: item.id,
              size: item.size,
              quantity: item.quantity,
              price: item.price,
              stripePriceId: item.stripePriceId,
            })),
          }),
        },
        include: {
          plan: true,
        },
      });

      // Create a Stripe customer portal session to show the updated subscription
      const session = await stripe.billingPortal.sessions.create({
        customer: subscription.stripeCustomerId as string,
        return_url: `${request.headers.get("origin")}/billing`,
      });

      return NextResponse.json({
        subscription: updatedSubscription,
        portalUrl: session.url,
      });
    } catch (error) {
      console.error("Error updating subscription:", error);
      return NextResponse.json(
        { error: "Failed to update subscription" },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error("Error processing request:", error);
    return NextResponse.json(
      { error: "Failed to process request" },
      { status: 500 }
    );
  }
}
