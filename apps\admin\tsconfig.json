{"compilerOptions": {"lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": false, "target": "es2017", "noEmit": true, "incremental": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "paths": {"@/*": ["./*"]}, "plugins": [{"name": "next"}], "strictNullChecks": true}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", "**/*.js", "**/*.jsx", ".next/types/**/*.ts", "lib/**/*.ts", "lib/**/*.tsx", "lib/next-auth/src/auth-options.ts", "lib/shared-db/index.ts"], "exclude": ["node_modules"]}