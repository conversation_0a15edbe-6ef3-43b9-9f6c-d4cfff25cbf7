"use client";

import React, { useEffect, useRef } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { ExternalLink, FileText, Globe, Download } from "lucide-react";
import { useLanguage } from "@/lib/language-context";
import ReactMarkdown from "react-markdown";
import { Source } from "./types";
import { CodeBlock } from "./components/CodeBlock";

interface CitationModalProps {
  isOpen: boolean;
  onClose: () => void;
  source: Source | null;
  highlightedText?: string;
}

export function CitationModal({
  isOpen,
  onClose,
  source,
  highlightedText,
}: CitationModalProps) {
  const { t } = useLanguage();
  const highlightRef = useRef<HTMLDivElement>(null);
  // Check if this is a web source
  const isWebSource =
    source?.metadata?.source === "web" ||
    source?.metadata?.type === "web_search";
  // Handle web sources differently
  const name =
    source?.metadata?.title &&
    source?.metadata?.title?.toLowerCase() !== "unknown"
      ? source.metadata.title
      : source?.metadata?.file_name &&
          source?.metadata?.file_name?.toLowerCase() !== "unknown"
        ? source.metadata.file_name
        : decodeURI(
            source?.metadata?.source
              ?.split("/")
              .pop()
              ?.split("-")
              .slice(1)
              .join("-") ?? "Document"
          );

  const fileName = isWebSource
    ? source?.metadata?.title ?? (source?.metadata?.fileName || "Web Result")
    : name;
  const workspaceName = source?.metadata?.workspace?.name;
  const fileId = source?.metadata?.fileId;
  const workspaceSlug =
    source?.metadata?.workspace?.slug ?? source?.metadata?.slug;
  const pageNumber =
    source?.metadata?.page || source?.metadata?.page === 0
      ? source?.metadata?.page + 1
      : null;
  const hasFileLink = !isWebSource && fileId && workspaceSlug;
  const webLink = isWebSource ? source?.metadata?.link : null;

  // Function to get platform-specific document URL
  const getDocumentUrl = () => {
    if (isWebSource) {
      return webLink;
    }

    // Check if this is a SharePoint document
    if (source?.metadata?.sharePointUrl) {
      return source.metadata.sharePointUrl;
    }

    // Check if this is a Google Drive document
    if (source?.metadata?.googleDriveUrl) {
      return source.metadata.googleDriveUrl;
    }

    // Check if this is an OneDrive document
    if (source?.metadata?.oneDriveUrl) {
      return source.metadata.oneDriveUrl;
    }

    // Default to SKH internal viewer
    if (hasFileLink) {
      return `/workspace/${workspaceSlug}/file/${fileId}`;
    }

    return null;
  };

  const documentUrl = getDocumentUrl();
  const shouldShowOpenButton = documentUrl !== null;

  // Function to handle document download - Direct frontend approach
  const handleDownload = () => {
    if (!source?.metadata?.fileId || isWebSource) {
      return;
    }

    // Create download URL - use the file's direct URL for download (preserves SAS tokens)
    const downloadUrl = source.metadata.url || source.metadata.source;
    if (!downloadUrl) {
      console.error("No download URL available for:", fileName);
      return;
    }

    const downloadFileName = fileName || "document";

    try {
      // Method 1: Direct download using anchor element with original URL (including SAS tokens)
      const link = document.createElement("a");
      link.href = downloadUrl;
      link.download = downloadFileName;
      link.target = "_blank";
      link.rel = "noopener noreferrer";

      // Temporarily add to DOM to trigger download
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      return;
    } catch (linkError) {
      try {
        // Method 2: Iframe approach for files that don't support direct download
        const iframe = document.createElement("iframe");
        iframe.style.display = "none";
        iframe.style.position = "absolute";
        iframe.style.left = "-9999px";
        iframe.src = downloadUrl;

        document.body.appendChild(iframe);

        // Clean up iframe after download attempt
        setTimeout(() => {
          if (document.body.contains(iframe)) {
            document.body.removeChild(iframe);
          }
        }, 2000);
      } catch (iframeError) {
        // Method 3: Open in new window as final fallback
        try {
          window.open(downloadUrl, "_blank", "noopener,noreferrer");
          console.log("Download initiated via new window");
        } catch (windowError) {
          console.error("All download methods failed:", windowError);
        }
      }
    }
  };

  const shouldShowDownloadButton = !isWebSource && source?.metadata?.fileId;

  // Function to get contextual content with rich text support
  const getContextualContent = () => {
    if (!source?.content) return { content: "", hasMatch: false };

    // Use relevantText from metadata if available (this is usually the best match)
    const relevantText = source.metadata?.relevantText;
    if (relevantText) {
      return {
        content: relevantText,
        hasMatch: true,
        isRelevantText: true,
      };
    }

    if (!highlightedText) {
      console.log("No highlighted text", source);
      // Show first 1000 characters with context
      const content = source?.content;
      source?.content?.substring(0, 1000) +
        (source.content.length > 1000 ? "..." : "");
      return { content, hasMatch: false };
    }

    // Try to find the highlighted text in the source content
    const searchText = highlightedText.trim();
    let position = source.content
      .toLowerCase()
      .indexOf(searchText.toLowerCase());

    // If not found, try with a more flexible search
    if (position === -1) {
      const firstWords = searchText.split(" ").slice(0, 3).join(" ");
      position = source.content.toLowerCase().indexOf(firstWords.toLowerCase());
    }

    // Get context around the highlighted text (±2 lines context, roughly 400 chars each side)
    const contextSize = 400;
    const startPos = position !== -1 ? Math.max(0, position - contextSize) : 0;
    const endPos =
      position !== -1
        ? Math.min(
            source.content.length,
            position + searchText.length + contextSize
          )
        : Math.min(source.content.length, 1000);

    let contextContent = source.content.substring(startPos, endPos);

    // Add ellipsis if content is truncated
    if (startPos > 0) contextContent = "..." + contextContent;
    if (endPos < source.content.length) contextContent = contextContent + "...";

    return {
      content: contextContent,
      hasMatch: position !== -1,
      matchText: position !== -1 ? searchText : null,
      isRelevantText: false,
    };
  };

  // Function to render content with highlighting
  const renderContentWithHighlight = () => {
    const { content, hasMatch, matchText } = getContextualContent();

    if (!hasMatch || !matchText) {
      // No match found, render as rich text without highlighting
      return (
        <div className="prose dark:prose-invert max-w-none text-sm">
          <ReactMarkdown
            components={{
              // Customize rendering for better display in modal
              p: ({ children }) => (
                <p className="mb-3 leading-relaxed">{children}</p>
              ),
              ul: ({ children }) => (
                <ul className="mb-3 ml-4 list-disc">{children}</ul>
              ),
              ol: ({ children }) => (
                <ol className="mb-3 ml-4 list-decimal">{children}</ol>
              ),
              li: ({ children }) => <li className="mb-1">{children}</li>,
              code: ({ inline, children, className, ...props }: any) => (
                <CodeBlock inline={inline} className={className} {...props}>
                  {children}
                </CodeBlock>
              ),
              pre: ({ children }: any) => <>{children}</>, // Let CodeBlock handle the pre wrapper
              strong: ({ children }) => (
                <strong className="font-semibold">{children}</strong>
              ),
              em: ({ children }) => <em className="italic">{children}</em>,
            }}
          >
            {content}
          </ReactMarkdown>
        </div>
      );
    }

    // Split content around the match for highlighting
    const lowerContent = content.toLowerCase();
    const lowerMatch = matchText.toLowerCase();
    const matchIndex = lowerContent.indexOf(lowerMatch);

    if (matchIndex === -1) {
      // Fallback if match not found in processed content
      return (
        <div className="prose dark:prose-invert max-w-none text-sm">
          <ReactMarkdown
            components={{
              code: ({ inline, children, className, ...props }: any) => (
                <CodeBlock inline={inline} className={className} {...props}>
                  {children}
                </CodeBlock>
              ),
              pre: ({ children }: any) => <>{children}</>, // Let CodeBlock handle the pre wrapper
            }}
          >
            {content}
          </ReactMarkdown>
        </div>
      );
    }

    const beforeMatch = content.substring(0, matchIndex);
    const matchedText = content.substring(
      matchIndex,
      matchIndex + matchText.length
    );
    const afterMatch = content.substring(matchIndex + matchText.length);

    const markdownComponents = {
      code: ({ inline, children, className, ...props }: any) => (
        <CodeBlock inline={inline} className={className} {...props}>
          {children}
        </CodeBlock>
      ),
      pre: ({ children }: any) => <>{children}</>, // Let CodeBlock handle the pre wrapper
    };

    return (
      <div className="prose dark:prose-invert max-w-none text-sm">
        <ReactMarkdown components={markdownComponents}>
          {beforeMatch}
        </ReactMarkdown>
        <div
          ref={highlightRef}
          className="bg-yellow-100 dark:bg-yellow-900/30 px-2 py-1 rounded border-l-4 border-yellow-400 dark:border-yellow-600 my-3 font-medium"
        >
          <ReactMarkdown components={markdownComponents}>
            {matchedText}
          </ReactMarkdown>
        </div>
        <ReactMarkdown components={markdownComponents}>
          {afterMatch}
        </ReactMarkdown>
      </div>
    );
  };

  // Scroll to highlighted text when modal opens
  useEffect(() => {
    if (isOpen && highlightRef.current) {
      setTimeout(() => {
        highlightRef.current?.scrollIntoView({
          behavior: "smooth",
          block: "center",
        });
      }, 100);
    }
  }, [isOpen]);

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="max-w-4xl w-[90vw] max-h-[85vh] flex flex-col">
        <DialogHeader className="flex flex-row items-center justify-between">
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2">
              <DialogTitle
                className="text-lg font-semibold truncate"
                title={fileName}
              >
                {fileName}
              </DialogTitle>

              {/* Relevance indicator */}
              {source?.metadata?.relevanceScore && (
                <div
                  className="flex items-center gap-0.5 px-2 py-1 bg-gray-100 dark:bg-gray-800 rounded-md"
                  title={`${t("chat.relevance")}: ${Math.round(
                    (source.metadata.relevanceScore || 0) * 100
                  )}%`}
                >
                  <div className="text-xs text-gray-600 dark:text-gray-300 mr-1">
                    {Math.round((source.metadata.relevanceScore || 0) * 100)}%
                  </div>
                  <div className="flex items-center gap-0.5">
                    {[...Array(5)].map((_, i) => {
                      const score = source.metadata.relevanceScore || 0;
                      const filled = i < Math.round(score * 5);
                      return (
                        <div
                          key={i}
                          className={`h-2 w-2 rounded-full ${
                            filled
                              ? "bg-primary"
                              : "bg-gray-200 dark:bg-gray-700"
                          }`}
                        />
                      );
                    })}
                  </div>
                </div>
              )}
            </div>
            {workspaceName && !isWebSource && (
              <p className="text-sm text-muted-foreground truncate">
                {workspaceName}
              </p>
            )}
            {isWebSource && source?.metadata?.displayLink && (
              <p className="text-sm text-blue-500 truncate">
                {source.metadata.displayLink}
              </p>
            )}
          </div>
          <div className="flex items-center gap-2">
            {shouldShowOpenButton && (
              <Button
                variant="outline"
                size="sm"
                className="flex items-center gap-1"
                onClick={() => {
                  window.open(documentUrl, "_blank", "noopener,noreferrer");
                }}
              >
                {isWebSource ? (
                  <>
                    <span>Visit Website</span>
                    <Globe className="h-3.5 w-3.5" />
                  </>
                ) : (
                  <>
                    <span>{t("chat.openDocument")}</span>
                    <ExternalLink className="h-3.5 w-3.5" />
                  </>
                )}
              </Button>
            )}
            {shouldShowDownloadButton && (
              <Button
                variant="outline"
                size="sm"
                className="flex items-center gap-1"
                onClick={handleDownload}
              >
                <span>{t("chat.downloadDocument")}</span>
                <Download className="h-3.5 w-3.5" />
              </Button>
            )}
          </div>
        </DialogHeader>

        <div className="mt-4 flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900/50 rounded-md text-sm relative">
          <div className="sticky top-0 z-10 bg-gray-100 dark:bg-gray-800 p-2 border-b border-gray-200 dark:border-gray-700 flex items-center justify-between">
            <div className="flex items-center gap-2">
              {isWebSource ? (
                <Globe className="h-4 w-4 text-blue-500" />
              ) : (
                <FileText className="h-4 w-4 text-primary/70" />
              )}
              <span className="font-medium">{fileName}</span>

              {/* Relevance percentage in sticky header */}
              {source?.metadata?.relevanceScore && (
                <div className="text-xs text-primary bg-primary/10 px-1.5 py-0.5 rounded-full">
                  {Math.round((source.metadata.relevanceScore || 0) * 100)}%
                </div>
              )}
            </div>
            <div className="flex items-center gap-2">
              {pageNumber !== null && (
                <div className="px-2 py-0.5 bg-primary/10 text-primary text-xs rounded-full font-medium">
                  {t("chat.page")} {pageNumber}
                </div>
              )}
            </div>
          </div>
          <div className="p-4">{renderContentWithHighlight()}</div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
