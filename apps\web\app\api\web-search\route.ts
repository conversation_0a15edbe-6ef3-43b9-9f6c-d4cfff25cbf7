import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/next-auth";
import db from "@/lib/shared-db";
import axios from "axios";
import {
  getLanguageFromRequest,
  createServerTranslator,
} from "@/lib/server-translations";

// Simple in-memory cache for web search results
// In production, this should be replaced with a proper caching solution like Redis
const searchCache = new Map<string, { results: any; timestamp: number }>();
const CACHE_TTL = 24 * 60 * 60 * 1000; // 24 hours in milliseconds

// Google Search API configuration
const GOOGLE_API_KEY = process.env.GOOGLE_API_KEY;
const GOOGLE_SEARCH_ENGINE_ID = process.env.GOOGLE_SEARCH_ENGINE_ID;
const GOOGLE_SEARCH_API_URL = "https://www.googleapis.com/customsearch/v1";

// Perform web search using Google Custom Search API
async function performWebSearch(query: string) {
  try {
    if (!GOOGLE_API_KEY || !GOOGLE_SEARCH_ENGINE_ID) {
      throw new Error("Google Search API configuration is missing");
    }

    const response = await axios.get(GOOGLE_SEARCH_API_URL, {
      params: {
        key: GOOGLE_API_KEY,
        cx: GOOGLE_SEARCH_ENGINE_ID,
        q: query,
        num: 5, // Number of results to return
      },
    });

    if (response.status !== 200) {
      throw new Error(`Search API returned status ${response.status}`);
    }

    // Extract and format the search results
    const items = response.data.items || [];
    return items.map((item: any) => ({
      title: item.title,
      link: item.link,
      snippet: item.snippet,
      displayLink: item.displayLink,
      source: "web",
    }));
  } catch (error) {
    console.error("Error performing web search:", error);
    return [];
  }
}

// Check if the tenant has exceeded their daily web search limit
async function checkWebSearchLimit(tenantId: string) {
  try {
    // Get the tenant's subscription
    const subscription = await db.subscription.findFirst({
      where: {
        tenantId,
        isActive: true,
      },
      include: {
        plan: true,
      },
    });

    if (!subscription) {
      return { allowed: false, message: "No active subscription found" };
    }

    const webSearchLimit = subscription.plan.webSearchLimit || 50; // Default to 50 if not specified

    // Count today's web searches
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const searchCount = await db.webSearchUsage.count({
      where: {
        tenantId,
        createdAt: {
          gte: today,
        },
      },
    });

    return {
      allowed: searchCount < webSearchLimit,
      current: searchCount,
      limit: webSearchLimit,
      message:
        searchCount >= webSearchLimit
          ? `Daily web search limit of ${webSearchLimit} reached. Please try again tomorrow.`
          : null,
    };
  } catch (error) {
    console.error("Error checking web search limit:", error);
    return { allowed: false, message: "Error checking web search limit" };
  }
}

export async function GET(request: NextRequest) {
  const language = getLanguageFromRequest(request);
  const t = createServerTranslator(language);

  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: t("common.unauthorized") },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const query = searchParams.get("query");
    const tenantId = searchParams.get("tenantId");
    const userId = searchParams.get("userId");

    if (!query) {
      return NextResponse.json(
        { error: "Query parameter is required" },
        { status: 400 }
      );
    }

    if (!tenantId) {
      return NextResponse.json(
        { error: "Tenant ID is required" },
        { status: 400 }
      );
    }

    // Check if the tenant has exceeded their daily web search limit
    const limitCheck = await checkWebSearchLimit(tenantId);
    if (!limitCheck.allowed) {
      return NextResponse.json(
        {
          error: limitCheck.message,
          limitExceeded: true,
          limitInfo: limitCheck,
        },
        { status: 429 }
      );
    }

    // Check cache first
    const cacheKey = `${query}-${tenantId}`;
    const cachedResult = searchCache.get(cacheKey);

    if (cachedResult && Date.now() - cachedResult.timestamp < CACHE_TTL) {
      // Log usage even for cached results
      await db.webSearchUsage.create({
        data: {
          tenantId,
          userId,
          query,
          cached: true,
        },
      });

      return NextResponse.json({
        results: cachedResult.results,
        cached: true,
      });
    }

    // Perform web search using a search API
    const searchResults = await performWebSearch(query);

    // Cache the results
    searchCache.set(cacheKey, {
      results: searchResults,
      timestamp: Date.now(),
    });

    // Log usage
    await db.webSearchUsage.create({
      data: {
        tenantId,
        userId,
        query,
        cached: false,
      },
    });

    return NextResponse.json({
      results: searchResults,
      cached: false,
    });
  } catch (error) {
    console.error("Error in web search API:", error);
    return NextResponse.json(
      { error: t("api.errors.failedToPerformWebSearch") },
      { status: 500 }
    );
  }
}
