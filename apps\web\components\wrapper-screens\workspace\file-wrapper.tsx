"use client";

import { useState, useRef, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import {
  ChevronLeft,
  Download,
  Edit2,
  ExternalLink,
  Trash2,
  RefreshCw,
  Play,
  Pause,
  Volume2,
  VolumeX,
  Maximize,
  SkipBack,
  SkipForward,
  PictureInPicture,
  Minimize,
  Info,
} from "lucide-react";
import { useLanguage } from "@/lib/language-context";
import { workspaceChatService } from "@/services/workspace-chat";
import { getCookie } from "@/utils/cookies";
import toast from "react-hot-toast";
import { Slider } from "@/components/ui/slider";

import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Select,
  SelectContent,
  SelectI<PERSON>,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useFileOperations } from "@/hooks/use-file-operations";
import { CreateFolderDialog } from "./page-wrapper";
import { FileDeleteConfirmation } from "@/components/dialogs/file-delete-confirmation";
import Image from "next/image";
import ReactMarkdown from "react-markdown";
import { CodeBlock } from "@/components/wrapper-screens/chat/components/CodeBlock";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { DeleteGuard, UpdateGuard } from "@/components/permission-guard";
import { formatFileSize, formatDuration } from "@/lib/utils/file-utils";

// Audio Preview Component
const AudioPreview = ({ file }: { file: any }) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [duration, setDuration] = useState(0);
  const [currentTime, setCurrentTime] = useState(0);
  const [volume, setVolume] = useState(1);
  const [isMuted, setIsMuted] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [playbackRate, setPlaybackRate] = useState(1);
  const audioRef = useRef<HTMLAudioElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // Playback speed options
  const playbackSpeeds = [
    { value: 0.5, label: "0.5x" },
    { value: 0.75, label: "0.75x" },
    { value: 1, label: "1x" },
    { value: 1.25, label: "1.25x" },
    { value: 1.5, label: "1.5x" },
    { value: 2, label: "2x" },
  ];

  // Safe file size formatting
  const getFileSize = () => {
    if (!file.size) {
      return "Unknown size";
    }

    // Handle both string and number formats
    let sizeInBytes: number;
    if (typeof file.size === "string") {
      // Try to parse the string as a number
      sizeInBytes = parseInt(file.size, 10);
      if (isNaN(sizeInBytes)) {
        // If it's already formatted (e.g., "1.2 MB"), return as is
        return file.size;
      }
    } else {
      sizeInBytes = file.size;
    }

    if (sizeInBytes === 0 || isNaN(sizeInBytes)) {
      return "Unknown size";
    }

    return formatFileSize(sizeInBytes);
  };

  // Safe file extension formatting
  const getFileExtension = () => {
    return file.extension?.toUpperCase() || "Unknown format";
  };

  useEffect(() => {
    const audio = audioRef.current;
    const container = containerRef.current;
    if (!audio || !container) return;

    const handleLoadedMetadata = () => {
      setDuration(audio.duration);
      setIsLoading(false);
    };

    const handleTimeUpdate = () => {
      setCurrentTime(audio.currentTime);
    };

    const handlePlay = () => setIsPlaying(true);
    const handlePause = () => setIsPlaying(false);
    const handleEnded = () => {
      setIsPlaying(false);
      setCurrentTime(0);
    };

    const handleError = () => {
      setError(
        "Failed to load audio file. Please check if the file is accessible."
      );
      setIsLoading(false);
    };

    const handleLoadStart = () => {
      setIsLoading(true);
      setError(null);
    };

    // Keyboard shortcuts
    const handleKeyDown = (e: KeyboardEvent) => {
      // Only handle shortcuts when the container is focused or no input is focused
      if (
        document.activeElement?.tagName === "INPUT" ||
        document.activeElement?.tagName === "TEXTAREA"
      ) {
        return;
      }

      switch (e.code) {
        case "Space":
          e.preventDefault();
          togglePlayPause();
          break;
        case "ArrowLeft":
          e.preventDefault();
          skipBackward();
          break;
        case "ArrowRight":
          e.preventDefault();
          skipForward();
          break;
        case "ArrowUp":
          e.preventDefault();
          adjustVolume(0.1);
          break;
        case "ArrowDown":
          e.preventDefault();
          adjustVolume(-0.1);
          break;
        case "KeyM":
          e.preventDefault();
          toggleMute();
          break;
      }
    };

    audio.addEventListener("loadedmetadata", handleLoadedMetadata);
    audio.addEventListener("timeupdate", handleTimeUpdate);
    audio.addEventListener("play", handlePlay);
    audio.addEventListener("pause", handlePause);
    audio.addEventListener("ended", handleEnded);
    audio.addEventListener("error", handleError);
    audio.addEventListener("loadstart", handleLoadStart);
    document.addEventListener("keydown", handleKeyDown);

    // Make container focusable for keyboard shortcuts
    container.setAttribute("tabIndex", "0");

    return () => {
      audio.removeEventListener("loadedmetadata", handleLoadedMetadata);
      audio.removeEventListener("timeupdate", handleTimeUpdate);
      audio.removeEventListener("play", handlePlay);
      audio.removeEventListener("pause", handlePause);
      audio.removeEventListener("ended", handleEnded);
      audio.removeEventListener("error", handleError);
      audio.removeEventListener("loadstart", handleLoadStart);
      document.removeEventListener("keydown", handleKeyDown);
    };
  }, []);

  const togglePlayPause = () => {
    const audio = audioRef.current;
    if (!audio) return;

    if (isPlaying) {
      audio.pause();
    } else {
      audio.play().catch((error) => {
        console.error("Error playing audio:", error);
        setError("Failed to play audio. Please try again.");
      });
    }
  };

  const handleSeek = (e: React.ChangeEvent<HTMLInputElement>) => {
    const audio = audioRef.current;
    if (!audio) return;

    const newTime = parseFloat(e.target.value);
    audio.currentTime = newTime;
    setCurrentTime(newTime);
  };

  const handleProgressClick = (e: React.MouseEvent<HTMLDivElement>) => {
    const audio = audioRef.current;
    if (!audio || !duration) return;

    const rect = e.currentTarget.getBoundingClientRect();
    const clickX = e.clientX - rect.left;
    const newTime = (clickX / rect.width) * duration;

    audio.currentTime = newTime;
    setCurrentTime(newTime);
  };

  const handleVolumeChange = (values: number[]) => {
    const audio = audioRef.current;
    if (!audio) return;

    const newVolume = values[0] / 100; // Convert from 0-100 to 0-1
    audio.volume = newVolume;
    setVolume(newVolume);
    setIsMuted(newVolume === 0);
  };

  const adjustVolume = (delta: number) => {
    const audio = audioRef.current;
    if (!audio) return;

    // Calculate new volume with better precision handling
    let newVolume = volume + delta;

    // Round to 2 decimal places to avoid floating-point precision issues
    newVolume = Math.round(newVolume * 100) / 100;

    // Ensure bounds are exactly 0 and 1
    if (newVolume <= 0) {
      newVolume = 0;
    } else if (newVolume >= 1) {
      newVolume = 1;
    }

    // Use the handleVolumeChange function to maintain consistency
    handleVolumeChange([Math.round(newVolume * 100)]);
  };

  const toggleMute = () => {
    const audio = audioRef.current;
    if (!audio) return;

    if (isMuted) {
      audio.volume = volume;
      setIsMuted(false);
    } else {
      audio.volume = 0;
      setIsMuted(true);
    }
  };

  const skipForward = (seconds: number = 10) => {
    const audio = audioRef.current;
    if (!audio) return;

    audio.currentTime = Math.min(audio.currentTime + seconds, duration);
  };

  const skipBackward = (seconds: number = 10) => {
    const audio = audioRef.current;
    if (!audio) return;

    audio.currentTime = Math.max(audio.currentTime - seconds, 0);
  };

  const handlePlaybackRateChange = (rate: string) => {
    const audio = audioRef.current;
    if (!audio) return;

    const newRate = parseFloat(rate);
    audio.playbackRate = newRate;
    setPlaybackRate(newRate);
  };

  // Validate file data
  if (!file || !file.url) {
    return (
      <div className="flex justify-center py-8">
        <Card className="max-w-2xl w-full p-8 text-center">
          <p className="text-xl mb-4 text-red-600">File Not Available</p>
          <p className="text-muted-foreground mb-4">
            The audio file could not be loaded. The file may have been moved or
            deleted.
          </p>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex justify-center py-8">
        <Card className="max-w-2xl w-full p-8 text-center">
          <p className="text-xl mb-4 text-red-600">Audio Preview Error</p>
          <p className="text-muted-foreground mb-4">{error}</p>
          <div className="flex gap-2 justify-center">
            <Button
              variant="outline"
              onClick={() => window.open(file.url, "_blank")}
              className="gap-2"
              disabled={!file.url}
            >
              <ExternalLink className="h-4 w-4" />
              Open in New Tab
            </Button>
            <Button
              variant="outline"
              onClick={() => {
                const link = document.createElement("a");
                link.download = file.name || "audio-file";
                link.href = file.url;
                link.click();
              }}
              className="gap-2"
              disabled={!file.url}
            >
              <Download className="h-4 w-4" />
              Download File
            </Button>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className="py-8 px-4">
      <div className="max-w-7xl mx-auto">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Left Column - Audio Player */}
          <div className="space-y-6">
            <Card
              className="w-full p-6 media-player-container"
              ref={containerRef}
            >
              <div className="space-y-6">
                {/* File Info */}
                <div className="text-center">
                  <h3 className="text-lg font-semibold mb-2" title={file.name}>
                    {file.name || "Unknown Audio File"}
                  </h3>
                  <div className="flex justify-center gap-4 text-sm text-muted-foreground flex-wrap">
                    <span>Format: {getFileExtension()}</span>
                    <span>Size: {getFileSize()}</span>
                    {duration > 0 && (
                      <span>Duration: {formatDuration(duration)}</span>
                    )}
                  </div>
                </div>

                {/* Audio Element */}
                <audio
                  ref={audioRef}
                  src={file.url}
                  preload="metadata"
                  className="hidden"
                  aria-label={`Audio player for ${file.name}`}
                />

                {/* Enhanced Audio Controls */}
                <div className="space-y-4">
                  {/* Progress Bar with Click-to-Seek */}
                  <div className="space-y-2">
                    <div
                      className="relative w-full h-3 bg-gray-200 rounded-lg cursor-pointer dark:bg-gray-700 group"
                      onClick={handleProgressClick}
                      role="progressbar"
                      aria-valuemin={0}
                      aria-valuemax={duration}
                      aria-valuenow={currentTime}
                      aria-label="Audio progress"
                    >
                      <div
                        className="absolute top-0 left-0 h-full bg-primary rounded-lg transition-all duration-150"
                        style={{
                          width: `${
                            duration ? (currentTime / duration) * 100 : 0
                          }%`,
                        }}
                      />
                      <div
                        className="absolute top-1/2 -translate-y-1/2 w-4 h-4 bg-primary rounded-full shadow-md opacity-0 group-hover:opacity-100 transition-opacity duration-150"
                        style={{
                          left: `${
                            duration ? (currentTime / duration) * 100 : 0
                          }%`,
                          transform: "translateX(-50%) translateY(-50%)",
                        }}
                      />
                    </div>
                    <div className="flex justify-between text-xs text-muted-foreground">
                      <span>{formatDuration(currentTime)}</span>
                      <span>{formatDuration(duration)}</span>
                    </div>
                  </div>

                  {/* Compact Control Layout */}
                  <div className="flex items-center justify-center gap-4">
                    {/* Skip Backward */}
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => skipBackward(10)}
                      disabled={isLoading}
                      title="Skip backward 10 seconds (←)"
                      aria-label="Skip backward 10 seconds"
                    >
                      <SkipBack className="h-4 w-4" />
                    </Button>

                    {/* Play/Pause */}
                    <Button
                      variant="outline"
                      size="lg"
                      onClick={togglePlayPause}
                      disabled={isLoading}
                      className="gap-2 px-6"
                      title={isPlaying ? "Pause (Space)" : "Play (Space)"}
                      aria-label={isPlaying ? "Pause audio" : "Play audio"}
                    >
                      {isPlaying ? (
                        <Pause className="h-5 w-5" />
                      ) : (
                        <Play className="h-5 w-5" />
                      )}
                      {isPlaying ? "Pause" : "Play"}
                    </Button>

                    {/* Skip Forward */}
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => skipForward(10)}
                      disabled={isLoading}
                      title="Skip forward 10 seconds (→)"
                      aria-label="Skip forward 10 seconds"
                    >
                      <SkipForward className="h-4 w-4" />
                    </Button>

                    {/* Volume Control */}
                    <div className="flex items-center gap-2 ml-4">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={toggleMute}
                        disabled={isLoading}
                        title={isMuted ? "Unmute (M)" : "Mute (M)"}
                        aria-label={isMuted ? "Unmute audio" : "Mute audio"}
                      >
                        {isMuted ? (
                          <VolumeX className="h-4 w-4" />
                        ) : (
                          <Volume2 className="h-4 w-4" />
                        )}
                      </Button>
                      <div className="volume-control w-24 h-5 flex items-center">
                        <Slider
                          value={[isMuted ? 0 : Math.round(volume * 100)]}
                          onValueChange={handleVolumeChange}
                          max={100}
                          step={1}
                          disabled={isLoading}
                          className="w-full cursor-pointer"
                          aria-label="Volume control"
                        />
                      </div>
                      <span className="text-xs text-muted-foreground w-8 text-center">
                        {Math.round((isMuted ? 0 : volume) * 100)}%
                      </span>
                    </div>

                    {/* Playback Speed */}
                    <div className="flex items-center gap-2 ml-4">
                      <span className="text-sm text-muted-foreground">
                        Speed:
                      </span>
                      <Select
                        value={playbackRate.toString()}
                        onValueChange={handlePlaybackRateChange}
                      >
                        <SelectTrigger className="w-20 h-8">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {playbackSpeeds.map((speed) => (
                            <SelectItem
                              key={speed.value}
                              value={speed.value.toString()}
                            >
                              {speed.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </div>

                {/* Loading State */}
                {isLoading && (
                  <div className="text-center py-4">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
                    <p className="text-sm text-muted-foreground">
                      Loading audio...
                    </p>
                  </div>
                )}

                {/* Keyboard Shortcuts Info */}
                <div className="flex justify-center pt-2 border-t">
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="gap-2 text-muted-foreground"
                        >
                          <Info className="h-4 w-4" />
                          Keyboard Shortcuts
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent side="top" className="max-w-xs">
                        <div className="space-y-1 text-sm">
                          <div className="font-semibold mb-2">
                            Audio Player Shortcuts
                          </div>
                          <div className="grid grid-cols-2 gap-2 text-xs">
                            <div>
                              <kbd className="px-1 py-0.5 bg-muted rounded text-xs">
                                Space
                              </kbd>{" "}
                              Play/Pause
                            </div>
                            <div>
                              <kbd className="px-1 py-0.5 bg-muted rounded text-xs">
                                M
                              </kbd>{" "}
                              Mute
                            </div>
                            <div>
                              <kbd className="px-1 py-0.5 bg-muted rounded text-xs">
                                ←
                              </kbd>{" "}
                              Skip -10s
                            </div>
                            <div>
                              <kbd className="px-1 py-0.5 bg-muted rounded text-xs">
                                →
                              </kbd>{" "}
                              Skip +10s
                            </div>
                            <div>
                              <kbd className="px-1 py-0.5 bg-muted rounded text-xs">
                                ↑
                              </kbd>{" "}
                              Volume +
                            </div>
                            <div>
                              <kbd className="px-1 py-0.5 bg-muted rounded text-xs">
                                ↓
                              </kbd>{" "}
                              Volume -
                            </div>
                          </div>
                        </div>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>

                {/* Action Buttons */}
                <div className="flex gap-2 justify-center pt-4 border-t">
                  <Button
                    variant="outline"
                    onClick={() => window.open(file.url, "_blank")}
                    className="gap-2"
                    disabled={!file.url}
                  >
                    <ExternalLink className="h-4 w-4" />
                    Open in New Tab
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => {
                      const link = document.createElement("a");
                      link.download = file.name || "audio-file";
                      link.href = file.url;
                      link.click();
                    }}
                    className="gap-2"
                    disabled={!file.url}
                  >
                    <Download className="h-4 w-4" />
                    Download File
                  </Button>
                </div>
              </div>
            </Card>
          </div>

          {/* Right Column - Content/Transcript */}
          <div className="space-y-6">
            <Card className="w-full p-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h4 className="text-lg font-semibold">Content</h4>
                  {file.content && (
                    <span className="text-sm text-muted-foreground">
                      {file.content.length} characters
                    </span>
                  )}
                </div>

                {file.content ? (
                  <div className="prose dark:prose-invert max-w-none">
                    <div className="max-h-[60vh] overflow-y-auto p-4 bg-muted/30 rounded-lg">
                      <ReactMarkdown
                        className="text-sm leading-relaxed"
                        components={{
                          code: ({
                            inline,
                            children,
                            className,
                            ...props
                          }: any) => (
                            <CodeBlock
                              inline={inline}
                              className={className}
                              {...props}
                            >
                              {children}
                            </CodeBlock>
                          ),
                          pre: ({ children }: any) => <>{children}</>, // Let CodeBlock handle the pre wrapper
                        }}
                      >
                        {file.content}
                      </ReactMarkdown>
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-8 text-muted-foreground">
                    <p className="mb-2">No transcript available</p>
                    <p className="text-sm">
                      Audio transcription may be processing or not configured
                    </p>
                  </div>
                )}

                {/* Metadata */}
                {file.metadata && (
                  <div className="pt-4 border-t">
                    <h5 className="font-medium mb-3">Metadata</h5>
                    <div className="space-y-4">
                      {/* Basic Audio Metadata */}
                      <div className="grid grid-cols-2 gap-2 text-sm">
                        {file.metadata.duration && (
                          <div>
                            <span className="text-muted-foreground">
                              Duration:
                            </span>{" "}
                            {formatDuration(file.metadata.duration)}
                          </div>
                        )}
                        {file.metadata.language && (
                          <div>
                            <span className="text-muted-foreground">
                              Language:
                            </span>{" "}
                            {file.metadata.language}
                          </div>
                        )}
                        {file.metadata.format && (
                          <div>
                            <span className="text-muted-foreground">
                              Format:
                            </span>{" "}
                            {file.metadata.format}
                          </div>
                        )}
                        {file.metadata.bitrate && (
                          <div>
                            <span className="text-muted-foreground">
                              Bitrate:
                            </span>{" "}
                            {file.metadata.bitrate}
                          </div>
                        )}
                        {file.metadata.sentiment && (
                          <div>
                            <span className="text-muted-foreground">
                              Sentiment:
                            </span>{" "}
                            <span
                              className={`px-2 py-1 rounded-full text-xs ${
                                file.metadata.sentiment.toLowerCase() ===
                                "positive"
                                  ? "bg-green-100 text-green-800"
                                  : file.metadata.sentiment.toLowerCase() ===
                                      "negative"
                                    ? "bg-red-100 text-red-800"
                                    : "bg-gray-100 text-gray-800"
                              }`}
                            >
                              {file.metadata.sentiment}
                            </span>
                          </div>
                        )}
                      </div>

                      {/* Keywords */}
                      {file.metadata.keywords &&
                        Array.isArray(file.metadata.keywords) &&
                        file.metadata.keywords.length > 0 && (
                          <div>
                            <span className="text-sm font-medium text-muted-foreground mb-2 block">
                              Keywords:
                            </span>
                            <div className="flex flex-wrap gap-1">
                              {file.metadata.keywords
                                .slice(0, 10)
                                .map((keyword, index) => (
                                  <span
                                    key={index}
                                    className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full"
                                  >
                                    {keyword}
                                  </span>
                                ))}
                              {file.metadata.keywords.length > 10 && (
                                <span className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full">
                                  +{file.metadata.keywords.length - 10} more
                                </span>
                              )}
                            </div>
                          </div>
                        )}

                      {/* Topics */}
                      {file.metadata.topics &&
                        Array.isArray(file.metadata.topics) &&
                        file.metadata.topics.length > 0 && (
                          <div>
                            <span className="text-sm font-medium text-muted-foreground mb-2 block">
                              Topics:
                            </span>
                            <div className="flex flex-wrap gap-1">
                              {file.metadata.topics.map((topic, index) => (
                                <span
                                  key={index}
                                  className="px-2 py-1 bg-purple-100 text-purple-800 text-xs rounded-full"
                                >
                                  {topic}
                                </span>
                              ))}
                            </div>
                          </div>
                        )}
                    </div>
                  </div>
                )}
              </div>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

// Video Preview Component
const VideoPreview = ({ file }: { file: any }) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [duration, setDuration] = useState(0);
  const [currentTime, setCurrentTime] = useState(0);
  const [volume, setVolume] = useState(1);
  const [isMuted, setIsMuted] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [playbackRate, setPlaybackRate] = useState(1);
  const [showControls, setShowControls] = useState(true);
  const [isPiPSupported, setIsPiPSupported] = useState(false);
  const [isPiPActive, setIsPiPActive] = useState(false);
  const videoRef = useRef<HTMLVideoElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const controlsTimeoutRef = useRef<ReturnType<typeof setTimeout>>();

  // Playback speed options
  const playbackSpeeds = [
    { value: 0.5, label: "0.5x" },
    { value: 0.75, label: "0.75x" },
    { value: 1, label: "1x" },
    { value: 1.25, label: "1.25x" },
    { value: 1.5, label: "1.5x" },
    { value: 2, label: "2x" },
  ];

  // Safe file size formatting
  const getFileSize = () => {
    if (!file.size) {
      return "Unknown size";
    }

    // Handle both string and number formats
    let sizeInBytes: number;
    if (typeof file.size === "string") {
      // Try to parse the string as a number
      sizeInBytes = parseInt(file.size, 10);
      if (isNaN(sizeInBytes)) {
        // If it's already formatted (e.g., "1.2 MB"), return as is
        return file.size;
      }
    } else {
      sizeInBytes = file.size;
    }

    if (sizeInBytes === 0 || isNaN(sizeInBytes)) {
      return "Unknown size";
    }

    return formatFileSize(sizeInBytes);
  };

  // Safe file extension formatting
  const getFileExtension = () => {
    return file.extension?.toUpperCase() || "Unknown format";
  };

  useEffect(() => {
    const video = videoRef.current;
    const container = containerRef.current;
    if (!video || !container) return;

    // Check Picture-in-Picture support
    setIsPiPSupported("pictureInPictureEnabled" in document);

    const handleLoadedMetadata = () => {
      setDuration(video.duration);
      setIsLoading(false);
    };

    const handleTimeUpdate = () => {
      setCurrentTime(video.currentTime);
    };

    const handlePlay = () => setIsPlaying(true);
    const handlePause = () => setIsPlaying(false);
    const handleEnded = () => {
      setIsPlaying(false);
      setCurrentTime(0);
    };

    const handleError = () => {
      setError(
        "Failed to load video file. Please check if the file is accessible."
      );
      setIsLoading(false);
    };

    const handleLoadStart = () => {
      setIsLoading(true);
      setError(null);
    };

    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement);
    };

    const handlePiPEnter = () => setIsPiPActive(true);
    const handlePiPLeave = () => setIsPiPActive(false);

    // Auto-hide controls
    const resetControlsTimeout = () => {
      if (controlsTimeoutRef.current) {
        clearTimeout(controlsTimeoutRef.current);
      }
      setShowControls(true);
      if (isPlaying) {
        controlsTimeoutRef.current = setTimeout(() => {
          setShowControls(false);
        }, 3000);
      }
    };

    const handleMouseMove = () => {
      resetControlsTimeout();
    };

    const handleMouseLeave = () => {
      if (isPlaying) {
        setShowControls(false);
      }
    };

    // Keyboard shortcuts
    const handleKeyDown = (e: KeyboardEvent) => {
      if (
        document.activeElement?.tagName === "INPUT" ||
        document.activeElement?.tagName === "TEXTAREA"
      ) {
        return;
      }

      switch (e.code) {
        case "Space":
          e.preventDefault();
          togglePlayPause();
          break;
        case "ArrowLeft":
          e.preventDefault();
          skipBackward();
          break;
        case "ArrowRight":
          e.preventDefault();
          skipForward();
          break;
        case "ArrowUp":
          e.preventDefault();
          adjustVolume(0.1);
          break;
        case "ArrowDown":
          e.preventDefault();
          adjustVolume(-0.1);
          break;
        case "KeyM":
          e.preventDefault();
          toggleMute();
          break;
        case "KeyF":
          e.preventDefault();
          toggleFullscreen();
          break;
      }
    };

    video.addEventListener("loadedmetadata", handleLoadedMetadata);
    video.addEventListener("timeupdate", handleTimeUpdate);
    video.addEventListener("play", handlePlay);
    video.addEventListener("pause", handlePause);
    video.addEventListener("ended", handleEnded);
    video.addEventListener("error", handleError);
    video.addEventListener("loadstart", handleLoadStart);
    video.addEventListener("enterpictureinpicture", handlePiPEnter);
    video.addEventListener("leavepictureinpicture", handlePiPLeave);
    document.addEventListener("fullscreenchange", handleFullscreenChange);
    document.addEventListener("keydown", handleKeyDown);
    container.addEventListener("mousemove", handleMouseMove);
    container.addEventListener("mouseleave", handleMouseLeave);

    // Make container focusable for keyboard shortcuts
    container.setAttribute("tabIndex", "0");

    return () => {
      video.removeEventListener("loadedmetadata", handleLoadedMetadata);
      video.removeEventListener("timeupdate", handleTimeUpdate);
      video.removeEventListener("play", handlePlay);
      video.removeEventListener("pause", handlePause);
      video.removeEventListener("ended", handleEnded);
      video.removeEventListener("error", handleError);
      video.removeEventListener("loadstart", handleLoadStart);
      video.removeEventListener("enterpictureinpicture", handlePiPEnter);
      video.removeEventListener("leavepictureinpicture", handlePiPLeave);
      document.removeEventListener("fullscreenchange", handleFullscreenChange);
      document.removeEventListener("keydown", handleKeyDown);
      container.removeEventListener("mousemove", handleMouseMove);
      container.removeEventListener("mouseleave", handleMouseLeave);

      if (controlsTimeoutRef.current) {
        clearTimeout(controlsTimeoutRef.current);
      }
    };
  }, [isPlaying]);

  const togglePlayPause = () => {
    const video = videoRef.current;
    if (!video) return;

    if (isPlaying) {
      video.pause();
    } else {
      video.play().catch((error) => {
        console.error("Error playing video:", error);
        setError("Failed to play video. Please try again.");
      });
    }
  };

  const handleSeek = (e: React.ChangeEvent<HTMLInputElement>) => {
    const video = videoRef.current;
    if (!video) return;

    const newTime = parseFloat(e.target.value);
    video.currentTime = newTime;
    setCurrentTime(newTime);
  };

  const handleProgressClick = (e: React.MouseEvent<HTMLDivElement>) => {
    const video = videoRef.current;
    if (!video || !duration) return;

    const rect = e.currentTarget.getBoundingClientRect();
    const clickX = e.clientX - rect.left;
    const newTime = (clickX / rect.width) * duration;

    video.currentTime = newTime;
    setCurrentTime(newTime);
  };

  const handleVolumeChange = (values: number[]) => {
    const video = videoRef.current;
    if (!video) return;

    const newVolume = values[0] / 100; // Convert from 0-100 to 0-1
    video.volume = newVolume;
    setVolume(newVolume);
    setIsMuted(newVolume === 0);
  };

  const adjustVolume = (delta: number) => {
    const video = videoRef.current;
    if (!video) return;

    // Calculate new volume with better precision handling
    let newVolume = volume + delta;

    // Round to 2 decimal places to avoid floating-point precision issues
    newVolume = Math.round(newVolume * 100) / 100;

    // Ensure bounds are exactly 0 and 1
    if (newVolume <= 0) {
      newVolume = 0;
    } else if (newVolume >= 1) {
      newVolume = 1;
    }

    // Use the handleVolumeChange function to maintain consistency
    handleVolumeChange([Math.round(newVolume * 100)]);
  };

  const toggleMute = () => {
    const video = videoRef.current;
    if (!video) return;

    if (isMuted) {
      video.volume = volume;
      setIsMuted(false);
    } else {
      video.volume = 0;
      setIsMuted(true);
    }
  };

  const skipForward = (seconds: number = 10) => {
    const video = videoRef.current;
    if (!video) return;

    video.currentTime = Math.min(video.currentTime + seconds, duration);
  };

  const skipBackward = (seconds: number = 10) => {
    const video = videoRef.current;
    if (!video) return;

    video.currentTime = Math.max(video.currentTime - seconds, 0);
  };

  const handlePlaybackRateChange = (rate: string) => {
    const video = videoRef.current;
    if (!video) return;

    const newRate = parseFloat(rate);
    video.playbackRate = newRate;
    setPlaybackRate(newRate);
  };

  const toggleFullscreen = () => {
    const container = containerRef.current;
    if (!container) return;

    if (!document.fullscreenElement) {
      container.requestFullscreen().catch((error) => {
        console.error("Error entering fullscreen:", error);
      });
    } else {
      document.exitFullscreen().catch((error) => {
        console.error("Error exiting fullscreen:", error);
      });
    }
  };

  const togglePictureInPicture = async () => {
    const video = videoRef.current;
    if (!video || !isPiPSupported) return;

    try {
      if (isPiPActive) {
        await document.exitPictureInPicture();
      } else {
        await (video as any).requestPictureInPicture();
      }
    } catch (error) {
      console.error("Error toggling Picture-in-Picture:", error);
    }
  };

  // Validate file data
  if (!file || !file.url) {
    return (
      <div className="flex justify-center py-8">
        <Card className="max-w-4xl w-full p-8 text-center">
          <p className="text-xl mb-4 text-red-600">File Not Available</p>
          <p className="text-muted-foreground mb-4">
            The video file could not be loaded. The file may have been moved or
            deleted.
          </p>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex justify-center py-8">
        <Card className="max-w-4xl w-full p-8 text-center">
          <p className="text-xl mb-4 text-red-600">Video Preview Error</p>
          <p className="text-muted-foreground mb-4">{error}</p>
          <div className="flex gap-2 justify-center">
            <Button
              variant="outline"
              onClick={() => window.open(file.url, "_blank")}
              className="gap-2"
              disabled={!file.url}
            >
              <ExternalLink className="h-4 w-4" />
              Open in New Tab
            </Button>
            <Button
              variant="outline"
              onClick={() => {
                const link = document.createElement("a");
                link.download = file.name || "video-file";
                link.href = file.url;
                link.click();
              }}
              className="gap-2"
              disabled={!file.url}
            >
              <Download className="h-4 w-4" />
              Download File
            </Button>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className="py-8 px-4">
      <div className="max-w-7xl mx-auto">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Left Column - Video Player */}
          <div className="space-y-6">
            <Card className="w-full p-6 media-player-container">
              <div className="space-y-6">
                {/* File Info */}
                <div className="text-center">
                  <h3 className="text-lg font-semibold mb-2" title={file.name}>
                    {file.name || "Unknown Video File"}
                  </h3>
                  <div className="flex justify-center gap-4 text-sm text-muted-foreground flex-wrap">
                    <span>Format: {getFileExtension()}</span>
                    <span>Size: {getFileSize()}</span>
                    {duration > 0 && (
                      <span>Duration: {formatDuration(duration)}</span>
                    )}
                  </div>
                </div>

                {/* Video Container */}
                <div
                  ref={containerRef}
                  className={`relative bg-black rounded-lg overflow-hidden group cursor-pointer ${
                    isFullscreen ? "fixed inset-0 z-50" : "aspect-video"
                  }`}
                  onClick={togglePlayPause}
                  role="button"
                  aria-label="Video player"
                  tabIndex={0}
                >
                  {/* Video Element */}
                  <video
                    ref={videoRef}
                    src={file.url}
                    preload="metadata"
                    className="w-full h-full object-contain"
                    poster={file.thumbnail}
                    aria-label={`Video: ${file.name}`}
                  />

                  {/* Loading Overlay */}
                  {isLoading && (
                    <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50">
                      <div className="text-center text-white">
                        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"></div>
                        <p className="text-lg">Loading video...</p>
                      </div>
                    </div>
                  )}

                  {/* Play Button Overlay */}
                  {!isPlaying && !isLoading && (
                    <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-30 group-hover:bg-opacity-50 transition-all duration-200">
                      <div className="bg-white bg-opacity-90 rounded-full p-4 group-hover:bg-opacity-100 transition-all duration-200">
                        <Play className="h-12 w-12 text-black ml-1" />
                      </div>
                    </div>
                  )}

                  {/* Enhanced Video Controls Overlay */}
                  <div
                    className={`absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black via-black/80 to-transparent p-4 transition-opacity duration-300 ${
                      showControls || !isPlaying ? "opacity-100" : "opacity-0"
                    }`}
                  >
                    {/* Progress Bar with Click-to-Seek */}
                    <div className="mb-4">
                      <div
                        className="relative w-full h-2 bg-white/30 rounded-lg cursor-pointer group"
                        onClick={handleProgressClick}
                        role="progressbar"
                        aria-valuemin={0}
                        aria-valuemax={duration}
                        aria-valuenow={currentTime}
                        aria-label="Video progress"
                      >
                        <div
                          className="absolute top-0 left-0 h-full bg-red-500 rounded-lg transition-all duration-150"
                          style={{
                            width: `${
                              duration ? (currentTime / duration) * 100 : 0
                            }%`,
                          }}
                        />
                        <div
                          className="absolute top-1/2 -translate-y-1/2 w-4 h-4 bg-red-500 rounded-full shadow-md opacity-0 group-hover:opacity-100 transition-opacity duration-150"
                          style={{
                            left: `${
                              duration ? (currentTime / duration) * 100 : 0
                            }%`,
                            transform: "translateX(-50%) translateY(-50%)",
                          }}
                        />
                      </div>
                      <div className="flex justify-between text-xs text-white mt-2">
                        <span>{formatDuration(currentTime)}</span>
                        <span>{formatDuration(duration)}</span>
                      </div>
                    </div>

                    {/* Control Buttons */}
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        {/* Skip Backward */}
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            skipBackward(10);
                          }}
                          disabled={isLoading}
                          className="text-white hover:bg-white/20"
                          title="Skip backward 10 seconds (←)"
                          aria-label="Skip backward 10 seconds"
                        >
                          <SkipBack className="h-4 w-4" />
                        </Button>

                        {/* Play/Pause */}
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            togglePlayPause();
                          }}
                          disabled={isLoading}
                          className="text-white hover:bg-white/20"
                          title={isPlaying ? "Pause (Space)" : "Play (Space)"}
                          aria-label={isPlaying ? "Pause video" : "Play video"}
                        >
                          {isPlaying ? (
                            <Pause className="h-5 w-5" />
                          ) : (
                            <Play className="h-5 w-5" />
                          )}
                        </Button>

                        {/* Skip Forward */}
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            skipForward(10);
                          }}
                          disabled={isLoading}
                          className="text-white hover:bg-white/20"
                          title="Skip forward 10 seconds (→)"
                          aria-label="Skip forward 10 seconds"
                        >
                          <SkipForward className="h-4 w-4" />
                        </Button>

                        {/* Volume Control */}
                        <div className="flex items-center gap-2 ml-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation();
                              toggleMute();
                            }}
                            disabled={isLoading}
                            className="text-white hover:bg-white/20"
                            title={isMuted ? "Unmute (M)" : "Mute (M)"}
                            aria-label={isMuted ? "Unmute video" : "Mute video"}
                          >
                            {isMuted ? (
                              <VolumeX className="h-4 w-4" />
                            ) : (
                              <Volume2 className="h-4 w-4" />
                            )}
                          </Button>
                          <div className="video-volume w-24 flex items-center">
                            <Slider
                              value={[isMuted ? 0 : Math.round(volume * 100)]}
                              onValueChange={handleVolumeChange}
                              max={100}
                              step={1}
                              disabled={isLoading}
                              className="w-full cursor-pointer"
                              aria-label="Volume control"
                              onClick={(e) => e.stopPropagation()}
                            />
                          </div>
                          <span className="text-xs text-white w-8">
                            {Math.round((isMuted ? 0 : volume) * 100)}%
                          </span>
                        </div>
                      </div>

                      <div className="flex items-center gap-2">
                        {/* Playback Speed */}
                        <div
                          className="flex items-center gap-2"
                          onClick={(e) => e.stopPropagation()}
                        >
                          <Select
                            value={playbackRate.toString()}
                            onValueChange={handlePlaybackRateChange}
                          >
                            <SelectTrigger className="w-16 h-8 bg-white/20 border-white/30 text-white text-xs">
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              {playbackSpeeds.map((speed) => (
                                <SelectItem
                                  key={speed.value}
                                  value={speed.value.toString()}
                                >
                                  {speed.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>

                        {/* Picture-in-Picture */}
                        {isPiPSupported && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation();
                              togglePictureInPicture();
                            }}
                            className="text-white hover:bg-white/20"
                            title="Picture-in-Picture"
                            aria-label="Toggle Picture-in-Picture"
                          >
                            <PictureInPicture className="h-4 w-4" />
                          </Button>
                        )}

                        {/* Fullscreen */}
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            toggleFullscreen();
                          }}
                          className="text-white hover:bg-white/20"
                          title={
                            isFullscreen
                              ? "Exit fullscreen (F)"
                              : "Fullscreen (F)"
                          }
                          aria-label={
                            isFullscreen
                              ? "Exit fullscreen"
                              : "Enter fullscreen"
                          }
                        >
                          {isFullscreen ? (
                            <Minimize className="h-4 w-4" />
                          ) : (
                            <Maximize className="h-4 w-4" />
                          )}
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Keyboard Shortcuts Info */}
                <div className="flex justify-center pt-2 border-t">
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="gap-2 text-muted-foreground"
                        >
                          <Info className="h-4 w-4" />
                          Keyboard Shortcuts
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent side="top" className="max-w-xs">
                        <div className="space-y-1 text-sm">
                          <div className="font-semibold mb-2">
                            Video Player Shortcuts
                          </div>
                          <div className="grid grid-cols-2 gap-2 text-xs">
                            <div>
                              <kbd className="px-1 py-0.5 bg-muted rounded text-xs">
                                Space
                              </kbd>{" "}
                              Play/Pause
                            </div>
                            <div>
                              <kbd className="px-1 py-0.5 bg-muted rounded text-xs">
                                F
                              </kbd>{" "}
                              Fullscreen
                            </div>
                            <div>
                              <kbd className="px-1 py-0.5 bg-muted rounded text-xs">
                                ←
                              </kbd>{" "}
                              Skip -10s
                            </div>
                            <div>
                              <kbd className="px-1 py-0.5 bg-muted rounded text-xs">
                                →
                              </kbd>{" "}
                              Skip +10s
                            </div>
                            <div>
                              <kbd className="px-1 py-0.5 bg-muted rounded text-xs">
                                ↑
                              </kbd>{" "}
                              Volume +
                            </div>
                            <div>
                              <kbd className="px-1 py-0.5 bg-muted rounded text-xs">
                                ↓
                              </kbd>{" "}
                              Volume -
                            </div>
                            <div>
                              <kbd className="px-1 py-0.5 bg-muted rounded text-xs">
                                M
                              </kbd>{" "}
                              Mute
                            </div>
                            <div>
                              <span className="text-muted-foreground">
                                + PiP support
                              </span>
                            </div>
                          </div>
                        </div>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>

                {/* Action Buttons */}
                <div className="flex gap-2 justify-center pt-4 border-t">
                  <Button
                    variant="outline"
                    onClick={() => window.open(file.url, "_blank")}
                    className="gap-2"
                    disabled={!file.url}
                  >
                    <ExternalLink className="h-4 w-4" />
                    Open in New Tab
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => {
                      const link = document.createElement("a");
                      link.download = file.name || "video-file";
                      link.href = file.url;
                      link.click();
                    }}
                    className="gap-2"
                    disabled={!file.url}
                  >
                    <Download className="h-4 w-4" />
                    Download File
                  </Button>
                </div>
              </div>
            </Card>
          </div>

          {/* Right Column - Content/Transcript */}
          <div className="space-y-6">
            <Card className="w-full p-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h4 className="text-lg font-semibold">Content</h4>
                  {file.content && (
                    <span className="text-sm text-muted-foreground">
                      {file.content.length} characters
                    </span>
                  )}
                </div>

                {file.content ? (
                  <div className="prose dark:prose-invert max-w-none">
                    <div className="max-h-[60vh] overflow-y-auto p-4 bg-muted/30 rounded-lg">
                      <ReactMarkdown
                        className="text-sm leading-relaxed"
                        components={{
                          code: ({
                            inline,
                            children,
                            className,
                            ...props
                          }: any) => (
                            <CodeBlock
                              inline={inline}
                              className={className}
                              {...props}
                            >
                              {children}
                            </CodeBlock>
                          ),
                          pre: ({ children }: any) => <>{children}</>, // Let CodeBlock handle the pre wrapper
                        }}
                      >
                        {file.content}
                      </ReactMarkdown>
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-8 text-muted-foreground">
                    <p className="mb-2">No transcript available</p>
                    <p className="text-sm">
                      Video transcription may be processing or not configured
                    </p>
                  </div>
                )}

                {/* Metadata */}
                {file.metadata && (
                  <div className="pt-4 border-t">
                    <h5 className="font-medium mb-3">Metadata</h5>
                    <div className="space-y-4">
                      {/* Basic Video Metadata */}
                      <div className="grid grid-cols-2 gap-2 text-sm">
                        {file.metadata.duration && (
                          <div>
                            <span className="text-muted-foreground">
                              Duration:
                            </span>{" "}
                            {formatDuration(file.metadata.duration)}
                          </div>
                        )}
                        {file.metadata.language && (
                          <div>
                            <span className="text-muted-foreground">
                              Language:
                            </span>{" "}
                            {file.metadata.language}
                          </div>
                        )}
                        {file.metadata.resolution && (
                          <div>
                            <span className="text-muted-foreground">
                              Resolution:
                            </span>{" "}
                            {file.metadata.resolution}
                          </div>
                        )}
                        {file.metadata.framerate && (
                          <div>
                            <span className="text-muted-foreground">
                              Frame Rate:
                            </span>{" "}
                            {file.metadata.framerate}
                          </div>
                        )}
                        {file.metadata.bitrate && (
                          <div>
                            <span className="text-muted-foreground">
                              Bitrate:
                            </span>{" "}
                            {file.metadata.bitrate}
                          </div>
                        )}
                        {file.metadata.codec && (
                          <div>
                            <span className="text-muted-foreground">
                              Codec:
                            </span>{" "}
                            {file.metadata.codec}
                          </div>
                        )}
                        {file.metadata.sentiment && (
                          <div>
                            <span className="text-muted-foreground">
                              Sentiment:
                            </span>{" "}
                            <span
                              className={`px-2 py-1 rounded-full text-xs ${
                                file.metadata.sentiment.toLowerCase() ===
                                "positive"
                                  ? "bg-green-100 text-green-800"
                                  : file.metadata.sentiment.toLowerCase() ===
                                      "negative"
                                    ? "bg-red-100 text-red-800"
                                    : "bg-gray-100 text-gray-800"
                              }`}
                            >
                              {file.metadata.sentiment}
                            </span>
                          </div>
                        )}
                      </div>

                      {/* Keywords */}
                      {file.metadata.keywords &&
                        Array.isArray(file.metadata.keywords) &&
                        file.metadata.keywords.length > 0 && (
                          <div>
                            <span className="text-sm font-medium text-muted-foreground mb-2 block">
                              Keywords:
                            </span>
                            <div className="flex flex-wrap gap-1">
                              {file.metadata.keywords
                                .slice(0, 10)
                                .map((keyword, index) => (
                                  <span
                                    key={index}
                                    className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full"
                                  >
                                    {keyword}
                                  </span>
                                ))}
                              {file.metadata.keywords.length > 10 && (
                                <span className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full">
                                  +{file.metadata.keywords.length - 10} more
                                </span>
                              )}
                            </div>
                          </div>
                        )}

                      {/* Topics */}
                      {file.metadata.topics &&
                        Array.isArray(file.metadata.topics) &&
                        file.metadata.topics.length > 0 && (
                          <div>
                            <span className="text-sm font-medium text-muted-foreground mb-2 block">
                              Topics:
                            </span>
                            <div className="flex flex-wrap gap-1">
                              {file.metadata.topics.map((topic, index) => (
                                <span
                                  key={index}
                                  className="px-2 py-1 bg-purple-100 text-purple-800 text-xs rounded-full"
                                >
                                  {topic}
                                </span>
                              ))}
                            </div>
                          </div>
                        )}

                      {/* Visual Analysis */}
                      {file.metadata.objects &&
                        Array.isArray(file.metadata.objects) &&
                        file.metadata.objects.length > 0 && (
                          <div>
                            <span className="text-sm font-medium text-muted-foreground mb-2 block">
                              Objects Detected:
                            </span>
                            <div className="flex flex-wrap gap-1">
                              {file.metadata.objects
                                .slice(0, 8)
                                .map((object, index) => (
                                  <span
                                    key={index}
                                    className="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full"
                                  >
                                    {object}
                                  </span>
                                ))}
                              {file.metadata.objects.length > 8 && (
                                <span className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full">
                                  +{file.metadata.objects.length - 8} more
                                </span>
                              )}
                            </div>
                          </div>
                        )}

                      {/* Brands */}
                      {file.metadata.brands &&
                        Array.isArray(file.metadata.brands) &&
                        file.metadata.brands.length > 0 && (
                          <div>
                            <span className="text-sm font-medium text-muted-foreground mb-2 block">
                              Brands Detected:
                            </span>
                            <div className="flex flex-wrap gap-1">
                              {file.metadata.brands.map((brand, index) => (
                                <span
                                  key={index}
                                  className="px-2 py-1 bg-orange-100 text-orange-800 text-xs rounded-full"
                                >
                                  {brand}
                                </span>
                              ))}
                            </div>
                          </div>
                        )}
                    </div>
                  </div>
                )}
              </div>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

// Utility function to strip markdown formatting from table cell content
const stripMarkdownFormatting = (text: string): string => {
  return (
    text
      // Remove bold formatting (**text** or __text__)
      .replace(/\*\*(.*?)\*\*/g, "$1")
      .replace(/__(.*?)__/g, "$1")
      // Remove italic formatting (*text* or _text_) - but be careful not to break other formatting
      .replace(/(?<!\*)\*([^*]+)\*(?!\*)/g, "$1")
      .replace(/(?<!_)_([^_]+)_(?!_)/g, "$1")
      // Remove inline code formatting (`text`)
      .replace(/`([^`]+)`/g, "$1")
      // Remove strikethrough formatting (~~text~~)
      .replace(/~~(.*?)~~/g, "$1")
  );
};

// Custom table parser for markdown tables
const parseMarkdownTable = (content: string) => {
  const lines = content.split("\n");
  const tables: Array<{
    startIndex: number;
    endIndex: number;
    headers: string[];
    rows: string[][];
    alignments: string[];
  }> = [];

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim();
    if (line.includes("|") && line.split("|").length > 2) {
      // Potential table start
      const nextLine = lines[i + 1]?.trim();
      if (nextLine && nextLine.includes("|") && nextLine.includes("-")) {
        // This is a table header with separator
        const headers = line
          .split("|")
          .map((h) => stripMarkdownFormatting(h.trim()))
          .filter((h) => h);
        const separatorCells = nextLine
          .split("|")
          .map((s) => s.trim())
          .filter((s) => s);
        const alignments = separatorCells.map((cell) => {
          if (cell.startsWith(":") && cell.endsWith(":")) return "center";
          if (cell.endsWith(":")) return "right";
          return "left";
        });

        const rows: string[][] = [];
        let j = i + 2;

        // Parse table rows
        while (j < lines.length && lines[j].trim().includes("|")) {
          const rowCells = lines[j]
            .split("|")
            .map((c) => stripMarkdownFormatting(c.trim()))
            .filter((c) => c);
          if (rowCells.length > 0) {
            rows.push(rowCells);
          }
          j++;
        }

        if (rows.length > 0) {
          tables.push({
            startIndex: i,
            endIndex: j - 1,
            headers,
            rows,
            alignments,
          });
        }

        i = j - 1; // Skip processed lines
      }
    }
  }

  return tables;
};

// Function to render content with tables
const renderContentWithTables = (content: string) => {
  const tables = parseMarkdownTable(content);

  if (tables.length === 0) {
    // No tables found, render normally with code block support
    return (
      <ReactMarkdown
        className="markdown"
        components={{
          code: ({ inline, children, className, ...props }: any) => (
            <CodeBlock inline={inline} className={className} {...props}>
              {children}
            </CodeBlock>
          ),
          pre: ({ children }: any) => <>{children}</>, // Let CodeBlock handle the pre wrapper
        }}
      >
        {content}
      </ReactMarkdown>
    );
  }

  // Split content and render with tables
  const lines = content.split("\n");
  const elements: React.ReactNode[] = [];
  let lastIndex = 0;

  tables.forEach((table, tableIndex) => {
    // Add content before table
    if (table.startIndex > lastIndex) {
      const beforeTableContent = lines
        .slice(lastIndex, table.startIndex)
        .join("\n");
      if (beforeTableContent.trim()) {
        elements.push(
          <ReactMarkdown
            key={`before-${tableIndex}`}
            className="markdown"
            components={{
              code: ({ inline, children, className, ...props }: any) => (
                <CodeBlock inline={inline} className={className} {...props}>
                  {children}
                </CodeBlock>
              ),
              pre: ({ children }: any) => <>{children}</>, // Let CodeBlock handle the pre wrapper
            }}
          >
            {beforeTableContent}
          </ReactMarkdown>
        );
      }
    }

    // Add table
    elements.push(
      <div key={`table-${tableIndex}`} className="my-4 overflow-x-auto">
        <Table>
          <TableHeader>
            <TableRow>
              {table.headers.map((header, i) => (
                <TableHead
                  key={i}
                  style={{ textAlign: table.alignments[i] as any }}
                >
                  {header}
                </TableHead>
              ))}
            </TableRow>
          </TableHeader>
          <TableBody>
            {table.rows.map((row, rowIndex) => (
              <TableRow key={rowIndex}>
                {row.map((cell, cellIndex) => (
                  <TableCell
                    key={cellIndex}
                    style={{ textAlign: table.alignments[cellIndex] as any }}
                  >
                    {cell}
                  </TableCell>
                ))}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    );

    lastIndex = table.endIndex + 1;
  });

  // Add remaining content after last table
  if (lastIndex < lines.length) {
    const afterTableContent = lines.slice(lastIndex).join("\n");
    if (afterTableContent.trim()) {
      elements.push(
        <ReactMarkdown
          key="after-last"
          className="markdown"
          components={{
            code: ({ inline, children, className, ...props }: any) => (
              <CodeBlock inline={inline} className={className} {...props}>
                {children}
              </CodeBlock>
            ),
            pre: ({ children }: any) => <>{children}</>, // Let CodeBlock handle the pre wrapper
          }}
        >
          {afterTableContent}
        </ReactMarkdown>
      );
    }
  }

  return <>{elements}</>;
};

export default function FilePage({
  workspaceSlug,
  tenantId,
  file,
  fileId,
  usageSummary,
  permission,
}) {
  const { t } = useLanguage();
  const router = useRouter();
  const searchParams: any = useSearchParams();

  const [isLoading, setIsLoading] = useState(false);
  const pageId = searchParams.get("page") || "";

  const [newFolderName, setNewFolderName] = useState(file?.name);
  const [isCreateFolderOpen, setIsCreateFolderOpen] = useState(false);
  const [fileDeleteDialog, setFileDeleteDialog] = useState<{
    isOpen: boolean;
    fileId: string;
    fileName: string;
  }>({ isOpen: false, fileId: "", fileName: "" });
  const [isFileDeleteLoading, setIsFileDeleteLoading] = useState(false);

  // Function to sanitize HTML content for safe rendering
  const sanitizeHtmlContent = (htmlContent: string): string => {
    // Basic sanitization - remove script tags and other potentially harmful elements
    let sanitized = htmlContent
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, "")
      .replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, "")
      .replace(/<object\b[^<]*(?:(?!<\/object>)<[^<]*)*<\/object>/gi, "")
      .replace(/<embed\b[^<]*(?:(?!<\/embed>)<[^<]*)*<\/embed>/gi, "")
      .replace(/<form\b[^<]*(?:(?!<\/form>)<[^<]*)*<\/form>/gi, "")
      .replace(/on\w+\s*=\s*["'][^"']*["']/gi, ""); // Remove event handlers

    // If content doesn't have proper HTML structure, wrap it
    if (!sanitized.includes("<html") && !sanitized.includes("<body")) {
      // Check if it's just a fragment
      if (sanitized.trim().startsWith("<") && sanitized.trim().endsWith(">")) {
        // It's HTML fragment, wrap it properly
        sanitized = `<div>${sanitized}</div>`;
      } else {
        // It might be plain text, wrap in paragraph
        sanitized = `<p>${sanitized}</p>`;
      }
    }

    return sanitized;
  };

  // Function to detect if content is likely markdown
  const isMarkdownContent = (content: string): boolean => {
    const markdownPatterns = [
      /^#{1,6}\s+/m, // Headers
      /\*\*.*\*\*/, // Bold
      /\*.*\*/, // Italic
      /\[.*\]\(.*\)/, // Links
      /^[-*+]\s+/m, // Lists
      /```/, // Code blocks
      /`.*`/, // Inline code
    ];

    return markdownPatterns.some((pattern) => pattern.test(content));
  };
  const { handleRenameFile, handleDeleteFile } = useFileOperations({
    workspaceSlug,
    tenantId,
    pageId: pageId as string,
    setIsLoading,
    usageSummary,
  });
  const isSyncedWithCloud = file?.gDriveFileId || file?.oneDriveFileId;
  const userId = getCookie("userId") || "";

  // Show file delete confirmation dialog
  const showFileDeleteConfirmation = (fileId: string, fileName: string) => {
    setFileDeleteDialog({ isOpen: true, fileId, fileName });
  };

  // Delete file after confirmation
  const handleConfirmedDeleteFile = async () => {
    setIsFileDeleteLoading(true);
    try {
      const success = await handleDeleteFile(fileDeleteDialog.fileId);

      if (success) {
        setFileDeleteDialog({ isOpen: false, fileId: "", fileName: "" });

        // Navigate back after successful deletion
        if (file?.parentId) {
          // If file is in a folder, go back to that folder
          router.push(
            `/workspace/${workspaceSlug}/folder/${file.parentId}?page=${pageId}`
          );
        } else {
          // If file is in a page, go back to the workspace
          router.push(`/workspace/${workspaceSlug}`);
        }
      }
    } catch (error) {
      console.error("Error deleting file:", error);
    } finally {
      setIsFileDeleteLoading(false);
    }
  };

  // Get vectorization status badge
  const getVectorizationStatusBadge = (status?: string) => {
    switch (status) {
      case "PENDING":
        return (
          <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
            {t("common.indexingStatus.pending")}
          </span>
        );
      case "PROCESSING":
        return (
          <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
            {t("common.indexingStatus.processing")}
          </span>
        );
      case "COMPLETED":
        return (
          <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
            {t("common.indexingStatus.indexed")}
          </span>
        );
      case "FAILED":
        return (
          <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
            {t("common.indexingStatus.failed")}
          </span>
        );
      default:
        return (
          <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200">
            Unknown
          </span>
        );
    }
  };

  // Handle manual vectorization
  const handleManualVectorization = async () => {
    try {
      toast.loading("Starting vectorization process...");

      await workspaceChatService.uploadForIndexing({
        userId: userId,
        document_path: file.url,
        workspaceSlug: workspaceSlug,
        tenantId: tenantId,
        pageId,
        file_id: fileId,
      });

      toast.remove();
      toast.success("Vectorization process started successfully!");
    } catch (error) {
      toast.remove();
      toast.error("Failed to start vectorization process. Please try again.");
      console.error("Error starting vectorization:", error);
    }
  };

  // Get file preview based on extension
  const getFilePreview = () => {
    if (!file) return null;

    const extension = file.extension?.toLowerCase();

    switch (extension) {
      case "jpg":
      case "jpeg":
      case "png":
      case "gif":
        return (
          <div className="flex justify-center py-8">
            <div className="rounded-lg overflow-hidden border max-w-2xl">
              <div className="aspect-video relative">
                <Image
                  src={file.url}
                  alt={file.name}
                  className="object-contain"
                  height={500}
                  width={500}
                  loading="lazy"
                  onError={(e) => {
                    console.error("Error loading image:", e);
                    e.currentTarget.src = "/placeholder-image.png";
                  }}
                />
              </div>
            </div>
          </div>
        );
      case "pdf":
        return (
          <div className="flex justify-center py-8">
            <div className="rounded-lg overflow-hidden border w-full">
              <div className="relative">
                <iframe
                  style={{
                    width: "100%",
                    height: "80vh",
                    border: "none",
                    paddingBottom: 16,
                  }}
                  src={`${file.url}`}
                  title={file.name}
                  onLoad={(e) => {
                    // Hide loading message when iframe is loaded
                    const loadingEl =
                      e.currentTarget.parentElement?.querySelector(
                        ".loading-message"
                      ) as HTMLElement | null;
                    if (loadingEl) loadingEl.style.display = "none";
                  }}
                />
                <div className="loading-message absolute inset-0 flex items-center justify-center bg-background/80">
                  <div className="text-center">
                    <p className="text-muted-foreground mb-4">
                      {t("workspace.loadingDocumentContent")}
                    </p>
                    <div className="flex gap-2 justify-center">
                      <Button
                        variant="outline"
                        onClick={() => window.open(file.url, "_blank")}
                        className="gap-2"
                      >
                        <ExternalLink className="h-4 w-4" />
                        {t("workspace.openInNewTab")}
                      </Button>
                      <Button
                        variant="outline"
                        onClick={() => downloadURI(file.url, file.name)}
                        className="gap-2"
                      >
                        <Download className="h-4 w-4" />
                        {t("workspace.downloadDocument")}
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        );
      case "doc":
      case "docx":
      case "ppt":
      case "pptx":
      case "xls":
      case "xlsx":
      case "ods":
      case "odt":
      case "odp":
      case "pptm":
      case "xlsm":
      case "docm":
      case "txt":
        return (
          <div className="p-6 max-w-4xl mx-auto">
            <Card className="p-6">
              {file.url ? (
                <div className="prose dark:prose-invert max-w-none">
                  <iframe
                    src={`https://view.officeapps.live.com/op/embed.aspx?src=${encodeURIComponent(
                      file.url
                    )}`}
                    style={{
                      width: "100%",
                      height: "80vh",
                      border: "none",
                      paddingBottom: 16,
                    }}
                    title={file.name}
                    onLoad={(e) => {
                      // Hide loading message when iframe is loaded
                      const loadingEl =
                        e.currentTarget.parentElement?.querySelector(
                          ".loading-message"
                        ) as HTMLElement | null;
                      if (loadingEl) loadingEl.style.display = "none";
                    }}
                  ></iframe>
                </div>
              ) : (
                <div className="text-center text-muted-foreground">
                  <p>{t("workspace.documentNotAvailable")}</p>
                  <p className="text-sm mt-2">
                    {t("workspace.documentCouldNotBeLoaded")}
                  </p>
                </div>
              )}
            </Card>
          </div>
        );
      case "md":
        return (
          <div className="p-6 max-w-4xl mx-auto">
            <Card className="p-6">
              <div className="prose dark:prose-invert max-w-none">
                {file.content ? (
                  renderContentWithTables(file.content)
                ) : (
                  <div className="flex items-center justify-center min-h-[200px]">
                    <div className="text-center">
                      <p className="text-muted-foreground mb-4">
                        {t("workspace.loadingMarkdownContent")}
                      </p>
                      <Button
                        variant="outline"
                        onClick={() => window.open(file.url, "_blank")}
                        className="gap-2"
                      >
                        <Download className="h-4 w-4" />
                        {t("workspace.viewRawMarkdown")}
                      </Button>
                    </div>
                  </div>
                )}
              </div>
            </Card>
          </div>
        );

      case "csv":
        return (
          <div className="p-6 max-w-4xl mx-auto">
            <Card className="p-6">
              {file.url ? (
                file.content ? (
                  <div className="overflow-auto max-h-[70vh]">
                    <div dangerouslySetInnerHTML={{ __html: file.content }} />
                  </div>
                ) : (
                  <div className="flex items-center justify-center min-h-[200px]">
                    <div className="text-center">
                      <p className="text-muted-foreground mb-4">
                        {t("workspace.loadingCsvContent")}
                      </p>
                      <Button
                        variant="outline"
                        onClick={() => window.open(file.url, "_blank")}
                        className="gap-2"
                      >
                        <Download className="h-4 w-4" />
                        {t("workspace.downloadCsvFile")}
                      </Button>
                    </div>
                  </div>
                )
              ) : (
                <div className="text-center text-muted-foreground">
                  <p>{t("workspace.csvFileNotAvailable")}</p>
                  <p className="text-sm mt-2">
                    {t("workspace.fileCouldNotBeLoaded")}
                  </p>
                </div>
              )}
            </Card>
          </div>
        );
      case "json":
        return (
          <div className="p-6 max-w-4xl mx-auto">
            <Card className="p-6">
              {file.url ? (
                file.content ? (
                  <pre className="whitespace-pre-wrap text-sm p-4 bg-muted rounded-md overflow-auto max-h-[70vh]">
                    {file.content}
                  </pre>
                ) : (
                  <div className="flex items-center justify-center min-h-[200px]">
                    <div className="text-center">
                      <p className="text-muted-foreground mb-4">
                        {t("workspace.loadingJsonContent")}
                      </p>
                      <Button
                        variant="outline"
                        onClick={() => window.open(file.url, "_blank")}
                        className="gap-2"
                      >
                        <Download className="h-4 w-4" />
                        {t("workspace.downloadJsonFile")}
                      </Button>
                    </div>
                  </div>
                )
              ) : (
                <div className="text-center text-muted-foreground">
                  <p>{t("workspace.jsonFileNotAvailable")}</p>
                  <p className="text-sm mt-2">
                    {t("workspace.fileCouldNotBeLoaded")}
                  </p>
                </div>
              )}
            </Card>
          </div>
        );
      case "html":
      case "htm":
        return (
          <div className="p-6 max-w-4xl mx-auto">
            <Card className="p-6">
              {file.content ? (
                // For URL-imported HTML content, render with proper styling
                <div className="prose dark:prose-invert max-w-none">
                  {/* Check if content might be markdown and show a notice */}
                  {isMarkdownContent(file.content) && (
                    <div className="mb-4 p-3 bg-blue-50 dark:bg-blue-950 border border-blue-200 dark:border-blue-800 rounded-md">
                      <p className="text-sm text-blue-700 dark:text-blue-300">
                        <strong>Note:</strong> This content appears to contain
                        Markdown formatting. For better display, consider
                        re-importing as Markdown format.
                      </p>
                    </div>
                  )}

                  <div
                    className="imported-html-content overflow-auto max-h-[70vh]"
                    dangerouslySetInnerHTML={{
                      __html: sanitizeHtmlContent(file.content),
                    }}
                  />

                  {/* Show source URL if available */}
                  {file.metadata?.sourceUrl && (
                    <div className="mt-4 pt-4 border-t text-sm text-muted-foreground">
                      <p>
                        <strong>Source:</strong>{" "}
                        <a
                          href={file.metadata.sourceUrl}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-primary hover:underline"
                        >
                          {file.metadata.sourceUrl}
                        </a>
                      </p>
                      {file.metadata.importedAt && (
                        <p className="mt-1">
                          <strong>Imported:</strong>{" "}
                          {new Date(file.metadata.importedAt).toLocaleString()}
                        </p>
                      )}
                      {file.metadata?.format && (
                        <p className="mt-1">
                          <strong>Format:</strong> {file.metadata.format}
                        </p>
                      )}
                      {file.metadata?.contentCleaningLevel && (
                        <p className="mt-1">
                          <strong>Cleaning Level:</strong>{" "}
                          {file.metadata.contentCleaningLevel}
                        </p>
                      )}
                    </div>
                  )}
                </div>
              ) : file.url ? (
                // Fallback to iframe for files without content (e.g., regular HTML files)
                <div className="flex justify-center">
                  <div className="rounded-lg overflow-hidden border w-full">
                    <div className="relative">
                      <iframe
                        style={{
                          width: "100%",
                          height: "80vh",
                          border: "none",
                          paddingBottom: 16,
                        }}
                        src={`${file.url}`}
                        title={file.name}
                        sandbox="allow-same-origin allow-scripts"
                        onLoad={(e) => {
                          // Hide loading message when iframe is loaded
                          const loadingEl =
                            e.currentTarget.parentElement?.querySelector(
                              ".loading-message"
                            ) as HTMLElement | null;
                          if (loadingEl) loadingEl.style.display = "none";
                        }}
                      />
                      <div className="loading-message absolute inset-0 flex items-center justify-center bg-background/80">
                        <div className="text-center">
                          <p className="text-muted-foreground mb-4">
                            {t("workspace.loadingHtmlContent")}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="flex items-center justify-center min-h-[200px]">
                  <div className="text-center">
                    <p className="text-muted-foreground mb-4">
                      {t("workspace.htmlFileNotAvailable")}
                    </p>
                    <p className="text-sm text-muted-foreground mt-2 mb-4">
                      {t("workspace.fileCouldNotBeLoaded")}
                    </p>
                  </div>
                </div>
              )}

              {/* Action buttons */}
              <div className="mt-6 pt-4 border-t flex gap-2 justify-center">
                <Button
                  variant="outline"
                  onClick={() => window.open(file.url, "_blank")}
                  className="gap-2"
                >
                  <ExternalLink className="h-4 w-4" />
                  {t("workspace.openInNewTab")}
                </Button>
                <Button
                  variant="outline"
                  onClick={() => downloadURI(file.url, file.name)}
                  className="gap-2"
                >
                  <Download className="h-4 w-4" />
                  {t("workspace.downloadHtmlFile")}
                </Button>
              </div>
            </Card>
          </div>
        );

      // Audio file cases
      case "mp3":
      case "wav":
      case "m4a":
      case "ogg":
      case "flac":
      case "aac":
        return <AudioPreview file={file} />;

      // Video file cases
      case "mp4":
      case "avi":
      case "mov":
      case "webm":
      case "mkv":
      case "flv":
      case "wmv":
      case "m4v":
      case "3gp":
        return <VideoPreview file={file} />;

      default:
        return (
          <div className="flex justify-center py-8">
            <div className="rounded-lg overflow-hidden border max-w-lg p-8 text-center">
              <p className="text-xl mb-4">
                {t("workspace.fileTypeCannotBePreviewedTitle")}
              </p>
              <p className="text-muted-foreground mb-4">
                {t("workspace.fileTypeCannotBePreviewedDesc", {
                  extension: extension ? `.${extension}` : "",
                })}
              </p>
              <div className="flex gap-2 justify-center">
                <Button
                  variant="outline"
                  onClick={() => window.open(file.url, "_blank")}
                  className="gap-2"
                >
                  <ExternalLink className="h-4 w-4" />
                  {t("workspace.openInNewTab")}
                </Button>
                <Button
                  variant="outline"
                  onClick={() => downloadURI(file.url, file.name)}
                  className="gap-2"
                >
                  <Download className="h-4 w-4" />
                  {t("workspace.downloadFile")}
                </Button>
              </div>
            </div>
          </div>
        );
    }
  };

  const handleRename = async () => {
    if (await handleRenameFile(fileId, newFolderName)) {
      setNewFolderName("");
    }
  };
  function downloadURI(uri: string, name: string) {
    let cleanedUrl = uri.split("?")[0];

    try {
      // Method 1: Direct download using anchor element with original URL (including SAS tokens)
      const link = document.createElement("a");
      link.href = cleanedUrl;
      link.download = name;
      link.target = "_blank";
      link.rel = "noopener noreferrer";

      // Temporarily add to DOM to trigger download
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      console.log("Download initiated via direct link");
      return;
    } catch (linkError) {
      console.log(
        "Direct link download failed, trying iframe approach:",
        linkError
      );

      try {
        // Method 2: Iframe approach for files that don't support direct download
        const iframe = document.createElement("iframe");
        iframe.style.display = "none";
        iframe.style.position = "absolute";
        iframe.style.left = "-9999px";
        iframe.src = cleanedUrl;

        document.body.appendChild(iframe);

        // Clean up iframe after download attempt
        setTimeout(() => {
          if (document.body.contains(iframe)) {
            document.body.removeChild(iframe);
          }
        }, 2000);

        console.log("Download initiated via iframe");
      } catch (iframeError) {
        console.error("Iframe download failed:", iframeError);

        // Method 3: Open in new window as final fallback
        try {
          window.open(cleanedUrl, "_blank", "noopener,noreferrer");
          console.log("Download initiated via new window");
        } catch (windowError) {
          console.error("All download methods failed:", windowError);
          // Show user-friendly error message
          alert(t("toast.unableToDownloadFile", { name }));
        }
      }
    }
  }
  if (!file) {
    return <div>{t("workspace.loadingFile")}</div>;
  }

  return (
    <div className="h-full">
      <div className="flex justify-between items-center p-4 border-b">
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="sm"
            className="gap-1"
            onClick={() => router.back()}
          >
            <ChevronLeft className="h-4 w-4" />
            {t("workspace.back")}
          </Button>
          <div>
            <h1 className="text-xl font-bold">{file?.name}</h1>
            <p className="text-sm text-muted-foreground">
              {file?.extension ? `.${file.extension}` : ""} · {file?.size} ·
              {t("workspace.lastModified", {
                date: file ? new Date(file.createdAt).toLocaleDateString() : "",
              })}
            </p>
          </div>
        </div>

        <div className="flex gap-2">
          <div className="ml-2">
            <Tooltip>
              <TooltipTrigger asChild>
                {getVectorizationStatusBadge(file?.vectorizationStatus)}
              </TooltipTrigger>
              <TooltipContent>
                {t("workspace.vectorizationStatusTooltip")}
              </TooltipContent>
            </Tooltip>
          </div>
          {(file?.vectorizationStatus === "PENDING" ||
            file?.vectorizationStatus === "FAILED") && (
            <Button
              variant="outline"
              size="sm"
              className="gap-2"
              onClick={handleManualVectorization}
            >
              <RefreshCw className="h-3 w-3" />
              {file?.vectorizationStatus === "PENDING"
                ? t("common.indexingStatus.startIndexing")
                : t("common.indexingStatus.retryIndexing")}
            </Button>
          )}
          <Button
            variant="outline"
            size="sm"
            className="gap-2"
            onClick={() => {
              if (
                [
                  "docx",
                  "doc",
                  "pptx",
                  "ppt",
                  "pptm",
                  "xlsx",
                  "xls",
                  "odt",
                  "ods",
                  "odp",
                  "xlsm",
                  "txt",
                  "docm",
                ]?.includes(file?.extension)
              ) {
                window.open(
                  `https://view.officeapps.live.com/op/embed.aspx?src=${encodeURIComponent(
                    file.url
                  )}`,
                  "_blank"
                );
              } else {
                window.open(file.url, "_blank");
              }
            }}
          >
            <ExternalLink className="h-4 w-4" />
            {t("workspace.openInNewTab")}
          </Button>
          <Button
            variant="outline"
            size="sm"
            className="gap-2"
            onClick={() => downloadURI(file.url, file.name)}
          >
            <Download className="h-4 w-4" />
            {t("workspace.downloadFile")}
          </Button>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button disabled={isSyncedWithCloud} variant="ghost" size="sm">
                {t("workspace.more")}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>{t("workspace.actions")}</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <UpdateGuard
                hasPermission={permission?.file?.update}
                resource="FILE"
                fallback={
                  <DropdownMenuItem disabled>
                    <Edit2 className="h-4 w-4 mr-2" />
                    {t("workspace.noAccess")}
                  </DropdownMenuItem>
                }
              >
                <DropdownMenuItem onClick={() => setIsCreateFolderOpen(true)}>
                  <Edit2 className="h-4 w-4 mr-2" />
                  {t("workspace.rename")}
                </DropdownMenuItem>
              </UpdateGuard>

              <DropdownMenuSeparator />
              <DeleteGuard
                hasPermission={permission?.file?.delete}
                resource="FILE"
                fallback={
                  <DropdownMenuItem disabled>
                    <Trash2 className="h-4 w-4 mr-2" />
                    {t("workspace.noAccess")}
                  </DropdownMenuItem>
                }
              >
                <DropdownMenuItem
                  className="text-red-500"
                  onClick={() =>
                    showFileDeleteConfirmation(fileId, file?.name || "")
                  }
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  {t("workspace.delete")}
                </DropdownMenuItem>
              </DeleteGuard>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      <div className="overflow-auto">{getFilePreview()}</div>
      <CreateFolderDialog
        isLoading={isLoading}
        title={t("workspace.renameFile")}
        subTitle={t("workspace.enterFileName")}
        isOpen={isCreateFolderOpen}
        onClose={() => {
          setIsCreateFolderOpen(false);
          setNewFolderName("");
        }}
        folderName={newFolderName}
        onFolderNameChange={(e: React.ChangeEvent<HTMLInputElement>) =>
          setNewFolderName(e.target.value)
        }
        onCreateFolder={handleRename}
        label={t("workspace.newFileName")}
        labelPlaceholder={t("workspace.enterFileNamePlaceholder")}
        buttonText={t("workspace.renameFile")}
      />

      {/* File Delete Confirmation Dialog */}
      <FileDeleteConfirmation
        isOpen={fileDeleteDialog.isOpen}
        onClose={() =>
          setFileDeleteDialog({ isOpen: false, fileId: "", fileName: "" })
        }
        onConfirm={handleConfirmedDeleteFile}
        fileName={fileDeleteDialog.fileName}
        isLoading={isFileDeleteLoading}
      />
    </div>
  );
}
