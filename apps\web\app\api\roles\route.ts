import { NextResponse } from "next/server";
import { getToken } from "next-auth/jwt";
import db from "@/lib/shared-db";

/**
 * GET /api/roles
 * Get all custom roles for a tenant
 */
export async function GET(request: Request) {
  try {
    const url = new URL(request.url);
    const tenantId = url.searchParams.get("tenantId");
    const userId = request.headers.get("x-user-id");

    if (!tenantId) {
      return NextResponse.json(
        { error: "TenantId is required" },
        { status: 400 }
      );
    }

    // Verify user has access to this tenant
    const membership = await db.membership.findFirst({
      where: {
        userId,
        tenantId,
      },
    });

    if (!membership) {
      return NextResponse.json(
        { error: "You don't have access to this tenant" },
        { status: 403 }
      );
    }

    // Get all custom roles for this tenant
    const customRoles = await db.customRole.findMany({
      where: { tenantId },
      include: {
        permissions: true,
      },
      orderBy: { name: "asc" },
    });

    return NextResponse.json({ customRoles });
  } catch (error) {
    console.error("Error fetching custom roles:", error);
    return NextResponse.json(
      { error: "Failed to fetch custom roles" },
      { status: 500 }
    );
  }
}

/**
 * POST /api/roles
 * Create a new custom role
 */
export async function POST(request: Request) {
  try {
    const token = await getToken({ req: request as any });
    if (!token) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const { name, description, tenantId, permissionIds, workspaceIds } = body;

    // Validate required fields
    if (!name || !tenantId) {
      return NextResponse.json(
        { error: "Name and tenantId are required" },
        { status: 400 }
      );
    }

    // Verify user has owner access to this tenant
    const membership = await db.membership.findFirst({
      where: {
        userId: token.sub,
        tenantId,
        role: "OWNER",
      },
    });

    if (!membership) {
      return NextResponse.json(
        { error: "Only owners can create custom roles" },
        { status: 403 }
      );
    }

    // Check if a role with the same name already exists
    const existingRole = await db.customRole.findFirst({
      where: {
        name,
        tenantId,
      },
    });

    if (existingRole) {
      return NextResponse.json(
        { error: "A role with this name already exists" },
        { status: 400 }
      );
    }

    // Create the custom role
    const customRole = await db.customRole.create({
      data: {
        name,
        description,
        tenantId,
      },
    });

    // Add permissions if provided
    if (permissionIds && permissionIds.length > 0) {
      const permissionConnections = permissionIds.map(
        (permissionId: string) => ({
          permissionId,
          customRoleId: customRole.id,
        })
      );

      await db.customRolePermission.createMany({
        data: permissionConnections,
      });
    }

    // Handle workspace assignments if provided
    if (workspaceIds && workspaceIds.length > 0) {
      // Get all workspaces to verify they exist and belong to the tenant
      const workspaces = await db.workspace.findMany({
        where: {
          id: { in: workspaceIds },
          tenantId,
        },
      });

      if (workspaces.length !== workspaceIds.length) {
        return NextResponse.json(
          {
            error:
              "One or more workspaces not found or do not belong to this tenant",
          },
          { status: 400 }
        );
      }

      // Create workspace associations
      for (const workspace of workspaces) {
        await db.customRoleWorkspace.create({
          data: {
            customRoleId: customRole.id,
            workspaceId: workspace.id,
          },
        });
      }
    }

    // Get the created role with permissions
    const createdRole = await db.customRole.findUnique({
      where: { id: customRole.id },
      include: {
        permissions: {
          include: {
            permission: true,
          },
        },
      },
    });

    return NextResponse.json({ customRole: createdRole });
  } catch (error) {
    console.error("Error creating custom role:", error);
    return NextResponse.json(
      { error: "Failed to create custom role" },
      { status: 500 }
    );
  }
}
