"""
Public Chatbot API Endpoint for Swiss Knowledge Hub

This endpoint provides public access to chatbot functionality using API key authentication
instead of JWT tokens. It reuses the same CopilotKit services as the internal chat system.
"""

from fastapi import APIRouter, Depends, HTTPException, Request, status
from fastapi.responses import <PERSON><PERSON><PERSON><PERSON>ponse
from typing import Dict, Any, Optional, List
from pydantic import BaseModel
from src.services.copilotkit_rag_service import copilotkit_rag_service
from src.api.deps import get_db
from starlette.responses import StreamingResponse
import json
import logging
from bson import ObjectId
import asyncio
from motor.motor_asyncio import AsyncIOMotorDatabase
import hashlib
import time
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

router = APIRouter()

class PublicChatbotRequest(BaseModel):
    message: str
    session_token: Optional[str] = None
    search_modes: Optional[List[str]] = None
    images: Optional[List[str]] = None
    metadata: Optional[Dict[str, Any]] = None
    stream: Optional[bool] = False
    include_web_results: Optional[bool] = False

class PublicChatbotResponse(BaseModel):
    response: str
    session_token: str
    sources: Optional[List[Dict[str, Any]]] = None
    error: Optional[str] = None

# Rate limiting storage (in production, use Redis)
rate_limit_storage = {}

def get_client_ip(request: Request) -> str:
    """Extract client IP from request headers."""
    forwarded = request.headers.get("x-forwarded-for")
    real_ip = request.headers.get("x-real-ip")
    cf_connecting_ip = request.headers.get("cf-connecting-ip")
    
    if cf_connecting_ip:
        return cf_connecting_ip
    if forwarded:
        return forwarded.split(",")[0].strip()
    if real_ip:
        return real_ip
    
    return request.client.host if request.client else "unknown"

def hash_identifier(identifier: str) -> str:
    """Hash an identifier for privacy."""
    return hashlib.sha256(identifier.encode()).hexdigest()[:16]

def check_rate_limit(key: str, limit: int, window_seconds: int) -> Dict[str, Any]:
    """Simple rate limiting implementation."""
    now = time.time()
    
    if key not in rate_limit_storage:
        rate_limit_storage[key] = []
    
    # Clean old entries
    rate_limit_storage[key] = [
        timestamp for timestamp in rate_limit_storage[key]
        if now - timestamp < window_seconds
    ]
    
    # Check if limit exceeded
    if len(rate_limit_storage[key]) >= limit:
        return {
            "allowed": False,
            "remaining": 0,
            "reset_time": int(now + window_seconds)
        }
    
    # Add current request
    rate_limit_storage[key].append(now)
    
    return {
        "allowed": True,
        "remaining": limit - len(rate_limit_storage[key]),
        "reset_time": int(now + window_seconds)
    }

async def validate_chatbot_api_key(
    request: Request,
    chatbot_id: str,
    api_key: str,
    db: AsyncIOMotorDatabase
) -> Dict[str, Any]:
    """Validate chatbot API key and return chatbot configuration."""
    try:
        logger.info(f"Validating chatbot API key: {api_key} {chatbot_id}")
        # Find chatbot with matching API key
        chatbot = await db.Chatbot.find_one({
            "_id": ObjectId(chatbot_id),
            "apiKey": api_key,
            "isActive": True
        })
        logger.info(f"Chatbot: {chatbot}")
        
        if not chatbot:
            return {
                "success": False,
                "error": "Invalid chatbot ID or API key",
                "status_code": 401
            }
        
        # Get tenant information
        tenant = await db.Tenant.find_one({"_id": chatbot["tenantId"]})
        if not tenant:
            return {
                "success": False,
                "error": "Chatbot is temporarily unavailable",
                "status_code": 503
            }

        # Check tenant status - default to "active" if status field is missing (for backward compatibility)
        tenant_status = tenant.get("status", "active")
        if tenant_status not in ["active"]:
            return {
                "success": False,
                "error": "Chatbot is temporarily unavailable",
                "status_code": 503
            }
        
        # Get user information
        user = await db.User.find_one({"_id": chatbot["userId"]})
        if not user:
            return {
                "success": False,
                "error": "Chatbot owner not found",
                "status_code": 404
            }
        
        # Validate domain access if origin is provided
        origin = request.headers.get("origin")
        if origin and chatbot.get("allowedDomains"):
            allowed_domains = chatbot["allowedDomains"]
            if allowed_domains:
                try:
                    from urllib.parse import urlparse
                    origin_domain = urlparse(origin).hostname
                    
                    domain_allowed = False
                    for domain in allowed_domains:
                        if domain.startswith("*."):
                            # Wildcard subdomain
                            base_domain = domain[2:]
                            if origin_domain.endswith(base_domain):
                                domain_allowed = True
                                break
                        elif domain == origin_domain:
                            domain_allowed = True
                            break
                    
                    if not domain_allowed:
                        return {
                            "success": False,
                            "error": "Domain not allowed to access this chatbot",
                            "status_code": 403
                        }
                except Exception:
                    return {
                        "success": False,
                        "error": "Invalid origin header",
                        "status_code": 400
                    }
        
        # Check rate limits
        client_ip = get_client_ip(request)
        hashed_ip = hash_identifier(client_ip)
        
        # Rate limit checks (per minute, hour, day)
        rate_checks = [
            ("minute", 60, 60),  # 60 requests per minute
            ("hour", 1000, 3600),  # 1000 requests per hour
            ("day", 10000, 86400)  # 10000 requests per day
        ]
        
        for period, limit, window in rate_checks:
            rate_key = f"{chatbot_id}:{hashed_ip}:{period}"
            rate_result = check_rate_limit(rate_key, limit, window)
            
            if not rate_result["allowed"]:
                return {
                    "success": False,
                    "error": f"Rate limit exceeded ({period})",
                    "status_code": 429,
                    "headers": {
                        "Retry-After": str(rate_result["reset_time"] - int(time.time())),
                        "X-RateLimit-Remaining": "0",
                        "X-RateLimit-Reset": str(rate_result["reset_time"])
                    }
                }
        
        return {
            "success": True,
            "chatbot": chatbot,
            "tenant": tenant,
            "user": user
        }
        
    except Exception as e:
        logger.error(f"Error validating chatbot API key: {e}")
        return {
            "success": False,
            "error": "Security validation failed",
            "status_code": 500
        }

async def get_chatbot_api_key(request: Request) -> str:
    """Extract API key from request headers."""
    auth_header = request.headers.get("authorization")
    if not auth_header or not auth_header.startswith("Bearer "):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Missing or invalid API key"
        )
    
    return auth_header[7:]  # Remove "Bearer " prefix

@router.post("/chatbot/{chatbot_id}/chat", response_model=Dict[str, Any], status_code=200)
async def public_chatbot_chat(
    chatbot_id: str,
    request: Request,
    query: PublicChatbotRequest,
    db = Depends(get_db)
):
    """
    Public chatbot chat endpoint using API key authentication.
    Reuses the same CopilotKit services as the internal chat system.
    """
    try:
        logger.info(f"Public chatbot chat: {chatbot_id} {query}")
        
        # Extract API key
        api_key = await get_chatbot_api_key(request)
        
        # Validate chatbot and API key
        validation_result = await validate_chatbot_api_key(request, chatbot_id, api_key, db)
        
        if not validation_result["success"]:
            raise HTTPException(
                status_code=validation_result["status_code"],
                detail=validation_result["error"],
                headers=validation_result.get("headers", {})
            )
        
        chatbot = validation_result["chatbot"]
        user = validation_result["user"]
        
        # Set the database client for the service
        copilotkit_rag_service.db_client = db
        
        # Prepare chat parameters
        user_id = str(chatbot["userId"])
        tenant_id = str(chatbot["tenantId"])
        user_name = user.get("name", "Anonymous")
        
        # Get search mode from chatbot configuration
        search_mode = query.search_modes
        include_web_results=query.include_web_results
        
        # Generate session token if not provided
        session_token = query.session_token
        if not session_token:
            import uuid
            session_token = str(uuid.uuid4())
        
        
        
        # Call CopilotKit service
        start_time = time.time()
        
        if query.stream:
            # Handle streaming response
            async def generate_stream():
                try:
                    async for chunk in copilotkit_rag_service.query_workspace(
                        user_id=user_id,
                        question=query.message,
                        tenant_id=tenant_id,
                        user_name=user_name,
                        search_mode=search_mode,
                        include_web_results=include_web_results,
                        stream=True
                    ):
                        yield f"data: {json.dumps(chunk)}\n\n"
                except Exception as e:
                    logger.error(f"Error in streaming chat: {e}")
                    error_chunk = {
                        "error": str(e),
                        "done": True
                    }
                    yield f"data: {json.dumps(error_chunk)}\n\n"
            
            return StreamingResponse(
                generate_stream(),
                media_type="text/plain",
                headers={
                    "Cache-Control": "no-cache",
                    "Connection": "keep-alive",
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "POST, OPTIONS",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                }
            )
        else:
            # Handle non-streaming response
            response_generator = copilotkit_rag_service.query_workspace(
                user_id=user_id,
                question=query.message,
                tenant_id=tenant_id,
                user_name=user_name,
                search_mode=search_mode,
                include_web_results=include_web_results,
                stream=False
            )
            
            # Get the single response
            chat_response = None
            async for response in response_generator:
                chat_response = response
                break
            
            if not chat_response:
                raise HTTPException(
                    status_code=500,
                    detail="No response generated"
                )
            
            response_time = time.time() - start_time
            
            # Update session with response time
            await db.ChatbotSession.update_one(
                {"sessionToken": session_token},
                {"$set": {"averageResponseTime": response_time * 1000}}  # Convert to ms
            )
            
            # Update chatbot usage statistics
            await db.Chatbot.update_one(
                {"_id": ObjectId(chatbot_id)},
                {
                    "$inc": {
                        "usageCount": 1,
                        "monthlyUsage": 1
                    },
                    "$set": {"lastUsedAt": datetime.utcnow()}
                }
            )
            
            # Return response with CORS headers
            response_data = {
                "response": chat_response.get("answer", "I'm sorry, I couldn't generate a response."),
                "session_token": session_token,
                "sources": chat_response.get("sources", []),
                "metadata": {
                    "response_time": response_time,
                    "search_mode": search_mode,
                    "include_web_results": include_web_results
                }
            }

            headers = {
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "POST, OPTIONS",
                "Access-Control-Allow-Headers": "Content-Type, Authorization, X-Requested-With"
            }

            return JSONResponse(content=response_data, headers=headers)
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in public chatbot chat: {e}")
        raise HTTPException(
            status_code=500,
            detail="Internal server error"
        )

@router.get("/chatbot/{chatbot_id}/config", response_model=Dict[str, Any], status_code=200)
async def public_chatbot_config(
    chatbot_id: str,
    request: Request,
    db = Depends(get_db)
):
    """
    Get public chatbot configuration.
    Returns chatbot settings needed for SDK initialization.
    """
    try:
        # Extract API key (can be from header or query parameter)
        api_key = None
        auth_header = request.headers.get("authorization")
        if auth_header and auth_header.startswith("Bearer "):
            api_key = auth_header[7:]
        else:
            # Fallback to query parameter for easier integration
            api_key = request.query_params.get("apiKey")

        if not api_key:
            headers = {
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "GET, OPTIONS",
                "Access-Control-Allow-Headers": "Content-Type, Authorization, X-Requested-With"
            }
            return JSONResponse(
                content={"error": "Missing API key"},
                status_code=401,
                headers=headers
            )

        # Validate chatbot and API key
        validation_result = await validate_chatbot_api_key(request, chatbot_id, api_key, db)

        if not validation_result["success"]:
            headers = {
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "GET, OPTIONS",
                "Access-Control-Allow-Headers": "Content-Type, Authorization, X-Requested-With"
            }
            return JSONResponse(
                content={"error": validation_result["error"]},
                status_code=validation_result["status_code"],
                headers=validation_result.get("headers", headers)
            )

        chatbot = validation_result["chatbot"]
        tenant = validation_result["tenant"]
        user = validation_result["user"]

        # Build configuration response
        config = {
            "id": str(chatbot["_id"]),
            "name": chatbot.get("name", "AI Assistant"),
            "description": chatbot.get("description", ""),
            "isActive": chatbot.get("isActive", True),
            "access": chatbot.get("access", "public"),
            "customization": {
                "theme": chatbot.get("theme", {
                    "primaryColor": "#007bff",
                    "secondaryColor": "#6c757d",
                    "fontFamily": "Inter, sans-serif",
                    "borderRadius": "8px"
                }),
                "position": chatbot.get("position", "bottom-right"),
                "size": chatbot.get("size", "medium"),
                "greeting": chatbot.get("greeting", "Hello! How can I help you today?"),
                "placeholder": chatbot.get("placeholder", "Type your message..."),
                "showBranding": chatbot.get("showBranding", True)
            },
            "searchModes": chatbot.get("searchModes", ["internal"]),
            "llmScope": chatbot.get("llmScope", ["internal"]),
            "maxTokens": chatbot.get("maxTokens", 4000),
            "temperature": chatbot.get("temperature", 0.7),
            "rateLimits": {
                "perMinute": 60,
                "perHour": 1000,
                "perDay": 10000
            },
            "tenant": {
                "id": str(tenant["_id"]),
                "name": tenant.get("name", ""),
                "status": tenant.get("status", "active")
            }
        }

        # Return configuration with CORS headers
        headers = {
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Methods": "GET, OPTIONS",
            "Access-Control-Allow-Headers": "Content-Type, Authorization, X-Requested-With",
            "Cache-Control": "public, max-age=300"  # Cache for 5 minutes
        }

        return JSONResponse(content=config, headers=headers)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in public chatbot config: {e}")
        headers = {
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Methods": "GET, OPTIONS",
            "Access-Control-Allow-Headers": "Content-Type, Authorization, X-Requested-With"
        }
        return JSONResponse(
            content={"error": "Internal server error"},
            status_code=500,
            headers=headers
        )

@router.options("/chatbot/{chatbot_id}/config")
async def public_chatbot_config_options(chatbot_id: str):
    """Handle CORS preflight requests for config endpoint."""
    headers = {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "GET, OPTIONS",
        "Access-Control-Allow-Headers": "Content-Type, Authorization, X-Requested-With",
        "Access-Control-Max-Age": "86400"
    }
    return JSONResponse(content={"message": "OK"}, headers=headers)

@router.options("/chatbot/{chatbot_id}/chat")
async def public_chatbot_chat_options(chatbot_id: str):
    """Handle CORS preflight requests for chat endpoint."""
    headers = {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "POST, OPTIONS",
        "Access-Control-Allow-Headers": "Content-Type, Authorization, X-Requested-With",
        "Access-Control-Max-Age": "86400"
    }
    return JSONResponse(content={"message": "OK"}, headers=headers)
