import { SharedThreadView } from "@/components/wrapper-screens/shared-thread";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/next-auth";
import { cookies } from "next/headers";
import { getTranslations } from "@/lib/server-i18n";

interface SharedThreadPageProps {
  params: {
    shareToken: string;
  };
  searchParams: {
    messageId?: string; // For highlighting specific messages
  };
}

export default async function SharedThreadPage({
  params,
  searchParams,
}: SharedThreadPageProps) {
  const session: any = await getServerSession(authOptions);
  const userId = cookies().get("userId")?.value ?? session?.userId;
  const { t } = getTranslations();

  try {
    // Fetch shared thread data
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/shared/thread/${params.shareToken}`,
      {
        headers: {
          Cookie: cookies().toString(),
        },
      }
    );

    if (!response.ok) {
      if (response.status === 404) {
        return (
          <div className="flex items-center justify-center min-h-screen">
            <div className="text-center">
              <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2">
                {t("sharedThread.threadNotFound")}
              </h1>
              <p className="text-gray-600 dark:text-gray-400">
                {t("sharedThread.threadNotFoundDescription")}
              </p>
            </div>
          </div>
        );
      }

      if (response.status === 410) {
        return (
          <div className="flex items-center justify-center min-h-screen">
            <div className="text-center">
              <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2">
                {t("sharedThread.linkExpired")}
              </h1>
              <p className="text-gray-600 dark:text-gray-400">
                {t("sharedThread.linkExpiredDescription")}
              </p>
            </div>
          </div>
        );
      }

      if (response.status === 403) {
        return (
          <div className="flex items-center justify-center min-h-screen">
            <div className="text-center">
              <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2">
                {t("sharedThread.accessDenied")}
              </h1>
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                {t("sharedThread.accessDeniedDescription")}
              </p>
              {!session && (
                <a
                  href="/sign-in"
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary hover:bg-primary/90"
                >
                  {t("sharedThread.signInToAccess")}
                </a>
              )}
            </div>
          </div>
        );
      }

      throw new Error(`Failed to fetch shared thread: ${response.status}`);
    }

    const data = await response.json();

    return (
      <SharedThreadView
        chat={data.chat}
        shareInfo={data.shareInfo}
        tenant={data.tenant}
        canComment={data.canComment && !data?.shareInfo?.isPublic}
        currentUser={
          session
            ? {
                id: userId,
                name: session.user?.name || session.userName,
                email: session.user?.email,
                image: session.user?.image,
              }
            : null
        }
        highlightedMessageId={searchParams.messageId}
      />
    );
  } catch (error) {
    console.error("Error loading shared thread:", error);
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2">
            {t("sharedThread.errorLoadingThread")}
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            {t("sharedThread.errorLoadingThreadDescription")}
          </p>
        </div>
      </div>
    );
  }
}
