"use client";

import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Building, Loader2 } from "lucide-react";

import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { createTenant } from "@/services";
import { useTranslatedToast } from "@/hooks/use-translated-toast";
import { useState } from "react";
import { useLanguage } from "@/lib/language-context";

const formSchema = z.object({
  name: z.string().min(2, {
    message: "Company name must be at least 2 characters.",
  }),
  url: z
    .string()
    .regex(/^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9](?:\.[a-zA-Z]{2,})+/, {
      message: "Please enter a valid domain (e.g., example.com)",
    })
    .optional()
    .or(z.literal("")),
  image: z.string().optional(),
  description: z.string().optional(),
});

export function CompanyProfileForm() {
  const [isLoading, setIsLoading] = useState(false);
  const toast = useTranslatedToast();
  const { t } = useLanguage();

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema as any),
    defaultValues: {
      name: "",
      url: "",
      image: "",
      description: "",
    },
  });

  async function onSubmit(values: z.infer<typeof formSchema>) {
    setIsLoading(true);
    toast.loading("toast.creatingCompany");

    try {
      const tenant = await createTenant({
        ...values,
        slug: values.name.toLowerCase().replace(/\s+/g, "-"),
      });
      toast.remove();

      if (!tenant?.data?.id) {
        toast.error("toast.somethingWentWrong");
      } else {
        toast.success("toast.companyCreated");
        // Force a hard navigation instead of client-side route replacement
        window.location.replace("/dashboard");
      }
    } catch (error) {
      console.error("Onboarding error:", error);
      toast.error("toast.companyCreateFailed");
    } finally {
      setIsLoading(false);
    }
  }
  return (
    <Dialog open={true}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <div className="flex items-center gap-2">
            <Building className="h-6 w-6" />
            <DialogTitle className="text-xl">
              {t("organization.createOrganization")}
            </DialogTitle>
          </div>
          <DialogDescription>
            {t("dashboard.getStartedMessage")}
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("common.name")}</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter your company name" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="url"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("common.website")}</FormLabel>
                  <div className="flex">
                    <div className="flex items-center justify-center rounded-l-md border border-r-0 bg-muted px-3 text-sm text-muted-foreground">
                      http://
                    </div>
                    <FormControl>
                      <Input
                        className="rounded-l-none"
                        placeholder="www.figma.com"
                        {...field}
                      />
                    </FormControl>
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("common.description")}</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Enter a description..."
                      className="resize-none"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <div className="flex justify-between pt-2">
              <Button disabled={isLoading} type="submit">
                {isLoading ? <Loader2 className="animate-spin mr-2" /> : null}
                {t("common.confirm")}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
