name: Ceruniq Development Deployment

on:
  push:
    branches:
      - ceruniq-dev
    paths:
      - 'apps/terraform/**'
      - 'apps/web/**'
      - 'apps/admin/**'
      - 'apps/api/**'

permissions:
  id-token: write
  contents: read

jobs:
  check_changes:
    runs-on: ubuntu-latest
    outputs:
      terraform: ${{ steps.filter.outputs.terraform }}
      nextjs: ${{ steps.filter.outputs.nextjs }}
      fastapi: ${{ steps.filter.outputs.fastapi }}
      admin: ${{ steps.filter.outputs.admin }}
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4
      - name: Check for Changes
        id: filter
        uses: dorny/paths-filter@v3
        with:
          filters: |
            terraform:
              - 'apps/terraform/**'
            nextjs:
              - 'apps/web/**'
            fastapi:
              - 'apps/api/**'
            admin:
              - 'apps/admin/**'

  terraform:
    needs: [check_changes]
    runs-on: ubuntu-latest
    environment: ceruniq-development
    if: needs.check_changes.outputs.terraform == 'true'
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Azure Login
        uses: azure/login@v2
        with:
          client-id: ${{ secrets.AZURE_CLIENT_ID }}
          tenant-id: ${{ secrets.AZURE_TENANT_ID }}
          subscription-id: ${{ secrets.AZURE_SUBSCRIPTION_ID }}

      - name: Set up Terraform
        uses: hashicorp/setup-terraform@v2
        with:
          terraform_version: 1.5.7

      - name: Deploy Terraform
        env:
          ARM_CLIENT_ID: ${{ secrets.AZURE_CLIENT_ID }}
          ARM_CLIENT_SECRET: ${{ secrets.AZURE_CLIENT_SECRET }}
          ARM_TENANT_ID: ${{ secrets.AZURE_TENANT_ID }}
          ARM_SUBSCRIPTION_ID: ${{ secrets.AZURE_SUBSCRIPTION_ID }}
        run: |
          cd apps/terraform
          terraform init -backend-config="key=terraform.tfstate"
          terraform plan \
            -var "client_id=${{ secrets.AZURE_CLIENT_ID }}" \
            -var "client_secret=${{ secrets.AZURE_CLIENT_SECRET }}" \
            -var "tenant_id=${{ secrets.AZURE_TENANT_ID }}" \
            -var "subscription_id=${{ secrets.AZURE_SUBSCRIPTION_ID }}" \
            -var-file=dev.tfvars
          terraform apply \
            -var "client_id=${{ secrets.AZURE_CLIENT_ID }}" \
            -var "client_secret=${{ secrets.AZURE_CLIENT_SECRET }}" \
            -var "tenant_id=${{ secrets.AZURE_TENANT_ID }}" \
            -var "subscription_id=${{ secrets.AZURE_SUBSCRIPTION_ID }}" \
            -var-file=dev.tfvars -auto-approve

  deploy_nextjs:
    needs: [check_changes]
    runs-on: ubuntu-latest
    environment: ceruniq-development
    if: needs.check_changes.outputs.nextjs == 'true'
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 18

      - name: Install pnpm
        run: npm install -g pnpm

      - name: Install Dependencies
        run: |
          pnpm install
          cd apps/web
          npm install

      - name: Deploy to Azure SWA
        id: deploy
        uses: Azure/static-web-apps-deploy@v1
        with:
          azure_static_web_apps_api_token: ${{ secrets.AZURE_STATIC_WEB_APPS_API_TOKEN }}
          action: "upload"
          production_branch: ceruniq-dev
          app_location: "apps/web"
          api_location: ""
          output_location: ""
        env:
          STRIPE_SECRET_KEY: ${{ vars.STRIPE_SECRET_KEY }}
          NEXT_PUBLIC_DOCS_URL: ${{ vars.NEXT_PUBLIC_DOCS_URL }}
          STRIPE_PUBLISHABLE_KEY: ${{ vars.STRIPE_PUBLISHABLE_KEY }}
          STRIPE_WEBHOOK_SECRET: ${{ vars.STRIPE_WEBHOOK_SECRET }}
          NEXT_ENCRYPTION_CLOAK_KEY: ${{ vars.NEXT_ENCRYPTION_CLOAK_KEY }}
          NEXT_PUBLIC_API_BASE_URL: ${{ vars.NEXT_PUBLIC_API_BASE_URL }}
          NEXT_ENCRYPTION_CLOAK_KEYCHAIN: ${{ vars.NEXT_ENCRYPTION_CLOAK_KEYCHAIN }}
          NEXT_PUBLIC_MICROSOFT_CLIENT_ID: ${{ vars.NEXT_PUBLIC_MICROSOFT_CLIENT_ID }}
          NEXT_PUBLIC_GOOGLE_CLIENT_ID: ${{ vars.NEXT_PUBLIC_GOOGLE_CLIENT_ID }}
          NEXT_PUBLIC_API_URL: ${{ vars.NEXT_PUBLIC_API_URL }}
          NEXT_PUBLIC_WS_URL: ${{ vars.NEXT_PUBLIC_WS_URL }}
          NEXT_PUBLIC_SEND_EMAIL_FROM: ${{ vars.NEXT_PUBLIC_SEND_EMAIL_FROM }}
          NEXT_PUBLIC_COMMUNICATION_SERVICES_CONNECTION_STRING: ${{ vars.NEXT_PUBLIC_COMMUNICATION_SERVICES_CONNECTION_STRING }}
          NEXT_PUBLIC_DATADOG_ENABLED: ${{ vars.NEXT_PUBLIC_DATADOG_ENABLED }}
          NEXT_PUBLIC_DATADOG_APPLICATION_ID: ${{ vars.NEXT_PUBLIC_DATADOG_APPLICATION_ID }}
          NEXT_PUBLIC_DATADOG_CLIENT_TOKEN: ${{ vars.NEXT_PUBLIC_DATADOG_CLIENT_TOKEN }}
          NEXT_PUBLIC_DATADOG_SITE: ${{ vars.NEXT_PUBLIC_DATADOG_SITE }}
          NEXT_PUBLIC_APP_VERSION: ${{ vars.NEXT_PUBLIC_APP_VERSION }}
          DATADOG_API_KEY: ${{ vars.DATADOG_API_KEY }}
          NEXT_PUBLIC_IS_PARTNER_CONSOLE: "1"
  
  deploy_admin:
    needs: [check_changes]
    runs-on: ubuntu-latest
    environment: ceruniq-development
    if: needs.check_changes.outputs.admin == 'true'
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 18

      - name: Install pnpm
        run: npm install -g pnpm

      - name: Install Dependencies
        run: |
          pnpm install
          cd apps/admin
          npm install

      - name: Deploy to Azure SWA
        id: deploy
        uses: Azure/static-web-apps-deploy@v1
        with:
          azure_static_web_apps_api_token: ${{ secrets.AZURE_STATIC_ADMIN_APPS_API_TOKEN }}
          action: "upload"
          production_branch: dev
          app_location: "apps/admin"
          api_location: ""
          output_location: ""
        env:
          NEXT_ENCRYPTION_CLOAK_KEY: ${{ vars.NEXT_ENCRYPTION_CLOAK_KEY }}
          NEXT_PUBLIC_API_BASE_URL: ${{ vars.NEXT_PUBLIC_ADMIN_API_BASE_URL }}
          NEXT_ENCRYPTION_CLOAK_KEYCHAIN: ${{ vars.NEXT_ENCRYPTION_CLOAK_KEYCHAIN }}
          NEXT_PUBLIC_WEB_URL: ${{ vars.NEXT_PUBLIC_WEB_URL }}
          DATABASE_URL: ${{ vars.DATABASE_URL }}
          NEXT_PUBLIC_SEND_EMAIL_FROM: ${{ vars.NEXT_PUBLIC_SEND_EMAIL_FROM }}
          NEXT_PUBLIC_COMMUNICATION_SERVICES_CONNECTION_STRING: ${{ vars.NEXT_PUBLIC_COMMUNICATION_SERVICES_CONNECTION_STRING }}
          NEXT_PUBLIC_IS_PARTNER_CONSOLE: "1"
  deploy_fastapi:
    needs: [check_changes]
    runs-on: ubuntu-latest
    environment: ceruniq-development
    if: needs.check_changes.outputs.fastapi == 'true'
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Package FastAPI App
        run: |
          cd apps/api
          zip -r fastapi-app.zip . -x "*.git*" "node_modules/*" "__pycache__/*"

      - name: Deploy FastAPI to Azure App Services
        uses: azure/webapps-deploy@v2
        with:
          app-name: "ceruniq-fastapi-dev"
          publish-profile: ${{ secrets.AZURE_WEBAPP_PUBLISH_PROFILE }}
          package: "apps/api/fastapi-app.zip"
