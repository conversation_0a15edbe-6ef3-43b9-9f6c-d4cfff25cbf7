"use client";

import { useEffect, useState } from "react";
import { useSession } from "next-auth/react";
import { useLanguage } from "@/lib/language-context";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { getGroups, deleteGroup } from "@/services/src/group";
import { CreateGroupDialog } from "@/components/model/create-group-dialog";
import { BulkGroupImportDialog } from "@/components/bulk-import/bulk-group-import-dialog";
import {
  Plus,
  Users,
  HelpCircle,
  Search,
  Eye,
  Trash2,
  MoreHorizontal,
} from "lucide-react";
import toast from "react-hot-toast";
import { useRouter } from "next/navigation";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { getCookie } from "@/utils/cookies";
import {
  Tooltip,
  TooltipProvider,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Badge } from "@/components/ui/badge";

// Define types for our components
interface Group {
  id: string;
  name: string;
  description?: string;
  tenantId: string;
  groupMembers: GroupMember[];
  customRoleId?: string;
  customRole?: CustomRole;
  createdAt: string;
  updatedAt: string;
}

interface CustomRole {
  id: string;
  name: string;
  description?: string;
  permissions?: RolePermission[];
}

interface RolePermission {
  id: string;
  permission?: {
    action: string;
    resource: string;
  };
}

interface GroupMember {
  id: string;
  userId: string;
  groupId: string;
  user: {
    id: string;
    name: string;
    email: string;
    image?: string;
  };
}

export default function GroupsPage() {
  const { data: session } = useSession();
  const { t } = useLanguage();
  const router = useRouter();
  const [groups, setGroups] = useState<Group[]>([]);
  const [filteredGroups, setFilteredGroups] = useState<Group[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [deleteDialog, setDeleteDialog] = useState<{
    isOpen: boolean;
    groupId: string;
    groupName: string;
  }>({ isOpen: false, groupId: "", groupName: "" });
  const [isDeleteLoading, setIsDeleteLoading] = useState(false);
  const tenantId = getCookie("currentOrganizationId") as string;
  const userId = getCookie("userId") as string;

  const fetchGroups = async () => {
    if (userId && tenantId) {
      try {
        setLoading(true);
        const result = await getGroups(userId, tenantId);
        if (result?.data) {
          setGroups(result.data);
          setFilteredGroups(result.data);
        }
      } catch (error) {
        console.error("Error fetching groups:", error);
        toast.error(t("groups.failedToFetchGroups"));
      } finally {
        setLoading(false);
      }
    }
  };

  useEffect(() => {
    fetchGroups();
  }, [session]);

  // Refresh data when page becomes visible
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (!document.hidden && !loading && groups.length >= 0) {
        fetchGroups();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [loading, groups.length]);

  // Add focus event listener to refresh data when user returns to the page
  useEffect(() => {
    const handleFocus = () => {
      // Only refresh if we have data already (not on initial load)
      if (!loading && groups.length >= 0) {
        fetchGroups();
      }
    };

    window.addEventListener('focus', handleFocus);

    return () => {
      window.removeEventListener('focus', handleFocus);
    };
  }, [loading, groups.length]);

  // Filter groups based on search term
  useEffect(() => {
    if (!searchTerm.trim()) {
      setFilteredGroups(groups);
    } else {
      const filtered = groups.filter(
        (group) =>
          group.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          group.description?.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setFilteredGroups(filtered);
    }
  }, [searchTerm, groups]);

  // Callback functions for real-time UI updates
  const handleGroupCreated = () => {
    fetchGroups(); // Refresh the groups list
  };

  const handleViewGroup = (groupId: string) => {
    router.push(`/settings/groups/${groupId}`);
  };

  const handleDeleteGroup = async (groupId: string) => {
    if (!tenantId) return;

    try {
      setIsDeleteLoading(true);
      const result = await deleteGroup(groupId, tenantId, userId);
      if (result?.error) {
        toast.error(result.error || t("common.error"));
        return;
      }
      toast.success(t("groups.groupDeletedSuccessfully"));

      // Update the local state
      const updatedGroups = groups.filter((group) => group.id !== groupId);
      setGroups(updatedGroups);
      setFilteredGroups(updatedGroups);

      // Close the dialog
      setDeleteDialog({ isOpen: false, groupId: "", groupName: "" });
    } catch (error) {
      toast.error(t("groups.failedToDeleteGroup"));
    } finally {
      setIsDeleteLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <h2 className="text-xl font-semibold mb-2">{t("common.loading")}</h2>
          <p className="text-muted-foreground">{t("groups.loadingGroups")}</p>
        </div>
      </div>
    );
  }

  return (
    <TooltipProvider>
      <div className="space-y-6">
        {/* Header Section */}
        <Card>
          <CardHeader className="p-2">
            <div className="flex flex-col gap-4 lg:flex-row lg:items-center lg:justify-between">
              {/* Left Section: Icon + Title + Description */}
              <div className="flex items-start gap-3">
                <div className="p-2 bg-primary/10 rounded-lg">
                  <Users className="h-6 w-6 text-primary" />
                </div>

                <div>
                  <div className="flex items-center gap-2">
                    <CardTitle className="text-2xl">
                      {t("groups.title")}
                    </CardTitle>
                    <Tooltip>
                      <TooltipTrigger>
                        <HelpCircle className="h-5 w-5 text-muted-foreground" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p className="max-w-xs">{t("groups.groupsHelp")}</p>
                      </TooltipContent>
                    </Tooltip>
                  </div>
                  <p className="text-muted-foreground mt-1">
                    {t("groups.groupsDescription")}
                  </p>
                </div>
              </div>

              {/* Right Section: Buttons */}
              <div className="flex lg:items-center gap-2 md:flex-col">
                <BulkGroupImportDialog
                  tenantId={tenantId}
                  onImportComplete={fetchGroups}
                />
                <CreateGroupDialog
                  tenantId={tenantId}
                  trigger={
                    <Button>
                      <Plus className="mr-2 h-4 w-4" />
                      {t("groups.createGroup")}
                    </Button>
                  }
                  userId={userId}
                  onGroupCreated={handleGroupCreated}
                />
              </div>
            </div>
          </CardHeader>
        </Card>

        {groups.length === 0 ? (
          <Card>
            <CardContent className="flex flex-col items-center justify-center p-12">
              <div className="bg-primary/10 p-4 rounded-full mb-6">
                <Users className="h-12 w-12 text-primary" />
              </div>
              <h2 className="text-2xl font-semibold mb-3">
                {t("groups.noGroupsYet")}
              </h2>
              <p className="text-muted-foreground mb-8 text-center max-w-md">
                {t("groups.createFirstGroupDescription")}
              </p>
              <CreateGroupDialog
                tenantId={tenantId}
                trigger={
                  <Button size="lg">
                    <Plus className="mr-2 h-5 w-5" />
                    {t("groups.createGroup")}
                  </Button>
                }
                userId={userId}
                onGroupCreated={handleGroupCreated}
              />
            </CardContent>
          </Card>
        ) : (
          <div className="space-y-6">
            {/* Search and Filter Section */}
            <div className="relative w-full max-w-sm">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input
                placeholder={t("groups.searchGroups")}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            {/* Groups Table */}
            <Card>
              <CardContent className="p-0">
                {filteredGroups.length === 0 ? (
                  <div className="flex flex-col items-center justify-center py-12 px-4">
                    <div className="bg-muted/50 p-3 rounded-full mb-4">
                      <Users className="h-8 w-8 text-muted-foreground" />
                    </div>
                    <h3 className="text-lg font-medium mb-2">
                      {searchTerm
                        ? t("groups.noGroupsFound")
                        : t("groups.noGroupsYet")}
                    </h3>
                    <p className="text-muted-foreground text-center mb-6">
                      {searchTerm
                        ? t("groups.tryDifferentSearch")
                        : t("groups.createFirstGroupDescription")}
                    </p>
                    {!searchTerm && (
                      <CreateGroupDialog
                        tenantId={tenantId}
                        trigger={
                          <Button size="sm">
                            <Plus className="mr-2 h-4 w-4" />
                            {t("groups.createGroup")}
                          </Button>
                        }
                        userId={userId}
                        onGroupCreated={handleGroupCreated}
                      />
                    )}
                  </div>
                ) : (
                  <div className="border rounded-lg overflow-hidden">
                    <Table>
                      <TableHeader>
                        <TableRow className="bg-muted/50 hover:bg-muted/50">
                          <TableHead>{t("groups.groupName")}</TableHead>
                          <TableHead>{t("common.description")}</TableHead>
                          <TableHead>{t("groups.members")}</TableHead>
                          <TableHead>{t("groups.role")}</TableHead>
                          <TableHead className="w-[100px] text-right">
                            {t("common.actions")}
                          </TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {filteredGroups.map((group) => (
                          <TableRow
                            key={group.id}
                            className="hover:bg-muted/50"
                            onClick={() => handleViewGroup(group.id)}
                          >
                            <TableCell>
                              <div className="flex items-center gap-3">
                                <div className="p-2 bg-primary/10 rounded-lg">
                                  <Users className="h-4 w-4 text-primary" />
                                </div>
                                <div>
                                  <div className="font-medium">
                                    {group.name}
                                  </div>
                                  <div className="text-sm text-muted-foreground">
                                    {new Date(
                                      group.createdAt
                                    ).toLocaleDateString()}
                                  </div>
                                </div>
                              </div>
                            </TableCell>
                            <TableCell className="text-muted-foreground">
                              {group.description || t("common.noDescription")}
                            </TableCell>
                            <TableCell>
                              <div className="flex items-center gap-2">
                                <div className="flex -space-x-2">
                                  {group.groupMembers
                                    .slice(0, 3)
                                    .map((member) => (
                                      <Avatar
                                        key={member.id}
                                        className="h-6 w-6 border-2 border-background"
                                      >
                                        <AvatarImage
                                          src={member.user.image}
                                          alt={member.user.name}
                                        />
                                        <AvatarFallback className="bg-primary/10 text-primary text-xs">
                                          {member.user.name
                                            ?.split(" ")
                                            .map((n: string) => n[0])
                                            .join("")}
                                        </AvatarFallback>
                                      </Avatar>
                                    ))}
                                  {group.groupMembers.length > 3 && (
                                    <div className="h-6 w-6 rounded-full bg-muted border-2 border-background flex items-center justify-center text-xs text-muted-foreground">
                                      +{group.groupMembers.length - 3}
                                    </div>
                                  )}
                                </div>
                                <span className="text-sm text-muted-foreground ml-2">
                                  {group.groupMembers.length}{" "}
                                  {group.groupMembers.length === 1
                                    ? t("common.member")
                                    : t("common.members")}
                                </span>
                              </div>
                            </TableCell>
                            <TableCell>
                              {group.customRole ? (
                                <Badge variant="secondary">
                                  {group.customRole.name}
                                </Badge>
                              ) : (
                                <span className="text-sm text-muted-foreground">
                                  {t("groups.noRoleAssigned")}
                                </span>
                              )}
                            </TableCell>
                            <TableCell className="text-right">
                              <DropdownMenu>
                                <DropdownMenuTrigger
                                  asChild
                                  onClick={(e) => e.stopPropagation()}
                                >
                                  <Button
                                    variant="ghost"
                                    size="icon"
                                    className="h-8 w-8"
                                  >
                                    <MoreHorizontal className="h-4 w-4" />
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                  <DropdownMenuItem
                                    onClick={() => handleViewGroup(group.id)}
                                    className="cursor-pointer"
                                  >
                                    <Eye className="mr-2 h-4 w-4" />
                                    {t("common.view")}
                                  </DropdownMenuItem>
                                  <DropdownMenuItem
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      setDeleteDialog({
                                        isOpen: true,
                                        groupId: group.id,
                                        groupName: group.name,
                                      });
                                    }}
                                    className="cursor-pointer text-destructive focus:text-destructive"
                                  >
                                    <Trash2 className="mr-2 h-4 w-4" />
                                    {t("common.delete")}
                                  </DropdownMenuItem>
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        )}

        {/* Delete Group Dialog */}
        <AlertDialog
          open={deleteDialog.isOpen}
          onOpenChange={(open) =>
            !open &&
            setDeleteDialog({ isOpen: false, groupId: "", groupName: "" })
          }
        >
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>
                {t("groups.deleteGroupConfirm")}
              </AlertDialogTitle>
              <AlertDialogDescription>
                {t("groups.deleteGroupDescription", {
                  groupName: deleteDialog.groupName,
                })}
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel disabled={isDeleteLoading}>
                {t("common.cancel")}
              </AlertDialogCancel>
              <AlertDialogAction
                onClick={() => handleDeleteGroup(deleteDialog.groupId)}
                disabled={isDeleteLoading}
                className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
              >
                {isDeleteLoading ? t("common.deleting") : t("common.delete")}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>
    </TooltipProvider>
  );
}
