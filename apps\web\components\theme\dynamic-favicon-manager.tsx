"use client";

import { useEffect, useRef } from "react";
import { ThemeConfig } from "@/types/theme-config";

interface DynamicFaviconManagerProps {
  themeConfig: ThemeConfig | null;
}

/**
 * Dynamic Favicon Manager
 * 
 * This component handles dynamic favicon and title updates that work properly with Next.js.
 * It addresses the following issues:
 * 1. Conflicts between Next.js static metadata and client-side updates
 * 2. Proper cleanup and persistence across navigation
 * 3. Real-time updates when theme configuration changes
 */
export function DynamicFaviconManager({ themeConfig }: DynamicFaviconManagerProps) {
  const isPartnerConsole = process.env.NEXT_PUBLIC_IS_PARTNER_CONSOLE === "1";
  const appliedConfigRef = useRef<ThemeConfig | null>(null);

  useEffect(() => {
    if (!isPartnerConsole || !themeConfig) return;

    // Only apply if the configuration has actually changed
    if (JSON.stringify(appliedConfigRef.current) === JSON.stringify(themeConfig)) {
      return;
    }

    console.log("DynamicFaviconManager: Applying theme config", themeConfig);

    // Update favicon
    if (themeConfig.faviconUrl) {
      updateFavicon(themeConfig.faviconUrl);
    }

    // Update document title
    if (themeConfig.brandName) {
      updateDocumentTitle(themeConfig.brandName);
    }

    // Store the applied configuration
    appliedConfigRef.current = themeConfig;
  }, [themeConfig, isPartnerConsole]);

  // Listen for real-time theme updates and branding updates
  useEffect(() => {
    if (!isPartnerConsole) return;

    const handleRealtimeThemeUpdate = (event: CustomEvent) => {
      const themeData = event.detail;
      console.log("DynamicFaviconManager: Received real-time theme update", themeData);

      if (themeData?.themeConfig) {
        const config = themeData.themeConfig;

        // Update favicon
        if (config.faviconUrl) {
          updateFavicon(config.faviconUrl);
        }

        // Update document title
        if (config.brandName) {
          updateDocumentTitle(config.brandName);
        }

        // Store the applied configuration
        appliedConfigRef.current = config;
      }
    };

    const handleBrandingUpdate = (event: CustomEvent) => {
      const themeData = event.detail;
      console.log("DynamicFaviconManager: Received branding update", themeData);

      if (themeData?.themeConfig) {
        const config = themeData.themeConfig;

        // Update favicon
        if (config.faviconUrl) {
          updateFavicon(config.faviconUrl);
        }

        // Update document title
        if (config.brandName) {
          updateDocumentTitle(config.brandName);
        }

        // Store the applied configuration
        appliedConfigRef.current = config;
      }
    };

    const handleThemeConfigUpdate = (event: CustomEvent) => {
      const themeData = event.detail;
      console.log("DynamicFaviconManager: Received theme config update", themeData);

      if (themeData?.themeConfig) {
        const config = themeData.themeConfig;

        // Update favicon
        if (config.faviconUrl) {
          updateFavicon(config.faviconUrl);
        }

        // Update document title
        if (config.brandName) {
          updateDocumentTitle(config.brandName);
        }

        // Store the applied configuration
        appliedConfigRef.current = config;
      }
    };

    window.addEventListener("realtimeThemeUpdate", handleRealtimeThemeUpdate as EventListener);
    window.addEventListener("brandingUpdate", handleBrandingUpdate as EventListener);
    window.addEventListener("themeConfigUpdated", handleThemeConfigUpdate as EventListener);

    return () => {
      window.removeEventListener("realtimeThemeUpdate", handleRealtimeThemeUpdate as EventListener);
      window.removeEventListener("brandingUpdate", handleBrandingUpdate as EventListener);
      window.removeEventListener("themeConfigUpdated", handleThemeConfigUpdate as EventListener);
    };
  }, [isPartnerConsole]);

  return null; // This component doesn't render anything
}

/**
 * Update favicon with proper Next.js compatibility
 */
function updateFavicon(faviconUrl: string) {
  try {
    // Remove only our dynamically created favicon links to avoid conflicts with Next.js metadata
    const dynamicFavicons = document.querySelectorAll('link[data-dynamic-favicon="true"]');
    dynamicFavicons.forEach(link => {
      try {
        if (link.parentNode) {
          link.parentNode.removeChild(link);
        }
      } catch (removeError) {
        console.warn('DynamicFaviconManager: Could not remove dynamic favicon link:', removeError);
      }
    });

    // Create new favicon links that match the Next.js structure with media queries
    // Light theme favicon
    const lightFaviconLink = document.createElement('link');
    lightFaviconLink.rel = 'icon';
    lightFaviconLink.type = 'image/png';
    lightFaviconLink.href = faviconUrl;
    lightFaviconLink.media = '(prefers-color-scheme: light)';
    lightFaviconLink.setAttribute('data-dynamic-favicon', 'true');
    document.head.appendChild(lightFaviconLink);

    // Dark theme favicon (using same URL for now)
    const darkFaviconLink = document.createElement('link');
    darkFaviconLink.rel = 'icon';
    darkFaviconLink.type = 'image/png';
    darkFaviconLink.href = faviconUrl;
    darkFaviconLink.media = '(prefers-color-scheme: dark)';
    darkFaviconLink.setAttribute('data-dynamic-favicon', 'true');
    document.head.appendChild(darkFaviconLink);

    // Also create a fallback favicon without media query for older browsers
    const fallbackFaviconLink = document.createElement('link');
    fallbackFaviconLink.rel = 'shortcut icon';
    fallbackFaviconLink.type = 'image/png';
    fallbackFaviconLink.href = faviconUrl;
    fallbackFaviconLink.setAttribute('data-dynamic-favicon', 'true');
    document.head.appendChild(fallbackFaviconLink);

    console.log('DynamicFaviconManager: Favicon updated successfully to:', faviconUrl);
  } catch (error) {
    console.error('DynamicFaviconManager: Error updating favicon:', error);
  }
}

/**
 * Update document title with proper handling
 */
function updateDocumentTitle(brandName: string) {
  try {
    // Get the current page title (everything after the first " - " if it exists)
    const currentTitle = document.title;
    const titleParts = currentTitle.split(' - ');
    
    let newTitle = brandName;
    
    // If there's a page-specific title, preserve it
    if (titleParts.length > 1) {
      const pageTitle = titleParts.slice(1).join(' - ');
      newTitle = `${pageTitle} - ${brandName}`;
    }
    
    document.title = newTitle;
    console.log('DynamicFaviconManager: Document title updated to:', newTitle);
  } catch (error) {
    console.error('DynamicFaviconManager: Error updating document title:', error);
  }
}

/**
 * Hook to manually trigger favicon and title updates
 */
export function useDynamicFavicon() {
  const isPartnerConsole = process.env.NEXT_PUBLIC_IS_PARTNER_CONSOLE === "1";

  const updateFaviconAndTitle = (themeConfig: ThemeConfig) => {
    if (!isPartnerConsole) return;

    if (themeConfig.faviconUrl) {
      updateFavicon(themeConfig.faviconUrl);
    }

    if (themeConfig.brandName) {
      updateDocumentTitle(themeConfig.brandName);
    }
  };

  const resetFaviconAndTitle = () => {
    if (!isPartnerConsole) return;

    // Remove dynamic favicons
    const dynamicFavicons = document.querySelectorAll('link[data-dynamic-favicon="true"]');
    dynamicFavicons.forEach(link => link.remove());

    // Reset title to default
    document.title = "Swiss Knowledge Hub";
  };

  return {
    updateFaviconAndTitle,
    resetFaviconAndTitle,
  };
}
