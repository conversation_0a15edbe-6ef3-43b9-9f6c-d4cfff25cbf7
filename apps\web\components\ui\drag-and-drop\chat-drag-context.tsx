"use client";

import React, { createContext, useContext, useState, useCallback } from 'react';
import { DragState, DragData, DropResult, ChatDragContextValue } from './types';
import { updateChat } from '@/services/src/chat';
import toast from 'react-hot-toast';
import { useLanguage } from '@/lib/language-context';

const ChatDragContext = createContext<ChatDragContextValue | null>(null);

interface ChatDragProviderProps {
  children: React.ReactNode;
  onChatMoved?: (chatId: string, newGroupId: string) => void;
}

export function ChatDragProvider({ children, onChatMoved }: ChatDragProviderProps) {
  const { t } = useLanguage();
  const [dragState, setDragState] = useState<DragState>({
    isDragging: false,
    draggedItem: null,
    dragOverGroupId: null,
    dragOverPosition: null,
  });

  const onDragStart = useCallback((data: DragData) => {
    setDragState({
      isDragging: true,
      draggedItem: data,
      dragOverGroupId: null,
      dragOverPosition: null,
    });
  }, []);

  const onDragEnd = useCallback(() => {
    setDragState({
      isDragging: false,
      draggedItem: null,
      dragOverGroupId: null,
      dragOverPosition: null,
    });
  }, []);

  const onDragOver = useCallback((groupId: string, position?: 'top' | 'bottom' | 'center') => {
    setDragState(prev => ({
      ...prev,
      dragOverGroupId: groupId,
      dragOverPosition: position || 'center',
    }));
  }, []);

  const onDrop = useCallback(async (result: DropResult) => {
    const { draggedItem } = dragState;
    
    if (!draggedItem) return;

    // Don't do anything if dropping in the same group
    if (draggedItem.sourceGroupId === result.targetGroupId) {
      onDragEnd();
      return;
    }

    try {
      toast.loading(t("chat.moving") || "Moving chat...");
      
      const response = await updateChat({
        id: draggedItem.chatId,
        groupId: result.targetGroupId,
      });

      toast.dismiss();

      if (response.error) {
        toast.error(response.error);
      } else {
        toast.success(t("chat.moveSuccess") || "Chat moved successfully");
        onChatMoved?.(draggedItem.chatId, result.targetGroupId);
        
        // Reload the page to refresh the sidebar
        window.location.reload();
      }
    } catch (error) {
      toast.dismiss();
      toast.error(t("chat.moveFailed") || "Failed to move chat");
      console.error('Error moving chat:', error);
    } finally {
      onDragEnd();
    }
  }, [dragState, onDragEnd, onChatMoved, t]);

  const value: ChatDragContextValue = {
    dragState,
    onDragStart,
    onDragEnd,
    onDragOver,
    onDrop,
  };

  return (
    <ChatDragContext.Provider value={value}>
      {children}
    </ChatDragContext.Provider>
  );
}

export function useChatDrag() {
  const context = useContext(ChatDragContext);
  if (!context) {
    throw new Error('useChatDrag must be used within a ChatDragProvider');
  }
  return context;
}
