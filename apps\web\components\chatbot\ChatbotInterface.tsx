"use client";

import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ChatbotAccessGuard } from './ChatbotAccessGuard';
import { MessageSquare, Send, Bot } from 'lucide-react';

interface ChatbotInterfaceProps {
  chatbotId: string;
  chatbotName?: string;
  showAccessDetails?: boolean;
}

/**
 * Example chatbot interface component that uses access control
 * This demonstrates how to protect chatbot interfaces with company-based access
 */
export const ChatbotInterface: React.FC<ChatbotInterfaceProps> = ({
  chatbotId,
  chatbotName = "Chatbot",
  showAccessDetails = false
}) => {
  const [message, setMessage] = React.useState("");
  const [messages, setMessages] = React.useState<Array<{
    id: string;
    content: string;
    sender: 'user' | 'bot';
    timestamp: Date;
  }>>([]);

  const handleSendMessage = () => {
    if (!message.trim()) return;

    const userMessage = {
      id: Date.now().toString(),
      content: message,
      sender: 'user' as const,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setMessage("");

    // Simulate bot response
    setTimeout(() => {
      const botMessage = {
        id: (Date.now() + 1).toString(),
        content: `Hello! I'm ${chatbotName}. I received your message: "${userMessage.content}". This is a demo response.`,
        sender: 'bot' as const,
        timestamp: new Date()
      };
      setMessages(prev => [...prev, botMessage]);
    }, 1000);
  };

  const chatInterface = (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Bot className="h-5 w-5" />
          {chatbotName}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Messages Area */}
        <div className="h-96 border rounded-lg p-4 overflow-y-auto bg-gray-50">
          {messages.length === 0 ? (
            <div className="flex items-center justify-center h-full text-muted-foreground">
              <div className="text-center">
                <MessageSquare className="h-8 w-8 mx-auto mb-2" />
                <p>Start a conversation with {chatbotName}</p>
              </div>
            </div>
          ) : (
            <div className="space-y-3">
              {messages.map((msg) => (
                <div
                  key={msg.id}
                  className={`flex ${msg.sender === 'user' ? 'justify-end' : 'justify-start'}`}
                >
                  <div
                    className={`max-w-xs lg:max-w-md px-3 py-2 rounded-lg ${
                      msg.sender === 'user'
                        ? 'bg-blue-500 text-white'
                        : 'bg-white border'
                    }`}
                  >
                    <p className="text-sm">{msg.content}</p>
                    <p className={`text-xs mt-1 ${
                      msg.sender === 'user' ? 'text-blue-100' : 'text-gray-500'
                    }`}>
                      {msg.timestamp.toLocaleTimeString()}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Input Area */}
        <div className="flex gap-2">
          <Input
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            placeholder="Type your message..."
            onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
            className="flex-1"
          />
          <Button onClick={handleSendMessage} disabled={!message.trim()}>
            <Send className="h-4 w-4" />
          </Button>
        </div>

        <p className="text-xs text-muted-foreground text-center">
          This is a demo interface. In a real implementation, messages would be sent to the chatbot API.
        </p>
      </CardContent>
    </Card>
  );

  return (
    <ChatbotAccessGuard 
      chatbotId={chatbotId} 
      showDetails={showAccessDetails}
    >
      {chatInterface}
    </ChatbotAccessGuard>
  );
};

// Example usage component
export const ChatbotDemo: React.FC = () => {
  const [selectedChatbotId, setSelectedChatbotId] = React.useState<string>("");

  return (
    <div className="space-y-6 p-6">
      <div className="text-center">
        <h1 className="text-2xl font-bold mb-2">Chatbot Access Control Demo</h1>
        <p className="text-muted-foreground">
          Enter a chatbot ID to test the access control system
        </p>
      </div>

      <Card className="max-w-md mx-auto">
        <CardContent className="p-4">
          <div className="space-y-3">
            <Input
              value={selectedChatbotId}
              onChange={(e) => setSelectedChatbotId(e.target.value)}
              placeholder="Enter chatbot ID..."
            />
            <Button 
              onClick={() => {/* This would navigate to the chatbot */}}
              disabled={!selectedChatbotId.trim()}
              className="w-full"
            >
              Test Chatbot Access
            </Button>
          </div>
        </CardContent>
      </Card>

      {selectedChatbotId && (
        <ChatbotInterface 
          chatbotId={selectedChatbotId}
          chatbotName="Demo Chatbot"
          showAccessDetails={true}
        />
      )}
    </div>
  );
};

export default ChatbotInterface;
