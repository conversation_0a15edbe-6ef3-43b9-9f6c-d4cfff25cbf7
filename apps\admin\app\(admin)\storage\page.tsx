import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import db from "@/lib/shared-db";
import { StorageChartsClient } from "@/components/storage/storage-charts-client";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/next-auth";
import { redirect } from "next/navigation";

async function fetchStorageData(tenantFilter: string = "", searchQuery: string = "") {
  

  const tenants = await db.tenant.findMany({
    select: {
      id: true,
      name: true,
      workspaces: {
        select: {
          files: {
            select: {
              size: true,
              createdAt: true,
            },
          },
        },
      },
    },
  });

  const storageUsageByTenant = tenants.map((tenant) => {
    let totalKB = 0;

    for (const workspace of tenant.workspaces) {
      for (const file of workspace.files) {
        const match = file.size?.match(/^([\d.]+)\s*(KB|MB|GB)?$/i);
        if (!match) continue;

        const value = parseFloat(match[1]);
        const unit = match[2]?.toUpperCase() || "KB";

        if (unit === "KB") totalKB += value;
        else if (unit === "MB") totalKB += value * 1024;
        else if (unit === "GB") totalKB += value * 1024 * 1024;
      }
    }

    const usedGB = parseFloat((totalKB / 1024 / 1024).toFixed(2));
    const allocatedGB = Math.max(usedGB + 10, 50); // dummy allocation
    return {
      name: tenant.name,
      used: usedGB,
      allocated: allocatedGB,
    };
  });

  // Apply filters if provided
  let filteredData = storageUsageByTenant;
  
  if (tenantFilter) {
    filteredData = filteredData.filter(tenant => tenant.name === tenantFilter);
  }
  
  if (searchQuery) {
    const searchTerm = searchQuery.toLowerCase().trim();
    filteredData = filteredData.filter(tenant => 
      tenant.name.toLowerCase().includes(searchTerm)
    );
  }
  
  return filteredData;
}

export default async function StoragePage({
  searchParams,
}: {
  searchParams: { search?: string; page?: string; tenant?: string };
}) {
  const session = await getServerSession(authOptions);
  if (!session) {
    redirect("/auth/signin");
  }
  
  // Parse search params
  const searchQuery = searchParams.search || "";
  const tenantFilter = searchParams.tenant || "";
  
  const tenantData = await fetchStorageData(tenantFilter, searchQuery);

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold">Storage Usage</h1>
        <p className="text-muted-foreground">Monitor storage usage across all tenants</p>
      </div>

      <StorageChartsClient tenantData={tenantData} />
    </div>
  );
}
