"use client";

import * as React from "react";
import Link from "next/link";
import { ChevronRight, Home } from "lucide-react";
import { cn } from "@/lib/utils";
import { usePathname } from "next/navigation";

interface BreadcrumbProps extends React.HTMLAttributes<HTMLDivElement> {
  segments?: {
    name: string;
    href: string;
  }[];
  separator?: React.ReactNode;
  home?: boolean;
}

export function Breadcrumb({
  segments: customSegments,
  separator = <ChevronRight className="h-4 w-4" />,
  home = true,
  className,
  ...props
}: BreadcrumbProps) {
  const pathname = usePathname();

  // Generate segments from pathname if not provided
  const segments = React.useMemo(() => {
    if (customSegments) return customSegments;

    // Skip empty segments and filter out any admin-specific paths
    const paths = (pathname || "")
      .split("/")
      .filter(
        (segment) => segment && segment !== "admin" && segment !== "(admin)"
      );

    return paths.map((segment, index) => {
      // Build the href up to this segment
      const href = `/${paths.slice(0, index + 1).join("/")}`;

      // Format the segment name (capitalize, replace hyphens with spaces)
      const name = segment
        .replace(/-/g, " ")
        .replace(/\b\w/g, (char) => char.toUpperCase());

      return { name, href };
    });
  }, [pathname, customSegments]);

  if (segments.length === 0) return null;

  return (
    <nav
      aria-label="Breadcrumb"
      className={cn(
        "flex items-center text-sm text-muted-foreground",
        className
      )}
      {...props}
    >
      <ol className="flex items-center space-x-2">
        {home && (
          <li>
            <Link
              href="/dashboard"
              className="flex items-center hover:text-foreground"
            >
              <Home className="h-4 w-4" />
              <span className="sr-only">Home</span>
            </Link>
          </li>
        )}

        {home && segments.length > 0 && (
          <li className="flex items-center">{separator}</li>
        )}

        {segments.map((segment, index) => (
          <React.Fragment key={segment.href}>
            <li>
              <Link
                href={segment.href}
                className={cn(
                  "hover:text-foreground",
                  index === segments.length - 1 && "font-medium text-foreground"
                )}
              >
                {segment.name}
              </Link>
            </li>

            {index < segments.length - 1 && (
              <li className="flex items-center">{separator}</li>
            )}
          </React.Fragment>
        ))}
      </ol>
    </nav>
  );
}
