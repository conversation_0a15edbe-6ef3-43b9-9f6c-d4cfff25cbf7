import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/next-auth";
import db from "@/lib/shared-db";
import crypto from "crypto";

// GET /api/companies/[id] - Get a specific company
export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const session: any = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id } = params;

    // Get user
    const emailHash = crypto
      .createHash("sha256")
      .update(session.user.email)
      .digest("hex");

    const user = await db.user.findUnique({
      where: { emailHash },
      include: {
        membership: true,
      },
    });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Get current organization from session or first membership
    const currentOrgId =
      session.currentOrganization?.id || user.membership[0]?.tenantId;

    // Get company details
    const company = await db.company.findUnique({
      where: { id },
      include: {
        members: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
                createdAt: true,
              },
            },
          },
        },
        tenant: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    if (!company) {
      return NextResponse.json({ error: "Company not found" }, { status: 404 });
    }

    // Check if user has access to this company (either through tenant membership or company membership)
    const hasTenantAccess = user.membership.find(
      (m) => m.tenantId === company.tenantId
    );
    const hasCompanyAccess = company.members.find((m) => m.userId === user.id);

    if (!hasTenantAccess && !hasCompanyAccess) {
      return NextResponse.json({ error: "Access denied" }, { status: 403 });
    }

    // Format company data
    const formattedCompany = {
      id: company.id,
      name: company.name,
      slug: company.slug,
      description: company.description || "",
      maxSignups: company.maxSignups,
      currentSignups: company.members.length,
      inviteLink:
        company.inviteLink ||
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/invite/${company.slug}`,
      createdAt: company.createdAt.toISOString().split("T")[0],
      members: company.members.map((m: any) => ({
        id: m.id,
        name: m.user.name,
        email: m.user.email,
        role: m.role.toLowerCase(),
        joinedAt: m.joinedAt.toISOString().split("T")[0],
        status: m.status.toLowerCase(),
      })),
    };

    return NextResponse.json({ company: formattedCompany });
  } catch (error) {
    console.error("Error fetching company:", error);
    return NextResponse.json(
      { error: "Failed to fetch company" },
      { status: 500 }
    );
  }
}

// PUT /api/companies/[id] - Update a company
export async function PUT(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const session: any = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id } = params;
    const { name, description, maxSignups } = await request.json();

    // Get user
    const emailHash = crypto
      .createHash("sha256")
      .update(session.user.email)
      .digest("hex");

    const user = await db.user.findUnique({
      where: { emailHash },
      include: {
        membership: true,
      },
    });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Get company to check access
    const company = await db.company.findUnique({
      where: { id },
      include: {
        members: {
          where: { userId: user.id },
        },
      },
    });

    if (!company) {
      return NextResponse.json({ error: "Company not found" }, { status: 404 });
    }

    // Check if user has admin access to this company (either through tenant or company membership)
    const hasTenantAccess = user.membership.find(
      (m: any) => m.tenantId === company.tenantId
    );
    const companyMember = company.members[0];

    if (
      !hasTenantAccess &&
      (!companyMember || !["OWNER", "ADMIN"].includes(companyMember.role))
    ) {
      return NextResponse.json(
        { error: "Insufficient permissions" },
        { status: 403 }
      );
    }

    // Update company
    const updatedCompany = await db.company.update({
      where: { id },
      data: {
        name: name || undefined,
        description: description || undefined,
        maxSignups: maxSignups || undefined,
      },
      include: {
        members: true,
      },
    });

    // Format response
    const formattedCompany = {
      id: updatedCompany.id,
      name: updatedCompany.name,
      slug: updatedCompany.slug,
      description: updatedCompany.description || "",
      maxSignups: updatedCompany.maxSignups,
      currentSignups: updatedCompany.members.length,
      inviteLink:
        updatedCompany.inviteLink ||
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/invite/${updatedCompany.slug}`,
      createdAt: updatedCompany.createdAt.toISOString().split("T")[0],
    };

    return NextResponse.json({
      message: "Company updated successfully",
      company: formattedCompany,
    });
  } catch (error) {
    console.error("Error updating company:", error);
    return NextResponse.json(
      { error: "Failed to update company" },
      { status: 500 }
    );
  }
}

// DELETE /api/companies/[id] - Delete a company
export async function DELETE(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const session: any = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id } = params;

    // Get user
    const emailHash = crypto
      .createHash("sha256")
      .update(session.user.email)
      .digest("hex");

    const user = await db.user.findUnique({
      where: { emailHash },
      include: {
        membership: true,
      },
    });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Get company to check access and ownership
    const company = await db.company.findUnique({
      where: { id },
      include: {
        members: {
          where: { userId: user.id },
        },
      },
    });

    if (!company) {
      return NextResponse.json({ error: "Company not found" }, { status: 404 });
    }

    // Check if user has owner access to this company (either through tenant or company membership)
    const hasTenantAccess = user.membership.find(
      (m: any) => m.tenantId === company.tenantId
    );
    const companyMember = company.members[0];

    if (
      !hasTenantAccess &&
      (!companyMember || companyMember.role !== "OWNER")
    ) {
      return NextResponse.json(
        { error: "Only owners can delete companies" },
        { status: 403 }
      );
    }

    // Delete company and related data in a transaction
    await db.$transaction(async (tx: any) => {
      // Delete company members
      await tx.companyMember.deleteMany({
        where: { companyId: id },
      });

      // Remove company references from chatbots
      const chatbotsWithCompany = await tx.chatbot.findMany({
        where: {
          companyIds: {
            has: id,
          },
        },
      });

      for (const chatbot of chatbotsWithCompany) {
        const updatedCompanyIds = chatbot.companyIds.filter((companyId: string) => companyId !== id);
        await tx.chatbot.update({
          where: { id: chatbot.id },
          data: { companyIds: updatedCompanyIds },
        });
      }

      // Delete other related data as needed
      // (company-specific data, etc.)

      // Finally delete the company
      await tx.company.delete({
        where: { id },
      });
    });

    return NextResponse.json({
      message: "Company deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting company:", error);
    return NextResponse.json(
      { error: "Failed to delete company" },
      { status: 500 }
    );
  }
}
