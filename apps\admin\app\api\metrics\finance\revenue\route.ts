import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/next-auth";
import db from "@/lib/shared-db";

export async function GET(request: Request) {
  const session = await getServerSession(authOptions);

  if (!session) {
    return NextResponse.json(
      { error: "Unauthorized" },
      { status: 401 }
    );
  }

  try {
    // Get all active subscriptions
    const activeSubscriptions = await db.subscription.findMany({
      where: {
        isActive: true
      },
      include: {
        plan: true
      }
    });

    // Calculate MRR and ARR
    let mrr = 0;
    let arr = 0;

    activeSubscriptions.forEach(subscription => {
      // Get base plan price
      const basePrice = subscription.billingInterval === 'month' 
        ? subscription.plan.price || 0
        : (subscription.plan.price || 0) / 12;
      
      // Add additional users cost
      const additionalUsersCost = subscription.additionalUsers * 
        (subscription.billingInterval === 'month' 
          ? subscription.plan.additionalUserFee || 0
          : (subscription.plan.additionalUserFee || 0) / 12);
      
      // Add additional storage cost (simplified calculation)
      const additionalStorageCost = subscription.additionalStorageGB * 10; // Assuming $10 per GB
      
      // Calculate total monthly revenue for this subscription
      const subscriptionMrr = basePrice + additionalUsersCost + additionalStorageCost;
      
      mrr += subscriptionMrr;
    });

    // ARR is MRR * 12
    arr = mrr * 12;

    // Get revenue by plan type
    const revenueByPlan = await db.subscription.groupBy({
      by: ['planId'],
      where: {
        isActive: true
      },
      _count: {
        _all: true
      }
    });

    // Get plan details
    const planIds = revenueByPlan.map(item => item.planId);
    const plans = await db.plan.findMany({
      where: {
        id: {
          in: planIds
        }
      },
      select: {
        id: true,
        name: true,
        type: true
      }
    });

    // Map plan names to the results
    const revenueByPlanWithNames = revenueByPlan.map(item => {
      const plan = plans.find(p => p.id === item.planId);
      return {
        planId: item.planId,
        planName: plan?.name || 'Unknown',
        planType: plan?.type || 'Unknown',
        subscriptionCount: item._count._all
      };
    });

    return NextResponse.json({
      mrr: Math.round(mrr * 100) / 100,
      arr: Math.round(arr * 100) / 100,
      activeSubscriptions: activeSubscriptions.length,
      revenueByPlan: revenueByPlanWithNames
    });
  } catch (error) {
    console.error("Error fetching revenue metrics:", error);
    return NextResponse.json(
      { error: "Failed to fetch revenue metrics" },
      { status: 500 }
    );
  }
}
