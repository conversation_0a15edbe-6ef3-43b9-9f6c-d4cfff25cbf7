import { NextRequest, NextResponse } from "next/server";
import db from "@/lib/shared-db";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/next-auth";

export async function GET(
  req: Request,
  { params }: { params: { chatbotId: string } }
) {
  try {
    const session: any = await getServerSession(authOptions);
    if (!session) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const url = new URL(req.url);
    const tenantId = url.searchParams.get("tenantId");

    if (!tenantId) {
      return NextResponse.json(
        { error: "Tenant ID is required" },
        { status: 400 }
      );
    }

    // Check if chatbot exists and user has access
    const chatbot = await db.chatbot.findUnique({
      where: {
        id: params.chatbotId,
      },
    });

    if (!chatbot) {
      return NextResponse.json({ error: "Chatbot not found" }, { status: 404 });
    }

    // Verify user has access to this chatbot
    if (chatbot.userId !== session.userId || chatbot.tenantId !== tenantId) {
      return NextResponse.json({ error: "Access denied" }, { status: 403 });
    }

    // Check if chatbot has an API key
    if (!chatbot.apiKey) {
      return NextResponse.json(
        {
          error:
            "Chatbot must have an API key to generate embed code. Please generate an API key first.",
        },
        { status: 400 }
      );
    }

    // Get the base URL for the SDK
    const baseUrl = process.env.NEXT_PUBLIC_API_BASE_URL;
    const sdkUrl = `${baseUrl}/sdk/chatbot-sdk.js`;

    // Parse customization settings
    let customization: any = {};
    if (chatbot.customization) {
      try {
        customization =
          typeof chatbot.customization === "string"
            ? JSON.parse(chatbot.customization)
            : chatbot.customization;
      } catch (error) {
        console.error("Error parsing customization JSON:", error);
        customization = {};
      }
    }

    // Generate embed code based on chatbot type
    let embedCode = "";

    switch (chatbot.type) {
      case "web-snippet":
        embedCode = `<!-- Swiss Knowledge Hub Chatbot -->
<script src="${sdkUrl}"></script>
<script>
  SwissKnowledgeHub.init({
    chatbotId: '${chatbot.id}',
    apiKey: '${chatbot.apiKey}',
    position: '${customization.position || "bottom-right"}',
    size: '${customization.size || "medium"}',
    theme: {
      primaryColor: '${customization.theme?.primaryColor || "#007bff"}',
      secondaryColor: '${customization.theme?.secondaryColor || "#6c757d"}',
      fontFamily: '${customization.theme?.fontFamily || "Inter, sans-serif"}',
      borderRadius: '${customization.theme?.borderRadius || "8px"}'
    },
    greeting: '${
      customization.greeting ||
      `Hi! I'm ${chatbot.name}. How can I help you today?`
    }',
    placeholder: '${customization.placeholder || "Type your message..."}',
    showBranding: ${customization.showBranding !== false}
  });
</script>`;
        break;

      case "inline-embedding":
        embedCode = `<!-- Swiss Knowledge Hub Chatbot - Inline -->
<div id="skh-chatbot-container"></div>
<script src="${sdkUrl}"></script>
<script>
  SwissKnowledgeHub.init({
    chatbotId: '${chatbot.id}',
    apiKey: '${chatbot.apiKey}',
    container: '#skh-chatbot-container',
    mode: 'inline',
    theme: {
      primaryColor: '${customization.theme?.primaryColor || "#007bff"}',
      secondaryColor: '${customization.theme?.secondaryColor || "#6c757d"}',
      fontFamily: '${customization.theme?.fontFamily || "Inter, sans-serif"}',
      borderRadius: '${customization.theme?.borderRadius || "8px"}'
    },
    greeting: '${
      customization.greeting ||
      `Hi! I'm ${chatbot.name}. How can I help you today?`
    }',
    placeholder: '${customization.placeholder || "Type your message..."}',
    showBranding: ${customization.showBranding !== false}
  });
</script>`;
        break;

      case "dedicated-page":
        const dedicatedUrl = `${baseUrl}/chat/${chatbot.id}`;
        embedCode = `<!-- Swiss Knowledge Hub Chatbot - Dedicated Page -->
<!-- Redirect users to: ${dedicatedUrl} -->
<a href="${dedicatedUrl}" target="_blank" class="skh-chat-button">
  Chat with ${chatbot.name}
</a>

<!-- Or embed as iframe -->
<iframe 
  src="${dedicatedUrl}" 
  width="100%" 
  height="600" 
  frameborder="0"
  title="${chatbot.name} Chat">
</iframe>`;
        break;

      default:
        return NextResponse.json(
          { error: "Invalid chatbot type" },
          { status: 400 }
        );
    }

    // Generate additional integration examples
    const integrationExamples = {
      react: `// React Component Integration
import { useEffect } from 'react';

export function ChatbotWidget() {
  useEffect(() => {
    // Load SDK script
    const script = document.createElement('script');
    script.src = '${sdkUrl}';
    script.onload = () => {
      window.SwissKnowledgeHub.init({
        chatbotId: '${chatbot.id}',
        apiKey: '${chatbot.apiKey}',
        position: '${customization.position || "bottom-right"}'
      });
    };
    document.head.appendChild(script);

    return () => {
      // Cleanup
      if (window.SwissKnowledgeHub) {
        window.SwissKnowledgeHub.destroy();
      }
    };
  }, []);

  return null;
}`,

      vue: `<!-- Vue.js Component Integration -->
<template>
  <div></div>
</template>

<script>
export default {
  name: 'ChatbotWidget',
  mounted() {
    this.loadChatbot();
  },
  beforeDestroy() {
    if (window.SwissKnowledgeHub) {
      window.SwissKnowledgeHub.destroy();
    }
  },
  methods: {
    loadChatbot() {
      const script = document.createElement('script');
      script.src = '${sdkUrl}';
      script.onload = () => {
        window.SwissKnowledgeHub.init({
          chatbotId: '${chatbot.id}',
          apiKey: '${chatbot.apiKey}',
          position: '${customization.position || "bottom-right"}'
        });
      };
      document.head.appendChild(script);
    }
  }
}
</script>`,

      angular: `// Angular Component Integration
import { Component, OnInit, OnDestroy } from '@angular/core';

@Component({
  selector: 'app-chatbot',
  template: ''
})
export class ChatbotComponent implements OnInit, OnDestroy {
  
  ngOnInit() {
    this.loadChatbot();
  }

  ngOnDestroy() {
    if ((window as any).SwissKnowledgeHub) {
      (window as any).SwissKnowledgeHub.destroy();
    }
  }

  private loadChatbot() {
    const script = document.createElement('script');
    script.src = '${sdkUrl}';
    script.onload = () => {
      (window as any).SwissKnowledgeHub.init({
        chatbotId: '${chatbot.id}',
        apiKey: '${chatbot.apiKey}',
        position: '${customization.position || "bottom-right"}'
      });
    };
    document.head.appendChild(script);
  }
}`,
    };

    return NextResponse.json({
      embedCode,
      scriptUrl: sdkUrl,
      chatbotId: chatbot.id,
      type: chatbot.type,
      integrationExamples,
      configuration: {
        position: customization.position || "bottom-right",
        size: customization.size || "medium",
        theme: customization.theme || {},
        greeting:
          customization.greeting ||
          `Hi! I'm ${chatbot.name}. How can I help you today?`,
        placeholder: customization.placeholder || "Type your message...",
        showBranding: customization.showBranding !== false,
      },
    });
  } catch (error) {
    console.error("[CHATBOT_EMBED_CODE_GET]", error);
    return NextResponse.json({ error: "Internal Error" }, { status: 500 });
  }
}
