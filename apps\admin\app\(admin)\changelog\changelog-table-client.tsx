"use client";

import React, { useState, useTransition } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Search,
  Calendar,
  User,
  GitBranch,
  Package,
  Plus,
  MoreVertical,
  Edit,
  Trash2,
  Eye,
} from "lucide-react";
import { formatDate } from "@/lib/utils";
import { ChangelogDetailModal } from "./changelog-detail-modal";
import { ChangelogCreateModal } from "./changelog-create-modal";
import { ChangelogDeleteModal } from "./changelog-delete-modal";
import { ChangelogEditModal } from "./changelog-edit-modal";

// TypeScript interfaces
interface Changelog {
  id: string;
  title: string;
  content: string;
  version?: string;
  type: "RELEASE" | "HOTFIX" | "MAINTENANCE" | "ANNOUNCEMENT";
  priority: "LOW" | "MEDIUM" | "HIGH" | "CRITICAL";
  isActive: boolean;
  targetTenants: string[];
  targetEnvironment?: string;
  publishedAt: Date;
  expiresAt?: Date;
  githubCommitSha?: string;
  deploymentId?: string;
  authorId?: string;
  createdAt: Date;
  updatedAt: Date;
}

interface ChangelogData {
  changelogs: Changelog[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  searchQuery: string;
}

interface ChangelogTableClientProps {
  data: ChangelogData;
}

// Helper functions for styling
const getTypeBadgeStyle = (type: string) => {
  switch (type) {
    case "RELEASE":
      return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";
    case "HOTFIX":
      return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200";
    case "MAINTENANCE":
      return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200";
    case "ANNOUNCEMENT":
      return "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200";
    default:
      return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200";
  }
};

const getPriorityBadgeStyle = (priority: string) => {
  switch (priority) {
    case "CRITICAL":
      return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200";
    case "HIGH":
      return "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200";
    case "MEDIUM":
      return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200";
    case "LOW":
      return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200";
    default:
      return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200";
  }
};

const getTypeLabel = (type: string) => {
  switch (type) {
    case "RELEASE":
      return "New Feature";
    case "HOTFIX":
      return "Bug Fix";
    case "MAINTENANCE":
      return "Improvement";
    case "ANNOUNCEMENT":
      return "Announcement";
    default:
      return type;
  }
};

export function ChangelogTableClient({ data }: ChangelogTableClientProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [isPending, startTransition] = useTransition();
  const [searchQuery, setSearchQuery] = useState(data.searchQuery);
  const [selectedChangelog, setSelectedChangelog] = useState<Changelog | null>(
    null
  );
  const [detailModalOpen, setDetailModalOpen] = useState(false);
  const [createModalOpen, setCreateModalOpen] = useState(false);
  const [editModalOpen, setEditModalOpen] = useState(false);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [changelogToEdit, setChangelogToEdit] = useState<Changelog | null>(
    null
  );
  const [changelogToDelete, setChangelogToDelete] = useState<Changelog | null>(
    null
  );

  const { changelogs } = data;

  // Handle changelog click
  const handleChangelogClick = (changelog: Changelog) => {
    setSelectedChangelog(changelog);
    setDetailModalOpen(true);
  };

  // Handle successful changelog creation
  const handleCreateSuccess = () => {
    // Refresh the page to show the new changelog
    window.location.reload();
  };

  // Handle edit action
  const handleEditClick = (changelog: Changelog, e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent triggering the detail modal
    setChangelogToEdit(changelog);
    setEditModalOpen(true);
  };

  // Handle delete action
  const handleDeleteClick = (changelog: Changelog, e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent triggering the detail modal
    setChangelogToDelete(changelog);
    setDeleteModalOpen(true);
  };

  // Handle successful edit/delete
  const handleEditDeleteSuccess = () => {
    // Refresh the page to show the updated changelog list
    window.location.reload();
  };

  // Handle view public changelog
  const handleViewPublicChangelog = () => {
    const baseUrl = process.env.NEXT_PUBLIC_WEB_URL || "http://localhost:3000";
    const publicChangelogUrl = `${baseUrl}/changelog`;
    window.open(publicChangelogUrl, "_blank");
  };

  // Handle search with debounce
  const handleSearch = React.useCallback(
    (query: string) => {
      const params = new URLSearchParams(searchParams?.toString() || "");
      if (query) {
        params.set("search", query);
      } else {
        params.delete("search");
      }

      startTransition(() => {
        router.push(`/changelog?${params.toString()}`);
      });
    },
    [router, searchParams]
  );

  // Debounced search effect
  React.useEffect(() => {
    const timer = setTimeout(() => {
      if (searchQuery !== data.searchQuery) {
        handleSearch(searchQuery);
      }
    }, 300);

    return () => clearTimeout(timer);
  }, [searchQuery, data.searchQuery, handleSearch]);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Changelog</h1>
          <p className="text-muted-foreground">
            Manage and view all system changelog entries
          </p>
        </div>
        <div className="flex items-center space-x-3">
          {/* Search Input */}
          <div className="relative">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search changelogs..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-[250px] pl-9"
              disabled={isPending}
            />
          </div>

          {/* View Public Changelog Button */}
          <Button
            variant="outline"
            size="sm"
            onClick={handleViewPublicChangelog}
            className="flex items-center space-x-2"
          >
            <Eye className="h-4 w-4" />
            <span>View</span>
          </Button>

          {/* Create Changelog Button */}
          <Button
            onClick={() => setCreateModalOpen(true)}
            className="flex items-center space-x-2"
          >
            <Plus className="h-4 w-4" />
            <span>Create Changelog</span>
          </Button>
        </div>
      </div>

      {/* Main Content */}
      <div>
        {changelogs.length === 0 ? (
          <div className="flex flex-col items-center justify-center py-12 text-center">
            <Package className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold mb-2">
              No changelog entries found
            </h3>
            <p className="text-muted-foreground">
              {data.searchQuery
                ? "Try adjusting your search criteria"
                : "No changelog entries have been created yet"}
            </p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {changelogs.map((changelog) => (
              <Card
                key={changelog.id}
                className="hover:shadow-md transition-shadow cursor-pointer relative"
                onClick={() => handleChangelogClick(changelog)}
              >
                <CardContent className="p-4 space-y-3">
                  {/* Header with Action Dropdown */}
                  <div className="flex items-start justify-between">
                    <div className="flex-1 min-w-0">
                      {/* Title - Single Line */}
                      <h3 className="text-base font-semibold truncate pr-2">
                        {changelog.title}
                      </h3>
                    </div>

                    {/* Action Dropdown */}
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-6 w-6 p-0 hover:bg-muted flex-shrink-0"
                          onClick={(e) => e.stopPropagation()}
                        >
                          <MoreVertical className="h-3 w-3" />
                          <span className="sr-only">Open menu</span>
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end" className="w-40">
                        <DropdownMenuItem
                          onClick={(e) => handleEditClick(changelog, e)}
                          className="cursor-pointer"
                        >
                          <Edit className="mr-2 h-4 w-4" />
                          Edit
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={(e) => handleDeleteClick(changelog, e)}
                          className="cursor-pointer text-red-600 focus:text-red-600"
                        >
                          <Trash2 className="mr-2 h-4 w-4" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>

                  {/* Version, Badge and Date - Second Line */}
                  <div className="flex items-center justify-between flex-wrap gap-2">
                    <div className="flex items-center space-x-2 flex-wrap">
                      {changelog.version && (
                        <Badge variant="outline" className="text-xs">
                          v{changelog.version}
                        </Badge>
                      )}
                      <Badge className={getTypeBadgeStyle(changelog.type)}>
                        {getTypeLabel(changelog.type)}
                      </Badge>
                      <Badge
                        className={getPriorityBadgeStyle(changelog.priority)}
                      >
                        {changelog.priority}
                      </Badge>
                      {!changelog.isActive && (
                        <Badge
                          variant="outline"
                          className="text-muted-foreground"
                        >
                          Inactive
                        </Badge>
                      )}
                    </div>
                    <div className="flex items-center space-x-1 text-xs text-muted-foreground">
                      <Calendar className="h-3 w-3" />
                      <span>{formatDate(changelog.publishedAt)}</span>
                    </div>
                  </div>

                  {/* Description - Limited to 180 characters */}
                  <div className="text-sm text-muted-foreground">
                    <p className="line-clamp-3">
                      {changelog.content.length > 180
                        ? `${changelog.content.substring(0, 180)}...`
                        : changelog.content}
                    </p>
                  </div>

                  {/* Metadata Footer */}
                  {(changelog.targetEnvironment ||
                    changelog.githubCommitSha ||
                    changelog.targetTenants.length > 0) && (
                    <div className="flex flex-wrap items-center gap-3 text-xs text-muted-foreground pt-2 border-t">
                      {changelog.targetEnvironment && (
                        <div className="flex items-center space-x-1">
                          <span>Env:</span>
                          <Badge variant="outline" className="text-xs">
                            {changelog.targetEnvironment}
                          </Badge>
                        </div>
                      )}
                      {changelog.githubCommitSha && (
                        <div className="flex items-center space-x-1">
                          <GitBranch className="h-3 w-3" />
                          <span>
                            {changelog.githubCommitSha.substring(0, 7)}
                          </span>
                        </div>
                      )}
                      {changelog.targetTenants.length > 0 && (
                        <div className="flex items-center space-x-1">
                          <span>Tenants:</span>
                          <Badge variant="outline" className="text-xs">
                            {changelog.targetTenants.length}
                          </Badge>
                        </div>
                      )}
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>

      {/* Changelog Detail Modal */}
      <ChangelogDetailModal
        changelog={selectedChangelog}
        open={detailModalOpen}
        onOpenChange={setDetailModalOpen}
      />

      {/* Changelog Create Modal */}
      <ChangelogCreateModal
        open={createModalOpen}
        onOpenChange={setCreateModalOpen}
        onSuccess={handleCreateSuccess}
      />

      {/* Changelog Edit Modal */}
      <ChangelogEditModal
        changelog={changelogToEdit}
        open={editModalOpen}
        onOpenChange={setEditModalOpen}
        onSuccess={handleEditDeleteSuccess}
      />

      {/* Changelog Delete Modal */}
      <ChangelogDeleteModal
        changelog={changelogToDelete}
        open={deleteModalOpen}
        onOpenChange={setDeleteModalOpen}
        onSuccess={handleEditDeleteSuccess}
      />
    </div>
  );
}
