import { NextResponse } from "next/server";
import db from "@/lib/shared-db";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/next-auth";
import { nanoid } from "nanoid";

// Create or get thread share
export async function POST(
  req: Request,
  { params }: { params: { chatId: string } }
) {
  try {
    const session: any = await getServerSession(authOptions);
    if (!session) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const { isPublic, expiresAt } = await req.json();

    // Verify user has access to this chat
    const chat = await db.chat.findFirst({
      where: {
        id: params.chatId,
      },
      include: {
        user: true,
      },
    });

    if (!chat) {
      return new NextResponse("Chat not found", { status: 404 });
    }

    // Check if share already exists
    let threadShare = await db.threadShare.findFirst({
      where: {
        chatId: params.chatId,
      },
    });

    if (threadShare) {
      // Update existing share
      threadShare = await db.threadShare.update({
        where: { id: threadShare.id },
        data: {
          isPublic: isPublic ?? threadShare.isPublic,
          expiresAt: expiresAt ? new Date(expiresAt) : threadShare.expiresAt,
        },
      });
    } else {
      // Create new share
      threadShare = await db.threadShare.create({
        data: {
          chatId: params.chatId,
          shareToken: nanoid(32), // Generate unique token
          isPublic: isPublic ?? false,
          expiresAt: expiresAt ? new Date(expiresAt) : null,
          createdById: session.userId,
          tenantId: chat.tenantId, // Use the chat's tenantId
        },
      });
    }

    // Generate share URL
    const shareUrl = `${process.env.NEXT_PUBLIC_API_BASE_URL}/shared/thread/${threadShare.shareToken}`;

    return NextResponse.json({
      shareToken: threadShare.shareToken,
      shareUrl,
      isPublic: threadShare.isPublic,
      expiresAt: threadShare.expiresAt,
    });
  } catch (error) {
    console.error("[THREAD_SHARE_POST]", error);
    return NextResponse.json({ error: "Internal Error" }, { status: 500 });
  }
}

// Get thread share info
export async function GET(
  req: Request,
  { params }: { params: { chatId: string } }
) {
  try {
    const session: any = await getServerSession(authOptions);
    if (!session) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    // Verify user has access to this chat
    const chat = await db.chat.findFirst({
      where: {
        id: params.chatId,
      },
    });

    if (!chat) {
      return new NextResponse("Chat not found", { status: 404 });
    }

    const threadShare = await db.threadShare.findFirst({
      where: {
        chatId: params.chatId,
      },
      include: {
        createdBy: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    if (!threadShare) {
      return NextResponse.json({ shared: false });
    }

    const shareUrl = `${process.env.NEXT_PUBLIC_API_BASE_URL}/shared/thread/${threadShare.shareToken}`;

    return NextResponse.json({
      shared: true,
      shareToken: threadShare.shareToken,
      shareUrl,
      isPublic: threadShare.isPublic,
      expiresAt: threadShare.expiresAt,
      createdBy: threadShare.createdBy,
      createdAt: threadShare.createdAt,
    });
  } catch (error) {
    console.error("[THREAD_SHARE_GET]", error);
    return NextResponse.json({ error: "Internal Error" }, { status: 500 });
  }
}

// Delete thread share
export async function DELETE(
  req: Request,
  { params }: { params: { chatId: string } }
) {
  try {
    const session: any = await getServerSession(authOptions);
    if (!session) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    // Verify user has access to this chat
    const chat = await db.chat.findFirst({
      where: {
        id: params.chatId,
      },
    });

    if (!chat) {
      return new NextResponse("Chat not found", { status: 404 });
    }

    await db.threadShare.deleteMany({
      where: {
        chatId: params.chatId,
      },
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("[THREAD_SHARE_DELETE]", error);
    return NextResponse.json({ error: "Internal Error" }, { status: 500 });
  }
}
