"use client";

import React, { useState, useEffect } from "react";
import { useRouter, useParams } from "next/navigation";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";

import {
  ArrowLeft,
  Save,
  Bot,
  Palette,
  Lock,
  Sliders,
  Code,
  Globe,
  Monitor,
} from "lucide-react";
import Link from "next/link";
import {
  chatbotService,
  ChatbotConfig,
  getCompaniesForDropdown,
} from "@/services";
import { getCookie } from "@/utils/cookies";
import { toast } from "react-hot-toast";
import { useLanguage } from "@/lib/language-context";

export default function EditChatbotPage() {
  const { t } = useLanguage();
  const router = useRouter();
  const params = useParams();
  const chatbotId = params?.id as string;

  const [config, setConfig] = useState<ChatbotConfig | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [companies, setCompanies] = useState<{ id: string; name: string }[]>(
    []
  );

  useEffect(() => {
    loadChatbot();
  }, [chatbotId]);

  useEffect(() => {
    const loadCompanies = async () => {
      try {
        const result = await getCompaniesForDropdown();
        if ("companies" in result) {
          setCompanies(result.companies);
        } else {
          console.error("Failed to load companies:", result.error);
        }
      } catch (error) {
        console.error("Error loading companies:", error);
      }
    };

    loadCompanies();
  }, []);

  const loadChatbot = async () => {
    try {
      setLoading(true);
      const tenantId = getCookie("currentOrganizationId");

      if (!tenantId) {
        toast.error("Organization not found");
        router.push("/settings/partner/chatbots");
        return;
      }

      const response = await chatbotService.getChatbot(chatbotId, tenantId);

      if ("error" in response) {
        toast.error(response.error);
        router.push("/settings/partner/chatbots");
        return;
      }

      setConfig(response.chatbot);
    } catch (error) {
      console.error("Error loading chatbot:", error);
      toast.error(t("chatbot.failedToLoadChatbot"));
      router.push("/settings/partner/chatbots");
    } finally {
      setLoading(false);
    }
  };

  const updateConfig = (updates: Partial<ChatbotConfig>) => {
    if (!config) return;
    setConfig({ ...config, ...updates });
  };

  const updateCustomization = (
    updates: Partial<ChatbotConfig["customization"]>
  ) => {
    if (!config) return;
    updateConfig({
      customization: {
        ...config.customization,
        ...updates,
      },
    });
  };

  const updateTheme = (
    themeUpdates: Partial<NonNullable<ChatbotConfig["customization"]>["theme"]>
  ) => {
    if (!config) return;
    updateCustomization({
      theme: {
        ...config.customization?.theme,
        ...themeUpdates,
      },
    });
  };

  const handleSave = async () => {
    if (!config) return;

    let toastId: string | undefined;

    try {
      setSaving(true);
      const tenantId = getCookie("currentOrganizationId");
      toastId = toast.loading(t("chatbot.saving"));

      if (!tenantId) {
        toast.dismiss(toastId);
        toast.error("Organization not found");
        return;
      }

      console.log("Updating chatbot with config:", config);

      // Filter out read-only fields and prepare update data
      const updateData = {
        name: config.name,
        description: config.description,
        type: config.type,
        access: config.access,
        // Ensure public chatbots have empty companyIds
        companyIds: config.access === "public" ? [] : config.companyIds || [],
        isActive: config.isActive,
        allowedDomains: config.allowedDomains || [],
        customization: config.customization,
        llmScope: config.llmScope,
        searchModes: config.searchModes,
        maxTokens: config.maxTokens,
        temperature: config.temperature,
        rateLimitPerMinute: config.rateLimitPerMinute,
        rateLimitPerHour: config.rateLimitPerHour,
        rateLimitPerDay: config.rateLimitPerDay,
        tenantId,
      };

      console.log("Sending update data:", updateData);

      // Call the actual API to update the chatbot
      const response = await chatbotService.updateChatbot(
        chatbotId,
        updateData
      );

      if ("error" in response) {
        toast.dismiss(toastId);
        toast.error(response.error);
        return;
      }

      toast.dismiss(toastId);
      toast.success(t("chatbot.chatbotUpdatedSuccess"));

      // Redirect back to details page
      router.push(`/settings/partner/chatbots/${params?.id || "unknown"}`);
    } catch (error) {
      console.error("Error updating chatbot:", error);
      if (toastId) {
        toast.dismiss(toastId);
      }
      toast.error(t("chatbot.failedToUpdateChatbot"));
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-center">
          <Bot className="h-12 w-12 mx-auto mb-4 animate-pulse" />
          <p>{t("chatbot.loadingChatbot")}</p>
        </div>
      </div>
    );
  }

  if (!config) {
    return (
      <div className="text-center py-12">
        <Bot className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
        <h3 className="text-lg font-semibold mb-2">Chatbot not found</h3>
        <p className="text-muted-foreground mb-4">
          The chatbot you're looking for doesn't exist or you don't have access
          to it.
        </p>
        <Button asChild>
          <Link href="/settings/partner/chatbots">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Chatbots
          </Link>
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Link href={`/settings/partner/chatbots/${params?.id || "unknown"}`}>
            <Button variant="ghost" size="sm">
              <ArrowLeft className="mr-2 h-4 w-4" />
              {t("chatbot.backToDetails")}
            </Button>
          </Link>
          <div>
            <h1 className="text-2xl font-bold flex items-center gap-2">
              <Bot className="h-6 w-6" />
              {t("chatbot.editChatbot")}
            </h1>
            <p className="text-muted-foreground">
              {t("chatbot.modifyConfiguration")}
            </p>
          </div>
        </div>
        <Button onClick={handleSave} disabled={saving}>
          <Save className="mr-2 h-4 w-4" />
          {saving ? t("chatbot.saving") : t("chatbot.saveChanges")}
        </Button>
      </div>

      {/* Basic Configuration */}
      <Card>
        <CardHeader>
          <CardTitle>{t("chatbot.basicConfiguration")}</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="name">{t("chatbot.chatbotName")} *</Label>
            <Input
              id="name"
              placeholder={t("chatbot.enterChatbotName")}
              value={config.name}
              onChange={(e) => updateConfig({ name: e.target.value })}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">{t("chatbot.description")} *</Label>
            <Textarea
              id="description"
              placeholder={t("chatbot.describeChatbot")}
              value={config.description}
              onChange={(e) => updateConfig({ description: e.target.value })}
              rows={3}
            />
          </div>
          <div className="space-y-3">
            <Label>{t("chatbot.chatbotType")} *</Label>
            <RadioGroup
              value={config.type}
              onValueChange={(value) =>
                updateConfig({ type: value as ChatbotConfig["type"] })
              }
            >
              <div className="flex items-center space-x-2 p-3 border rounded-lg">
                <RadioGroupItem value="web-snippet" id="web-snippet" />
                <div className="flex-1">
                  <div className="flex items-center gap-2">
                    <Code className="h-4 w-4" />
                    <Label htmlFor="web-snippet" className="font-medium">
                      Web Snippet
                    </Label>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Embed chatbot on any website using a JavaScript snippet
                  </p>
                </div>
              </div>

              <div className="flex items-center space-x-2 p-3 border rounded-lg">
                <RadioGroupItem
                  value="inline-embedding"
                  id="inline-embedding"
                />
                <div className="flex-1">
                  <div className="flex items-center gap-2">
                    <Monitor className="h-4 w-4" />
                    <Label htmlFor="inline-embedding" className="font-medium">
                      Inline Embedding
                    </Label>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Embed chatbot directly into your application or webpage
                  </p>
                </div>
              </div>

              <div className="flex items-center space-x-2 p-3 border rounded-lg">
                <RadioGroupItem value="dedicated-page" id="dedicated-page" />
                <div className="flex-1">
                  <div className="flex items-center gap-2">
                    <Globe className="h-4 w-4" />
                    <Label htmlFor="dedicated-page" className="font-medium">
                      Dedicated Web Page
                    </Label>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Create a standalone webpage for your chatbot
                  </p>
                </div>
              </div>
            </RadioGroup>
          </div>
        </CardContent>
      </Card>

      {/* Access Configuration */}
      <Card>
        <CardHeader>
          <CardTitle>{t("chatbot.accessConfiguration")}</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-3">
            <Label>Access Level *</Label>
            <RadioGroup
              value={config.access}
              onValueChange={(value) => {
                const accessType = value as ChatbotConfig["access"];
                // Automatically clear companyIds when switching to public access
                if (accessType === "public") {
                  updateConfig({
                    access: accessType,
                    companyIds: [],
                  });
                } else {
                  updateConfig({ access: accessType });
                }
              }}
            >
              <div className="flex items-center space-x-2 p-3 border rounded-lg">
                <RadioGroupItem value="public" id="public" />
                <div className="flex-1">
                  <div className="flex items-center gap-2">
                    <Globe className="h-4 w-4" />
                    <Label htmlFor="public" className="font-medium">
                      Public
                    </Label>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Anyone can access and use this chatbot
                  </p>
                </div>
              </div>

              <div className="flex items-center space-x-2 p-3 border rounded-lg">
                <RadioGroupItem value="private" id="private" />
                <div className="flex-1">
                  <div className="flex items-center gap-2">
                    <Lock className="h-4 w-4" />
                    <Label htmlFor="private" className="font-medium">
                      Private
                    </Label>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Only specified companies can access this chatbot
                  </p>
                </div>
              </div>
            </RadioGroup>
          </div>

          {config.access === "private" && (
            <div className="p-4 bg-orange-50 border border-orange-200 rounded-lg">
              <div className="flex items-center gap-2">
                <Lock className="h-4 w-4 text-orange-600" />
                <p className="text-sm text-orange-800 font-medium">
                  Private Access Enabled
                </p>
              </div>
              <p className="text-sm text-orange-700 mt-1">
                This chatbot will require authentication to access. Configure
                allowed users in the security settings.
              </p>
            </div>
          )}

          {config.access === "public" && (
            <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
              <div className="flex items-center gap-2">
                <Globe className="h-4 w-4 text-green-600" />
                <p className="text-sm text-green-800 font-medium">
                  Public Access Enabled
                </p>
              </div>
              <p className="text-sm text-green-700 mt-1">
                Your chatbot will be accessible to anyone with the link or embed
                code.
              </p>
            </div>
          )}
          {config.access === "private" && (
            <div className="space-y-2">
              <Label htmlFor="companies">Companies *</Label>
              <Select
                value=""
                onValueChange={(value) => {
                  if (value && value !== "none") {
                    const currentCompanies = config.companyIds || [];
                    if (!currentCompanies.includes(value)) {
                      updateConfig({
                        companyIds: [...currentCompanies, value],
                      });
                    }
                  }
                }}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select companies to grant access" />
                </SelectTrigger>
                <SelectContent>
                  {companies
                    .filter(
                      (company) =>
                        !(config.companyIds || []).includes(company.id)
                    )
                    .map((company) => (
                      <SelectItem key={company.id} value={company.id}>
                        {company.name}
                      </SelectItem>
                    ))}
                  {companies.filter(
                    (company) => !(config.companyIds || []).includes(company.id)
                  ).length === 0 && (
                    <SelectItem value="none" disabled>
                      All companies selected
                    </SelectItem>
                  )}
                </SelectContent>
              </Select>

              {/* Selected Companies */}
              {config.companyIds && config.companyIds.length > 0 && (
                <div className="space-y-2">
                  <Label className="text-sm">Selected Companies:</Label>
                  <div className="flex flex-wrap gap-2">
                    {config.companyIds.map((companyId) => {
                      const company = companies.find((c) => c.id === companyId);
                      return company ? (
                        <Badge
                          key={companyId}
                          variant="secondary"
                          className="flex items-center gap-1"
                        >
                          {company.name}
                          <button
                            type="button"
                            onClick={() => {
                              updateConfig({
                                companyIds:
                                  config.companyIds?.filter(
                                    (id) => id !== companyId
                                  ) || [],
                              });
                            }}
                            className="ml-1 hover:bg-gray-300 rounded-full w-4 h-4 flex items-center justify-center text-xs"
                          >
                            ×
                          </button>
                        </Badge>
                      ) : null;
                    })}
                  </div>
                </div>
              )}

              <p className="text-sm text-muted-foreground">
                Select companies whose members will have access to this private
                chatbot. All users in the selected companies will automatically
                have access.
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Theme Customization */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Palette className="h-4 w-4" />
            {t("chatbot.themeAndAppearance")}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="primaryColor">{t("chatbot.primaryColor")}</Label>
              <div className="flex items-center gap-2">
                <Input
                  id="primaryColor"
                  type="color"
                  value={config.customization?.theme?.primaryColor || "#007bff"}
                  onChange={(e) =>
                    updateTheme({ primaryColor: e.target.value })
                  }
                  className="w-16 h-10"
                />
                <Input
                  value={config.customization?.theme?.primaryColor || "#007bff"}
                  onChange={(e) =>
                    updateTheme({ primaryColor: e.target.value })
                  }
                  placeholder="#007bff"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="secondaryColor">
                {t("chatbot.secondaryColor")}
              </Label>
              <div className="flex items-center gap-2">
                <Input
                  id="secondaryColor"
                  type="color"
                  value={
                    config.customization?.theme?.secondaryColor || "#6c757d"
                  }
                  onChange={(e) =>
                    updateTheme({ secondaryColor: e.target.value })
                  }
                  className="w-16 h-10"
                />
                <Input
                  value={
                    config.customization?.theme?.secondaryColor || "#6c757d"
                  }
                  onChange={(e) =>
                    updateTheme({ secondaryColor: e.target.value })
                  }
                  placeholder="#6c757d"
                />
              </div>
            </div>
          </div>

          <div className="space-y-2">
            <Label>{t("chatbot.widgetPosition")}</Label>
            <RadioGroup
              value={config.customization?.position || "bottom-right"}
              onValueChange={(value) =>
                updateCustomization({ position: value as any })
              }
            >
              <div className="grid grid-cols-2 gap-2">
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="bottom-right" id="bottom-right" />
                  <Label htmlFor="bottom-right">Bottom Right</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="bottom-left" id="bottom-left" />
                  <Label htmlFor="bottom-left">Bottom Left</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="top-right" id="top-right" />
                  <Label htmlFor="top-right">Top Right</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="top-left" id="top-left" />
                  <Label htmlFor="top-left">Top Left</Label>
                </div>
              </div>
            </RadioGroup>
          </div>

          <div className="space-y-2">
            <Label htmlFor="greeting">Greeting Message</Label>
            <Textarea
              id="greeting"
              value={
                config.customization?.greeting ||
                "Hello! How can I help you today?"
              }
              onChange={(e) =>
                updateCustomization({ greeting: e.target.value })
              }
              placeholder="Hello! How can I help you today?"
              rows={2}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="placeholder">Input Placeholder</Label>
            <Input
              id="placeholder"
              value={
                config.customization?.placeholder || "Type your message..."
              }
              onChange={(e) =>
                updateCustomization({ placeholder: e.target.value })
              }
              placeholder="Type your message..."
            />
          </div>
        </CardContent>
      </Card>

      {/* Advanced Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Sliders className="h-4 w-4" />
            {t("chatbot.advancedSettings")}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="maxTokens">Max Tokens</Label>
              <Input
                id="maxTokens"
                type="number"
                value={config.maxTokens || 4000}
                onChange={(e) =>
                  updateConfig({ maxTokens: parseInt(e.target.value) || 4000 })
                }
                min={100}
                max={8000}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="temperature">Temperature</Label>
              <Input
                id="temperature"
                type="number"
                step="0.1"
                value={config.temperature || 0.7}
                onChange={(e) =>
                  updateConfig({
                    temperature: parseFloat(e.target.value) || 0.7,
                  })
                }
                min={0}
                max={2}
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label>Search Modes</Label>
            <div className="flex flex-wrap gap-2">
              {["internal", "web", "hybrid"].map((mode) => (
                <div key={mode} className="flex items-center space-x-2">
                  <Checkbox
                    id={mode}
                    checked={config.searchModes?.includes(mode) || false}
                    onCheckedChange={(checked) => {
                      const currentModes = config.searchModes || [];
                      if (checked) {
                        updateConfig({
                          searchModes: [...currentModes, mode],
                        });
                      } else {
                        updateConfig({
                          searchModes: currentModes.filter((m) => m !== mode),
                        });
                      }
                    }}
                  />
                  <Label htmlFor={mode} className="text-sm">
                    {mode.charAt(0).toUpperCase() + mode.slice(1)} Search
                  </Label>
                </div>
              ))}
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="isActive"
              checked={config.isActive}
              onCheckedChange={(checked) =>
                updateConfig({ isActive: !!checked })
              }
            />
            <Label htmlFor="isActive">Active</Label>
          </div>
        </CardContent>
      </Card>

      {/* Action Buttons */}
      <div className="flex justify-between">
        <Link href={`/settings/partner/chatbots/${params?.id || "unknown"}`}>
          <Button variant="outline">
            <ArrowLeft className="mr-2 h-4 w-4" />
            {t("chatbot.cancel")}
          </Button>
        </Link>
        <Button onClick={handleSave} disabled={saving}>
          <Save className="mr-2 h-4 w-4" />
          {saving ? t("chatbot.saving") : t("chatbot.saveChanges")}
        </Button>
      </div>
    </div>
  );
}
