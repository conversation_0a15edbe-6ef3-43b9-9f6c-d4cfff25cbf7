import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import db from "@/lib/shared-db";
import { authOptions } from "@/lib/next-auth/src/auth-options";
import { withPermission } from "@/lib/permission-middleware";

// Get all users for a tenant
export const GET = withPermission(
  async (request: Request) => {
    try {
      const url = new URL(request.url);
      const tenantId = request.headers.get("x-tenant-id");
      const customRoleOnly = url.searchParams.get("customRoleOnly") === "true";

      if (!tenantId) {
        return NextResponse.json(
          { error: "Tenant ID is required" },
          { status: 400 }
        );
      }

      if (customRoleOnly) {
        // Get all users who are members of the tenant and have a custom role
        const users = await db.user.findMany({
          where: {
            membership: {
              some: {
                tenantId,
                role: {
                  in: ["MEMBER", "CUSTOM"],
                },
              },
            },
          },
          select: {
            id: true,
            name: true,
            email: true,
            image: true,
          },
        });

        return NextResponse.json({
          data: users,
          message: "Users retrieved successfully",
        });
      }

      // Get all users who are members of the tenant
      const users = await db.user.findMany({
        where: {
          membership: {
            some: {
              tenantId,
            },
          },
        },
        select: {
          id: true,
          name: true,
          email: true,
          image: true,
          membership: {
            where: {
              tenantId,
            },
            select: {
              role: true,
              customRoleId: true,
              customRole: true,
            },
          },
        },
      });

      return NextResponse.json({
        data: users,
        message: "Users retrieved successfully",
      });
    } catch (error) {
      console.error("Error fetching users:", error);
      return NextResponse.json(
        { error: "Failed to fetch users" },
        { status: 500 }
      );
    }
  },
  "READ",
  "MEMBER"
);
