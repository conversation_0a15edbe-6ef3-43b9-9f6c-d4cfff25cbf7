import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/next-auth";
import db from "@/lib/shared-db";

export async function GET(request: Request) {
  const session = await getServerSession(authOptions);

  if (!session) {
    return NextResponse.json(
      { error: "Unauthorized" },
      { status: 401 }
    );
  }

  try {
    // Get user role distribution
    const roleDistribution = await db.membership.groupBy({
      by: ['role'],
      _count: {
        _all: true
      }
    });

    // Calculate total users
    const totalMemberships = roleDistribution.reduce(
      (sum, item) => sum + item._count._all, 
      0
    );

    // Calculate percentages
    const roleDistributionWithPercentage = roleDistribution.map(item => ({
      role: item.role,
      count: item._count._all,
      percentage: Math.round((item._count._all / totalMemberships) * 100 * 100) / 100
    }));

    // Get role distribution by tenant
    const rolesByTenant = await db.membership.groupBy({
      by: ['tenantId', 'role'],
      _count: {
        _all: true
      }
    });

    // Get tenant names
    const tenantIds = [...new Set(rolesByTenant.map(item => item.tenantId))];
    const tenants = await db.tenant.findMany({
      where: {
        id: {
          in: tenantIds
        }
      },
      select: {
        id: true,
        name: true
      }
    });

    // Organize data by tenant
    const rolesByTenantOrganized = tenantIds.map(tenantId => {
      const tenant = tenants.find(t => t.id === tenantId);
      const tenantRoles = rolesByTenant.filter(item => item.tenantId === tenantId);
      
      return {
        tenantId,
        tenantName: tenant?.name || 'Unknown',
        roles: tenantRoles.map(item => ({
          role: item.role,
          count: item._count._all
        }))
      };
    });

    return NextResponse.json({
      totalMemberships,
      roleDistribution: roleDistributionWithPercentage,
      rolesByTenant: rolesByTenantOrganized
    });
  } catch (error) {
    console.error("Error fetching user role metrics:", error);
    return NextResponse.json(
      { error: "Failed to fetch user role metrics" },
      { status: 500 }
    );
  }
}
