import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/next-auth";
import db from "@/lib/shared-db";
import { objectsToCSV } from "@/lib/export-utils";

export async function GET(request: Request) {
  const session = await getServerSession(authOptions);

  if (!session) {
    return NextResponse.json(
      { error: "Unauthorized" },
      { status: 401 }
    );
  }

  try {
    const { searchParams } = new URL(request.url);
    const fromDate = searchParams.get("from") ? new Date(searchParams.get("from") as string) : new Date(new Date().setDate(new Date().getDate() - 30));
    const toDate = searchParams.get("to") ? new Date(searchParams.get("to") as string) : new Date();
    
    // Get all API requests in the date range
    const apiRequests = await db.aPIRequest.findMany({
      where: {
        timestamp: {
          gte: fromDate,
          lte: toDate
        }
      },
      include: {
        tenant: {
          select: {
            name: true
          }
        },
        user: {
          select: {
            name: true,
            email: true
          }
        }
      },
      orderBy: {
        timestamp: 'desc'
      }
    });
    
    // Format the data for CSV export
    const exportData = apiRequests.map(request => {
      return {
        id: request.id,
        timestamp: request.timestamp.toISOString(),
        endpoint: request.endpoint,
        method: request.method,
        statusCode: request.statusCode,
        success: request.success ? 'Yes' : 'No',
        latencyMs: request.latencyMs,
        tenantId: request.tenantId,
        tenantName: request.tenant?.name || 'Unknown',
        userId: request.userId,
        userName: request.user?.name || 'Unknown',
        userEmail: request.user?.email || 'Unknown',
        ipAddress: request.ipAddress || 'Unknown',
        userAgent: request.userAgent || 'Unknown'
      };
    });
    
    // Define CSV headers
    const headers = [
      'id',
      'timestamp',
      'endpoint',
      'method',
      'statusCode',
      'success',
      'latencyMs',
      'tenantId',
      'tenantName',
      'userId',
      'userName',
      'userEmail',
      'ipAddress',
      'userAgent'
    ];
    
    // Generate CSV content
    const csvContent = objectsToCSV(exportData, headers);
    
    // Return CSV content
    return new NextResponse(csvContent, {
      headers: {
        'Content-Type': 'text/csv',
        'Content-Disposition': `attachment; filename="system_metrics.csv"`
      }
    });
  } catch (error) {
    console.error("Error exporting system metrics:", error);
    return NextResponse.json(
      { error: "Failed to export system metrics" },
      { status: 500 }
    );
  }
}
