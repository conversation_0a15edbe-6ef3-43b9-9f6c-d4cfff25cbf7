import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import {
  Brain,
  ChevronDown,
  ChevronUp,
  Search,
  Target,
  CheckCircle,
  AlertCircle,
  Clock,
  BarChart3,
} from "lucide-react";

interface ResearchProgress {
  phase?: string;
  message?: string;
  progress?: number;
  iteration?: number;
  confidence?: number;
  gaps_identified?: string[];
  research_plan?: {
    main_topic?: string;
    subtopics?: string[];
    research_questions?: string[];
  };
  iterations?: Array<{
    iteration: number;
    phase: string;
    confidence: number;
    gaps_identified: string[];
    findings_preview: string;
  }>;
}

interface ResearchSummary {
  iterations_conducted?: number;
  final_confidence?: number;
  quality_score?: number;
  subtopics_covered?: number;
  sources_consulted?: number;
}

interface DeepResearchProgressProps {
  researchProgress?: ResearchProgress;
  researchSummary?: ResearchSummary;
  iterations?: Array<{
    iteration: number;
    phase: string;
    confidence: number;
    gaps_identified: string[];
    findings_preview: string;
  }>;
  isComplete?: boolean;
}

const phaseIcons = {
  planning: <Target className="h-4 w-4" />,
  planning_complete: <CheckCircle className="h-4 w-4" />,
  research_iteration: <Search className="h-4 w-4" />,
  iteration_complete: <CheckCircle className="h-4 w-4" />,
  synthesis: <Brain className="h-4 w-4" />,
  complete: <CheckCircle className="h-4 w-4" />,
  confidence_achieved: <Target className="h-4 w-4" />,
};

const phaseColors = {
  planning: "bg-blue-500",
  planning_complete: "bg-green-500",
  research_iteration: "bg-yellow-500",
  iteration_complete: "bg-green-500",
  synthesis: "bg-purple-500",
  complete: "bg-green-500",
  confidence_achieved: "bg-green-500",
};

export const DeepResearchProgress: React.FC<DeepResearchProgressProps> = ({
  researchProgress,
  researchSummary,
  iterations,
  isComplete = false,
}) => {
  const [isExpanded, setIsExpanded] = useState(true);
  const [showIterations, setShowIterations] = useState(false);

  if (!researchProgress && !researchSummary && !isComplete) {
    return null;
  }

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return "text-green-600";
    if (confidence >= 0.6) return "text-yellow-600";
    return "text-red-600";
  };

  const getConfidenceBadgeVariant = (confidence: number) => {
    if (confidence >= 0.8) return "default";
    if (confidence >= 0.6) return "secondary";
    return "destructive";
  };

  return (
    <Card className="mb-4 border-l-4 border-l-blue-500">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Brain className="h-5 w-5 text-blue-600" />
            <CardTitle className="text-lg">
              {isComplete ? "Deep Research Complete" : "Deep Research in Progress"}
            </CardTitle>
          </div>
          <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
            <CollapsibleTrigger asChild>
              <Button variant="ghost" size="sm">
                {isExpanded ? (
                  <ChevronUp className="h-4 w-4" />
                ) : (
                  <ChevronDown className="h-4 w-4" />
                )}
              </Button>
            </CollapsibleTrigger>
          </Collapsible>
        </div>
      </CardHeader>

      <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
        <CollapsibleContent>
          <CardContent className="space-y-4">
            {/* Current Progress */}
            {researchProgress && !isComplete && (
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  {phaseIcons[researchProgress.phase as keyof typeof phaseIcons] || (
                    <Clock className="h-4 w-4" />
                  )}
                  <span className="font-medium capitalize">
                    {researchProgress.phase?.replace("_", " ")}
                  </span>
                  {researchProgress.iteration && (
                    <Badge variant="outline">
                      Iteration {researchProgress.iteration}
                    </Badge>
                  )}
                </div>

                {researchProgress.message && (
                  <p className="text-sm text-muted-foreground">
                    {researchProgress.message}
                  </p>
                )}

                {researchProgress.progress !== undefined && (
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Progress</span>
                      <span>{Math.round(researchProgress.progress * 100)}%</span>
                    </div>
                    <Progress value={researchProgress.progress * 100} className="h-2" />
                  </div>
                )}

                {researchProgress.confidence !== undefined && (
                  <div className="flex items-center gap-2">
                    <span className="text-sm">Confidence:</span>
                    <Badge
                      variant={getConfidenceBadgeVariant(researchProgress.confidence)}
                    >
                      {Math.round(researchProgress.confidence * 100)}%
                    </Badge>
                  </div>
                )}

                {researchProgress.gaps_identified && researchProgress.gaps_identified.length > 0 && (
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <AlertCircle className="h-4 w-4 text-yellow-600" />
                      <span className="text-sm font-medium">Knowledge Gaps Identified:</span>
                    </div>
                    <ul className="text-sm text-muted-foreground space-y-1 ml-6">
                      {researchProgress.gaps_identified.map((gap, index) => (
                        <li key={index} className="list-disc">
                          {gap}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            )}

            {/* Research Plan */}
            {researchProgress?.research_plan && (
              <div className="space-y-3 border-t pt-3">
                <h4 className="font-medium flex items-center gap-2">
                  <Target className="h-4 w-4" />
                  Research Plan
                </h4>
                <div className="space-y-2 text-sm">
                  <div>
                    <span className="font-medium">Topic:</span>{" "}
                    {researchProgress.research_plan.main_topic}
                  </div>
                  {researchProgress.research_plan.subtopics && (
                    <div>
                      <span className="font-medium">Subtopics:</span>{" "}
                      {researchProgress.research_plan.subtopics.length} identified
                    </div>
                  )}
                  {researchProgress.research_plan.research_questions && (
                    <div>
                      <span className="font-medium">Research Questions:</span>{" "}
                      {researchProgress.research_plan.research_questions.length} planned
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Research Summary */}
            {researchSummary && (
              <div className="space-y-3 border-t pt-3">
                <h4 className="font-medium flex items-center gap-2">
                  <BarChart3 className="h-4 w-4" />
                  Research Summary
                </h4>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span>Iterations:</span>
                      <Badge variant="outline">{researchSummary.iterations_conducted || 0}</Badge>
                    </div>
                    <div className="flex justify-between">
                      <span>Sources:</span>
                      <Badge variant="outline">{researchSummary.sources_consulted || 0}</Badge>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span>Confidence:</span>
                      <Badge
                        variant={getConfidenceBadgeVariant(researchSummary.final_confidence || 0)}
                      >
                        {Math.round((researchSummary.final_confidence || 0) * 100)}%
                      </Badge>
                    </div>
                    <div className="flex justify-between">
                      <span>Quality:</span>
                      <Badge
                        variant={getConfidenceBadgeVariant(researchSummary.quality_score || 0)}
                      >
                        {Math.round((researchSummary.quality_score || 0) * 100)}%
                      </Badge>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Iterations Detail */}
            {iterations && iterations.length > 0 && (
              <div className="space-y-3 border-t pt-3">
                <div className="flex items-center justify-between">
                  <h4 className="font-medium flex items-center gap-2">
                    <Search className="h-4 w-4" />
                    Research Iterations ({iterations.length})
                  </h4>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowIterations(!showIterations)}
                  >
                    {showIterations ? "Hide" : "Show"} Details
                    {showIterations ? (
                      <ChevronUp className="h-4 w-4 ml-1" />
                    ) : (
                      <ChevronDown className="h-4 w-4 ml-1" />
                    )}
                  </Button>
                </div>

                {showIterations && (
                  <div className="space-y-3">
                    {iterations.map((iteration, index) => (
                      <div key={index} className="border rounded-lg p-3 space-y-2">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <Badge variant="outline">Iteration {iteration.iteration}</Badge>
                            <span className="text-sm capitalize">{iteration.phase}</span>
                          </div>
                          <Badge
                            variant={getConfidenceBadgeVariant(iteration.confidence)}
                          >
                            {Math.round(iteration.confidence * 100)}%
                          </Badge>
                        </div>
                        {iteration.findings_preview && (
                          <p className="text-sm text-muted-foreground">
                            {iteration.findings_preview}
                          </p>
                        )}
                        {iteration.gaps_identified.length > 0 && (
                          <div className="text-sm">
                            <span className="font-medium">Gaps:</span>{" "}
                            {iteration.gaps_identified.join(", ")}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </CollapsibleContent>
      </Collapsible>
    </Card>
  );
};
