import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/next-auth/src/auth-options";
import db from "@/lib/shared-db";
import { withPermission } from "@/lib/permission-middleware";
import jwt from "jsonwebtoken";

/**
 * Call Python backend for URL operations
 */
async function callPythonBackend(endpoint: string, data: any, session: any) {
  const pythonApiUrl =
    process.env.NEXT_PUBLIC_API_URL || "http://localhost:8000";
  const payload = {
    userId: session.userId,
    email: session.user?.email,
    name: session.user?.name,
    // Include the current organization if available
    organizationId: session.currentOrganization?.id,
    // Add any other information you need
    iat: Math.floor(Date.now() / 1000),
  };

  // Generate a token directly
  const secret = process.env.NEXTAUTH_SECRET;
  if (!secret) {
    throw new Error("NEXTAUTH_SECRET is not defined");
  }

  // Create a token that expires in 1 hour
  const token = jwt.sign(payload, secret, { expiresIn: "1h" });

  try {
    const response = await fetch(
      `${pythonApiUrl}/api/v1/url-import/${endpoint}`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(data),
      }
    );

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(
        errorData.detail || `HTTP ${response.status}: ${response.statusText}`
      );
    }

    return await response.json();
  } catch (error) {
    console.error(`Error calling Python backend (${endpoint}):`, error);
    throw error;
  }
}

/**
 * Checks if a URL has already been imported
 */
async function checkDuplicateUrl(url: string, workspaceId: string) {
  // For MongoDB, we need to use a different approach to query JSON fields
  const existingFile = await db.file.findFirst({
    where: {
      workspaceId,
      // Use a raw filter for MongoDB to query inside the JSON
      metadata: {
        // This syntax works with MongoDB to check if the sourceUrl field exists and equals the URL
        sourceUrl: url,
      },
    },
  });

  return existingFile !== null;
}

/**
 * Handler for URL import requests
 */
async function handleUrlImport(request: Request) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get request data
    const { url, action, workspaceId, options } = await request.json();

    if (!url) {
      return NextResponse.json({ error: "URL is required" }, { status: 400 });
    }

    // Handle different actions
    if (action === "fetch") {
      try {
        const result = await callPythonBackend(
          "fetch",
          {
            url,
            action: "fetch",
            options: options || {},
          },
          session
        );

        // Check if the Python backend returned an error (success: false)
        if (result.success === false) {
          return NextResponse.json(
            { error: result.error || "Failed to fetch URL content" },
            { status: 400 }
          );
        }

        return NextResponse.json(result);
      } catch (error) {
        return NextResponse.json(
          { error: error.message || "Failed to fetch URL content" },
          { status: 500 }
        );
      }
    } else if (action === "sitemap") {
      try {
        const result = await callPythonBackend(
          "sitemap",
          {
            url,
            action: "sitemap",
          },
          session
        );

        // Check if the Python backend returned an error (success: false)
        if (result.success === false) {
          return NextResponse.json(
            { error: result.error || "Failed to fetch sitemap" },
            { status: 400 }
          );
        }

        return NextResponse.json(result);
      } catch (error) {
        return NextResponse.json(
          { error: error.message || "Failed to fetch sitemap" },
          { status: 500 }
        );
      }
    } else if (action === "check-duplicate" && workspaceId) {
      try {
        const result = await callPythonBackend(
          "check-duplicate",
          {
            url,
            action: "check-duplicate",
            workspace_id: workspaceId,
          },
          session
        );
        return NextResponse.json(result);
      } catch (error) {
        // Fallback to local check if Python backend fails
        const isDuplicate = await checkDuplicateUrl(url, workspaceId);
        return NextResponse.json({ is_duplicate: isDuplicate });
      }
    } else if (action === "batch-import" && workspaceId) {
      // This is still handled in the frontend for now
      return NextResponse.json(
        {
          error: "Batch import is handled in the frontend",
        },
        { status: 501 }
      );
    }

    return NextResponse.json({ error: "Invalid action" }, { status: 400 });
  } catch (error) {
    console.error("Error handling URL import:", error);
    return NextResponse.json(
      { error: `Failed to process request: ${error.message}` },
      { status: 500 }
    );
  }
}

// Export the handler with permission check
export const POST = withPermission(handleUrlImport, "CREATE", "FILE");
