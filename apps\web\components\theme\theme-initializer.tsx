"use client";

import { useEffect } from "react";
import { useTheme } from "next-themes";
import { ThemeConfig } from "@/types/theme-config";
import { generateThemeVariables } from "@/services/theme-config";

interface ThemeInitializerProps {
  tenantThemePreset: string | null;
  themeConfig?: ThemeConfig | null;
}

// Function to apply comprehensive text color overrides
function applyTextColorOverrides() {
  const styleId = "theme-text-color-overrides";

  // Remove existing style element if it exists
  const existingStyle = document.getElementById(styleId);
  if (existingStyle) {
    existingStyle.remove();
  }

  // Create comprehensive CSS overrides for text elements
  const css = `
    /* Comprehensive text color overrides using CSS custom properties */

    /* Primary text elements */
    .text-foreground,
    .text-card-foreground,
    .text-popover-foreground,
    h1, h2, h3, h4, h5, h6,
    .font-semibold,
    .font-bold,
    .font-medium {
      color: hsl(var(--foreground)) !important;
    }

    /* Muted and secondary text */
    .text-muted-foreground,
    .text-secondary-foreground,
    .text-sm.text-muted-foreground,
    .text-xs.text-muted-foreground,
    p.text-muted-foreground,
    .text-gray-500,
    .text-gray-600,
    .text-slate-500,
    .text-slate-600 {
      color: hsl(var(--muted-foreground)) !important;
    }

    /* Labels and form elements */
    label,
    .text-sm.font-medium,
    .text-sm.font-semibold,
    .peer-disabled\\:cursor-not-allowed {
      color: hsl(var(--foreground)) !important;
    }

    /* Card and content text */
    .text-card-foreground,
    .card-title,
    .card-description {
      color: hsl(var(--card-foreground)) !important;
    }

    /* Settings and admin interface text */
    .space-y-2 p,
    .space-y-4 p,
    .text-xs,
    .text-sm:not(.bg-primary):not(.bg-secondary):not(.bg-destructive),
    .description,
    .helper-text {
      color: hsl(var(--muted-foreground)) !important;
    }

    /* Table and list text */
    .table-cell,
    .table-head,
    .list-item,
    .dropdown-item {
      color: hsl(var(--foreground)) !important;
    }

    /* Navigation and menu text */
    .nav-item,
    .menu-item,
    .sidebar-item {
      color: hsl(var(--sidebar-foreground)) !important;
    }
  `;

  // Create and append style element
  const styleElement = document.createElement("style");
  styleElement.id = styleId;
  styleElement.textContent = css;
  document.head.appendChild(styleElement);
}

export function ThemeInitializer({ tenantThemePreset, themeConfig }: ThemeInitializerProps) {
  const { setTheme } = useTheme();
  const isPartnerConsole = process.env.NEXT_PUBLIC_IS_PARTNER_CONSOLE === "1";

  useEffect(() => {
    // Only apply tenant theme preset if partner console is enabled
    if (!isPartnerConsole) {
      return;
    }
    // Only run on client side
    if (typeof window === "undefined") return;

    if (tenantThemePreset) {
      console.log("ThemeInitializer: Applying tenant theme preset:", tenantThemePreset);

      // Always update localStorage and apply theme to ensure consistency
      localStorage.setItem("theme", tenantThemePreset);

      // Apply the theme using next-themes
      setTheme(tenantThemePreset);

      // Force apply the theme class to the HTML element
      const root = document.documentElement;
      root.classList.remove("light", "dark", "system");
      root.classList.add(tenantThemePreset);

      console.log("ThemeInitializer: Theme applied successfully");
    }
  }, [tenantThemePreset, setTheme, isPartnerConsole]);

  // Apply custom colors from theme configuration
  useEffect(() => {
    // Only apply custom colors if partner console is enabled
    if (!isPartnerConsole) {
      return;
    }
    // Only run on client side
    if (typeof window === "undefined") return;

    if (themeConfig) {
      console.log("ThemeInitializer: Applying custom colors:", themeConfig);

      // Generate and apply CSS variables for custom colors
      const themeVariables = generateThemeVariables(themeConfig);
      const root = document.documentElement;

      // Apply color variables to the root element
      Object.entries(themeVariables).forEach(([property, value]) => {
        root.style.setProperty(property, value);
      });

      // Apply comprehensive text color overrides
      applyTextColorOverrides();

      console.log("ThemeInitializer: Custom colors applied successfully");
    }
  }, [themeConfig, isPartnerConsole]);

  // Listen for theme config updates from the settings page (only if partner console is enabled)
  useEffect(() => {
    if (!isPartnerConsole) {
      return;
    }

    const handleThemeConfigUpdate = (event: CustomEvent) => {
      const { themePreset, themeConfig } = event.detail;

      // Handle theme preset changes
      if (themePreset) {
        localStorage.setItem("theme", themePreset);
        setTheme(themePreset);

        // Force apply the theme class
        const root = document.documentElement;
        root.classList.remove("light", "dark");
        root.classList.add(themePreset);
      }

      // Handle brand name updates by dispatching a realtimeThemeUpdate event
      // This ensures the ThemeConfigProvider context is updated
      if (themeConfig) {
        const realtimeEvent = new CustomEvent("realtimeThemeUpdate", {
          detail: { themeConfig },
        });
        window.dispatchEvent(realtimeEvent);
      }
    };

    // Listen for real-time theme updates from WebSocket (only if partner console is enabled)
    const handleRealtimeThemeUpdate = (event: CustomEvent) => {
      if (!isPartnerConsole) return;

      const themeData = event.detail;
      console.log("Real-time theme update received in initializer:", themeData);

      if (themeData?.themeConfig?.themePreset) {
        const themePreset = themeData.themeConfig.themePreset;
        localStorage.setItem("theme", themePreset);
        setTheme(themePreset);

        // Force apply the theme class
        const root = document.documentElement;
        root.classList.remove("light", "dark");
        root.classList.add(themePreset);
      }
    };

    window.addEventListener("themeConfigUpdated", handleThemeConfigUpdate as EventListener);
    window.addEventListener("realtimeThemeUpdate", handleRealtimeThemeUpdate as EventListener);

    return () => {
      window.removeEventListener("themeConfigUpdated", handleThemeConfigUpdate as EventListener);
      window.removeEventListener("realtimeThemeUpdate", handleRealtimeThemeUpdate as EventListener);
    };
  }, [setTheme, isPartnerConsole]);

  return null;
}
