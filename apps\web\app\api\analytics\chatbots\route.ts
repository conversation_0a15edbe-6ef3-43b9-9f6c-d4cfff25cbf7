import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/next-auth";
import db from "@/lib/shared-db";

export async function GET(request: NextRequest) {
  try {
    const session: any = await getServerSession(authOptions);
    if (!session?.userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const timeRange = searchParams.get("timeRange") || "7d";
    const status = searchParams.get("status");
    const tenantId = searchParams.get("tenantId");

    // Calculate date range
    const now = new Date();
    const timeRangeMap: { [key: string]: number } = {
      "24h": 24 * 60 * 60 * 1000,
      "7d": 7 * 24 * 60 * 60 * 1000,
      "30d": 30 * 24 * 60 * 60 * 1000,
      "90d": 90 * 24 * 60 * 60 * 1000,
    };
    const startDate = new Date(
      now.getTime() - (timeRangeMap[timeRange] || timeRangeMap["7d"])
    );

    // Build chatbot filter
    const chatbotFilter: any = {
      tenantId: tenantId,
      userId: session.userId,
    };
    if (status) {
      chatbotFilter.access = status;
    }

    // Get all chatbots for the organization
    const chatbots = await db.chatbot.findMany({
      where: chatbotFilter,
      orderBy: {
        createdAt: "desc",
      },
    });

    // Get analytics data for each chatbot
    const chatbotAnalytics = await Promise.all(
      chatbots.map(async (chatbot) => {
        // Get session stats (equivalent to conversation stats)
        const totalSessions = await db.chatbotSession.count({
          where: {
            chatbotId: chatbot.id,
            startedAt: {
              gte: startDate,
            },
          },
        });

        const totalMessages = await db.chatbotMessage.count({
          where: {
            chatbotId: chatbot.id,
            createdAt: {
              gte: startDate,
            },
          },
        });

        // Get unique users (estimated by unique IP addresses)
        const sessions = await db.chatbotSession.findMany({
          where: {
            chatbotId: chatbot.id,
            startedAt: {
              gte: startDate,
            },
          },
          select: {
            ipAddress: true,
            userId: true,
            averageResponseTime: true,
            satisfactionRating: true,
          },
        });

        const uniqueIPs = new Set(
          sessions.map((s) => s.ipAddress).filter(Boolean)
        );
        const uniqueUsers =
          sessions.filter((s) => s.userId).length > 0
            ? new Set(sessions.map((s) => s.userId).filter(Boolean)).size
            : uniqueIPs.size;

        const avgResponseTime =
          sessions.length > 0
            ? sessions.reduce(
                (sum, s) => sum + (s.averageResponseTime || 0),
                0
              ) / sessions.length
            : 0;

        const satisfactionRatings = sessions
          .filter((s) => s.satisfactionRating)
          .map((s) => s.satisfactionRating!);
        const avgSatisfaction =
          satisfactionRatings.length > 0
            ? satisfactionRatings.reduce((sum, rating) => sum + rating, 0) /
              satisfactionRatings.length
            : 0;

        const stats = {
          totalConversations: totalSessions,
          uniqueUsers: uniqueUsers,
          totalMessages: totalMessages,
          avgResponseTime: avgResponseTime,
          avgSatisfaction: avgSatisfaction,
        };

        // Get daily stats for trends using ChatbotAnalytics
        const analyticsData = await db.chatbotAnalytics.findMany({
          where: {
            chatbotId: chatbot.id,
            date: {
              gte: startDate,
            },
          },
          orderBy: {
            date: "asc",
          },
        });

        // If no analytics data, generate from sessions (fallback)
        let dailyStats: any[] = [];
        if (analyticsData.length > 0) {
          dailyStats = analyticsData.map((analytics) => ({
            date: analytics.date.toISOString().split("T")[0],
            conversations: analytics.totalSessions,
            messages: analytics.totalMessages,
            users: analytics.uniqueUsers,
            responseTime: analytics.averageResponseTime,
          }));
        } else {
          // Fallback: group sessions by date
          const sessionsByDate = await db.chatbotSession.groupBy({
            by: ["startedAt"],
            where: {
              chatbotId: chatbot.id,
              startedAt: {
                gte: startDate,
              },
            },
            _count: {
              id: true,
            },
            _avg: {
              averageResponseTime: true,
            },
          });

          // Process into daily stats (simplified)
          const dateMap = new Map<string, any>();
          for (const session of sessionsByDate) {
            const dateStr = session.startedAt.toISOString().split("T")[0];
            if (!dateMap.has(dateStr)) {
              dateMap.set(dateStr, {
                date: dateStr,
                conversations: 0,
                messages: 0,
                users: 0,
                responseTime: 0,
              });
            }
            const dayData = dateMap.get(dateStr);
            dayData.conversations += session._count.id;
            dayData.responseTime = session._avg.averageResponseTime || 0;
          }
          dailyStats = Array.from(dateMap.values()).sort((a, b) =>
            a.date.localeCompare(b.date)
          );
        }

        // Get error stats from session error counts
        const sessionsWithErrors = await db.chatbotSession.findMany({
          where: {
            chatbotId: chatbot.id,
            startedAt: {
              gte: startDate,
            },
            errorCount: {
              gt: 0,
            },
          },
          select: {
            errorCount: true,
          },
        });

        const totalErrors = sessionsWithErrors.reduce(
          (sum, session) => sum + session.errorCount,
          0
        );
        const errorRate =
          stats.totalConversations > 0
            ? (totalErrors / stats.totalConversations) * 100
            : 0;

        // Calculate uptime (simplified - you might want to implement proper uptime tracking)
        const uptime = Math.max(95, 100 - errorRate);

        // Get last activity from sessions
        const lastActivity = await db.chatbotSession.findFirst({
          where: {
            chatbotId: chatbot.id,
          },
          orderBy: {
            lastActivityAt: "desc",
          },
          select: {
            lastActivityAt: true,
          },
        });

        const getLastActivityText = (lastActivityDate: Date | null) => {
          if (!lastActivityDate) return "No activity";
          const diff = now.getTime() - lastActivityDate.getTime();
          const minutes = Math.floor(diff / (1000 * 60));
          const hours = Math.floor(diff / (1000 * 60 * 60));
          const days = Math.floor(diff / (1000 * 60 * 60 * 24));

          if (minutes < 60) return `${minutes} minutes ago`;
          if (hours < 24) return `${hours} hours ago`;
          return `${days} days ago`;
        };

        return {
          id: chatbot.id.toString(),
          name: chatbot.name,
          status: chatbot.access || "public",
          isActive: chatbot.isActive !== false,
          totalConversations: stats.totalConversations,
          totalMessages: stats.totalMessages,
          uniqueUsers: stats.uniqueUsers,
          averageResponseTime: Number((stats.avgResponseTime || 0).toFixed(1)),
          satisfactionScore: Number((stats.avgSatisfaction || 0).toFixed(1)),
          lastActivity: getLastActivityText(lastActivity?.lastActivityAt),
          errorRate: Number(errorRate.toFixed(1)),
          uptime: Number(uptime.toFixed(1)),
          dailyStats: dailyStats.map((stat) => ({
            date: stat.date,
            conversations: stat.conversations,
            messages: stat.messages,
            users: stat.users,
            responseTime: Number((stat.responseTime || 0).toFixed(1)),
          })),
        };
      })
    );

    // Calculate overview stats
    const overview = {
      totalChatbots: chatbots.length,
      publicChatbots: chatbots.filter((c) => c.access === "public" || !c.access)
        .length,
      privateChatbots: chatbots.filter((c) => c.access === "private").length,
      activeChatbots: chatbots.filter((c) => c.isActive !== false).length,
      totalConversations: chatbotAnalytics.reduce(
        (sum, c) => sum + c.totalConversations,
        0
      ),
      totalMessages: chatbotAnalytics.reduce(
        (sum, c) => sum + c.totalMessages,
        0
      ),
      averageResponseTime: Number(
        (
          chatbotAnalytics.reduce((sum, c) => sum + c.averageResponseTime, 0) /
          Math.max(chatbotAnalytics.length, 1)
        ).toFixed(1)
      ),
      overallSatisfaction: Number(
        (
          chatbotAnalytics.reduce((sum, c) => sum + c.satisfactionScore, 0) /
          Math.max(chatbotAnalytics.length, 1)
        ).toFixed(1)
      ),
    };

    return NextResponse.json({
      overview,
      chatbots: chatbotAnalytics,
    });
  } catch (error) {
    console.error("Error fetching chatbot analytics:", error);
    return NextResponse.json(
      { error: "Failed to fetch analytics data" },
      { status: 500 }
    );
  }
}
