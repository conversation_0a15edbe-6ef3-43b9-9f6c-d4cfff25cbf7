"use client";

import { useEffect } from "react";
import { themeCache } from "@/lib/theme-cache";
import { generateThemeVariables } from "@/services/theme-config";
import { ThemeConfig } from "@/types/theme-config";

interface ThemePreloaderProps {
  tenantId?: string;
  initialThemeConfig?: ThemeConfig;
}

/**
 * ThemePreloader applies cached theme colors immediately on page load
 * to prevent theme flashing during navigation
 */
export function ThemePreloader({
  tenantId,
  initialThemeConfig,
}: ThemePreloaderProps) {
  useEffect(() => {
    const isPartnerConsole = process.env.NEXT_PUBLIC_IS_PARTNER_CONSOLE === "1";

    if (!isPartnerConsole || typeof window === "undefined") {
      return;
    }

    // Function to apply theme colors immediately
    const applyThemeColors = (
      themeConfig: ThemeConfig,
      themeMode: "light" | "dark"
    ) => {
      try {
        const themeVariables = generateThemeVariables(themeConfig, themeMode);
        const root = document.documentElement;

        // Apply CSS variables immediately
        Object.entries(themeVariables).forEach(([property, value]) => {
          if (value) {
            root.style.setProperty(property, value);
          }
        });

        console.log(
          `ThemePreloader: Applied ${themeMode} theme colors immediately`
        );
      } catch (error) {
        console.error("ThemePreloader: Error applying theme colors:", error);
      }
    };

    // Get current theme mode from localStorage or HTML class
    const getCurrentThemeMode = (): "light" | "dark" => {
      const storedTheme = localStorage.getItem("theme");
      if (storedTheme === "light" || storedTheme === "dark") {
        return storedTheme;
      }

      // Check HTML class
      const root = document.documentElement;
      if (root.classList.contains("dark")) {
        return "dark";
      }
      if (root.classList.contains("light")) {
        return "light";
      }

      // Default to light if system preference detection fails
      return "light";
    };

    // Try to get theme config from multiple sources
    let themeConfig: ThemeConfig | null = null;

    // 1. Use initial config if provided
    if (initialThemeConfig) {
      themeConfig = initialThemeConfig;
      console.log("ThemePreloader: Using initial theme config");
    }
    // 2. Try to get from cache
    else if (tenantId) {
      themeConfig = themeCache.get(tenantId);
      if (themeConfig) {
        console.log("ThemePreloader: Using cached theme config");
      }
    }

    // Apply theme colors if we have config
    if (themeConfig) {
      const currentTheme = getCurrentThemeMode();
      applyThemeColors(themeConfig, currentTheme);

      // Also apply theme mode if specified in config
      if (themeConfig.themePreset) {
        const root = document.documentElement;
        root.classList.remove("light", "dark");
        root.classList.add(themeConfig.themePreset);
        localStorage.setItem("theme", themeConfig.themePreset);
      }
    }
  }, [tenantId, initialThemeConfig]);

  // This component doesn't render anything
  return null;
}

/**
 * Script to be injected in the document head for immediate theme application
 * This prevents any flash of unstyled content
 */
export function generateThemePreloadScript(
  tenantId?: string,
  themeConfig?: ThemeConfig
): string {
  return `
    (function() {
      try {
        const isPartnerConsole = ${
          process.env.NEXT_PUBLIC_IS_PARTNER_CONSOLE === "1"
        };
        if (!isPartnerConsole) return;

        const tenantId = "${tenantId || ""}";
        const initialConfig = ${JSON.stringify(themeConfig || null)};
        
        // Get current theme mode
        const getCurrentThemeMode = () => {
          const storedTheme = localStorage.getItem("theme");
          if (storedTheme === "light" || storedTheme === "dark") {
            return storedTheme;
          }
          return document.documentElement.classList.contains("dark") ? "dark" : "light";
        };

        // Simple theme variable generation (subset of full function)
        const applyBasicThemeColors = (config, themeMode) => {
          if (!config) return;

          const root = document.documentElement;
          const getColorValue = (lightColor, darkColor) => {
            if (themeMode === "dark" && darkColor) return darkColor;
            if (themeMode === "light" && lightColor) return lightColor;
            return lightColor || darkColor;
          };

          // Apply basic colors to prevent flashing
          const primaryColor = getColorValue(
            config.lightPrimaryColor,
            config.darkPrimaryColor
          );

          const navigationBg = getColorValue(
            config.lightNavigationBackgroundColor,
            config.darkNavigationBackgroundColor
          );

          const contentBg = getColorValue(
            config.lightContentBackgroundColor,
            config.darkContentBackgroundColor
          );

          if (primaryColor) {
            const hsl = hexToHsl(primaryColor);
            if (hsl) {
              root.style.setProperty("--primary", hsl);
            }
          }

          if (navigationBg) {
            const hsl = hexToHsl(navigationBg);
            if (hsl) {
              root.style.setProperty("--navigation-background", hsl);
              root.style.setProperty("--sidebar-background", hsl + " / 0.8");
            }
          }

          if (contentBg) {
            const hsl = hexToHsl(contentBg);
            if (hsl) {
              root.style.setProperty("--card", hsl);
            }
          }
        };

        // Simple hex to HSL conversion
        const hexToHsl = (hex) => {
          if (!hex || !hex.startsWith("#")) return null;
          
          const r = parseInt(hex.slice(1, 3), 16) / 255;
          const g = parseInt(hex.slice(3, 5), 16) / 255;
          const b = parseInt(hex.slice(5, 7), 16) / 255;

          const max = Math.max(r, g, b);
          const min = Math.min(r, g, b);
          let h, s, l = (max + min) / 2;

          if (max === min) {
            h = s = 0;
          } else {
            const d = max - min;
            s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
            switch (max) {
              case r: h = (g - b) / d + (g < b ? 6 : 0); break;
              case g: h = (b - r) / d + 2; break;
              case b: h = (r - g) / d + 4; break;
            }
            h /= 6;
          }

          return Math.round(h * 360) + " " + Math.round(s * 100) + "% " + Math.round(l * 100) + "%";
        };

        // Try to get theme config
        let config = initialConfig;
        
        // If no initial config, try cache
        if (!config && tenantId) {
          try {
            const cached = localStorage.getItem("theme-cache");
            if (cached) {
              const cacheData = JSON.parse(cached);
              const tenantCache = cacheData[tenantId];
              if (tenantCache && tenantCache.config) {
                config = tenantCache.config;
              }
            }
          } catch (e) {
            // Ignore cache errors
          }
        }

        // Apply theme colors immediately
        if (config) {
          const currentTheme = getCurrentThemeMode();
          applyBasicThemeColors(config, currentTheme);
          
          // Apply theme mode
          if (config.themePreset) {
            document.documentElement.classList.remove("light", "dark");
            document.documentElement.classList.add(config.themePreset);
            localStorage.setItem("theme", config.themePreset);
          }
        }

      } catch (error) {
        console.error("Theme preload script error:", error);
      }
    })();
  `;
}
