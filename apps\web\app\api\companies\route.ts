import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/next-auth";
import db from "@/lib/shared-db";
import crypto from "crypto";

// GET /api/companies - Get all companies for the current user's organization
export async function GET(request: Request) {
  try {
    const session: any = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get user and their current organization
    const emailHash = crypto
      .createHash("sha256")
      .update(session.user.email)
      .digest("hex");

    const user = await db.user.findUnique({
      where: { emailHash },
      include: {
        membership: {
          include: {
            tenant: true,
          },
        },
      },
    });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Get current organization from session or first membership
    const currentOrgId =
      session.currentOrganization?.id || user.membership[0]?.tenantId;

    if (!currentOrgId) {
      return NextResponse.json(
        { error: "No organization found" },
        { status: 404 }
      );
    }

    // Check if user has admin access to the organization
    const membership = user.membership.find((m) => m.tenantId === currentOrgId);
    if (!membership || !["OWNER", "ADMIN"].includes(membership.role)) {
      return NextResponse.json(
        { error: "Insufficient permissions" },
        { status: 403 }
      );
    }

    // Get companies managed by this tenant/organization
    const companies = await db.company.findMany({
      where: {
        tenantId: currentOrgId,
      },
      select: {
        id: true,
        name: true,
        slug: true,
        description: true,
        maxSignups: true,
        inviteLink: true,
        status: true,
        createdAt: true,
        updatedAt: true,
        members: {
          select: {
            id: true,
            role: true,
            status: true,
            user: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    // Transform the data to match the UI expectations
    const formattedCompanies = companies.map((company) => ({
      id: company.id,
      name: company.name,
      slug: company.slug,
      description: company.description || "",
      maxSignups: company.maxSignups,
      currentSignups: company.members.length,
      inviteLink:
        company.inviteLink ||
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/invite/${company.slug}`,
      createdAt: company.createdAt.toISOString().split("T")[0],
      members: company.members.length,
    }));

    return NextResponse.json({
      companies: formattedCompanies,
      total: formattedCompanies.length,
    });
  } catch (error) {
    console.error("Error fetching companies:", error);
    return NextResponse.json(
      { error: "Failed to fetch companies" },
      { status: 500 }
    );
  }
}

// POST /api/companies - Create a new company
export async function POST(request: Request) {
  try {
    const session: any = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { name, slug, description, maxSignups = 50 } = await request.json();

    // Validate required fields
    if (!name || !slug) {
      return NextResponse.json(
        { error: "Name and slug are required" },
        { status: 400 }
      );
    }

    // Get user
    const emailHash = crypto
      .createHash("sha256")
      .update(session.user.email)
      .digest("hex");

    const user = await db.user.findUnique({
      where: { emailHash },
      include: {
        membership: true,
      },
    });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Check if user has admin access
    const currentOrgId =
      session.currentOrganization?.id || user.membership[0]?.tenantId;
    const membership = user.membership.find((m) => m.tenantId === currentOrgId);

    if (!membership || !["OWNER", "ADMIN"].includes(membership.role)) {
      return NextResponse.json(
        { error: "Insufficient permissions" },
        { status: 403 }
      );
    }

    // Check if slug already exists for this tenant
    const existingCompany = await db.company.findFirst({
      where: {
        slug: { startsWith: slug },
        tenantId: currentOrgId,
      },
    });

    const finalSlug = existingCompany ? `${slug}-${Date.now()}` : slug;

    // Create company in a transaction
    const result = await db.$transaction(async (tx) => {
      const newCompany = await tx.company.create({
        data: {
          name,
          slug: finalSlug,
          description,
          maxSignups,
          tenantId: currentOrgId,
          inviteLink: `${process.env.NEXT_PUBLIC_API_BASE_URL}/invite/${finalSlug}`,
        },
      });

      // Create initial membership for the creator
      await tx.companyMember.create({
        data: {
          userId: user.id,
          companyId: newCompany.id,
          role: "OWNER",
        },
      });

      return newCompany;
    });

    // Format response to match UI expectations
    const formattedCompany = {
      id: result.id,
      name: result.name,
      slug: result.slug,
      description: result.description || "",
      maxSignups: result.maxSignups,
      currentSignups: 1, // Creator is the first member
      inviteLink:
        result.inviteLink ||
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/invite/${result.slug}`,
      createdAt: result.createdAt.toISOString().split("T")[0],
    };

    return NextResponse.json(
      {
        message: "Company created successfully",
        company: formattedCompany,
      },
      { status: 201 }
    );
  } catch (error) {
    console.error("Error creating company:", error);
    return NextResponse.json(
      { error: "Failed to create company" },
      { status: 500 }
    );
  }
}
