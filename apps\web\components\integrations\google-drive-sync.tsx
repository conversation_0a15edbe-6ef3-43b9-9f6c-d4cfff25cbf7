"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  googleDriveService,
  SyncOptions,
} from "@/services/src/integration/google";
import { Loader2, RefreshCcw } from "lucide-react";
import { useLanguage } from "@/lib/language-context";
import { getCookie } from "@/utils/cookies";
import toast from "react-hot-toast";

interface GoogleDriveSyncProps {
  tenantId: string;
  workspaceSlug: string;
  pageId?: string;
  folderId?: string;
  onSyncComplete?: () => void;
}

export function GoogleDriveSync({
  tenantId,
  workspaceSlug,
  pageId,
  folderId,
  onSyncComplete,
}: GoogleDriveSyncProps) {
  const [isSyncing, setIsSyncing] = useState(false);
  const [syncedFiles, setSyncedFiles] = useState(0);
  const { t } = useLanguage();
  const userId = getCookie("userId") ?? "";

  const handleSync = async () => {
    if (!folderId) {
      // If no folder ID is provided, we need to list folders and let the user select one
      toast.error(t("integrations.selectFolderFirst"));
      return;
    }

    setIsSyncing(true);
    setSyncedFiles(0);

    try {
      const options: SyncOptions = {
        folderId,
        tenantId,
        workspaceSlug,
        pageId,
        recursive: true,
        checkSubFiles: true,
        userId,
      };

      const result = await googleDriveService.checkAndSyncFolder(options);

      if (result.synced) {
        setSyncedFiles(result.syncedFiles || 0);
        if (onSyncComplete) {
          onSyncComplete();
        }
      }
    } catch (error) {
      console.error("Sync error:", error);
    } finally {
      setIsSyncing(false);
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <RefreshCcw className="h-5 w-5" />
          {t("integrations.googleDriveSync")}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="flex flex-col gap-4">
          <p className="text-sm text-muted-foreground">
            {t("integrations.syncDescription")}
          </p>

          <div className="flex items-center justify-between">
            <Button
              onClick={handleSync}
              disabled={isSyncing || !folderId}
              className="gap-2"
            >
              {isSyncing ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin" />
                  {t("common.syncing")}
                </>
              ) : (
                <>
                  <RefreshCcw className="h-4 w-4" />
                  {t("integrations.syncNow")}
                </>
              )}
            </Button>

            {syncedFiles > 0 && !isSyncing && (
              <span className="text-sm text-muted-foreground">
                {t("integrations.filesSynced", { count: syncedFiles })}
              </span>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
