/**
 * Swiss Knowledge Hub Chatbot SDK - Test Suite
 * 
 * Comprehensive test suite for the chatbot SDK
 * Run with: npm test or in browser with test runner
 */

// Mock DOM environment for Node.js testing
if (typeof window === 'undefined') {
  global.window = {
    location: { origin: 'https://test.com' },
    document: {
      createElement: () => ({ style: {}, addEventListener: () => {} }),
      head: { appendChild: () => {} },
      body: { appendChild: () => {} },
      querySelector: () => null,
      getElementById: () => null
    },
    addEventListener: () => {},
    fetch: () => Promise.resolve({ ok: true, json: () => Promise.resolve({}) })
  };
  global.document = window.document;
}

// Test utilities
const TestUtils = {
  createMockChatbot: (overrides = {}) => ({
    id: 'test-chatbot-id',
    name: 'Test Chatbot',
    apiKey: 'skh_test_key_1234567890abcdef',
    allowedDomains: ['test.com', '*.example.com'],
    customization: {
      theme: {
        primaryColor: '#007bff',
        secondaryColor: '#6c757d',
        fontFamily: 'Inter, sans-serif',
        borderRadius: '8px'
      },
      position: 'bottom-right',
      size: 'medium',
      greeting: 'Hello! How can I help you?',
      placeholder: 'Type your message...',
      showBranding: true
    },
    rateLimits: {
      perMinute: 60,
      perHour: 1000,
      perDay: 10000
    },
    ...overrides
  }),

  createMockConfig: (overrides = {}) => ({
    chatbotId: 'test-chatbot-id',
    apiKey: 'skh_test_key_1234567890abcdef',
    position: 'bottom-right',
    size: 'medium',
    mode: 'widget',
    debug: true,
    ...overrides
  }),

  mockFetch: (response) => {
    global.fetch = jest.fn(() =>
      Promise.resolve({
        ok: true,
        json: () => Promise.resolve(response),
        headers: new Map()
      })
    );
  },

  mockFetchError: (status = 500, message = 'Server Error') => {
    global.fetch = jest.fn(() =>
      Promise.resolve({
        ok: false,
        status,
        json: () => Promise.resolve({ error: message }),
        text: () => Promise.resolve(message)
      })
    );
  }
};

// Test Suite: SDK Initialization
describe('SwissKnowledgeHub SDK Initialization', () => {
  let SDK;

  beforeEach(() => {
    // Reset DOM
    document.body.innerHTML = '';
    
    // Mock the SDK (in real tests, this would be imported)
    SDK = {
      instances: new Map(),
      init: jest.fn(),
      destroy: jest.fn(),
      destroyAll: jest.fn()
    };
  });

  test('should initialize with valid configuration', () => {
    const config = TestUtils.createMockConfig();
    
    SDK.init.mockReturnValue({ instanceId: 'test-instance-1' });
    
    const instance = SDK.init(config);
    
    expect(SDK.init).toHaveBeenCalledWith(config);
    expect(instance).toHaveProperty('instanceId');
  });

  test('should throw error with missing chatbotId', () => {
    const config = TestUtils.createMockConfig();
    delete config.chatbotId;
    
    SDK.init.mockImplementation(() => {
      throw new Error('chatbotId and apiKey are required');
    });
    
    expect(() => SDK.init(config)).toThrow('chatbotId and apiKey are required');
  });

  test('should throw error with missing apiKey', () => {
    const config = TestUtils.createMockConfig();
    delete config.apiKey;
    
    SDK.init.mockImplementation(() => {
      throw new Error('chatbotId and apiKey are required');
    });
    
    expect(() => SDK.init(config)).toThrow('chatbotId and apiKey are required');
  });

  test('should handle multiple instances', () => {
    const config1 = TestUtils.createMockConfig({ chatbotId: 'bot1' });
    const config2 = TestUtils.createMockConfig({ chatbotId: 'bot2' });
    
    SDK.init.mockReturnValueOnce({ instanceId: 'instance-1' });
    SDK.init.mockReturnValueOnce({ instanceId: 'instance-2' });
    
    const instance1 = SDK.init(config1);
    const instance2 = SDK.init(config2);
    
    expect(instance1.instanceId).toBe('instance-1');
    expect(instance2.instanceId).toBe('instance-2');
    expect(SDK.init).toHaveBeenCalledTimes(2);
  });
});

// Test Suite: Configuration Loading
describe('Chatbot Configuration Loading', () => {
  beforeEach(() => {
    TestUtils.mockFetch(TestUtils.createMockChatbot());
  });

  test('should load configuration from API', async () => {
    const config = TestUtils.createMockConfig();
    
    // Mock the loadConfig method
    const loadConfig = async () => {
      const response = await fetch(`/api/public/chatbot/${config.chatbotId}/config`, {
        headers: {
          'Authorization': `Bearer ${config.apiKey}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (!response.ok) {
        throw new Error(`Failed to load config: ${response.status}`);
      }
      
      return await response.json();
    };
    
    const result = await loadConfig();
    
    expect(fetch).toHaveBeenCalledWith(
      `/api/public/chatbot/${config.chatbotId}/config`,
      expect.objectContaining({
        headers: expect.objectContaining({
          'Authorization': `Bearer ${config.apiKey}`
        })
      })
    );
    expect(result).toHaveProperty('id');
    expect(result).toHaveProperty('customization');
  });

  test('should handle configuration load failure', async () => {
    TestUtils.mockFetchError(401, 'Invalid API key');
    
    const config = TestUtils.createMockConfig();
    
    const loadConfig = async () => {
      const response = await fetch(`/api/public/chatbot/${config.chatbotId}/config`);
      if (!response.ok) {
        throw new Error(`Failed to load config: ${response.status}`);
      }
      return await response.json();
    };
    
    await expect(loadConfig()).rejects.toThrow('Failed to load config: 401');
  });
});

// Test Suite: Chat Functionality
describe('Chat Functionality', () => {
  beforeEach(() => {
    TestUtils.mockFetch({
      response: 'Hello! How can I help you today?',
      sessionToken: 'session-123',
      sources: []
    });
  });

  test('should send chat message successfully', async () => {
    const config = TestUtils.createMockConfig();
    const message = 'Hello, chatbot!';
    
    const sendMessage = async () => {
      const response = await fetch(`/api/public/chatbot/${config.chatbotId}/chat`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${config.apiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          message,
          sessionToken: null,
          metadata: {
            domain: window.location.hostname,
            userAgent: 'test-agent',
            referrer: ''
          }
        })
      });
      
      if (!response.ok) {
        throw new Error(`Chat request failed: ${response.status}`);
      }
      
      return await response.json();
    };
    
    const result = await sendMessage();
    
    expect(fetch).toHaveBeenCalledWith(
      `/api/public/chatbot/${config.chatbotId}/chat`,
      expect.objectContaining({
        method: 'POST',
        headers: expect.objectContaining({
          'Authorization': `Bearer ${config.apiKey}`
        }),
        body: expect.stringContaining(message)
      })
    );
    expect(result).toHaveProperty('response');
    expect(result).toHaveProperty('sessionToken');
  });

  test('should handle chat message failure', async () => {
    TestUtils.mockFetchError(429, 'Rate limit exceeded');
    
    const config = TestUtils.createMockConfig();
    
    const sendMessage = async () => {
      const response = await fetch(`/api/public/chatbot/${config.chatbotId}/chat`, {
        method: 'POST',
        headers: { 'Authorization': `Bearer ${config.apiKey}` },
        body: JSON.stringify({ message: 'test' })
      });
      
      if (!response.ok) {
        throw new Error(`Chat request failed: ${response.status}`);
      }
      
      return await response.json();
    };
    
    await expect(sendMessage()).rejects.toThrow('Chat request failed: 429');
  });

  test('should include session token in subsequent requests', async () => {
    const config = TestUtils.createMockConfig();
    const sessionToken = 'existing-session-123';
    
    const sendMessage = async () => {
      await fetch(`/api/public/chatbot/${config.chatbotId}/chat`, {
        method: 'POST',
        headers: { 'Authorization': `Bearer ${config.apiKey}` },
        body: JSON.stringify({
          message: 'Follow-up message',
          sessionToken: sessionToken
        })
      });
    };
    
    await sendMessage();
    
    expect(fetch).toHaveBeenCalledWith(
      expect.any(String),
      expect.objectContaining({
        body: expect.stringContaining(sessionToken)
      })
    );
  });
});

// Test Suite: Security Validation
describe('Security Validation', () => {
  test('should validate API key format', () => {
    const validateApiKey = (apiKey) => {
      if (!apiKey || !apiKey.startsWith('skh_') || apiKey.length < 40) {
        throw new Error('Invalid API key format');
      }
      return true;
    };
    
    expect(() => validateApiKey('invalid-key')).toThrow('Invalid API key format');
    expect(() => validateApiKey('skh_short')).toThrow('Invalid API key format');
    expect(() => validateApiKey('')).toThrow('Invalid API key format');
    expect(validateApiKey('skh_1234567890abcdef1234567890abcdef12345678')).toBe(true);
  });

  test('should validate chatbot ID format', () => {
    const validateChatbotId = (chatbotId) => {
      if (!chatbotId || !/^[0-9a-fA-F]{24}$/.test(chatbotId)) {
        throw new Error('Invalid chatbot ID format');
      }
      return true;
    };
    
    expect(() => validateChatbotId('invalid-id')).toThrow('Invalid chatbot ID format');
    expect(() => validateChatbotId('123')).toThrow('Invalid chatbot ID format');
    expect(() => validateChatbotId('')).toThrow('Invalid chatbot ID format');
    expect(validateChatbotId('507f1f77bcf86cd799439011')).toBe(true);
  });

  test('should validate domain access', () => {
    const validateDomain = (origin, allowedDomains) => {
      if (!allowedDomains || allowedDomains.length === 0) {
        return true;
      }
      
      try {
        const originDomain = new URL(origin).hostname;
        return allowedDomains.some(domain => {
          if (domain.startsWith('*.')) {
            const baseDomain = domain.slice(2);
            return originDomain.endsWith(baseDomain);
          }
          return domain === originDomain;
        });
      } catch {
        return false;
      }
    };
    
    const allowedDomains = ['example.com', '*.subdomain.com'];
    
    expect(validateDomain('https://example.com', allowedDomains)).toBe(true);
    expect(validateDomain('https://test.subdomain.com', allowedDomains)).toBe(true);
    expect(validateDomain('https://unauthorized.com', allowedDomains)).toBe(false);
    expect(validateDomain('invalid-url', allowedDomains)).toBe(false);
    expect(validateDomain('https://example.com', [])).toBe(true);
  });
});

// Test Suite: Rate Limiting
describe('Rate Limiting', () => {
  test('should track rate limit attempts', () => {
    const rateLimitStore = new Map();
    
    const checkRateLimit = (key, limit, windowMs) => {
      const now = Date.now();
      const record = rateLimitStore.get(key);
      
      if (!record || now > record.resetTime) {
        rateLimitStore.set(key, { count: 1, resetTime: now + windowMs });
        return { allowed: true, remaining: limit - 1 };
      }
      
      if (record.count >= limit) {
        return { allowed: false, remaining: 0 };
      }
      
      record.count++;
      return { allowed: true, remaining: limit - record.count };
    };
    
    const key = 'test-key';
    const limit = 3;
    const windowMs = 60000;
    
    // First 3 requests should be allowed
    expect(checkRateLimit(key, limit, windowMs).allowed).toBe(true);
    expect(checkRateLimit(key, limit, windowMs).allowed).toBe(true);
    expect(checkRateLimit(key, limit, windowMs).allowed).toBe(true);
    
    // 4th request should be blocked
    expect(checkRateLimit(key, limit, windowMs).allowed).toBe(false);
  });
});

// Test Suite: Error Handling
describe('Error Handling', () => {
  test('should handle network errors gracefully', async () => {
    global.fetch = jest.fn(() => Promise.reject(new Error('Network error')));
    
    const handleNetworkError = async () => {
      try {
        await fetch('/api/test');
        return { success: true };
      } catch (error) {
        return { success: false, error: error.message };
      }
    };
    
    const result = await handleNetworkError();
    
    expect(result.success).toBe(false);
    expect(result.error).toBe('Network error');
  });

  test('should handle API errors with proper status codes', async () => {
    TestUtils.mockFetchError(403, 'Domain not allowed');
    
    const handleApiError = async () => {
      const response = await fetch('/api/test');
      if (!response.ok) {
        const error = await response.json();
        return { success: false, status: response.status, error: error.error };
      }
      return { success: true };
    };
    
    const result = await handleApiError();
    
    expect(result.success).toBe(false);
    expect(result.status).toBe(403);
    expect(result.error).toBe('Domain not allowed');
  });
});

// Export for Node.js testing
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    TestUtils,
    // Add test suites here for programmatic access
  };
}

console.log('Swiss Knowledge Hub Chatbot SDK - Test Suite Loaded');
console.log('Run tests with your preferred test runner (Jest, Mocha, etc.)');
