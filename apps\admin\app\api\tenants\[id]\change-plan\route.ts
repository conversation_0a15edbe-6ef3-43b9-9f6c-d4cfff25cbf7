import { authOptions } from "@/lib/next-auth";
import dbPromise from "@/lib/shared-db";
import { getServerSession } from "next-auth/next";
import { NextResponse } from "next/server";
import { revalidatePath } from "next/cache";

export async function POST(
  request: Request,
  { params }: { params: { id: string } }
) {
  const session = await getServerSession(authOptions);

  if (!session) {
    return NextResponse.json(
      { error: "Unauthorized" },
      { status: 401 }
    );
  }

  try {
    const db = await dbPromise;
    const body = await request.json();
    const { planId, activateAccount = false } = body;

    // Validate required fields
    if (!planId) {
      return NextResponse.json(
        { error: "Plan ID is required" },
        { status: 400 }
      );
    }

    const { id: tenantId } = params;

    // Verify tenant exists
    const tenant = await db.tenant.findUnique({
      where: { id: tenantId },
      include: {
        Subscription: {
          where: { isActive: true },
          include: { plan: true }
        }
      }
    });

    if (!tenant) {
      return NextResponse.json(
        { error: "Tenant not found" },
        { status: 404 }
      );
    }

    // Verify plan exists and is active
    const newPlan = await db.plan.findUnique({
      where: { id: planId }
    });

    if (!newPlan || !newPlan.isActive) {
      return NextResponse.json(
        { error: "Invalid or inactive plan selected" },
        { status: 400 }
      );
    }

    // Check if tenant already has this plan
    const currentSubscription = tenant.Subscription[0];
    if (currentSubscription && currentSubscription.planId === planId) {
      return NextResponse.json(
        { error: "Tenant is already on this plan" },
        { status: 400 }
      );
    }

    // Use transaction to ensure atomicity
    const result = await db.$transaction(async (prisma) => {
      // Deactivate current subscription if exists
      if (currentSubscription) {
        await prisma.subscription.update({
          where: { id: currentSubscription.id },
          data: {
            isActive: false,
            endDate: new Date()
          }
        });
      }

      // Create new subscription
      const newSubscription = await prisma.subscription.create({
        data: {
          tenantId,
          planId,
          isActive: true,
          startDate: new Date(),
          additionalUsers: 0,
          additionalStorageGB: 0
        },
        include: {
          plan: true,
          tenant: true
        }
      });

      return newSubscription;
    });

    // Log the plan change for audit purposes
    console.log(`Plan changed for tenant ${tenantId}: ${currentSubscription?.plan?.name || 'No Plan'} -> ${newPlan.name}`);

    // ✅ Revalidate the tenant list page and tenant detail page to ensure fresh data
    revalidatePath('/tenants');
    revalidatePath(`/tenants/${tenantId}`);

    return NextResponse.json({
      message: "Plan changed successfully",
      subscription: {
        id: result.id,
        planName: result.plan.name,
        planType: result.plan.type,
        startDate: result.startDate,
        isActive: result.isActive
      }
    });

  } catch (error) {
    console.error("Error changing plan:", error);
    return NextResponse.json(
      { error: "Failed to change plan" },
      { status: 500 }
    );
  }
}

// GET endpoint to fetch available plans for a tenant
export async function GET(
  _request: Request,
  { params }: { params: { id: string } }
) {
  const session = await getServerSession(authOptions);

  if (!session) {
    return NextResponse.json(
      { error: "Unauthorized" },
      { status: 401 }
    );
  }

  try {
    const db = await dbPromise;
    const { id: tenantId } = params;

    // Get tenant's current plan
    const tenant = await db.tenant.findUnique({
      where: { id: tenantId },
      include: {
        Subscription: {
          where: { isActive: true },
          include: { plan: true }
        }
      }
    });

    if (!tenant) {
      return NextResponse.json(
        { error: "Tenant not found" },
        { status: 404 }
      );
    }

    const currentPlanId = tenant.Subscription[0]?.planId;

    // Get all active plans except the current one
    const availablePlans = await db.plan.findMany({
      where: {
        isActive: true,
        ...(currentPlanId && { id: { not: currentPlanId } })
      },
      select: {
        id: true,
        name: true,
        type: true,
        description: true,
        price: true,
        includedUsers: true,
        vectorStoreGB: true,
        webSearchLimit: true,
        additionalUserFee: true
      },
      orderBy: [
        { type: "asc" },
        { price: "asc" }
      ]
    });

    return NextResponse.json({
      currentPlan: tenant.Subscription[0]?.plan || null,
      availablePlans
    });

  } catch (error) {
    console.error("Error fetching available plans:", error);
    return NextResponse.json(
      { error: "Failed to fetch available plans" },
      { status: 500 }
    );
  }
}
