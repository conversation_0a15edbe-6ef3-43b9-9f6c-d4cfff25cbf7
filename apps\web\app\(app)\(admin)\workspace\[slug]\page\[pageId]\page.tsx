import WorkspacePage from "@/components/wrapper-screens/workspace/page-wrapper";
import { getPage, getVectorStoreUsageSummary } from "@/services";
import { checkSharePointPageAccess } from "@/lib/sharepoint-page-access";
import { getPageMembership } from "@/lib/page-membership";
import { getTranslations } from "@/lib/server-i18n";
import { cookies } from "next/headers";
import { redirect } from "next/navigation";

export default async function Page({ params, searchParams }) {
  const { pageId, slug } = params;

  const tenantId = cookies().get("currentOrganizationId")?.value;
  const userId = cookies().get("userId")?.value;
  if (!tenantId || !userId) {
    return redirect("/sign-in");
  }

  // Get server-side translations
  const { t } = getTranslations();

  // Check if force refresh is requested
  const forceRefresh = searchParams?.refreshSharePointAccess === "true";

  const [page, usageSummary, sharePointAccess, pageMembership] =
    await Promise.all([
      getPage({
        workspaceSlug: slug,
        tenantId,
        pageId,
        userId,
      }),
      getVectorStoreUsageSummary(tenantId, userId),
      // Check SharePoint access for this page
      checkSharePointPageAccess(userId, tenantId, pageId, forceRefresh, t),
      // Get user's page membership
      getPageMembership(userId, pageId),
    ]);

  if (!page || !page.data) {
    return redirect(`/my-hub`);
  }

  return (
    <WorkspacePage
      tenantId={tenantId}
      permission={page?.permission}
      page={page?.data ?? []}
      workspaceSlug={slug}
      pageId={pageId}
      usageSummary={usageSummary}
      sharePointAccess={sharePointAccess}
      pageMembership={pageMembership}
    />
  );
}
