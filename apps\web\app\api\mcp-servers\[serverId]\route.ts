import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/next-auth";
import db from "@/lib/shared-db";

interface MCPServerUpdate {
  name?: string;
  description?: string;
  server_type?: "STDIO" | "HTTP";
  command?: string;
  args?: string[];
  env?: Record<string, string>;
  url?: string;
  headers?: Record<string, string>;
  timeout?: number;
  auto_restart?: boolean;
  is_public?: boolean;
  status?: "ACTIVE" | "INACTIVE" | "ERROR" | "TESTING";
}

interface MCPServerResponse {
  id: string;
  name: string;
  description?: string;
  server_type: "STDIO" | "HTTP";
  command?: string;
  args?: string[];
  env?: Record<string, string>;
  url?: string;
  headers?: Record<string, string>;
  status: string;
  last_error?: string;
  timeout: number;
  auto_restart: boolean;
  is_public: boolean;
  user_id: string;
  tenant_id: string;
  created_at: string;
  updated_at: string;
}

/**
 * GET /api/mcp-servers/[serverId] - Get a specific MCP server
 */
export async function GET(
  req: NextRequest,
  { params }: { params: { serverId: string } }
) {
  try {
    const session: any = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { serverId } = params;

    // Find server with access control
    const server = await db.mCPServer.findFirst({
      where: {
        id: serverId,
        tenantId: session.currentOrganization?.id,
        OR: [{ userId: session.userId }, { isPublic: true }],
      },
    });

    if (!server) {
      return NextResponse.json(
        { error: "MCP server not found" },
        { status: 404 }
      );
    }

    // Convert to response format
    const response: MCPServerResponse = {
      id: server.id,
      name: server.name,
      description: server.description,
      command: server.command,
      args: server.args,
      env: server.env,
      status: server.status,
      last_error: server.lastError,
      timeout: server.timeout,
      server_type: server.serverType as "STDIO" | "HTTP",
      url: server.url,
      headers: server.headers,
      auto_restart: server.autoRestart,
      is_public: server.isPublic,
      user_id: server.userId,
      tenant_id: server.tenantId,
      created_at: server.createdAt.toISOString(),
      updated_at: server.updatedAt.toISOString(),
    };

    return NextResponse.json(response);
  } catch (error: any) {
    console.error("[MCP_SERVER_GET]", error);
    return NextResponse.json(
      { error: error.message || "Failed to fetch MCP server" },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/mcp-servers/[serverId] - Update an MCP server
 */
export async function PUT(
  req: NextRequest,
  { params }: { params: { serverId: string } }
) {
  try {
    const session: any = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { serverId } = params;
    const body: MCPServerUpdate = await req.json();

    // Find server with ownership check
    const server = await db.mCPServer.findFirst({
      where: {
        id: serverId,
        tenantId: session.currentOrganization?.id,
        userId: session.userId, // Only owner can update
      },
    });

    if (!server) {
      return NextResponse.json(
        { error: "MCP server not found or access denied" },
        { status: 404 }
      );
    }

    // Build update data
    const updateData: any = {
      updatedAt: new Date(),
    };

    if (body.name !== undefined) {
      // Check name uniqueness
      const existingServer = await db.mCPServer.findFirst({
        where: {
          name: body.name,
          tenantId: session.currentOrganization?.id,
          id: { not: serverId },
        },
      });
      if (existingServer) {
        return NextResponse.json(
          { error: `MCP server with name '${body.name}' already exists` },
          { status: 400 }
        );
      }
      updateData.name = body.name;
    }

    // Validate NPX commands are not allowed
    if (
      body.command !== undefined &&
      body.command.toLowerCase().includes("npx")
    ) {
      return NextResponse.json(
        {
          error:
            "NPX-based servers are not supported in this environment. Please use Python scripts or other direct executables.",
        },
        { status: 400 }
      );
    }

    if (body.description !== undefined)
      updateData.description = body.description;
    if (body.command !== undefined) updateData.command = body.command;
    if (body.args !== undefined) updateData.args = body.args;
    if (body.env !== undefined) updateData.env = body.env;
    if (body.url !== undefined) updateData.url = body.url;
    if (body.headers !== undefined) updateData.headers = body.headers;
    if (body.server_type !== undefined)
      updateData.serverType = body.server_type;
    if (body.timeout !== undefined) updateData.timeout = body.timeout;
    if (body.auto_restart !== undefined)
      updateData.autoRestart = body.auto_restart;
    if (body.is_public !== undefined) updateData.isPublic = body.is_public;
    if (body.status !== undefined) updateData.status = body.status;

    // Update in database
    const updatedServer = await db.mCPServer.update({
      where: { id: serverId },
      data: updateData,
    });

    // Convert to response format
    const response: MCPServerResponse = {
      id: updatedServer.id,
      name: updatedServer.name,
      description: updatedServer.description,
      server_type: updatedServer.serverType as "STDIO" | "HTTP",
      command: updatedServer.command,
      args: updatedServer.args as string[],
      env: updatedServer.env as Record<string, string>,
      url: updatedServer.url,
      headers: updatedServer.headers as Record<string, string>,
      status: updatedServer.status,
      last_error: updatedServer.lastError,
      timeout: updatedServer.timeout,
      auto_restart: updatedServer.autoRestart,
      is_public: updatedServer.isPublic,
      user_id: updatedServer.userId,
      tenant_id: updatedServer.tenantId,
      created_at: updatedServer.createdAt.toISOString(),
      updated_at: updatedServer.updatedAt.toISOString(),
    };

    return NextResponse.json(response);
  } catch (error: any) {
    console.error("[MCP_SERVER_PUT]", error);
    return NextResponse.json(
      { error: error.message || "Failed to update MCP server" },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/mcp-servers/[serverId] - Delete an MCP server
 */
export async function DELETE(
  req: NextRequest,
  { params }: { params: { serverId: string } }
) {
  try {
    const session: any = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { serverId } = params;

    // Find server with ownership check
    const server = await db.mCPServer.findFirst({
      where: {
        id: serverId,
        tenantId: session.currentOrganization?.id,
        userId: session.userId, // Only owner can delete
      },
    });

    if (!server) {
      return NextResponse.json(
        { error: "MCP server not found or access denied" },
        { status: 404 }
      );
    }

    // Delete from database
    await db.mCPServer.delete({
      where: { id: serverId },
    });

    // Also delete any associated chat sessions
    await db.mCPChatSession.deleteMany({
      where: { mcpServerId: serverId },
    });

    return NextResponse.json({ message: "MCP server deleted successfully" });
  } catch (error: any) {
    console.error("[MCP_SERVER_DELETE]", error);
    return NextResponse.json(
      { error: error.message || "Failed to delete MCP server" },
      { status: 500 }
    );
  }
}
