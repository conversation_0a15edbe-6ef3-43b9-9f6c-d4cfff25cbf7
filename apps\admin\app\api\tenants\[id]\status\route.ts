import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/next-auth";
import dbPromise from "@/lib/shared-db";
import { revalidatePath } from "next/cache";

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  let body: any = null;

  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    const db = await dbPromise;
    const tenantId = params.id;

    // Parse request body to get the desired status
    body = await request.json();
    const { status } = body;

    console.log(`Processing tenant status update request for tenant ${tenantId}:`, { status });

    // Validate payload - status should be "active", "inactive", or "suspended"
    const validStatuses = ["active", "inactive", "suspended"];
    if (!validStatuses.includes(status)) {
      return NextResponse.json(
        { error: `Status must be one of: ${validStatuses.join(", ")}` },
        { status: 400 }
      );
    }

    // Verify tenant exists
    const tenant = await db.tenant.findUnique({
      where: { id: tenantId },
      select: {
        id: true,
        name: true,
        status: true,
        Subscription: {
          select: {
            id: true,
            isActive: true
          }
        }
      }
    });

    if (!tenant) {
      return NextResponse.json(
        { error: "Tenant not found" },
        { status: 404 }
      );
    }

    const currentStatus = tenant.status || "inactive";

    // Update tenant status and manage subscription state accordingly
    let subscriptionUpdateResult: { count: number } | null = null;
    
    if (status === "active") {
      // Activate tenant: ensure they have active subscriptions
      if (tenant.Subscription.length > 0) {
        subscriptionUpdateResult = await db.subscription.updateMany({
          where: {
            tenantId: tenantId
          },
          data: {
            isActive: true,
            updatedAt: new Date()
          }
        });
      }
    } else if (status === "suspended" || status === "inactive") {
      // Suspend/Deactivate tenant: deactivate all subscriptions
      if (tenant.Subscription.length > 0) {
        subscriptionUpdateResult = await db.subscription.updateMany({
          where: {
            tenantId: tenantId,
            isActive: true
          },
          data: {
            isActive: false,
            updatedAt: new Date()
          }
        });
      }
    }

    // Update tenant status in database
    const updatedTenant = await db.tenant.update({
      where: { id: tenantId },
      data: {
        status: status,
        updatedAt: new Date()
      },
      select: {
        id: true,
        name: true,
        status: true,
        Subscription: {
          select: {
            id: true,
            isActive: true
          }
        }
      }
    });

    // Log the status change for audit purposes
    console.log(`Tenant status updated for tenant ${tenantId}: ${currentStatus} -> ${status}`);
    if (subscriptionUpdateResult) {
      console.log(`Updated ${subscriptionUpdateResult.count} subscriptions for tenant ${tenantId}`);
    }

    // Determine action message
    let actionMessage = "";
    switch (status) {
      case "active":
        actionMessage = currentStatus === "suspended" ? "reactivated" : "activated";
        break;
      case "suspended":
        actionMessage = "suspended";
        break;
      case "inactive":
        actionMessage = "deactivated";
        break;
    }

    // ✅ Revalidate the tenant list page and tenant detail page to ensure fresh data
    revalidatePath('/tenants');
    revalidatePath(`/tenants/${params.id}`);

    return NextResponse.json({
      message: `Tenant ${actionMessage} successfully`,
      tenant: {
        id: updatedTenant.id,
        name: updatedTenant.name,
        status: updatedTenant.status,
        hasActiveSubscription: updatedTenant.Subscription.some(sub => sub.isActive)
      }
    });

  } catch (error) {
    console.error("Error updating tenant status:", error);
    
    return NextResponse.json(
      { 
        error: "Failed to update tenant status",
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
