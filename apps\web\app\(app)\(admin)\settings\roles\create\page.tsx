"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useLanguage } from "@/lib/language-context";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { ArrowLeft, Check, ChevronsUpDown, Loader2 } from "lucide-react";
import toast from "react-hot-toast";
import { getWorkspace } from "@/services";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { getCookie } from "@/utils/cookies";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from "@/components/ui/command";
import { cn } from "@/lib/utils";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

// Define interfaces for our data structures
interface Permission {
  id: string;
  action: string;
  resource: string;
  description?: string;
}

interface Workspace {
  id: string;
  name: string;
  description?: string;
}

export default function CreateRolePage() {
  const router = useRouter();
  const { t } = useLanguage();
  const [name, setName] = useState("");
  const [description, setDescription] = useState("");
  const [selectedPermissions, setSelectedPermissions] = useState<string[]>([]);
  const [selectedWorkspaces, setSelectedWorkspaces] = useState<Workspace[]>([]);
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [workspaces, setWorkspaces] = useState<Workspace[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [openWorkspaceSelector, setOpenWorkspaceSelector] = useState(false);
  const tenantId = getCookie("currentOrganizationId") as string;
  const userId = getCookie("userId") as string;

  // Define the order of resources
  const resourceOrder = ["WORKSPACE", "PAGE", "FOLDER", "FILE"];

  // Group permissions by resource
  const unsortedGroupedPermissions = permissions.reduce(
    (acc: any, permission) => {
      const resource = permission.resource;
      if (!acc[resource]) {
        acc[resource] = [];
      }
      acc[resource].push(permission);
      return acc;
    },
    {}
  );

  // Create a new ordered object based on the specified resource order
  const groupedPermissions = resourceOrder.reduce(
    (orderedAcc: any, resource) => {
      if (unsortedGroupedPermissions[resource]) {
        orderedAcc[resource] = unsortedGroupedPermissions[resource];
      }
      return orderedAcc;
    },
    {}
  );

  // Fetch permissions and workspaces
  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      try {
        // Fetch permissions
        const permissionsResponse = await fetch("/api/permissions");
        if (permissionsResponse.ok) {
          const data = await permissionsResponse.json();
          setPermissions(data.permissions || []);
        }

        // Fetch workspaces

        const workspacesResponse = await getWorkspace(tenantId, userId);
        if (workspacesResponse.workspaces) {
          setWorkspaces(workspacesResponse.workspaces || []);
        }
      } catch (error) {
        console.error("Error fetching data:", error);
        toast.error(t("common.error"));
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [tenantId, t]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const workspaceIds = selectedWorkspaces.map((workspace) => workspace.id);

      const response = await fetch("/api/roles", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          name,
          description,
          permissionIds: selectedPermissions,
          workspaceIds,
          tenantId,
        }),
      });

      if (response.ok) {
        toast.success(t("roles.roleCreatedSuccess"));
        // Force a hard navigation to refresh the roles list
        window.location.href = "/settings/roles";
      } else {
        const error = await response.json();
        toast.error(error.error || t("roles.roleCreateFailed"));
      }
    } catch (error) {
      console.error("Error creating role:", error);
      toast.error(t("roles.roleCreateFailed"));
    } finally {
      setIsSubmitting(false);
    }
  };

  // Helper function to get child resources based on hierarchy
  const getChildResources = (resource: string): string[] => {
    const hierarchy = {
      WORKSPACE: ["PAGE", "FOLDER", "FILE"],
      PAGE: ["FOLDER", "FILE"],
      FOLDER: ["FILE"],
      FILE: [],
    };
    return hierarchy[resource] || [];
  };
  // Helper function to get all permissions for a specific action and resource
  const getPermissionsByActionAndResource = (
    action: string,
    resource: string
  ): Permission[] => {
    return permissions.filter(
      (p) => p.action === action && p.resource === resource
    );
  };

  // Check if all permissions for a resource are selected
  const areAllResourcePermissionsSelected = (
    resourcePermissions: Permission[]
  ) => {
    const permissionIds = resourcePermissions.map((p: Permission) => p.id);
    return permissionIds.every((id: string) =>
      selectedPermissions.includes(id)
    );
  };
  // Helper function to check if a parent resource permission is required
  const requiresParentPermission = (
    permission: Permission
  ): {
    required: boolean;
    parentResource: string;
    parentPermissionId?: string;
  } => {
    const { action, resource } = permission;

    // Define parent relationships
    const parentMap = {
      FILE: "FOLDER",
      FOLDER: "PAGE",
      PAGE: "WORKSPACE",
    };

    const parentResource = parentMap[resource];
    if (!parentResource) return { required: false, parentResource: "" };

    // Find the corresponding parent permission with the same action
    const parentPermission = permissions.find(
      (p) => p.action === action && p.resource === parentResource
    );

    if (!parentPermission) return { required: false, parentResource };

    // Check if the parent permission is selected
    const hasParentPermission = selectedPermissions.includes(
      parentPermission.id
    );

    return {
      required: !hasParentPermission,
      parentResource,
      parentPermissionId: parentPermission.id,
    };
  };

  // Helper function to check if all required parent permissions are selected
  const hasAllRequiredParentPermissions = (permission: Permission): boolean => {
    // If it's a workspace, no parent permissions are required
    if (permission.resource === "WORKSPACE") return true;

    // Check direct parent
    const { required, parentPermissionId } =
      requiresParentPermission(permission);

    // If direct parent permission is required but not selected, return false
    if (required) return false;

    // If parent permission exists, recursively check if its parent permissions are selected
    if (parentPermissionId) {
      const parentPermission = findPermissionById(parentPermissionId);
      if (parentPermission) {
        return hasAllRequiredParentPermissions(parentPermission);
      }
    }

    return true;
  };
  // Check if a permission can be granted (has all required parent permissions)
  const canGrantPermission = (permission: Permission): boolean => {
    return hasAllRequiredParentPermissions(permission);
  };

  // Helper function to apply permission to child resources
  const applyPermissionToChildren = (
    action: string,
    resource: string
  ): string[] => {
    const childResources = getChildResources(resource);
    const childPermissionIds: string[] = [];

    childResources.forEach((childResource) => {
      const childPermissions = getPermissionsByActionAndResource(
        action,
        childResource
      );
      childPermissionIds.push(...childPermissions.map((p) => p.id));
    });

    return childPermissionIds;
  };

  // Helper function to get all affected child permissions when removing a permission
  const getAffectedChildPermissions = (permission: Permission): string[] => {
    // Get direct child permissions
    const childPermissionIds = applyPermissionToChildren(
      permission.action,
      permission.resource
    );

    // For each child resource, find all permissions with the same action
    const allAffectedIds: string[] = [...childPermissionIds];

    // Return all affected permission IDs
    return allAffectedIds;
  };

  const handlePermissionChange = (
    permissionId: string,
    checked: boolean | string
  ) => {
    // Convert to boolean if it's a string
    const isChecked =
      typeof checked === "string" ? checked === "true" : checked;

    const permission = findPermissionById(permissionId);
    if (!permission) return;

    if (isChecked) {
      // Check if all required parent permissions are selected
      if (!hasAllRequiredParentPermissions(permission)) {
        // Get the direct parent resource
        const { parentResource } = requiresParentPermission(permission);

        // Show toast notification about the parent permission requirement
        toast.error(
          t("roles.errorMessages.parentPermissionRequired", {
            action: permission.action,
            parentResource: parentResource
          })
        );
        return;
      }

      // Get child permissions to apply based on hierarchy
      const childPermissionIds = applyPermissionToChildren(
        permission.action,
        permission.resource
      );

      // Add the current permission and all child permissions
      const newPermissions = [permissionId, ...childPermissionIds].filter(
        (id) => !selectedPermissions.includes(id)
      );

      setSelectedPermissions([...selectedPermissions, ...newPermissions]);
    } else {
      // If unchecking a parent permission, also uncheck all child permissions
      const permission = findPermissionById(permissionId);
      if (!permission) return;

      // Get all affected child permissions
      const affectedPermissionIds = getAffectedChildPermissions(permission);

      // Show toast notification about removing child permissions
      if (affectedPermissionIds.length > 0) {
        toast.success(
          t("roles.errorMessages.permissionRemovedWithCascade", {
            action: permission.action,
            resource: permission.resource
          })
        );
      }

      // Remove this permission and all its child permissions
      setSelectedPermissions(
        selectedPermissions.filter(
          (id) => id !== permissionId && !affectedPermissionIds.includes(id)
        )
      );
    }
  };

  // Helper function to find permission by ID
  const findPermissionById = (id: string): Permission | undefined => {
    return permissions.find((p) => p.id === id);
  };

  // Toggle all permissions for a resource
  const toggleResourcePermissions = (
    resourcePermissions: Permission[],
    checked: boolean | string
  ) => {
    // Convert to boolean if it's a string
    const isChecked =
      typeof checked === "string" ? checked === "true" : checked;

    // Get the resource type from the first permission
    const resource = resourcePermissions[0]?.resource;
    if (!resource) return;

    const permissionIds = resourcePermissions.map((p: Permission) => p.id);

    if (isChecked) {
      // For each permission in this resource, get child permissions
      const allChildPermissionIds: string[] = [];

      resourcePermissions.forEach((permission) => {
        // Check if all required parent permissions are selected
        if (!hasAllRequiredParentPermissions(permission)) {
          // Get the direct parent resource
          const { parentResource } = requiresParentPermission(permission);

          // Show toast notification about the parent permission requirement
          toast.error(
            t("roles.errorMessages.cannotGrantWithoutParent", {
              action: permission.action,
              resource: resource,
              parentResource: parentResource
            })
          );
          return;
        }

        const childPermissionIds = applyPermissionToChildren(
          permission.action,
          permission.resource
        );
        allChildPermissionIds.push(...childPermissionIds);
      });

      // Add all permissions that aren't already selected (including child permissions)
      const newPermissions = [
        ...permissionIds,
        ...allChildPermissionIds,
      ].filter((id: string) => !selectedPermissions.includes(id));

      setSelectedPermissions([...selectedPermissions, ...newPermissions]);
    } else {
      // Get all affected child permissions for each permission in this resource
      const allAffectedPermissionIds: string[] = [];

      resourcePermissions.forEach((permission) => {
        const affectedPermissionIds = getAffectedChildPermissions(permission);
        allAffectedPermissionIds.push(...affectedPermissionIds);
      });

      // Show toast notification about removing child permissions
      if (allAffectedPermissionIds.length > 0) {
        toast.success(
          t("roles.errorMessages.allPermissionsRemovedWithCascade", {
            resource: resource
          })
        );
      }

      // Remove all permissions for this resource and its children
      setSelectedPermissions(
        selectedPermissions.filter(
          (id: string) =>
            !permissionIds.includes(id) &&
            !allAffectedPermissionIds.includes(id)
        )
      );
    }
  };
  const addWorkspace = (workspace: Workspace) => {
    if (!selectedWorkspaces.some((w) => w.id === workspace.id)) {
      setSelectedWorkspaces([...selectedWorkspaces, workspace]);
    }
    setOpenWorkspaceSelector(false);
  };

  const removeWorkspace = (workspaceId: string) => {
    setSelectedWorkspaces(
      selectedWorkspaces.filter((w) => w.id !== workspaceId)
    );
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  return (
    <div className="container py-6 max-w-5xl">
      <div className="flex items-center mb-6">
        <Button
          variant="ghost"
          size="sm"
          className="mr-2"
          onClick={() => router.push("/settings/roles")}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          {t("common.back")}
        </Button>
        <h1 className="text-2xl font-bold">{t("roles.createRole")}</h1>
      </div>

      <form onSubmit={handleSubmit} className="space-y-8">
        <Card>
          <CardHeader>
            <CardTitle>{t("roles.basicInfo")}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4">
              <div className="grid grid-cols-1 md:grid-cols-4 items-center gap-4">
                <Label htmlFor="name" className="md:text-right">
                  {t("roles.name")}
                </Label>
                <Input
                  id="name"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  className="col-span-1 md:col-span-3"
                  required
                />
              </div>
              <div className="grid grid-cols-1 md:grid-cols-4 items-center gap-4">
                <Label htmlFor="description" className="md:text-right">
                  {t("roles.description")}
                </Label>
                <Textarea
                  id="description"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  className="col-span-1 md:col-span-3"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>{t("roles.assignWorkspaces")}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex flex-wrap gap-2">
                {selectedWorkspaces.map((workspace) => (
                  <div
                    key={workspace.id}
                    className="flex items-center gap-2 bg-secondary text-secondary-foreground px-3 py-1 rounded-full"
                  >
                    <span>{workspace.name}</span>
                    <Button
                      type="button"
                      variant="ghost"
                      size="icon"
                      className="h-5 w-5 rounded-full"
                      onClick={() => removeWorkspace(workspace.id)}
                    >
                      <span className="sr-only">{t("common.remove")}</span>
                      <span aria-hidden="true">×</span>
                    </Button>
                  </div>
                ))}
              </div>

              <Popover
                open={openWorkspaceSelector}
                onOpenChange={setOpenWorkspaceSelector}
              >
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    role="combobox"
                    aria-expanded={openWorkspaceSelector}
                    className="w-full justify-between"
                  >
                    {t("roles.selectWorkspaces")}
                    <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-full p-0">
                  <Command>
                    <CommandInput
                      placeholder={t("workspace.searchWorkspaces")}
                    />
                    <CommandEmpty>{t("groups.noWorkspacesFound")}</CommandEmpty>
                    <CommandGroup>
                      {workspaces.map((workspace) => (
                        <CommandItem
                          key={workspace.id}
                          value={workspace.name}
                          onSelect={() => addWorkspace(workspace)}
                        >
                          <Check
                            className={cn(
                              "mr-2 h-4 w-4",
                              selectedWorkspaces.some(
                                (w) => w.id === workspace.id
                              )
                                ? "opacity-100"
                                : "opacity-0"
                            )}
                          />
                          {workspace.name}
                        </CommandItem>
                      ))}
                    </CommandGroup>
                  </Command>
                </PopoverContent>
              </Popover>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>{t("roles.permissions")}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="border rounded-md p-2 max-h-[400px] overflow-y-auto">
              <Accordion type="single" className="w-full" collapsible>
                {Object.entries(groupedPermissions).map(
                  ([resource, resourcePermissions]) => (
                    <AccordionItem key={resource} value={resource}>
                      <AccordionTrigger className="px-4">
                        <div className="flex items-center gap-2">
                          <Checkbox
                            id={`resource-${resource}`}
                            checked={areAllResourcePermissionsSelected(
                              resourcePermissions as Permission[]
                            )}
                            disabled={
                              resource !== "WORKSPACE" &&
                              (resourcePermissions as Permission[]).some(
                                (p) =>
                                  !canGrantPermission(p) &&
                                  !selectedPermissions.includes(p.id)
                              )
                            }
                            onCheckedChange={(checked) =>
                              toggleResourcePermissions(
                                resourcePermissions as Permission[],
                                checked
                              )
                            }
                            onClick={(e) => e.stopPropagation()}
                          />
                          <Label
                            htmlFor={`resource-${resource}`}
                            onClick={(e) => e.stopPropagation()}
                            className={
                              resource !== "WORKSPACE" &&
                              (resourcePermissions as Permission[]).some(
                                (p) =>
                                  !canGrantPermission(p) &&
                                  !selectedPermissions.includes(p.id)
                              )
                                ? "text-muted-foreground"
                                : ""
                            }
                          >
                            {t(`roles.resources.${resource}`)}
                            {resource === "WORKSPACE" && (
                              <span className="ml-2 text-xs text-blue-600 dark:text-blue-400">
                                (L1)
                              </span>
                            )}
                            {resource === "PAGE" && (
                              <span className="ml-2 text-xs text-green-600 dark:text-green-400">
                                (L2)
                              </span>
                            )}
                            {resource === "FOLDER" && (
                              <span className="ml-2 text-xs text-amber-600 dark:text-amber-400">
                                (L3)
                              </span>
                            )}
                            {resource === "FILE" && (
                              <span className="ml-2 text-xs text-purple-600 dark:text-purple-400">
                                (L4)
                              </span>
                            )}
                            {resource !== "WORKSPACE" &&
                              (resourcePermissions as Permission[]).some(
                                (p) =>
                                  !canGrantPermission(p) &&
                                  !selectedPermissions.includes(p.id)
                              ) && (
                                <span
                                  className="ml-2 text-xs text-red-600 dark:text-red-400"
                                  title="Missing parent permissions"
                                >
                                  🔒
                                </span>
                              )}
                          </Label>
                        </div>
                      </AccordionTrigger>
                      <AccordionContent className="px-2 pt-1 pb-0">
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-1 mb-2">
                          {(resourcePermissions as Permission[]).map(
                            (permission: Permission) => (
                              <div
                                key={permission.id}
                                className="flex items-center space-x-2"
                              >
                                <Checkbox
                                  id={permission.id}
                                  checked={selectedPermissions.includes(
                                    permission.id
                                  )}
                                  disabled={
                                    !canGrantPermission(permission) &&
                                    !selectedPermissions.includes(permission.id)
                                  }
                                  onCheckedChange={(checked) =>
                                    handlePermissionChange(
                                      permission.id,
                                      checked
                                    )
                                  }
                                />
                                <Label
                                  htmlFor={permission.id}
                                  className={
                                    !canGrantPermission(permission) &&
                                    !selectedPermissions.includes(permission.id)
                                      ? "text-muted-foreground"
                                      : ""
                                  }
                                >
                                  {permission.action ? t(`roles.actions.${permission.action}`) : "Invalid Action"}
                                  {(permission.resource === "PAGE" ||
                                    permission.resource === "FOLDER" ||
                                    permission.resource === "FILE") && (
                                    <span className="ml-1 text-xs text-blue-600 dark:text-blue-400">
                                      ↓
                                    </span>
                                  )}
                                  {!canGrantPermission(permission) &&
                                    !selectedPermissions.includes(
                                      permission.id
                                    ) && (
                                      <span
                                        className="ml-1 text-xs text-red-600 dark:text-red-400"
                                        title="Missing parent permission"
                                      >
                                        🔒
                                      </span>
                                    )}
                                </Label>
                              </div>
                            )
                          )}
                        </div>
                      </AccordionContent>
                    </AccordionItem>
                  )
                )}
              </Accordion>
            </div>
          </CardContent>
        </Card>

        <div className="flex justify-end gap-4">
          <Button
            type="button"
            variant="outline"
            onClick={() => router.push("/settings/roles")}
          >
            {t("common.cancel")}
          </Button>
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                {t("common.creating")}
              </>
            ) : (
              t("common.create")
            )}
          </Button>
        </div>
      </form>
    </div>
  );
}
