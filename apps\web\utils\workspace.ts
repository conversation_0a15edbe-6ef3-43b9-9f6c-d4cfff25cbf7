import { get<PERSON><PERSON>ie } from "./cookies";
import { getWorkspace } from "@/services";

/**
 * Fetches workspace data by slug
 * @param slug Workspace slug
 * @returns Workspace data or null if not found
 */
export const fetchWorkspaceBySlug = async (slug: string) => {
  try {
    // Only proceed if we have a slug
    if (!slug) {
      return null;
    }

    // Get tenant and user IDs from cookies
    const tenantId = getCookie("currentOrganizationId") || "";
    const userId = getCookie("userId") || "";

    if (!tenantId || !userId) {
      return null;
    }

    // Fetch workspace data from API
    const response = await getWorkspace(tenantId, userId, slug);
    return response?.workspace || null;
  } catch (error) {
    console.error("Error fetching workspace:", error);
    return null;
  }
};

/**
 * Cache for workspace data to avoid repeated API calls
 */
const workspaceCache: Record<string, any> = {};

/**
 * Fetches workspace data by slug with caching
 * @param slug Workspace slug
 * @returns Workspace data or null if not found
 */
export const fetchWorkspaceBySlugWithCache = async (slug: string) => {
  // Return from cache if available
  if (workspaceCache[slug]) {
    return workspaceCache[slug];
  }

  // Fetch fresh data
  const workspace = await fetchWorkspaceBySlug(slug);

  // Cache the result if successful
  if (workspace) {
    workspaceCache[slug] = workspace;
  }

  return workspace;
};
