import WorkspacePages from "@/components/wrapper-screens/workspace/pages-wrapper";
import { getPage } from "@/services";
import { getDriveFolders } from "@/services/src/integration";
import { cookies } from "next/headers";
import { redirect } from "next/navigation";

export default async function pages({ params }: any) {
  const workspaceSlug = params.slug;
  const tenantId = await cookies().get("currentOrganizationId")?.value;
  const userId = await cookies().get("userId")?.value;
  const [pages, folders] = await Promise.all([
    getPage({ workspaceSlug, tenantId, userId }),
    getDriveFolders(tenantId, userId),
  ]);

  if (!tenantId || !userId) {
    return redirect("/sign-in");
  }
  if (!pages || !pages.data) {
    return redirect(`/my-hub`);
  }

  return (
    <WorkspacePages
      permission={pages?.permission}
      folder={folders}
      workspaceId={pages?.data?.[0]?.workspaceId}
      tenantId={tenantId}
      workspaceSlug={workspaceSlug}
      pages={pages?.data ?? []}
    />
  );
}
