"use client";

import { DeleteConfirmationDialog } from "./delete-confirmation-dialog";
import { useLanguage } from "@/lib/language-context";

interface FileDeleteConfirmationProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  fileName: string;
  isLoading?: boolean;
}

export function FileDeleteConfirmation({
  isOpen,
  onClose,
  onConfirm,
  fileName,
  isLoading = false,
}: FileDeleteConfirmationProps) {
  const { t } = useLanguage();

  return (
    <DeleteConfirmationDialog
      isOpen={isOpen}
      onClose={onClose}
      onConfirm={onConfirm}
      title={t("workspace.deleteFileConfirm")}
      description={t("workspace.deleteFileWarning", { name: fileName })}
      isLoading={isLoading}
      loadingText={t("workspace.deletingFile")}
      destructiveActionText={t("common.delete")}
    />
  );
}
