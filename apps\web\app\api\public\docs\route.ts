import { NextRequest, NextResponse } from "next/server";

interface APIEndpoint {
  method: string;
  path: string;
  description: string;
  authentication: string;
  parameters?: Record<string, any>;
  requestBody?: Record<string, any>;
  responses: Record<string, any>;
  examples?: Record<string, any>;
}

interface APIDocumentation {
  title: string;
  version: string;
  description: string;
  baseUrl: string;
  authentication: {
    type: string;
    description: string;
    example: string;
  };
  endpoints: APIEndpoint[];
}

export async function GET(req: NextRequest) {
  const { searchParams } = new URL(req.url);
  const format = searchParams.get("format") || "json";

  const baseUrl = `${req.nextUrl.protocol}//${req.nextUrl.host}/api/public`;

  const documentation: APIDocumentation = {
    title: "Swiss Knowledge Hub - Public Chatbot API",
    version: "1.0.0",
    description:
      "Public API for interacting with chatbots, retrieving configurations, analytics, and managing webhooks. Supports real-time streaming responses for enhanced user experience.",
    baseUrl,
    authentication: {
      type: "Bearer Token",
      description:
        "Use your chatbot's API key as a Bearer token in the Authorization header",
      example: "Authorization: Bearer skh_your_api_key_here",
    },
    endpoints: [
      {
        method: "POST",
        path: "/chatbot/{chatbotId}/chat",
        description:
          "Send a message to the chatbot and receive a response. Supports both streaming and non-streaming responses.",
        authentication: "Required",
        requestBody: {
          message: "string (required) - The user's message",
          sessionToken:
            "string (optional) - Session identifier for conversation continuity",
          metadata: "object (optional) - Additional metadata for the message",
        },
        responses: {
          "200": {
            description: "Successful response",
            schema: {
              response: "string - The chatbot's response",
              sessionToken: "string - Session identifier",
              sources: "array - Source documents used (if applicable)",
              responseTime: "number - Response time in milliseconds",
            },
          },
          "401": { description: "Invalid API key" },
          "403": { description: "Chatbot not active or domain not allowed" },
          "429": { description: "Rate limit exceeded" },
        },
        examples: {
          request: {
            message: "Hello, how can you help me?",
            sessionToken: "session_123",
            metadata: { source: "website" },
          },
          response: {
            response:
              "Hello! I'm here to help you with any questions you have.",
            sessionToken: "session_123",
            sources: [],
            responseTime: 1250,
          },
          streaming: {
            note: "When streaming is enabled (stream: true in query), responses are sent as Server-Sent Events (SSE)",
            events: [
              'data: {"type":"content","content":"Hello! I\'m","sessionToken":"session_123"}',
              'data: {"type":"content","content":" here to help","sessionToken":"session_123"}',
              'data: {"type":"sources","sources":[],"sessionToken":"session_123"}',
              'data: {"type":"done","sessionToken":"session_123","responseTime":1250}',
            ],
          },
        },
      },
      {
        method: "GET",
        path: "/chatbot/{chatbotId}/chat",
        description: "Retrieve chat history for a session",
        authentication: "Required",
        parameters: {
          sessionToken: "string (required) - Session identifier",
          limit: "number (optional) - Maximum number of messages to return",
          offset: "number (optional) - Number of messages to skip",
        },
        responses: {
          "200": {
            description: "Chat history retrieved successfully",
            schema: {
              messages: "array - List of messages in the session",
              sessionToken: "string - Session identifier",
              messagesCount: "number - Total number of messages",
            },
          },
        },
      },
      {
        method: "POST",
        path: "/chat/simple",
        description: "Simplified chat endpoint for basic integrations",
        authentication: "Required",
        requestBody: {
          chatbotId: "string (required) - The chatbot ID",
          message: "string (required) - The user's message",
          sessionId: "string (optional) - Session identifier",
          apiKey: "string (required) - Your API key",
          stream:
            "boolean (optional) - Enable streaming response (default: false)",
        },
        responses: {
          "200": {
            description: "Successful response",
            schema: {
              response: "string - The chatbot's response",
              sessionId: "string - Session identifier",
              sources: "array - Source documents used",
            },
          },
        },
      },
      {
        method: "GET",
        path: "/chatbot/{chatbotId}/config",
        description: "Get public chatbot configuration",
        authentication: "Required",
        parameters: {
          domain: "string (optional) - Domain for validation",
        },
        responses: {
          "200": {
            description: "Configuration retrieved successfully",
            schema: {
              id: "string - Chatbot ID",
              name: "string - Chatbot name",
              customization: "object - Theme and UI customization",
              features: "object - Available features and limits",
              rateLimits: "object - Rate limiting configuration",
            },
          },
        },
      },
      {
        method: "GET",
        path: "/chatbot/{chatbotId}/status",
        description: "Get chatbot status and health information",
        authentication: "Required",
        responses: {
          "200": {
            description: "Status retrieved successfully",
            schema: {
              id: "string - Chatbot ID",
              isActive: "boolean - Whether chatbot is active",
              status: "string - Current status (online/offline/maintenance)",
              uptime: "object - Uptime statistics",
              performance: "object - Performance metrics",
              limits: "object - Current rate limit usage",
            },
          },
        },
      },
      {
        method: "GET",
        path: "/chatbot/{chatbotId}/analytics",
        description: "Get chatbot analytics and usage statistics",
        authentication: "Required",
        parameters: {
          days: "number (optional) - Number of days to include (default: 30)",
          details: "boolean (optional) - Include detailed daily stats",
        },
        responses: {
          "200": {
            description: "Analytics retrieved successfully",
            schema: {
              totalSessions: "number - Total number of sessions",
              totalMessages: "number - Total number of messages",
              averageResponseTime: "number - Average response time",
              topDomains: "array - Most active domains",
              dailyStats: "array - Daily usage statistics (if details=true)",
            },
          },
        },
      },
      {
        method: "POST",
        path: "/chatbot/{chatbotId}/webhook",
        description: "Configure webhook for chatbot events",
        authentication: "Required",
        requestBody: {
          url: "string (required) - Webhook URL",
          secret: "string (optional) - Secret for signature verification",
          events: "array (required) - Events to subscribe to",
          active: "boolean (optional) - Whether webhook is active",
        },
        responses: {
          "200": { description: "Webhook configured successfully" },
        },
      },
      {
        method: "GET",
        path: "/chatbot/{chatbotId}/webhook",
        description: "Get current webhook configuration",
        authentication: "Required",
        responses: {
          "200": {
            description: "Webhook configuration retrieved",
            schema: {
              webhook:
                "object - Current webhook configuration (excluding secret)",
            },
          },
        },
      },
    ],
  };

  if (format === "html") {
    // Return HTML documentation
    const html = generateHTMLDocumentation(documentation);
    return new NextResponse(html, {
      headers: {
        "Content-Type": "text/html",
        "Access-Control-Allow-Origin": "*",
      },
    });
  }

  // Return JSON documentation
  return NextResponse.json(documentation, {
    headers: {
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "GET, OPTIONS",
      "Access-Control-Allow-Headers": "Content-Type",
    },
  });
}

function generateHTMLDocumentation(docs: APIDocumentation): string {
  return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${docs.title}</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #333; border-bottom: 3px solid #007bff; padding-bottom: 10px; }
        h2 { color: #555; margin-top: 30px; }
        h3 { color: #666; }
        .endpoint { background: #f8f9fa; padding: 20px; margin: 20px 0; border-radius: 6px; border-left: 4px solid #007bff; }
        .method { display: inline-block; padding: 4px 8px; border-radius: 4px; color: white; font-weight: bold; margin-right: 10px; }
        .method.GET { background: #28a745; }
        .method.POST { background: #007bff; }
        .method.PUT { background: #ffc107; color: #333; }
        .method.DELETE { background: #dc3545; }
        .path { font-family: monospace; background: #e9ecef; padding: 2px 6px; border-radius: 3px; }
        .auth-required { color: #dc3545; font-weight: bold; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 4px; overflow-x: auto; }
        code { background: #e9ecef; padding: 2px 4px; border-radius: 3px; font-family: monospace; }
        .example { background: #e7f3ff; padding: 15px; border-radius: 4px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>${docs.title}</h1>
        <p><strong>Version:</strong> ${docs.version}</p>
        <p><strong>Base URL:</strong> <code>${docs.baseUrl}</code></p>
        <p>${docs.description}</p>
        
        <h2>Authentication</h2>
        <p><strong>Type:</strong> ${docs.authentication.type}</p>
        <p>${docs.authentication.description}</p>
        <div class="example">
            <strong>Example:</strong><br>
            <code>${docs.authentication.example}</code>
        </div>
        
        <h2>Endpoints</h2>
        ${docs.endpoints
          .map(
            (endpoint) => `
            <div class="endpoint">
                <h3>
                    <span class="method ${endpoint.method}">${
                      endpoint.method
                    }</span>
                    <span class="path">${endpoint.path}</span>
                    ${
                      endpoint.authentication === "Required"
                        ? '<span class="auth-required">(Auth Required)</span>'
                        : ""
                    }
                </h3>
                <p>${endpoint.description}</p>
                
                ${
                  endpoint.parameters
                    ? `
                    <h4>Parameters</h4>
                    <ul>
                        ${Object.entries(endpoint.parameters)
                          .map(
                            ([key, value]) => `
                            <li><code>${key}</code>: ${value}</li>
                        `
                          )
                          .join("")}
                    </ul>
                `
                    : ""
                }
                
                ${
                  endpoint.requestBody
                    ? `
                    <h4>Request Body</h4>
                    <ul>
                        ${Object.entries(endpoint.requestBody)
                          .map(
                            ([key, value]) => `
                            <li><code>${key}</code>: ${value}</li>
                        `
                          )
                          .join("")}
                    </ul>
                `
                    : ""
                }
                
                <h4>Responses</h4>
                <ul>
                    ${Object.entries(endpoint.responses)
                      .map(
                        ([code, response]) => `
                        <li><strong>${code}:</strong> ${
                          typeof response === "object"
                            ? response.description
                            : response
                        }</li>
                    `
                      )
                      .join("")}
                </ul>
                
                ${
                  endpoint.examples
                    ? `
                    <h4>Example</h4>
                    <div class="example">
                        <strong>Request:</strong>
                        <pre>${JSON.stringify(
                          endpoint.examples.request,
                          null,
                          2
                        )}</pre>
                        <strong>Response:</strong>
                        <pre>${JSON.stringify(
                          endpoint.examples.response,
                          null,
                          2
                        )}</pre>
                    </div>
                `
                    : ""
                }
            </div>
        `
          )
          .join("")}
    </div>
</body>
</html>
  `;
}

export async function OPTIONS(req: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "GET, OPTIONS",
      "Access-Control-Allow-Headers": "Content-Type",
    },
  });
}
