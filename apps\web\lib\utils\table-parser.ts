/**
 * Utility functions for parsing and rendering markdown tables
 */

// Helper function to strip markdown formatting from text while preserving citations
const stripMarkdownFormatting = (text: string): string => {
  return text
    .replace(/\*\*(.*?)\*\*/g, "$1") // Bold
    .replace(/\*(.*?)\*/g, "$1") // Italic
    .replace(/`(.*?)`/g, "$1") // Code
    .replace(/\[(.*?)\]\(.*?\)/g, "$1") // Links (but not citations)
    .replace(/#{1,6}\s*/g, "") // Headers
    .trim();
};

// Helper function to strip markdown formatting but preserve citation markers
const stripMarkdownKeepCitations = (text: string): string => {
  return (
    text
      .replace(/\*\*(.*?)\*\*/g, "$1") // Bold
      .replace(/\*(.*?)\*/g, "$1") // Italic
      .replace(/`(.*?)`/g, "$1") // Code
      // Don't remove citation markers [D1], [W1], [1], etc.
      .replace(/\[([^\]]+)\]\([^)]+\)/g, (match, linkText) => {
        // Only remove markdown links, not citation markers
        if (/^(D|W)?\d+$/.test(linkText)) {
          return match; // Keep citation markers
        }
        return linkText; // Remove other links
      })
      .replace(/#{1,6}\s*/g, "") // Headers
      .trim()
  );
};

export interface ParsedTable {
  startIndex: number;
  endIndex: number;
  headers: string[];
  rows: string[][];
  alignments: string[];
}

/**
 * Parse markdown tables from content
 */
export const parseMarkdownTable = (content: string): ParsedTable[] => {
  const lines = content.split("\n");
  const tables: ParsedTable[] = [];

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim();
    if (line.includes("|") && line.split("|").length > 2) {
      // Potential table start
      const nextLine = lines[i + 1]?.trim();
      if (nextLine && nextLine.includes("|") && nextLine.includes("-")) {
        // This is a table header with separator
        const headers = line
          .split("|")
          .map((h) => stripMarkdownFormatting(h.trim()))
          .filter((h) => h);
        const separatorCells = nextLine
          .split("|")
          .map((s) => s.trim())
          .filter((s) => s);
        const alignments = separatorCells.map((cell) => {
          if (cell.startsWith(":") && cell.endsWith(":")) return "center";
          if (cell.endsWith(":")) return "right";
          return "left";
        });

        const rows: string[][] = [];
        let j = i + 2;

        // Parse table rows
        while (j < lines.length && lines[j].trim().includes("|")) {
          const rowCells = lines[j]
            .split("|")
            .map((c) => stripMarkdownKeepCitations(c.trim()))
            .filter((c) => c);
          if (rowCells.length > 0) {
            rows.push(rowCells);
          }
          j++;
        }

        if (rows.length > 0) {
          tables.push({
            startIndex: i,
            endIndex: j - 1,
            headers,
            rows,
            alignments,
          });
        }

        i = j - 1; // Skip processed lines
      }
    }
  }

  return tables;
};
