"use client";

import { Icon } from "@/components/icons";
import { DisconnectAccountModal } from "@/components/model/social-account-disconnect-modal";
import { But<PERSON> } from "@/components/ui/button";
import { useSocialAccountStore } from "@/lib/shared-store";
import { googleLogin, outlookLogin } from "@/services";
import { getCookie } from "@/utils/cookies";
import { useRouter, useSearchParams } from "next/navigation";
import { useLanguage } from "@/lib/language-context";
import toast from "react-hot-toast";

interface Integration {
  id: string;
  platform: string;
  accountId?: string;
}

interface IntegrationScreenProps {
  integration: Integration[];
}

export const IntegrationScreen = ({ integration }: IntegrationScreenProps) => {
  const router = useRouter();
  const searchParams = useSearchParams();

  const error = searchParams?.get("error");
  if (error) {
    console.log("error", error);
    toast.error(error);
  }
  const { t } = useLanguage();
  const connectMapping = {
    OUTLOOK: outlookLogin,
    GOOGLE: googleLogin,
  };
  const tenantId = getCookie("currentOrganizationId") ?? "";
  const metaData = [
    {
      key: "OUTLOOK",
      iconName: "OneDrive",
      description: t("integration.oneDriveDescription"),
      platform: "SharePoint",
      heading: "",
      subHeading: "",
      hasConnect: true,
      hasConfig: false,
      category: "calender",
    },
    {
      key: "GOOGLE",
      iconName: "GDrive",
      description: t("integration.googleDriveDescription"),
      platform: "Drive",
      heading: "",
      subHeading: "",
      hasConnect: true,
      hasConfig: false,
      category: "calender",
    },
  ];
  return (
    <div className="grid w-full grid-cols-1 gap-4 md:grid-cols-2 xl:grid-cols-3">
      {metaData?.map((item, index) => {
        return (
          <SocialMenu
            tenantId={tenantId}
            router={router}
            key={index}
            connect={connectMapping[item?.key]}
            item={item}
            socialAccount={integration?.find(
              (user) => user.platform === item?.key
            )}
          />
        );
      })}
      <DisconnectAccountModal />
    </div>
  );
};

interface SocialMenuItem {
  key: string;
  iconName: string;
  description: string;
  platform: string;
  heading: string;
  subHeading: string;
  hasConnect: boolean;
  hasConfig: boolean;
  category: string;
  isCommingSoon?: boolean;
}

interface SocialMenuProps {
  connect: Function;
  item: SocialMenuItem;
  socialAccount?: Integration;
  tenantId: string;
  router: ReturnType<typeof useRouter>;
}

export const SocialMenu = (props: SocialMenuProps) => {
  const { connect, item, socialAccount, tenantId, router } = props;
  const { t } = useLanguage();
  const userId = getCookie("userId") ?? "";

  const updateData = useSocialAccountStore(
    (state) => (state as any).disconnect
  );

  return (
    <div className="relative flex flex-col justify-between rounded-xl border p-4 text-left shadow-sm">
      <>
        {item?.hasConnect ? (
          <div className="absolute right-4 top-4 flex items-center rounded  px-2 py-1 text-xs bg-secondary">
            {socialAccount?.accountId ? (
              <span className="relative mr-1 flex h-2 w-2">
                <span className="animate-ping-slow absolute inline-flex h-full w-full rounded-full bg-green-500 opacity-75"></span>
                <span className="relative inline-flex h-2 w-2 rounded-full bg-green-500"></span>
              </span>
            ) : (
              <span className="relative mr-1 flex h-2 w-2">
                <span className="relative inline-flex h-2 w-2 rounded-full bg-destructive"></span>
              </span>
            )}
            {socialAccount?.accountId
              ? t("integration.connected")
              : t("integration.notConnected")}
          </div>
        ) : null}
        <div className="flex justify-start">
          <Icon name={item.iconName} className="mb-6 h-8 w-8" />
        </div>
        <h3 className="text-lg font-bold ">{item?.platform}</h3>
        <p className="text-xs ">{item?.description}</p>
      </>
      <div className="mt-4 flex space-x-2">
        {item?.hasConnect ? (
          <>
            {item?.isCommingSoon ? (
              <Button
                disabled
                className="rounded-md bg-primary px-4 py-2 text-sm font-normal text-white"
              >
                {t("integration.comingSoon")}
              </Button>
            ) : !socialAccount?.accountId ? (
              <Button
                onClick={() => connect({ tenantId, userId })}
                className="rounded-md bg-primary px-4 py-2 text-sm font-normal text-white"
              >
                {t("integration.connect")}
              </Button>
            ) : (
              <div className="flex gap-4">
                <Button
                  variant="destructive"
                  size="sm"
                  className="flex w-full items-center justify-start space-x-2 rounded-md text-left text-sm transition-all duration-75"
                  onClick={() =>
                    updateData(true, {
                      id: socialAccount?.id,
                      platform: item?.platform,
                    })
                  }
                >
                  {t("integration.disconnect")}
                </Button>
                {item?.hasConfig && (
                  <Button
                    onClick={() =>
                      router.push(
                        `/settings/integration/${item?.platform?.toLowerCase()}`
                      )
                    }
                    variant="outline"
                    size="sm"
                  >
                    {t("integration.configure")}
                  </Button>
                )}
              </div>
            )}
          </>
        ) : (
          <>
            <Button
              onClick={() =>
                router.push(
                  `/settings/integration/${item?.platform?.toLowerCase()}`
                )
              }
              size="sm"
            >
              {t("integration.manage")}
            </Button>
          </>
        )}
      </div>
    </div>
  );
};
