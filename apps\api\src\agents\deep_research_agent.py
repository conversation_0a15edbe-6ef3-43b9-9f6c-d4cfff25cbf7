"""
Deep Research Agent for iterative, comprehensive research and analysis.

This agent performs multi-round research, iteratively improving and expanding
answers until they reach a comprehensive, accurate, and complete state.
"""

import asyncio
import logging
import time
from typing import Dict, List, Any, Optional, AsyncGenerator, Tu<PERSON>
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

# Translation dictionary for localized messages
TRANSLATIONS = {
    "en": {
        "analyzing_query": "🔍 Analyzing query and creating comprehensive research plan...",
        "research_plan_created": "📋 Research plan created: {count} subtopics identified",
        "research_iteration": "🔬 Research iteration {current}/{total}",
        "iteration_complete": "✅ Iteration {iteration} complete (confidence: {confidence:.1%})",
        "confidence_achieved": "🎯 Confidence threshold reached ({confidence:.1%})",
        "synthesizing_answer": "🧠 Synthesizing comprehensive answer...",
        "research_complete": "✅ Deep research complete - comprehensive analysis ready",
        "error_occurred": "An error occurred during deep research",
        "no_server_connected": "No MCP server connected",
        "query_processing_failed": "Query processing failed",
        "tool_execution": "Executing tool: {tool_name}",
        "tool_error": "Error executing tool: {tool_name}",
        "final_response_failed": "Final response failed"
    },
    "de": {
        "analyzing_query": "🔍 Analysiere Anfrage und erstelle umfassenden Recherche-Plan...",
        "research_plan_created": "📋 Recherche-Plan erstellt: {count} Unterthemen identifiziert",
        "research_iteration": "🔬 Recherche-Iteration {current}/{total}",
        "iteration_complete": "✅ Iteration {iteration} abgeschlossen (Vertrauen: {confidence:.1%})",
        "confidence_achieved": "🎯 Vertrauensschwelle erreicht ({confidence:.1%})",
        "synthesizing_answer": "🧠 Synthese umfassender Antwort...",
        "research_complete": "✅ Tiefenforschung abgeschlossen - umfassende Analyse bereit",
        "error_occurred": "Ein Fehler ist während der Tiefenforschung aufgetreten",
        "no_server_connected": "Kein MCP-Server verbunden",
        "query_processing_failed": "Anfrageverarbeitung fehlgeschlagen",
        "tool_execution": "Tool wird ausgeführt: {tool_name}",
        "tool_error": "Fehler bei Tool-Ausführung: {tool_name}",
        "final_response_failed": "Finale Antwort fehlgeschlagen"
    }
}

def get_translation(key: str, language: str = "en", **kwargs) -> str:
    """Get translated message with optional formatting parameters."""
    try:
        message = TRANSLATIONS.get(language, TRANSLATIONS["en"]).get(key, key)
        return message.format(**kwargs) if kwargs else message
    except (KeyError, ValueError):
        return TRANSLATIONS["en"].get(key, key)


class ResearchPhase(Enum):
    """Research phases for the deep research process."""
    INITIAL_ANALYSIS = "initial_analysis"
    INFORMATION_GATHERING = "information_gathering"
    SOURCE_VALIDATION = "source_validation"
    SYNTHESIS = "synthesis"
    VALIDATION = "validation"
    REFINEMENT = "refinement"
    COMPLETION = "completion"


@dataclass
class ResearchIteration:
    """Represents a single research iteration."""
    iteration_number: int
    phase: ResearchPhase
    query: str
    findings: str
    sources: List[Dict[str, Any]]
    confidence_score: float
    gaps_identified: List[str]
    next_actions: List[str]
    timestamp: float


@dataclass
class ResearchPlan:
    """Research plan for comprehensive investigation."""
    main_topic: str
    subtopics: List[str]
    research_questions: List[str]
    information_sources: List[str]
    success_criteria: List[str]
    max_iterations: int = 5


class DeepResearchAgent:
    """
    Advanced research agent that performs iterative, comprehensive research.
    
    This agent mimics the research process shown in ChatGPT's example,
    performing multiple rounds of investigation, analysis, and refinement
    until achieving a complete and accurate answer.
    """
    
    def __init__(self, max_iterations: int = 7, confidence_threshold: float = 0.95):
        self.max_iterations = max_iterations
        self.confidence_threshold = confidence_threshold
        self.iterations: List[ResearchIteration] = []
        self.research_plan: Optional[ResearchPlan] = None
        
    async def conduct_deep_research(
        self,
        query: str,
        user_id: str,
        tenant_id: str,
        workspace_ids: List[str],
        search_tools: Dict[str, Any],
        stream: bool = True,
        language: str = "en"
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """
        Conduct comprehensive deep research with iterative improvement.
        
        Args:
            query: The research question/topic
            user_id: User identifier
            tenant_id: Tenant identifier
            workspace_ids: Available workspace IDs
            search_tools: Available search and analysis tools
            stream: Whether to stream progress updates
            
        Yields:
            Research progress updates and final comprehensive answer
        """
        start_time = time.time()
        
        try:
            # Phase 1: Initial Analysis and Planning
            if stream:
                yield {
                    "phase": "planning",
                    "message": get_translation("analyzing_query", language),
                    "progress": 0.1
                }
            
            self.research_plan = await self._create_research_plan(query)
            
            if stream:
                yield {
                    "phase": "planning_complete",
                    "message": get_translation("research_plan_created", language, count=len(self.research_plan.subtopics)),
                    "research_plan": {
                        "main_topic": self.research_plan.main_topic,
                        "subtopics": self.research_plan.subtopics,
                        "research_questions": self.research_plan.research_questions
                    },
                    "progress": 0.2
                }
            
            # Phase 2: Iterative Research Process
            iteration_count = 0
            current_confidence = 0.0
            comprehensive_answer = ""
            all_sources = []
            
            while (iteration_count < self.max_iterations and 
                   current_confidence < self.confidence_threshold):
                
                iteration_count += 1
                
                if stream:
                    yield {
                        "phase": "research_iteration",
                        "message": get_translation("research_iteration", language, current=iteration_count, total=self.max_iterations),
                        "iteration": iteration_count,
                        "progress": 0.2 + (iteration_count / self.max_iterations) * 0.65
                    }
                
                # Conduct research iteration
                iteration_result = await self._conduct_research_iteration(
                    iteration_count,
                    query,
                    user_id,
                    tenant_id,
                    workspace_ids,
                    search_tools
                )
                
                self.iterations.append(iteration_result)
                current_confidence = iteration_result.confidence_score
                
                if stream:
                    yield {
                        "phase": "iteration_complete",
                        "message": get_translation("iteration_complete", language, iteration=iteration_count, confidence=current_confidence),
                        "iteration": iteration_count,
                        "confidence": current_confidence,
                        "findings": iteration_result.findings[:200] + "..." if len(iteration_result.findings) > 200 else iteration_result.findings,
                        "gaps_identified": iteration_result.gaps_identified,
                        "progress": 0.2 + (iteration_count / self.max_iterations) * 0.65
                    }
                
                # Accumulate sources
                iteration_sources = iteration_result.sources
                logger.info(f"Iteration {iteration_count} returned {len(iteration_sources)} sources")
                all_sources.extend(iteration_sources)
                
                # Check if we should continue
                if current_confidence >= self.confidence_threshold:
                    if stream:
                        yield {
                            "phase": "confidence_achieved",
                            "message": get_translation("confidence_achieved", language, confidence=current_confidence),
                            "progress": 0.85
                        }
                    break
                
                # Brief pause between iterations
                await asyncio.sleep(0.5)
            
            # Phase 3: Final Synthesis
            if stream:
                yield {
                    "phase": "synthesis",
                    "message": get_translation("synthesizing_answer", language),
                    "progress": 0.90
                }
            
            comprehensive_answer, thinking_process = await self._synthesize_final_answer(
                query, self.iterations, search_tools
            )

            # Validate synthesis result
            logger.info(f"Synthesis completed - Answer: {len(comprehensive_answer) if comprehensive_answer else 0} chars, Thinking: {len(thinking_process) if thinking_process else 0} chars")

            # Phase 4: Quality Assessment
            quality_score = await self._assess_answer_quality(comprehensive_answer, query)

            elapsed_time = time.time() - start_time

            # Debug logging before final result
            logger.info(f"Creating final result - Answer length: {len(comprehensive_answer) if comprehensive_answer else 0}")
            logger.info(f"Answer preview: {comprehensive_answer[:100] if comprehensive_answer else 'None'}...")
            logger.info(f"Thinking length: {len(thinking_process) if thinking_process else 0}")
            logger.info(f"Sources count: {len(all_sources)}, Iterations count: {len(self.iterations)}")
            logger.info(f"Research plan subtopics: {len(self.research_plan.subtopics) if self.research_plan else 0}")

            # Ensure we have valid data
            final_answer = comprehensive_answer if comprehensive_answer else ""
            final_thinking = thinking_process if thinking_process else ""

            # Deduplicate sources to prevent duplicate citations
            final_sources = self._deduplicate_sources(all_sources if all_sources else [])

            # Debug source information
            logger.info(f"Sources before deduplication: {len(all_sources) if all_sources else 0}")
            logger.info(f"Final sources after deduplication: {len(final_sources)} total sources")
            doc_sources = [s for s in final_sources if s.get('metadata', {}).get('source_type') == 'document']
            web_sources = [s for s in final_sources if s.get('metadata', {}).get('source_type') == 'web_search']
            logger.info(f"Source breakdown: {len(doc_sources)} document sources, {len(web_sources)} web sources")
            final_iterations = [
                {
                    "iteration": i.iteration_number,
                    "phase": i.phase.value,
                    "confidence": i.confidence_score,
                    "gaps_identified": i.gaps_identified,
                    "findings_preview": i.findings[:100] + "..." if len(i.findings) > 100 else i.findings
                }
                for i in self.iterations
            ] if self.iterations else []

            logger.info(f"Final data prepared - Answer: {len(final_answer)}, Sources: {len(final_sources)}, Iterations: {len(final_iterations)}")

            # Final result
            final_result = {
                "phase": "complete",
                "message": get_translation("research_complete", language),
                "progress": 1.0,
                "answer": final_answer,
                "thinking": final_thinking,
                "research_summary": {
                    "iterations_conducted": iteration_count,
                    "final_confidence": current_confidence,
                    "quality_score": quality_score,
                    "subtopics_covered": len(self.research_plan.subtopics) if self.research_plan else 0,
                    "sources_consulted": len(final_sources)
                },
                "sources": final_sources,
                "iterations": final_iterations,
                "elapsed_time": elapsed_time,
                "status": "complete"
            }

            logger.info(f"Final result created - Answer: {len(final_result.get('answer', ''))}, Sources: {len(final_result.get('sources', []))}, Iterations: {len(final_result.get('iterations', []))}")

            # Yield final comprehensive result
            logger.info(f"Deep research complete - yielding final result with {len(final_result.get('answer', ''))} char answer")
            yield final_result
                
        except Exception as e:
            logger.error(f"Error in deep research: {e}")
            yield {
                "phase": "error",
                "error": str(e),
                "status": "failed"
            }
    
    async def _create_research_plan(self, query: str) -> ResearchPlan:
        """Create a comprehensive research plan for the given query."""
        # This would use an LLM to analyze the query and create a research plan
        # For now, we'll create a basic plan structure
        
        # Analyze query to identify main topic and subtopics
        main_topic = query.strip()
        
        # Generate subtopics (this would be done by LLM in real implementation)
        subtopics = [
            f"Definition and overview of {main_topic}",
            f"History and evolution of {main_topic}",
            f"Technical specifications of {main_topic}",
            f"Use cases and applications of {main_topic}",
            f"Tools and libraries for {main_topic}",
            f"Security considerations for {main_topic}",
            f"Future trends and developments in {main_topic}"
        ]
        
        research_questions = [
            f"What is {main_topic} and how is it defined?",
            f"What is the history and evolution of {main_topic}?",
            f"What are the technical details and specifications?",
            f"How is {main_topic} used in practice?",
            f"What tools and resources are available?",
            f"What are the security implications?",
            f"What are current trends and future directions?"
        ]
        
        return ResearchPlan(
            main_topic=main_topic,
            subtopics=subtopics,
            research_questions=research_questions,
            information_sources=["documents", "web", "technical_specs"],
            success_criteria=[
                "Comprehensive coverage of all subtopics",
                "Accurate technical information",
                "Multiple reliable sources",
                "Clear and well-structured presentation"
            ],
            max_iterations=self.max_iterations
        )

    async def _conduct_research_iteration(
        self,
        iteration_number: int,
        query: str,
        user_id: str,
        tenant_id: str,
        workspace_ids: List[str],
        search_tools: Dict[str, Any]
    ) -> ResearchIteration:
        """Conduct a single research iteration."""

        # Determine current phase based on iteration
        if iteration_number == 1:
            phase = ResearchPhase.INITIAL_ANALYSIS
        elif iteration_number <= 4:
            phase = ResearchPhase.INFORMATION_GATHERING
        elif iteration_number == 5:
            phase = ResearchPhase.SOURCE_VALIDATION
        elif iteration_number == 6:
            phase = ResearchPhase.SYNTHESIS
        else:
            phase = ResearchPhase.REFINEMENT

        # Identify what to research in this iteration
        research_focus = await self._determine_research_focus(iteration_number, query)

        # Conduct searches and gather information
        findings, sources = await self._gather_information(
            research_focus, user_id, tenant_id, workspace_ids, search_tools
        )

        # Analyze findings and identify gaps
        gaps_identified = await self._identify_knowledge_gaps(findings, query, iteration_number)

        # Determine next actions
        next_actions = await self._plan_next_actions(gaps_identified, iteration_number)

        # Calculate confidence score
        confidence_score = await self._calculate_confidence_score(findings, gaps_identified, iteration_number)

        return ResearchIteration(
            iteration_number=iteration_number,
            phase=phase,
            query=research_focus,
            findings=findings,
            sources=sources,
            confidence_score=confidence_score,
            gaps_identified=gaps_identified,
            next_actions=next_actions,
            timestamp=time.time()
        )

    async def _determine_research_focus(self, iteration_number: int, original_query: str) -> str:
        """Determine what to focus on in this research iteration."""

        if iteration_number == 1:
            return f"Comprehensive overview, definition, and basic technical specifications of {original_query}"
        elif iteration_number == 2:
            # Focus on gaps from previous iteration
            if self.iterations:
                gaps = self.iterations[-1].gaps_identified
                if gaps:
                    return f"Deep dive into {gaps[0]} related to {original_query}"
            return f"Detailed technical specifications, standards, file formats, and implementation details for {original_query}"
        elif iteration_number == 3:
            return f"Practical applications, real-world usage, tools, software support, and industry adoption of {original_query}"
        elif iteration_number == 4:
            return f"Advanced features, security considerations, best practices, and integration methods for {original_query}"
        elif iteration_number == 5:
            return f"Source validation and relevance verification for all gathered information about {original_query}"
        elif iteration_number == 6:
            return f"Examples, code implementations, step-by-step guides, and practical tutorials for {original_query}"
        else:
            return f"Latest developments, future outlook, market trends, and comprehensive ecosystem analysis of {original_query}"

    async def _gather_information(
        self,
        research_focus: str,
        user_id: str,
        tenant_id: str,
        workspace_ids: List[str],
        search_tools: Dict[str, Any]
    ) -> Tuple[str, List[Dict[str, Any]]]:
        """Gather information for the current research focus."""

        findings = []
        sources = []

        # Special handling for source validation iteration
        if "source validation" in research_focus.lower() or "relevance verification" in research_focus.lower():
            return await self._validate_sources_and_relevance(research_focus, user_id, tenant_id, workspace_ids, search_tools)

        # Document search
        doc_findings_relevant = False
        if "document_search" in search_tools:
            doc_results = await search_tools["document_search"](
                research_focus, user_id, tenant_id, workspace_ids
            )
            if doc_results:
                doc_summary = doc_results.get('summary', '')
                # Check if document results are relevant to the query
                if self._is_content_relevant(doc_summary, research_focus):
                    findings.append(f"Document Analysis: {doc_summary}")
                    doc_sources = doc_results.get('sources', [])
                    logger.info(f"Document search returned {len(doc_sources)} sources for query: {research_focus[:50]}...")
                    sources.extend(doc_sources)
                    doc_findings_relevant = True
                else:
                    findings.append(f"Document Analysis: No sufficiently relevant documents found.")

        # Web search
        web_findings_added = False
        if "web_search" in search_tools:
            web_results = await search_tools["web_search"](research_focus)
            if web_results:
                web_summary = web_results.get('summary', '')
                if web_summary and not web_summary.startswith("Error"):
                    findings.append(f"Web Research: {web_summary}")
                    sources.extend(web_results.get('sources', []))
                    web_findings_added = True

        # If no relevant document findings but we have web results, prioritize web content
        if not doc_findings_relevant and web_findings_added:
            # Remove the "no relevant documents" message and focus on web results
            findings = [f for f in findings if not f.startswith("Document Analysis: No sufficiently")]
            if not any(f.startswith("Document Analysis:") for f in findings):
                findings.insert(0, f"Document Analysis: No relevant documents found in workspace. Focusing on web research.")

        # Combine findings
        combined_findings = "\n\n".join(findings) if findings else f"Research conducted on: {research_focus}"

        return combined_findings, sources

    async def _validate_sources_and_relevance(
        self,
        research_focus: str,
        user_id: str,
        tenant_id: str,
        workspace_ids: List[str],
        search_tools: Dict[str, Any]
    ) -> Tuple[str, List[Dict[str, Any]]]:
        """Validate sources and assess relevance of gathered information."""

        validation_findings = []
        validated_sources = []

        # Extract original query from research focus
        original_query = research_focus.split("about ")[-1] if "about " in research_focus else research_focus

        # Analyze all previous iterations for source quality and relevance
        total_sources = 0
        relevant_sources = 0
        high_quality_sources = 0

        for iteration in self.iterations:
            for source in iteration.sources:
                total_sources += 1

                # Check source relevance
                source_content = source.get('content', '')
                source_title = source.get('title', '')
                source_url = source.get('url', '')

                # Assess relevance
                is_relevant = self._assess_source_relevance(source_content, source_title, original_query)
                if is_relevant:
                    relevant_sources += 1
                    validated_sources.append(source)

                # Assess quality
                is_high_quality = self._assess_source_quality(source_url, source_title, source_content)
                if is_high_quality:
                    high_quality_sources += 1

        # Calculate validation metrics
        relevance_ratio = relevant_sources / total_sources if total_sources > 0 else 0
        quality_ratio = high_quality_sources / total_sources if total_sources > 0 else 0

        # Create validation report
        validation_findings.append(f"Source Validation Analysis:")
        validation_findings.append(f"- Total sources analyzed: {total_sources}")
        validation_findings.append(f"- Relevant sources: {relevant_sources} ({relevance_ratio:.1%})")
        validation_findings.append(f"- High-quality sources: {high_quality_sources} ({quality_ratio:.1%})")

        # Assess information consistency
        consistency_score = self._assess_information_consistency()
        validation_findings.append(f"- Information consistency score: {consistency_score:.1%}")

        # Identify potential issues
        issues = []
        if relevance_ratio < 0.7:
            issues.append("Low source relevance detected - some sources may be off-topic")
        if quality_ratio < 0.5:
            issues.append("Source quality concerns - mix of authoritative and questionable sources")
        if consistency_score < 0.8:
            issues.append("Information consistency issues - conflicting data found")

        if issues:
            validation_findings.append(f"\nValidation Concerns:")
            for issue in issues:
                validation_findings.append(f"- {issue}")
        else:
            validation_findings.append(f"\nValidation Result: Sources are highly relevant and reliable for comprehensive analysis.")

        # Provide recommendations
        validation_findings.append(f"\nRecommendations:")
        if relevance_ratio >= 0.8:
            validation_findings.append("- Proceed with synthesis using validated sources")
        else:
            validation_findings.append("- Focus on most relevant sources during synthesis")
            validation_findings.append("- Consider additional targeted research if needed")

        combined_validation = "\n".join(validation_findings)

        # Return validation findings but keep ALL original sources
        # Don't filter out sources during validation - just add validation metadata
        all_original_sources = []
        for iteration in self.iterations:
            for source in iteration.sources:
                # Add validation metadata to each source
                source_with_validation = source.copy()
                source_content = source.get('content', '')
                source_title = source.get('title', '')

                # Add validation metadata
                is_relevant = self._assess_source_relevance(source_content, source_title, original_query)
                is_high_quality = self._assess_source_quality(source.get('url', ''), source_title, source_content)

                source_with_validation['validation'] = {
                    'is_relevant': is_relevant,
                    'is_high_quality': is_high_quality,
                    'validated_in_iteration': 5
                }

                all_original_sources.append(source_with_validation)

        return combined_validation, all_original_sources

    def _assess_source_relevance(self, content: str, title: str, query: str) -> bool:
        """Assess if a source is relevant to the research query."""
        if not content and not title:
            return False

        # Combine title and content for analysis
        full_text = f"{title} {content}".lower()
        query_lower = query.lower()

        # Extract key terms from query
        query_terms = set(query_lower.split())
        text_words = set(full_text.split())

        # Calculate relevance score
        overlap = len(query_terms.intersection(text_words))
        overlap_ratio = overlap / len(query_terms) if query_terms else 0

        # Check for key terms presence
        key_term_matches = sum(1 for term in query_terms if len(term) > 3 and term in full_text)

        # Consider relevant if good overlap or key term matches
        return overlap_ratio > 0.4 or key_term_matches >= 2

    def _assess_source_quality(self, url: str, title: str, content: str) -> bool:
        """Assess the quality and credibility of a source."""
        quality_score = 0

        # URL-based quality indicators
        if url:
            high_quality_domains = [
                'wikipedia.org', 'rfc-editor.org', 'ietf.org', 'w3.org',
                'mozilla.org', 'microsoft.com', 'apple.com', 'google.com',
                'github.com', 'stackoverflow.com', '.edu', '.gov'
            ]
            if any(domain in url.lower() for domain in high_quality_domains):
                quality_score += 0.4

        # Title-based quality indicators
        if title:
            quality_indicators = [
                'specification', 'standard', 'rfc', 'documentation',
                'official', 'guide', 'tutorial', 'reference'
            ]
            if any(indicator in title.lower() for indicator in quality_indicators):
                quality_score += 0.3

        # Content-based quality indicators
        if content:
            if len(content) > 200:  # Substantial content
                quality_score += 0.2
            if any(term in content.lower() for term in ['rfc', 'standard', 'specification']):
                quality_score += 0.1

        return quality_score >= 0.5

    def _assess_information_consistency(self) -> float:
        """Assess consistency of information across iterations."""
        if len(self.iterations) < 2:
            return 1.0

        # Simple consistency check based on common terms
        all_findings = [iter.findings.lower() for iter in self.iterations]

        # Count common technical terms across iterations
        common_terms = set()
        for findings in all_findings:
            words = findings.split()
            technical_words = [w for w in words if len(w) > 5]
            common_terms.update(technical_words)

        # Calculate consistency based on term overlap
        consistency_scores = []
        for i in range(len(all_findings) - 1):
            current_words = set(all_findings[i].split())
            next_words = set(all_findings[i + 1].split())
            overlap = len(current_words.intersection(next_words))
            total = len(current_words.union(next_words))
            if total > 0:
                consistency_scores.append(overlap / total)

        return sum(consistency_scores) / len(consistency_scores) if consistency_scores else 1.0

    def _is_content_relevant(self, content: str, query: str) -> bool:
        """Check if content is relevant to the research query."""
        if not content or not query:
            return False

        # Convert to lowercase for comparison
        content_lower = content.lower()
        query_lower = query.lower()

        # Extract key terms from query
        query_terms = set(query_lower.split())

        # Check for direct term matches
        content_words = set(content_lower.split())
        overlap = len(query_terms.intersection(content_words))
        overlap_ratio = overlap / len(query_terms) if query_terms else 0

        # Consider relevant if:
        # 1. High term overlap (>40%)
        # 2. Contains key query terms
        # 3. Doesn't contain obvious irrelevant indicators

        if overlap_ratio > 0.4:
            return True

        # Check for key terms in content
        for term in query_terms:
            if len(term) > 3 and term in content_lower:
                return True

        # Check for irrelevant content indicators
        irrelevant_indicators = [
            "machine learning", "artificial intelligence", "neural network",
            "deep learning", "data science", "algorithm", "model training",
            "no relevant", "no documents found", "error", "failed"
        ]

        for indicator in irrelevant_indicators:
            if indicator in content_lower:
                return False

        # Default to relevant if no clear irrelevance detected
        return len(content.strip()) > 50  # Has substantial content

    async def _identify_knowledge_gaps(
        self, findings: str, original_query: str, iteration_number: int
    ) -> List[str]:
        """Identify gaps in current knowledge that need further research."""

        # This would use an LLM to analyze findings and identify gaps
        # For now, we'll simulate gap identification based on iteration

        gaps = []

        if iteration_number == 1:
            gaps = [
                "Technical specifications and standards need comprehensive detail",
                "File format structure and syntax require explanation",
                "Practical implementation examples are missing",
                "Historical context and evolution need coverage"
            ]
        elif iteration_number == 2:
            gaps = [
                "Security considerations and privacy implications need investigation",
                "Tool ecosystem and software support require comprehensive mapping",
                "Performance characteristics and limitations unclear",
                "Integration methods and APIs need documentation"
            ]
        elif iteration_number == 3:
            gaps = [
                "Advanced features and capabilities not fully covered",
                "Industry adoption patterns and market trends unclear",
                "Comparison with alternatives and competing standards needed",
                "Real-world use cases and success stories missing"
            ]
        elif iteration_number == 4:
            gaps = [
                "Code examples and implementation tutorials needed",
                "Best practices and recommended approaches unclear",
                "Troubleshooting and common issues not addressed",
                "Platform-specific considerations missing"
            ]
        elif iteration_number == 5:
            gaps = [
                "Source relevance and credibility need validation",
                "Information accuracy and consistency require verification",
                "Outdated or conflicting information needs filtering",
                "Primary vs secondary source quality assessment needed"
            ]
        else:
            gaps = [
                "Latest developments and future roadmap need investigation",
                "Edge cases and limitations need comprehensive clarification",
                "Vendor support and commercial implementations unclear",
                "Compliance and regulatory aspects need coverage"
            ]

        # Filter gaps based on findings length (simulate LLM analysis)
        if len(findings) > 1000:  # Good amount of information
            gaps = gaps[:2]  # Fewer gaps
        elif len(findings) > 500:
            gaps = gaps[:3]

        return gaps

    async def _plan_next_actions(self, gaps: List[str], iteration_number: int) -> List[str]:
        """Plan next actions based on identified gaps."""

        actions = []

        for gap in gaps[:2]:  # Focus on top 2 gaps
            if "technical" in gap.lower():
                actions.append("Search for technical documentation and specifications")
            elif "security" in gap.lower():
                actions.append("Investigate security best practices and vulnerabilities")
            elif "tool" in gap.lower():
                actions.append("Research available tools and libraries")
            elif "example" in gap.lower():
                actions.append("Find practical examples and use cases")
            else:
                actions.append(f"Research: {gap}")

        if iteration_number >= self.max_iterations - 1:
            actions.append("Prepare final synthesis and validation")

        return actions

    async def _calculate_confidence_score(
        self, findings: str, gaps: List[str], iteration_number: int
    ) -> float:
        """Calculate confidence score for current research state."""

        # Ensure we don't reach threshold until after source validation (iteration 5)
        if iteration_number <= 5:
            # More conservative scoring to ensure all iterations complete
            base_score = 0.2 + (iteration_number * 0.12)  # Slower increase
        else:
            # Allow higher confidence after source validation
            base_score = 0.3 + (iteration_number * 0.15)

        # Adjust based on findings quality
        if len(findings) > 2000:
            base_score += 0.15  # Reduced bonus
        elif len(findings) > 1000:
            base_score += 0.08  # Reduced bonus

        # Adjust based on gaps
        gap_penalty = len(gaps) * 0.04  # Reduced penalty
        base_score -= gap_penalty

        # Ensure we don't exceed threshold until iteration 6+
        if iteration_number <= 5:
            max_score = 0.88  # Keep below 0.95 threshold
        else:
            max_score = 0.98

        # Cap at reasonable values
        return min(max(base_score, 0.1), max_score)

    async def _synthesize_final_answer(
        self,
        original_query: str,
        iterations: List[ResearchIteration],
        search_tools: Dict[str, Any]
    ) -> Tuple[str, str]:
        """Synthesize a comprehensive final answer from all research iterations."""

        # Collect all findings
        all_findings = []
        for iteration in iterations:
            all_findings.append(f"Iteration {iteration.iteration_number} ({iteration.phase.value}):\n{iteration.findings}")

        combined_research = "\n\n" + "="*50 + "\n\n".join(all_findings)

        # Filter and prioritize research findings for better synthesis
        filtered_research = self._filter_research_for_synthesis(combined_research, original_query)

        # Check if we have good web research results
        has_web_results = "Web Research:" in filtered_research and not "No web search results" in filtered_research
        has_relevant_docs = "Document Analysis:" in filtered_research and not "No sufficiently relevant documents" in filtered_research

        # Extract key information from web results for better synthesis
        key_findings = self._extract_key_findings(filtered_research, original_query)

        # Create comprehensive synthesis prompt with explicit instructions for detailed response
        synthesis_prompt = f"""
You are an expert researcher providing an EXTREMELY COMPREHENSIVE and DETAILED answer about: "{original_query}"

KEY FINDINGS FROM RESEARCH:
{key_findings}

TASK: Create the most detailed, authoritative, and comprehensive response possible using ALL the information found in the research above. Extract EVERY piece of useful information from the sources.

STRUCTURE YOUR RESPONSE AS FOLLOWS:
# {original_query.title()} - Comprehensive Analysis

## 1. Overview and Definition
- Provide detailed definition and explanation
- Include all alternative names and terminology
- Explain the purpose and significance
- Cover historical background if mentioned

## 2. Technical Specifications and Standards
- Detail ALL technical specifications found
- Include specific standards (RFC numbers, versions, etc.)
- Explain file formats, extensions, and structure
- Cover encoding, character sets, and data formats
- Include version information and compatibility details

## 3. File Format and Structure
- Explain the internal structure and syntax
- Detail required and optional fields
- Provide examples of format structure
- Explain data organization and hierarchy

## 4. Practical Applications and Use Cases
- List ALL practical applications mentioned
- Explain real-world usage scenarios
- Detail integration with other systems
- Cover platform and software compatibility
- Include industry adoption and usage patterns

## 5. Tools, Software, and Implementation
- List ALL tools and software that support this
- Detail implementation methods and approaches
- Cover programming libraries and APIs
- Explain integration procedures
- Include platform-specific considerations

## 6. Security, Privacy, and Best Practices
- Detail ALL security considerations found
- Explain privacy implications
- List best practices and recommendations
- Cover potential vulnerabilities or concerns
- Include compliance and regulatory aspects

## 7. Advanced Topics and Features
- Cover advanced features and capabilities
- Explain extensions and customizations
- Detail interoperability with other formats
- Include performance considerations
- Cover scalability and limitations

## 8. Industry Standards and Ecosystem
- Detail related standards and specifications
- Explain ecosystem and related technologies
- Cover vendor support and implementations
- Include market adoption and trends

## 9. Examples and Practical Implementation
- Provide concrete examples where possible
- Detail step-by-step implementation guidance
- Include code snippets or format examples if mentioned
- Cover common use cases and scenarios

## 10. Future Outlook and Development
- Include information about future developments
- Cover ongoing standardization efforts
- Detail planned improvements or changes
- Explain market trends and adoption patterns

CRITICAL INSTRUCTIONS:
- Extract EVERY piece of information from the research sources
- Be extremely detailed and comprehensive
- Use ALL available information, no matter how technical
- Include specific details like version numbers, RFC numbers, standards
- Expand on every point with as much detail as possible
- If sources mention specific examples, include them
- Make each section as detailed as the research allows
- Do NOT summarize - provide full details
- Aim for maximum information density and comprehensiveness
- If research is limited in some areas, focus heavily on areas with good information
"""

        # Use Azure DeepSeek R1 for final synthesis
        if "deepseek_synthesis" in search_tools:
            try:
                logger.info(f"Starting DeepSeek synthesis for query: {original_query}")
                answer, thinking = await search_tools["deepseek_synthesis"](synthesis_prompt)
                logger.info(f"DeepSeek synthesis completed. Answer length: {len(answer) if answer else 0}")

                # Validate synthesis result
                if not answer or len(answer.strip()) < 50:
                    logger.warning("DeepSeek synthesis returned empty or very short answer, using fallback")
                    answer = await self._create_fallback_synthesis(original_query, iterations)
                    thinking = f"Fallback synthesis used due to DeepSeek synthesis issues. Synthesized from {len(iterations)} research iterations."

            except Exception as e:
                logger.error(f"Error in DeepSeek synthesis: {e}")
                answer = await self._create_fallback_synthesis(original_query, iterations)
                thinking = f"Fallback synthesis used due to error: {str(e)}. Synthesized from {len(iterations)} research iterations."
        else:
            # Fallback synthesis
            logger.info("Using fallback synthesis (DeepSeek not available)")
            answer = await self._create_fallback_synthesis(original_query, iterations)
            thinking = f"Synthesized comprehensive answer from {len(iterations)} research iterations covering multiple aspects of {original_query}."

        # Final validation
        if not answer:
            logger.error("Final answer is empty, creating emergency fallback")
            answer = f"# Research Analysis: {original_query}\n\nBased on {len(iterations)} research iterations, comprehensive analysis was conducted. Please check the research iterations for detailed findings."

        logger.info(f"Final synthesis result - Answer length: {len(answer)}, Thinking length: {len(thinking) if thinking else 0}")
        return answer, thinking

    def _extract_key_findings(self, research_text: str, query: str) -> str:
        """Extract and format key findings from research for comprehensive synthesis."""

        key_findings = []
        detailed_content = []

        # Extract web search results and their details
        lines = research_text.split('\n')
        current_source = None

        for line in lines:
            line = line.strip()

            # Look for web search results
            if line.startswith('[W') and ']' in line:
                if current_source:
                    key_findings.append(current_source)

                # Extract title
                bracket_end = line.find(']')
                title = line[bracket_end+1:].strip()
                current_source = f"SOURCE: {title}"

            elif line.startswith('URL:') and current_source:
                url = line.replace('URL:', '').strip()
                current_source += f"\nURL: {url}"
 
            elif line.startswith('content:') and current_source:
                content = line.replace('content:', '').strip()
                current_source += f"\nDETAILED CONTENT: {content}"

        # Add the last source
        if current_source:
            key_findings.append(current_source)

        # Extract ALL key information summaries with more detail
        if "Key Information Summary:" in research_text:
            summary_start = research_text.find("Key Information Summary:")
            if summary_start != -1:
                # Get much more content from the summary
                summary_section = research_text[summary_start:summary_start+2000]
                summary_lines = summary_section.split('\n')[1:]  # Get all lines after header

                summary_content = []
                for line in summary_lines:
                    if line.strip() and not line.startswith('Iteration') and len(line.strip()) > 10:
                        summary_content.append(line.strip())

                if summary_content:
                    detailed_content.append("COMPREHENSIVE INFORMATION EXTRACTED:\n" + "\n".join(summary_content))

        # Extract document analysis content if relevant
        doc_analysis_sections = []
        if "Document Analysis:" in research_text:
            doc_parts = research_text.split("Document Analysis:")
            for part in doc_parts[1:]:  # Skip first empty part
                # Get the content until next major section
                content = part.split("Web Research:")[0].strip()
                if content and len(content) > 50 and "No relevant documents" not in content:
                    doc_analysis_sections.append(f"DOCUMENT FINDINGS:\n{content}")

        # Extract web research content with more detail
        web_research_sections = []
        if "Web Research:" in research_text:
            web_parts = research_text.split("Web Research:")
            for part in web_parts[1:]:  # Skip first empty part
                # Get the content until next major section or end
                content = part.split("Iteration")[0].strip()
                if content and len(content) > 100:
                    web_research_sections.append(f"WEB RESEARCH FINDINGS:\n{content}")

        # Combine all findings for maximum information
        all_findings = []

        if key_findings:
            all_findings.append("=== RESEARCH SOURCES ===\n" + "\n\n".join(key_findings))

        if detailed_content:
            all_findings.extend(detailed_content)

        if doc_analysis_sections:
            all_findings.extend(doc_analysis_sections)

        if web_research_sections:
            all_findings.extend(web_research_sections)

        # Format the comprehensive findings
        if all_findings:
            return "\n\n".join(all_findings)
        else:
            return "Limited relevant information found in research iterations."

    def _filter_research_for_synthesis(self, combined_research: str, original_query: str) -> str:
        """Filter and prioritize research findings for better synthesis."""

        # Split research by iterations
        iteration_sections = combined_research.split("="*50)
        filtered_sections = []

        for section in iteration_sections:
            if not section.strip():
                continue

            # Check if this iteration has relevant content
            if self._is_content_relevant(section, original_query):
                filtered_sections.append(section)
            else:
                # Keep the iteration header but summarize irrelevant findings
                lines = section.split('\n')
                header_lines = [line for line in lines[:3] if line.strip()]  # Keep iteration header

                # Check if there are any web research findings in this iteration
                web_findings = [line for line in lines if 'Web Research:' in line or 'web search' in line.lower()]

                if web_findings:
                    # Keep web findings even if document search was irrelevant
                    filtered_content = header_lines + web_findings
                    filtered_sections.append('\n'.join(filtered_content))
                else:
                    # Summarize that this iteration had limited relevant findings
                    summary = header_lines + ["Limited relevant information found in this iteration."]
                    filtered_sections.append('\n'.join(summary))

        return "\n\n" + "="*50 + "\n\n".join(filtered_sections) if filtered_sections else combined_research

    async def _create_fallback_synthesis(
        self, original_query: str, iterations: List[ResearchIteration]
    ) -> str:
        """Create a fallback synthesis when advanced tools aren't available."""

        sections = []

        # Introduction
        sections.append(f"# Comprehensive Analysis: {original_query}")
        sections.append(f"\nBased on extensive research across {len(iterations)} iterations, here is a comprehensive analysis:")

        # Research summary
        sections.append("\n## Research Summary")
        for i, iteration in enumerate(iterations, 1):
            confidence_indicator = "🟢" if iteration.confidence_score > 0.7 else "🟡" if iteration.confidence_score > 0.4 else "🔴"
            sections.append(f"\n**Iteration {i}** {confidence_indicator} (Confidence: {iteration.confidence_score:.1%})")
            sections.append(f"- Focus: {iteration.query}")
            sections.append(f"- Key findings: {iteration.findings[:200]}...")
            if iteration.gaps_identified:
                sections.append(f"- Gaps identified: {', '.join(iteration.gaps_identified[:2])}")

        # Consolidated findings
        sections.append("\n## Consolidated Analysis")
        all_findings_text = " ".join([iter.findings for iter in iterations])

        # Extract key themes (simplified)
        if "definition" in all_findings_text.lower():
            sections.append("\n### Definition and Overview")
            sections.append("Comprehensive definition and overview based on research findings.")

        if "history" in all_findings_text.lower() or "evolution" in all_findings_text.lower():
            sections.append("\n### Historical Context")
            sections.append("Historical development and evolution traced through research.")

        if "technical" in all_findings_text.lower() or "specification" in all_findings_text.lower():
            sections.append("\n### Technical Specifications")
            sections.append("Technical details and specifications identified in research.")

        # Research quality indicators
        total_sources = sum(len(iter.sources) for iter in iterations)
        final_confidence = iterations[-1].confidence_score if iterations else 0.0

        sections.append(f"\n## Research Quality Metrics")
        sections.append(f"- **Iterations conducted**: {len(iterations)}")
        sections.append(f"- **Sources consulted**: {total_sources}")
        sections.append(f"- **Final confidence**: {final_confidence:.1%}")
        sections.append(f"- **Research depth**: {'Comprehensive' if len(iterations) >= 3 else 'Moderate'}")

        return "\n".join(sections)

    async def _assess_answer_quality(self, answer: str, original_query: str) -> float:
        """Assess the quality of the final answer."""

        quality_score = 0.5  # Base score

        # Length assessment
        if len(answer) > 2000:
            quality_score += 0.2
        elif len(answer) > 1000:
            quality_score += 0.1

        # Structure assessment (simple heuristics)
        if "##" in answer or "**" in answer:  # Has structure
            quality_score += 0.1

        if answer.count('\n') > 10:  # Well-formatted
            quality_score += 0.1

        # Content coverage (keyword analysis)
        query_words = original_query.lower().split()
        answer_lower = answer.lower()

        coverage = sum(1 for word in query_words if word in answer_lower) / len(query_words)
        quality_score += coverage * 0.1

        return min(quality_score, 1.0)

    def _deduplicate_sources(self, sources: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Remove duplicate sources based on content, title, and URL."""
        if not sources:
            return []

        seen_sources = set()
        deduplicated = []

        for source in sources:
            # Create a unique identifier for each source
            title = source.get('title', '').strip().lower()
            url = source.get('url', '').strip().lower() if source.get('url') else ''
            content = source.get('content', '').strip()[:200].lower()  # First 200 chars for comparison

            # For documents, use filename as additional identifier
            filename = ''
            if source.get('metadata', {}).get('filename'):
                filename = source.get('metadata', {}).get('filename', '').strip().lower()
            elif source.get('metadata', {}).get('source_type') == 'document':
                filename = title

            # Create unique identifier based on available data
            if url:
                # For web sources, URL is the primary identifier
                identifier = f"url:{url}"
            elif filename:
                # For documents, filename is the primary identifier
                identifier = f"doc:{filename}"
            elif title and len(title) > 5:
                # For other sources with meaningful titles
                identifier = f"title:{title}"
            elif content and len(content) > 50:
                # Fallback to content hash for sources without clear identifiers
                identifier = f"content:{hash(content)}"
            else:
                # Skip sources without sufficient identifying information
                continue

            # Only add if we haven't seen this source before
            if identifier not in seen_sources:
                seen_sources.add(identifier)
                deduplicated.append(source)
            else:
                logger.info(f"Removing duplicate source: {title[:50]}...")

        logger.info(f"Deduplication: {len(sources)} -> {len(deduplicated)} sources")
        return deduplicated
