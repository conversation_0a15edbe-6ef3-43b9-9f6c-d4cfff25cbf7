import ProfilePage from "@/components/wrapper-screens/settings/profile/screen";
import { getUserAccount } from "@/services";
import { cookies } from "next/headers";
import { redirect } from "next/navigation";
import { getTranslations } from "@/lib/server-i18n";

export default async function Page() {
  const { t } = getTranslations();
  const userId = await cookies().get("userId")?.value;

  if (!userId) {
    return redirect("/sign-in");
  }

  try {
    const user = await getUserAccount(userId);
    return <ProfilePage user={user} />;
  } catch (error) {
    console.error("Error loading profile:", error);
    return (
      <div className="p-4 text-center">
        <p>{t("profile.errorLoading")}</p>
      </div>
    );
  }
}
