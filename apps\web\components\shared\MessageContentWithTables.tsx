import React from "react";
import { parseMarkdownTable } from "@/lib/utils/table-parser";
import { MessageContent } from "@/components/wrapper-screens/chat/components/MessageContent";
import { Source } from "@/components/wrapper-screens/chat/types";
import { CitationProcessor } from "@/lib/utils/citation-processor";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

interface MessageContentWithTablesProps {
  content: string;
  sources?: Source[];
  onCitationClick: (source: Source, highlightedText?: string) => void;
}

/**
 * Helper function to process citations in table cell content
 */
const processCellContent = (
  cellText: string,
  sources: Source[],
  onCitationClick: (source: Source, highlightedText?: string) => void
): React.ReactNode => {
  // Check if cell has citations
  const hasCitations = /\[(D|W)?\d+\]/.test(cellText);

  if (!hasCitations || sources.length === 0) {
    return cellText;
  }

  // Create citation processor
  const processor = new CitationProcessor(sources, onCitationClick);

  // Process the text and convert citation markers to clickable elements
  const segments = processor.processText(cellText);
  return processor.renderProcessedText(segments);
};

/**
 * Component that renders markdown content with tables and processed citations
 */
export const MessageContentWithTables: React.FC<
  MessageContentWithTablesProps
> = ({ content, sources = [], onCitationClick }) => {
  const processedContent = content?.replace(/\\n/g, "\n") || "";
  const tables = parseMarkdownTable(processedContent);

  if (tables.length === 0) {
    // No tables found, use MessageContent for citation processing
    return (
      <MessageContent
        content={processedContent}
        sources={sources}
        onCitationClick={onCitationClick}
      />
    );
  }

  // Split content and render with tables and citations
  const lines = processedContent.split("\n");
  const elements: React.ReactNode[] = [];
  let lastIndex = 0;

  tables.forEach((table, tableIndex) => {
    // Add content before table with citation processing
    if (table.startIndex > lastIndex) {
      const beforeTableContent = lines
        .slice(lastIndex, table.startIndex)
        .join("\n");
      if (beforeTableContent.trim()) {
        elements.push(
          <MessageContent
            key={`before-${tableIndex}`}
            content={beforeTableContent}
            sources={sources}
            onCitationClick={onCitationClick}
          />
        );
      }
    }

    // Add table
    elements.push(
      <div
        key={`table-${tableIndex}`}
        className="my-4 overflow-x-auto max-w-full"
      >
        <Table className="min-w-full">
          <TableHeader>
            <TableRow>
              {table.headers.map((header, i) => (
                <TableHead
                  key={i}
                  style={{ textAlign: table.alignments[i] as any }}
                  className="whitespace-nowrap"
                >
                  {processCellContent(header, sources, onCitationClick)}
                </TableHead>
              ))}
            </TableRow>
          </TableHeader>
          <TableBody>
            {table.rows.map((row, rowIndex) => (
              <TableRow key={rowIndex}>
                {row.map((cell, cellIndex) => (
                  <TableCell
                    key={cellIndex}
                    style={{ textAlign: table.alignments[cellIndex] as any }}
                    className="whitespace-nowrap"
                  >
                    {processCellContent(cell, sources, onCitationClick)}
                  </TableCell>
                ))}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    );

    lastIndex = table.endIndex + 1;
  });

  // Add remaining content after last table
  if (lastIndex < lines.length) {
    const afterTableContent = lines.slice(lastIndex).join("\n");
    if (afterTableContent.trim()) {
      elements.push(
        <MessageContent
          key="after-last-table"
          content={afterTableContent}
          sources={sources}
          onCitationClick={onCitationClick}
        />
      );
    }
  }

  return <div>{elements}</div>;
};
