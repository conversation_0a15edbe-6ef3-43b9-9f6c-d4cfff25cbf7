import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/next-auth";
import db from "@/lib/shared-db";

export async function GET(request: Request) {
  const session = await getServerSession(authOptions);

  if (!session) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const { searchParams } = new URL(request.url);
    const timeRange = searchParams.get("timeRange") || "30d";
    const planTier = searchParams.get("planTier");
    const region = searchParams.get("region");
    const format = searchParams.get("format") || "full";

    // Parse time range
    const days = timeRange === "7d" ? 7 : timeRange === "30d" ? 30 : 90;
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);
    const endDate = new Date();

    // Base filters
    const tenantFilter: any = {};
    if (planTier) {
      tenantFilter.Subscription = {
        some: {
          plan: {
            name: {
              contains: planTier,
              mode: 'insensitive'
            }
          }
        }
      };
    }

    let csvContent = "";
    let filename = `executive-dashboard-${new Date().toISOString().split('T')[0]}.csv`;

    if (format === "snapshot") {
      // Export KPI snapshot
      filename = `executive-snapshot-${new Date().toISOString().split('T')[0]}.csv`;
      
      // Get current KPI metrics
      const activeTenants = await db.tenant.count({
        where: {
          ...tenantFilter,
          OR: [
            {
              TokenUsage: {
                some: {
                  timestamp: { gte: startDate, lte: endDate }
                }
              }
            },
            {
              APIRequest: {
                some: {
                  timestamp: { gte: startDate, lte: endDate }
                }
              }
            }
          ]
        }
      });

      const activeUsers = await db.user.count({
        where: {
          OR: [
            {
              TokenUsage: {
                some: {
                  timestamp: { gte: startDate, lte: endDate }
                }
              }
            },
            {
              APIRequest: {
                some: {
                  timestamp: { gte: startDate, lte: endDate }
                }
              }
            }
          ]
        }
      });

      const subscriptions = await db.subscription.findMany({
        where: {
          isActive: true,
          ...(planTier && {
            plan: {
              name: {
                contains: planTier,
                mode: 'insensitive'
              }
            }
          })
        },
        include: {
          plan: true
        }
      });

      const mrr = subscriptions.reduce((sum, sub) => {
        const monthlyAmount = sub.billingInterval === 'year' 
          ? (sub.plan?.price || 0) / 12 
          : (sub.plan?.price || 0);
        return sum + monthlyAmount;
      }, 0);

      const tokenUsageCosts = await db.tokenUsage.aggregate({
        where: {
          timestamp: { gte: startDate, lte: endDate },
          ...(planTier && {
            tenant: {
              Subscription: {
                some: {
                  plan: {
                    name: {
                      contains: planTier,
                      mode: 'insensitive'
                    }
                  }
                }
              }
            }
          })
        },
        _sum: {
          cost: true
        }
      });

      const infrastructureCost = tokenUsageCosts._sum.cost || 0;
      const grossMargin = mrr > 0 ? ((mrr - infrastructureCost) / mrr) * 100 : 0;

      // Create CSV content for snapshot
      csvContent = [
        "Metric,Value,Date,Time Range",
        `Active Tenants,${activeTenants},${new Date().toISOString()},${timeRange}`,
        `Active Users,${activeUsers},${new Date().toISOString()},${timeRange}`,
        `Monthly Recurring Revenue,$${mrr.toFixed(2)},${new Date().toISOString()},Current`,
        `Infrastructure Cost,$${infrastructureCost.toFixed(2)},${new Date().toISOString()},${timeRange}`,
        `Gross Margin,${grossMargin.toFixed(2)}%,${new Date().toISOString()},Current`,
        `Active Subscriptions,${subscriptions.length},${new Date().toISOString()},Current`
      ].join("\n");

    } else {
      // Export full dashboard data
      filename = `executive-full-${new Date().toISOString().split('T')[0]}.csv`;

      // Get detailed tenant data
      const tenants = await db.tenant.findMany({
        where: tenantFilter,
        include: {
          Subscription: {
            include: { plan: true }
          },
          TokenUsage: {
            where: {
              timestamp: { gte: startDate, lte: endDate }
            }
          },
          APIRequest: {
            where: {
              timestamp: { gte: startDate, lte: endDate }
            }
          },
          Membership: true
        }
      });

      // Create detailed CSV
      const headers = [
        "Tenant ID",
        "Tenant Name",
        "Plan",
        "Created Date",
        "Active Users",
        "Total Token Usage",
        "Token Cost",
        "API Requests",
        "Successful Requests",
        "Failed Requests",
        "Monthly Revenue",
        "Profit Margin",
        "Status"
      ];

      const rows = tenants.map(tenant => {
        const subscription = tenant.Subscription[0];
        const plan = subscription?.plan;
        const tokenUsage = tenant.TokenUsage.reduce((sum, usage) => 
          sum + usage.inputTokens + usage.outputTokens, 0);
        const tokenCost = tenant.TokenUsage.reduce((sum, usage) => 
          sum + usage.cost, 0);
        const totalRequests = tenant.APIRequest.length;
        const successfulRequests = tenant.APIRequest.filter(req => req.success).length;
        const failedRequests = totalRequests - successfulRequests;
        const monthlyRevenue = subscription?.billingInterval === 'year' 
          ? (plan?.price || 0) / 12 
          : (plan?.price || 0);
        const profitMargin = monthlyRevenue > 0 
          ? ((monthlyRevenue - tokenCost) / monthlyRevenue) * 100 
          : 0;

        return [
          tenant.id,
          `"${tenant.name}"`,
          plan?.name || "No Plan",
          tenant.createdAt.toISOString().split('T')[0],
          tenant.Membership.length,
          tokenUsage,
          tokenCost.toFixed(2),
          totalRequests,
          successfulRequests,
          failedRequests,
          monthlyRevenue.toFixed(2),
          profitMargin.toFixed(2),
          subscription?.isActive ? "Active" : "Inactive"
        ].join(",");
      });

      csvContent = [headers.join(","), ...rows].join("\n");
    }

    // Return CSV file
    const response = new NextResponse(csvContent, {
      status: 200,
      headers: {
        'Content-Type': 'text/csv',
        'Content-Disposition': `attachment; filename="${filename}"`,
      },
    });

    return response;

  } catch (error) {
    console.error("Error exporting dashboard data:", error);
    return NextResponse.json(
      { error: "Failed to export dashboard data" },
      { status: 500 }
    );
  }
}
