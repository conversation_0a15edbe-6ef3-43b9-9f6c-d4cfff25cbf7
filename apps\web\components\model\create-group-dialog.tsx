"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { useLanguage } from "@/lib/language-context";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  <PERSON><PERSON>Header,
  <PERSON><PERSON>T<PERSON><PERSON>,
  Di<PERSON>Trigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { useRouter } from "next/navigation";
import toast from "react-hot-toast";
import { createGroup } from "@/services/src/group";

interface CreateGroupDialogProps {
  tenantId?: string;
  userId?: string;
  trigger: React.ReactNode;
  onGroupCreated?: () => void; // Callback to refresh parent component
}

export function CreateGroupDialog({
  tenantId,
  trigger,
  userId,
  onGroupCreated,
}: CreateGroupDialogProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [name, setName] = useState("");
  const [description, setDescription] = useState("");
  const router = useRouter();
  const { t } = useLanguage();

  const onSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!tenantId) {
      toast.error(t("common.error"));
      return;
    }

    try {
      setIsLoading(true);
      const result = await createGroup(
        {
          tenantId,
          name,
          description,
        },
        userId
      );
      if (result?.error) {
        toast.error(result.error || t("common.error"));
        return;
      }
      toast.success(t("groups.groupCreated"));
      setIsOpen(false);
      setName("");
      setDescription("");

      // Trigger parent component refresh
      if (onGroupCreated) {
        onGroupCreated();
      } else {
        // Fallback to router refresh if no callback provided
        router.refresh();
      }
    } catch (error) {
      toast.error(t("groups.groupCreateFailed"));
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>{trigger}</DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{t("groups.createNewGroup")}</DialogTitle>
          <DialogDescription>
            {t("groups.createGroupDescription")}
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={onSubmit}>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="name">{t("groups.groupName")}</Label>
              <Input
                id="name"
                placeholder={t("groups.enterGroupName")}
                value={name}
                onChange={(e) => setName(e.target.value)}
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="description">{t("groups.description")}</Label>
              <Textarea
                id="description"
                placeholder={t("groups.enterDescription")}
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                rows={3}
              />
            </div>
          </div>
          <DialogFooter>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? t("common.creating") : t("common.create")}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
