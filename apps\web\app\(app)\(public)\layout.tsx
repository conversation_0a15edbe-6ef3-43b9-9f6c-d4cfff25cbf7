import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/next-auth";
import { cookies } from "next/headers";
import { ThemeConfigProvider } from "@/lib/theme-config-context";
import { DynamicFaviconManager } from "@/components/theme/dynamic-favicon-manager";
import { PublicThemeInitializer } from "@/components/theme/public-theme-initializer";
import { RealTimeThemeSynchronizer } from "@/components/theme/real-time-theme-synchronizer";
import { ThemePreloader } from "@/components/theme/theme-preloader";
import db from "@/lib/shared-db";

interface PublicLayoutProps {
  children: React.ReactNode;
}

export default async function PublicLayout({ children }: PublicLayoutProps) {
  const session: any = await getServerSession(authOptions);

  // Get tenant ID from cookies or session, fallback to test tenant for public pages
  const tenantId =
    cookies().get("currentOrganizationId")?.value ??
    session?.memberships?.[0]?.tenant?.id ??
    "686aa81188c3cd31d8035002"; // Test tenant ID for public pages

  console.log("tenantId_from_changelog",tenantId)

  const isPartnerConsole = process.env.NEXT_PUBLIC_IS_PARTNER_CONSOLE === "1";

  // Fetch theme configuration directly from database if partner console is enabled
  let themeConfigDetails: any = null;
  if (isPartnerConsole && tenantId) {
    try {
      console.log("PublicLayout: Fetching theme config for tenant:", tenantId);
      themeConfigDetails = await db.themeConfiguration.findUnique({
        where: { tenantId: tenantId },
        select: {
          id: true,
          tenantId: true,
          brandName: true,
          logoUrl: true,
          faviconUrl: true,
          fullAppLogoUrl: true,
          // Light theme color fields
          lightPrimaryColor: true,
          lightSecondaryColor: true,
          lightAccentColor: true,
          lightNavigationBackgroundColor: true,
          lightContentBackgroundColor: true,
          lightForegroundColor: true,
          // Dark theme color fields
          darkPrimaryColor: true,
          darkSecondaryColor: true,
          darkAccentColor: true,
          darkNavigationBackgroundColor: true,
          darkContentBackgroundColor: true,
          darkForegroundColor: true,
          themePreset: true,
          isActive: true,
          version: true,
          updatedAt: true,
        },
      });

      if (themeConfigDetails && !themeConfigDetails.isActive) {
        console.log("PublicLayout: Theme config found but not active");
        themeConfigDetails = null;
      } else if (themeConfigDetails) {
        console.log("PublicLayout: Theme config loaded successfully:", themeConfigDetails.brandName);
      } else {
        console.log("PublicLayout: No theme config found for tenant:", tenantId);
      }
    } catch (error: any) {
      console.error("PublicLayout: Error fetching theme config:", error.message);
      themeConfigDetails = null;
    }
  }

  return (
    <ThemeConfigProvider tenantId={tenantId} initialConfig={themeConfigDetails || undefined}>
      <ThemePreloader
        tenantId={tenantId}
        initialThemeConfig={themeConfigDetails}
      />
      <PublicThemeInitializer
        initialTenantId={tenantId}
        initialThemeConfig={themeConfigDetails}
      />
      <RealTimeThemeSynchronizer>
        <DynamicFaviconManager themeConfig={themeConfigDetails} />
        {children}
      </RealTimeThemeSynchronizer>
    </ThemeConfigProvider>
  );
}
