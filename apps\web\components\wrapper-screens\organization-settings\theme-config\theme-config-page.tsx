"use client";

import { useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  Upload,
  Eye,
  RotateCcw,
  Loader2,
  ImageIcon,
  AlertCircle,
} from "lucide-react";
import { useLanguage } from "@/lib/language-context";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { PartnerConsoleGuard } from "@/components/partner-console-guard";
import { AdminOnlyGuard, useUserRole } from "@/components/auth/role-guard";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import toast from "react-hot-toast";
import { useRouter } from "next/navigation";
import { useTheme } from "next-themes";
import {
  updateThemeConfig,
  getThemeConfig,
  uploadLogo,
  deleteLogo,
  resetThemeConfig,
  validateThemeColors,
  generateThemeVariables,
} from "@/services/theme-config";
import ThemeConfigHeader from "@/components/theme-config-page-components/theme-config-header";
import ThemeConfigLogo from "@/components/theme-config-page-components/theme-config-logo";
import ThemeColorCustomization from "@/components/theme-config-page-components/theme-color-customization";

// Export the type for use in child components
export type { ThemeConfigFormValues };

const themeConfigSchema = z.object({
  brandName: z.string().optional(),
  logoUrl: z.string().url().optional().or(z.literal("")),
  faviconUrl: z.string().url().optional().or(z.literal("")),
  fullAppLogoUrl: z.string().url().optional().or(z.literal("")),
  themeType: z.enum(["default", "custom"]),

  // Light theme color fields
  lightPrimaryColor: z
    .string()
    .regex(/^#[0-9A-Fa-f]{6}$/)
    .optional()
    .or(z.literal("")),
  lightSecondaryColor: z
    .string()
    .regex(/^#[0-9A-Fa-f]{6}$/)
    .optional()
    .or(z.literal("")),
  lightAccentColor: z
    .string()
    .regex(/^#[0-9A-Fa-f]{6}$/)
    .optional()
    .or(z.literal("")),
  lightNavigationBackgroundColor: z
    .string()
    .regex(/^#[0-9A-Fa-f]{6}$/)
    .optional()
    .or(z.literal("")),
  lightContentBackgroundColor: z
    .string()
    .regex(/^#[0-9A-Fa-f]{6}$/)
    .optional()
    .or(z.literal("")),
  lightForegroundColor: z
    .string()
    .regex(/^#[0-9A-Fa-f]{6}$/)
    .optional()
    .or(z.literal("")),

  // Dark theme color fields
  darkPrimaryColor: z
    .string()
    .regex(/^#[0-9A-Fa-f]{6}$/)
    .optional()
    .or(z.literal("")),
  darkSecondaryColor: z
    .string()
    .regex(/^#[0-9A-Fa-f]{6}$/)
    .optional()
    .or(z.literal("")),
  darkAccentColor: z
    .string()
    .regex(/^#[0-9A-Fa-f]{6}$/)
    .optional()
    .or(z.literal("")),
  darkNavigationBackgroundColor: z
    .string()
    .regex(/^#[0-9A-Fa-f]{6}$/)
    .optional()
    .or(z.literal("")),
  darkContentBackgroundColor: z
    .string()
    .regex(/^#[0-9A-Fa-f]{6}$/)
    .optional()
    .or(z.literal("")),
  darkForegroundColor: z
    .string()
    .regex(/^#[0-9A-Fa-f]{6}$/)
    .optional()
    .or(z.literal("")),

  themePreset: z.enum(["light", "dark"]).optional(),
});

type ThemeConfigFormValues = z.infer<typeof themeConfigSchema>;

interface ThemeConfigPageProps {
  organization: any;
  userRole: string;
}

const themePresets = [
  { value: "light", label: "Light Theme", description: "Light color scheme" },
  { value: "dark", label: "Dark Theme", description: "Dark color scheme" },
];

const themeTypes = [
  {
    value: "default",
    label: "Default Theme",
  },
  {
    value: "custom",
    label: "Custom Theme",
  },
];

export default function ThemeConfigPage({
  organization,
  userRole,
}: ThemeConfigPageProps) {
  const router = useRouter();
  const { t } = useLanguage();
  const [saving, setSaving] = useState(false);
  const [loading, setLoading] = useState(true);
  const [previewMode, setPreviewMode] = useState(false);
  const [logoFile, setLogoFile] = useState<File | null>(null);
  const [logoFilesDark, setLogoFilesDark] = useState<File | null>(null);
  const [faviconFile, setFaviconFile] = useState<File | null>(null);
  const [fullAppLogoFile, setFullAppLogoFile] = useState<File | null>(null);

  // Separate uploading states for each logo type
  const [uploadingLogo, setUploadingLogo] = useState(false);
  const [uploadingFavicon, setUploadingFavicon] = useState(false);
  const [uploadingFullAppLogo, setUploadingFullAppLogo] = useState(false);
  const [themeApplying, setThemeApplying] = useState(false);
  const [realtimeUpdateReceived, setRealtimeUpdateReceived] = useState(false);

  // Check if partner console is enabled
  const isPartnerConsole = process.env.NEXT_PUBLIC_IS_PARTNER_CONSOLE === "1";

  // Get current theme for color generation
  const { resolvedTheme } = useTheme();

  // Use role hook to check permissions
  const { isAdmin } = useUserRole(organization?.id);

  // If partner console is not enabled, show message
  if (!isPartnerConsole) {
    return (
      <div className="container mx-auto py-8">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Palette className="h-5 w-5" />
              {t("settings.themeConfig.title")}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Partner Console Required</AlertTitle>
              <AlertDescription>
                {t("settings.partnerConsole.partnerConsoleRequiredDescription")}
              </AlertDescription>
            </Alert>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Check if user has admin permissions
  if (!isAdmin) {
    return (
      <div className="container mx-auto py-8">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Palette className="h-5 w-5" />
              {t("settings.themeConfig.title")}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>
                {t("settings.themeConfig.adminAccessRequired")}
              </AlertTitle>
              <AlertDescription>
                {t("settings.themeConfig.adminAccessDescription")}
              </AlertDescription>
            </Alert>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Listen for real-time theme updates from other users
  useEffect(() => {
    const handleRealtimeThemeUpdate = (event: CustomEvent) => {
      const themeData = event.detail;
      console.log("Theme config page received real-time update:", themeData);

      // Show notification that theme was updated by another user
      if (themeData?.updatedBy && themeData.updatedBy !== organization?.id) {
        setRealtimeUpdateReceived(true);
        toast(t("settings.themeConfig.updatedByTeamMember"));

        // Auto-hide the notification after 5 seconds
        setTimeout(() => {
          setRealtimeUpdateReceived(false);
        }, 5000);
      }
    };

    window.addEventListener(
      "realtimeThemeUpdate",
      handleRealtimeThemeUpdate as EventListener
    );

    return () => {
      window.removeEventListener(
        "realtimeThemeUpdate",
        handleRealtimeThemeUpdate as EventListener
      );
    };
  }, [organization?.id]);

  const form = useForm<ThemeConfigFormValues>({
    resolver: zodResolver(themeConfigSchema),
    defaultValues: {
      brandName: "",
      logoUrl: "",
      faviconUrl: "",
      fullAppLogoUrl: "",
      themeType: "default",
      // Light theme color fields
      lightPrimaryColor: "",
      lightSecondaryColor: "",
      lightAccentColor: "",
      lightNavigationBackgroundColor: "",
      lightContentBackgroundColor: "",
      lightForegroundColor: "",
      // Dark theme color fields
      darkPrimaryColor: "",
      darkSecondaryColor: "",
      darkAccentColor: "",
      darkNavigationBackgroundColor: "",
      darkContentBackgroundColor: "",
      darkForegroundColor: "",
      themePreset: "light",
    },
  });

  // Function to apply preview colors immediately
  const applyPreviewColors = (themeData: Partial<ThemeConfigFormValues>) => {
    const root = document.documentElement;

    // Get current form values to merge with the preview data
    const currentFormValues = form.getValues();
    const mergedData = { ...currentFormValues, ...themeData };

    // Generate theme variables from the merged form data, passing current theme mode
    const themeVariables = generateThemeVariables(
      {
        // Light theme color fields
        lightPrimaryColor: mergedData.lightPrimaryColor || "",
        lightSecondaryColor: mergedData.lightSecondaryColor || "",
        lightAccentColor: mergedData.lightAccentColor || "",
        lightNavigationBackgroundColor:
          mergedData.lightNavigationBackgroundColor || "",
        lightContentBackgroundColor:
          mergedData.lightContentBackgroundColor || "",
        lightForegroundColor: mergedData.lightForegroundColor || "",

        // Dark theme color fields
        darkPrimaryColor: mergedData.darkPrimaryColor || "",
        darkSecondaryColor: mergedData.darkSecondaryColor || "",
        darkAccentColor: mergedData.darkAccentColor || "",
        darkNavigationBackgroundColor:
          mergedData.darkNavigationBackgroundColor || "",
        darkContentBackgroundColor: mergedData.darkContentBackgroundColor || "",
        darkForegroundColor: mergedData.darkForegroundColor || "",

        themePreset: mergedData.themePreset,
      },
      resolvedTheme as "light" | "dark"
    );

    // Apply the CSS variables to the root element
    Object.entries(themeVariables).forEach(([property, value]) => {
      if (value) {
        root.style.setProperty(property, value);
      } else {
        // Remove the custom property to fall back to default
        root.style.removeProperty(property);
      }
    });
  };

  // Function to reset theme to saved configuration without page reload
  const resetToSavedTheme = () => {
    const root = document.documentElement;

    // Get the saved theme configuration from the form's default values
    const savedThemeData = form.formState.defaultValues;

    if (savedThemeData) {
      // Apply the saved theme configuration
      const themeVariables = generateThemeVariables(
        {
          // Light theme color fields
          lightPrimaryColor: savedThemeData.lightPrimaryColor || "",
          lightSecondaryColor: savedThemeData.lightSecondaryColor || "",
          lightAccentColor: savedThemeData.lightAccentColor || "",
          lightNavigationBackgroundColor:
            savedThemeData.lightNavigationBackgroundColor || "",
          lightContentBackgroundColor:
            savedThemeData.lightContentBackgroundColor || "",
          lightForegroundColor: savedThemeData.lightForegroundColor || "",

          // Dark theme color fields
          darkPrimaryColor: savedThemeData.darkPrimaryColor || "",
          darkSecondaryColor: savedThemeData.darkSecondaryColor || "",
          darkAccentColor: savedThemeData.darkAccentColor || "",
          darkNavigationBackgroundColor:
            savedThemeData.darkNavigationBackgroundColor || "",
          darkContentBackgroundColor:
            savedThemeData.darkContentBackgroundColor || "",
          darkForegroundColor: savedThemeData.darkForegroundColor || "",

          themePreset: savedThemeData.themePreset,
        },
        resolvedTheme as "light" | "dark"
      );

      // Apply the CSS variables to the root element
      Object.entries(themeVariables).forEach(([property, value]) => {
        if (value) {
          root.style.setProperty(property, value);
        } else {
          // Remove the custom property to fall back to default
          root.style.removeProperty(property);
        }
      });
    } else {
      // If no saved configuration, remove all custom theme properties to fall back to defaults
      const themeProperties = [
        "--primary",
        "--primary-foreground",
        "--secondary",
        "--secondary-foreground",
        "--accent",
        "--accent-foreground",
        "--background",
        "--foreground",
        "--card",
        "--card-foreground",
        "--popover",
        "--popover-foreground",
        "--muted",
        "--muted-foreground",
        "--border",
        "--input",
        "--sidebar-primary",
        "--sidebar-primary-foreground",
        "--sidebar-accent",
        "--sidebar-accent-foreground",
        "--sidebar-background",
        "--sidebar-foreground",
        "--sidebar-muted",
        "--sidebar-muted-foreground",
        "--sidebar-border",
      ];

      themeProperties.forEach((property) => {
        root.style.removeProperty(property);
      });
    }
  };

  // Load existing theme config
  useEffect(() => {
    const loadThemeConfig = async () => {
      if (!organization?.id) {
        console.log("No organization ID available");
        return;
      }

      try {
        console.log("Loading theme config for organization:", organization.id);
        const config = await getThemeConfig(organization.id);
        console.log("Loaded theme config:", config);

        if (config) {
          // Determine theme type based on existing data (check new light/dark color fields)
          const hasCustomColors = !!(
            config.lightPrimaryColor ||
            config.lightSecondaryColor ||
            config.lightAccentColor ||
            config.lightNavigationBackgroundColor ||
            config.lightContentBackgroundColor ||
            config.lightForegroundColor ||
            config.darkPrimaryColor ||
            config.darkSecondaryColor ||
            config.darkAccentColor ||
            config.darkNavigationBackgroundColor ||
            config.darkContentBackgroundColor ||
            config.darkForegroundColor
          );

          const themeType = hasCustomColors ? "custom" : "default";

          const formData = {
            brandName: config.brandName || "Swiss Knowledge Hub",
            logoUrl: config.logoUrl || "",
            faviconUrl: config.faviconUrl || "",
            fullAppLogoUrl: config.fullAppLogoUrl || "",
            themeType: themeType as "default" | "custom",
            // Light theme color fields
            lightPrimaryColor: config.lightPrimaryColor || "",
            lightSecondaryColor: config.lightSecondaryColor || "",
            lightAccentColor: config.lightAccentColor || "",
            lightNavigationBackgroundColor:
              config.lightNavigationBackgroundColor || "",
            lightContentBackgroundColor:
              config.lightContentBackgroundColor || "",
            lightForegroundColor: config.lightForegroundColor || "",
            // Dark theme color fields
            darkPrimaryColor: config.darkPrimaryColor || "",
            darkSecondaryColor: config.darkSecondaryColor || "",
            darkAccentColor: config.darkAccentColor || "",
            darkNavigationBackgroundColor:
              config.darkNavigationBackgroundColor || "",
            darkContentBackgroundColor: config.darkContentBackgroundColor || "",
            darkForegroundColor: config.darkForegroundColor || "",
            themePreset: (config.themePreset as "light" | "dark") || "light",
          };
          console.log("Form data to reset with:", formData);
          form.reset(formData);
          console.log("Form reset completed");

          // Force update form values to ensure they're properly set
          Object.entries(formData).forEach(([key, value]) => {
            form.setValue(key as keyof typeof formData, value);
          });
          console.log("Form values after manual set:", form.getValues());
        } else {
          // If no config exists, set default values with "Swiss Knowledge Hub" as brand name
          const defaultFormData = {
            brandName: "Swiss Knowledge Hub", // Use hardcoded default brand name
            logoUrl: "",
            faviconUrl: "",
            fullAppLogoUrl: "", // Add missing fullAppLogoUrl field
            themeType: "default" as const,
            // Light theme color fields
            lightPrimaryColor: "",
            lightSecondaryColor: "",
            lightAccentColor: "",
            lightNavigationBackgroundColor: "",
            lightContentBackgroundColor: "",
            lightForegroundColor: "",
            // Dark theme color fields
            darkPrimaryColor: "",
            darkSecondaryColor: "",
            darkAccentColor: "",
            darkNavigationBackgroundColor: "",
            darkContentBackgroundColor: "",
            darkForegroundColor: "",
            themePreset: "light" as const,
          };
          console.log("Setting default form data:", defaultFormData);
          form.reset(defaultFormData);
        }
      } catch (error) {
        console.error("Failed to load theme config:", error);
      } finally {
        setLoading(false);
      }
    };

    loadThemeConfig();
  }, [organization?.id, form]);

  const handleFileUpload = async (
    file: File,
    type: "logo" | "favicon" | "fullAppLogo"
  ) => {
    if (!organization?.id) return;

    // Set specific uploading state based on type
    const setUploadingState = (loading: boolean) => {
      switch (type) {
        case "logo":
          setUploadingLogo(loading);
          break;
        case "favicon":
          setUploadingFavicon(loading);
          break;
        case "fullAppLogo":
          setUploadingFullAppLogo(loading);
          break;
      }
    };

    setUploadingState(true);
    try {
      const result = await uploadLogo(organization.id, file, type);
      if (result.success && result.url) {
        const fieldName =
          type === "logo"
            ? "logoUrl"
            : type === "fullAppLogo"
              ? "fullAppLogoUrl"
              : "faviconUrl";
        form.setValue(fieldName, result.url);
        toast.success(result.message || "File uploaded successfully");

        // Trigger immediate real-time update for logo/favicon changes
        const currentFormData = form.getValues();
        const updatedFormData = {
          ...currentFormData,
          [fieldName]: result.url,
        };

        // Emit custom event for immediate local update
        const themeUpdateEvent = new CustomEvent("themeConfigUpdated", {
          detail: {
            themePreset: updatedFormData.themePreset,
            themeConfig: updatedFormData,
          },
        });
        window.dispatchEvent(themeUpdateEvent);
      } else {
        toast.error(result.message || "Failed to upload file");
      }
    } catch (error) {
      toast.error("Failed to upload file");
    } finally {
      setUploadingState(false);
    }
  };

  const handleFileDelete = async (type: "logo" | "favicon" | "fullAppLogo") => {
    if (!organization?.id) return;

    // Set specific uploading state based on type
    const setUploadingState = (loading: boolean) => {
      switch (type) {
        case "logo":
          setUploadingLogo(loading);
          break;
        case "favicon":
          setUploadingFavicon(loading);
          break;
        case "fullAppLogo":
          setUploadingFullAppLogo(loading);
          break;
      }
    };

    setUploadingState(true);
    try {
      const result = await deleteLogo(organization.id, type);
      if (result.success) {
        const fieldName =
          type === "logo"
            ? "logoUrl"
            : type === "fullAppLogo"
              ? "fullAppLogoUrl"
              : "faviconUrl";
        form.setValue(fieldName, "");
        toast.success(result.message || "File deleted successfully");

        // Trigger immediate real-time update for logo/favicon deletion
        const currentFormData = form.getValues();
        const updatedFormData = {
          ...currentFormData,
          [fieldName]: "",
        };

        // Emit custom event for immediate local update
        const themeUpdateEvent = new CustomEvent("themeConfigUpdated", {
          detail: {
            themePreset: updatedFormData.themePreset,
            themeConfig: updatedFormData,
          },
        });
        window.dispatchEvent(themeUpdateEvent);
      } else {
        toast.error(result.message || "Failed to delete file");
      }
    } catch (error) {
      toast.error("Failed to delete file");
    } finally {
      setUploadingState(false);
    }
  };

  const onSubmit = async (data: ThemeConfigFormValues) => {
    if (!organization?.id) return;

    setSaving(true);
    setThemeApplying(true);
    try {
      // Prepare data based on theme type
      let processedData: ThemeConfigFormValues;

      if (data.themeType === "default") {
        // For default theme: clear custom color fields, keep themePreset
        processedData = {
          ...data,
          // Clear all light theme color fields
          lightPrimaryColor: "",
          lightSecondaryColor: "",
          lightAccentColor: "",
          lightNavigationBackgroundColor: "",
          lightContentBackgroundColor: "",
          lightForegroundColor: "",
          // Clear all dark theme color fields
          darkPrimaryColor: "",
          darkSecondaryColor: "",
          darkAccentColor: "",
          darkNavigationBackgroundColor: "",
          darkContentBackgroundColor: "",
          darkForegroundColor: "",
          // Keep themePreset for default theme
        };
      } else {
        // For custom theme: clear themePreset, keep custom colors
        processedData = {
          ...data,
          themePreset: undefined,
          // Keep all custom color fields
        };
      }

      // Validate theme colors only for custom theme
      if (data.themeType === "custom") {
        const validation = validateThemeColors(processedData);
        if (!validation.isValid) {
          validation.errors.forEach((error) => toast.error(error));
          return;
        }
      }

      const result = await updateThemeConfig(organization.id, processedData);
      if (result.success) {
        toast.success(
          result.message || t("settings.themeConfig.updateSuccess")
        );

        // Emit custom event to notify theme initializer for immediate local update
        const themeUpdateEvent = new CustomEvent("themeConfigUpdated", {
          detail: {
            themePreset: processedData.themePreset,
            themeConfig: processedData,
          },
        });
        window.dispatchEvent(themeUpdateEvent);

        // Note: Real-time updates to other users will be handled via WebSocket
        // No page reload needed anymore
      } else {
        toast.error(result.message || t("settings.themeConfig.updateFailed"));
      }
    } catch (error) {
      toast.error(t("settings.themeConfig.updateFailed"));
    } finally {
      setSaving(false);
      // Add a small delay to show the applying state
      setTimeout(() => {
        setThemeApplying(false);
      }, 1000);
    }
  };

  const handleReset = async () => {
    if (!organization?.id) return;

    setSaving(true);
    try {
      const result = await resetThemeConfig(organization.id);
      if (result.success) {
        // Reset form with proper default values
        const defaultFormData = {
          brandName: "Swiss Knowledge Hub", // Use hardcoded default brand name
          logoUrl: "",
          faviconUrl: "",
          fullAppLogoUrl: "",
          themeType: "default" as const,
          // Light theme color fields
          lightPrimaryColor: "",
          lightSecondaryColor: "",
          lightAccentColor: "",
          lightNavigationBackgroundColor: "",
          lightContentBackgroundColor: "",
          lightForegroundColor: "",
          // Dark theme color fields
          darkPrimaryColor: "",
          darkSecondaryColor: "",
          darkAccentColor: "",
          darkNavigationBackgroundColor: "",
          darkContentBackgroundColor: "",
          darkForegroundColor: "",
          themePreset: "light" as const,
        };
        form.reset(defaultFormData);
        toast.success(result.message || t("settings.themeConfig.resetSuccess"));

        // Emit custom event to notify theme initializer of reset
        const themeResetEvent = new CustomEvent("themeConfigUpdated", {
          detail: { themePreset: "light" },
        });
        window.dispatchEvent(themeResetEvent);

        // Note: Real-time updates to other users will be handled via WebSocket
        // No page reload needed anymore
      } else {
        toast.error(result.message || t("settings.themeConfig.resetFailed"));
      }
    } catch (error) {
      toast.error(t("settings.themeConfig.resetFailed"));
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <Loader2 className="h-6 w-6 animate-spin" />
      </div>
    );
  }

  return (
    <PartnerConsoleGuard>
      <AdminOnlyGuard tenantId={organization?.id}>
        <div className="space-y-6">
          <ThemeConfigHeader
            previewMode={previewMode}
            setPreviewMode={setPreviewMode}
            applyPreviewColors={applyPreviewColors}
            resetToSavedTheme={resetToSavedTheme}
            saving={saving}
            form={form}
          />
          {realtimeUpdateReceived && (
            <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
              <div className="flex items-center gap-2 text-blue-800">
                <div className="h-2 w-2 bg-blue-500 rounded-full animate-pulse"></div>
                <span className="text-sm font-medium">
                  {t("settings.themeConfig.updatedByTeamMember")}
                </span>
              </div>
            </div>
          )}

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              {/* Brand Identity */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <ImageIcon className="h-5 w-5" />
                    Brand Identity
                  </CardTitle>
                  <CardDescription>
                    Configure your organization's brand name and logos
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <FormField
                    control={form.control}
                    name="brandName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Brand Name</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Enter your brand name"
                            {...field}
                            disabled={!isAdmin}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <ThemeConfigLogo
                    form={form}
                    isAdmin={isAdmin}
                    uploadingLogo={uploadingLogo}
                    uploadingFavicon={uploadingFavicon}
                    uploadingFullAppLogo={uploadingFullAppLogo}
                    setLogoFile={setLogoFile}
                    setFaviconFile={setFaviconFile}
                    setFullAppLogoFile={setFullAppLogoFile}
                    handleFileUpload={handleFileUpload}
                    handleFileDelete={handleFileDelete}
                  />
                </CardContent>
              </Card>

              {/* Theme Type Selector */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Palette className="h-5 w-5" />
                    Theme Type
                  </CardTitle>
                  <CardDescription>
                    Choose how you want to configure your theme
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <FormField
                    control={form.control}
                    name="themeType"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          {t("settings.themeConfig.configurationType")}
                        </FormLabel>
                        <FormControl>
                          <Select
                            onValueChange={(value) => {
                              field.onChange(value);
                              // When switching theme types, we may want to clear certain fields
                              if (value === "default") {
                                // Clear custom colors when switching to default
                                // Clear light theme colors
                                form.setValue("lightPrimaryColor", "");
                                form.setValue("lightSecondaryColor", "");
                                form.setValue("lightAccentColor", "");
                                form.setValue(
                                  "lightNavigationBackgroundColor",
                                  ""
                                );
                                form.setValue(
                                  "lightContentBackgroundColor",
                                  ""
                                );
                                form.setValue("lightForegroundColor", "");
                                // Clear dark theme colors
                                form.setValue("darkPrimaryColor", "");
                                form.setValue("darkSecondaryColor", "");
                                form.setValue("darkAccentColor", "");
                                form.setValue(
                                  "darkNavigationBackgroundColor",
                                  ""
                                );
                                form.setValue("darkContentBackgroundColor", "");
                                form.setValue("darkForegroundColor", "");
                              } else if (value === "custom") {
                                // Set default theme preset when switching to custom
                                if (!form.getValues("themePreset")) {
                                  form.setValue("themePreset", "light");
                                }
                              }
                            }}
                            value={field.value}
                            disabled={!isAdmin}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select theme type" />
                            </SelectTrigger>
                            <SelectContent>
                              {themeTypes.map((type) => (
                                <SelectItem key={type.value} value={type.value}>
                                  <div className="flex flex-col">
                                    <span className="font-medium">
                                      {type.label}
                                    </span>
                                  </div>
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </FormControl>
                        <FormDescription>
                          Default theme uses built-in presets, while custom
                          theme allows full color customization
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </CardContent>
              </Card>

              {/* Theme Mode - Only show for default theme */}
              {form.watch("themeType") === "default" && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Palette className="h-5 w-5" />
                      Theme Mode
                    </CardTitle>
                    <CardDescription>
                      Choose between light and dark theme modes
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <FormField
                      control={form.control}
                      name="themePreset"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Default Theme Mode</FormLabel>
                          <FormControl>
                            <Select
                              onValueChange={field.onChange}
                              value={field.value}
                              disabled={!isAdmin}
                            >
                              <SelectTrigger>
                                <SelectValue placeholder="Select theme mode" />
                              </SelectTrigger>
                              <SelectContent>
                                {themePresets.map((preset) => (
                                  <SelectItem
                                    key={preset.value}
                                    value={preset.value}
                                  >
                                    <div className="flex flex-col">
                                      <span className="font-medium">
                                        {preset.label}
                                      </span>
                                    </div>
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </FormControl>
                          <FormDescription>
                            Sets the default theme mode for the application
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </CardContent>
                </Card>
              )}

              {/* Color Customization - Only show for custom theme */}
              {form.watch("themeType") === "custom" && (
                <ThemeColorCustomization
                  form={form}
                  previewMode={previewMode}
                  applyPreviewColors={applyPreviewColors}
                  isAdmin={isAdmin}
                />
              )}

              {/* Save Actions */}
              {isAdmin && (
                <div className="flex justify-between items-center flex-wrap">
                  {/* Action Buttons */}
                  <div className="flex space-x-2">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={handleReset}
                      disabled={saving}
                    >
                      <RotateCcw className="h-4 w-4 mr-2" />
                      {t("settings.themeConfig.resetToDefault")}
                    </Button>
                    <Button type="submit" disabled={saving || themeApplying}>
                      {saving ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          {t("settings.themeConfig.saving")}
                        </>
                      ) : themeApplying ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          {t("settings.themeConfig.applyingTheme")}
                        </>
                      ) : (
                        <>
                          <Upload className="mr-2 h-4 w-4" />
                          {t("settings.themeConfig.saveTheme")}
                        </>
                      )}
                    </Button>
                  </div>
                </div>
              )}
            </form>
          </Form>
        </div>
      </AdminOnlyGuard>
    </PartnerConsoleGuard>
  );
}
