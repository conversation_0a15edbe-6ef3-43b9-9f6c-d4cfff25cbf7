"""
Encryption utilities for handling encrypted fields in the database.
This module provides functionality to decrypt message content and other encrypted fields
that are encrypted using the prisma-field-encryption package with @47ng/cloak.
"""

import os
import base64
import json
import logging
import struct
from typing import Optional, Dict, Any, List
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.kdf.hkdf import HKDF
from cryptography.hazmat.backends import default_backend

logger = logging.getLogger(__name__)

class CloakDecryption:
    """
    Handles decryption of fields encrypted by @47ng/cloak library.
    This implementation follows the cloak encryption format used by prisma-field-encryption.
    """

    def __init__(self):
        self.master_key = os.getenv("NEXT_ENCRYPTION_CLOAK_KEY", "")
        self.keychain_data = os.getenv("NEXT_ENCRYPTION_CLOAK_KEYCHAIN", "")
        logger.info(f"Encryption keys found: {self.master_key} and {(self.keychain_data)}")

        self.decryption_keys = []

        if not self.master_key or not self.keychain_data:
            logger.warning("Encryption keys not found in environment variables")
            return

        try:
            self._initialize_decryption_keys()
        except Exception as e:
            logger.error(f"Failed to initialize decryption keys: {str(e)}")


    def _initialize_decryption_keys(self):
        """Initialize decryption keys from the keychain."""
        logger.info("Initializing encryption keys...")

        # First, extract and add the master key
        master_key_bytes = None
        if self.master_key:
            try:
                master_key_bytes = self._extract_master_key(self.master_key)
                if master_key_bytes:
                    self.decryption_keys.append(master_key_bytes)
                    logger.info("Added master key to decryption keys")
            except Exception as e:
                logger.warning(f"Failed to decode master key: {str(e)}")

        # Then, try to decrypt and parse the keychain
        if self.keychain_data and master_key_bytes:
            try:
                # The keychain is encrypted, decrypt it first
                decrypted_keychain = self._decrypt_keychain(self.keychain_data, master_key_bytes)

                if decrypted_keychain:
                    # Parse the decrypted keychain as JSON
                    try:
                        keychain = json.loads(decrypted_keychain)
                        logger.info("Successfully decrypted and parsed keychain")

                        # Extract keys from keychain
                        if isinstance(keychain, dict):
                            for key_id, key_data in keychain.items():
                                try:
                                    if isinstance(key_data, dict) and 'key' in key_data:
                                        # The key is base64 encoded
                                        key_bytes = base64.b64decode(key_data['key'])
                                        self.decryption_keys.append(key_bytes)
                                        logger.info(f"Added keychain key: {key_id}")
                                    elif isinstance(key_data, str):
                                        # Direct key string
                                        key_bytes = base64.b64decode(key_data)
                                        self.decryption_keys.append(key_bytes)
                                        logger.info(f"Added direct key: {key_id}")
                                except Exception as e:
                                    logger.warning(f"Failed to decode key {key_id}: {str(e)}")
                                    continue
                        else:
                            logger.warning(f"Decrypted keychain is not a dict: {type(keychain)}")
                    except json.JSONDecodeError as e:
                        logger.warning(f"Failed to parse decrypted keychain as JSON: {str(e)}")

            except Exception as e:
                logger.warning(f"Failed to decrypt keychain: {str(e)}")
        elif self.keychain_data and not master_key_bytes:
            logger.warning("Keychain found but no master key available to decrypt it")

        if len(self.decryption_keys) == 0:
            logger.warning("No decryption keys were successfully loaded")
        else:
            logger.info(f"Successfully initialized {len(self.decryption_keys)} decryption keys")

    def _extract_master_key(self, master_key_str: str) -> Optional[bytes]:
        """Extract the actual key bytes from master key string."""
        try:
            if master_key_str.startswith('k1.aesgcm256.'):
                # Format: k1.aesgcm256.{base64_key}
                parts = master_key_str.split('.')
                if len(parts) >= 3:
                    key_data = parts[2]
                    return base64.b64decode(key_data)
            else:
                # Try direct base64 decode
                return base64.b64decode(master_key_str)
        except Exception as e:
            logger.error(f"Failed to extract master key: {str(e)}")
            return None

    def _decrypt_keychain(self, keychain_str: str, master_key: bytes) -> Optional[str]:
        """Decrypt the encrypted keychain using the master key."""
        try:
            if keychain_str.startswith('v1.aesgcm256.'):
                # The keychain is encrypted in v1.aesgcm256 format
                return self._decrypt_v1_aesgcm256(keychain_str, master_key)
            else:
                # Try other decryption methods
                return self._decrypt_cloak_format(keychain_str, master_key)
        except Exception as e:
            logger.error(f"Failed to decrypt keychain: {str(e)}")
            return None

    def _parse_keychain(self, keychain_data: str) -> Optional[Dict[str, Any]]:
        """Parse keychain data in various formats."""
        try:
            # Format 1: Base64 encoded JSON
            try:
                keychain_bytes = base64.b64decode(keychain_data)
                keychain = json.loads(keychain_bytes.decode('utf-8'))
                logger.info("Parsed keychain as base64-encoded JSON")
                return keychain
            except UnicodeDecodeError:
                # Try different encodings
                for encoding in ['latin-1', 'ascii', 'cp1252']:
                    try:
                        keychain = json.loads(keychain_bytes.decode(encoding))
                        logger.info(f"Parsed keychain using {encoding} encoding")
                        return keychain
                    except Exception:
                        continue
            except Exception:
                pass

            # Format 2: Direct JSON string
            try:
                keychain = json.loads(keychain_data)
                logger.info("Parsed keychain as direct JSON")
                return keychain
            except Exception:
                pass

            # Format 3: URL-safe base64 encoded JSON
            try:
                keychain_bytes = base64.urlsafe_b64decode(keychain_data)
                keychain = json.loads(keychain_bytes.decode('utf-8'))
                logger.info("Parsed keychain as URL-safe base64-encoded JSON")
                return keychain
            except Exception:
                pass

            logger.warning("Could not parse keychain in any known format")
            return None

        except Exception as e:
            logger.error(f"Error parsing keychain: {str(e)}")
            return None

    def decrypt_field(self, encrypted_value: str) -> Optional[str]:
        """
        Decrypt an encrypted field value using cloak format.

        Args:
            encrypted_value: The encrypted string from the database

        Returns:
            Decrypted string or None if decryption fails
        """
        if not encrypted_value or not self.decryption_keys:
            return encrypted_value

        # Try to detect if the value is actually encrypted
        # if not self._is_encrypted_value(encrypted_value):
        #     return encrypted_value

        # Try each decryption key
        for key in self.decryption_keys:
            try:
                decrypted = self._decrypt_cloak_format(encrypted_value, key)
                if decrypted and decrypted != encrypted_value:
                    return decrypted
            except Exception as e:
                logger.debug(f"Cloak decryption attempt failed: {str(e)}")
                continue

        logger.debug(f"Failed to decrypt field value with all available keys")
        return encrypted_value  # Return original if decryption fails

    def _is_encrypted_value(self, value: str) -> bool:
        """Check if a value appears to be encrypted."""
        if not value:
            return False

        # Check for cloak version prefixes (v1.aesgcm256, etc.)
        if value.startswith('v1.aesgcm256.'):
            return True

        # Check for other encryption prefixes
        encryption_prefixes = ['enc:', 'encrypted:', '$encrypted$', 'cloak:', 'v1.']
        if any(value.startswith(prefix) for prefix in encryption_prefixes):
            return True

        # Check for cloak format - typically base64 encoded
        try:
            # If it's valid base64 and longer than typical plaintext, likely encrypted
            decoded = base64.b64decode(value, validate=True)
            # Cloak encrypted values are typically longer and have specific structure
            return len(decoded) > 20
        except:
            pass

        return False

    def _decrypt_v1_aesgcm256(self, encrypted_value: str, key: bytes) -> Optional[str]:
        """
        Decrypt v1.aesgcm256 format specifically.
        Format: v1.aesgcm256.{keyId}.{nonce}.{ciphertext}
        """
        try:
            # Parse the v1.aesgcm256 format
            parts = encrypted_value.split('.')
            if len(parts) < 4 or parts[0] != 'v1' or parts[1] != 'aesgcm256':
                return None

            key_id = parts[2]  # Key identifier (8 hex chars)
            nonce_b64 = parts[3]  # Base64 encoded nonce
            ciphertext_b64 = '.'.join(parts[4:])  # Rest is base64 encoded ciphertext+tag

            # Decode nonce and ciphertext
            try:
                nonce = base64.urlsafe_b64decode(nonce_b64 + '==')  # Add padding if needed
            except:
                nonce = base64.b64decode(nonce_b64 + '==')

            try:
                ciphertext_with_tag = base64.urlsafe_b64decode(ciphertext_b64 + '==')
            except:
                ciphertext_with_tag = base64.b64decode(ciphertext_b64 + '==')

            # Extract tag (last 16 bytes) and ciphertext
            if len(ciphertext_with_tag) < 16:
                return None

            tag = ciphertext_with_tag[-16:]
            ciphertext = ciphertext_with_tag[:-16]

            # Ensure key is 32 bytes for AES-256
            if len(key) != 32:
                # Derive key using HKDF
                hkdf = HKDF(
                    algorithm=hashes.SHA256(),
                    length=32,
                    salt=b'',
                    info=b'',
                    backend=default_backend()
                )
                key = hkdf.derive(key)

            # Decrypt using AES-256-GCM
            cipher = Cipher(
                algorithms.AES(key),
                modes.GCM(nonce, tag),
                backend=default_backend()
            )
            decryptor = cipher.decryptor()

            # Decrypt the ciphertext
            plaintext = decryptor.update(ciphertext)
            decryptor.finalize()

            # Try to decode as UTF-8
            return plaintext.decode('utf-8')

        except Exception as e:
            logger.debug(f"v1.aesgcm256 decryption failed: {str(e)}")
            return None

    def _decrypt_cloak_format(self, encrypted_value: str, key: bytes) -> Optional[str]:
        """
        Decrypt value using cloak format.
        Cloak uses AES-256-GCM encryption with a specific format.
        """
        try:
            # Handle v1.aesgcm256 format specifically
            if encrypted_value.startswith('v1.aesgcm256.'):
                return self._decrypt_v1_aesgcm256(encrypted_value, key)

            # Remove any other prefixes
            clean_value = encrypted_value
            if encrypted_value.startswith('enc:'):
                clean_value = encrypted_value[4:]
            elif encrypted_value.startswith('encrypted:'):
                clean_value = encrypted_value[10:]
            elif encrypted_value.startswith('$encrypted$'):
                clean_value = encrypted_value[11:]
            elif encrypted_value.startswith('cloak:'):
                clean_value = encrypted_value[6:]

            # Decode the base64 encrypted data
            try:
                encrypted_data = base64.b64decode(clean_value)
            except Exception:
                # Try URL-safe base64
                encrypted_data = base64.urlsafe_b64decode(clean_value)

            # Cloak format: [nonce (12 bytes)] + [ciphertext] + [tag (16 bytes)]
            if len(encrypted_data) < 28:  # minimum: 12 + 0 + 16
                return None

            # Extract components
            nonce = encrypted_data[:12]
            tag = encrypted_data[-16:]
            ciphertext = encrypted_data[12:-16]

            # Ensure key is 32 bytes for AES-256
            if len(key) != 32:
                # Derive key using HKDF
                hkdf = HKDF(
                    algorithm=hashes.SHA256(),
                    length=32,
                    salt=b'',
                    info=b'',
                    backend=default_backend()
                )
                key = hkdf.derive(key)

            # Decrypt using AES-256-GCM
            cipher = Cipher(
                algorithms.AES(key),
                modes.GCM(nonce),
                backend=default_backend()
            )
            decryptor = cipher.decryptor()

            # Decrypt the ciphertext
            plaintext = decryptor.update(ciphertext)

            # Verify the tag and finalize
            decryptor.finalize_with_tag(tag)

            # Try to decode as UTF-8
            return plaintext.decode('utf-8')

        except Exception as e:
            logger.debug(f"Cloak decryption failed: {str(e)}")

            # Fallback: try simpler decryption methods
            try:
                return self._fallback_decrypt(encrypted_value, key)
            except Exception as e2:
                logger.debug(f"Fallback decryption failed: {str(e2)}")
                return None

    def _fallback_decrypt(self, encrypted_value: str, key: bytes) -> Optional[str]:
        """Fallback decryption methods for different formats."""
        try:
            # Remove prefixes
            clean_value = encrypted_value
            for prefix in ['enc:', 'encrypted:', '$encrypted$', 'cloak:']:
                if encrypted_value.startswith(prefix):
                    clean_value = encrypted_value[len(prefix):]
                    break

            # Try base64 decode + simple XOR
            try:
                encrypted_bytes = base64.b64decode(clean_value)
                # Simple XOR with key
                key_cycle = (key * ((len(encrypted_bytes) // len(key)) + 1))[:len(encrypted_bytes)]
                decrypted_bytes = bytes(a ^ b for a, b in zip(encrypted_bytes, key_cycle))

                # Try to decode as UTF-8
                result = decrypted_bytes.decode('utf-8', errors='ignore')
                # Check if result looks like valid text
                if result and len(result.strip()) > 0 and result.isprintable():
                    return result
            except Exception:
                pass

            # If the value looks like it might be plain text, return it
            if encrypted_value and len(encrypted_value.strip()) > 0:
                try:
                    # Check if it's already valid UTF-8 text
                    encrypted_value.encode('utf-8')
                    return encrypted_value
                except:
                    pass

            return None

        except Exception as e:
            logger.debug(f"Fallback decryption failed: {str(e)}")
            return None

    def decrypt_message_content(self, message: Dict[str, Any]) -> Dict[str, Any]:
        """
        Decrypt the content field of a message document.

        Args:
            message: Message document from MongoDB

        Returns:
            Message document with decrypted content
        """
        if not message or 'content' not in message:
            return message

        original_content = message['content']
        decrypted_content = self.decrypt_field(original_content)

        # Only update if decryption was successful and different from original
        if decrypted_content and decrypted_content != original_content:
            message['content'] = decrypted_content

        return message

    def decrypt_user_fields(self, user: Dict[str, Any]) -> Dict[str, Any]:
        """
        Decrypt encrypted fields in a user document.

        Args:
            user: User document from MongoDB

        Returns:
            User document with decrypted fields
        """
        if not user:
            return user

        # Decrypt name and email fields
        if 'name' in user:
            decrypted_name = self.decrypt_field(user['name'])
            if decrypted_name:
                user['name'] = decrypted_name

        if 'email' in user:
            decrypted_email = self.decrypt_field(user['email'])
            if decrypted_email:
                user['email'] = decrypted_email

        return user

# Global instance
field_decryption = CloakDecryption()

def decrypt_message_content(message: Dict[str, Any]) -> Dict[str, Any]:
    """Convenience function to decrypt message content."""
    return field_decryption.decrypt_message_content(message)

def decrypt_user_fields(user: Dict[str, Any]) -> Dict[str, Any]:
    """Convenience function to decrypt user fields."""
    return field_decryption.decrypt_user_fields(user)

def decrypt_field(encrypted_value: str) -> Optional[str]:
    """Convenience function to decrypt a single field."""
    return field_decryption.decrypt_field(encrypted_value)

def is_encryption_available() -> bool:
    """Check if encryption keys are available."""
    return len(field_decryption.decryption_keys) > 0
