import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/next-auth/src/auth-options";
import db from "@/lib/shared-db";
import crypto from "crypto";
import { withPermission } from "@/lib/permission-middleware";

interface BulkGroupImportRequest {
  email: string;
  groupId?: string;
  groupName?: string;
  tenantId: string;
}

async function bulkImportGroupMember(request: NextRequest) {
  try {
    const session: any = await getServerSession(authOptions);
    
    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body: BulkGroupImportRequest = await request.json();
    const { email, groupId, groupName, tenantId } = body;

    // Validate required fields
    if (!email || !tenantId) {
      return NextResponse.json(
        { error: "Email and tenantId are required" },
        { status: 400 }
      );
    }

    // Validate group identifier
    if (!groupId && !groupName) {
      return NextResponse.json(
        { error: "Either groupId or groupName is required" },
        { status: 400 }
      );
    }

    // Find the group
    let group;
    if (groupId) {
      group = await db.group.findFirst({
        where: {
          id: groupId,
          tenantId,
        },
      });
    } else if (groupName) {
      group = await db.group.findFirst({
        where: {
          name: groupName,
          tenantId,
        },
      });
    }

    if (!group) {
      return NextResponse.json(
        { error: `Group not found: ${groupName || groupId}` },
        { status: 404 }
      );
    }

    // Find the user by email and verify they're a member of the organization
    const emailHash = crypto.createHash("sha256").update(email).digest("hex");
    const user = await db.user.findUnique({
      where: { emailHash },
      include: {
        membership: {
          where: { tenantId },
        },
      },
    });

    if (!user) {
      return NextResponse.json(
        { error: `User not found: ${email}` },
        { status: 404 }
      );
    }

    if (user.membership.length === 0) {
      return NextResponse.json(
        { error: `User ${email} is not a member of this organization` },
        { status: 400 }
      );
    }

    // Check if user is already a member of the group
    const existingGroupMember = await db.groupMember.findFirst({
      where: {
        groupId: group.id,
        userId: user.id,
      },
    });

    if (existingGroupMember) {
      return NextResponse.json(
        { error: `User ${email} is already a member of group ${group.name}` },
        { status: 400 }
      );
    }

    // Add user to the group
    const groupMember = await db.groupMember.create({
      data: {
        groupId: group.id,
        userId: user.id,
      },
    });

    // Get the user's membership for workspace access
    const memberShipId = user.membership[0].id;

    // Handle workspace access if the group has a custom role
    let groupWorkspaces = [];
    if (group.customRoleId) {
      groupWorkspaces = await db.customRoleWorkspace.findMany({
        where: {
          customRoleId: group.customRoleId,
        },
        include: {
          workspace: true,
        },
      });

      // For each workspace in the group, ensure the user has access
      for (const groupWorkspace of groupWorkspaces) {
        // Check if the user already has direct access to the workspace
        const existingWorkspaceMember = await db.workspaceMember.findFirst({
          where: {
            userId: user.id,
            workspaceId: (groupWorkspace as any)?.workspaceId,
          },
        });

        // If the user doesn't have direct access, create a workspace member entry
        if (!existingWorkspaceMember) {
          await db.workspaceMember.create({
            data: {
              userId: user.id,
              workspaceId: (groupWorkspace as any)?.workspaceId,
              membershipId: memberShipId,
              customRoleId: (groupWorkspace as any)?.customRoleId,
              role: "CUSTOM", // Default role for group-based access
            },
          });
        }
      }
    }

    return NextResponse.json({
      message: "User added to group successfully",
      data: {
        groupMemberId: groupMember.id,
        userId: user.id,
        email: user.email,
        name: user.name,
        groupId: group.id,
        groupName: group.name,
        workspacesGranted: groupWorkspaces.length,
      },
    });

  } catch (error) {
    console.error("Bulk group import error:", error);
    
    // Handle specific database errors
    if (error instanceof Error) {
      if (error.message.includes('Unique constraint')) {
        return NextResponse.json(
          { error: "User is already a member of this group" },
          { status: 400 }
        );
      }
    }

    return NextResponse.json(
      { error: "Failed to add user to group" },
      { status: 500 }
    );
  }
}

// Export with permission middleware
export const POST = withPermission(
  bulkImportGroupMember,
  "UPDATE",
  "WORKSPACE"
);
