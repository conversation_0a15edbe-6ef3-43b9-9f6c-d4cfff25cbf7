"use client";

import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  ReactNode,
} from "react";

// Define the languages we support
export type Language = "en" | "de";

// Create the context
interface LanguageContextType {
  language: Language;
  setLanguage: (language: Language) => void;
  t: (key: string, params?: Record<string, any>) => string;
  translations: Record<string, any>;
}

const LanguageContext = createContext<LanguageContextType | undefined>(
  undefined
);

// English translations (default)
import enTranslations from "../i18n/locales/en.json";
// German translations
import deTranslations from "../i18n/locales/de.json";
import { getCookie, setCookie } from "@/utils/cookies";

// Translations holder
const translationsMap: Record<Language, Record<string, any>> = {
  en: enTranslations,
  de: deTranslations,
};

// Language provider component
export function LanguageProvider({ children }: { children: ReactNode }) {
  const [language, setLanguageState] = useState<Language>("en");
  const [translations, setTranslations] =
    useState<Record<string, any>>(enTranslations);
  const [isLoaded, setIsLoaded] = useState(false);

  // Helper function to get nested translation
  const translate = (key: string, params?: Record<string, any>): string => {
    const keys = key.split(".");
    let result: any = translations;

    for (const k of keys) {
      if (result && typeof result === "object" && k in result) {
        result = result[k];
      } else {
        // Fallback to key if translation not found
        return key;
      }
    }

    if (typeof result === "string" && params) {
      // Replace all parameters in the string
      return Object.entries(params).reduce((str, [key, value]) => {
        return str.replace(new RegExp(`{${key}}`, "g"), String(`${value}`));
      }, result);
    }

    return typeof result === "string" ? result : key;
  };

  // Helper function to set language
  const setLanguage = (newLanguage: Language) => {
    // Save language preference
    if (typeof window !== "undefined") {
      setCookie("language", newLanguage);
    }

    // Update state
    setLanguageState(newLanguage);
    setTranslations(translationsMap[newLanguage]);
  };

  // Initialize language on mount
  useEffect(() => {
    const initLanguage = () => {
      let initialLanguage: Language = "en"; // Default

      // Check localStorage
      if (typeof window !== "undefined") {
        const savedLanguage = getCookie("language") as Language;
        if (
          savedLanguage &&
          (savedLanguage === "en" || savedLanguage === "de")
        ) {
          initialLanguage = savedLanguage;
        }
      }

      // Set initial state
      setLanguageState(initialLanguage);
      setTranslations(translationsMap[initialLanguage]);
      setIsLoaded(true);
    };

    initLanguage();
  }, []);

  // Don't render until translations are loaded
  if (!isLoaded) {
    return null;
  }

  return (
    <LanguageContext.Provider
      value={{
        language,
        setLanguage,
        t: translate,
        translations,
      }}
    >
      {children}
    </LanguageContext.Provider>
  );
}

// Hook to use the language context
export function useLanguage() {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error("useLanguage must be used within a LanguageProvider");
  }
  return context;
}
