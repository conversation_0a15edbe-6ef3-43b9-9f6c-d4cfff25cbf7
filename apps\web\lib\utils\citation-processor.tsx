import React from "react";
import { Source } from "@/components/wrapper-screens/chat/types";

export interface CitationMatch {
  text: string;
  index: number;
  start: number;
  end: number;
  isWeb: boolean;
  sourceIndex: number;
}

export interface ProcessedTextSegment {
  type: "text" | "citation";
  content: string;
  citationIndex?: number;
  isWeb?: boolean;
  sourceIndex?: number;
}

/**
 * Processes text content to identify and convert citation markers into clickable elements
 */
export class CitationProcessor {
  private documentSources: Source[];
  private webSources: Source[];
  private onCitationClick: (source: Source, highlightedText?: string) => void;

  constructor(
    sources: Source[],
    onCitationClick: (source: Source, highlightedText?: string) => void
  ) {
    // Separate document and web sources
    this.documentSources = sources.filter(
      (source) =>
        source.metadata?.source !== "web" &&
        source.metadata?.type !== "web_search"
    );
    this.webSources = sources.filter(
      (source) =>
        source.metadata?.source === "web" ||
        source.metadata?.type === "web_search"
    );
    this.onCitationClick = onCitationClick;
  }

  /**
   * Finds all citation markers in the text
   */
  private findCitations(text: string): CitationMatch[] {
    const citations: CitationMatch[] = [];

    // Pattern for document citations: [D1], [D2], etc.
    const docPattern = /\[D(\d+)\]/g;
    let docMatch;
    while ((docMatch = docPattern.exec(text)) !== null) {
      const citationNumber = parseInt(docMatch[1], 10);
      citations.push({
        text: docMatch[0],
        index: citationNumber,
        start: docMatch.index,
        end: docMatch.index + docMatch[0].length,
        isWeb: false,
        sourceIndex: citationNumber - 1, // Convert to 0-based index
      });
    }

    // Pattern for web citations: [W1], [W2], etc.
    const webPattern = /\[W(\d+)\]/g;
    let webMatch;
    while ((webMatch = webPattern.exec(text)) !== null) {
      const citationNumber = parseInt(webMatch[1], 10);
      citations.push({
        text: webMatch[0],
        index: citationNumber,
        start: webMatch.index,
        end: webMatch.index + webMatch[0].length,
        isWeb: true,
        sourceIndex: citationNumber - 1, // Convert to 0-based index
      });
    }

    // Legacy pattern for backward compatibility: [1], [2], etc. (treat as documents)
    const legacyPattern = /\[(\d+)\]/g;
    let legacyMatch;
    while ((legacyMatch = legacyPattern.exec(text)) !== null) {
      const citationNumber = parseInt(legacyMatch[1], 10);
      // Skip if this is already processed as D or W citation
      const beforeChar = text.substring(
        Math.max(0, legacyMatch.index - 1),
        legacyMatch.index
      );
      if (!beforeChar.includes("D") && !beforeChar.includes("W")) {
        citations.push({
          text: legacyMatch[0],
          index: citationNumber,
          start: legacyMatch.index,
          end: legacyMatch.index + legacyMatch[0].length,
          isWeb: false, // Default to document for legacy citations
          sourceIndex: citationNumber - 1, // Convert to 0-based index
        });
      }
    }

    // Sort by position in text
    return citations.sort((a, b) => a.start - b.start);
  }

  /**
   * Processes text and returns segments with citation information
   */
  public processText(text: string): ProcessedTextSegment[] {
    const citations = this.findCitations(text);

    if (citations.length === 0) {
      return [{ type: "text", content: text }];
    }

    const segments: ProcessedTextSegment[] = [];
    let lastEnd = 0;

    citations.forEach((citation) => {
      // Add text before citation
      if (citation.start > lastEnd) {
        segments.push({
          type: "text",
          content: text.substring(lastEnd, citation.start),
        });
      }

      // Add citation segment
      const displayNumber = citation.index; // Use the original citation number from text
      segments.push({
        type: "citation",
        content: displayNumber.toString(),
        citationIndex: displayNumber,
        isWeb: citation.isWeb,
        sourceIndex: citation.sourceIndex,
      });

      lastEnd = citation.end;
    });

    // Add remaining text
    if (lastEnd < text.length) {
      segments.push({
        type: "text",
        content: text.substring(lastEnd),
      });
    }

    return segments;
  }

  /**
   * Gets the source for a citation
   */
  public getSourceForCitation(
    citationIndex: number,
    isWeb: boolean,
    sourceIndex: number
  ): Source | null {
    if (isWeb) {
      return this.webSources[sourceIndex] || null;
    } else {
      return this.documentSources[sourceIndex] || null;
    }
  }

  /**
   * Renders processed text segments as React elements
   */
  public renderProcessedText(
    segments: ProcessedTextSegment[]
  ): React.ReactNode[] {
    return segments.map((segment, index) => {
      if (segment.type === "text") {
        return <span key={index}>{segment.content}</span>;
      } else {
        // Citation segment
        const source = this.getSourceForCitation(
          segment.citationIndex!,
          segment.isWeb!,
          segment.sourceIndex!
        );

        if (!source) {
          // Fallback if source not found
          return <span key={index}>[{segment.content}]</span>;
        }

        const sourceTitle =
          source.metadata?.title || source.metadata?.fileName || "Source";
        const sourceType = segment.isWeb ? "Web" : "Document";
        const sourcePreview =
          source.content?.substring(0, 150) +
          (source.content?.length > 150 ? "..." : "");
        const sourceUrl = segment.isWeb ? source.metadata?.link : null;

        // Create rich tooltip content
        const tooltipContent = `${sourceType}: ${sourceTitle}${
          sourceUrl ? `\n${sourceUrl}` : ""
        }\n\n${sourcePreview}`;

        return (
          <sup
            key={index}
            className={`
              inline-block cursor-pointer mx-0.5 px-1.5 py-0.5 mt-1 text-xs font-medium rounded-md
              transition-all duration-200 hover:scale-110 hover:shadow-md
              ${
                segment.isWeb
                  ? "bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 hover:bg-blue-200 dark:hover:bg-blue-900/50 border border-blue-200 dark:border-blue-800"
                  : "bg-emerald-100 dark:bg-emerald-900/30 text-emerald-700 dark:text-emerald-300 hover:bg-emerald-200 dark:hover:bg-emerald-900/50 border border-emerald-200 dark:border-emerald-800"
              }
            `}
            onClick={() => {
              // Extract relevant text for highlighting
              const relevantText =
                source.metadata?.relevantText ||
                source.content.substring(0, 200);
              this.onCitationClick(source, relevantText);
            }}
            title={tooltipContent}
            onMouseEnter={(e) => {
              // Enhanced hover effect
              e.currentTarget.style.transform = "scale(1.1)";
            }}
            onMouseLeave={(e) => {
              // Reset hover effect
              e.currentTarget.style.transform = "scale(1)";
            }}
          >
            {segment.isWeb
              ? `W${segment.citationIndex}`
              : `D${segment.citationIndex}`}
          </sup>
        );
      }
    });
  }
}

/**
 * Hook to use citation processing
 */
export const useCitationProcessor = (
  sources: Source[],
  onCitationClick: (source: Source, highlightedText?: string) => void
) => {
  const processor = new CitationProcessor(sources, onCitationClick);

  return {
    processText: (text: string) => processor.processText(text),
    renderText: (text: string) => {
      const segments = processor.processText(text);
      return processor.renderProcessedText(segments);
    },
  };
};
