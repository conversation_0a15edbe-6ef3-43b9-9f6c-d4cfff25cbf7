import axios from "axios";
import { apiUrl } from "..";
import { Plan } from "./plans";

import { StorageTierItem } from "./stripe";

export interface Subscription {
  id: string;
  tenantId: string;
  planId: string;
  plan?: Plan;
  startDate: string;
  endDate: string | null;
  isActive: boolean;
  isOnTrial?: boolean; // Whether the subscription is on a trial period
  trialEndDate?: string | null; // When the trial period ends
  additionalUsers: number;
  additionalStorageGB?: number; // Additional vector storage in GB (for backward compatibility)
  storageTierItems?: StorageTierItem[]; // Array of storage tier objects
  stripeSubscriptionId?: string; // Main Stripe subscription ID (for backward compatibility)
  stripePlanSubscriptionId?: string; // Stripe subscription ID for the base plan
  stripeUsersSubscriptionId?: string; // Stripe subscription ID for additional users
  stripeStorageSubscriptionId?: string; // Stripe subscription ID for additional storage
  stripeCustomerId?: string; // Stripe customer ID
  billingInterval?: string;
  createdAt: string;
  updatedAt: string;
}

export interface SubscriptionWithPlan extends Subscription {
  plan: Plan;
}

export interface SubscriptionResponse {
  subscription: SubscriptionWithPlan | null;
}

export interface CreateSubscriptionData {
  tenantId: string;
  planId: string;
  additionalUsers?: number;
  additionalStorageGB?: number;
}

export interface UpdateSubscriptionData {
  planId?: string;
  additionalUsers?: number;
  additionalStorageGB?: number;
  isActive?: boolean;
}

// Get active subscription for a tenant
export const getTenantSubscription = async (
  tenantId: string,
  userId: string
): Promise<SubscriptionWithPlan | null> => {
  try {
    const response = await axios.get(
      `${apiUrl}/subscriptions?tenantId=${tenantId}&userId=${userId}`
    );
    if (!response?.data?.subscription) {
      return null;
    }
    return response?.data?.subscription;
  } catch (error) {
    console.error(`Error fetching subscription for tenant ${tenantId}:`, error);
    throw error;
  }
};

// Get a specific subscription by ID
export const getSubscription = async (
  subscriptionId: string
): Promise<SubscriptionWithPlan> => {
  try {
    const response = await axios.get<SubscriptionResponse>(
      `${apiUrl}/subscriptions/${subscriptionId}`
    );
    return response.data.subscription as SubscriptionWithPlan;
  } catch (error) {
    console.error(`Error fetching subscription ${subscriptionId}:`, error);
    throw error;
  }
};

// Create a new subscription
export const createSubscription = async (
  data: CreateSubscriptionData
): Promise<SubscriptionWithPlan> => {
  try {
    const response = await axios.post<SubscriptionResponse>(
      `${apiUrl}/subscriptions`,
      data
    );
    return response.data.subscription as SubscriptionWithPlan;
  } catch (error) {
    console.error("Error creating subscription:", error);
    throw error;
  }
};

// Update a subscription
export const updateSubscription = async (
  subscriptionId: string,
  data: UpdateSubscriptionData
): Promise<SubscriptionWithPlan> => {
  try {
    const response = await axios.put<SubscriptionResponse>(
      `${apiUrl}/subscriptions/${subscriptionId}`,
      data
    );
    return response.data.subscription as SubscriptionWithPlan;
  } catch (error) {
    console.error(`Error updating subscription ${subscriptionId}:`, error);
    throw error;
  }
};

// Cancel a subscription
export const cancelSubscription = async (
  subscriptionId: string
): Promise<void> => {
  try {
    await axios.delete(`${apiUrl}/subscriptions/${subscriptionId}`);
  } catch (error) {
    console.error(`Error cancelling subscription ${subscriptionId}:`, error);
    throw error;
  }
};

// Upgrade a tenant's subscription to a new plan
export const upgradeTenantSubscription = async (
  tenantId: string,
  planId: string,
  userId: string,
  additionalUsers?: number,
  additionalStorageGB?: number
): Promise<SubscriptionWithPlan> => {
  try {
    // First check if there's an existing active subscription
    const currentSubscription = await getTenantSubscription(tenantId, userId);

    if (currentSubscription) {
      // Update the existing subscription
      return await updateSubscription(currentSubscription.id, {
        planId,
        additionalUsers,
        additionalStorageGB,
      });
    } else {
      // Create a new subscription
      return await createSubscription({
        tenantId,
        planId,
        additionalUsers,
        additionalStorageGB,
      });
    }
  } catch (error) {
    console.error(
      `Error upgrading subscription for tenant ${tenantId}:`,
      error
    );
    throw error;
  }
};
