"use client";

import { useState, useEffect } from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  AlertTriangle,
  TrendingUp,
  AlertCircle,
  DollarSign,
  Activity,
  BarChart3,
  Clock,
  User,
  Database,
  Zap,
  ExternalLink,
  CheckCircle,
  Mail,
  Settings
} from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import { EnhancedChart } from "./enhanced-chart";

interface Alert {
  id: string;
  type: "usage_spike" | "quota_risk" | "system_error" | "cost_alert";
  severity: "red" | "orange" | "yellow" | "green";
  title: string;
  description: string;
  tenantId?: string;
  tenantName?: string;
  timestamp: string;
  acknowledged: boolean;
}

interface AlertDetailModalProps {
  alert: Alert | null;
  isOpen: boolean;
  onClose: () => void;
  onAction: (action: "acknowledge" | "notify" | "configure") => void;
}

const severityConfig = {
  red: {
    color: "text-red-600",
    bg: "bg-red-50 dark:bg-red-950",
    border: "border-red-200 dark:border-red-800",
    badge: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",
  },
  orange: {
    color: "text-orange-600",
    bg: "bg-orange-50 dark:bg-orange-950",
    border: "border-orange-200 dark:border-orange-800",
    badge: "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200",
  },
  yellow: {
    color: "text-yellow-600",
    bg: "bg-yellow-50 dark:bg-yellow-950",
    border: "border-yellow-200 dark:border-yellow-800",
    badge: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",
  },
  green: {
    color: "text-green-600",
    bg: "bg-green-50 dark:bg-green-950",
    border: "border-green-200 dark:border-green-800",
    badge: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
  },
};

export function AlertDetailModal({ alert, isOpen, onClose, onAction }: AlertDetailModalProps) {
  const [chartData, setChartData] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (alert && isOpen) {
      fetchAlertData();
    }
  }, [alert, isOpen]);

  const fetchAlertData = async () => {
    if (!alert) return;
    
    setLoading(true);
    try {
      // Generate mock chart data based on alert type
      const mockData = generateMockChartData(alert);
      setChartData(mockData);
    } catch (error) {
      console.error("Error fetching alert data:", error);
    } finally {
      setLoading(false);
    }
  };

  const generateMockChartData = (alert: Alert) => {
    const days = 7; // Default to 7 days for demo
    const data: any[] = []; // ✅ Explicitly type as any[] to allow different object shapes
    
    for (let i = days - 1; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      const dateStr = date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
      
      if (alert.type === 'usage_spike') {
        // Token usage trend
        const baseValue = 1000 + Math.random() * 500;
        const spikeMultiplier = i === 0 ? 3 : 1; // Spike on latest day
        data.push({
          name: dateStr,
          tokens: Math.round(baseValue * spikeMultiplier),
          cost: Math.round(baseValue * spikeMultiplier * 0.002 * 100) / 100
        });
      } else if (alert.type === 'quota_risk') {
        // Storage usage trend
        const baseUsage = 70 + (7 - i) * 3; // Gradual increase
        data.push({
          name: dateStr,
          usage: Math.min(baseUsage + Math.random() * 5, 95),
          limit: 100
        });
      } else if (alert.type === 'system_error') {
        // Error rate trend
        const errorRate = i === 0 ? 15 + Math.random() * 10 : Math.random() * 3;
        data.push({
          name: dateStr,
          errors: Math.round(errorRate),
          requests: Math.round(100 + Math.random() * 50)
        });
      } else if (alert.type === 'cost_alert') {
        // Cost vs revenue trend
        const revenue = 500 + Math.random() * 100;
        const cost = revenue * (0.8 + Math.random() * 0.4); // Cost 80-120% of revenue
        data.push({
          name: dateStr,
          revenue: Math.round(revenue),
          cost: Math.round(cost)
        });
      }
    }
    
    return data;
  };

  if (!alert) return null;

  const severity = severityConfig[alert.severity];
  const getAlertIcon = () => {
    switch (alert.type) {
      case 'usage_spike': return TrendingUp;
      case 'quota_risk': return AlertTriangle;
      case 'system_error': return AlertCircle;
      case 'cost_alert': return DollarSign;
      default: return AlertCircle;
    }
  };

  const AlertIcon = getAlertIcon();

  const getChartConfig = (): {
    title: string;
    config: Record<string, { label: string; color: string }>;
    formatterType: "number" | "currency" | "storage" | "percentage";
  } => {
    switch (alert.type) {
      case 'usage_spike':
        return {
          title: "Token Usage Trend",
          config: {
            tokens: { label: "Tokens", color: "#3B82F6" },
            cost: { label: "Cost (CHF)", color: "#EF4444" }
          },
          formatterType: "number" as const
        };
      case 'quota_risk':
        return {
          title: "Storage Usage Trend",
          config: {
            usage: { label: "Usage %", color: "#F59E0B" },
            limit: { label: "Limit %", color: "#EF4444" }
          },
          formatterType: "percentage" as const
        };
      case 'system_error':
        return {
          title: "Error Rate Trend",
          config: {
            errors: { label: "Errors", color: "#EF4444" },
            requests: { label: "Total Requests", color: "#6B7280" }
          },
          formatterType: "number" as const
        };
      case 'cost_alert':
        return {
          title: "Cost vs Revenue Trend",
          config: {
            revenue: { label: "Revenue (CHF)", color: "#10B981" },
            cost: { label: "Cost (CHF)", color: "#EF4444" }
          },
          formatterType: "currency" as const
        };
      default:
        return {
          title: "Alert Data",
          config: {},
          formatterType: "number" as const
        };
    }
  };

  const chartConfig = getChartConfig();

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <div className="flex items-center space-x-3">
            <div className={`p-2 rounded-full ${severity.bg}`}>
              <AlertIcon className={`h-5 w-5 ${severity.color}`} />
            </div>
            <div>
              <DialogTitle className="flex items-center space-x-2">
                <span>{alert.title}</span>
                <Badge className={severity.badge}>
                  {alert.severity.toUpperCase()}
                </Badge>
                {alert.acknowledged && (
                  <Badge variant="secondary">Acknowledged</Badge>
                )}
              </DialogTitle>
              <DialogDescription className="mt-1">
                {alert.description}
              </DialogDescription>
            </div>
          </div>
        </DialogHeader>

        <Tabs defaultValue="overview" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="chart">Trend Analysis</TabsTrigger>
            <TabsTrigger value="actions">Actions</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm font-medium flex items-center space-x-2">
                    <User className="h-4 w-4" />
                    <span>Tenant Information</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Name:</span>
                    <span className="text-sm font-medium">{alert.tenantName || 'Unknown'}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">ID:</span>
                    <span className="text-sm font-mono">{alert.tenantId || 'N/A'}</span>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm font-medium flex items-center space-x-2">
                    <Clock className="h-4 w-4" />
                    <span>Alert Details</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Type:</span>
                    <span className="text-sm font-medium capitalize">{alert.type.replace('_', ' ')}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Triggered:</span>
                    <span className="text-sm">
                      {formatDistanceToNow(new Date(alert.timestamp), { addSuffix: true })}
                    </span>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="chart" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">{chartConfig.title}</CardTitle>
              </CardHeader>
              <CardContent>
                {loading ? (
                  <div className="h-64 flex items-center justify-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                  </div>
                ) : (
                  <EnhancedChart
                    data={chartData}
                    type="line"
                    height={300}
                    config={chartConfig.config}
                    formatterType={chartConfig.formatterType}
                    showLegend={true}
                  />
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="actions" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {!alert.acknowledged && (
                <Button
                  onClick={() => onAction("acknowledge")}
                  className="flex items-center space-x-2"
                >
                  <CheckCircle className="h-4 w-4" />
                  <span>Acknowledge Alert</span>
                </Button>
              )}
              
              <Button
                variant="outline"
                onClick={() => onAction("notify")}
                className="flex items-center space-x-2"
              >
                <Mail className="h-4 w-4" />
                <span>Notify Admin</span>
              </Button>
              
              <Button
                variant="outline"
                onClick={() => onAction("configure")}
                className="flex items-center space-x-2"
              >
                <Settings className="h-4 w-4" />
                <span>Configure Thresholds</span>
              </Button>
            </div>

            {alert.tenantId && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">Quick Navigation</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="w-full justify-start"
                    onClick={() => window.open(`/tenants/${alert.tenantId}`, '_blank')}
                  >
                    <ExternalLink className="h-4 w-4 mr-2" />
                    View Tenant Details
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="w-full justify-start"
                    onClick={() => window.open(`/tenants/${alert.tenantId}/usage`, '_blank')}
                  >
                    <BarChart3 className="h-4 w-4 mr-2" />
                    View Usage Analytics
                  </Button>
                </CardContent>
              </Card>
            )}
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
}
