"use client";

import { useState, useEffect, useCallback, useTransition } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { formatDate } from "@/lib/utils";
import { Eye, MoreHorizontal, Search, X } from "lucide-react";
import Link from "next/link";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { UsersClient } from "./users-client";

// Custom hook for debouncing values (same as in tenants)
function useDebounceLocal<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
}

// Types
interface User {
  id: string;
  name: string;
  email: string;
  tenantName: string;
  tenantId: string;
  role: string;
  lastActive: string;
  createdAt: string;
}

interface UsersByTenant {
  name: string;
  users: number;
}

interface UsersByRole {
  name: string;
  users: number;
}

interface UsersData {
  users: User[];
  usersByTenant: UsersByTenant[];
  usersByRole: UsersByRole[];
  allTenants: [],
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  searchQuery: string;
}

interface UsersTableClientProps {
  data: UsersData;
}

export function UsersTableClient({ data }: UsersTableClientProps) {
  const { users, usersByTenant, usersByRole, pagination, searchQuery, allTenants } = data;
  const router = useRouter();
  const searchParams = useSearchParams();
  const [isPending, startTransition] = useTransition();
  const [searchInput, setSearchInput] = useState(searchQuery);
  const [selectedTenantId, setSelectedTenantId] = useState<string | undefined>();

  // Debounce the search input to avoid excessive URL updates
  const debouncedSearchInput = useDebounceLocal(searchInput, 400);

  // Handle search with URL update
  const handleSearch = useCallback((query: string) => {
    const params = new URLSearchParams(searchParams?.toString());
    if (query.trim()) {
      params.set("search", query.trim());
    } else {
      params.delete("search");
    }
    params.set("page", "1"); // Reset to first page on search

    startTransition(() => {
      router.push(`/users?${params.toString()}`);
    });
  }, [searchParams, router]);

  // Effect to trigger search when debounced input changes
  useEffect(() => {
    // Only trigger search if the debounced value is different from current search query
    if (debouncedSearchInput !== searchQuery) {
      handleSearch(debouncedSearchInput);
    }
  }, [debouncedSearchInput, searchQuery, handleSearch]);

  // Handle pagination
  const handlePageChange = (newPage: number) => {
    const params = new URLSearchParams(searchParams?.toString());
    params.set("page", newPage.toString());

    startTransition(() => {
      router.push(`/users?${params.toString()}`);
    });
  };

  // Handle row click navigation
  const handleRowClick = (userId: string) => {
    startTransition(() => {
      router.push(`/users/${userId}`);
    });
  };

  // Handle clear search
  const handleClearSearch = () => {
    setSearchInput("");
    // The useEffect will handle the URL update when debouncedSearchInput changes
  };

  useEffect(() => {
    const tenantIdFromURL = searchParams?.get("tenant") || "";
    setSelectedTenantId(tenantIdFromURL);
  }, [searchParams]);

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold">Users</h1>
        <p className="text-muted-foreground">
          Manage all users across all tenants
        </p>
      </div>

      {/* Pass data to component for charts */}
      <UsersClient usersData={{ users: [], usersByTenant, usersByRole }} />

      {/* Users Table */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>
              All Users
              {selectedTenantId && (
                <span className="ml-2 text-sm font-normal text-muted-foreground">
                  (filtered by tenant)
                </span>
              )}
              {searchQuery && (
                <span className="ml-2 text-sm font-normal text-muted-foreground">
                  (filtered by "{searchQuery}")
                </span>
              )}
            </CardTitle>
            <div className="relative flex w-full max-w-2xl gap-2 justify-end">
              {/* Tenant Dropdown */}
              <div className="relative w-[180px]">
                <Select
                  value={selectedTenantId}
                  onValueChange={(value) => {
                    setSelectedTenantId(value);
                    const params = new URLSearchParams(searchParams?.toString());

                    if (value) {
                      params.set("tenant", value);
                    } else {
                      params.delete("tenant");
                    }

                    params.set("page", "1");

                    startTransition(() => {
                      router.push(`/users?${params.toString()}`);
                    });
                  }}
                >
                  <SelectTrigger className={`w-full ${selectedTenantId ? 'pr-9' : ''}`}>
                    <SelectValue placeholder="Select tenant" />
                  </SelectTrigger>
                  <SelectContent>
                    {allTenants.map((tenant: any) => (
                      <SelectItem key={tenant.id} value={tenant.id}>
                        {tenant.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                {selectedTenantId ? (
                  <button
                    type="button"
                    onClick={() => {
                      setSelectedTenantId("");
                      const params = new URLSearchParams(searchParams?.toString());
                      params.delete("tenant");
                      params.set("page", "1");

                      startTransition(() => {
                        router.push(`/users?${params.toString()}`);
                      });
                    }}
                    className="absolute right-2 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground"
                  >
                    <X className="h-4 w-4" />
                  </button>
                ) : null}
              </div>

              {/* Search Input */}
              <div className="relative">
                <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                <Input
                  placeholder="Search users, emails, tenants, roles..."
                  value={searchInput}
                  onChange={(e) => setSearchInput(e.target.value)}
                  className="w-full pl-9 pr-9"
                />
                {searchInput && (
                  <button
                    onClick={handleClearSearch}
                    className="absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground"
                    type="button"
                  >
                    <X className="h-4 w-4" />
                  </button>
                )}
              </div>
            </div>

          </div>
          {isPending && (
            <div className="mt-2 text-sm text-muted-foreground">
              Searching...
            </div>
          )}
        </CardHeader>
        <CardContent>
          {users.length === 0 ? (
            <div className="flex h-32 items-center justify-center">
              <p className="text-muted-foreground">
                {searchQuery ? "No users found matching your search." : "No users found."}
              </p>
            </div>
          ) : (
            <>
              <div className="overflow-x-auto">
                <table className="w-full text-sm">
                  <thead>
                    <tr className="border-b">
                      <th className="px-4 py-3 text-left font-medium">Name</th>
                      <th className="px-4 py-3 text-left font-medium">Email</th>
                      <th className="px-4 py-3 text-left font-medium">Tenant</th>
                      <th className="px-4 py-3 text-left font-medium">Role</th>
                      <th className="px-4 py-3 text-left font-medium">Created</th>
                      <th className="px-4 py-3 text-left font-medium">Last Active</th>
                      <th className="px-4 py-3 text-right font-medium">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {users.map((user) => (
                      <tr
                        key={user.id}
                        className="border-b cursor-pointer hover:bg-muted/50 transition-color"
                        role="button"
                        tabIndex={0}
                        aria-label={`View details for ${user.name}`}
                        onClick={() => handleRowClick(user.id)}
                        onKeyDown={(e) => {
                          if (e.key === 'Enter' || e.key === ' ') {
                            e.preventDefault();
                            handleRowClick(user.id);
                          }
                        }}
                      >
                        <td className="px-4 py-3">{user.name}</td>
                        <td className="px-4 py-3">{user.email}</td>
                        <td className="px-4 py-3">{user.tenantName}</td>
                        <td className="px-4 py-3">{user.role}</td>
                        <td className="px-4 py-3">{formatDate(user.createdAt)}</td>
                        <td className="px-4 py-3">{formatDate(user.lastActive)}</td>
                        <td className="px-4 py-3 text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button
                                variant="ghost"
                                size="icon"
                                onClick={(e) => e.stopPropagation()}
                                aria-label={`More actions for ${user.name}`}
                              >
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem
                                onClick={(e) => {
                                  e.stopPropagation();
                                }}
                                className="text-red-600 focus:text-red-600 focus:bg-red-50"
                              >
                                Delete
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {/* Pagination */}
              {pagination.totalPages > 1 && (
                <div className="mt-4 flex items-center justify-between">
                  <div className="text-sm text-muted-foreground">
                    Showing {(pagination.page - 1) * pagination.limit + 1} to{" "}
                    {Math.min(pagination.page * pagination.limit, pagination.total)} of{" "}
                    {pagination.total} results
                  </div>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePageChange(pagination.page - 1)}
                      disabled={pagination.page <= 1 || isPending}
                    >
                      Previous
                    </Button>
                    <div className="flex gap-1">
                      {Array.from({ length: Math.min(5, pagination.totalPages) }, (_, i) => {
                        const pageNum = Math.max(1, pagination.page - 2) + i;
                        if (pageNum > pagination.totalPages) return null;

                        return (
                          <Button
                            key={pageNum}
                            variant={pageNum === pagination.page ? "default" : "outline"}
                            size="sm"
                            onClick={() => handlePageChange(pageNum)}
                            disabled={isPending}
                            className="w-8"
                          >
                            {pageNum}
                          </Button>
                        );
                      })}
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePageChange(pagination.page + 1)}
                      disabled={pagination.page >= pagination.totalPages || isPending}
                    >
                      Next
                    </Button>
                  </div>
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
