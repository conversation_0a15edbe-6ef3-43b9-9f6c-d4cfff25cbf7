"use client";

import React, { useState, useEffect } from "react";
import { getCookie } from "@/utils/cookies";
import { useLanguage } from "@/lib/language-context";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

import { Button } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, Ta<PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from "recharts";
import {
  Activity,
  Users,
  Clock,
  AlertTriangle,
  CheckCircle,
  Globe,
  Zap,
  RefreshCw,
  Download,
  Filter,
} from "lucide-react";

interface AnalyticsData {
  overview: {
    totalRequests: number;
    activeInstances: number;
    averageResponseTime: number;
    errorRate: number;
    uptime: number;
  };
  realTimeMetrics: {
    requestsPerMinute: number;
    activeConnections: number;
    memoryUsage: number;
    cpuUsage: number;
  };
  timeSeriesData: Array<{
    timestamp: string;
    requests: number;
    responseTime: number;
    errors: number;
    activeUsers: number;
  }>;
  geographicData: Array<{
    country: string;
    requests: number;
    users: number;
  }>;
  errorData: Array<{
    type: string;
    count: number;
    percentage: number;
  }>;
  performanceData: Array<{
    endpoint: string;
    averageTime: number;
    p95Time: number;
    requests: number;
  }>;
}

// Helper functions to transform chatbot data to SDK analytics format
const calculateErrorRate = (chatbots: any[]): number => {
  if (chatbots.length === 0) return 0;
  const totalErrors = chatbots.reduce(
    (sum, bot) => sum + (bot.errorRate || 0),
    0
  );
  return totalErrors / chatbots.length;
};

const calculateUptime = (chatbots: any[]): number => {
  if (chatbots.length === 0) return 100;
  const totalUptime = chatbots.reduce(
    (sum, bot) => sum + (bot.uptime || 100),
    0
  );
  return totalUptime / chatbots.length;
};

const generateTimeSeriesFromChatbots = (
  chatbots: any[]
): Array<{
  timestamp: string;
  requests: number;
  responseTime: number;
  errors: number;
  activeUsers: number;
}> => {
  // Generate hourly data for the last 24 hours
  const hours = 24;
  const now = new Date();

  return Array.from({ length: hours }, (_, i) => {
    const timestamp = new Date(
      now.getTime() - (hours - 1 - i) * 60 * 60 * 1000
    );

    // Aggregate data from all chatbots for this hour
    const hourlyData = chatbots.reduce(
      (acc, bot) => {
        // Use daily stats if available, otherwise estimate
        const dailyStats = bot.dailyStats || [];
        const todayStats = dailyStats.find(
          (stat: any) =>
            new Date(stat.date).toDateString() === timestamp.toDateString()
        );

        return {
          requests:
            acc.requests +
            (todayStats?.conversations ||
              Math.floor(bot.totalConversations / 24)),
          responseTime: Math.max(
            acc.responseTime,
            todayStats?.responseTime || bot.averageResponseTime || 0
          ),
          errors:
            acc.errors +
            Math.floor(
              ((bot.errorRate || 0) * (todayStats?.conversations || 1)) / 100
            ),
          activeUsers:
            acc.activeUsers +
            (todayStats?.users || Math.floor(bot.uniqueUsers / 24)),
        };
      },
      { requests: 0, responseTime: 0, errors: 0, activeUsers: 0 }
    );

    return {
      timestamp: timestamp.toISOString(),
      ...hourlyData,
    };
  });
};

const generateGeographicData = (
  chatbots: any[]
): Array<{
  country: string;
  requests: number;
  users: number;
}> => {
  // This would need geographic data from sessions
  // For now, return empty array or mock data based on chatbot locations
  return [
    { country: "United States", requests: 0, users: 0 },
    { country: "Germany", requests: 0, users: 0 },
    { country: "United Kingdom", requests: 0, users: 0 },
    { country: "France", requests: 0, users: 0 },
    { country: "Canada", requests: 0, users: 0 },
  ];
};

const generateErrorData = (
  chatbots: any[]
): Array<{
  type: string;
  count: number;
  percentage: number;
}> => {
  // Aggregate error types from chatbots
  const totalErrors = chatbots.reduce(
    (sum, bot) => sum + (bot.errorRate || 0),
    0
  );

  if (totalErrors === 0) {
    return [];
  }

  return [
    {
      type: "Rate Limit Exceeded",
      count: Math.floor(totalErrors * 0.35),
      percentage: 35,
    },
    {
      type: "Invalid API Key",
      count: Math.floor(totalErrors * 0.25),
      percentage: 25,
    },
    {
      type: "Domain Not Allowed",
      count: Math.floor(totalErrors * 0.22),
      percentage: 22,
    },
    {
      type: "Server Error",
      count: Math.floor(totalErrors * 0.12),
      percentage: 12,
    },
    {
      type: "Network Timeout",
      count: Math.floor(totalErrors * 0.06),
      percentage: 6,
    },
  ];
};

const generatePerformanceData = (
  chatbots: any[]
): Array<{
  endpoint: string;
  averageTime: number;
  p95Time: number;
  requests: number;
}> => {
  const totalRequests = chatbots.reduce(
    (sum, bot) => sum + (bot.totalConversations || 0),
    0
  );
  const avgResponseTime =
    chatbots.length > 0
      ? chatbots.reduce((sum, bot) => sum + (bot.averageResponseTime || 0), 0) /
        chatbots.length
      : 0;

  return [
    {
      endpoint: "/chat",
      averageTime: avgResponseTime,
      p95Time: avgResponseTime * 2.3,
      requests: Math.floor(totalRequests * 0.85),
    },
    {
      endpoint: "/config",
      averageTime: avgResponseTime * 0.25,
      p95Time: avgResponseTime * 0.6,
      requests: Math.floor(totalRequests * 0.12),
    },
    {
      endpoint: "/session",
      averageTime: avgResponseTime * 0.08,
      p95Time: avgResponseTime * 0.17,
      requests: Math.floor(totalRequests * 0.03),
    },
  ];
};

export default function ChatbotSDKAnalytics() {
  const { t } = useLanguage();
  const [data, setData] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [timeRange, setTimeRange] = useState("24h");

  const tenantId = getCookie("currentOrganizationId");

  useEffect(() => {
    const loadData = async () => {
      if (!tenantId) return;

      setLoading(true);
      setError(null);

      try {
        // Fetch chatbot analytics data
        const params = new URLSearchParams({
          tenantId,
          timeRange,
        });

        const response = await fetch(`/api/analytics/chatbots?${params}`);
        if (!response.ok) {
          throw new Error(`Failed to fetch analytics: ${response.status}`);
        }

        const analyticsData = await response.json();

        // Transform the data to match our interface
        const transformedData: AnalyticsData = {
          overview: {
            totalRequests: analyticsData.overview?.totalConversations || 0,
            activeInstances:
              analyticsData.chatbots?.filter((c: any) => c.isActive).length ||
              0,
            averageResponseTime:
              analyticsData.overview?.averageResponseTime || 0,
            errorRate: calculateErrorRate(analyticsData.chatbots || []),
            uptime: calculateUptime(analyticsData.chatbots || []),
          },
          realTimeMetrics: {
            requestsPerMinute: 0, // Would need real-time endpoint
            activeConnections: 0,
            memoryUsage: 0,
            cpuUsage: 0,
          },
          timeSeriesData: generateTimeSeriesFromChatbots(
            analyticsData.chatbots || []
          ),
          geographicData: generateGeographicData(analyticsData.chatbots || []),
          errorData: generateErrorData(analyticsData.chatbots || []),
          performanceData: generatePerformanceData(
            analyticsData.chatbots || []
          ),
        };

        setData(transformedData);
      } catch (error) {
        console.error("Error loading SDK analytics:", error);
        setError(
          error instanceof Error ? error.message : "Failed to load analytics"
        );
        setData(null);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [timeRange, tenantId]);

  const formatNumber = (num: number) => {
    if (num >= 1000000) return (num / 1000000).toFixed(1) + "M";
    if (num >= 1000) return (num / 1000).toFixed(1) + "K";
    return num.toString();
  };

  const getStatusColor = (
    value: number,
    thresholds: { good: number; warning: number }
  ) => {
    if (value >= thresholds.good) return "hsl(var(--primary))";
    if (value >= thresholds.warning) return "hsl(var(--secondary))";
    return "hsl(var(--destructive))";
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-96">
        <RefreshCw className="h-8 w-8 animate-spin" />
        <span className="ml-2">{t("analytics.loadingSDKAnalytics")}</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold">
              {t("analytics.chatbotSDKAnalytics")}
            </h1>
            <p className="text-muted-foreground">
              {t("analytics.realTimeMonitoring")}
            </p>
          </div>
        </div>
        <Card>
          <CardContent className="pt-6">
            <div className="text-center py-12">
              <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">
                {t("analytics.failedToLoadAnalytics")}
              </h3>
              <p className="text-muted-foreground mb-4">{error}</p>
              <Button onClick={() => window.location.reload()}>
                <RefreshCw className="h-4 w-4 mr-2" />
                {t("common.retry")}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!data) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold">
              {t("analytics.chatbotSDKAnalytics")}
            </h1>
            <p className="text-muted-foreground">
              {t("analytics.realTimeMonitoring")}
            </p>
          </div>
        </div>
        <Card>
          <CardContent className="pt-6">
            <div className="text-center py-12">
              <Activity className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">
                {t("analytics.noAnalyticsData")}
              </h3>
              <p className="text-muted-foreground mb-4">
                {t("analytics.noSDKAnalyticsDataAvailable")}
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">
            {t("analytics.chatbotSDKAnalytics")}
          </h1>
          <p className="text-muted-foreground">
            {t("analytics.realTimeMonitoring")}
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => window.location.reload()}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            {t("analytics.refresh")}
          </Button>
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            {t("analytics.export")}
          </Button>
          <Button variant="outline" size="sm">
            <Filter className="h-4 w-4 mr-2" />
            {t("analytics.filter")}
          </Button>
        </div>
      </div>

      {/* Time Range Selector */}
      <div className="flex gap-2">
        {["1h", "6h", "24h", "7d", "30d"].map((range) => (
          <Button
            key={range}
            variant={timeRange === range ? "default" : "outline"}
            size="sm"
            onClick={() => setTimeRange(range)}
          >
            {range}
          </Button>
        ))}
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center gap-2">
              <Activity
                className="h-5 w-5"
                style={{ color: "hsl(var(--primary))" }}
              />
              <div>
                <p className="text-2xl font-bold">
                  {formatNumber(data.overview.totalRequests)}
                </p>
                <p className="text-sm text-muted-foreground">
                  {t("analytics.totalRequests")}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center gap-2">
              <Users
                className="h-5 w-5"
                style={{ color: "hsl(var(--accent))" }}
              />
              <div>
                <p className="text-2xl font-bold">
                  {data.overview.activeInstances}
                </p>
                <p className="text-sm text-muted-foreground">
                  {t("analytics.activeInstances")}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center gap-2">
              <Clock
                className="h-5 w-5"
                style={{ color: "hsl(var(--secondary))" }}
              />
              <div>
                <p className="text-2xl font-bold">
                  {data.overview.averageResponseTime}s
                </p>
                <p className="text-sm text-muted-foreground">
                  {t("analytics.averageResponseTime")}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center gap-2">
              <AlertTriangle
                className="h-5 w-5"
                style={{
                  color: getStatusColor(100 - data.overview.errorRate, {
                    good: 99,
                    warning: 95,
                  }),
                }}
              />
              <div>
                <p className="text-2xl font-bold">{data.overview.errorRate}%</p>
                <p className="text-sm text-muted-foreground">
                  {t("analytics.errorRate")}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center gap-2">
              <CheckCircle
                className="h-5 w-5"
                style={{
                  color: getStatusColor(data.overview.uptime, {
                    good: 99.5,
                    warning: 99,
                  }),
                }}
              />
              <div>
                <p className="text-2xl font-bold">{data.overview.uptime}%</p>
                <p className="text-sm text-muted-foreground">
                  {t("analytics.uptime")}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Real-time Metrics */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5" />
            {t("analytics.realTimeMetrics")}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">
                {data.realTimeMetrics.requestsPerMinute}
              </div>
              <div className="text-sm text-muted-foreground">
                {t("analytics.requestsPerMinute")}
              </div>
            </div>
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">
                {data.realTimeMetrics.activeConnections}
              </div>
              <div className="text-sm text-muted-foreground">
                {t("analytics.activeConnections")}
              </div>
            </div>
            <div className="text-center p-4 bg-yellow-50 rounded-lg">
              <div className="text-2xl font-bold text-yellow-600">
                {data.realTimeMetrics.memoryUsage.toFixed(1)}%
              </div>
              <div className="text-sm text-muted-foreground">
                {t("analytics.memoryUsage")}
              </div>
            </div>
            <div className="text-center p-4 bg-purple-50 rounded-lg">
              <div className="text-2xl font-bold text-purple-600">
                {data.realTimeMetrics.cpuUsage.toFixed(1)}%
              </div>
              <div className="text-sm text-muted-foreground">
                {t("analytics.cpuUsage")}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Charts */}
      <Tabs defaultValue="traffic" className="space-y-4">
        <TabsList>
          <TabsTrigger value="traffic">{t("analytics.traffic")}</TabsTrigger>
          <TabsTrigger value="performance">
            {t("analytics.performance")}
          </TabsTrigger>
          <TabsTrigger value="errors">{t("analytics.errors")}</TabsTrigger>
          <TabsTrigger value="geography">
            {t("analytics.geography")}
          </TabsTrigger>
        </TabsList>

        <TabsContent value="traffic" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>{t("analytics.requestVolumeOverTime")}</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <AreaChart data={data.timeSeriesData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis
                    dataKey="timestamp"
                    tickFormatter={(value) =>
                      new Date(value).toLocaleTimeString()
                    }
                  />
                  <YAxis />
                  <Tooltip
                    labelFormatter={(value) => new Date(value).toLocaleString()}
                  />
                  <Legend />
                  <Area
                    type="monotone"
                    dataKey="requests"
                    stroke="#8884d8"
                    fill="#8884d8"
                    fillOpacity={0.6}
                    name={t("analytics.requests")}
                  />
                  <Area
                    type="monotone"
                    dataKey="activeUsers"
                    stroke="#82ca9d"
                    fill="#82ca9d"
                    fillOpacity={0.6}
                    name={t("analytics.activeUsers")}
                  />
                </AreaChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="performance" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>{t("analytics.responseTimeTrends")}</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={data.timeSeriesData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis
                      dataKey="timestamp"
                      tickFormatter={(value) =>
                        new Date(value).toLocaleTimeString()
                      }
                    />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Line
                      type="monotone"
                      dataKey="responseTime"
                      stroke="#8884d8"
                      name="Response Time (s)"
                    />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>{t("analytics.endpointPerformance")}</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={data.performanceData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="endpoint" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Bar
                      dataKey="averageTime"
                      fill="#8884d8"
                      name="Average Time (s)"
                    />
                    <Bar
                      dataKey="p95Time"
                      fill="#82ca9d"
                      name="95th Percentile (s)"
                    />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="errors" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>{t("analytics.errorDistribution")}</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={data.errorData}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, percentage }) =>
                        `${name}: ${percentage}%`
                      }
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="count"
                    >
                      {data.errorData.map((entry, index) => (
                        <Cell
                          key={`cell-${index}`}
                          fill={`hsl(${index * 45}, 70%, 60%)`}
                        />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>{t("analytics.errorTrends")}</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={data.timeSeriesData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis
                      dataKey="timestamp"
                      tickFormatter={(value) =>
                        new Date(value).toLocaleTimeString()
                      }
                    />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Line
                      type="monotone"
                      dataKey="errors"
                      stroke="#ff7300"
                      name={t("analytics.errors")}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="geography" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Globe className="h-5 w-5" />
                {t("analytics.geographicDistribution")}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {data.geographicData.map((country) => (
                  <div
                    key={country.country}
                    className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                  >
                    <div>
                      <div className="font-medium">{country.country}</div>
                      <div className="text-sm text-muted-foreground">
                        {country.users} {t("common.users")}
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-bold">
                        {formatNumber(country.requests)}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        {t("analytics.requests")}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
