import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { formatDate, formatTime } from "@/lib/utils";
import {
  ArrowRight,
  CheckCircle2,
  Clock,
  XCircle,
  User,
  LogIn,
  Activity,
  MoreHorizontal,
} from "lucide-react";
import Link from "next/link";
import { cn } from "@/lib/utils";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, TabsList, TabsTrigger } from "@/components/ui/tabs";

interface ApiRequest {
  id: string;
  timestamp: string;
  endpoint: string;
  method: string;
  statusCode: number;
  success: boolean;
  tenantName: string;
  userName: string;
}

interface UserLogin {
  id: string;
  name: string;
  email: string;
  lastLogin: string;
  tenantName: string;
}

interface RecentActivityProps {
  apiRequests: ApiRequest[];
  userLogins: UserLogin[];
}

export function RecentActivity({ apiRequests, userLogins }: RecentActivityProps) {
  const getStatusIcon = (success: boolean) => {
    return success ? (
      <CheckCircle2 className="h-4 w-4 text-green-500" />
    ) : (
      <XCircle className="h-4 w-4 text-red-500" />
    );
  };

  const getStatusColor = (success: boolean) => {
    return success
      ? "text-green-600 bg-green-50 dark:text-green-400 dark:bg-green-950"
      : "text-red-600 bg-red-50 dark:text-red-400 dark:bg-red-950";
  };

  const getMethodColor = (method: string) => {
    switch (method.toUpperCase()) {
      case "GET":
        return "text-blue-600 bg-blue-50 dark:text-blue-400 dark:bg-blue-950";
      case "POST":
        return "text-green-600 bg-green-50 dark:text-green-400 dark:bg-green-950";
      case "PUT":
        return "text-amber-600 bg-amber-50 dark:text-amber-400 dark:bg-amber-950";
      case "DELETE":
        return "text-red-600 bg-red-50 dark:text-red-400 dark:bg-red-950";
      default:
        return "text-gray-600 bg-gray-50 dark:text-gray-400 dark:bg-gray-950";
    }
  };

  return (
    <Card className="col-span-full overflow-hidden">
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <CardTitle className="text-base font-medium">Recent Activity</CardTitle>
        <Button variant="ghost" size="icon" className="h-8 w-8 rounded-full">
          <MoreHorizontal className="h-4 w-4" />
          <span className="sr-only">More options</span>
        </Button>
      </CardHeader>
      <CardContent className="p-0">
        <Tabs defaultValue="api" className="w-full">
          <TabsList className="grid w-full grid-cols-2 rounded-none border-b bg-transparent px-4">
            <TabsTrigger
              value="api"
              className="data-[state=active]:bg-transparent data-[state=active]:shadow-none"
            >
              <Activity className="mr-2 h-4 w-4" />
              API Requests
            </TabsTrigger>
            <TabsTrigger
              value="logins"
              className="data-[state=active]:bg-transparent data-[state=active]:shadow-none"
            >
              <LogIn className="mr-2 h-4 w-4" />
              User Logins
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="api" className="m-0">
            <div className="divide-y">
              {apiRequests.map((request) => (
                <div
                  key={request.id}
                  className="flex items-center gap-4 px-6 py-3 hover:bg-muted/50"
                >
                  <div className="flex h-10 w-10 items-center justify-center rounded-full bg-blue-100 dark:bg-blue-950">
                    <Activity className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                  </div>
                  <div className="flex-1 space-y-1">
                    <div className="flex items-center gap-2">
                      <p className="text-sm font-medium truncate max-w-[200px]">
                        {request.endpoint}
                      </p>
                      <span
                        className={cn(
                          "inline-flex items-center gap-1 rounded-full px-2 py-0.5 text-xs font-medium",
                          getStatusColor(request.success)
                        )}
                      >
                        {getStatusIcon(request.success)}
                        {request.statusCode}
                      </span>
                      <span
                        className={cn(
                          "inline-flex items-center rounded-full px-2 py-0.5 text-xs font-medium",
                          getMethodColor(request.method)
                        )}
                      >
                        {request.method}
                      </span>
                    </div>
                    <div className="flex items-center gap-4">
                      <p className="text-xs text-muted-foreground">
                        {formatDate(request.timestamp)} {formatTime(request.timestamp)}
                      </p>
                      <p className="text-xs text-muted-foreground">
                        {request.userName} ({request.tenantName})
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </TabsContent>
          
          <TabsContent value="logins" className="m-0">
            <div className="divide-y">
              {userLogins.map((login) => (
                <div
                  key={login.id}
                  className="flex items-center gap-4 px-6 py-3 hover:bg-muted/50"
                >
                  <div className="flex h-10 w-10 items-center justify-center rounded-full bg-indigo-100 dark:bg-indigo-950">
                    <User className="h-5 w-5 text-indigo-600 dark:text-indigo-400" />
                  </div>
                  <div className="flex-1 space-y-1">
                    <div className="flex items-center gap-2">
                      <p className="text-sm font-medium">{login.name}</p>
                      <span className="inline-flex items-center gap-1 rounded-full bg-green-50 px-2 py-0.5 text-xs font-medium text-green-600 dark:bg-green-950 dark:text-green-400">
                        <LogIn className="h-3 w-3" />
                        Login
                      </span>
                    </div>
                    <div className="flex items-center gap-4">
                      <p className="text-xs text-muted-foreground">
                        {formatDate(login.lastLogin)} {formatTime(login.lastLogin)}
                      </p>
                      <p className="text-xs text-muted-foreground">
                        {login.email} ({login.tenantName})
                      </p>
                    </div>
                  </div>
                  <Link href={`/users/${login.id}`}>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-8 w-8 rounded-full"
                    >
                      <ArrowRight className="h-4 w-4" />
                      <span className="sr-only">View user</span>
                    </Button>
                  </Link>
                </div>
              ))}
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
