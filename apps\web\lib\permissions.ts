import { PermissionAction, PermissionResource } from "@prisma/client";
import db from "@/lib/shared-db";

/**
 * Check if a user has a specific permission for a resource
 * @param userId - The ID of the user
 * @param tenantId - The ID of the tenant
 * @param action - The action to check (CREATE, READ, UPDATE, DELETE)
 * @param resource - The resource to check (WORKSPACE, PAGE, FOLDER, FILE, MEMBER)
 * @param resourceIdOrSlug - Optional ID or slug of the specific resource
 * @returns Promise<boolean> - Whether the user has the permission
 */
export async function hasPermission(
  userId: string,
  tenantId: string,
  action: PermissionAction,
  resource: PermissionResource,
  resourceIdOrSlug?: string
): Promise<boolean> {
  try {
    // First, determine if we're working with an ID or a slug
    let resourceId: string | undefined = undefined;
    let workspaceId: string | undefined = undefined;

    if (resourceIdOrSlug) {
      // Check if the provided value is a slug (contains non-ObjectId characters)
      const isSlug = /[^a-f0-9]/i.test(resourceIdOrSlug);

      if (isSlug) {
        // If it's a workspace slug, find the workspace by slug
        const workspace = await db.workspace.findFirst({
          where: {
            slug: resourceIdOrSlug,
            tenantId,
          },
          select: { id: true },
        });

        if (workspace) {
          resourceId = workspace.id;
          workspaceId = workspace.id;
        }
      } else {
        // If it's not a slug or not a workspace resource, use it as is
        resourceId = resourceIdOrSlug;

        // For WORKSPACE resource, workspaceId is the same as resourceId
        if (resource === "WORKSPACE") {
          workspaceId = resourceId;
        }
      }
    }

    // If we have a workspaceId, check workspace membership first
    if (workspaceId) {
      // Check direct workspace membership
      const workspaceMember = await db.workspaceMember.findFirst({
        where: {
          userId,
          workspaceId,
        },
        include: {
          customRole: {
            include: {
              permissions: {
                include: {
                  permission: true,
                },
              },
            },
          },
        },
      });

      if (workspaceMember) {
        // Workspace owners have all permissions for their workspace
        if (workspaceMember.role === "OWNER") {
          return true;
        }

        // For workspace admins, check built-in role configuration
        if (workspaceMember.role === "ADMIN") {
          // Check if there's a custom built-in role configuration for ADMIN
          const builtInRoleConfig = await db.builtInRoleConfig.findFirst({
            where: {
              tenantId,
              roleType: "ADMIN",
            },
            include: {
              permissions: {
                include: {
                  permission: true,
                },
              },
            },
          });

          if (builtInRoleConfig) {
            // Use configured permissions for ADMIN role
            const hasRequiredPermission = builtInRoleConfig.permissions.some(
              (p: any) =>
                p.permission.action === action && p.permission.resource === resource
            );
            return hasRequiredPermission;
          } else {
            // Fall back to default ADMIN permissions (all except owner-only)
            const ownerOnlyPermissions = [
              {
                action: "DELETE" as PermissionAction,
                resource: "WORKSPACE" as PermissionResource,
              },
            ];

            const isOwnerOnly = ownerOnlyPermissions.some(
              (p) => p.action === action && p.resource === resource
            );

            if (!isOwnerOnly) {
              return true;
            }
          }
        }

        // Check custom role permissions for the workspace
        if (workspaceMember.customRole) {
          const hasRequiredPermission =
            workspaceMember.customRole.permissions.some(
              (p: any) =>
                p.permission.action === action &&
                p.permission.resource === resource
            );
          if (hasRequiredPermission) {
            return true;
          }
          return false;
        }
      }

      // If no direct workspace membership, check for group-based access through workspace associations
      const userGroups = await db.groupMember.findMany({
        where: {
          userId,
        },
        include: {
          group: {
            include: {
              groupWorkspaces: {
                where: {
                  workspaceId,
                },
                include: {
                  customRole: {
                    include: {
                      permissions: {
                        include: {
                          permission: true,
                        },
                      },
                    },
                  },
                },
              },
              customRole: {
                include: {
                  permissions: {
                    include: {
                      permission: true,
                    },
                  },
                },
              },
            },
          },
        },
      });

      // Check if any of the user's groups have access to this specific workspace
      for (const groupMember of userGroups) {
        // First check if the group has a workspace-specific role for this workspace
        for (const groupWorkspace of groupMember.group.groupWorkspaces) {
          if (groupWorkspace.customRole) {
            const hasRequiredPermission =
              groupWorkspace.customRole.permissions.some(
                (p: any) =>
                  p.permission.action === action &&
                  p.permission.resource === resource
              );

            if (hasRequiredPermission) {
              return true;
            }
          }
        }

        // If no workspace-specific role, check the group's general custom role
        // but only if the group has access to this workspace
        if (groupMember.group.groupWorkspaces.length > 0 && groupMember.group.customRole) {
          const hasRequiredPermission =
            groupMember.group.customRole.permissions.some(
              (p: any) =>
                p.permission.action === action &&
                p.permission.resource === resource
            );

          if (hasRequiredPermission) {
            return true;
          }
        }
      }
    }

    // If no workspace-specific permission found, fall back to tenant-level permissions
    // Check if the user is a member of the tenant
    const membership = await db.membership.findFirst({
      where: {
        userId,
        tenantId,
      },
      include: {
        customRole: {
          include: {
            permissions: {
              include: {
                permission: true,
              },
            },
          },
        },
      },
    });

    if (!membership) {
      return false;
    }

    // If the user is an OWNER, they have all permissions
    if (membership.role === "OWNER") {
      return true;
    }

    // If the user is an ADMIN, check built-in role configuration first
    if (membership.role === "ADMIN") {
      // Check if there's a custom built-in role configuration for ADMIN
      const builtInRoleConfig = await db.builtInRoleConfig.findFirst({
        where: {
          tenantId,
          roleType: "ADMIN",
        },
        include: {
          permissions: {
            include: {
              permission: true,
            },
          },
        },
      });

      if (builtInRoleConfig) {
        // Use configured permissions for ADMIN role
        const hasRequiredPermission = builtInRoleConfig.permissions.some(
          (p: any) =>
            p.permission.action === action && p.permission.resource === resource
        );
        return hasRequiredPermission;
      } else {
        // Fall back to default ADMIN permissions (all except owner-only)
        const ownerOnlyPermissions = [
          {
            action: "DELETE" as PermissionAction,
            resource: "WORKSPACE" as PermissionResource,
          },
        ];

        const isOwnerOnly = ownerOnlyPermissions.some(
          (p) => p.action === action && p.resource === resource
        );

        if (!isOwnerOnly) {
          return true;
        }
      }
    }

    // If the user has a custom role, check if it has the required permission
    if (membership.customRole) {
      const hasRequiredPermission = membership.customRole.permissions.some(
        (p: any) =>
          p.permission.action === action && p.permission.resource === resource
      );

      if (hasRequiredPermission) {
        return true;
      }
    } else if (membership.role === "MEMBER") {
      // Check if there's a custom built-in role configuration for MEMBER
      const builtInRoleConfig = await db.builtInRoleConfig.findFirst({
        where: {
          tenantId,
          roleType: "MEMBER",
        },
        include: {
          permissions: {
            include: {
              permission: true,
            },
          },
        },
      });

      if (builtInRoleConfig) {
        // Use configured permissions for MEMBER role
        const hasRequiredPermission = builtInRoleConfig.permissions.some(
          (p: any) =>
            p.permission.action === action && p.permission.resource === resource
        );
        return hasRequiredPermission;
      } else {
        // Fall back to default MEMBER permissions (read-only)
        const defaultMemberPermissions = [
          { action: "READ", resource: "WORKSPACE" },
          { action: "READ", resource: "PAGE" },
          { action: "READ", resource: "FOLDER" },
          { action: "READ", resource: "FILE" },
          { action: "READ", resource: "MEMBER" },
        ];

        const hasDefaultPermission = defaultMemberPermissions.some(
          (p) => p.action === action && p.resource === resource
        );
        return hasDefaultPermission;
      }
    }

    // Check group-based permissions for tenant-level access
    const userGroups = await db.groupMember.findMany({
      where: {
        userId,
      },
      include: {
        group: {
          include: {
            groupWorkspaces: {
              include: {
                customRole: {
                  include: {
                    permissions: {
                      include: {
                        permission: true,
                      },
                    },
                  },
                },
              },
            },
            customRole: {
              include: {
                permissions: {
                  include: {
                    permission: true,
                  },
                },
              },
            },
          },
        },
      },
    });

    // Check permissions from group roles
    for (const groupMember of userGroups) {
      // Check workspace-specific group permissions
      for (const groupWorkspace of groupMember.group.groupWorkspaces) {
        if (groupWorkspace.customRole) {
          const hasRequiredPermission = groupWorkspace.customRole.permissions.some(
            (p: any) =>
              p.permission.action === action && p.permission.resource === resource
          );

          if (hasRequiredPermission) {
            return true;
          }
        }
      }

      // Check general group permissions
      if (groupMember.group.customRole) {
        const hasRequiredPermission = groupMember.group.customRole.permissions.some(
          (p: any) =>
            p.permission.action === action && p.permission.resource === resource
        );

        if (hasRequiredPermission) {
          return true;
        }
      }
    }

    // Default to no permission
    return false;
  } catch (error) {
    console.error("Error checking permission:", error);
    return false;
  }
}

/**
 * Get all permissions for a user in a tenant
 * @param userId - The ID of the user
 * @param tenantId - The ID of the tenant
 * @param workspaceIdOrSlug - Optional workspace ID or slug to get workspace-specific permissions
 * @returns Promise<Array<{action: PermissionAction, resource: PermissionResource}>> - List of permissions
 */
export async function getUserPermissions(
  userId: string,
  tenantId: string,
  workspaceIdOrSlug?: string
): Promise<Array<{ action: PermissionAction; resource: PermissionResource }>> {
  try {
    let userPermissions: Array<{
      action: PermissionAction;
      resource: PermissionResource;
    }> = [];

    // Determine if we're working with a workspaceId or workspaceSlug
    let workspaceId: string | undefined = undefined;

    if (workspaceIdOrSlug) {
      // Check if the provided value is a slug (contains non-ObjectId characters)
      const isSlug = /[^a-f0-9]/i.test(workspaceIdOrSlug);

      if (isSlug) {
        // If it's a slug, find the workspace by slug
        const workspace = await db.workspace.findFirst({
          where: {
            slug: workspaceIdOrSlug,
            tenantId,
          },
          select: { id: true },
        });

        if (workspace) {
          workspaceId = workspace.id;
        }
      } else {
        // If it's not a slug, assume it's already a workspaceId
        workspaceId = workspaceIdOrSlug;
      }
    }

    // If workspaceId is resolved, check workspace membership first
    if (workspaceId) {
      const workspaceMember = await db.workspaceMember.findFirst({
        where: {
          userId,
          workspaceId,
        },
        include: {
          customRole: {
            include: {
              permissions: {
                include: {
                  permission: true,
                },
              },
            },
          },
        },
      });

      if (workspaceMember) {
        // Workspace owners have all permissions for their workspace
        if (workspaceMember.role === "OWNER") {
          const allPermissions = await db.permission.findMany();
          return allPermissions.map((p: any) => ({
            action: p.action,
            resource: p.resource,
          }));
        }

        // For workspace admins, check built-in role configuration
        if (workspaceMember.role === "ADMIN") {
          // Check if there's a custom built-in role configuration for ADMIN
          const builtInRoleConfig = await db.builtInRoleConfig.findFirst({
            where: {
              tenantId,
              roleType: "ADMIN",
            },
            include: {
              permissions: {
                include: {
                  permission: true,
                },
              },
            },
          });

          if (builtInRoleConfig) {
            // Use configured permissions for ADMIN role
            return builtInRoleConfig.permissions.map((p: any) => ({
              action: p.permission.action,
              resource: p.permission.resource,
            }));
          } else {
            // Fall back to default ADMIN permissions (all except owner-only)
            const ownerOnlyPermissions = [
              {
                action: "DELETE" as PermissionAction,
                resource: "WORKSPACE" as PermissionResource,
              },
            ];

            const allPermissions = await db.permission.findMany();
            return allPermissions
              .filter(
                (p: any) =>
                  !ownerOnlyPermissions.some(
                    (op) => op.action === p.action && op.resource === p.resource
                  )
              )
              .map((p: any) => ({ action: p.action, resource: p.resource }));
          }
        }

        // Add permissions from workspace custom role
        if (workspaceMember.customRole) {
          userPermissions = workspaceMember.customRole.permissions.map(
            (p: any) => ({
              action: p.permission.action,
              resource: p.permission.resource,
            })
          );
        }
      }

      // Add permissions from group roles for this specific workspace
      const userGroups = await db.groupMember.findMany({
        where: {
          userId,
        },
        include: {
          group: {
            include: {
              groupWorkspaces: {
                where: {
                  workspaceId,
                },
                include: {
                  customRole: {
                    include: {
                      permissions: {
                        include: {
                          permission: true,
                        },
                      },
                    },
                  },
                },
              },
              customRole: {
                include: {
                  permissions: {
                    include: {
                      permission: true,
                    },
                  },
                },
              },
            },
          },
        },
      });

      // Add permissions from group roles
      for (const groupMember of userGroups) {
        // First add permissions from workspace-specific roles
        for (const groupWorkspace of groupMember.group.groupWorkspaces) {
          if (groupWorkspace.customRole) {
            const groupPermissions = groupWorkspace.customRole.permissions.map(
              (p: any) => ({
                action: p.permission.action,
                resource: p.permission.resource,
              })
            );

            // Add unique permissions from the workspace-specific group role
            for (const permission of groupPermissions) {
              if (
                !userPermissions.some(
                  (p) =>
                    p.action === permission.action &&
                    p.resource === permission.resource
                )
              ) {
                userPermissions.push(permission);
              }
            }
          }
        }

        // Then add permissions from the group's general custom role
        // but only if the group has access to this workspace
        if (groupMember.group.groupWorkspaces.length > 0 && groupMember.group.customRole) {
          const groupPermissions = groupMember.group.customRole.permissions.map(
            (p: any) => ({
              action: p.permission.action,
              resource: p.permission.resource,
            })
          );

          // Add unique permissions from the general group role
          for (const permission of groupPermissions) {
            if (
              !userPermissions.some(
                (p) =>
                  p.action === permission.action &&
                  p.resource === permission.resource
              )
            ) {
              userPermissions.push(permission);
            }
          }
        }
      }

      // If we have workspace-specific permissions, return them
      if (userPermissions.length > 0) {
        return userPermissions;
      }
    }

    // If no workspace-specific permissions or no workspaceId provided,
    // fall back to tenant-level permissions
    const membership = await db.membership.findFirst({
      where: {
        userId,
        tenantId,
      },
      include: {
        customRole: {
          include: {
            permissions: {
              include: {
                permission: true,
              },
            },
          },
        },
      },
    });

    if (!membership) {
      return [];
    }

    // If the user is an OWNER, they have all permissions
    if (membership.role === "OWNER") {
      // Return all possible permissions
      const allPermissions = await db.permission.findMany();
      return allPermissions.map((p: any) => ({
        action: p.action,
        resource: p.resource,
      }));
    }

    // If the user is an ADMIN, check built-in role configuration first
    if (membership.role === "ADMIN") {
      // Check if there's a custom built-in role configuration for ADMIN
      const builtInRoleConfig = await db.builtInRoleConfig.findFirst({
        where: {
          tenantId,
          roleType: "ADMIN",
        },
        include: {
          permissions: {
            include: {
              permission: true,
            },
          },
        },
      });

      if (builtInRoleConfig) {
        // Use configured permissions for ADMIN role
        return builtInRoleConfig.permissions.map((p: any) => ({
          action: p.permission.action,
          resource: p.permission.resource,
        }));
      } else {
        // Fall back to default ADMIN permissions (all except owner-only)
        const ownerOnlyPermissions = [
          {
            action: "DELETE" as PermissionAction,
            resource: "WORKSPACE" as PermissionResource,
          },
        ];

        // Get all permissions
        const allPermissions = await db.permission.findMany();

        // Filter out owner-only permissions
        return allPermissions
          .filter(
            (p: any) =>
              !ownerOnlyPermissions.some(
                (op) => op.action === p.action && op.resource === p.resource
              )
          )
          .map((p: any) => ({ action: p.action, resource: p.resource }));
      }
    }

    // Get permissions from user's custom role
    if (membership.customRole) {
      userPermissions = membership.customRole.permissions.map((p: any) => ({
        action: p.permission.action,
        resource: p.permission.resource,
      }));
    } else {
      // Check if there's a custom built-in role configuration for MEMBER
      const builtInRoleConfig = await db.builtInRoleConfig.findFirst({
        where: {
          tenantId,
          roleType: "MEMBER",
        },
        include: {
          permissions: {
            include: {
              permission: true,
            },
          },
        },
      });

      if (builtInRoleConfig) {
        // Use configured permissions for MEMBER role
        userPermissions = builtInRoleConfig.permissions.map((p: any) => ({
          action: p.permission.action,
          resource: p.permission.resource,
        }));
      } else {
        // Default member role has limited permissions
        userPermissions = [
          {
            action: "READ" as PermissionAction,
            resource: "WORKSPACE" as PermissionResource,
          },
          {
            action: "READ" as PermissionAction,
            resource: "PAGE" as PermissionResource,
          },
          {
            action: "READ" as PermissionAction,
            resource: "FOLDER" as PermissionResource,
          },
          {
            action: "READ" as PermissionAction,
            resource: "FILE" as PermissionResource,
          },
          {
            action: "READ" as PermissionAction,
            resource: "MEMBER" as PermissionResource,
          },
        ];
      }
    }

    // Get permissions from group roles (including both general and workspace-specific permissions)

    // Add permissions from group roles (including both general and workspace-specific permissions)
    const userGroupsWithWorkspaces = await db.groupMember.findMany({
      where: {
        userId,
      },
      include: {
        group: {
          include: {
            groupWorkspaces: {
              include: {
                customRole: {
                  include: {
                    permissions: {
                      include: {
                        permission: true,
                      },
                    },
                  },
                },
              },
            },
            customRole: {
              include: {
                permissions: {
                  include: {
                    permission: true,
                  },
                },
              },
            },
          },
        },
      },
    });

    for (const groupMember of userGroupsWithWorkspaces) {
      // Add permissions from workspace-specific group roles
      for (const groupWorkspace of groupMember.group.groupWorkspaces) {
        if (groupWorkspace.customRole) {
          const workspaceGroupPermissions = groupWorkspace.customRole.permissions.map(
            (p: any) => ({
              action: p.permission.action,
              resource: p.permission.resource,
            })
          );

          // Add unique permissions from workspace-specific group roles
          for (const permission of workspaceGroupPermissions) {
            if (
              !userPermissions.some(
                (p) =>
                  p.action === permission.action &&
                  p.resource === permission.resource
              )
            ) {
              userPermissions.push(permission);
            }
          }
        }
      }

      // Add permissions from general group roles
      if (groupMember.group.customRole) {
        const groupPermissions = groupMember.group.customRole.permissions.map(
          (p: any) => ({
            action: p.permission.action,
            resource: p.permission.resource,
          })
        );

        // Add unique permissions from the general group role
        for (const permission of groupPermissions) {
          if (
            !userPermissions.some(
              (p) =>
                p.action === permission.action &&
                p.resource === permission.resource
            )
          ) {
            userPermissions.push(permission);
          }
        }
      }
    }

    return userPermissions;
  } catch (error) {
    console.error("Error getting user permissions:", error);
    return [];
  }
}
