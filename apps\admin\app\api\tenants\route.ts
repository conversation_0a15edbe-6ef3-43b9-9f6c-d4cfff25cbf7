import { authOptions } from "@/lib/next-auth";
import db from "@/lib/shared-db";
import { getServerSession } from "next-auth/next";
import { NextResponse } from "next/server";

export async function GET() {
  const session = await getServerSession(authOptions);

  if (!session) {
    return NextResponse.json(
      {
        error: "Unauthorized",
      },
      { status: 401 }
    );
  }

  try {
    // Get all tenants with subscription info
    const tenants = await db.tenant.findMany({
      select: {
        id: true,
        name: true,
        slug: true,
        createdAt: true,
        Subscription: {
          where: {
            isActive: true,
          },
          select: {
            plan: {
              select: {
                name: true,
              },
            },
          },
        },
      },
      orderBy: {
        name: "asc",
      },
    });

    // Format the tenants
    const formattedTenants = tenants.map((tenant) => ({
      id: tenant.id,
      name: tenant.name,
      slug: tenant.slug,
      createdAt: tenant.createdAt,
      plan: tenant.Subscription[0]?.plan?.name || "No Plan",
      hasActiveSubscription: tenant.Subscription.length > 0,
    }));

    return NextResponse.json(formattedTenants);
  } catch (error) {
    console.error("Error fetching tenants:", error);
    return NextResponse.json(
      {
        error: "Internal Server Error",
      },
      { status: 500 }
    );
  }
}

export async function POST(request: Request) {
  const session = await getServerSession(authOptions);

  if (!session) {
    return NextResponse.json(
      { error: "Unauthorized" },
      { status: 401 }
    );
  }

  try {
    const body = await request.json();
    const {
      tenantName,
      planId,
      adminEmail,
      customQuotas,
      enabledFeatures
    } = body;

    // Validate required fields
    if (!tenantName || !planId) {
      return NextResponse.json(
        { error: "Missing required fields: tenantName and planId are required" },
        { status: 400 }
      );
    }

    // Validate email format if provided
    if (adminEmail) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(adminEmail)) {
        return NextResponse.json(
          { error: "Invalid email format" },
          { status: 400 }
        );
      }
    }

    // Check if plan exists
    const plan = await db.plan.findUnique({
      where: { id: planId }
    });

    if (!plan) {
      return NextResponse.json(
        { error: "Invalid plan selected" },
        { status: 400 }
      );
    }

    // Check if user with this email already exists (only if email provided)
    let existingUser = null;
    if (adminEmail) {
      existingUser = await db.user.findUnique({
        where: { emailHash: adminEmail.toLowerCase() }
      });
    }

    // Generate tenant slug from name
    const slug = tenantName
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim();

    // Check if slug is unique
    const existingTenant = await db.tenant.findFirst({
      where: { slug }
    });

    if (existingTenant) {
      return NextResponse.json(
        { error: "A tenant with this name already exists" },
        { status: 400 }
      );
    }

    // Start transaction to create tenant and optionally admin user
    const result = await db.$transaction(async (tx: any) => {
      // Create tenant
      const tenant = await tx.tenant.create({
        data: {
          name: tenantName,
          slug,
          isOnboarded: false,
        }
      });

      let user: any = null;
      let membership: any = null;

      // Only create/link admin user if email is provided
      if (adminEmail) {
        if (existingUser) {
          user = existingUser;
        } else {
          // Create admin user
          user = await tx.user.create({
            data: {
              name: adminEmail.split('@')[0], // Use email prefix as default name
              email: adminEmail,
              emailHash: adminEmail.toLowerCase(),
              emailVerified: new Date(),
            }
          });
        }

        // Create membership (admin role)
        membership = await tx.membership.create({
          data: {
            tenantId: tenant.id,
            userId: user.id,
            role: 'ADMIN'
          }
        });
      }

      // Create subscription
      const subscription = await tx.subscription.create({
        data: {
          tenantId: tenant.id,
          planId: plan.id,
          isActive: true,
          additionalUsers: 0,
          additionalStorageGB: customQuotas?.additionalStorageGB || 0,
        }
      });

      return { tenant, user, membership, subscription };
    });

    // Prepare response based on whether admin user was created
    const response: any = {
      success: true,
      tenant: {
        id: result.tenant.id,
        name: result.tenant.name,
        slug: result.tenant.slug,
      },
      subscription: {
        id: result.subscription.id,
        planId: result.subscription.planId,
        isActive: result.subscription.isActive,
      }
    };

    if (result.user) {
      response.adminUser = {
        id: result.user.id,
        email: result.user.email,
        name: result.user.name,
      };
      response.message = `Tenant "${result.tenant.name}" created successfully with admin user "${result.user.email}"`;
    } else {
      response.message = `Tenant "${result.tenant.name}" created successfully. Admin user can be added later.`;
    }

    return NextResponse.json(response);

  } catch (error) {
    console.error("Error creating tenant:", error);
    return NextResponse.json(
      { error: "Failed to create tenant" },
      { status: 500 }
    );
  }
}
