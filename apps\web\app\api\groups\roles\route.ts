import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import db from "@/lib/shared-db";
import { authOptions } from "@/lib/next-auth/src/auth-options";
import { withPermission } from "@/lib/permission-middleware";

/**
 * POST /api/groups/roles
 * Assign a custom role to a group
 */
export const POST = withPermission(
  async (request: Request) => {
    try {
      const session: any = await getServerSession(authOptions);

      if (!session?.user?.email) {
        return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
      }

      const { groupId, customRoleId, tenantId } = await request.json();

      if (!groupId || !customRoleId || !tenantId) {
        return NextResponse.json(
          { error: "Group ID, Custom Role ID, and Tenant ID are required" },
          { status: 400 }
        );
      }

      // Verify the group exists and belongs to the tenant
      const group = await db.group.findFirst({
        where: {
          id: groupId,
          tenantId,
        },
      });
      const oldCustomRoleId = group?.customRoleId;

      if (!group) {
        return NextResponse.json(
          { error: "Group not found or does not belong to this tenant" },
          { status: 404 }
        );
      }

      // Verify the custom role exists and belongs to the tenant
      const customRole = await db.customRole.findFirst({
        where: {
          id: customRoleId,
          tenantId,
        },
      });

      if (!customRole) {
        return NextResponse.json(
          { error: "Custom role not found or does not belong to this tenant" },
          { status: 404 }
        );
      }
      const updateAll = await db.$transaction(async (tx) => {
        // Update the group with the custom role
        const group = await tx.group.update({
          where: {
            id: groupId,
          },
          data: {
            customRoleId,
          },
          include: {
            customRole: {
              include: {
                workspaces: {
                  include: {
                    workspace: true,
                  },
                },
              },
            },
          },
        });

        const groupMembers = await tx.groupMember.findMany({
          where: {
            groupId,
          },
          include: {
            user: true,
          },
        });
        const userIds = groupMembers.map((member) => member.user.id);

        // Get the workspaces assigned to this custom role
        const roleWorkspaces = group.customRole?.workspaces || [];

        // Remove existing group workspace assignments
        await tx.groupWorkspace.deleteMany({
          where: {
            groupId,
          },
        });

        // Create new group workspace assignments based on the role's workspaces
        for (const roleWorkspace of roleWorkspaces) {
          await tx.groupWorkspace.create({
            data: {
              groupId,
              workspaceId: roleWorkspace.workspaceId,
              customRoleId,
            },
          });
        }

        // Remove existing workspace memberships for group members that were created through the old role
        if (oldCustomRoleId) {
          await tx.workspaceMember.deleteMany({
            where: {
              userId: {
                in: userIds,
              },
              customRoleId: oldCustomRoleId,
              role: "CUSTOM",
            },
          });
        }

        // Create new workspace members for group members based on the role's workspaces
        const memberships = await tx.membership.findMany({
          where: {
            userId: {
              in: userIds,
            },
            tenantId,
          },
        });

        for (const user of groupMembers) {
          const userMembership = memberships.find(
            (m) => m.userId === user.userId,
          );
          if (userMembership) {
            for (const roleWorkspace of roleWorkspaces) {
              const existingMember = await tx.workspaceMember.findFirst({
                where: {
                  userId: user?.userId,
                  workspaceId: roleWorkspace?.workspaceId,
                },
              });

              if (existingMember) {
                await tx.workspaceMember.update({
                  where: {
                    id: existingMember.id,
                  },
                  data: {
                    customRoleId,
                  },
                });
              } else {
                await tx.workspaceMember.create({
                  data: {
                    userId: user?.userId,
                    workspaceId: roleWorkspace?.workspaceId,
                    membershipId: userMembership?.id,
                    customRoleId,
                    role: "CUSTOM",
                  },
                });
              }
            }
          }
        }

        return group;
      });

      return NextResponse.json({
        data: updateAll?.group,
        message: "Custom role assigned to group successfully",
      });
    } catch (error) {
      console.error("Error assigning custom role to group:", error);
      return NextResponse.json(
        { error: "Failed to assign custom role to group" },
        { status: 500 }
      );
    }
  },
  "UPDATE",
  "WORKSPACE"
);

/**
 * DELETE /api/groups/roles
 * Remove a custom role from a group
 */
export const DELETE = withPermission(
  async (request: Request) => {
    try {
      const session: any = await getServerSession(authOptions);

      if (!session?.user?.email) {
        return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
      }

      const { groupId, tenantId } = await request.json();

      if (!groupId || !tenantId) {
        return NextResponse.json(
          { error: "Group ID and Tenant ID are required" },
          { status: 400 }
        );
      }

      // Verify the group exists and belongs to the tenant
      const group = await db.group.findFirst({
        where: {
          id: groupId,
          tenantId,
        },
      });

      if (!group) {
        return NextResponse.json(
          { error: "Group not found or does not belong to this tenant" },
          { status: 404 }
        );
      }

      // Remove the custom role from the group

      const oldCustomRoleId = group?.customRoleId;
      const updateAll = await db.$transaction(async (tx) => {
        // Update the group to remove the custom role
        const updatedGroup = await tx.group.update({
          where: {
            id: groupId,
          },
          data: {
            customRoleId: null,
          },
        });

        const groupMembers = await tx.groupMember.findMany({
          where: {
            groupId,
          },
          include: {
            user: true,
          },
        });

        // Remove all group workspace assignments
        await tx.groupWorkspace.deleteMany({
          where: {
            groupId,
          },
        });

        const userIds = groupMembers.map((member) => member.user.id);

        // Remove workspace members that were created through this group's custom role
        await tx.workspaceMember.deleteMany({
          where: {
            userId: {
              in: userIds,
            },
            customRoleId: oldCustomRoleId,
            role: "CUSTOM", // Only remove group-based access, not direct access
          },
        });
      });
      return NextResponse.json({
        data: updateAll,
        message: "Custom role removed from group successfully",
      });
    } catch (error) {
      console.error("Error removing custom role from group:", error);
      return NextResponse.json(
        { error: "Failed to remove custom role from group" },
        { status: 500 }
      );
    }
  },
  "UPDATE",
  "WORKSPACE"
);
