"use client";

import React from "react";
import { usePathname } from "next/navigation";
import { useLanguage } from "@/lib/language-context";
import Link from "next/link";
import { cn } from "@/lib/utils";
import {
  Users,
  Building,
  Unplug,
  Shield,
  UserCircle,
  Settings,
  ChevronDown,
  ChevronRight,
  Server,
  Menu,
  X,
  Bot,
  Palette,
} from "lucide-react";
import { Separator } from "@/components/ui/separator";

export default function SettingsLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname() ?? "";
  const { t } = useLanguage();
  const isPartnerConsole = process.env.NEXT_PUBLIC_IS_PARTNER_CONSOLE === "1";
  const defaultExpandedGroups = ["organization"];

  const [expandedGroups, setExpandedGroups] = React.useState<Set<string>>(
    new Set(defaultExpandedGroups)
  );
  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = React.useState(false);

  const toggleGroup = (groupId: string) => {
    const newExpanded = new Set(expandedGroups);
    if (newExpanded.has(groupId)) {
      newExpanded.delete(groupId);
    } else {
      newExpanded.add(groupId);
    }
    setExpandedGroups(newExpanded);
  };

  const toggleMobileSidebar = () => {
    setIsMobileSidebarOpen(!isMobileSidebarOpen);
  };

  const closeMobileSidebar = () => {
    setIsMobileSidebarOpen(false);
  };

  const navGroups = [
    {
      id: "organization",
      title:
        t("settings.navigationGroups.organizationManagement") ||
        "Organization Management",
      items: [
        {
          title: t("settings.organization.title"),
          href: "/settings",
          icon: Building,
          subTitle: t("settings.organization.description"),
        },
        {
          title: t("settings.members.title"),
          href: "/settings/members",
          subTitle: t("settings.members.description"),
          icon: Users,
        },
        {
          title: t("settings.roles.title") || "Roles",
          href: "/settings/roles",
          subTitle:
            t("settings.roles.description") ||
            "Manage custom roles and permissions",
          icon: Shield,
        },
        {
          title: t("settings.groups.title") || "Groups",
          href: "/settings/groups",
          subTitle:
            t("settings.groups.description") ||
            "Manage groups and workspace access",
          icon: UserCircle,
        },
        ...(isPartnerConsole
          ? [
              {
                title: t("settings.themeConfig.title") || "Theme Configuration",
                href: "/settings/theme-config",
                subTitle:
                  t("settings.themeConfig.description") ||
                  "Customize branding and appearance",
                icon: Palette,
              },
            ]
          : []),
      ],
    },
    {
      id: "user-access",
      title:
        t("settings.navigationGroups.userAccessControl") ||
        "User & Access Control",
      items: [
        {
          title:
            t("settings.userWorkspaceManagement.title") ||
            "User Workspace Management",
          href: "/settings/user-workspace-management",
          subTitle:
            t("settings.userWorkspaceManagement.description") ||
            "Manage user access to workspaces",
          icon: UserCircle,
        },
      ],
    },
    {
      id: "ai-data",
      title: t("settings.navigationGroups.aiData") || "AI & Data",
      items: [
        {
          title: t("settings.llmScope.title") || "LLM Scope",
          href: "/settings/llm-scope",
          subTitle:
            t("settings.llmScope.description") ||
            "Control AI assistant data access and capabilities",
          icon: Settings,
        },
        {
          title: t("settings.mcp.title") || "MCP Servers",
          href: "/settings/mcp-servers",
          subTitle:
            t("settings.mcp.description") ||
            "Manage Model Context Protocol servers",
          icon: Server,
        },
      ],
    },
    {
      id: "integrations",
      title: t("settings.navigationGroups.integrations") || "Integrations",
      items: [
        {
          title: t("settings.integrations.title"),
          href: "/settings/integrations",
          subTitle: t("settings.integrations.description"),
          icon: Unplug,
        },
      ],
    },
  ];

  // Auto-expand the section containing the current route
  React.useEffect(() => {
    navGroups.forEach((group) => {
      const isCurrentGroupActive = group.items.some(
        (item) =>
          pathname === item.href ||
          (item.href !== "/settings" && pathname.startsWith(item.href + "/"))
      );
      if (isCurrentGroupActive && !expandedGroups.has(group.id)) {
        setExpandedGroups((prev) => new Set([...prev, group.id]));
      }
    });
  }, [pathname]);
  // Add Partner Console section if enabled
  if (isPartnerConsole) {
    navGroups.push({
      id: "partner-console",
      title: t("settings.partnerConsole.title") || "Partner Console",
      items: [
        {
          title: t("settings.partnerConsole.companyManagement") || "Company",
          href: "/settings/partner/company",
          subTitle:
            t("settings.partnerConsole.companyManagementDescription") ||
            "Manage company information and settings",
          icon: Building,
        },
        {
          title: t("settings.partnerConsole.chatbotsManagement") || "Chatbots",
          href: "/settings/partner/chatbots",
          subTitle:
            t("settings.partnerConsole.chatbotsManagement") ||
            "Manage your organization's chatbots",
          icon: Bot,
        },
        {
          title: t("settings.partnerConsole.userAnalytics") || "User Analytics",
          href: "/settings/partner/analytics/users",
          subTitle:
            t("settings.partnerConsole.userAnalyticsDescription") ||
            "Monitor user behavior and performance",
          icon: Users,
        },
      ],
    });
  }
  // Auto-expand the section containing the current route
  React.useEffect(() => {
    navGroups.forEach((group) => {
      const isCurrentGroupActive = group.items.some(
        (item) =>
          pathname === item.href ||
          (item.href !== "/settings" && pathname.startsWith(item.href + "/"))
      );
      if (isCurrentGroupActive && !expandedGroups.has(group.id)) {
        setExpandedGroups((prev) => new Set([...prev, group.id]));
      }
    });
  }, [pathname]);
  // Add Partner Console section if enable

  // Find all nav items for current nav detection
  const allNavItems = navGroups.flatMap((group) => group.items);
  const currentNav = allNavItems.find(
    (item) =>
      pathname === item.href ||
      (item.href !== "/settings" && pathname.startsWith(item.href + "/"))
  );

  // Sidebar component for reuse
  const SidebarContent = () => (
    <nav className="flex flex-col space-y-2">
      {navGroups.map((group) => {
        const isExpanded = expandedGroups.has(group.id);

        return (
          <div key={group.id} className="space-y-1">
            {/* Group Header */}
            <button
              onClick={() => toggleGroup(group.id)}
              className="flex items-center justify-between w-full px-3 py-2 text-sm font-semibold text-foreground hover:bg-accent/50 rounded-md transition-colors min-h-[2.5rem]"
            >
              <span className="text-left leading-tight">{group.title}</span>
              <div className="flex-shrink-0 ml-2">
                {isExpanded ? (
                  <ChevronDown className="h-4 w-4" />
                ) : (
                  <ChevronRight className="h-4 w-4" />
                )}
              </div>
            </button>

            {/* Group Items */}
            {isExpanded && (
              <div className="ml-3 space-y-1 border-l border-border pl-3">
                {group.items.map((item) => {
                  const Icon = item.icon;
                  const isActive = pathname === item.href;

                  return (
                    <Link
                      key={item.href}
                      href={item.href}
                      onClick={closeMobileSidebar}
                      className={cn(
                        "flex items-center rounded-md px-3 py-2 text-sm font-medium transition-colors",
                        isActive
                          ? "bg-accent text-accent-foreground"
                          : "text-muted-foreground hover:bg-accent hover:text-accent-foreground"
                      )}
                    >
                      <Icon className="mr-2 h-4 w-4" />
                      <span>{item.title}</span>
                    </Link>
                  );
                })}
              </div>
            )}
          </div>
        );
      })}
    </nav>
  );

  return (
    <div className="flex flex-col h-full px-4 py-4 overflow-hidden">
      <div className="flex-none space-y-0.5 mb-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold tracking-tight">
              {currentNav?.title}
            </h1>
            <p className="text-muted-foreground">{currentNav?.subTitle}</p>
          </div>
          {/* Mobile hamburger button */}
          <button
            onClick={toggleMobileSidebar}
            className="lg:hidden p-2 rounded-md hover:bg-accent transition-colors"
            aria-label="Toggle sidebar"
          >
            <Menu className="h-5 w-5" />
          </button>
        </div>
      </div>
      <Separator className="mb-2 flex-none" />
      <div className="flex flex-1 gap-6 min-h-0 ">
        {/* Sidebar navigation */}
        <aside className="hidden lg:flex w-64 shrink-0 flex-none">
          <nav className="flex flex-col space-y-2">
            {navGroups.map((group) => {
              const isExpanded = expandedGroups.has(group.id);

              return (
                <div key={group.id} className="space-y-1">
                  {/* Group Header */}
                  <button
                    onClick={() => toggleGroup(group.id)}
                    className="flex items-center justify-between w-full px-3 py-2 text-sm font-semibold text-foreground hover:bg-accent/50 rounded-md transition-colors"
                  >
                    <span>{group.title}</span>
                    {isExpanded ? (
                      <ChevronDown className="h-4 w-4" />
                    ) : (
                      <ChevronRight className="h-4 w-4" />
                    )}
                  </button>

                  {/* Group Items */}
                  {isExpanded && (
                    <div className="ml-3 space-y-1 border-l border-border pl-3">
                      {group.items.map((item) => {
                        const Icon = item.icon;
                        // Fix: Prevent /settings from matching /settings/groups/[id]
                        // Only match exact path or nested paths with proper separation
                        const isActive =
                          pathname === item.href ||
                          (item.href !== "/settings" &&
                            pathname.startsWith(item.href + "/"));

                        return (
                          <Link
                            key={item.href}
                            href={item.href}
                            className={cn(
                              "flex items-center rounded-md px-3 py-2 text-sm font-medium transition-colors",
                              isActive
                                ? "bg-accent text-accent-foreground"
                                : "text-muted-foreground hover:bg-accent hover:text-accent-foreground"
                            )}
                          >
                            <Icon className="mr-2 h-4 w-4" />
                            <span>{item.title}</span>
                          </Link>
                        );
                      })}
                    </div>
                  )}
                </div>
              );
            })}
          </nav>
        </aside>

        {/* Mobile Sidebar Overlay */}
        {isMobileSidebarOpen && (
          <div className="lg:hidden fixed inset-0 z-50 flex">
            {/* Backdrop */}
            <div
              className="fixed inset-0 bg-black/20 backdrop-blur-sm"
              onClick={closeMobileSidebar}
            />

            {/* Sidebar */}
            <aside className="relative flex flex-col w-64 h-full bg-background border-r border-border shadow-lg animate-in slide-in-from-left duration-300">
              {/* Close button */}
              <div className="flex items-center justify-between p-4 border-b border-border">
                <h2 className="text-lg font-semibold">Settings</h2>
                <button
                  onClick={closeMobileSidebar}
                  className="p-2 rounded-md hover:bg-accent transition-colors"
                  aria-label="Close sidebar"
                >
                  <X className="h-4 w-4" />
                </button>
              </div>

              {/* Sidebar content */}
              <div className="flex-1 overflow-y-auto p-4">
                <SidebarContent />
              </div>
            </aside>
          </div>
        )}

        {/* Vertical Separator - only visible on desktop */}
        <Separator orientation="vertical" className="hidden lg:block h-full" />

        {/* Main content */}
        <main className="flex-1 overflow-y-auto min-h-0">{children}</main>
      </div>
    </div>
  );
}
