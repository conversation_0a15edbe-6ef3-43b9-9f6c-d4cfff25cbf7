"use client";

import * as React from "react";
import { CalendarIcon } from "lucide-react";
import { addDays, format } from "date-fns";
import { DateRange } from "react-day-picker";
import { useMetrics } from "@/components/metrics/metrics-context";

import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface DateRangeSelectorProps {
  onChange: (range: { from: Date; to: Date } | undefined) => void;
  className?: string;
}

export function DateRangeSelector({
  onChange,
  className,
}: DateRangeSelectorProps) {
  const { dateRange, setDateRange } = useMetrics();

  const [date, setDate] = React.useState<DateRange | undefined>({
    from: dateRange.from,
    to: dateRange.to,
  });

  const [preset, setPreset] = React.useState<string>("last30Days");

  // Predefined date ranges
  const presets = {
    last7Days: {
      label: "Last 7 Days",
      range: {
        from: addDays(new Date(), -7),
        to: new Date(),
      },
    },
    last30Days: {
      label: "Last 30 Days",
      range: {
        from: addDays(new Date(), -30),
        to: new Date(),
      },
    },
    last90Days: {
      label: "Last 90 Days",
      range: {
        from: addDays(new Date(), -90),
        to: new Date(),
      },
    },
    thisMonth: {
      label: "This Month",
      range: {
        from: new Date(new Date().getFullYear(), new Date().getMonth(), 1),
        to: new Date(),
      },
    },
    lastMonth: {
      label: "Last Month",
      range: {
        from: new Date(new Date().getFullYear(), new Date().getMonth() - 1, 1),
        to: new Date(new Date().getFullYear(), new Date().getMonth(), 0),
      },
    },
    thisYear: {
      label: "This Year",
      range: {
        from: new Date(new Date().getFullYear(), 0, 1),
        to: new Date(),
      },
    },
  };

  // Handle preset change
  const handlePresetChange = (value: string) => {
    setPreset(value);
    const newRange = presets[value as keyof typeof presets].range;
    setDate(newRange);
    setDateRange(newRange);
    onChange?.(newRange);
  };

  // Handle date range change
  const handleDateRangeChange = (range: DateRange | undefined) => {
    if (range?.from && range?.to) {
      setDate(range);
      setPreset("custom");
      setDateRange({ from: range.from, to: range.to });
      onChange?.({ from: range.from, to: range.to });
    }
  };

  return (
    <div className={cn("flex items-center space-x-2", className)}>
      <Select value={preset} onValueChange={handlePresetChange}>
        <SelectTrigger className="w-[180px]">
          <SelectValue placeholder="Select a preset" />
        </SelectTrigger>
        <SelectContent>
          {Object.entries(presets).map(([key, { label }]) => (
            <SelectItem key={key} value={key}>
              {label}
            </SelectItem>
          ))}
          {preset === "custom" && (
            <SelectItem value="custom">Custom Range</SelectItem>
          )}
        </SelectContent>
      </Select>

      <Popover>
        <PopoverTrigger asChild>
          <Button
            id="date"
            variant={"outline"}
            className={cn(
              "w-[300px] justify-start text-left font-normal",
              !date && "text-muted-foreground"
            )}
          >
            <CalendarIcon className="mr-2 h-4 w-4" />
            {date?.from ? (
              date.to ? (
                <>
                  {format(date.from, "LLL dd, y")} -{" "}
                  {format(date.to, "LLL dd, y")}
                </>
              ) : (
                format(date.from, "LLL dd, y")
              )
            ) : (
              <span>Pick a date</span>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0" align="start">
          <Calendar
            initialFocus
            mode="range"
            defaultMonth={date?.from}
            selected={date}
            onSelect={handleDateRangeChange}
            numberOfMonths={2}
          />
        </PopoverContent>
      </Popover>
    </div>
  );
}
