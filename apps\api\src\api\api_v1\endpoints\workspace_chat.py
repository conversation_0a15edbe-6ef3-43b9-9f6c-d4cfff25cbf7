from fastapi import APIRouter, Depends, HTTPException, status
from typing import Dict, Any, Optional, List
from pydantic import BaseModel
from src.services.workspace_rag import WorkspaceRAGManager
from src.api.deps import get_db
import json
import datetime
from src.services.usage_tracker import UsageTracker
from bson import ObjectId
import logging
from urllib.parse import unquote
import asyncio
import os

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

router = APIRouter()

# Initialize the workspace RAG manager
workspace_rag_manager = WorkspaceRAGManager()

class DocumentIndex(BaseModel):
    workspaceSlug: str
    document_path: str
    document_type: Optional[str] = "auto"
    metadata: Optional[Dict[str, Any]] = None
    page_id: Optional[str] = None

class WorkspaceAccess(BaseModel):
    user_id: str
    workspace_ids: List[str]

# Custom JSON encoder to handle non-serializable objects
class CustomJSONEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, ObjectId):
            return str(obj)
        # Add more custom type handling as needed
        return super().default(obj)

async def check_llm_scope_permissions(tenant_id: str, include_web_results: bool, db):
    """
    Check if the requested operation is allowed by the tenant's LLM scope settings.
    """
    try:
        # Get tenant's LLM scope settings
        tenant = await db.Tenant.find_one({"_id": ObjectId(tenant_id)})
        if not tenant:
            return False, "Tenant not found"

        llm_scope = tenant.get("llmScope", ["INTERNAL_ONLY"])

        # Handle both old single string format and new array format
        if isinstance(llm_scope, str):
            llm_scope = [llm_scope]

        # Check if web search is allowed
        if include_web_results:
            web_allowed = any(scope in ["EXTERNAL_ONLY", "HYBRID", "FULL_ACCESS", "web", "hybrid"] for scope in llm_scope)
            if not web_allowed:
                return False, f"Web search is not allowed with current LLM scope: {llm_scope}. Contact your administrator to change the scope settings."

        # Check if MCP_ONLY scope is being used correctly
        if llm_scope == ["MCP_ONLY"] or "MCP_ONLY" in llm_scope:
            return False, "MCP_ONLY scope requires using MCP servers. Please use the MCP chat endpoint instead."

        # For now, internal document access is allowed for all scopes except EXTERNAL_ONLY
        # This can be extended later if needed

        return True, None

    except Exception as e:
        logger.error(f"Error checking LLM scope permissions: {e}")
        return False, "Error checking LLM scope permissions"


@router.post("/index", response_model=Dict[str, Any], status_code=200)
async def index_workspace_document(
    index_request: DocumentIndex,
    current_user: str,
    tenant_id: str,
    file_id: str,
    db = Depends(get_db)
):
    # Initialize usage tracker
    start_time = datetime.datetime.now(datetime.timezone.utc)
    usage_tracker = UsageTracker(db)
    """
    Index a document in a workspace, with access control.
    Uses background processing for document indexing.
    """
    # Set the database client
    workspace_rag_manager.db_client = db
    await db.File.update_one(
        {"_id": ObjectId(file_id)},
        {"$set": {"vectorizationStatus": "PROCESSING"}}
    )

    # Get document path for processing
    document_path = index_request.document_path

    # Check if this is an audio/video file that should use the original URL
    is_audio_video_url = False
    if document_path.startswith(('http://', 'https://')):
        # Check if it's an audio or video file by looking at the URL extension
        audio_video_extensions = ['.mp3', '.wav', '.m4a', '.ogg', '.mp4', '.avi', '.mov', '.webm', '.flac', '.aac']
        is_audio_video_url = any(ext in document_path.lower() for ext in audio_video_extensions)

        # Also try to check content type via HEAD request
        if not is_audio_video_url:
            try:
                import aiohttp
                async with aiohttp.ClientSession() as session:
                    async with session.head(document_path) as response:
                        content_type = response.headers.get('content-type', '').lower()
                        is_audio_video_url = (
                            content_type.startswith('audio/') or
                            content_type.startswith('video/')
                        )
            except Exception as e:
                logger.warning(f"Could not determine content type for URL: {e}")

    # For background processing, we pass the original URL and let the background service handle downloads
    if is_audio_video_url:
        logger.info(f"Audio/video file detected, preserving URL for processing: {document_path}")
    else:
        logger.info(f"Document file will be processed in background: {document_path}")

    slug=unquote(index_request.workspaceSlug)
    workspace=await db.Workspace.find_one(
        {
            "slug":slug,
        }
    )

    # Handle video/audio files with async processing
    if is_audio_video_url:
        logger.info(f"Starting async video processing for file {file_id}")

        # Import video processing service
        from src.services.video_processing_service import video_processing_service
        video_processing_service.db_client = db

        # Create video processing job
        video_files = [{
            "url": document_path,
            "name": os.path.basename(document_path),
            "type": "video" if any(ext in document_path.lower() for ext in ['.mp4', '.avi', '.mov', '.webm', '.mkv', '.flv', '.wmv', '.m4v', '.3gp']) else "audio",
            "size": 0  # Size not available from URL
        }]

        job_id = await video_processing_service.create_video_processing_job(
            video_files=video_files,
            user_id=current_user,
            tenant_id=tenant_id,
            file_id=file_id,
            workspace_slug=slug
        )

        logger.info(f"Created video processing job {job_id} for file {file_id}")

        # Return success - file status will remain PROCESSING until webhook completes
        result = {
            "status": 200,
            "message": f"Video/audio file submitted for background processing",
            "job_id": job_id,
            "processing_status": "async_processing_started"
        }
    else:
        # Process non-video/audio files with background processing
        logger.info(f"Starting async document processing for file {file_id}")

        # Import document processing service
        from src.services.document_processing_service import document_processing_service
        document_processing_service.db_client = db

        # Create document processing job with timeout protection
        try:
            job_id = await asyncio.wait_for(
                document_processing_service.create_document_processing_job(
                    document_path=document_path,
                    document_type=index_request.document_type,
                    user_id=current_user,
                    tenant_id=tenant_id,
                    workspace_id=str(workspace["_id"]),
                    file_id=file_id,
                    workspace_slug=slug,
                    metadata=index_request.metadata,
                    page_id=index_request.page_id
                ),
                timeout=10.0  # 30 second timeout for job creation
            )
        except asyncio.TimeoutError:
            logger.error(f"Timeout creating document processing job for file {file_id}")
            # Update file status to failed
            await db.File.update_one(
                {"_id": ObjectId(file_id)},
                {"$set": {"vectorizationStatus": "FAILED"}}
            )
            raise HTTPException(
                status_code=status.HTTP_408_REQUEST_TIMEOUT,
                detail="Document processing job creation timed out. Please try again."
            )
        except Exception as e:
            logger.error(f"Error creating document processing job for file {file_id}: {e}")
            # Update file status to failed
            await db.File.update_one(
                {"_id": ObjectId(file_id)},
                {"$set": {"vectorizationStatus": "FAILED"}}
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to create document processing job: {str(e)}"
            )

        logger.info(f"Created document processing job {job_id} for file {file_id}")

        # Return success - file status will remain PROCESSING until job completes
        result = {
            "status": 200,
            "message": f"Document submitted for background processing",
            "job_id": job_id,
            "processing_status": "async_processing_started"
        }

    # Log token usage for document indexing
    try:
        # Read file content based on file type
        import magic
        from pypdf import PdfReader

        # Handle URL vs local file path for file type detection
        if document_path.startswith(('http://', 'https://')):
            # For URLs (audio/video files), determine type from URL extension
            audio_video_extensions = ['.mp3', '.wav', '.m4a', '.ogg', '.mp4', '.avi', '.mov', '.webm', '.flac', '.aac']
            if any(ext in document_path.lower() for ext in audio_video_extensions):
                if any(ext in document_path.lower() for ext in ['.mp3', '.wav', '.m4a', '.ogg', '.flac', '.aac']):
                    file_type = 'audio/mpeg'  # Generic audio type
                else:
                    file_type = 'video/mp4'   # Generic video type
            else:
                file_type = 'application/octet-stream'  # Unknown type
        else:
            # For local files, use magic to detect type
            mime = magic.Magic(mime=True)
            file_type = mime.from_file(document_path)

        if file_type.startswith('application/pdf'):
            # Handle PDF files
            with open(document_path, 'rb') as file:
                pdf_reader = PdfReader(file)
                document_content = '\n'.join([page.extract_text() for page in pdf_reader.pages])
        elif file_type.startswith('text/') or file_type in ['application/csv', 'text/csv'] or 'markdown' in file_type:
            # Handle text, CSV, and markdown files
            with open(document_path, 'r', encoding='utf-8', errors='ignore') as file:
                document_content = file.read()
        elif file_type in ['application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'application/vnd.ms-word']:
            # Handle Word documents
            try:
                from docx import Document
                if file_type.endswith('wordprocessingml.document'):  # .docx
                    doc = Document(document_path)
                    paragraphs = [p.text for p in doc.paragraphs if p.text.strip()]
                    document_content = '\n'.join(paragraphs)
                else:  # .doc files
                    document_content = f"Word document (.doc): {document_path} (requires conversion to .docx for full text extraction)"
            except Exception as word_error:
                logger.warning(f"Error reading Word document for token logging: {word_error}")
                document_content = f"Word document: {document_path} (content could not be read for token counting)"
        elif file_type in ['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'application/vnd.ms-excel']:
            # Handle Excel files
            try:
                import pandas as pd
                df = pd.read_excel(document_path)
                # Convert Excel content to text for token counting
                document_content = f"Excel file with {df.shape[0]} rows and {df.shape[1]} columns\n"
                # Convert column names to strings to avoid join errors
                column_names = [str(col) for col in df.columns]
                document_content += f"Columns: {', '.join(column_names)}\n"
                try:
                    # Limit the string conversion to prevent memory issues
                    document_content += df.to_string(max_rows=50, max_cols=20, max_colwidth=30)
                except Exception as string_error:
                    logger.warning(f"Error converting Excel to string: {string_error}")
                    try:
                        # Fallback: convert to strings first
                        df_str = df.astype(str)
                        document_content += df_str.to_string(max_rows=50, max_cols=20, max_colwidth=30)
                    except Exception:
                        document_content += "Content: Unable to convert data to string (contains complex data types)"
            except Exception as excel_error:
                logger.warning(f"Error reading Excel file for token logging: {excel_error}")
                document_content = f"Excel file: {document_path} (content could not be read for token counting)"
        elif file_type in ['application/vnd.openxmlformats-officedocument.presentationml.presentation', 'application/vnd.ms-powerpoint']:
            # Handle PowerPoint files
            document_content = f"PowerPoint presentation: {document_path} (slide content extraction not implemented for token counting)"
        elif file_type.startswith('audio/') or file_type.startswith('video/'):
            # Handle audio/video files - minimal content for token counting
            if document_path.startswith(('http://', 'https://')):
                document_content = f"Audio/Video file URL: {document_path} (content will be processed by Azure Video Indexer)"
            else:
                document_content = f"Audio/Video file: {document_path} (content not processed for token counting)"
        else:
            # Skip token logging for unsupported file types
            logger.warning(f"Skipping token logging for unsupported file type: {file_type}")
            document_content = ''

        metadata_str = json.dumps(index_request.metadata) if index_request.metadata else ""

        # Log token usage for document content and metadata
        await usage_tracker.log_token_usage(
            tenant_id=tenant_id,
            input_text=document_content + metadata_str,
            output_text="",  # No output for indexing
            request_type="document_indexing",
            model_used="embed-v-4-0"  # Standard embedding model
        )
        await usage_tracker.log_api_request(
           user_id=current_user,
            tenant_id=tenant_id,
            endpoint="/index_document",
            method="POST",
            status_code=200,
            duration=int((datetime.datetime.now(datetime.timezone.utc) - start_time).total_seconds() * 1000)
        )
    except Exception as e:
        logger.error(f"Error logging token usage for document indexing: {e}")

    # No file cleanup needed since we're using background processing

    if result.get("status") == 403:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You do not have access to this workspace"
        )
    elif result.get("status") == 500:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=result.get("error", "An error occurred indexing your document")
        )

    return result

@router.get("/generate-title", status_code=200)
async def generate_title(
    query: str,
    tenant_id:str,
    db = Depends(get_db)
):
    """
    Get list of workspaces the current user has access to.
    """

        # Set the database client
    workspace_rag_manager.db_client = db
    workspace=await db.Workspace.find_one(
        {
            "tenantId":ObjectId(tenant_id),
        }
    )
    # Get workspaces the user has access to
    content = await workspace_rag_manager.generate_title_from_question(query,tenant_id,workspace["_id"])

    return content

@router.get("/delete-file", status_code=200)
async def delete_file(
    file_id: str,
    workspaceSlug: str,
    tenant_id: str,
    db = Depends(get_db)
):
    workspace_rag_manager.db_client = db
    slug=unquote(workspaceSlug)
    workspace=await db.Workspace.find_one(
    {
        "slug":slug,
    }
    )
    content = await workspace_rag_manager.delete_file(file_id=file_id,workspace_id=workspace["_id"],tenant_id=tenant_id)

    return content

@router.post("/clear-agent-cache", status_code=200)
async def clear_agent_cache(
    workspace_id: Optional[str] = None,
    db = Depends(get_db)
):
    """
    Clear the agent cache for a specific workspace or all workspaces.
    This is useful when configuration changes require agents to be recreated.
    """
    workspace_rag_manager.db_client = db
    result = workspace_rag_manager.clear_agent_cache(workspace_id)
    return result


@router.get("/document-processing-status/{job_id}", response_model=Dict[str, Any], status_code=200)
async def get_document_processing_status(
    job_id: str,
    current_user: str,
    tenant_id: str,
    db = Depends(get_db)
):
    """
    Get the status of a document processing job.
    """
    try:
        # Import document processing service
        from src.services.document_processing_service import document_processing_service
        document_processing_service.db_client = db

        # Get job status
        job_status = await document_processing_service.get_job_status(job_id)

        if not job_status:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Document processing job not found"
            )

        return {
            "status": 200,
            "job": job_status
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting document processing status: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get document processing status"
        )
