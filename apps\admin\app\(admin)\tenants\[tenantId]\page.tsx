import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/next-auth";
import { redirect } from "next/navigation";
import db from "@/lib/shared-db";
import { TenantDetailClient } from "@/components/tenants/tenant-detail-client";
import { notFound } from "next/navigation";

interface Plan {
  id: string;
  name: string;
  type: string;
  description?: string; // ✅ Changed from string | null to optional string to match client component
  price?: number; // ✅ Made optional to match client component
  includedUsers: number;
  vectorStoreGB: number;
  webSearchLimit: number;
  additionalUserFee?: number; // ✅ Made optional to match client component
}

// Enhanced types for tenant detail view
interface TenantDetail {
  id: string;
  name: string;
  slug: string;
  createdAt: Date;
  plan: {
    id: string;
    name: string;
    type: string;
    price: number;
    features?: any;
  } | null;
  status: "active" | "inactive" | "suspended";
  llmScope?: string[];
  fileUploadLimitDuringChat?: number;
  users: Array<{
    id: string;
    name: string;
    email: string;
    role: string;
    lastSeen: Date | null;
    status: string;
  }>;
  usage: {
    tokenUsage: {
      current: number;
      inputTokens: number;
      outputTokens: number;
    };
    storage: {
      current: number;
    };
    apiRequests: {
      current: number;
    };
  };
  tokenAnalytics: {
    dailyUsage: Array<{
      date: string;
      totalTokens: number;
    }>;
    featureBreakdown: Array<{
      requestType: string;
      totalTokens: number;
      percentage: number;
      count: number;
    }>;
  };
  financial: {
    infraCost: number;
    revenue: number;
    margin: number;
    costBreakdown: {
      tokenCost: number;
      storageCost: number;
      apiCost: number;
    };
    dailyTrends?: Array<{
      date: string;
      revenue: number;
      infraCost: number;
      margin: number;
      marginPercent: number;
    }>;
  };
  healthScore: {
    score: number;
    breakdown: {
      lastActiveScore: number;
      quotaUsageScore: number;
      featureAdoptionScore: number;
      supportTicketPenalty: number;
      churnPredictorPenalty: number;
      featureUsageDetails?: {
        totalFeaturesAvailable: number;
        featuresUsed: number;
        featureUsageStats: Array<{
          featureName: string;
          usageCount: number;
          percentage: number;
        }>;
      };
    };
  };
  lastActivity: Date | null;
}

const USD_TO_CHF_RATE = 0.91;

// Helper functions
async function calculateTokenUsage(tenantId: string) {
  const now = new Date();
  const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);

  const tokenUsage = await db.tokenUsage.findMany({
    where: {
      tenantId,
      timestamp: {
        gte: startOfMonth,
        lte: now,
      },
    },
    select: {
      inputTokens: true,
      outputTokens: true,
      cost: true,
    },
  });

  const totalInputTokens = tokenUsage.reduce((total, usage) => {
    const inputTokens = Number(usage.inputTokens) || 0;
    return total + inputTokens;
  }, 0);

  const totalOutputTokens = tokenUsage.reduce((total, usage) => {
    const outputTokens = Number(usage.outputTokens) || 0;
    return total + outputTokens;
  }, 0);

  const totalTokens = totalInputTokens + totalOutputTokens;

  const totalCost = tokenUsage.reduce((total, usage) => {
    const cost = Number(usage.cost) || 0;
    return total + cost;
  }, 0);

  return {
    current: Number.isFinite(totalTokens) ? totalTokens : 0,
    inputTokens: Number.isFinite(totalInputTokens) ? totalInputTokens : 0,
    outputTokens: Number.isFinite(totalOutputTokens) ? totalOutputTokens : 0,
    cost: Number.isFinite(totalCost * USD_TO_CHF_RATE)
      ? totalCost * USD_TO_CHF_RATE
      : 0,
  };
}

async function calculateStorageUsage(tenantId: string) {
  const now = new Date();
  const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);

  // Get all files for this tenant's workspaces created this month
  const files = await db.file.findMany({
    where: {
      workspace: {
        tenantId: tenantId,
      },
      createdAt: {
        gte: startOfMonth,
        lte: now,
      },
    },
    select: {
      size: true,
    },
  });

  // Calculate total storage in GB
  const totalBytes = files.reduce((total, file) => {
    if (!file.size) return total;

    // Parse size string (e.g., "1.5MB", "500KB", "2GB")
    const sizeStr = file.size.toString();
    const match = sizeStr.match(/^([\d.]+)\s*(KB|MB|GB)?$/i);

    if (!match) return total;

    const value = parseFloat(match[1]);
    const unit = (match[2] || "B").toUpperCase();

    switch (unit) {
      case "KB":
        return total + value * 1024;
      case "MB":
        return total + value * 1024 * 1024;
      case "GB":
        return total + value * 1024 * 1024 * 1024;
      default:
        return total + value; // Assume bytes
    }
  }, 0);

  const totalGB = totalBytes / (1024 * 1024 * 1024);

  // Get actual storage usage from VectorStoreUsage table
  const vectorStoreUsage = await db.vectorStoreUsage.findMany({
    where: {
      tenantId: tenantId,
      timestamp: {
        gte: startOfMonth,
        lte: now,
      },
    },
    select: {
      usageGB: true,
      timestamp: true,
    },
    orderBy: {
      timestamp: "desc",
    },
  });

  // Get the latest storage usage for the month
  const latestStorageUsage =
    vectorStoreUsage.length > 0 ? vectorStoreUsage[0].usageGB : 0;

  // Get storage tier pricing to calculate costs
  const storageTiers = await db.storageTier.findMany({
    where: {
      isActive: true,
    },
    select: {
      sizeGB: true,
      price: true,
    },
    orderBy: {
      sizeGB: "asc",
    },
  });

  // Calculate storage cost based on usage and tier pricing
  let actualStorageCost = 0;
  if (latestStorageUsage > 0 && storageTiers.length > 0) {
    // Find the appropriate tier for the usage
    const appropriateTier =
      storageTiers.find((tier) => latestStorageUsage <= tier.sizeGB) ||
      storageTiers[storageTiers.length - 1];

    if (appropriateTier) {
      // Calculate proportional cost based on usage
      const usageRatio = latestStorageUsage / appropriateTier.sizeGB;
      actualStorageCost = appropriateTier.price * usageRatio;
    }
  }

  return {
    current: totalGB,
    cost: actualStorageCost,
  };
}

async function calculateApiUsage(tenantId: string) {
  const now = new Date();
  const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);

  const apiRequests = await db.aPIRequest.findMany({
    where: {
      tenantId,
      timestamp: {
        gte: startOfMonth,
        lte: now,
      },
    },
    select: {
      id: true,
    },
  });

  const current = apiRequests.length;

  // Get actual API costs from database if available
  // For now, return 0 cost since we don't have actual API cost data in the database
  return {
    current,
    cost: 0, // No hardcoded API cost - would need actual cost tracking
  };
}

async function calculateDailyTokenUsage(tenantId: string) {
  const now = new Date();
  const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

  const tokenUsage = await db.tokenUsage.findMany({
    where: {
      tenantId,
      timestamp: {
        gte: thirtyDaysAgo,
        lte: now,
      },
    },
    select: {
      inputTokens: true,
      outputTokens: true,
      timestamp: true,
    },
  });

  // Group by date and sum tokens
  const dailyUsageMap = new Map<string, number>();

  // Initialize all days with 0
  for (let i = 0; i < 30; i++) {
    const date = new Date(thirtyDaysAgo.getTime() + i * 24 * 60 * 60 * 1000);
    const dateStr = date.toISOString().split("T")[0];
    dailyUsageMap.set(dateStr, 0);
  }

  // Aggregate actual usage
  tokenUsage.forEach((usage) => {
    const dateStr = usage.timestamp.toISOString().split("T")[0];
    const inputTokens = Number(usage.inputTokens) || 0;
    const outputTokens = Number(usage.outputTokens) || 0;
    const totalTokens = inputTokens + outputTokens;

    if (Number.isFinite(totalTokens)) {
      dailyUsageMap.set(
        dateStr,
        (dailyUsageMap.get(dateStr) || 0) + totalTokens
      );
    }
  });

  // Convert to array format for charts
  return Array.from(dailyUsageMap.entries()).map(([date, totalTokens]) => {
    const formattedDate = new Date(date).toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
    });
    const validTokens = Number.isFinite(totalTokens) ? totalTokens : 0;

    return {
      date: formattedDate,
      totalTokens: validTokens,
    };
  });
}

async function calculateFeatureBreakdown(tenantId: string) {
  const now = new Date();
  const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);

  const tokenUsage = await db.tokenUsage.findMany({
    where: {
      tenantId,
      timestamp: {
        gte: startOfMonth,
        lte: now,
      },
    },
    select: {
      inputTokens: true,
      outputTokens: true,
      requestType: true,
    },
  });

  // Group by request type
  const featureMap = new Map<string, { totalTokens: number; count: number }>();

  tokenUsage.forEach((usage) => {
    const requestType = usage.requestType || "unknown";
    const inputTokens = Number(usage.inputTokens) || 0;
    const outputTokens = Number(usage.outputTokens) || 0;
    const totalTokens = inputTokens + outputTokens;

    if (!Number.isFinite(totalTokens)) {
      return; // Skip invalid entries
    }

    if (!featureMap.has(requestType)) {
      featureMap.set(requestType, { totalTokens: 0, count: 0 });
    }

    const current = featureMap.get(requestType)!;
    current.totalTokens += totalTokens;
    current.count += 1;
  });

  const totalTokensAllFeatures = Array.from(featureMap.values()).reduce(
    (sum, feature) => sum + feature.totalTokens,
    0
  );

  // Convert to array format with percentages and validate data
  return Array.from(featureMap.entries())
    .map(([requestType, data]) => {
      const totalTokens = Number(data.totalTokens) || 0;
      const count = Number(data.count) || 0;
      const percentage =
        totalTokensAllFeatures > 0
          ? (totalTokens / totalTokensAllFeatures) * 100
          : 0;

      return {
        requestType: requestType.charAt(0).toUpperCase() + requestType.slice(1),
        totalTokens: Number.isFinite(totalTokens) ? totalTokens : 0,
        count: Number.isFinite(count) ? count : 0,
        percentage: Number.isFinite(percentage) ? percentage : 0,
      };
    })
    .filter((item) => item.totalTokens > 0 && Number.isFinite(item.totalTokens)) // Filter out invalid entries
    .sort((a, b) => b.totalTokens - a.totalTokens);
}

async function getLastActivity(tenantId: string): Promise<Date | null> {
  const lastApiRequest = await db.aPIRequest.findFirst({
    where: { tenantId },
    orderBy: { timestamp: "desc" },
    select: { timestamp: true },
  });

  const lastTokenUsage = await db.tokenUsage.findFirst({
    where: { tenantId },
    orderBy: { timestamp: "desc" },
    select: { timestamp: true },
  });

  const dates = [lastApiRequest?.timestamp, lastTokenUsage?.timestamp].filter(
    Boolean
  ) as Date[];

  return dates.length > 0
    ? new Date(Math.max(...dates.map((d) => d.getTime())))
    : null;
}

async function calculateDailyFinancialTrends(tenantId: string): Promise<
  Array<{
    date: string;
    revenue: number;
    infraCost: number;
    margin: number;
    marginPercent: number;
  }>
> {
  const now = new Date();
  const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

  // Get daily token usage costs
  const dailyTokenCosts = await db.tokenUsage.findMany({
    where: {
      tenantId,
      timestamp: {
        gte: thirtyDaysAgo,
        lte: now,
      },
    },
    select: {
      cost: true,
      timestamp: true,
    },
  });

  // Get daily storage usage (VectorStoreUsage doesn't have cost field)
  const dailyStorageUsage = await db.vectorStoreUsage.findMany({
    where: {
      tenantId,
      timestamp: {
        gte: thirtyDaysAgo,
        lte: now,
      },
    },
    select: {
      usageGB: true,
      timestamp: true,
    },
  });

  // Get storage tier pricing for cost calculation
  const storageTiers = await db.storageTier.findMany({
    where: {
      isActive: true,
    },
    select: {
      sizeGB: true,
      price: true,
    },
    orderBy: {
      sizeGB: "asc",
    },
  });

  // Get tenant's subscription for revenue calculation
  const subscription = await db.subscription.findFirst({
    where: {
      tenantId,
      isActive: true,
    },
    select: {
      plan: {
        select: {
          price: true,
        },
      },
    },
  });

  // Group costs by date
  const dailyCostMap = new Map<
    string,
    { tokenCost: number; storageCost: number }
  >();

  // Initialize all days with 0 costs
  for (let i = 0; i < 30; i++) {
    const date = new Date(thirtyDaysAgo.getTime() + i * 24 * 60 * 60 * 1000);
    const dateStr = date.toISOString().split("T")[0];
    dailyCostMap.set(dateStr, { tokenCost: 0, storageCost: 0 });
  }

  // Aggregate token costs by day
  dailyTokenCosts.forEach((usage) => {
    const dateStr = usage.timestamp.toISOString().split("T")[0];
    const cost = Number(usage.cost) || 0;
    const existing = dailyCostMap.get(dateStr);
    if (existing) {
      existing.tokenCost += cost;
    }
  });

  // Calculate storage costs by day using usage and tier pricing
  dailyStorageUsage.forEach((usage) => {
    const dateStr = usage.timestamp.toISOString().split("T")[0];
    const usageGB = Number(usage.usageGB) || 0;

    // Calculate cost based on usage and tier pricing
    let cost = 0;
    if (usageGB > 0 && storageTiers.length > 0) {
      const appropriateTier =
        storageTiers.find((tier) => usageGB <= tier.sizeGB) ||
        storageTiers[storageTiers.length - 1];
      if (appropriateTier) {
        const usageRatio = usageGB / appropriateTier.sizeGB;
        cost = appropriateTier.price * usageRatio;
      }
    }

    const existing = dailyCostMap.get(dateStr);
    if (existing) {
      existing.storageCost += cost;
    }
  });

  // Calculate daily revenue (only if subscription exists, otherwise 0)
  const monthlyRevenue = subscription?.plan?.price
    ? Number(subscription.plan.price) * USD_TO_CHF_RATE
    : 0;
  const dailyRevenue = monthlyRevenue / 30; // Simple daily distribution

  // Convert to array format for charts
  return Array.from(dailyCostMap.entries())
    .map(([date, costs]) => {
      const formattedDate = new Date(date).toLocaleDateString("en-US", {
        month: "short",
        day: "numeric",
      });
      const totalInfraCost = costs.tokenCost + costs.storageCost;
      const margin = dailyRevenue - totalInfraCost;
      const marginPercent =
        dailyRevenue > 0 ? (margin / dailyRevenue) * 100 : 0;

      return {
        date: formattedDate,
        revenue: Number.isFinite(dailyRevenue) ? dailyRevenue : 0,
        infraCost: Number.isFinite(totalInfraCost) ? totalInfraCost : 0,
        margin: Number.isFinite(margin) ? margin : 0,
        marginPercent: Number.isFinite(marginPercent) ? marginPercent : 0,
      };
    })
    .filter(
      (item) =>
        Number.isFinite(item.revenue) &&
        Number.isFinite(item.infraCost) &&
        Number.isFinite(item.margin) &&
        Number.isFinite(item.marginPercent)
    );
}

// Helper function to format feature names for display
function formatFeatureName(requestType: string): string {
  const featureNameMap: { [key: string]: string } = {
    Rag_query: "RAG Query",
    Web_search: "Web Search",
    Document_upload: "Document Upload",
    Chat_completion: "Chat Completion",
    Embedding_generation: "Embedding Generation",
    Text_summarization: "Text Summarization",
  };

  return (
    featureNameMap[requestType] ||
    requestType.replace(/_/g, " ").replace(/\b\w/g, (l) => l.toUpperCase())
  );
}

async function calculateHealthScore(
  tenantId: string,
  tenant: any
): Promise<{
  score: number;
  breakdown: {
    lastActiveScore: number;
    quotaUsageScore: number;
    featureAdoptionScore: number;
    supportTicketPenalty: number;
    churnPredictorPenalty: number;
    featureUsageDetails?: {
      totalFeaturesAvailable: number;
      featuresUsed: number;
      featureUsageStats: Array<{
        featureName: string;
        usageCount: number;
        percentage: number;
      }>;
    };
  };
}> {
  let breakdown = {
    lastActiveScore: 0,
    quotaUsageScore: 0,
    featureAdoptionScore: 0,
    supportTicketPenalty: 0,
    churnPredictorPenalty: 0,
    featureUsageDetails: {
      totalFeaturesAvailable: 0,
      featuresUsed: 0,
      featureUsageStats: [] as Array<{
        featureName: string;
        usageCount: number;
        percentage: number;
      }>,
    },
  };

  // 1. Last Active Days (30% weight)
  let lastActiveScore = 25; // Default for no activity
  if (tenant.lastActivity) {
    const daysSinceActivity = Math.floor(
      (Date.now() - new Date(tenant.lastActivity).getTime()) /
        (1000 * 60 * 60 * 24)
    );
    if (daysSinceActivity <= 7) lastActiveScore = 100;
    else if (daysSinceActivity <= 30) lastActiveScore = 75;
    else if (daysSinceActivity <= 90) lastActiveScore = 50;
    else lastActiveScore = 25;
  }
  breakdown.lastActiveScore = lastActiveScore;

  // 2. Quota Usage Sustained (25% weight)
  let quotaUsageScore = 100;
  if (tenant.plan) {
    // Get plan limits and calculate usage percentage
    const plan = await db.plan.findUnique({
      where: { id: tenant.plan.id },
      select: {
        vectorStoreGB: true,
        webSearchLimit: true,
      },
    });

    if (plan) {
      // Calculate storage usage percentage
      const storageUsagePercent =
        plan.vectorStoreGB > 0
          ? (tenant.usage.storage.current / plan.vectorStoreGB) * 100
          : 0;

      // Since there's no token limit in the plan, we'll use a reasonable threshold
      // based on typical usage patterns (e.g., 100,000 tokens per month for active usage)
      const reasonableTokenThreshold = 100000;
      const tokenUsagePercent =
        tenant.usage.tokenUsage.current > 0
          ? (tenant.usage.tokenUsage.current / reasonableTokenThreshold) * 100
          : 0;

      // Use the higher of the two usage percentages
      const maxUsagePercent = Math.max(storageUsagePercent, tokenUsagePercent);

      if (maxUsagePercent <= 50) quotaUsageScore = 100;
      else if (maxUsagePercent <= 80) quotaUsageScore = 75;
      else if (maxUsagePercent <= 95) quotaUsageScore = 50;
      else quotaUsageScore = 25;
    }
  }
  breakdown.quotaUsageScore = quotaUsageScore;

  // 3. Feature Adoption (20% weight)
  // Use TokenUsage table which has requestType field for better feature adoption tracking
  const tokenUsageRequests = await db.tokenUsage.findMany({
    where: { tenantId },
    select: { requestType: true, inputTokens: true, outputTokens: true },
  });

  // Calculate detailed feature usage statistics
  const featureUsageMap = new Map<string, { count: number; tokens: number }>();
  let totalUsageCount = 0;
  let totalTokens = 0;

  tokenUsageRequests.forEach(
    (req: {
      requestType: string;
      inputTokens: number;
      outputTokens: number;
    }) => {
      const existing = featureUsageMap.get(req.requestType) || {
        count: 0,
        tokens: 0,
      };
      const tokens = (req.inputTokens || 0) + (req.outputTokens || 0);
      featureUsageMap.set(req.requestType, {
        count: existing.count + 1,
        tokens: existing.tokens + tokens,
      });
      totalUsageCount++;
      totalTokens += tokens;
    }
  );

  // Define available features (this could be made configurable)
  const availableFeatures = [
    "Rag_query",
    "Web_search",
    "Document_upload",
    "Chat_completion",
    "Embedding_generation",
    "Text_summarization",
  ];

  // Create feature usage statistics
  const featureUsageStats = Array.from(featureUsageMap.entries())
    .map(([requestType, stats]) => ({
      featureName: formatFeatureName(requestType),
      usageCount: stats.count,
      percentage:
        totalUsageCount > 0
          ? Math.round((stats.count / totalUsageCount) * 100)
          : 0,
    }))
    .sort((a, b) => b.usageCount - a.usageCount);

  const uniqueFeatures = featureUsageMap.size;
  const totalFeaturesAvailable = availableFeatures.length;

  let featureAdoptionScore = 25; // Default for no usage
  if (uniqueFeatures >= 3) featureAdoptionScore = 100;
  else if (uniqueFeatures === 2) featureAdoptionScore = 75;
  else if (uniqueFeatures === 1) featureAdoptionScore = 50;

  breakdown.featureAdoptionScore = featureAdoptionScore;
  breakdown.featureUsageDetails = {
    totalFeaturesAvailable,
    featuresUsed: uniqueFeatures,
    featureUsageStats,
  };

  // 4. Support Ticket Volume (-15% penalty)
  // Note: Assuming support tickets would be in a separate table
  // For now, using a placeholder calculation based on tenant status
  let supportTicketPenalty = 0;
  if (tenant.status === "suspended") supportTicketPenalty = -15;
  else if (tenant.status === "inactive") supportTicketPenalty = -5;
  breakdown.supportTicketPenalty = supportTicketPenalty;

  // 5. Churn Predictors (-10% penalty)
  let churnPredictorPenalty = 0;

  // Check for churn indicators
  const hasRecentActivity =
    tenant.lastActivity &&
    Date.now() - new Date(tenant.lastActivity).getTime() <
      7 * 24 * 60 * 60 * 1000;

  const hasUsage =
    tenant.usage.tokenUsage.current > 0 ||
    tenant.usage.apiRequests.current > 0 ||
    tenant.usage.storage.current > 0;

  if (!hasRecentActivity && !hasUsage)
    churnPredictorPenalty = -10; // Major issues
  else if (!hasRecentActivity || !hasUsage) churnPredictorPenalty = -5; // Minor issues
  breakdown.churnPredictorPenalty = churnPredictorPenalty;

  // Calculate weighted final score
  const weightedScore =
    lastActiveScore * 0.3 +
    quotaUsageScore * 0.25 +
    featureAdoptionScore * 0.2 +
    supportTicketPenalty +
    churnPredictorPenalty;

  const finalScore = Math.max(0, Math.min(100, Math.round(weightedScore)));

  return {
    score: finalScore,
    breakdown,
  };
}

async function fetchTenantDetail(
  tenantId: string
): Promise<TenantDetail | null> {
  // First, get the basic tenant data without restrictions to handle missing llmScope field
  const tenant = await db.tenant.findUnique({
    where: { id: tenantId },
    include: {
      Subscription: {
        where: { isActive: true },
        include: {
          plan: {
            select: {
              id: true,
              name: true,
              type: true,
              price: true,
            },
          },
        },
      },
      Membership: {
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      },
    },
  });

  if (!tenant) return null;

  const plan = tenant.Subscription[0]?.plan || null;

  // Calculate usage metrics
  const tokenUsage = await calculateTokenUsage(tenantId);
  const storageUsage = await calculateStorageUsage(tenantId);
  const apiUsage = await calculateApiUsage(tenantId);
  const lastActivity = await getLastActivity(tenantId);

  // Calculate token analytics
  const dailyTokenUsage = await calculateDailyTokenUsage(tenantId);
  const featureBreakdown = await calculateFeatureBreakdown(tenantId);

  // Calculate financial metrics
  const infraCost = tokenUsage.cost + storageUsage.cost + apiUsage.cost;
  const revenue = plan ? Number(plan.price) * USD_TO_CHF_RATE : 0;
  const margin = revenue - infraCost;

  // Get real daily financial trend data from database
  const dailyFinancialTrends = await calculateDailyFinancialTrends(tenantId);

  const tenantDetail: TenantDetail = {
    id: tenant.id,
    name: tenant.name,
    slug: tenant.slug,
    createdAt: tenant.createdAt,
    plan,
    status: tenant.status || "inactive", // ✅ Use database status field
    // Handle missing llmScope field for older tenants - default to ["INTERNAL_ONLY"]
    llmScope: (tenant.llmScope as string[]) || ["INTERNAL_ONLY"],
    // Handle missing fileUploadLimitDuringChat field for older tenants - default to 5
    fileUploadLimitDuringChat: (tenant as any).fileUploadLimitDuringChat || 5,
    users: tenant.Membership.map((m: any) => ({
      id: m.user.id,
      name: m.user.name || "Unknown",
      email: m.user.email,
      role: m.role,
      lastSeen: null, // Would need to track this separately
      status: "active",
    })),
    usage: {
      tokenUsage: {
        current: tokenUsage.current,
        inputTokens: tokenUsage.inputTokens,
        outputTokens: tokenUsage.outputTokens,
      },
      storage: {
        current: storageUsage.current,
      },
      apiRequests: {
        current: apiUsage.current,
      },
    },
    tokenAnalytics: {
      dailyUsage: dailyTokenUsage,
      featureBreakdown: featureBreakdown,
    },
    financial: {
      infraCost,
      revenue,
      margin,
      costBreakdown: {
        tokenCost: tokenUsage.cost,
        storageCost: storageUsage.cost,
        apiCost: apiUsage.cost,
      },
      dailyTrends: dailyFinancialTrends,
    },
    healthScore: {
      score: 0,
      breakdown: {
        lastActiveScore: 0,
        quotaUsageScore: 0,
        featureAdoptionScore: 0,
        supportTicketPenalty: 0,
        churnPredictorPenalty: 0,
        featureUsageDetails: {
          totalFeaturesAvailable: 0,
          featuresUsed: 0,
          featureUsageStats: [],
        },
      },
    }, // Will be calculated below
    lastActivity,
  };

  tenantDetail.healthScore = await calculateHealthScore(tenantId, tenantDetail);

  return tenantDetail;
}

async function fetchAvailablePlans(): Promise<Plan[]> {
  // Get all active plans for the Change Plan functionality
  const plans = await db.plan.findMany({
    where: {
      isActive: true,
    },
    select: {
      id: true,
      name: true,
      type: true,
      description: true,
      price: true,
      includedUsers: true,
      vectorStoreGB: true,
      webSearchLimit: true,
      additionalUserFee: true,
    },
    orderBy: [{ type: "asc" }, { price: "asc" }],
  });

  // ✅ Transform to match the expected Plan interface with proper type conversion
  return plans.map((plan: any) => ({
    id: plan.id,
    name: plan.name,
    type: plan.type,
    description: plan.description || undefined, // Convert null to undefined
    price: plan.price || undefined, // Handle optional price
    includedUsers: plan.includedUsers,
    vectorStoreGB: plan.vectorStoreGB,
    webSearchLimit: plan.webSearchLimit,
    additionalUserFee: plan.additionalUserFee || undefined, // Handle optional fee
  })) as Plan[];
}

export default async function TenantDetailPage({
  params,
}: {
  params: { tenantId: string };
}) {
  const session = await getServerSession(authOptions);

  if (!session) {
    redirect("/login");
  }

  // Fetch tenant detail and available plans in parallel
  const [tenantDetail, availablePlans] = await Promise.all([
    fetchTenantDetail(params.tenantId),
    fetchAvailablePlans(),
  ]);

  if (!tenantDetail) {
    notFound();
  }

  return (
    <TenantDetailClient tenant={tenantDetail} availablePlans={availablePlans} />
  );
}
