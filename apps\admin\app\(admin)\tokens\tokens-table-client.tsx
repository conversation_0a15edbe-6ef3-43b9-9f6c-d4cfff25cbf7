"use client";

import { useState, useEffect, useCallback, useTransition } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { formatCurrency, formatNumber, formatDate, formatTime } from "@/lib/utils";
import { Search, X } from "lucide-react";
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  <PERSON><PERSON>hart,
  Pie,
  Cell,
} from "recharts";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

// Custom hook for debouncing values
function useDebounceLocal<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
}

// Types
interface TokenUsage {
  id: string;
  userId: string | null;
  userName: string;
  tenantId: string;
  tenantName: string;
  inputTokens: number;
  outputTokens: number;
  totalTokens: number;
  timestamp: string;
  requestType: string;
  modelUsed: string;
  cost: number;
}

interface MonthlyUsage {
  name: string;
  input: number;
  output: number;
  total: number;
}
interface ModelUsage {
  name: string;
  value: number;
}

interface TenantUsage {
  name: string;
  input: number;
  output: number;
  cost: number;
}

interface TokensData {
  tokenUsages: TokenUsage[];
  monthlyUsage: MonthlyUsage[];
  modelUsage: ModelUsage[];
  tenantUsage: TenantUsage[];
  allTanent: {
    id: string;
    name: string;
  }[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  searchQuery: string;
}

interface TokensTableClientProps {
  data: TokensData;
}

const COLORS = ["#4f46e5", "#06b6d4", "#10b981", "#f59e0b", "#ef4444", "#8b5cf6", "#ec4899"];

export function TokensTableClient({ data }: TokensTableClientProps) {
  const { tokenUsages, monthlyUsage, modelUsage, tenantUsage, pagination, searchQuery, allTanent } = data;
  const router = useRouter();
  const searchParams = useSearchParams();
  const [isPending, startTransition] = useTransition();
  const [searchInput, setSearchInput] = useState(searchQuery);
  const [selectedTenantId, setSelectedTenantId] = useState<string | undefined>();

  // Debounce the search input to avoid excessive URL updates
  const debouncedSearchInput = useDebounceLocal(searchInput, 400);

  // Handle search with URL update
  const handleSearch = useCallback((query: string) => {
    const params = new URLSearchParams(searchParams?.toString());
    if (query.trim()) {
      params.set("search", query.trim());
    } else {
      params.delete("search");
    }
    params.set("page", "1"); // Reset to first page on search

    startTransition(() => {
      router.push(`/tokens?${params.toString()}`);
    });
  }, [searchParams, router]);

  // Effect to trigger search when debounced input changes
  useEffect(() => {
    // Only trigger search if the debounced value is different from current search query
    if (debouncedSearchInput !== searchQuery) {
      handleSearch(debouncedSearchInput);
    }
  }, [debouncedSearchInput, searchQuery, handleSearch]);

  // Effect to get tenant ID from URL parameters
  useEffect(() => {
    const tenantIdFromURL = searchParams?.get("tenant") || "";
    setSelectedTenantId(tenantIdFromURL);
  }, [searchParams]);

  // Handle pagination
  const handlePageChange = (newPage: number) => {
    const params = new URLSearchParams(searchParams?.toString());
    params.set("page", newPage.toString());

    startTransition(() => {
      router.push(`/tokens?${params.toString()}`);
    });
  };

  // Handle clear search
  const handleClearSearch = () => {
    setSearchInput("");
    // The useEffect will handle the URL update when debouncedSearchInput changes
  };

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (!active || !payload || !payload.length) return null;

    return (
      <div className="rounded-lg border bg-card p-3 text-sm shadow-lg">
        <div className="mb-2 font-semibold">{label}</div>
        {payload.map((entry: any, index: number) => (
          <div key={index} className="flex items-center justify-between gap-4 py-1">
            <div className="flex items-center gap-2">
              <div
                className="h-3 w-3 rounded-full"
                style={{ backgroundColor: entry.color }}
              />
              <span className="font-medium">{entry.name}</span>
            </div>
            <span className="font-semibold">{entry.value.toLocaleString()}</span>
          </div>
        ))}
      </div>
    );
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold">Token Usage</h1>
        <p className="text-muted-foreground">
          Monitor token usage across all tenants and users
        </p>
      </div>

      {/* Charts */}
      <div className="grid gap-6 md:grid-cols-2">
        <Card className="overflow-hidden border shadow-md hover:shadow-lg transition-shadow duration-300">
          <CardHeader className="bg-muted/30">
            <CardTitle className="text-lg font-semibold">Monthly Token Usage</CardTitle>
          </CardHeader>
          <CardContent className="h-[300px] pt-6">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={monthlyUsage}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip content={<CustomTooltip />} />
                <Bar dataKey="input" name="Input Tokens" fill="#4f46e5" />
                <Bar dataKey="output" name="Output Tokens" fill="#06b6d4" />
                <Bar dataKey="total" name="Total Tokens" fill="#10b981" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        <Card className="overflow-hidden border shadow-md hover:shadow-lg transition-shadow duration-300">
          <CardHeader className="bg-muted/30">
            <CardTitle className="text-lg font-semibold">Model Usage Distribution</CardTitle>
          </CardHeader>
          <CardContent className="h-[300px] pt-6">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={modelUsage}
                  cx="50%"
                  cy="50%"
                  outerRadius={80}
                  innerRadius={30}
                  paddingAngle={2}
                  dataKey="value"
                  labelLine={false}
                >
                  {modelUsage.map((_, index) => (
                    <Cell
                      key={`cell-${index}`}
                      fill={COLORS[index % COLORS.length]}
                      stroke="#fff"
                      strokeWidth={1}
                    />
                  ))}
                </Pie>
                <Tooltip
                  content={<CustomTooltip />}
                />
                <Legend
                  layout="vertical"
                  align="right"
                  verticalAlign="middle"
                  iconType="circle"
                  iconSize={8}
                  wrapperStyle={{
                    paddingLeft: 20,
                    fontSize: 12
                  }}
                />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      <Card className="overflow-hidden border shadow-md hover:shadow-lg transition-shadow duration-300">
        <CardHeader className="bg-muted/30">
          <CardTitle className="text-lg font-semibold">Token Usage by Tenant</CardTitle>
        </CardHeader>
        <CardContent className="h-[400px] pt-6">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart
              data={tenantUsage}
              margin={{
                top: 20,
                right: 30,
                left: 20,
                bottom: 5,
              }}
            >
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip content={<CustomTooltip />} />
              <Legend />
              <Bar
                dataKey="input"
                name="Input Tokens"
                fill="#8884d8"
              />
              <Bar
                dataKey="output"
                name="Output Tokens"
                fill="#82ca9d"
              />
            </BarChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      {/* Token Usage Table */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>
              Token Usage Details
              {searchQuery && (
                <span className="ml-2 text-sm font-normal text-muted-foreground">
                  (filtered by "{searchQuery}")
                </span>
              )}
            </CardTitle>
            <div className="flex gap-2 w-full md:w-auto">
              {/* Tenant Dropdown */}
              <div className="relative w-[180px]">
                <Select
                  value={selectedTenantId}
                  onValueChange={(value) => {
                    setSelectedTenantId(value);
                    const params = new URLSearchParams(searchParams?.toString());

                    if (value) {
                      params.set("tenant", value);
                    } else {
                      params.delete("tenant");
                    }

                    params.set("page", "1");

                    startTransition(() => {
                      router.push(`/tokens?${params.toString()}`);
                    });
                  }}
                >
                  <SelectTrigger className={`${selectedTenantId ? 'pr-8' : ''}`}>
                    <SelectValue placeholder="Select tenant" />
                  </SelectTrigger>
                  <SelectContent>
                    {allTanent.map((tenant) => (
                      <SelectItem key={tenant.name} value={tenant.name}>
                        {tenant.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                {selectedTenantId ? (
                  <button
                    type="button"
                    onClick={() => {
                      setSelectedTenantId("");
                      const params = new URLSearchParams(searchParams?.toString());
                      params.delete("tenant");
                      params.set("page", "1");

                      startTransition(() => {
                        router.push(`/tokens?${params.toString()}`);
                      });
                    }}
                    className="absolute right-2 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground"
                  >
                    <X className="h-4 w-4" />
                  </button>
                ) : null}
              </div>

              {/* Search Input */}
              <div className="relative w-64">
                <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                <Input
                  placeholder="Search users, tenants, models..."
                  value={searchInput}
                  onChange={(e) => setSearchInput(e.target.value)}
                  className="pl-9 pr-9"
                />
                {searchInput && (
                  <button
                    onClick={handleClearSearch}
                    className="absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground"
                    type="button"
                  >
                    <X className="h-4 w-4" />
                  </button>
                )}
              </div>
            </div>
          </div>
          {isPending && (
            <div className="mt-2 text-sm text-muted-foreground">
              Searching...
            </div>
          )}
        </CardHeader>
        <CardContent>
          {tokenUsages.length === 0 ? (
            <div className="flex h-32 items-center justify-center">
              <p className="text-muted-foreground">
                {searchQuery ? "No token usage found matching your search." : "No token usage found."}
              </p>
            </div>
          ) : (
            <>
              <div className="overflow-x-auto">
                <table className="w-full text-sm">
                  <thead>
                    <tr className="border-b">
                      <th className="px-4 py-3 text-left font-medium">User</th>
                      <th className="px-4 py-3 text-left font-medium">Tenant</th>
                      <th className="px-4 py-3 text-left font-medium">Model</th>
                      <th className="px-4 py-3 text-left font-medium">Request Type</th>
                      <th className="px-4 py-3 text-left font-medium">Input Tokens</th>
                      <th className="px-4 py-3 text-left font-medium">Output Tokens</th>
                      <th className="px-4 py-3 text-left font-medium">Total Tokens</th>
                      <th className="px-4 py-3 text-left font-medium">Cost (CHF)</th>
                      <th className="px-4 py-3 text-left font-medium">Timestamp</th>
                    </tr>
                  </thead>
                  <tbody>
                    {tokenUsages.map((usage) => (
                      <tr key={usage.id} className="border-b">
                        <td className="px-4 py-3">{usage.userName}</td>
                        <td className="px-4 py-3">{usage.tenantName}</td>
                        <td className="px-4 py-3">{usage.modelUsed}</td>
                        <td className="px-4 py-3">
                          <span className="rounded-full bg-blue-100 px-2 py-1 text-xs font-medium text-blue-800 dark:bg-blue-800 dark:text-blue-100">
                            {usage.requestType}
                          </span>
                        </td>
                        <td className="px-4 py-3">{formatNumber(usage.inputTokens)}</td>
                        <td className="px-4 py-3">{formatNumber(usage.outputTokens)}</td>
                        <td className="px-4 py-3">{formatNumber(usage.totalTokens)}</td>
                        <td className="px-4 py-3">{usage.cost.toFixed(7)}</td>
                        <td className="px-4 py-3">
                          <div className="text-xs">
                            <div>{formatDate(usage.timestamp)}</div>
                            <div className="text-muted-foreground">{formatTime(usage.timestamp)}</div>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {/* Pagination */}
              {pagination.totalPages > 1 && (
                <div className="mt-4 flex items-center justify-between">
                  <div className="text-sm text-muted-foreground">
                    Showing {(pagination.page - 1) * pagination.limit + 1} to{" "}
                    {Math.min(pagination.page * pagination.limit, pagination.total)} of{" "}
                    {pagination.total} results
                  </div>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePageChange(pagination.page - 1)}
                      disabled={pagination.page <= 1 || isPending}
                    >
                      Previous
                    </Button>
                    <div className="flex gap-1">
                      {Array.from({ length: Math.min(5, pagination.totalPages) }, (_, i) => {
                        const pageNum = Math.max(1, pagination.page - 2) + i;
                        if (pageNum > pagination.totalPages) return null;

                        return (
                          <Button
                            key={pageNum}
                            variant={pageNum === pagination.page ? "default" : "outline"}
                            size="sm"
                            onClick={() => handlePageChange(pageNum)}
                            disabled={isPending}
                            className="w-8"
                          >
                            {pageNum}
                          </Button>
                        );
                      })}
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePageChange(pagination.page + 1)}
                      disabled={pagination.page >= pagination.totalPages || isPending}
                    >
                      Next
                    </Button>
                  </div>
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>

      {/* Cost Analysis Summary */}
      <Card>
        <CardHeader>
          <CardTitle>Cost Analysis Summary</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b">
                  <th className="px-4 py-3 text-left font-medium">Tenant</th>
                  <th className="px-4 py-3 text-left font-medium">Input Tokens</th>
                  <th className="px-4 py-3 text-left font-medium">Output Tokens</th>
                  <th className="px-4 py-3 text-left font-medium">Total Tokens</th>
                  <th className="px-4 py-3 text-left font-medium">Estimated Cost (CHF)</th>
                </tr>
              </thead>
              <tbody>
                {tenantUsage.map((tenant, index) => {
                  const totalTokens = tenant.input + tenant.output;
                  // Calculate cost based on the tenant usage data which already includes aggregated costs
                  const totalCost = tenant.cost || 0;

                  return (
                    <tr key={index} className="border-b">
                      <td className="px-4 py-3">{tenant.name}</td>
                      <td className="px-4 py-3">{formatNumber(tenant.input)}</td>
                      <td className="px-4 py-3">{formatNumber(tenant.output)}</td>
                      <td className="px-4 py-3">{formatNumber(totalTokens)}</td>
                      <td className="px-4 py-3">{totalCost.toFixed(7)}</td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
