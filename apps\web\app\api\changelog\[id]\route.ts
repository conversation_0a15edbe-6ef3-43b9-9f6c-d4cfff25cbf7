import { NextRequest, NextResponse } from "next/server";
import { authOptions } from "@/lib/next-auth";
import db from "@/lib/shared-db";
import { getServerSession } from "next-auth/next";

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session: any = await getServerSession(authOptions);
    if (!session?.userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const changelogId = params.id;

    const changelog = await db.changelog.findUnique({
      where: { id: changelogId },
    });

    if (!changelog) {
      return NextResponse.json(
        { error: "Changelog not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({ data: changelog });
  } catch (error) {
    console.error("Error fetching changelog:", error);
    return NextResponse.json(
      { error: "Failed to fetch changelog" },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session: any = await getServerSession(authOptions);
    if (!session?.userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if user is admin
    const user = await db.user.findUnique({
      where: { id: session.user.id },
      include: { membership: true },
    });

    const isAdmin = user?.membership?.some(
      (m) => m.role === "ADMIN" || m.role === "OWNER"
    );
    if (!isAdmin) {
      return NextResponse.json(
        { error: "Admin access required" },
        { status: 403 }
      );
    }

    const changelogId = params.id;
    const body = await request.json();

    // Check if changelog exists
    const existingChangelog = await db.changelog.findUnique({
      where: { id: changelogId },
    });

    if (!existingChangelog) {
      return NextResponse.json(
        { error: "Changelog not found" },
        { status: 404 }
      );
    }

    // Prepare update data
    const updateData: any = {};

    if (body.title !== undefined) updateData.title = body.title;
    if (body.content !== undefined) updateData.content = body.content;
    if (body.version !== undefined) updateData.version = body.version || null;
    if (body.type !== undefined) updateData.type = body.type;
    if (body.priority !== undefined) updateData.priority = body.priority;
    if (body.isActive !== undefined) updateData.isActive = body.isActive;
    if (body.targetTenants !== undefined)
      updateData.targetTenants = body.targetTenants;
    if (body.targetEnvironment !== undefined)
      updateData.targetEnvironment = body.targetEnvironment || null;
    if (body.publishedAt !== undefined)
      updateData.publishedAt = body.publishedAt
        ? new Date(body.publishedAt)
        : undefined;
    if (body.expiresAt !== undefined)
      updateData.expiresAt = body.expiresAt ? new Date(body.expiresAt) : null;
    if (body.githubCommitSha !== undefined)
      updateData.githubCommitSha = body.githubCommitSha || null;
    if (body.deploymentId !== undefined)
      updateData.deploymentId = body.deploymentId || null;

    // Update changelog
    const changelog = await db.changelog.update({
      where: { id: changelogId },
      data: updateData,
    });

    return NextResponse.json({ data: changelog });
  } catch (error) {
    console.error("Error updating changelog:", error);
    return NextResponse.json(
      { error: "Failed to update changelog" },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session: any = await getServerSession(authOptions);
    if (!session?.userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if user is admin
    const user = await db.user.findUnique({
      where: { id: session.userId },
      include: { membership: true },
    });

    const isAdmin = user?.membership?.some(
      (m) => m.role === "ADMIN" || m.role === "OWNER"
    );
    if (!isAdmin) {
      return NextResponse.json(
        { error: "Admin access required" },
        { status: 403 }
      );
    }

    const changelogId = params.id;

    // Check if changelog exists
    const existingChangelog = await db.changelog.findUnique({
      where: { id: changelogId },
    });

    if (!existingChangelog) {
      return NextResponse.json(
        { error: "Changelog not found" },
        { status: 404 }
      );
    }

    // Delete changelog (this will cascade delete user views)
    await db.changelog.delete({
      where: { id: changelogId },
    });

    return NextResponse.json({
      success: true,
      message: "Changelog deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting changelog:", error);
    return NextResponse.json(
      { error: "Failed to delete changelog" },
      { status: 500 }
    );
  }
}
