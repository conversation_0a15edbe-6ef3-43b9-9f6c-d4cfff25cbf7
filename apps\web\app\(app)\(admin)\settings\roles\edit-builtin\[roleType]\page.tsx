"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useLanguage } from "@/lib/language-context";
import { But<PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { ArrowLeft, Shield, Crown, Users, Loader2, Save } from "lucide-react";
import toast from "react-hot-toast";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";

import { getCookie } from "@/utils/cookies";
import {
  findPermissionById,
  requiresParentPermission,
  hasAllRequiredParentPermissions,
  canGrantPermission,
  applyPermissionToChildren,
  getAffectedChildPermissions,
  getResourceGroupState,
} from "@/lib/permission-hierarchy";

interface Permission {
  id: string;
  action: string;
  resource: string;
  description?: string;
}

interface BuiltInRoleConfig {
  name: string;
  description: string;
  icon: React.ReactNode;
  permissions: string[];
  editable: boolean;
}

const getBuiltInRoles = (t: any): Record<string, BuiltInRoleConfig> => ({
  ADMIN: {
    name: t("roles.builtInRoleNames.admin"),
    description: t("roles.builtInRoleDescriptions.admin"),
    icon: <Shield className="h-4 w-4" />,
    permissions: [], // Will be loaded from API
    editable: true,
  },
  MEMBER: {
    name: t("roles.builtInRoleNames.member"),
    description: t("roles.builtInRoleDescriptions.member"),
    icon: <Users className="h-4 w-4" />,
    permissions: [], // Will be loaded from API
    editable: true,
  },
  OWNER: {
    name: t("roles.builtInRoleNames.owner"),
    description: t("roles.builtInRoleDescriptions.owner"),
    icon: <Crown className="h-4 w-4" />,
    permissions: [], // Owners have all permissions by default
    editable: false, // Owners cannot be edited
  },
});

export default function EditBuiltinRolePage({ 
  params 
}: { 
  params: { roleType: string } 
}) {
  const roleType = params.roleType.toUpperCase();
  const router = useRouter();
  const { t } = useLanguage();
  const [selectedPermissions, setSelectedPermissions] = useState<string[]>([]);
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const tenantId = getCookie("currentOrganizationId") as string;
  const userId = getCookie("userId") as string;

  const BUILTIN_ROLES = getBuiltInRoles(t);
  const roleConfig = BUILTIN_ROLES[roleType];

  // Redirect if invalid role type
  if (!roleConfig) {
    router.push("/settings/roles");
    return null;
  }

  // Redirect if trying to edit owner role
  if (!roleConfig.editable) {
    toast.error(t("roles.ownerRoleCannotBeEdited"));
    router.push("/settings/roles");
    return null;
  }

  // Group permissions by resource, excluding MEMBER permissions
  const groupedPermissions = permissions
    .filter(permission => permission.resource !== "MEMBER")
    .reduce((acc, permission) => {
      if (!acc[permission.resource]) {
        acc[permission.resource] = [];
      }
      acc[permission.resource].push(permission);
      return acc;
    }, {} as Record<string, Permission[]>);

  // Define the correct hierarchy order for display
  const hierarchyOrder = ["WORKSPACE", "PAGE", "FOLDER", "FILE"];

  // Sort grouped permissions according to hierarchy
  const sortedGroupedPermissions = hierarchyOrder.reduce((acc, resource) => {
    if (groupedPermissions[resource]) {
      acc[resource] = groupedPermissions[resource];
    }
    return acc;
  }, {} as Record<string, Permission[]>);

  // Fetch permissions and current role configuration
  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      try {
        // Fetch all permissions
        const permissionsResponse = await fetch("/api/permissions");
        if (permissionsResponse.ok) {
          const data = await permissionsResponse.json();
          setPermissions(data.permissions || []);
        }

        // Fetch current role permissions
        const roleResponse = await fetch(`/api/roles/builtin/${roleType}?tenantId=${tenantId}`, {
          headers: {
            "x-user-id": userId,
            "x-tenant-id": tenantId,
          },
        });
        
        if (roleResponse.ok) {
          const roleData = await roleResponse.json();
          setSelectedPermissions(roleData.permissions || []);
        }
      } catch (error) {
        console.error("Error fetching data:", error);
        toast.error(t("common.error"));
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [roleType, tenantId, userId, t]);

  // Helper function to handle individual permission changes with hierarchy
  const handlePermissionChange = (
    permissionId: string,
    checked: boolean | string
  ) => {
    // Convert to boolean if it's a string
    const isChecked = typeof checked === "string" ? checked === "true" : checked;

    const permission = findPermissionById(permissions, permissionId);
    if (!permission) return;

    if (isChecked) {
      // Check if all required parent permissions are selected
      if (!hasAllRequiredParentPermissions(permissions, selectedPermissions, permission)) {
        // Get the direct parent resource
        const { parentResource } = requiresParentPermission(permissions, permission);

        // Show toast notification about the parent permission requirement
        toast.error(
          t("roles.errorMessages.parentPermissionRequired", {
            action: permission.action,
            parentResource: parentResource
          })
        );
        return;
      }

      // Get child permissions to apply based on hierarchy
      const childPermissionIds = applyPermissionToChildren(
        permissions,
        permission.action,
        permission.resource
      );

      // Add the current permission and all child permissions
      const newPermissions = [permissionId, ...childPermissionIds].filter(
        (id) => !selectedPermissions.includes(id)
      );

      setSelectedPermissions([...selectedPermissions, ...newPermissions]);

      // Show success message for cascading permissions
      if (childPermissionIds.length > 0) {
        toast.success(
          t("roles.errorMessages.permissionGrantedWithCascade", {
            action: permission.action,
            resource: permission.resource
          })
        );
      }
    } else {
      // If unchecking a parent permission, also uncheck all child permissions
      const affectedPermissionIds = getAffectedChildPermissions(permissions, permission);

      // Show toast notification about removing child permissions
      if (affectedPermissionIds.length > 0) {
        toast.success(
          t("roles.errorMessages.permissionRemovedWithCascade", {
            action: permission.action,
            resource: permission.resource
          })
        );
      }

      // Remove this permission and all its child permissions
      setSelectedPermissions(
        selectedPermissions.filter(
          (id) => id !== permissionId && !affectedPermissionIds.includes(id)
        )
      );
    }
  };

  // Helper function to handle resource group changes (accordion header checkboxes)
  const handleResourceGroupChange = (
    resource: string,
    resourcePermissions: Permission[],
    checked: boolean | string
  ) => {
    const isChecked = typeof checked === "string" ? checked === "true" : checked;

    if (isChecked) {
      // For each permission in this resource, get child permissions
      const allChildPermissionIds: string[] = [];
      const permissionsToAdd: string[] = [];

      resourcePermissions.forEach((permission) => {
        // Check if all required parent permissions are selected
        if (!hasAllRequiredParentPermissions(permissions, selectedPermissions, permission)) {
          // Get the direct parent resource
          const { parentResource } = requiresParentPermission(permissions, permission);

          // Show toast notification about the parent permission requirement
          toast.error(
            t("roles.errorMessages.cannotGrantWithoutParent", {
              action: permission.action,
              resource: resource,
              parentResource: parentResource
            })
          );
          return;
        }

        const childPermissionIds = applyPermissionToChildren(
          permissions,
          permission.action,
          permission.resource
        );
        allChildPermissionIds.push(...childPermissionIds);
        permissionsToAdd.push(permission.id);
      });

      // Add all permissions for this resource and their children
      const newPermissions = [...permissionsToAdd, ...allChildPermissionIds].filter(
        (id) => !selectedPermissions.includes(id)
      );

      setSelectedPermissions([...selectedPermissions, ...newPermissions]);

      if (allChildPermissionIds.length > 0) {
        toast.success(t("roles.errorMessages.allPermissionsGrantedWithCascade", { resource }));
      }
    } else {
      // Remove all permissions for this resource and their children
      const allAffectedIds: string[] = [];

      resourcePermissions.forEach((permission) => {
        const childPermissionIds = getAffectedChildPermissions(permissions, permission);
        allAffectedIds.push(permission.id, ...childPermissionIds);
      });

      setSelectedPermissions(
        selectedPermissions.filter((id) => !allAffectedIds.includes(id))
      );

      toast.success(t("roles.errorMessages.allPermissionsRemovedWithCascade", { resource }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const response = await fetch(`/api/roles/builtin/${roleType}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
          "x-user-id": userId,
          "x-tenant-id": tenantId,
        },
        body: JSON.stringify({
          permissions: selectedPermissions,
          tenantId,
        }),
      });

      if (response.ok) {
        toast.success(t("roles.roleUpdatedSuccessfully", { role: roleConfig.name }));
        router.push("/settings/roles");
      } else {
        const error = await response.json();
        toast.error(error.error || t("roles.failedToUpdateRole"));
      }
    } catch (error) {
      console.error("Error updating role:", error);
      toast.error(t("common.error"));
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  return (
    <div className="container py-6 max-w-5xl">
      <div className="flex items-center mb-6">
        <Button
          variant="ghost"
          size="sm"
          className="mr-2"
          onClick={() => router.push("/settings/roles")}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          {t("common.back")}
        </Button>
        <div className="flex items-center gap-2">
          {roleConfig.icon}
          <h1 className="text-2xl font-bold">
            {t("roles.editRoleTitle", { role: roleConfig.name })}
          </h1>
        </div>
      </div>

      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            {roleConfig.icon}
            {t("roles.roleConfiguration", { role: roleConfig.name })}
          </CardTitle>
          <CardDescription>
            {roleConfig.description}
          </CardDescription>
        </CardHeader>
      </Card>

      <form onSubmit={handleSubmit}>
        <Card>
          <CardHeader>
            <CardTitle>{t("roles.permissionsTitle")}</CardTitle>
            <CardDescription>
              {t("roles.selectPermissionsFor", { role: roleConfig.name })}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Accordion type="multiple" className="w-full">
                {Object.entries(sortedGroupedPermissions).map(([resource, resourcePermissions]) => {
                  const resourceGroupState = getResourceGroupState(
                    resourcePermissions as Permission[],
                    selectedPermissions
                  );

                  return (
                    <AccordionItem key={resource} value={resource}>
                      <AccordionTrigger className="text-left">
                        <div className="flex items-center gap-2">
                          <Checkbox
                            id={`resource-${resource}`}
                            checked={resourceGroupState.checked}
                            ref={(el) => {
                              if (el && el.querySelector) {
                                const checkbox = el.querySelector('input[type="checkbox"]') as HTMLInputElement;
                                if (checkbox) {
                                  checkbox.indeterminate = resourceGroupState.indeterminate;
                                }
                              }
                            }}
                            disabled={
                              resource !== "WORKSPACE" &&
                              (resourcePermissions as Permission[]).some(
                                (p) =>
                                  !canGrantPermission(permissions, selectedPermissions, p) &&
                                  !selectedPermissions.includes(p.id)
                              )
                            }
                            onCheckedChange={(checked) =>
                              handleResourceGroupChange(
                                resource,
                                resourcePermissions as Permission[],
                                checked
                              )
                            }
                          />
                          <span className="font-medium capitalize">
                            {t("roles.permissionsGroup", { resource: resource.toLowerCase() })}
                          </span>
                          <span className="text-sm text-muted-foreground">
                            ({resourcePermissions.filter(p => selectedPermissions.includes(p.id)).length}/{resourcePermissions.length})
                          </span>
                        </div>
                      </AccordionTrigger>
                      <AccordionContent>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 pt-4">
                          {(resourcePermissions as Permission[]).map((permission: Permission) => {
                            const canGrant = canGrantPermission(permissions, selectedPermissions, permission);
                            const isSelected = selectedPermissions.includes(permission.id);

                            return (
                              <div key={permission.id} className="flex items-center space-x-2">
                                <Checkbox
                                  id={permission.id}
                                  checked={isSelected}
                                  disabled={!canGrant && !isSelected}
                                  onCheckedChange={(checked) =>
                                    handlePermissionChange(permission.id, checked)
                                  }
                                />
                                <Label
                                  htmlFor={permission.id}
                                  className={`text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 ${
                                    !canGrant && !isSelected
                                      ? "text-muted-foreground"
                                      : ""
                                  }`}
                                >
                                  {permission.action && permission.resource ? (
                                    <>
                                      {t(`roles.actions.${permission.action}`)} {t(`roles.resources.${permission.resource}`)}
                                    </>
                                  ) : (
                                    "Invalid Permission"
                                  )}
                                </Label>
                                {permission.description && (
                                  <span className="text-xs text-muted-foreground">
                                    - {permission.description}
                                  </span>
                                )}
                              </div>
                            );
                          })}
                        </div>
                      </AccordionContent>
                    </AccordionItem>
                  );
                })}
              </Accordion>
          </CardContent>
        </Card>

        <div className="flex justify-end gap-4 mt-6">
          <Button
            type="button"
            variant="outline"
            onClick={() => router.push("/settings/roles")}
          >
            {t("common.cancel")}
          </Button>
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                {t("roles.savingChanges")}
              </>
            ) : (
              <>
                <Save className="mr-2 h-4 w-4" />
                {t("roles.saveChanges")}
              </>
            )}
          </Button>
        </div>
      </form>
    </div>
  );
}
