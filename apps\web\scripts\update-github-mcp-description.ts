#!/usr/bin/env ts-node

import db from "../lib/shared-db";

async function updateGitHubMCPDescription() {
  console.log("Updating GitHub MCP Server description to German...");

  try {
    // Find the server with the English description
    const server = await db.mCPServer.findFirst({
      where: {
        description: {
          contains: "GitHub MCP Server for repository management and code analysis"
        }
      }
    });

    if (!server) {
      console.log("❌ No server found with the English description");
      return;
    }

    console.log(`Found server: ${server.name}`);

    // Update the description to German
    const updatedServer = await db.mCPServer.update({
      where: {
        id: server.id
      },
      data: {
        description: "GitHub MCP Server für Repository-Verwaltung und Code-Analyse"
      }
    });

    console.log("✅ Successfully updated server description to German:");
    console.log(`   Old: ${server.description}`);
    console.log(`   New: ${updatedServer.description}`);

  } catch (error) {
    console.error("❌ Error updating server description:", error);
  } finally {
    await db.$disconnect();
  }
}

// Run the script
updateGitHubMCPDescription()
  .catch((error) => {
    console.error("<PERSON><PERSON><PERSON> failed:", error);
    process.exit(1);
  });
