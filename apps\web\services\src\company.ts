import { apiUrl } from "..";

// Types
export interface Company {
  id: string;
  name: string;
  slug: string;
  description: string;
  maxSignups: number;
  currentSignups: number;
  inviteLink: string;
  createdAt: string;
  members?: CompanyMember[];
}

export interface CompanyMember {
  id: string;
  name: string;
  email: string;
  role: "owner" | "admin" | "member" | "viewer";
  joinedAt: string;
  status: "active" | "pending" | "inactive";
}

export interface CreateCompanyData {
  name: string;
  slug: string;
  description?: string;
  maxSignups?: number;
}

export interface UpdateCompanyData {
  name?: string;
  description?: string;
  maxSignups?: number;
}

export interface AddMemberData {
  email: string;
  role?: "OWNER" | "ADMIN" | "MEMBER" | "VIEWER";
}

export interface UpdateCompanyMemberData {
  role: "OWNER" | "ADMIN" | "MEMBER" | "VIEWER";
}

// Get companies for dropdown (simplified)
export const getCompaniesForDropdown = async (): Promise<
  { companies: { id: string; name: string }[] } | { error: string }
> => {
  try {
    const response = await fetch(`${apiUrl}/companies`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Failed to fetch companies");
    }

    const data = await response.json();
    const simplifiedCompanies = data.companies.map((company: Company) => ({
      id: company.id,
      name: company.name,
    }));

    return { companies: simplifiedCompanies };
  } catch (error) {
    console.error("Get companies for dropdown error:", error);
    return { error: error.message || "Error fetching companies" };
  }
};

// Get all companies
export const getCompanies = async (): Promise<
  { companies: Company[]; total: number } | { error: string }
> => {
  try {
    const response = await fetch(`${apiUrl}/companies`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Failed to fetch companies");
    }

    return await response.json();
  } catch (error) {
    console.error("Get companies error:", error);
    return { error: error.message || "Error fetching companies" };
  }
};

// Get a specific company
export const getCompany = async (
  id: string
): Promise<{ company: Company } | { error: string }> => {
  try {
    const response = await fetch(`${apiUrl}/companies/${id}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Failed to fetch company");
    }

    return await response.json();
  } catch (error) {
    console.error("Get company error:", error);
    return { error: error.message || "Error fetching company" };
  }
};

// Create a new company
export const createCompany = async (
  data: CreateCompanyData
): Promise<{ company: Company; message: string } | { error: string }> => {
  try {
    const response = await fetch(`${apiUrl}/companies`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Failed to create company");
    }

    return await response.json();
  } catch (error) {
    console.error("Create company error:", error);
    return { error: error.message || "Error creating company" };
  }
};

// Update a company
export const updateCompany = async (
  id: string,
  data: UpdateCompanyData
): Promise<{ company: Company; message: string } | { error: string }> => {
  try {
    const response = await fetch(`${apiUrl}/companies/${id}`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Failed to update company");
    }

    return await response.json();
  } catch (error) {
    console.error("Update company error:", error);
    return { error: error.message || "Error updating company" };
  }
};

// Delete a company
export const deleteCompany = async (
  id: string
): Promise<{ message: string } | { error: string }> => {
  try {
    const response = await fetch(`${apiUrl}/companies/${id}`, {
      method: "DELETE",
      headers: {
        "Content-Type": "application/json",
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Failed to delete company");
    }

    return await response.json();
  } catch (error) {
    console.error("Delete company error:", error);
    return { error: error.message || "Error deleting company" };
  }
};

// Get company members
export const getCompanyMembers = async (
  id: string
): Promise<{ members: CompanyMember[]; total: number } | { error: string }> => {
  try {
    const response = await fetch(`${apiUrl}/companies/${id}/members`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Failed to fetch company members");
    }

    return await response.json();
  } catch (error) {
    console.error("Get company members error:", error);
    return { error: error.message || "Error fetching company members" };
  }
};

// Add a member to company
export const addCompanyMember = async (
  id: string,
  data: AddMemberData
): Promise<{ member: CompanyMember; message: string } | { error: string }> => {
  try {
    const response = await fetch(`${apiUrl}/companies/${id}/members`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Failed to add company member");
    }

    return await response.json();
  } catch (error) {
    console.error("Add company member error:", error);
    return { error: error.message || "Error adding company member" };
  }
};

// Update member role
export const updateCompanyMemberRole = async (
  companyId: string,
  memberId: string,
  role: string
): Promise<{ member: CompanyMember; message: string } | { error: string }> => {
  try {
    const response = await fetch(
      `${apiUrl}/companies/${companyId}/members/${memberId}`,
      {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ role }),
      }
    );

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Failed to update member role");
    }

    return await response.json();
  } catch (error) {
    console.error("Update member role error:", error);
    return { error: error.message || "Error updating member role" };
  }
};

// Remove member from company
export const removeCompanyMember = async (
  companyId: string,
  memberId: string
): Promise<{ message: string } | { error: string }> => {
  try {
    const response = await fetch(
      `${apiUrl}/companies/${companyId}/members/${memberId}`,
      {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
        },
      }
    );

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Failed to remove member");
    }

    return await response.json();
  } catch (error) {
    console.error("Remove member error:", error);
    return { error: error.message || "Error removing member" };
  }
};
