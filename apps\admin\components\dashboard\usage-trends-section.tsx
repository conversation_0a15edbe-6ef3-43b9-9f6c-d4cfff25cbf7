"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardDescription } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { 
  LineChart, 
  Line, 
  AreaChart, 
  Area, 
  BarChart, 
  Bar,
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer,
  ComposedChart
} from "recharts";
import {
  TrendingUp,
  Users,
  Zap,
  Activity,
  HardDrive
} from "lucide-react";
import { cn } from "@/lib/utils";
import { DashboardFilters } from "./dashboard-filters";

interface UsageTrendsData {
  activeTenantsData: Array<{ name: string; value: number }>;
  tokenUsageData: Array<{
    name: string;
    inputTokens: number;
    outputTokens: number;
    totalTokens: number;
    value: number; // For backward compatibility
  }>;
  apiVolumeData: Array<{ name: string; success: number; errors: number }>;
  storageGrowthData: Array<{ name: string; value: number }>;
}

interface UsageTrendsSectionProps {
  data?: UsageTrendsData;
  filters: DashboardFilters;
  loading?: boolean;
}

interface ChartCardProps {
  title: string;
  description: string;
  icon: React.ElementType;
  children: React.ReactNode;
}

function ChartCard({ title, description, icon: Icon, children }: ChartCardProps) {
  return (
    <Card className="overflow-hidden">
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <div className="flex items-center space-x-2">
          <div className="p-2 rounded-full bg-primary/10">
            <Icon className="h-4 w-4 text-primary" />
          </div>
          <div>
            <CardTitle className="text-base font-medium">{title}</CardTitle>
            <CardDescription className="text-xs">{description}</CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent className="h-[300px] pt-4">
        {children}
      </CardContent>
    </Card>
  );
}

function CustomTooltip({ active, payload, label, formatter }: any) {
  if (active && payload && payload.length) {
    return (
      <div className="bg-background border rounded-lg shadow-lg p-3">
        <p className="font-medium text-sm">{label}</p>
        {payload.map((entry: any, index: number) => (
          <p key={index} className="text-sm" style={{ color: entry.color }}>
            {`${entry.name}: ${formatter ? formatter(entry.value) : entry.value}`}
          </p>
        ))}
      </div>
    );
  }
  return null;
}

export function UsageTrendsSection({ data, filters, loading }: UsageTrendsSectionProps) {
  if (loading || !data) {
    return (
      <div>
        <h2 className="text-lg font-semibold mb-4">Usage Trends</h2>
        <div className="grid gap-4 md:grid-cols-2">
          {Array.from({ length: 4 }).map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader>
                <div className="h-5 bg-gray-200 rounded w-40"></div>
                <div className="h-3 bg-gray-200 rounded w-60"></div>
              </CardHeader>
              <CardContent>
                <div className="h-[300px] bg-gray-200 rounded"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  const formatNumber = (value: number) => {
    return new Intl.NumberFormat('en-US').format(value);
  };

  const formatTokens = (value: number) => {
    if (value >= 1000000) {
      return `${(value / 1000000).toFixed(1)}M`;
    } else if (value >= 1000) {
      return `${(value / 1000).toFixed(1)}K`;
    }
    return value.toString();
  };

  const formatStorage = (value: number) => {
    return `${value.toFixed(2)} GB`;
  };

  return (
    <div>
      <h2 className="text-lg font-semibold mb-4">Usage Trends</h2>
      <div className="grid gap-4 md:grid-cols-2">
        {/* Active Tenants Line Chart */}
        <ChartCard
          title="Active Tenants"
          description={`Daily active tenants over ${filters.timeRange}`}
          icon={Users}
        >
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={data.activeTenantsData}>
              <CartesianGrid strokeDasharray="3 3" stroke="#888" strokeOpacity={0.2} />
              <XAxis 
                dataKey="name" 
                axisLine={false}
                tickLine={false}
                tick={{ fontSize: 12 }}
              />
              <YAxis 
                axisLine={false}
                tickLine={false}
                tick={{ fontSize: 12 }}
              />
              <Tooltip 
                content={<CustomTooltip formatter={formatNumber} />}
              />
              <Line 
                type="monotone" 
                dataKey="value" 
                stroke="#3B82F6" 
                strokeWidth={2}
                dot={{ fill: "#3B82F6", strokeWidth: 2, r: 4 }}
                activeDot={{ r: 6, stroke: "#3B82F6", strokeWidth: 2 }}
              />
            </LineChart>
          </ResponsiveContainer>
        </ChartCard>

        {/* Token Usage Stacked Area Chart */}
        <ChartCard
          title="Token Usage Breakdown"
          description="Input vs Output token consumption over time"
          icon={Zap}
        >
          <ResponsiveContainer width="100%" height="100%">
            <AreaChart data={data.tokenUsageData}>
              <defs>
                <linearGradient id="inputTokenGradient" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="5%" stopColor="#3B82F6" stopOpacity={0.3} />
                  <stop offset="95%" stopColor="#3B82F6" stopOpacity={0} />
                </linearGradient>
                <linearGradient id="outputTokenGradient" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="5%" stopColor="#10B981" stopOpacity={0.3} />
                  <stop offset="95%" stopColor="#10B981" stopOpacity={0} />
                </linearGradient>
              </defs>
              <CartesianGrid strokeDasharray="3 3" stroke="#888" strokeOpacity={0.2} />
              <XAxis
                dataKey="name"
                axisLine={false}
                tickLine={false}
                tick={{ fontSize: 12 }}
              />
              <YAxis
                axisLine={false}
                tickLine={false}
                tick={{ fontSize: 12 }}
              />
              <Tooltip
                content={({ active, payload, label }) => {
                  if (active && payload && payload.length) {
                    const data = payload[0].payload;
                    return (
                      <div className="bg-background p-3 border border-border rounded-lg shadow-lg">
                        <p className="font-medium text-foreground">{label}</p>
                        <div className="space-y-1 mt-2">
                          <div className="flex items-center justify-between">
                            <span className="flex items-center text-muted-foreground">
                              <div className="w-3 h-3 bg-blue-500 rounded-full mr-2"></div>
                              Input Tokens:
                            </span>
                            <span className="font-medium text-foreground">{formatTokens(data.inputTokens)}</span>
                          </div>
                          <div className="flex items-center justify-between">
                            <span className="flex items-center text-muted-foreground">
                              <div className="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                              Output Tokens:
                            </span>
                            <span className="font-medium text-foreground">{formatTokens(data.outputTokens)}</span>
                          </div>
                          <div className="flex items-center justify-between border-t border-border pt-1">
                            <span className="font-medium text-foreground">Total:</span>
                            <span className="font-bold text-foreground">{formatTokens(data.totalTokens)}</span>
                          </div>
                        </div>
                      </div>
                    );
                  }
                  return null;
                }}
              />
              <Legend />
              <Area
                type="monotone"
                dataKey="inputTokens"
                stackId="1"
                stroke="#3B82F6"
                strokeWidth={2}
                fill="url(#inputTokenGradient)"
                name="Input Tokens"
              />
              <Area
                type="monotone"
                dataKey="outputTokens"
                stackId="1"
                stroke="#10B981"
                strokeWidth={2}
                fill="url(#outputTokenGradient)"
                name="Output Tokens"
              />
            </AreaChart>
          </ResponsiveContainer>
        </ChartCard>

        {/* API Volume & Errors Combo Chart */}
        <ChartCard
          title="API Volume & Errors"
          description="Request volume with error overlay"
          icon={Activity}
        >
          <ResponsiveContainer width="100%" height="100%">
            <ComposedChart data={data.apiVolumeData}>
              <CartesianGrid strokeDasharray="3 3" stroke="#888" strokeOpacity={0.2} />
              <XAxis 
                dataKey="name" 
                axisLine={false}
                tickLine={false}
                tick={{ fontSize: 12 }}
              />
              <YAxis 
                yAxisId="left"
                axisLine={false}
                tickLine={false}
                tick={{ fontSize: 12 }}
              />
              <YAxis 
                yAxisId="right"
                orientation="right"
                axisLine={false}
                tickLine={false}
                tick={{ fontSize: 12 }}
              />
              <Tooltip 
                content={<CustomTooltip />}
              />
              <Legend />
              <Line
                yAxisId="left"
                type="monotone"
                dataKey="success"
                stroke="#10B981"
                strokeWidth={2}
                name="Successful Requests"
              />
              <Bar
                yAxisId="right"
                dataKey="errors"
                fill="#EF4444"
                name="Errors"
                opacity={0.7}
              />
            </ComposedChart>
          </ResponsiveContainer>
        </ChartCard>

        {/* Storage Usage Growth Line Chart */}
        <ChartCard
          title="Storage Growth"
          description="Cumulative storage consumption"
          icon={HardDrive}
        >
          <ResponsiveContainer width="100%" height="100%">
            <AreaChart data={data.storageGrowthData}>
              <defs>
                <linearGradient id="storageGradient" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="5%" stopColor="#F59E0B" stopOpacity={0.3} />
                  <stop offset="95%" stopColor="#F59E0B" stopOpacity={0} />
                </linearGradient>
              </defs>
              <CartesianGrid strokeDasharray="3 3" stroke="#888" strokeOpacity={0.2} />
              <XAxis
                dataKey="name"
                axisLine={false}
                tickLine={false}
                tick={{ fontSize: 12 }}
              />
              <YAxis
                axisLine={false}
                tickLine={false}
                tick={{ fontSize: 12 }}
              />
              <Tooltip
                content={<CustomTooltip formatter={formatStorage} />}
              />
              <Area
                type="monotone"
                dataKey="value"
                stroke="#F59E0B"
                strokeWidth={2}
                fill="url(#storageGradient)"
              />
            </AreaChart>
          </ResponsiveContainer>
        </ChartCard>
      </div>
    </div>
  );
}
