"use client";

import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import {
  Server,
  Wrench,
  Clock,
  ChevronDown,
  ChevronRight,
  CheckCircle,
  XCircle,
  Info,
  Zap,
} from "lucide-react";

interface MCPToolCall {
  tool_name: string;
  tool_args: Record<string, any>;
  timestamp: number;
  server_name: string;
  server_id: string;
}

interface MCPExecutionSummary {
  server_used: string;
  tools_executed: string[];
  total_calls: number;
  success: boolean;
}

interface MCPInfo {
  selected_servers: string[];
  server_id?: string;
  server_name?: string;
  server_command?: string;
  server_description?: string;
  tools_used: string[];
  tool_calls: MCPToolCall[];
  server_info?: any;
  elapsed_time?: number;
  total_tool_calls: number;
  error?: string;
  execution_summary: MCPExecutionSummary;
  is_mcp_response?: boolean;
}

interface MCPInfoDisplayProps {
  mcpInfo: MCPInfo;
  className?: string;
}

export function MCPInfoDisplay({ mcpInfo, className }: MCPInfoDisplayProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [showToolDetails, setShowToolDetails] = useState(false);

  if (!mcpInfo || !mcpInfo.is_mcp_response) {
    return null;
  }

  const hasError = !!mcpInfo.error;
  const hasToolCalls = mcpInfo.tool_calls && mcpInfo.tool_calls.length > 0;

  return (
    <Card
      className={`mt-2 border-l-4 ${
        hasError ? "border-l-red-500" : "border-l-blue-500"
      } ${className}`}
    >
      <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
        <CollapsibleTrigger asChild>
          <CardHeader className="pb-2 cursor-pointer hover:bg-muted/50 transition-colors">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Server className="h-4 w-4 text-blue-500" />
                <CardTitle className="text-sm font-medium">
                  MCP Server Response
                </CardTitle>
                {hasError ? (
                  <XCircle className="h-4 w-4 text-red-500" />
                ) : (
                  <CheckCircle className="h-4 w-4 text-green-500" />
                )}
              </div>
              <div className="flex items-center gap-2">
                {mcpInfo.elapsed_time && (
                  <Badge variant="outline" className="text-xs">
                    <Clock className="h-3 w-3 mr-1" />
                    {mcpInfo.elapsed_time.toFixed(2)}s
                  </Badge>
                )}
                {isExpanded ? (
                  <ChevronDown className="h-4 w-4" />
                ) : (
                  <ChevronRight className="h-4 w-4" />
                )}
              </div>
            </div>
          </CardHeader>
        </CollapsibleTrigger>

        <CollapsibleContent>
          <CardContent className="pt-0">
            {/* Server Information */}
            <div className="space-y-3">
              <div className="flex items-center gap-2 text-sm">
                <Server className="h-4 w-4 text-muted-foreground" />
                <span className="font-medium">
                  {mcpInfo.server_name || "Unknown Server"}
                </span>
                {mcpInfo.server_description && (
                  <span className="text-muted-foreground">
                    - {mcpInfo.server_description}
                  </span>
                )}
              </div>

              {/* Execution Summary */}
              <div className="bg-muted/50 rounded-lg p-3">
                <div className="flex items-center gap-2 mb-2">
                  <Info className="h-4 w-4 text-blue-500" />
                  <span className="text-sm font-medium">Execution Summary</span>
                </div>
                <div className="grid grid-cols-2 gap-2 text-xs">
                  <div>
                    <span className="text-muted-foreground">Status:</span>
                    <Badge
                      variant={
                        mcpInfo.execution_summary.success
                          ? "default"
                          : "destructive"
                      }
                      className="ml-2 text-xs"
                    >
                      {mcpInfo.execution_summary.success ? "Success" : "Failed"}
                    </Badge>
                  </div>
                  <div>
                    <span className="text-muted-foreground">Tools Used:</span>
                    <span className="ml-2 font-medium">
                      {mcpInfo.execution_summary.total_calls}
                    </span>
                  </div>
                </div>
              </div>

              {/* Tools Used */}
              {mcpInfo.tools_used && mcpInfo.tools_used.length > 0 && (
                <div>
                  <div className="flex items-center gap-2 mb-2">
                    <Wrench className="h-4 w-4 text-green-500" />
                    <span className="text-sm font-medium">Tools Used</span>
                  </div>
                  <div className="flex flex-wrap gap-1">
                    {mcpInfo.tools_used.map((tool, index) => (
                      <Badge
                        key={index}
                        variant="secondary"
                        className="text-xs"
                      >
                        <Zap className="h-3 w-3 mr-1" />
                        {tool}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}

              {/* Tool Call Details */}
              {hasToolCalls && (
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2">
                      <Wrench className="h-4 w-4 text-orange-500" />
                      <span className="text-sm font-medium">
                        Tool Call Details
                      </span>
                      <Badge variant="outline" className="text-xs">
                        {mcpInfo.tool_calls.length} calls
                      </Badge>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setShowToolDetails(!showToolDetails)}
                      className="text-xs"
                    >
                      {showToolDetails ? "Hide" : "Show"} Details
                    </Button>
                  </div>

                  {showToolDetails && (
                    <div className="space-y-2">
                      {mcpInfo.tool_calls.map((toolCall, index) => (
                        <Card key={index} className="bg-muted/30">
                          <CardContent className="p-3">
                            <div className="flex items-center justify-between mb-2">
                              <div className="flex items-center gap-2">
                                <Zap className="h-3 w-3 text-orange-500" />
                                <span className="text-sm font-medium">
                                  {toolCall.tool_name}
                                </span>
                              </div>
                              <span className="text-xs text-muted-foreground">
                                {new Date(
                                  toolCall.timestamp * 1000
                                ).toLocaleTimeString()}
                              </span>
                            </div>
                            {Object.keys(toolCall.tool_args).length > 0 && (
                              <div className="text-xs">
                                <span className="text-muted-foreground">
                                  Arguments:
                                </span>
                                <pre className="mt-1 bg-background rounded p-2 overflow-x-auto">
                                  {JSON.stringify(toolCall.tool_args, null, 2)}
                                </pre>
                              </div>
                            )}
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  )}
                </div>
              )}

              {/* Error Information */}
              {hasError && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                  <div className="flex items-center gap-2 mb-1">
                    <XCircle className="h-4 w-4 text-red-500" />
                    <span className="text-sm font-medium text-red-700">
                      Error
                    </span>
                  </div>
                  <p className="text-xs text-red-600">{mcpInfo.error}</p>
                </div>
              )}

              {/* Server Command (for debugging) */}
              {mcpInfo.server_command && (
                <details className="text-xs">
                  <summary className="cursor-pointer text-muted-foreground hover:text-foreground">
                    Server Command
                  </summary>
                  <code className="block mt-1 bg-muted rounded p-2 text-xs">
                    {mcpInfo.server_command}
                  </code>
                </details>
              )}
            </div>
          </CardContent>
        </CollapsibleContent>
      </Collapsible>
    </Card>
  );
}
