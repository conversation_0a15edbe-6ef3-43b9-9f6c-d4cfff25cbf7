import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/next-auth";
import db from "@/lib/shared-db";
import crypto from "crypto";

// GET /api/companies/[id]/members - Get company members
export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const session: any = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id } = params;

    // Get user
    const emailHash = crypto
      .createHash("sha256")
      .update(session.user.email)
      .digest("hex");

    const user = await db.user.findUnique({
      where: { emailHash },
      include: {
        membership: true,
      },
    });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Get company to check access
    const company = await db.company.findUnique({
      where: { id },
      include: {
        members: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
                createdAt: true,
              },
            },
          },
          orderBy: {
            createdAt: "asc",
          },
        },
      },
    });

    if (!company) {
      return NextResponse.json({ error: "Company not found" }, { status: 404 });
    }

    // Check if user has access to this company (either through tenant or company membership)
    const hasTenantAccess = user.membership.find(
      (m: any) => m.tenantId === company.tenantId
    );
    const hasCompanyAccess = company.members.find(
      (m: any) => m.userId === user.id
    );

    if (!hasTenantAccess && !hasCompanyAccess) {
      return NextResponse.json({ error: "Access denied" }, { status: 403 });
    }

    // Format members data
    const formattedMembers = company.members.map((member: any) => ({
      id: member.id,
      name: member.user.name,
      email: member.user.email,
      role: member.role.toLowerCase(),
      joinedAt: member.joinedAt.toISOString().split("T")[0],
      status: member.status.toLowerCase(),
    }));

    return NextResponse.json({
      members: formattedMembers,
      total: formattedMembers.length,
    });
  } catch (error) {
    console.error("Error fetching company members:", error);
    return NextResponse.json(
      { error: "Failed to fetch company members" },
      { status: 500 }
    );
  }
}

// POST /api/companies/[id]/members - Add a member to company
export async function POST(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const session: any = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id } = params;
    const { email, role = "MEMBER" } = await request.json();

    if (!email) {
      return NextResponse.json({ error: "Email is required" }, { status: 400 });
    }

    // Get user
    const emailHash = crypto
      .createHash("sha256")
      .update(session.user.email)
      .digest("hex");

    const user = await db.user.findUnique({
      where: { emailHash },
      include: {
        membership: true,
      },
    });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Get company to check access
    const company = await db.company.findUnique({
      where: { id },
      include: {
        members: {
          where: { userId: user.id },
        },
      },
    });

    if (!company) {
      return NextResponse.json({ error: "Company not found" }, { status: 404 });
    }

    // Check if user has admin access to this company (either through tenant or company membership)
    const hasTenantAccess = user.membership.find(
      (m: any) => m.tenantId === company.tenantId
    );
    const companyMember = company.members[0];

    if (
      !hasTenantAccess &&
      (!companyMember || !["OWNER", "ADMIN"].includes(companyMember.role))
    ) {
      return NextResponse.json(
        { error: "Insufficient permissions" },
        { status: 403 }
      );
    }

    // Find the user to be added
    const targetEmailHash = crypto
      .createHash("sha256")
      .update(email)
      .digest("hex");

    const targetUser = await db.user.findUnique({
      where: { emailHash: targetEmailHash },
    });

    if (!targetUser) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Check if user is already a member
    const existingMembership = await db.companyMember.findFirst({
      where: {
        userId: targetUser.id,
        companyId: id,
      },
    });

    if (existingMembership) {
      return NextResponse.json(
        { error: "User is already a member" },
        { status: 400 }
      );
    }

    // Add user to company
    const newMembership = await db.companyMember.create({
      data: {
        userId: targetUser.id,
        companyId: id,
        role: role.toUpperCase() as any,
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            createdAt: true,
          },
        },
      },
    });

    // Format response
    const formattedMember = {
      id: newMembership.id,
      name: newMembership.user.name,
      email: newMembership.user.email,
      role: newMembership.role.toLowerCase(),
      joinedAt: newMembership.joinedAt.toISOString().split("T")[0],
      status: newMembership.status.toLowerCase(),
    };

    return NextResponse.json(
      {
        message: "Member added successfully",
        member: formattedMember,
      },
      { status: 201 }
    );
  } catch (error) {
    console.error("Error adding company member:", error);
    return NextResponse.json(
      { error: "Failed to add company member" },
      { status: 500 }
    );
  }
}
