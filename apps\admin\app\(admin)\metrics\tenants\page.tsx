import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/next-auth";
import { redirect } from "next/navigation";
import db from "@/lib/shared-db";
import { addDays } from "date-fns";
import { TenantMetricsClient } from "@/components/metrics/tenant-metrics-client";

// Types for our data structures
interface TenantMetrics {
  totalActiveTenants: number;
  totalTenants: number;
  newTenants: number;
  growthRate: number;
}

interface TenantActivity {
  totalTenants: number;
  activeTenants: number;
  inactiveTenants: number;
  onboardedTenants: number;
  onboardingRate: number;
  activeTenantsCount: number;
  activityRate: number;
  churnRate: number;
  tenantHealthScore: number;
  canceledSubscriptions: number;
}

interface PlanDistribution {
  name: string;
  value: number;
}

interface NewTenantsOverTime {
  date?: string;
  month?: string;
  count: number;
}

interface TenantMetricsData {
  tenantMetrics: TenantMetrics;
  tenantActivity: TenantActivity;
  planDistribution: PlanDistribution[];
  tierDistribution: PlanDistribution[];
  trialSubscriptions: number;
  newTenantsOverTime: NewTenantsOverTime[];
  period: string;
}

// Helper function to convert date range to period parameter
function dateRangeToPeriod(from: Date | null, to: Date | null): string {
  // If either date is null, it's lifetime
  if (!from || !to) {
    return "lifetime";
  }
  
  const diffTime = Math.abs(to.getTime() - from.getTime());
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  if (diffDays <= 7) return "7days";
  if (diffDays <= 30) return "30days";
  if (diffDays <= 90) return "90days";
  if (diffDays <= 180) return "6months";
  if (diffDays <= 365) return "12months";
  return "12months";
}

// Server-side data fetching functions
async function fetchTenantMetrics(
  fromDate: Date | null,
  toDate: Date | null
): Promise<TenantMetrics> {
  // For lifetime queries, we need different logic
  const isLifetime = !fromDate || !toDate;
  
  const totalActiveTenants = await db.tenant.count({
    where: {
      Subscription: { some: { isActive: true } },
      ...(isLifetime ? {} : { createdAt: { gte: fromDate, lte: toDate } }),
    },
  });

  const totalTenants = await db.tenant.count({
    where: isLifetime ? {} : { createdAt: { lte: toDate! } },
  });

  let newTenants = 0;
  if (!isLifetime && fromDate && toDate) {
    newTenants = await db.tenant.count({
      where: { createdAt: { gte: fromDate, lte: toDate } },
    });
  } else {
    // For lifetime, get all tenants as "new" (or calculate based on a different logic)
    newTenants = totalTenants;
  }

  const growthRate = totalTenants > 0 ? (newTenants / totalTenants) * 100 : 0;

  return {
    totalActiveTenants,
    totalTenants,
    newTenants,
    growthRate: Math.round(growthRate * 100) / 100,
  };
}

async function fetchTenantActivity(
  fromDate: Date | null,
  toDate: Date | null
): Promise<TenantActivity> {
  const isLifetime = !fromDate || !toDate;
  
  const totalTenants = await db.tenant.count({
    where: isLifetime ? {} : { createdAt: { lte: toDate! } },
  });

  const onboardedTenants = await db.tenant.count({
    where: {
      isOnboarded: true,
      ...(isLifetime ? {} : { createdAt: { gte: fromDate, lte: toDate } }),
    },
  });

  const onboardingRate =
    totalTenants > 0 ? Math.round((onboardedTenants / totalTenants) * 100) : 0;

  const activeTenants = await db.tenant.count({
    where: {
      Subscription: { some: { isActive: true } },
      ...(isLifetime ? {} : { createdAt: { gte: fromDate, lte: toDate } }),
    },
  });

  const inactiveTenants = totalTenants - activeTenants;

  // For activity tracking, we need a date range. For lifetime, use a reasonable default
  let activityFromDate = fromDate;
  let activityToDate = toDate;
  
  if (isLifetime) {
    // For lifetime queries, check activity in the last 30 days
    activityToDate = new Date();
    activityFromDate = addDays(new Date(), -30);
  }

  const tenantsWithActivity = await db.aPIRequest.groupBy({
    by: ["tenantId"],
    where: { timestamp: { gte: activityFromDate!, lte: activityToDate! } },
    _count: { _all: true },
  });

  const activeTenantsCount = tenantsWithActivity.length;
  const activityRate =
    totalTenants > 0
      ? Math.round((activeTenantsCount / totalTenants) * 100)
      : 0;

  const canceledSubscriptions = await db.subscription.count({
    where: {
      isActive: false,
      endDate: isLifetime 
        ? { gte: activityFromDate!, lte: activityToDate! }
        : { gte: fromDate!, lte: toDate! },
    },
  });

  const churnRate =
    activeTenants > 0
      ? Math.round((canceledSubscriptions / activeTenants) * 100 * 10) / 10
      : 0;

  const tenantHealthScore = totalTenants > 0
    ? Math.round(((activeTenantsCount / totalTenants) * 0.6 + (onboardingRate / 100) * 0.4) * 100)
    : 0;

  return {
    totalTenants,
    activeTenants,
    inactiveTenants,
    onboardedTenants,
    onboardingRate,
    activeTenantsCount,
    activityRate,
    churnRate,
    tenantHealthScore,
    canceledSubscriptions,
  };
}

async function fetchPlanDistribution(fromDate: Date | null, toDate: Date | null) {
  const isLifetime = !fromDate || !toDate;
  
  const subscriptionsByPlan = await db.subscription.groupBy({
    by: ["planId"],
    where: {
      isActive: true,
      ...(isLifetime ? {} : { createdAt: { gte: fromDate, lte: toDate } }),
    },
    _count: { _all: true },
  });

  const planIds = subscriptionsByPlan.map((item) => item.planId);
  const plans = await db.plan.findMany({
    where: { id: { in: planIds } },
    select: { id: true, name: true, type: true },
  });

  const planDistribution = subscriptionsByPlan.map((item) => {
    const plan = plans.find((p) => p.id === item.planId);
    return { name: plan?.name || "Unknown", value: item._count._all };
  });

  const totalTenants = await db.tenant.count({
    where: isLifetime ? {} : { createdAt: { lte: toDate! } },
  });

  const tenantsWithSubscriptions = await db.tenant.count({
    where: {
      Subscription: { some: { isActive: true } },
      ...(isLifetime ? {} : { createdAt: { lte: toDate! } }),
    },
  });

  const tenantsWithoutSubscriptions = totalTenants - tenantsWithSubscriptions;

  if (tenantsWithoutSubscriptions > 0) {
    planDistribution.push({
      name: "No Plan",
      value: tenantsWithoutSubscriptions,
    });
  }

  // Get tier distribution
  const subscriptionsByTier = await db.subscription.findMany({
    where: {
      isActive: true,
      ...(isLifetime ? {} : { createdAt: { lte: toDate! } }),
    },
    include: { plan: true },
  });

  const tierCounts: Record<string, number> = {};
  subscriptionsByTier.forEach((sub) => {
    const tierType = sub.plan.type;
    tierCounts[tierType] = (tierCounts[tierType] || 0) + 1;
  });

  const tierDistribution = Object.entries(tierCounts).map(([tier, count]) => ({
    name: tier,
    value: count,
  }));

  if (tenantsWithoutSubscriptions > 0) {
    tierDistribution.push({ name: "FREE", value: tenantsWithoutSubscriptions });
  }

  const trialSubscriptions = await db.subscription.count({
    where: {
      isActive: true,
      isOnTrial: true,
      ...(isLifetime ? {} : { createdAt: { gte: fromDate, lte: toDate } }),
    },
  });

  return { planDistribution, tierDistribution, trialSubscriptions };
}

async function fetchNewTenantsOverTime(fromDate: Date | null, toDate: Date | null, period: string): Promise<NewTenantsOverTime[]> {
  const isLifetime = period === "lifetime";
  
  const whereClause = isLifetime
    ? {} // No date filter for lifetime
    : { createdAt: { gte: fromDate, lte: toDate } };

  const tenants = await db.tenant.findMany({
    where: whereClause,
    select: { createdAt: true },
    orderBy: { createdAt: "asc" },
  });
  // For lifetime, always use monthly grouping since it could span years
  const unit = isLifetime ? "months" : (period.includes("days") ? "days" : "months");
  const groupedData: Record<string, number> = {};

  if (unit === "days") {
    tenants.forEach((tenant) => {
      const dateKey = tenant.createdAt.toISOString().split("T")[0];
      groupedData[dateKey] = (groupedData[dateKey] || 0) + 1;
    });

    return Object.entries(groupedData).map(([date, count]) => ({
      date,
      count,
    }));
  } else {
    const monthNames = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];

    tenants.forEach((tenant) => {
      const month = monthNames[tenant.createdAt.getMonth()];
      const year = tenant.createdAt.getFullYear();
      const monthYear = `${month} ${year}`;
      groupedData[monthYear] = (groupedData[monthYear] || 0) + 1;
    });

    return Object.entries(groupedData)
      .map(([month, count]) => ({ month, count }))
      .sort((a, b) => {
        if (!a.month || !b.month) return 0;
        const [aMonth, aYear] = a.month.split(" ");
        const [bMonth, bYear] = b.month.split(" ");
        if (aYear !== bYear) return parseInt(aYear) - parseInt(bYear);
        return monthNames.indexOf(aMonth) - monthNames.indexOf(bMonth);
      });
  }
}

// Main server component
export default async function TenantMetricsPage({
  searchParams,
}: {
  searchParams: { from?: string; to?: string };
}) {
  const session = await getServerSession(authOptions);
  if (!session) {
    redirect("/auth/signin");
  }

  // Parse date range from search params
  // For lifetime queries, both dates will be null
  const fromDate = searchParams.from ? new Date(searchParams.from) : null;
  const toDate = searchParams.to ? new Date(searchParams.to) : null;
  
  // If no dates provided, default to last 30 days (but not for lifetime)
  const isLifetimeQuery = !searchParams.from && !searchParams.to;
  const actualFromDate = isLifetimeQuery ? null : (fromDate || addDays(new Date(), -30));
  const actualToDate = isLifetimeQuery ? null : (toDate || new Date());
  
  const period = dateRangeToPeriod(actualFromDate, actualToDate);

  // Fetch all data in parallel
  const [tenantMetrics, tenantActivity, planData, newTenantsOverTime] =
    await Promise.all([
      fetchTenantMetrics(actualFromDate, actualToDate),
      fetchTenantActivity(actualFromDate, actualToDate),
      fetchPlanDistribution(actualFromDate, actualToDate),
      fetchNewTenantsOverTime(actualFromDate, actualToDate, period),
    ]);

  const metricsData: TenantMetricsData = {
    tenantMetrics,
    tenantActivity,
    planDistribution: planData.planDistribution,
    tierDistribution: planData.tierDistribution,
    trialSubscriptions: planData.trialSubscriptions,
    newTenantsOverTime,
    period,
  };
  return <TenantMetricsClient data={metricsData} />;
}