import { NextResponse } from "next/server";
import db from "@/lib/shared-db";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/next-auth";
import {
  broadcastMentionNotifications,
  broadcastCommentReplyNotification,
} from "@/lib/websocket/notification-broadcaster";

// Get comments for a message
export async function GET(
  req: Request,
  { params }: { params: { chatId: string; messageId: string } }
) {
  try {
    const session: any = await getServerSession(authOptions);
    if (!session) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const { searchParams } = new URL(req.url);
    const includeResolved = searchParams.get("includeResolved") === "true";

    // Verify access to the chat
    const chat = await db.chat.findFirst({
      where: {
        id: params.chatId,
      },
    });

    if (!chat) {
      return new NextResponse("Chat not found", { status: 404 });
    }

    // Get comments for the message
    const comments = await db.comment.findMany({
      where: {
        messageId: params.messageId,
        status: includeResolved ? undefined : { not: "RESOLVED" },
      },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            email: true,
            image: true,
          },
        },
        replies: {
          include: {
            author: {
              select: {
                id: true,
                name: true,
                email: true,
                image: true,
              },
            },
          },
          orderBy: {
            createdAt: "asc",
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    return NextResponse.json({ comments });
  } catch (error) {
    console.error("[COMMENTS_GET]", error);
    return NextResponse.json({ error: "Internal Error" }, { status: 500 });
  }
}

// Create a new comment
export async function POST(
  req: Request,
  { params }: { params: { chatId: string; messageId: string } }
) {
  try {
    const session: any = await getServerSession(authOptions);
    if (!session) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const { content, parentId, mentions } = await req.json();

    if (!content || content.trim().length === 0) {
      return new NextResponse("Comment content is required", { status: 400 });
    }

    // Verify access to the chat
    const chat = await db.chat.findFirst({
      where: {
        id: params.chatId,
      },
    });

    if (!chat) {
      return new NextResponse("Chat not found", { status: 404 });
    }

    // Verify the message exists
    const message = await db.message.findFirst({
      where: {
        id: params.messageId,
        chatId: params.chatId,
      },
    });

    if (!message) {
      return new NextResponse("Message not found", { status: 404 });
    }

    // If this is a reply, verify the parent comment exists
    if (parentId) {
      const parentComment = await db.comment.findFirst({
        where: {
          id: parentId,
          messageId: params.messageId,
        },
      });

      if (!parentComment) {
        return new NextResponse("Parent comment not found", { status: 404 });
      }
    }

    // Create the comment
    const comment = await db.comment.create({
      data: {
        content: content.trim(),
        messageId: params.messageId,
        authorId: session.userId,
        parentId: parentId || null,
        mentions: mentions || null,
        tenantId: chat.tenantId,
      },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            email: true,
            image: true,
          },
        },
      },
    });

    // Create notifications for mentions
    if (mentions && mentions.length > 0) {
      const mentionNotifications = mentions.map((userId: string) => ({
        type: "MENTION",
        title: "You were mentioned in a comment",
        content: `${
          session.userName || session.user?.name
        } mentioned you in a comment`,
        userId,
        triggeredBy: session.userId,
        chatId: params.chatId,
        messageId: params.messageId,
        commentId: comment.id,
        tenantId: chat.tenantId,
      }));

      await db.notification.createMany({
        data: mentionNotifications,
      });
      console.log(
        "Created",
        mentionNotifications.length,
        "mention notifications"
      );

      // Broadcast real-time mention notifications
      try {
        await broadcastMentionNotifications(
          mentions,
          {
            id: session.userId,
            name: session.userName || session.user?.name || "Unknown User",
          },
          {
            chatId: params.chatId,
            messageId: params.messageId,
            commentId: comment.id,
            tenantId: chat.tenantId,
          }
        );
      } catch (error) {
        console.error("Failed to broadcast mention notifications:", error);
      }
    }

    // Create notification for parent comment author (if this is a reply)
    if (parentId) {
      const parentComment = await db.comment.findUnique({
        where: { id: parentId },
        include: { author: true },
      });

      if (parentComment && parentComment.authorId !== session.userId) {
        await db.notification.create({
          data: {
            type: "COMMENT_REPLY",
            title: "Someone replied to your comment",
            content: `${
              session.userName || session.user?.name
            } replied to your comment`,
            userId: parentComment.authorId,
            triggeredBy: session.userId,
            chatId: params.chatId,
            messageId: params.messageId,
            commentId: comment.id,
            tenantId: chat.tenantId,
          },
        });

        // Broadcast real-time comment reply notification
        try {
          await broadcastCommentReplyNotification(
            parentComment.authorId,
            {
              id: session.userId,
              name: session.userName || session.user?.name || "Unknown User",
            },
            {
              chatId: params.chatId,
              messageId: params.messageId,
              commentId: comment.id,
              tenantId: chat.tenantId,
            }
          );
        } catch (error) {
          console.error(
            "Failed to broadcast comment reply notification:",
            error
          );
        }
      }
    }

    return NextResponse.json({ comment });
  } catch (error) {
    console.error("[COMMENTS_POST]", error);
    return NextResponse.json({ error: "Internal Error" }, { status: 500 });
  }
}
