# Chart Generation Guidelines for AI Responses

## Overview

This document provides comprehensive guidelines for when and how the AI should generate charts, graphs, and diagrams in responses to enhance user understanding and data visualization.

## When to Generate Charts

### 1. Data Analysis Requests
- **Numerical Data**: When presenting statistics, metrics, or quantitative information
- **Comparisons**: When comparing different categories, time periods, or values
- **Trends**: When showing changes over time or patterns in data
- **Performance Metrics**: When discussing KPIs, sales figures, or performance indicators

### 2. Process Explanations
- **Workflows**: When explaining step-by-step processes
- **Decision Trees**: When showing decision-making processes
- **System Architecture**: When describing technical systems or structures
- **User Journeys**: When mapping user experiences or customer flows

### 3. Relationship Mapping
- **Organizational Charts**: When discussing team structures or hierarchies
- **Dependencies**: When showing how different elements relate to each other
- **Network Diagrams**: When explaining connections between entities
- **Mind Maps**: When organizing complex concepts or ideas

## Chart Type Selection Guide

### Line Charts
**Use for:**
- Time series data (sales over months, user growth over time)
- Trend analysis
- Continuous data progression
- Multiple data series comparison over time

**Example scenarios:**
- "Show me our revenue growth over the last 12 months"
- "How has website traffic changed this year?"
- "Compare our performance metrics quarter by quarter"

### Bar Charts
**Use for:**
- Categorical comparisons
- Ranking data
- Discrete value comparisons
- Department/team performance comparisons

**Example scenarios:**
- "Compare sales by region"
- "Which products are performing best?"
- "Show department budget allocations"

### Pie Charts
**Use for:**
- Proportional data (percentages, market share)
- Budget breakdowns
- Resource allocation
- Composition analysis

**Example scenarios:**
- "What's our market share breakdown?"
- "How is the budget distributed?"
- "Show the composition of our customer base"

### Area Charts
**Use for:**
- Volume data over time
- Cumulative values
- Stacked data showing parts of a whole
- Filled trend visualization

**Example scenarios:**
- "Show cumulative sales growth"
- "Display stacked revenue by product line"
- "Visualize resource usage over time"

### Mermaid Flowcharts
**Use for:**
- Process flows
- Decision trees
- System workflows
- Step-by-step procedures

**Example scenarios:**
- "Explain our customer onboarding process"
- "How does the approval workflow work?"
- "Show the decision-making process"

### Mermaid Sequence Diagrams
**Use for:**
- API interactions
- System communications
- User-system interactions
- Time-based processes

**Example scenarios:**
- "How does authentication work?"
- "Show the API call sequence"
- "Explain the data flow between systems"

## Data Quality Requirements

### Minimum Data Requirements
- **At least 2 data points** for meaningful visualization
- **Consistent data structure** across all data points
- **Clear labels and categories** for all data elements
- **Appropriate data types** (numerical for quantitative charts)

### Data Validation
- Ensure data accuracy before visualization
- Check for missing or null values
- Validate data ranges and outliers
- Confirm data relevance to the question

## Best Practices

### 1. Context and Explanation
- **Always provide context** before presenting a chart
- **Explain the insights** derived from the visualization
- **Highlight key findings** or patterns in the data
- **Connect the chart** to the user's original question

### 2. Chart Configuration
- **Use meaningful titles** that describe what the chart shows
- **Include appropriate labels** for axes and data series
- **Choose appropriate colors** that are accessible and meaningful
- **Set reasonable dimensions** for readability

### 3. Data Presentation
- **Keep data clear and readable**
- **Avoid overcrowding** charts with too much information
- **Use consistent formatting** across similar charts
- **Round numbers appropriately** for better readability

### 4. Accessibility
- **Use color-blind friendly palettes**
- **Provide text alternatives** for key insights
- **Ensure sufficient contrast** in visual elements
- **Include legends** when necessary

## Common Scenarios and Examples

### Business Analytics
```
User: "How did our sales perform last quarter?"
AI Response: Include a line chart showing monthly sales trends with context about performance drivers.
```

### Process Documentation
```
User: "How does our bug reporting process work?"
AI Response: Include a Mermaid flowchart showing the step-by-step process from bug discovery to resolution.
```

### Comparative Analysis
```
User: "Which marketing channels are most effective?"
AI Response: Include a bar chart comparing conversion rates or ROI across different channels.
```

### Resource Planning
```
User: "How should we allocate our budget?"
AI Response: Include a pie chart showing recommended budget distribution across departments or initiatives.
```

## Error Handling

### Invalid Data
- If data is insufficient or invalid, explain why a chart cannot be generated
- Suggest what additional information would be needed
- Provide alternative ways to present the information

### Technical Issues
- If chart rendering fails, provide the data in tabular format
- Explain the intended visualization
- Offer to retry with different chart parameters

## Quality Checklist

Before generating any chart, ensure:
- [ ] The chart type matches the data and question
- [ ] Data is sufficient and accurate
- [ ] Chart enhances understanding (doesn't just repeat text)
- [ ] Title and labels are clear and descriptive
- [ ] Context is provided before and after the chart
- [ ] Key insights are highlighted in text
- [ ] Chart is accessible and readable

## Advanced Features

### Interactive Elements
- Use hover tooltips for additional information
- Include zoom and pan capabilities for large datasets
- Provide download options for charts when appropriate

### Multi-Chart Responses
- Combine multiple chart types when telling a complete story
- Ensure charts complement each other
- Maintain consistent styling across charts

### Dynamic Updates
- Consider if charts should update with new data
- Provide timestamps for data currency
- Indicate data sources and collection methods

## Conclusion

Charts and diagrams should enhance communication, not complicate it. Always prioritize clarity, accuracy, and relevance over visual complexity. The goal is to help users understand information better, make informed decisions, and gain insights from their data.
