"use client";

import React from "react";
import { <PERSON><PERSON>enderer } from "./ChartRenderer";
import { MermaidRenderer } from "./MermaidRenderer";
import { ChartData, MermaidDiagram, ParsedChart, detectCharts } from "@/lib/utils/chart-parser";

interface VisualizationRendererProps {
  content: string;
  className?: string;
}

interface VisualizationBlockProps {
  chart: ChartData | MermaidDiagram;
  className?: string;
}

/**
 * Renders a single visualization block (chart or diagram)
 */
const VisualizationBlock: React.FC<VisualizationBlockProps> = ({ chart, className }) => {
  if (chart.type === 'mermaid') {
    return <MermaidRenderer diagram={chart} className={className} />;
  } else {
    return <ChartRenderer chart={chart} className={className} />;
  }
};

/**
 * Detects and renders all visualizations in content
 */
export const VisualizationRenderer: React.FC<VisualizationRendererProps> = ({ 
  content, 
  className 
}) => {
  const charts = detectCharts(content);
  
  if (charts.length === 0) {
    return null;
  }

  // Split content and render with visualizations
  const lines = content.split('\n');
  const elements: React.ReactNode[] = [];
  let lastIndex = 0;

  charts.forEach((parsedChart, chartIndex) => {
    // Add content before the chart
    if (parsedChart.startIndex > lastIndex) {
      const beforeContent = lines
        .slice(lastIndex, parsedChart.startIndex)
        .join('\n')
        .trim();
      
      if (beforeContent) {
        elements.push(
          <div key={`content-before-${chartIndex}`} className="mb-4">
            {beforeContent}
          </div>
        );
      }
    }

    // Add the chart
    elements.push(
      <div key={`chart-${chartIndex}`} className="my-4">
        <VisualizationBlock chart={parsedChart.chart} className={className} />
      </div>
    );

    lastIndex = parsedChart.endIndex + 1;
  });

  // Add remaining content after the last chart
  if (lastIndex < lines.length) {
    const afterContent = lines.slice(lastIndex).join('\n').trim();
    if (afterContent) {
      elements.push(
        <div key="content-after" className="mt-4">
          {afterContent}
        </div>
      );
    }
  }

  return <div className="space-y-4">{elements}</div>;
};

/**
 * Hook to check if content contains visualizations
 */
export const useHasVisualizations = (content: string): boolean => {
  return React.useMemo(() => {
    const charts = detectCharts(content);
    return charts.length > 0;
  }, [content]);
};

/**
 * Utility to extract just the visualizations from content
 */
export const extractVisualizations = (content: string): ParsedChart[] => {
  return detectCharts(content);
};

/**
 * Utility to remove visualization blocks from content
 */
export const removeVisualizationsFromContent = (content: string): string => {
  const charts = detectCharts(content);
  if (charts.length === 0) {
    return content;
  }

  const lines = content.split('\n');
  let result = '';
  let lastIndex = 0;

  charts.forEach((chart) => {
    // Add content before the chart
    if (chart.startIndex > lastIndex) {
      result += lines.slice(lastIndex, chart.startIndex).join('\n');
    }
    lastIndex = chart.endIndex + 1;
  });

  // Add remaining content
  if (lastIndex < lines.length) {
    result += lines.slice(lastIndex).join('\n');
  }

  return result.trim();
};

/**
 * Component that renders content with visualizations separated
 */
interface ContentWithVisualizationsProps {
  content: string;
  renderContent: (content: string) => React.ReactNode;
  className?: string;
}

export const ContentWithVisualizations: React.FC<ContentWithVisualizationsProps> = ({
  content,
  renderContent,
  className
}) => {
  const charts = detectCharts(content);
  
  if (charts.length === 0) {
    return <>{renderContent(content)}</>;
  }

  const lines = content.split('\n');
  const elements: React.ReactNode[] = [];
  let lastIndex = 0;

  charts.forEach((parsedChart, chartIndex) => {
    // Add content before the chart
    if (parsedChart.startIndex > lastIndex) {
      const beforeContent = lines
        .slice(lastIndex, parsedChart.startIndex)
        .join('\n')
        .trim();
      
      if (beforeContent) {
        elements.push(
          <div key={`content-before-${chartIndex}`}>
            {renderContent(beforeContent)}
          </div>
        );
      }
    }

    // Add the chart
    elements.push(
      <div key={`chart-${chartIndex}`} className="my-4">
        <VisualizationBlock chart={parsedChart.chart} className={className} />
      </div>
    );

    lastIndex = parsedChart.endIndex + 1;
  });

  // Add remaining content after the last chart
  if (lastIndex < lines.length) {
    const afterContent = lines.slice(lastIndex).join('\n').trim();
    if (afterContent) {
      elements.push(
        <div key="content-after">
          {renderContent(afterContent)}
        </div>
      );
    }
  }

  return <div className="space-y-4">{elements}</div>;
};
