"use client";

import React, { createContext, useContext, useEffect, useState } from "react";
import { useSession } from "next-auth/react";
import { useChangelog } from "@/hooks/useChangelog";
import ChangelogModal from "./changelog-modal";

interface ChangelogContextType {
  hasUnviewedChangelogs: boolean;
  showChangelogModal: () => void;
  refreshChangelogs: () => Promise<void>;
}

const ChangelogContext = createContext<ChangelogContextType>({
  hasUnviewedChangelogs: false,
  showChangelogModal: () => {},
  refreshChangelogs: async () => {},
});

export const useChangelogContext = () => {
  const context = useContext(ChangelogContext);
  if (!context) {
    throw new Error(
      "useChangelogContext must be used within a ChangelogProvider"
    );
  }
  return context;
};

interface ChangelogProviderProps {
  children: React.ReactNode;
  tenantId?: string;
}

export const ChangelogProvider: React.FC<ChangelogProviderProps> = ({
  children,
  tenantId,
}) => {
  const { data: session } = useSession();
  const [isInitialized, setIsInitialized] = useState(false);

  const {
    changelogs,
    isLoading,
    showModal,
    setShowModal,
    markAsViewed,
    markMultipleAsViewed,
    removeViewedChangelogs,
    refreshChangelogs,
  } = useChangelog(tenantId);

  // Initialize after session is loaded
  useEffect(() => {
    if (session !== undefined) {
      setIsInitialized(true);
    }
  }, [session]);

  // Auto-show modal when user logs in and there are changelogs
  useEffect(() => {
    if (isInitialized && session?.user && changelogs.length > 0 && !showModal) {
      // Small delay to ensure the UI is ready
      const timer = setTimeout(() => {
        setShowModal(true);
      }, 1000);

      return () => clearTimeout(timer);
    }
  }, [
    isInitialized,
    session?.user,
    changelogs.length,
    showModal,
    setShowModal,
  ]);

  const handleCloseModal = () => {
    setShowModal(false);
  };

  const handleMarkViewed = async (changelogId: string, dismissed = false) => {
    await markAsViewed(changelogId, dismissed);
  };

  const showChangelogModal = () => {
    if (changelogs.length > 0) {
      setShowModal(true);
    }
  };

  const contextValue: ChangelogContextType = {
    hasUnviewedChangelogs: changelogs.length > 0,
    showChangelogModal,
    refreshChangelogs,
  };

  return (
    <ChangelogContext.Provider value={contextValue}>
      {children}

      {/* Render changelog modal */}
      {isInitialized && session?.user && (
        <ChangelogModal
          changelogs={changelogs}
          isOpen={showModal}
          onClose={handleCloseModal}
          onMarkViewed={handleMarkViewed}
          onMarkMultipleAsViewed={markMultipleAsViewed}
          onRemoveViewedChangelogs={removeViewedChangelogs}
        />
      )}
    </ChangelogContext.Provider>
  );
};
