import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/next-auth";
import db from "@/lib/shared-db";

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Parse request body
    const body = await request.json();
    const {
      title,
      content,
      version,
      type = "RELEASE",
      priority = "MEDIUM",
      targetTenants = [],
      targetEnvironment,
      publishedAt,
      expiresAt,
      githubCommitSha,
      deploymentId,
    } = body;

    // Validate required fields
    if (!title || !content) {
      return NextResponse.json(
        { error: "Title and content are required" },
        { status: 400 }
      );
    }

    // Validate type
    const validTypes = ["RELEASE", "HOTFIX", "MAINTENANCE", "ANNOUNCEMENT"];
    if (!validTypes.includes(type)) {
      return NextResponse.json(
        { error: "Invalid type" },
        { status: 400 }
      );
    }

    // Validate priority
    const validPriorities = ["LOW", "MEDIUM", "HIGH", "CRITICAL"];
    if (!validPriorities.includes(priority)) {
      return NextResponse.json(
        { error: "Invalid priority" },
        { status: 400 }
      );
    }

    // Get database connection
    

    // First, look up tenant IDs by name
    let tenantIds: string[] = [];
    if (Array.isArray(targetTenants) && targetTenants.length > 0) {
      const tenants = await db.tenant.findMany({
        where: {
          name: {
            in: targetTenants
          }
        },
        select: {
          id: true
        }
      });
      tenantIds = tenants.map(tenant => tenant.id);
    }

    // Create changelog entry
    const changelog = await db.changelog.create({
      data: {
        title: title.trim(),
        content: content.trim(),
        version: version?.trim() || null,
        type,
        priority,
        targetTenants: tenantIds,
        targetEnvironment: targetEnvironment?.trim() || null,
        publishedAt: publishedAt ? new Date(publishedAt) : new Date(),
        expiresAt: expiresAt ? new Date(expiresAt) : null,
        githubCommitSha: githubCommitSha?.trim() || null,
        deploymentId: deploymentId?.trim() || null,
        authorId: (session as any).userId || session.user?.email || null,
        isActive: true,
      },
    });

    return NextResponse.json(
      {
        message: "Changelog created successfully",
        changelog: {
          id: changelog.id,
          title: changelog.title,
          type: changelog.type,
          priority: changelog.priority,
          publishedAt: changelog.publishedAt,
        },
      },
      { status: 201 }
    );
  } catch (error) {
    console.error("Error creating changelog:", error);
    console.error("Error details:", {
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    });
    return NextResponse.json(
      {
        error: "Internal server error",
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1", 10);
    const limit = parseInt(searchParams.get("limit") || "5", 10);
    const search = searchParams.get("search") || "";

    // Validate pagination parameters
    const validPage = Math.max(1, page);
    const validLimit = Math.min(Math.max(1, limit), 100);

    // Get database connection
    

    // Build search filter
    const searchFilter = search
      ? {
          OR: [
            { title: { contains: search } },
            { content: { contains: search } },
            { version: { contains: search } },
          ],
        }
      : {};

    // Get total count for pagination
    const total = await db.changelog.count({
      where: searchFilter,
    });

    // Fetch changelogs with pagination
    const changelogs = await db.changelog.findMany({
      where: searchFilter,
      orderBy: { publishedAt: "desc" },
      skip: (validPage - 1) * validLimit,
      take: validLimit,
    });

    const totalPages = Math.ceil(total / validLimit);

    return NextResponse.json({
      changelogs,
      pagination: {
        page: validPage,
        limit: validLimit,
        total,
        totalPages,
      },
      searchQuery: search,
    });
  } catch (error) {
    console.error("Error fetching changelogs:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
