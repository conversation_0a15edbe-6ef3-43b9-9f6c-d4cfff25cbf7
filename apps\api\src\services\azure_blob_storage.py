"""
Azure Blob Storage Service for file uploads and management.

This service handles file uploads to Azure Blob Storage and provides
publicly accessible URLs for uploaded files.
"""

import os
import uuid
import logging
from datetime import datetime, timedelta
from typing import Optional, BinaryIO
from azure.storage.blob import BlobServiceClient, BlobClient, generate_blob_sas, BlobSasPermissions
from azure.core.exceptions import AzureError
from src.config import settings

logger = logging.getLogger(__name__)

class AzureBlobStorageService:
    """Service for Azure Blob Storage file operations."""
    
    def __init__(self):
        self.connection_string = settings.AZURE_STORAGE_CONNECTION_STRING
        self.container_name = settings.AZURE_STORAGE_CONTAINER_NAME

        if self.connection_string:
            try:
                self.blob_service_client = BlobServiceClient.from_connection_string(self.connection_string)
                self.container_client = self.blob_service_client.get_container_client(self.container_name)
                self.configured = True

                # Extract account name and key for SAS token generation
                self._extract_account_info()

                logger.info(f"Azure Blob Storage configured with container: {self.container_name}")
            except Exception as e:
                logger.error(f"Failed to initialize Azure Blob Storage: {e}")
                self.configured = False
        else:
            self.blob_service_client = None
            self.container_client = None
            self.configured = False
            logger.warning("Azure Blob Storage not configured - file uploads will not be available")

    def _extract_account_info(self):
        """Extract account name and key from connection string for SAS generation."""
        try:
            # Parse connection string to extract account name and key
            conn_parts = dict(part.split('=', 1) for part in self.connection_string.split(';') if '=' in part)
            self.account_name = conn_parts.get('AccountName')
            self.account_key = conn_parts.get('AccountKey')

            if not self.account_name or not self.account_key:
                logger.warning("Could not extract account name/key from connection string - SAS generation may fail")
                self.account_name = None
                self.account_key = None
        except Exception as e:
            logger.error(f"Error extracting account info: {e}")
            self.account_name = None
            self.account_key = None
    
    def ensure_container_exists(self) -> bool:
        """Ensure the container exists, create if it doesn't."""
        if not self.configured:
            return False

        try:
            # Check if container exists, create if it doesn't
            self.container_client.create_container()
            logger.info(f"Container '{self.container_name}' created")
            return True
        except Exception as e:
            if "ContainerAlreadyExists" in str(e):
                logger.debug(f"Container '{self.container_name}' already exists")
                return True
            else:
                logger.error(f"Failed to create container: {e}")
                return False
    
    async def upload_audio_file(
        self,
        file_content: bytes,
        filename: str,
        content_type: str = "audio/mpeg"
    ) -> Optional[str]:
        """
        Upload audio file to blob storage and return public URL.

        Args:
            file_content: Binary content of the audio file
            filename: Original filename
            content_type: MIME type of the file

        Returns:
            Public URL of the uploaded file, or None if upload failed
        """
        if not self.configured:
            logger.error("Azure Blob Storage not configured")
            return None

        try:
            # Ensure container exists
            container_exists = self.ensure_container_exists()
            if not container_exists:
                logger.error("Failed to ensure container exists")
                return None
            
            # Generate unique blob name with audio prefix
            file_extension = os.path.splitext(filename)[1] or '.mp3'
            blob_name = f"audio/{uuid.uuid4()}-{filename}"
            
            # Get blob client
            blob_client = self.container_client.get_blob_client(blob_name)
            
            # Upload file with metadata
            blob_client.upload_blob(
                file_content,
                blob_type="BlockBlob",
                content_settings={
                    "content_type": content_type,
                    "content_disposition": f"inline; filename={filename}"
                },
                metadata={
                    "original_filename": filename,
                    "file_type": "audio",
                    "upload_source": "audio_transcription"
                },
                overwrite=True
            )
            
            # Return the public URL
            blob_url = blob_client.url
            logger.info(f"Audio file uploaded successfully: {blob_name}")
            return blob_url
            
        except AzureError as e:
            logger.error(f"Azure error uploading audio file: {e}")
            return None
        except Exception as e:
            logger.error(f"Error uploading audio file to blob storage: {e}")
            return None
    
    def delete_audio_file(self, blob_url: str) -> bool:
        """
        Delete audio file from blob storage.

        Args:
            blob_url: URL of the blob to delete

        Returns:
            True if deletion was successful, False otherwise
        """
        if not self.configured:
            logger.error("Azure Blob Storage not configured")
            return False

        try:
            # Extract blob name from URL
            blob_name = blob_url.split('/')[-1]
            if not blob_name:
                logger.error("Invalid blob URL")
                return False

            # Get blob client and delete
            blob_client = self.container_client.get_blob_client(blob_name)
            blob_client.delete_blob()

            logger.info(f"Audio file deleted successfully: {blob_name}")
            return True

        except AzureError as e:
            logger.error(f"Azure error deleting audio file: {e}")
            return False
        except Exception as e:
            logger.error(f"Error deleting audio file from blob storage: {e}")
            return False
    
    def get_blob_url(self, blob_name: str) -> str:
        """
        Get the public URL for a blob.
        
        Args:
            blob_name: Name of the blob
            
        Returns:
            Public URL of the blob
        """
        if not self.configured:
            return ""
            
        try:
            blob_client = self.container_client.get_blob_client(blob_name)
            return blob_client.url
        except Exception as e:
            logger.error(f"Error getting blob URL: {e}")
            return ""
    
    def upload_file_from_stream(
        self,
        file_stream: BinaryIO,
        filename: str,
        content_type: str,
        folder_prefix: str = "uploads"
    ) -> Optional[str]:
        """
        Upload file from stream to blob storage.

        Args:
            file_stream: File stream to upload
            filename: Original filename
            content_type: MIME type of the file
            folder_prefix: Folder prefix for organization

        Returns:
            Public URL of the uploaded file, or None if upload failed
        """
        if not self.configured:
            logger.error("Azure Blob Storage not configured")
            return None

        try:
            # Ensure container exists
            container_exists = self.ensure_container_exists()
            if not container_exists:
                return None
            
            # Generate unique blob name
            blob_name = f"{folder_prefix}/{uuid.uuid4()}-{filename}"
            
            # Get blob client
            blob_client = self.container_client.get_blob_client(blob_name)
            
            # Upload file
            blob_client.upload_blob(
                file_stream,
                blob_type="BlockBlob",
                content_settings={
                    "content_type": content_type,
                    "content_disposition": f"inline; filename={filename}"
                },
                metadata={
                    "original_filename": filename,
                    "upload_source": "api_upload"
                },
                overwrite=True
            )
            
            # Return the public URL
            blob_url = blob_client.url
            logger.info(f"File uploaded successfully: {blob_name}")
            return blob_url
            
        except AzureError as e:
            logger.error(f"Azure error uploading file: {e}")
            return None
        except Exception as e:
            logger.error(f"Error uploading file to blob storage: {e}")
            return None

    def generate_sas_url_for_audio(self, blob_url: str, expiry_hours: int = 1) -> Optional[str]:
        """
        Generate a SAS URL for an audio blob to allow Azure Video Indexer access.

        Args:
            blob_url: The original blob URL
            expiry_hours: Hours until the SAS token expires (default: 1 hour)

        Returns:
            SAS-enabled URL or None if generation fails
        """
        if not self.configured or not self.account_name or not self.account_key:
            logger.error("Azure Blob Storage not properly configured for SAS generation")
            return None

        try:
            # Extract blob name from URL
            # URL format: https://account.blob.core.windows.net/container/blob-path
            url_parts = blob_url.split('/')
            if len(url_parts) < 5:
                logger.error(f"Invalid blob URL format: {blob_url}")
                return None

            # Extract blob name (everything after container name)
            container_index = url_parts.index(self.container_name)
            blob_name = '/'.join(url_parts[container_index + 1:])

            logger.info(f"Generating SAS token for blob: {blob_name}")

            # Generate SAS token with read permissions
            sas_token = generate_blob_sas(
                account_name=self.account_name,
                container_name=self.container_name,
                blob_name=blob_name,
                account_key=self.account_key,
                permission=BlobSasPermissions(read=True),
                expiry=datetime.utcnow() + timedelta(hours=expiry_hours)
            )

            # Construct SAS URL
            sas_url = f"{blob_url}?{sas_token}"

            logger.info(f"SAS URL generated successfully, expires in {expiry_hours} hour(s)")
            return sas_url

        except Exception as e:
            logger.error(f"Error generating SAS URL: {e}")
            import traceback
            traceback.print_exc()
            return None

# Global instance
azure_blob_storage_service = AzureBlobStorageService()
