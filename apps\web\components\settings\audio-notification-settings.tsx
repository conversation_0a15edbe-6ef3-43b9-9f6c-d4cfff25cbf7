"use client";

import React, { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { <PERSON>lider } from "@/components/ui/slider";
import { Badge } from "@/components/ui/badge";
import { Volume2, VolumeX, Play, Bell, MessageCircle } from "lucide-react";
import {
  audioNotificationManager,
  areNotificationSoundsEnabled,
  getNotificationVolume,
  setNotificationSoundsEnabled,
  setNotificationVolume,
  hasUserInteractedForAudio,
} from "@/lib/audio/notification-sounds";
import toast from "react-hot-toast";

export const AudioNotificationSettings: React.FC = () => {
  const [enabled, setEnabled] = useState(areNotificationSoundsEnabled());
  const [volume, setVolume] = useState(getNotificationVolume());
  const [hasInteracted, setHasInteracted] = useState(
    hasUserInteractedForAudio()
  );
  const [isTestingSound, setIsTestingSound] = useState<string | null>(null);

  useEffect(() => {
    // Update interaction status periodically
    const interval = setInterval(() => {
      setHasInteracted(hasUserInteractedForAudio());
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  const handleEnabledChange = (newEnabled: boolean) => {
    setEnabled(newEnabled);
    setNotificationSoundsEnabled(newEnabled);

    if (newEnabled) {
      toast.success("Audio notifications enabled");
    } else {
      toast.success("Audio notifications disabled");
    }
  };

  const handleVolumeChange = (newVolume: number[]) => {
    const volumeValue = newVolume[0];
    setVolume(volumeValue);
    setNotificationVolume(volumeValue);
  };

  const testSound = async (type: "mention" | "reply" | "general") => {
    if (!hasInteracted) {
      toast.error("Please interact with the page first to enable audio");
      return;
    }

    setIsTestingSound(type);
    try {
      await audioNotificationManager.testSound(type);
      toast.success(`${type} sound played`);
    } catch (error) {
      toast.error(`Failed to play ${type} sound`);
      console.error("Sound test error:", error);
    } finally {
      setTimeout(() => setIsTestingSound(null), 500);
    }
  };

  const getVolumeIcon = () => {
    if (!enabled || volume === 0) return <VolumeX className="h-4 w-4" />;
    return <Volume2 className="h-4 w-4" />;
  };

  const getVolumeLabel = () => {
    if (volume === 0) return "Muted";
    if (volume < 0.3) return "Low";
    if (volume < 0.7) return "Medium";
    return "High";
  };

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Bell className="h-5 w-5" />
          Audio Notification Settings
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Browser Interaction Status */}
        <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
          <div className="flex items-center gap-2">
            <div
              className={`w-2 h-2 rounded-full ${
                hasInteracted ? "bg-green-500" : "bg-red-500"
              }`}
            />
            <span className="text-sm font-medium">Browser Audio Status</span>
          </div>
          <Badge variant={hasInteracted ? "default" : "destructive"}>
            {hasInteracted ? "Ready" : "Needs Interaction"}
          </Badge>
        </div>

        {!hasInteracted && (
          <div className="p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
            <p className="text-sm text-yellow-800 dark:text-yellow-200">
              <strong>Note:</strong> Audio notifications require user
              interaction with the page first. Click anywhere on the page to
              enable audio playback.
            </p>
          </div>
        )}

        {/* Enable/Disable Toggle */}
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-sm font-medium">Enable Audio Notifications</h3>
            <p className="text-xs text-gray-500 dark:text-gray-400">
              Play sounds when you receive @mentions and comment replies
            </p>
          </div>
          <Switch checked={enabled} onCheckedChange={handleEnabledChange} />
        </div>

        {/* Volume Control */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {getVolumeIcon()}
              <span className="text-sm font-medium">Volume</span>
            </div>
            <Badge variant="outline">{getVolumeLabel()}</Badge>
          </div>

          <div className="px-3">
            <Slider
              value={[volume]}
              onValueChange={handleVolumeChange}
              max={1}
              min={0}
              step={0.1}
              disabled={!enabled}
              className="w-full"
            />
          </div>

          <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400">
            <span>Silent</span>
            <span>Loud</span>
          </div>
        </div>

        {/* Sound Test Buttons */}
        <div className="space-y-3">
          <h3 className="text-sm font-medium">Test Notification Sounds</h3>
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => testSound("mention")}
              disabled={
                !enabled || !hasInteracted || isTestingSound === "mention"
              }
              className="flex items-center gap-2"
            >
              {isTestingSound === "mention" ? (
                <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
              ) : (
                <Play className="h-3 w-3" />
              )}
              @Mention Sound
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={() => testSound("reply")}
              disabled={
                !enabled || !hasInteracted || isTestingSound === "reply"
              }
              className="flex items-center gap-2"
            >
              {isTestingSound === "reply" ? (
                <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
              ) : (
                <MessageCircle className="h-3 w-3" />
              )}
              Reply Sound
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={() => testSound("general")}
              disabled={
                !enabled || !hasInteracted || isTestingSound === "general"
              }
              className="flex items-center gap-2"
            >
              {isTestingSound === "general" ? (
                <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
              ) : (
                <Bell className="h-3 w-3" />
              )}
              General Sound
            </Button>
          </div>
        </div>

        {/* Information */}
        <div className="text-xs text-gray-500 dark:text-gray-400 space-y-1">
          <p>
            • @Mention sounds are higher priority and more attention-grabbing
          </p>
          <p>• Reply sounds are gentler for comment replies</p>
          <p>• General sounds are used for other notifications</p>
          <p>• Settings are saved locally in your browser</p>
        </div>
      </CardContent>
    </Card>
  );
};
