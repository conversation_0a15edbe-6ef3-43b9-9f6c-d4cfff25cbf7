from fastapi import APIRouter, Depends, HTTPException, status
from typing import Dict, Any, Optional, List
from pydantic import BaseModel
from src.api.deps import get_db
import json
import datetime
from src.services.usage_tracker import UsageTracker
from bson import ObjectId
import logging
from urllib.parse import unquote

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

router = APIRouter()

class FileIndexRequest(BaseModel):
    file_id: str
    document_path: str
    document_type: Optional[str] = "auto"
    metadata: Optional[Dict[str, Any]] = None
    page_id: Optional[str] = None

class BulkIndexRequest(BaseModel):
    workspace_slug: str
    files: List[FileIndexRequest]

class BulkIndexResponse(BaseModel):
    success: bool
    message: str
    total_files: int
    jobs_created: int
    failed_files: int
    job_ids: List[str]
    failed_file_ids: List[str]

@router.post("/bulk-index", response_model=BulkIndexResponse)
async def bulk_index_documents(
    bulk_request: BulkIndexRequest,
    current_user: str,
    tenant_id: str,
    db = Depends(get_db)
):
    """
    Bulk index multiple documents with background processing.
    Creates all jobs first, then processes them with controlled concurrency.
    """
    start_time = datetime.datetime.now(datetime.timezone.utc)
    usage_tracker = UsageTracker(db)
    
    logger.info(f"Starting bulk index for {len(bulk_request.files)} files")
    
    # Get workspace
    slug = unquote(bulk_request.workspace_slug)
    workspace = await db.Workspace.find_one({"slug": slug})
    
    if not workspace:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Workspace not found"
        )
    
    # Results tracking
    job_ids = []
    failed_file_ids = []
    jobs_created = 0

    # Create background job records in database (fast operation - no actual processing)
    logger.info(f"Creating background job records for {len(bulk_request.files)} files")
    
    for file_request in bulk_request.files:
        try:
            # Update file status to PROCESSING
            await db.File.update_one(
                {"_id": ObjectId(file_request.file_id)},
                {"$set": {"vectorizationStatus": "PROCESSING"}}
            )
            
            # Check if this is an audio/video file
            document_path = file_request.document_path
            is_audio_video_url = False
            
            if document_path.startswith(('http://', 'https://')):
                audio_video_extensions = ['.mp3', '.wav', '.m4a', '.ogg', '.mp4', '.avi', '.mov', '.webm', '.flac', '.aac']
                is_audio_video_url = any(ext in document_path.lower() for ext in audio_video_extensions)
            
            # Generate unique job ID
            import uuid
            job_id = str(uuid.uuid4())

            if is_audio_video_url:
                # Create video processing job record only (no actual processing)
                logger.info(f"Creating video processing job record for file {file_request.file_id}")

                job_data = {
                    "jobId": job_id,
                    "status": "pending",
                    "videoFiles": [{
                        "url": document_path,
                        "name": f"file_{file_request.file_id}",
                        "type": "video" if any(ext in document_path.lower() for ext in ['.mp4', '.avi', '.mov', '.webm', '.mkv', '.flv', '.wmv', '.m4v', '.3gp']) else "audio",
                        "size": 0
                    }],
                    "userId": ObjectId(current_user),
                    "tenantId": ObjectId(tenant_id),
                    "fileId": file_request.file_id,
                    "workspaceSlug": slug,
                    "createdAt": datetime.datetime.now(datetime.timezone.utc),
                    "updatedAt": datetime.datetime.now(datetime.timezone.utc)
                }

                await db.VideoProcessingJob.insert_one(job_data)

            else:
                # Create document processing job record only (no actual processing)
                logger.info(f"Creating document processing job record for file {file_request.file_id}")

                job_data = {
                    "jobId": job_id,
                    "status": "pending",
                    "documentPath": document_path,
                    "documentType": file_request.document_type,
                    "metadata": file_request.metadata or {},
                    "userId": ObjectId(current_user),
                    "tenantId": ObjectId(tenant_id),
                    "workspaceId": ObjectId(workspace["_id"]),
                    "fileId": file_request.file_id,
                    "pageId": file_request.page_id,
                    "workspaceSlug": slug,
                    "createdAt": datetime.datetime.now(datetime.timezone.utc),
                    "updatedAt": datetime.datetime.now(datetime.timezone.utc)
                }

                await db.DocumentProcessingJob.insert_one(job_data)
            
            job_ids.append(job_id)
            jobs_created += 1
            logger.info(f"Created job record {job_id} for file {file_request.file_id}")

        except Exception as e:
            logger.error(f"Error creating job record for file {file_request.file_id}: {e}")
            failed_file_ids.append(file_request.file_id)
            # Update file status to failed
            await db.File.update_one(
                {"_id": ObjectId(file_request.file_id)},
                {"$set": {"vectorizationStatus": "FAILED"}}
            )

    # Jobs are now in database and will be processed by background workers
    logger.info(f"Created {jobs_created} job records in database. Background workers will process them with controlled concurrency.")
    
    # Log token usage for bulk indexing
    try:
        # Estimate token usage based on number of files
        estimated_tokens = len(bulk_request.files) * 100  # Rough estimate
        
        await usage_tracker.log_token_usage(
            tenant_id=tenant_id,
            input_text=f"Bulk indexing {len(bulk_request.files)} files",
            output_text="",
            request_type="bulk_document_indexing",
            model_used="embed-v-4-0"
        )
        
        await usage_tracker.log_api_request(
            user_id=current_user,
            tenant_id=tenant_id,
            endpoint="/bulk-index",
            method="POST",
            status_code=200,
            duration=int((datetime.datetime.now(datetime.timezone.utc) - start_time).total_seconds() * 1000)
        )
    except Exception as e:
        logger.error(f"Error logging usage for bulk indexing: {e}")
    
    # Prepare response
    total_files = len(bulk_request.files)
    failed_files = len(failed_file_ids)
    
    response = BulkIndexResponse(
        success=jobs_created > 0,
        message=f"Created {jobs_created} background jobs for processing. {failed_files} files failed to create jobs." if failed_files > 0 else f"Successfully created {jobs_created} background jobs for processing.",
        total_files=total_files,
        jobs_created=jobs_created,
        failed_files=failed_files,
        job_ids=job_ids,
        failed_file_ids=failed_file_ids
    )
    
    logger.info(f"Bulk index completed: {jobs_created} jobs created, {failed_files} failed")
    return response

@router.get("/bulk-index-status", response_model=Dict[str, Any])
async def get_bulk_index_status(
    workspace_slug: str,
    current_user: str,
    tenant_id: str,
    db = Depends(get_db)
):
    """
    Get the status of all document processing jobs for a workspace.
    """
    try:
        # Get workspace
        slug = unquote(workspace_slug)
        workspace = await db.Workspace.find_one({"slug": slug})
        
        if not workspace:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Workspace not found"
            )
        
        # Get all document processing jobs for this workspace
        jobs = await db.DocumentProcessingJob.find({
            "workspaceId": workspace["_id"],
            "userId": ObjectId(current_user)
        }).sort("createdAt", -1).limit(100).to_list(100)
        
        # Count jobs by status
        status_counts = {
            "pending": 0,
            "processing": 0,
            "completed": 0,
            "failed": 0
        }
        
        job_details = []
        for job in jobs:
            status_counts[job["status"]] += 1
            job_details.append({
                "jobId": job["jobId"],
                "status": job["status"],
                "fileId": job["fileId"],
                "documentType": job["documentType"],
                "createdAt": job["createdAt"],
                "completedAt": job.get("completedAt"),
                "errorMessage": job.get("errorMessage")
            })
        
        return {
            "status": 200,
            "workspace_slug": workspace_slug,
            "status_counts": status_counts,
            "total_jobs": len(jobs),
            "jobs": job_details
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting bulk index status: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get bulk index status"
        )
