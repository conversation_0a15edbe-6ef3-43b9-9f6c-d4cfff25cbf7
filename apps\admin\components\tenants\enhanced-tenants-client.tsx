"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { Collapsible } from "@/components/ui/collapsible";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { formatDate } from "@/lib/utils";
import {
  Plus,
  Search,
  X,
  Building,
  CheckCircle,
  Filter,
  ChevronDown,
  RotateCcw,
  Ban,
  Bell,
  Users,
  Download,
  MoreHorizontal,
  Eye,
  ArrowUp,
  UserX,
  ArrowUpDown,
  Loader2,
  Power,
  PowerOff,
  Shield
} from "lucide-react";
import { useRouter, useSearchParams } from "next/navigation";
import { useState, useTransition, useEffect } from "react";
import { AddTenantModal } from "@/components/dashboard/add-tenant-modal";
import { ChangePlanModal } from "@/components/tenants/change-plan-modal";
import { useToast } from "@/components/ui/use-toast";
import Link from "next/link";

// Enhanced types for comprehensive tenant management
interface Tenant {
  id: string;
  name: string;
  slug: string;
  createdAt: Date;
  plan: string;
  status: "active" | "inactive" | "suspended";
  users: number;
  storage: string;
  hasActiveSubscription: boolean;
  lastActivity: Date | null;
  tokenUsagePercent: number;
  infraCost: number;
  margin: number;
  healthScore: number;
  planType: "STARTER" | "TEAM" | "PRO" | "ENTERPRISE" | "SOLO";
  // ✅ isActiveTenant removed - using unified status field
}

interface TenantStats {
  totalTenants: number;
  activeTenants: number;
  trialTenants: number;
  inactiveTenants: number;
  suspendedTenants: number;
  healthyTenants: number;
  atRiskTenants: number;
}

interface TenantsPageData {
  tenants: Tenant[];
  stats: TenantStats;
  availablePlans: Plan[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  searchQuery: string;
  initialFilter?: string;
}

interface Plan {
  id: string;
  name: string;
  type: string;
  description?: string;
  price?: number;
  includedUsers: number;
  vectorStoreGB: number;
  webSearchLimit: number;
  additionalUserFee?: number;
}

interface EnhancedTenantsClientProps {
  data: TenantsPageData;
}



// Helper functions
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('de-CH', {
    style: 'currency',
    currency: 'CHF',
    minimumFractionDigits: 2,
  }).format(amount);
};

const getStatusBadge = (status: string) => {
  const variants = {
    active: "bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100",
    inactive: "bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100",
    suspended: "bg-orange-100 text-orange-800 dark:bg-orange-800 dark:text-orange-100",
  };

  return (
    <Badge className={variants[status as keyof typeof variants] || variants.inactive}>
      {status.charAt(0).toUpperCase() + status.slice(1)}
    </Badge>
  );
};

const getHealthScoreBadge = (score: number) => {
  if (score >= 80) {
    return (
      <Badge className="bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100">
        Healthy
      </Badge>
    );
  } else if (score >= 60) {
    return (
      <Badge className="bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100">
        Watchlist
      </Badge>
    );
  } else {
    return (
      <Badge className="bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100">
        At Risk
      </Badge>
    );
  }
};

const getPlanBadge = (planType: string) => {
  const colors = {
    STARTER: "bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100",
    TEAM: "bg-purple-100 text-purple-800 dark:bg-purple-800 dark:text-purple-100",
    PRO: "bg-indigo-100 text-indigo-800 dark:bg-indigo-800 dark:text-indigo-100",
    ENTERPRISE: "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-100",
    SOLO: "bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100",
  };

  return (
    <Badge className={colors[planType as keyof typeof colors] || colors.STARTER}>
      {planType}
    </Badge>
  );
};

// ✅ getActivationBadge removed - now using unified status badges

export function EnhancedTenantsClient({ data }: EnhancedTenantsClientProps) {
  const { tenants: initialTenants, availablePlans, initialFilter } = data;
  const router = useRouter();
  const searchParams = useSearchParams();
  const [, startTransition] = useTransition();
  const { toast } = useToast();

  // ✅ Local state for tenants to enable real-time updates
  const [tenants, setTenants] = useState<Tenant[]>(initialTenants);

  // ✅ Sync local state when props change (e.g., on page refresh)
  useEffect(() => {
    setTenants(initialTenants);
  }, [initialTenants]);

  const [searchInput, setSearchInput] = useState("");
  const [addTenantOpen, setAddTenantOpen] = useState(false);

  // Frontend pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 4; // 4-5 tenants per page maximum

  // Advanced filter state - initialize based on URL parameters
  const getInitialHealthScoreFilter = () => {
    if (initialFilter === "risk") {
      return "at-risk";
    }
    return "all";
  };

  const getInitialStatusFilter = () => {
    if (initialFilter === "active") {
      return "active";
    }
    return "all";
  };


  const [planFilter, setPlanFilter] = useState<string[]>(["all"]);
  const [activityRange, setActivityRange] = useState("all");
  const [healthScoreRange, setHealthScoreRange] = useState(getInitialHealthScoreFilter());
  const [statusFilter, setStatusFilter] = useState(getInitialStatusFilter());
  const [overQuotaOnly, setOverQuotaOnly] = useState(false);
  const [lowMarginThreshold, setLowMarginThreshold] = useState<string>("");

  // Filter popover state
  const [filterPopoverOpen, setFilterPopoverOpen] = useState(false);

  // Bulk actions state
  const [selectedTenants, setSelectedTenants] = useState<string[]>([]);
  const [bulkActionLoading, setBulkActionLoading] = useState(false);
  const [notifyModalOpen, setNotifyModalOpen] = useState(false);
  const [assignPlanModalOpen, setAssignPlanModalOpen] = useState(false);
  const [suspendConfirmOpen, setSuspendConfirmOpen] = useState(false);

  // Individual tenant actions state
  const [upgradeModalOpen, setUpgradeModalOpen] = useState(false);
  const [individualSuspendOpen, setIndividualSuspendOpen] = useState(false);
  const [selectedTenantForAction, setSelectedTenantForAction] = useState<string | null>(null);

  // Change Plan state
  const [changePlanModalOpen, setChangePlanModalOpen] = useState(false);
  const [selectedTenantForChangePlan, setSelectedTenantForChangePlan] = useState<Tenant | null>(null);

  // Tenant activation state
  const [activationLoading, setActivationLoading] = useState<string | null>(null);

  // Form input states for modals
  const [selectedPlanForAssignment, setSelectedPlanForAssignment] = useState<string>(availablePlans[0]?.type || "STARTER");
  const [notificationMessage, setNotificationMessage] = useState("");
  const [notificationPriority, setNotificationPriority] = useState("standard");

  // ✅ Sync filter state when URL parameters change
  useEffect(() => {
    const currentFilter = searchParams?.get('filter');
    if (currentFilter === 'risk' && healthScoreRange !== 'at-risk') {
      setHealthScoreRange('at-risk');
      setStatusFilter('all');
      setFilterPopoverOpen(true); // Auto-open popover for risk filter
    } else if (currentFilter === 'active' && statusFilter !== 'active') {
      setStatusFilter('active');
      setHealthScoreRange('all');
      setFilterPopoverOpen(true); // Auto-open popover for active filter
    } else if (!currentFilter && (healthScoreRange === 'at-risk' || statusFilter === 'active')) {
      // Reset filters if no URL parameter but filters are active
      setHealthScoreRange('all');
      setStatusFilter('all');
    }
  }, [searchParams, healthScoreRange, statusFilter]);



  // Advanced filtering logic
  const filteredTenants = tenants.filter(tenant => {
    // Search filter
    const matchesSearch = !searchInput ||
      tenant.name.toLowerCase().includes(searchInput.toLowerCase()) ||
      tenant.slug.toLowerCase().includes(searchInput.toLowerCase());

    // Plan filter (multi-select) - ensure we handle all possible plan types
    const matchesPlan = planFilter.includes("all") || planFilter.includes(tenant.planType);

    // Activity range filter
    const matchesActivity = (() => {
      if (activityRange === "all") return true;
      if (!tenant.lastActivity) return false;

      const lastActivity = new Date(tenant.lastActivity);
      const now = new Date();
      const daysDiff = Math.floor((now.getTime() - lastActivity.getTime()) / (1000 * 60 * 60 * 24));

      switch (activityRange) {
        case "7": return daysDiff <= 7;
        case "30": return daysDiff <= 30;
        case "90": return daysDiff <= 90;
        default: return true;
      }
    })();

    // Health score filter
    const matchesHealthScore = (() => {
      if (healthScoreRange === "all") return true;
      const score = tenant.healthScore;

      switch (healthScoreRange) {
        case "healthy": return score >= 80;
        case "watchlist": return score >= 60 && score < 80;
        case "at-risk": return score < 60;
        default: return true;
      }
    })();

    // Status filter
    const matchesStatus = (() => {
      if (statusFilter === "all") return true;
      return tenant.status === statusFilter;
    })();

    // Over-quota filter
    const matchesOverQuota = !overQuotaOnly || tenant.tokenUsagePercent >= 80;

    // Low margin filter
    const matchesLowMargin = (() => {
      if (!lowMarginThreshold || lowMarginThreshold === "") return true;
      const threshold = parseFloat(lowMarginThreshold);
      if (isNaN(threshold)) return true;

      // Calculate margin percentage: (margin / revenue) * 100
      const revenue = tenant.infraCost + tenant.margin;
      if (revenue <= 0) return true; // Include tenants with zero/negative revenue when filter is active
      const marginPercent = (tenant.margin / revenue) * 100;

      return marginPercent < threshold;
    })();

    return matchesSearch && matchesPlan && matchesActivity && matchesHealthScore && matchesStatus && matchesOverQuota && matchesLowMargin;
  });

  // Calculate dynamic statistics based on filtered results
  const filteredStats = {
    totalTenants: filteredTenants.length,
    activeTenants: filteredTenants.filter(t => t.status === 'active').length,
    trialTenants: filteredTenants.filter(t => t.planType === 'STARTER').length, // Assuming STARTER is trial
    inactiveTenants: filteredTenants.filter(t => t.status === 'inactive').length,
    suspendedTenants: filteredTenants.filter(t => t.status === 'suspended').length,
    healthyTenants: filteredTenants.filter(t => t.healthScore >= 80).length,
    atRiskTenants: filteredTenants.filter(t => t.healthScore < 60).length,
  };

  // Check if any filters are active
  const hasActiveFilters = searchInput !== "" ||
    !planFilter.includes("all") ||
    activityRange !== "all" ||
    healthScoreRange !== "all" ||
    statusFilter !== "all" ||
    overQuotaOnly ||
    lowMarginThreshold !== "";

  const totalPages = Math.ceil(filteredTenants.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const paginatedTenants = filteredTenants.slice(startIndex, startIndex + itemsPerPage);

  // Handle search
  const handleSearch = (query: string) => {
    setSearchInput(query);
    setCurrentPage(1); // Reset to first page on search
  };

  // Handle simple plan filter (for header dropdown)
  const handleSimplePlanFilter = (plan: string) => {
    if (plan === "all") {
      setPlanFilter(["all"]);
    } else {
      setPlanFilter([plan]);
    }
    setCurrentPage(1);
  };

  // Handle advanced plan filter (multi-select)
  const handlePlanFilterChange = (plan: string) => {
    setPlanFilter(prev => {
      if (plan === "all") {
        return ["all"];
      }

      const newFilter = prev.filter(p => p !== "all");
      if (newFilter.includes(plan)) {
        const filtered = newFilter.filter(p => p !== plan);
        return filtered.length === 0 ? ["all"] : filtered;
      } else {
        return [...newFilter, plan];
      }
    });
    setCurrentPage(1);
  };

  // Update URL parameters when filters change
  const updateURLParams = (newHealthScore?: string, newStatus?: string) => {
    const params = new URLSearchParams(searchParams?.toString());

    // Remove filter parameter if no special filters are active
    if (newHealthScore === "all" && newStatus === "all") {
      params.delete("filter");
    } else if (newHealthScore === "at-risk") {
      params.set("filter", "risk");
    } else if (newStatus === "active") {
      params.set("filter", "active");
    } else {
      params.delete("filter");
    }

    startTransition(() => {
      router.push(`/tenants?${params.toString()}`);
    });
  };

  // Clear all filters
  const clearAllFilters = () => {
    setPlanFilter(["all"]);
    setActivityRange("all");
    setHealthScoreRange("all");
    setStatusFilter("all");
    setOverQuotaOnly(false);
    setLowMarginThreshold("");
    setCurrentPage(1);
    updateURLParams("all", "all");
  };

  // Handle pagination
  const handlePageChange = (newPage: number) => {
    setCurrentPage(newPage);
  };

  // Handle clear search
  const handleClearSearch = () => {
    setSearchInput("");
    setCurrentPage(1);
  };

  // Handle health score filter change with URL update
  const handleHealthScoreChange = (value: string) => {
    setHealthScoreRange(value);
    setCurrentPage(1);
    updateURLParams(value, statusFilter);
  };

  // Handle status filter change with URL update
  const handleStatusFilterChange = (value: string) => {
    setStatusFilter(value);
    setCurrentPage(1);
    updateURLParams(healthScoreRange, value);
  };

  // Handle Active Tenants card click
  const handleActiveTenantsClick = () => {
    setStatusFilter("active");
    setHealthScoreRange("all");
    setCurrentPage(1);
    updateURLParams("all", "active");
  };

  // Handle row click navigation
  const handleRowClick = (tenantId: string) => {
    startTransition(() => {
      router.push(`/tenants/${tenantId}`);
    });
  };

  // Bulk actions handlers
  const handleTenantSelect = (tenantId: string, checked: boolean) => {
    if (checked) {
      setSelectedTenants(prev => [...prev, tenantId]);
    } else {
      setSelectedTenants(prev => prev.filter(id => id !== tenantId));
    }
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      // Select ALL filtered tenants across all pages, not just current page
      setSelectedTenants(filteredTenants.map(t => t.id));
    } else {
      setSelectedTenants([]);
    }
  };

  const clearSelection = () => {
    setSelectedTenants([]);
  };

  const getSelectedTenants = () => {
    return tenants.filter(tenant => selectedTenants.includes(tenant.id));
  };

  const handleBulkSuspend = async () => {
    setBulkActionLoading(true);
    try {
      const selectedTenantsData = getSelectedTenants();
      const hasActiveTenants = selectedTenantsData.some(t => t.status === 'active');
      const newStatus = hasActiveTenants ? 'suspended' : 'active';

      // Process each tenant individually
      const results = await Promise.allSettled(
        selectedTenants.map(async (tenantId) => {
          const response = await fetch(`/api/tenants/${tenantId}/status`, {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              status: newStatus
            }),
          });

          if (!response.ok) {
            const errorData = await response.json();
            throw new Error(`Failed to update ${tenantId}: ${errorData.error || 'Unknown error'}`);
          }

          return { tenantId, status: newStatus };
        })
      );

      // Count successful and failed operations
      const successful = results.filter(result => result.status === 'fulfilled');
      const failed = results.filter(result => result.status === 'rejected');

      // Update local state for successful operations
      if (successful.length > 0) {
        setTenants((prevTenants: Tenant[]) =>
          prevTenants.map((tenant: Tenant) =>
            selectedTenants.includes(tenant.id)
              ? { ...tenant, status: newStatus as "active" | "inactive" | "suspended" }
              : tenant
          )
        );
      }

      // Show results
      if (failed.length === 0) {
        toast({
          title: "Success",
          description: `Successfully ${newStatus === 'suspended' ? 'suspended' : 'activated'} ${successful.length} tenant${successful.length > 1 ? 's' : ''}`,
        });
      } else {
        toast({
          title: "Partial Success",
          description: `${successful.length} tenant${successful.length > 1 ? 's' : ''} updated successfully, ${failed.length} failed`,
          variant: failed.length > successful.length ? "destructive" : "default",
        });
      }

      clearSelection();
    } catch (error) {
      console.error('Error in bulk suspend:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : 'Failed to update tenant status',
        variant: "destructive",
      });
    } finally {
      setBulkActionLoading(false);
      setSuspendConfirmOpen(false);
    }
  };

  const handleBulkNotify = async (message: string, priority: string) => {
    setBulkActionLoading(true);
    try {
      const selectedTenantsData = getSelectedTenants();

      // Create notification payload
      const notificationData = {
        message,
        priority,
        tenantIds: selectedTenants,
        tenantNames: selectedTenantsData.map(t => t.name),
        timestamp: new Date().toISOString(),
        type: 'bulk_tenant_notification'
      };

      // For now, we'll use the existing notification/alert system
      // In a real implementation, this would send to an admin notification service
      const response = await fetch('/api/admin/notifications', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(notificationData),
      });

      if (!response.ok) {
        throw new Error('Failed to send notification');
      }

      toast({
        title: "Notification Sent",
        description: `Admin notification sent for ${selectedTenants.length} tenant${selectedTenants.length > 1 ? 's' : ''}`,
      });

      clearSelection();
    } catch (error) {
      console.error('Error in bulk notify:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : 'Failed to send notification',
        variant: "destructive",
      });
    } finally {
      setBulkActionLoading(false);
      setNotifyModalOpen(false);
      // Reset form values
      setNotificationMessage("");
      setNotificationPriority("standard");
    }
  };

  const handleBulkAssignPlan = async (planType: string) => {
    setBulkActionLoading(true);
    try {
      // Process each tenant individually
      const results = await Promise.allSettled(
        selectedTenants.map(async (tenantId) => {
          const response = await fetch(`/api/tenants/${tenantId}/plan`, {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              planType: planType
            }),
          });

          if (!response.ok) {
            const errorData = await response.json();
            throw new Error(`Failed to update plan for ${tenantId}: ${errorData.error || 'Unknown error'}`);
          }

          return { tenantId, planType };
        })
      );

      // Count successful and failed operations
      const successful = results.filter(result => result.status === 'fulfilled');
      const failed = results.filter(result => result.status === 'rejected');

      // Update local state for successful operations
      if (successful.length > 0) {
        setTenants((prevTenants: Tenant[]) =>
          prevTenants.map((tenant: Tenant) =>
            selectedTenants.includes(tenant.id)
              ? { ...tenant, planType: planType as "STARTER" | "TEAM" | "PRO" | "ENTERPRISE" | "SOLO", plan: planType }
              : tenant
          )
        );
      }

      // Show results
      if (failed.length === 0) {
        toast({
          title: "Success",
          description: `Successfully assigned ${planType} plan to ${successful.length} tenant${successful.length > 1 ? 's' : ''}`,
        });
      } else {
        toast({
          title: "Partial Success",
          description: `${successful.length} tenant${successful.length > 1 ? 's' : ''} updated successfully, ${failed.length} failed`,
          variant: failed.length > successful.length ? "destructive" : "default",
        });
      }

      clearSelection();
    } catch (error) {
      console.error('Error in bulk assign plan:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : 'Failed to assign plans',
        variant: "destructive",
      });
    } finally {
      setBulkActionLoading(false);
      setAssignPlanModalOpen(false);
      // Reset form values
      setSelectedPlanForAssignment(availablePlans[0]?.type || "STARTER");
    }
  };

  const handleBulkDownloadCSV = () => {
    const selectedTenantsData = getSelectedTenants();
    const csvHeaders = [
      'Name', 'Slug', 'Plan', 'Status', 'Users', 'Storage', 'Last Activity',
      'Token Usage %', 'Infra Cost (CHF)', 'Margin (CHF)', 'Health Score'
    ];

    const csvData = selectedTenantsData.map(tenant => [
      tenant.name,
      tenant.slug,
      tenant.plan,
      tenant.status,
      tenant.users.toString(),
      tenant.storage,
      tenant.lastActivity ? formatDate(tenant.lastActivity) : 'No activity',
      tenant.tokenUsagePercent.toFixed(1) + '%',
      formatCurrency(tenant.infraCost),
      formatCurrency(tenant.margin),
      tenant.healthScore.toString()
    ]);

    const csvContent = [csvHeaders, ...csvData]
      .map(row => row.map(field => `"${field}"`).join(','))
      .join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `tenants-export-${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    clearSelection();
  };

  // Individual tenant action handlers
  const handleIndividualView = (tenantId: string) => {
    startTransition(() => {
      router.push(`/tenants/${tenantId}`);
    });
  };

  const handleIndividualUpgrade = (tenantId: string) => {
    setSelectedTenantForAction(tenantId);
    setUpgradeModalOpen(true);
  };

  // Change Plan functionality
  const handleChangePlan = (tenantId: string) => {
    const tenant = tenants.find(t => t.id === tenantId);
    if (tenant) {
      setSelectedTenantForChangePlan(tenant);
      setChangePlanModalOpen(true);
    }
  };

  const handleChangePlanSuccess = (message: string) => {
    // Success is already handled by the modal with toast
    console.log('Plan change success:', message);
  };

  const handleChangePlanError = (error: string) => {
    // Error is already handled by the modal with toast
    console.error('Plan change error:', error);
  };

  // ✅ Unified tenant status handler
  const handleTenantStatusChange = async (tenantId: string, newStatus: "active" | "inactive" | "suspended") => {
    setActivationLoading(tenantId);

    try {
      const response = await fetch(`/api/tenants/${tenantId}/status`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          status: newStatus
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update tenant status');
      }

      const result = await response.json();

      // ✅ Update local state immediately after successful API call
      setTenants((prevTenants: Tenant[]) =>
        prevTenants.map((tenant: Tenant) =>
          tenant.id === tenantId
            ? { ...tenant, status: newStatus }
            : tenant
        )
      );

      toast({
        title: "Success",
        description: result.message,
      });

    } catch (error) {
      console.error('Error updating tenant status:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : 'Failed to update tenant status',
        variant: "destructive",
      });
    } finally {
      setActivationLoading(null);
    }
  };

  const handleIndividualSuspend = (tenantId: string) => {
    setSelectedTenantForAction(tenantId);
    setIndividualSuspendOpen(true);
  };

  const executeIndividualSuspend = async () => {
    if (!selectedTenantForAction) return;

    setBulkActionLoading(true);
    try {
      // TODO: Implement API call to suspend/reactivate individual tenant
      console.log('Individual suspend/reactivate:', selectedTenantForAction);
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
    } catch (error) {
      console.error('Error in individual suspend:', error);
    } finally {
      setBulkActionLoading(false);
      setIndividualSuspendOpen(false);
      setSelectedTenantForAction(null);
    }
  };

  const executeIndividualUpgrade = async (newPlan: string) => {
    if (!selectedTenantForAction) return;

    setBulkActionLoading(true);
    try {
      // TODO: Implement API call to upgrade individual tenant plan
      console.log('Individual upgrade:', { tenantId: selectedTenantForAction, newPlan });
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
    } catch (error) {
      console.error('Error in individual upgrade:', error);
    } finally {
      setBulkActionLoading(false);
      setUpgradeModalOpen(false);
      setSelectedTenantForAction(null);
    }
  };

  const getSelectedTenantForAction = () => {
    return tenants.find(tenant => tenant.id === selectedTenantForAction);
  };

  return (
    <div className="space-y-6">
      {/* Consolidated Header */}
      <div className="flex items-center justify-between">
        <div>
          <div className="flex items-center gap-3">
            <h1 className="text-3xl font-bold">All Tenants</h1>
            {healthScoreRange === "at-risk" && (
              <Badge variant="destructive" className="flex items-center gap-1">
                <Shield className="h-3 w-3" />
                At Risk Filter Active
              </Badge>
            )}
            {statusFilter === "active" && (
              <Badge variant="default" className="flex items-center gap-1 bg-green-600 hover:bg-green-700">
                <CheckCircle className="h-3 w-3" />
                Active Status Filter Applied
              </Badge>
            )}
          </div>
          <p className="text-muted-foreground">
            Comprehensive tenant oversight and management
          </p>
        </div>
        <div className="flex items-center gap-3">
          {/* Search Input */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <Input
              placeholder="Search by name or slug..."
              value={searchInput}
              onChange={(e) => handleSearch(e.target.value)}
              className="pl-10 pr-10 w-64"
            />
            {searchInput && (
              <Button
                variant="ghost"
                size="sm"
                className="absolute right-1 top-1/2 h-6 w-6 -translate-y-1/2 p-0"
                onClick={handleClearSearch}
              >
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>

          {/* Filters Popover */}
          <Popover open={filterPopoverOpen} onOpenChange={setFilterPopoverOpen}>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className="flex items-center gap-2"
              >
                <Filter className="h-4 w-4" />
                Filters
                <ChevronDown className="h-4 w-4" />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-80 max-h-96 overflow-y-auto" align="end">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h4 className="font-medium">Filter Tenants</h4>
                  <Button variant="ghost" size="sm" onClick={clearAllFilters} className="h-8 px-2">
                    <RotateCcw className="h-3 w-3 mr-1" />
                    Clear All
                  </Button>
                </div>

                {/* Plan Tiers Section */}
                <Collapsible defaultOpen>
                  <div className="space-y-3">
                    <Label className="text-sm font-medium">Plan Tiers</Label>
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <input
                          type="radio"
                          id="plan-all"
                          name="planFilter"
                          checked={planFilter.includes("all")}
                          onChange={() => setPlanFilter(["all"])}
                          className="h-4 w-4 rounded-sm"
                        />
                        <Label htmlFor="plan-all" className="text-sm font-normal">All Plans</Label>
                      </div>
                      {availablePlans.map((plan) => (
                        <div key={plan.type} className="flex items-center space-x-2">
                          <input
                            type="radio"
                            id={`plan-${plan.type}`}
                            name="planFilter"
                            checked={planFilter.includes(plan.type) && !planFilter.includes("all")}
                            onChange={() => setPlanFilter([plan.type])}
                            className="h-4 w-4 rounded-sm"
                          />
                          <Label htmlFor={`plan-${plan.type}`} className="text-sm font-normal">
                            {plan.name}
                          </Label>
                        </div>
                      ))}
                    </div>
                  </div>
                </Collapsible>

                <Separator />

                {/* Activity Range Section */}
                <Collapsible defaultOpen>
                  <div className="space-y-3">
                    <Label className="text-sm font-medium">Activity Range</Label>
                    <div className="space-y-2">
                      {[
                        { value: "all", label: "All Time" },
                        { value: "7", label: "Last 7 days" },
                        { value: "30", label: "Last 30 days" },
                        { value: "90", label: "Last 90 days" },
                        { value: "inactive", label: "Inactive (90+ days)" }
                      ].map((option) => (
                        <div key={option.value} className="flex items-center space-x-2">
                          <input
                            type="radio"
                            id={`activity-${option.value}`}
                            name="activityRange"
                            checked={activityRange === option.value}
                            onChange={() => setActivityRange(option.value)}
                            className="h-4 w-4 rounded-sm"
                          />
                          <Label htmlFor={`activity-${option.value}`} className="text-sm font-normal">
                            {option.label}
                          </Label>
                        </div>
                      ))}
                    </div>
                  </div>
                </Collapsible>

                <Separator />

                {/* Health Score Section */}
                <Collapsible defaultOpen>
                  <div className="space-y-3">
                    <Label className="text-sm font-medium">Health Score</Label>
                    <div className="space-y-2">
                      {[
                        { value: "all", label: "All Health Scores" },
                        { value: "healthy", label: "🟢 Healthy (80-100)" },
                        { value: "watchlist", label: "🟡 Watchlist (60-80)" },
                        { value: "at-risk", label: "🔴 At Risk (<60)" }
                      ].map((option) => (
                        <div key={option.value} className="flex items-center space-x-2">
                          <input
                            type="radio"
                            id={`health-${option.value}`}
                            name="healthScore"
                            checked={healthScoreRange === option.value}
                            onChange={() => handleHealthScoreChange(option.value)}
                            className="h-4 w-4 rounded-sm"
                          />
                          <Label htmlFor={`health-${option.value}`} className="text-sm font-normal">
                            {option.label}
                          </Label>
                        </div>
                      ))}
                    </div>
                  </div>
                </Collapsible>

                <Separator />

                {/* Status Section */}
                <Collapsible defaultOpen>
                  <div className="space-y-3">
                    <Label className="text-sm font-medium">Status</Label>
                    <div className="space-y-2">
                      {[
                        { value: "all", label: "All Statuses" },
                        { value: "active", label: "🟢 Active" },
                        { value: "inactive", label: "🔴 Inactive" },
                        { value: "suspended", label: "🟠 Suspended" }
                      ].map((option) => (
                        <div key={option.value} className="flex items-center space-x-2">
                          <input
                            type="radio"
                            id={`status-${option.value}`}
                            name="status"
                            checked={statusFilter === option.value}
                            onChange={() => handleStatusFilterChange(option.value)}
                            className="h-4 w-4 rounded-sm"
                          />
                          <Label htmlFor={`status-${option.value}`} className="text-sm font-normal">
                            {option.label}
                          </Label>
                        </div>
                      ))}
                    </div>
                  </div>
                </Collapsible>

                <Separator />

                {/* Special Filters Section */}
                <Collapsible defaultOpen>
                  <div className="space-y-3">
                    <Label className="text-sm font-medium">Special Filters</Label>
                    <div className="space-y-3">
                      {/* Over-Quota Filter */}
                      <div className="space-y-2">
                        <div className="flex items-center space-x-2">
                          <input
                            type="radio"
                            id="quota-all"
                            name="overQuota"
                            checked={!overQuotaOnly}
                            onChange={() => setOverQuotaOnly(false)}
                            className="h-4 w-4 rounded-sm"
                          />
                          <Label htmlFor="quota-all" className="text-sm font-normal">All Tenants</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <input
                            type="radio"
                            id="quota-over"
                            name="overQuota"
                            checked={overQuotaOnly}
                            onChange={() => setOverQuotaOnly(true)}
                            className="h-4 w-4 rounded-sm"
                          />
                          <Label htmlFor="quota-over" className="text-sm font-normal">Over-Quota Only</Label>
                        </div>
                      </div>

                      {/* Margin Threshold */}
                      <div className="space-y-2">
                        <Label htmlFor="margin-input" className="text-sm font-medium">
                          Show tenants with margin below %
                        </Label>
                        <div className="flex items-center space-x-2">
                          <Input
                            id="margin-input"
                            type="number"
                            step="0.1"
                            placeholder="e.g., 20.5"
                            value={lowMarginThreshold}
                            onChange={(e) => setLowMarginThreshold(e.target.value)}
                            className="flex-1"
                          />
                          <span className="text-sm text-muted-foreground">%</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </Collapsible>

                {/* Filter Summary */}
                <div className="pt-3 border-t">
                  <div className="text-sm text-muted-foreground">
                    {filteredTenants.length} tenant{filteredTenants.length !== 1 ? 's' : ''} found
                  </div>
                </div>
              </div>
            </PopoverContent>
          </Popover>

          {/* Add Tenant Button */}
          <Button onClick={() => setAddTenantOpen(true)}>
            <Plus className="mr-2 h-4 w-4" />
            Add Tenant
          </Button>
        </div>
      </div>



      {/* Enhanced Stats Cards - Dynamic based on filtered results */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center">
              <Building className="mr-2 h-4 w-4" />
              Total
              {hasActiveFilters && <span className="ml-1 text-xs text-muted-foreground">(filtered)</span>}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{filteredStats.totalTenants}</div>
          </CardContent>
        </Card>
        <Card
          className="cursor-pointer hover:bg-muted/50 transition-colors"
          onClick={handleActiveTenantsClick}
        >
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center">
              <CheckCircle className="mr-2 h-4 w-4 text-green-600" />
              Active
              {hasActiveFilters && <span className="ml-1 text-xs text-muted-foreground">(filtered)</span>}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{filteredStats.activeTenants}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">
              Trial
              {hasActiveFilters && <span className="ml-1 text-xs text-muted-foreground">(filtered)</span>}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{filteredStats.trialTenants}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">
              Inactive
              {hasActiveFilters && <span className="ml-1 text-xs text-muted-foreground">(filtered)</span>}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{filteredStats.inactiveTenants}</div>
          </CardContent>
        </Card>
        <Card className="hidden">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">
              Suspended
              {hasActiveFilters && <span className="ml-1 text-xs text-muted-foreground">(filtered)</span>}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">{filteredStats.suspendedTenants}</div>
          </CardContent>
        </Card>
        <Card className="hidden">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">
              🟢 Healthy
              {hasActiveFilters && <span className="ml-1 text-xs text-muted-foreground">(filtered)</span>}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{filteredStats.healthyTenants}</div>
          </CardContent>
        </Card>
        <Card className="hidden">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">
              🔴 At Risk
              {hasActiveFilters && <span className="ml-1 text-xs text-muted-foreground">(filtered)</span>}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{filteredStats.atRiskTenants}</div>
          </CardContent>
        </Card>
      </div>

      {/* Bulk Actions Menu Bar */}
      {selectedTenants.length > 0 && (
        <Card className="mb-4 border-l-4 border-l-primary">
          <CardContent className="py-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <span className="text-sm font-medium">
                  {selectedTenants.length} tenant{selectedTenants.length > 1 ? 's' : ''} selected
                </span>
                <Button variant="ghost" size="sm" onClick={clearSelection}>
                  <X className="h-4 w-4 mr-1" />
                  Clear Selection
                </Button>
              </div>

              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setSuspendConfirmOpen(true)}
                  disabled={bulkActionLoading}
                >
                  <Ban className="h-4 w-4 mr-1" />
                  {getSelectedTenants().some(t => t.status === 'active') ? 'Suspend' : 'Reactivate'}
                </Button>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setNotifyModalOpen(true)}
                  disabled={bulkActionLoading}
                >
                  <Bell className="h-4 w-4 mr-1" />
                  Notify Admin
                </Button>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setAssignPlanModalOpen(true)}
                  disabled={bulkActionLoading}
                >
                  <Users className="h-4 w-4 mr-1" />
                  Assign Plan
                </Button>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleBulkDownloadCSV}
                  disabled={bulkActionLoading}
                >
                  <Download className="h-4 w-4 mr-1" />
                  Download CSV
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Clean Tenants Table or Empty State */}
      <Card>
        <CardContent className="p-0">
          {filteredTenants.length === 0 ? (
            /* Empty State Message */
            <div className="flex flex-col items-center justify-center py-12 px-6 text-center">
              <div className="w-16 h-16 bg-muted rounded-full flex items-center justify-center mb-4">
                <Building className="h-8 w-8 text-muted-foreground" />
              </div>
              <h3 className="text-lg font-semibold mb-2">No tenants found</h3>
              <p className="text-muted-foreground mb-6 max-w-md">
                {hasActiveFilters
                  ? "No tenants match your current filter criteria."
                  : "There are no tenants in the system yet. Create your first tenant to get started."
                }
              </p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="border-b bg-muted/50">
                  <tr>
                    <th className="p-4 text-left">
                      <Checkbox
                        checked={selectedTenants.length === filteredTenants.length && filteredTenants.length > 0}
                        onCheckedChange={(checked) => handleSelectAll(checked === true)}
                        aria-label="Select all tenants"
                      />
                    </th>
                    <th className="p-4 text-left font-medium">Tenant Name</th>
                    <th className="p-4 text-left font-medium">Plan</th>
                    <th className="p-4 text-left font-medium">Status</th>
                    <th className="p-4 text-left font-medium">Last Activity</th>
                    <th className="p-4 text-left font-medium">Token Usage %</th>
                    <th className="p-4 text-left font-medium">Infra Cost</th>
                    <th className="p-4 text-left font-medium">Margin</th>
                    <th className="p-4 text-left font-medium">Health Score</th>
                    <th className="p-4 text-left font-medium">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {paginatedTenants.map((tenant) => (
                    <tr
                      key={tenant.id}
                      className={`border-b hover:bg-muted/50 transition-colors ${selectedTenants.includes(tenant.id) ? 'bg-muted/30' : ''
                        }`}
                    >
                      <td className="p-4" onClick={(e) => e.stopPropagation()}>
                        <Checkbox
                          checked={selectedTenants.includes(tenant.id)}
                          onCheckedChange={(checked) => handleTenantSelect(tenant.id, checked === true)}
                          aria-label={`Select ${tenant.name}`}
                        />
                      </td>
                      <td className="p-4 cursor-pointer" onClick={() => handleRowClick(tenant.id)}>
                        <div className="flex flex-col">
                          <Link
                            href={`/tenants/${tenant.id}`}
                            className="font-medium text-primary hover:underline"
                            onClick={(e) => e.stopPropagation()}
                          >
                            {tenant.name}
                          </Link>
                          <span className="text-sm text-muted-foreground">{tenant.slug}</span>
                        </div>
                      </td>
                      <td className="p-4 cursor-pointer" onClick={() => handleRowClick(tenant.id)}>
                        {getPlanBadge(tenant.planType)}
                      </td>
                      <td className="p-4 cursor-pointer" onClick={() => handleRowClick(tenant.id)}>
                        {getStatusBadge(tenant.status)}
                      </td>
                      <td className="p-4 cursor-pointer" onClick={() => handleRowClick(tenant.id)}>
                        <div className="text-sm">
                          {tenant.lastActivity
                            ? formatDate(tenant.lastActivity)
                            : <span className="text-muted-foreground">No activity</span>
                          }
                        </div>
                      </td>
                      <td className="p-4 cursor-pointer" onClick={() => handleRowClick(tenant.id)}>
                        <div className="flex items-center gap-2">
                          <div className="w-16 bg-muted rounded-full h-2">
                            <div
                              className={`h-2 rounded-full ${tenant.tokenUsagePercent >= 90 ? 'bg-red-500' :
                                tenant.tokenUsagePercent >= 70 ? 'bg-yellow-500' :
                                  'bg-green-500'
                                }`}
                              style={{ width: `${Math.min(100, tenant.tokenUsagePercent)}%` }}
                            />
                          </div>
                          <span className="text-sm font-medium">
                            {tenant.tokenUsagePercent.toFixed(1)}%
                          </span>
                        </div>
                      </td>
                      <td className="p-4 cursor-pointer" onClick={() => handleRowClick(tenant.id)}>
                        <span className="font-medium">
                          {formatCurrency(tenant.infraCost)}
                        </span>
                      </td>
                      <td className="p-4 cursor-pointer" onClick={() => handleRowClick(tenant.id)}>
                        <span className={`font-medium ${tenant.margin >= 0 ? 'text-green-600' : 'text-red-600'
                          }`}>
                          {formatCurrency(tenant.margin)}
                        </span>
                      </td>
                      <td className="cursor-pointer" onClick={() => handleRowClick(tenant.id)}>
                        {getHealthScoreBadge(tenant.healthScore)}
                      </td>
                      <td className="p-4" onClick={(e) => e.stopPropagation()}>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                              <MoreHorizontal className="h-4 w-4" />
                              <span className="sr-only">Open menu</span>
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={(e) => {
                              e.preventDefault();
                              e.stopPropagation();
                              handleIndividualView(tenant.id);
                            }}>
                              <Eye className="mr-2 h-4 w-4" />
                              View
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={(e) => {
                              e.preventDefault();
                              e.stopPropagation();
                              handleChangePlan(tenant.id);
                            }}>
                              <ArrowUpDown className="mr-2 h-4 w-4" />
                              Change Plan
                            </DropdownMenuItem>
                            {/* ✅ Unified Status Management */}
                            {tenant.status !== "active" && (
                              <DropdownMenuItem
                                onClick={(e) => {
                                  e.preventDefault();
                                  e.stopPropagation();
                                  handleTenantStatusChange(tenant.id, "active");
                                }}
                                disabled={activationLoading === tenant.id}
                              >
                                {activationLoading === tenant.id ? (
                                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                ) : (
                                  <Power className="mr-2 h-4 w-4" />
                                )}
                                {activationLoading === tenant.id ? 'Processing...' : 'Set Active'}
                              </DropdownMenuItem>
                            )}
                            {tenant.status !== "inactive" && (
                              <DropdownMenuItem
                                onClick={(e) => {
                                  e.preventDefault();
                                  e.stopPropagation();
                                  handleTenantStatusChange(tenant.id, "inactive");
                                }}
                                disabled={activationLoading === tenant.id}
                              >
                                {activationLoading === tenant.id ? (
                                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                ) : (
                                  <PowerOff className="mr-2 h-4 w-4" />
                                )}
                                {activationLoading === tenant.id ? 'Processing...' : 'Set Inactive'}
                              </DropdownMenuItem>
                            )}
                            {tenant.status !== "suspended" && (
                              <DropdownMenuItem
                                onClick={(e) => {
                                  e.preventDefault();
                                  e.stopPropagation();
                                  handleTenantStatusChange(tenant.id, "suspended");
                                }}
                                disabled={activationLoading === tenant.id}
                              >
                                {activationLoading === tenant.id ? (
                                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                ) : (
                                  <Ban className="mr-2 h-4 w-4" />
                                )}
                                {activationLoading === tenant.id ? 'Processing...' : 'Suspend'}
                              </DropdownMenuItem>
                            )}
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>)}

          {totalPages > 1 && (
            <div className="flex items-center justify-between p-4 border-t">
              <div className="text-sm text-muted-foreground">
                Showing {startIndex + 1} to {Math.min(startIndex + itemsPerPage, filteredTenants.length)} of{' '}
                {filteredTenants.length} tenants
              </div>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  disabled={currentPage <= 1}
                  onClick={() => handlePageChange(currentPage - 1)}
                >
                  Previous
                </Button>

                {/* Page Numbers */}
                <div className="flex gap-1">
                  {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                    <Button
                      key={page}
                      variant={currentPage === page ? "default" : "outline"}
                      size="sm"
                      className="w-8 h-8 p-0"
                      onClick={() => handlePageChange(page)}
                    >
                      {page}
                    </Button>
                  ))}
                </div>

                <Button
                  variant="outline"
                  size="sm"
                  disabled={currentPage >= totalPages}
                  onClick={() => handlePageChange(currentPage + 1)}
                >
                  Next
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Add Tenant Modal */}
      <AddTenantModal
        open={addTenantOpen}
        onOpenChange={setAddTenantOpen}
      />

      {/* Bulk Actions Modals */}

      {/* Suspend Confirmation Dialog */}
      <Dialog open={suspendConfirmOpen} onOpenChange={setSuspendConfirmOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {getSelectedTenants().some(t => t.status === 'active') ? 'Suspend Tenants' : 'Reactivate Tenants'}
            </DialogTitle>
            <DialogDescription>
              Are you sure you want to {getSelectedTenants().some(t => t.status === 'active') ? 'suspend' : 'reactivate'} the following {selectedTenants.length} tenant{selectedTenants.length > 1 ? 's' : ''}?
              <div className="mt-2 max-h-32 overflow-y-auto">
                <ul className="text-sm space-y-1">
                  {getSelectedTenants().map(tenant => (
                    <li key={tenant.id} className="flex justify-between">
                      <span>{tenant.name}</span>
                      <span className="text-muted-foreground">({tenant.status})</span>
                    </li>
                  ))}
                </ul>
              </div>
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setSuspendConfirmOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleBulkSuspend} disabled={bulkActionLoading}>
              {bulkActionLoading ? 'Processing...' : 'Confirm'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Notify Admin Modal */}
      <Dialog open={notifyModalOpen} onOpenChange={setNotifyModalOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Notify Admin</DialogTitle>
            <DialogDescription>
              Send a notification to administrators about {selectedTenants.length} selected tenant{selectedTenants.length > 1 ? 's' : ''}.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium mb-2 block">Priority</label>
              <Select value={notificationPriority} onValueChange={setNotificationPriority}>
                <SelectTrigger>
                  <SelectValue placeholder="Select priority" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="info">Info</SelectItem>
                  <SelectItem value="standard">Standard</SelectItem>
                  <SelectItem value="urgent">Urgent</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <label className="text-sm font-medium mb-2 block">Message</label>
              <Input
                placeholder="Enter notification message..."
                value={notificationMessage}
                onChange={(e) => setNotificationMessage(e.target.value)}
              />
            </div>
            <div className="text-sm text-muted-foreground">
              Selected tenants: {getSelectedTenants().map(t => t.name).join(', ')}
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setNotifyModalOpen(false)}>
              Cancel
            </Button>
            <Button
              onClick={() => handleBulkNotify(notificationMessage, notificationPriority)}
              disabled={bulkActionLoading || !notificationMessage.trim()}
            >
              {bulkActionLoading ? 'Sending...' : 'Send Notification'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Assign Plan Modal */}
      <Dialog open={assignPlanModalOpen} onOpenChange={setAssignPlanModalOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Assign Plan</DialogTitle>
            <DialogDescription>
              Assign a new plan to {selectedTenants.length} selected tenant{selectedTenants.length > 1 ? 's' : ''}.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium mb-2 block">New Plan</label>
              <Select value={selectedPlanForAssignment} onValueChange={setSelectedPlanForAssignment}>
                <SelectTrigger>
                  <SelectValue placeholder="Select plan" />
                </SelectTrigger>
                <SelectContent>
                  {availablePlans.map((plan) => (
                    <SelectItem key={plan.id} value={plan.type}>
                      {plan.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="text-sm">
              <div className="font-medium mb-2">Current plan distribution:</div>
              {Object.entries(
                getSelectedTenants().reduce((acc, tenant) => {
                  // Find the plan name for this plan type
                  const planName = availablePlans.find(p => p.type === tenant.planType)?.name || tenant.planType;
                  acc[planName] = (acc[planName] || 0) + 1;
                  return acc;
                }, {} as Record<string, number>)
              ).map(([planName, count]) => (
                <div key={planName} className="flex justify-between text-muted-foreground">
                  <span>{planName}</span>
                  <span>{count} tenant{count > 1 ? 's' : ''}</span>
                </div>
              ))}
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setAssignPlanModalOpen(false)}>
              Cancel
            </Button>
            <Button onClick={() => handleBulkAssignPlan(selectedPlanForAssignment)} disabled={bulkActionLoading}>
              {bulkActionLoading ? 'Assigning...' : 'Assign Plan'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Individual Tenant Action Modals */}

      {/* Individual Suspend Confirmation Dialog */}
      <Dialog open={individualSuspendOpen} onOpenChange={setIndividualSuspendOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {getSelectedTenantForAction()?.status === 'active' ? 'Suspend Tenant' : 'Reactivate Tenant'}
            </DialogTitle>
            <DialogDescription>
              Are you sure you want to {getSelectedTenantForAction()?.status === 'active' ? 'suspend' : 'reactivate'} the tenant "{getSelectedTenantForAction()?.name}"?
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIndividualSuspendOpen(false)}>
              Cancel
            </Button>
            <Button onClick={executeIndividualSuspend} disabled={bulkActionLoading}>
              {bulkActionLoading ? 'Processing...' : 'Confirm'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Individual Upgrade Modal */}
      <Dialog open={upgradeModalOpen} onOpenChange={setUpgradeModalOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Upgrade Tenant Plan</DialogTitle>
            <DialogDescription>
              Upgrade the plan for tenant "{getSelectedTenantForAction()?.name}".
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium mb-2 block">Current Plan</label>
              <div className="p-2 bg-muted rounded-md text-sm">
                {getSelectedTenantForAction()?.planType || 'Unknown'}
              </div>
            </div>
            <div>
              <label className="text-sm font-medium mb-2 block">New Plan</label>
              <Select defaultValue="">
                <SelectTrigger>
                  <SelectValue placeholder="Select new plan" />
                </SelectTrigger>
                <SelectContent>
                  {availablePlans.map((plan) => (
                    <SelectItem key={plan.id} value={plan.type}>
                      {plan.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setUpgradeModalOpen(false)}>
              Cancel
            </Button>
            <Button onClick={() => executeIndividualUpgrade('PRO')} disabled={bulkActionLoading}>
              {bulkActionLoading ? 'Upgrading...' : 'Upgrade Plan'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Change Plan Modal */}
      <ChangePlanModal
        open={changePlanModalOpen}
        onOpenChange={setChangePlanModalOpen}
        tenant={selectedTenantForChangePlan}
        onSuccess={handleChangePlanSuccess}
        onError={handleChangePlanError}
        availablePlans={availablePlans}
      />
    </div>
  );
}
