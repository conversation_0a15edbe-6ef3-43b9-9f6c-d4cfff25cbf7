"use client";

import React from 'react';
import { useDraggable } from '@dnd-kit/core';
import { CSS } from '@dnd-kit/utilities';
import { ChatItem } from './types';
import { cn } from '@/lib/utils';
import { GripVertical } from 'lucide-react';
import { MouseSensor, useSensor, useSensors } from '@dnd-kit/core';
import { useSortable } from '@dnd-kit/sortable';

interface DraggableChatProps {
  chat: ChatItem;
  children: React.ReactNode;
  className?: string;
}

interface DragHandleProps {
  listeners: any;
  attributes: any;
  isDragging: boolean;
}

function DragHandle({ listeners, attributes, isDragging }: DragHandleProps) {
  return (
    <div
      className={cn(
        "flex items-center justify-center h-full px-1 absolute left-0 top-0 bottom-0 z-20",
        "opacity-0 group-hover:opacity-60 hover:opacity-100 transition-opacity cursor-grab active:cursor-grabbing",
        isDragging && "opacity-100"
      )}
      style={{ pointerEvents: 'auto' }}
      {...listeners}
      {...attributes}
      title="Drag to move chat"
    >
      <GripVertical className="h-4 w-4 text-muted-foreground" />
    </div>
  );
}

export function DraggableChat({ chat, children, className }: DraggableChatProps) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
    isSorting,
  } = useSortable({
    id: `chat-${chat.id}`,
    data: {
      type: 'chat',
      chatId: chat.id,
      sourceGroupId: chat.groupId,
      chatTitle: chat.title,
    },
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={cn(
        "relative flex items-center group",
        isDragging && "opacity-0",
        className
      )}
      {...listeners}
      {...attributes}
      title="Drag to move chat"
    >
      <div className="flex-1 min-w-0">{children}</div>
    </div>
  );
}
