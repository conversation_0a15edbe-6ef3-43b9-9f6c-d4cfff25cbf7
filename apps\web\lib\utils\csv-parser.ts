/**
 * CSV parsing utilities for bulk member import
 */

export interface ParsedMemberData {
  email: string;
  name?: string;
  role: string;
}

export interface CSVParseResult {
  data: ParsedMemberData[];
  errors: Array<{
    row: number;
    field: string;
    message: string;
  }>;
  warnings: Array<{
    row: number;
    field: string;
    message: string;
  }>;
}

const REQUIRED_HEADERS = ["email", "role"];
const OPTIONAL_HEADERS = ["name"];
const VALID_ROLES = ["MEMBER", "ADMIN", "CUSTOM"];

/**
 * Parses CSV content and validates member data
 */
export function parseCSV(csvContent: string): CSVParseResult {
  const result: CSVParseResult = {
    data: [],
    errors: [],
    warnings: [],
  };

  try {
    const lines = csvContent.trim().split("\n");

    if (lines.length < 2) {
      result.errors.push({
        row: 0,
        field: "file",
        message: "CSV file must contain at least a header row and one data row",
      });
      return result;
    }

    // Parse headers
    const headers = lines[0].split(",").map((h) => h.trim().toLowerCase());

    // Validate required headers
    const missingHeaders = REQUIRED_HEADERS.filter(
      (header) => !headers.includes(header.toLowerCase())
    );

    if (missingHeaders.length > 0) {
      result.errors.push({
        row: 0,
        field: "headers",
        message: `Missing required headers: ${missingHeaders.join(", ")}`,
      });
      return result;
    }

    // Parse data rows
    for (let i = 1; i < lines.length; i++) {
      const rowData = parseCSVRow(lines[i]);
      const rowNumber = i + 1;

      if (rowData.length === 0) continue; // Skip empty rows

      const memberData: Partial<ParsedMemberData> = {};

      // Map CSV columns to member data
      headers.forEach((header, index) => {
        const value = rowData[index]?.trim() || "";

        switch (header) {
          case "email":
            memberData.email = value;
            break;
          case "name":
            memberData.name = value;
            break;
          case "role":
            memberData.role = value.toUpperCase();
            break;
        }
      });

      // Validate member data
      const validationResult = validateMemberData(memberData, rowNumber);
      result.errors.push(...validationResult.errors);
      result.warnings.push(...validationResult.warnings);

      if (validationResult.errors.length === 0) {
        result.data.push(memberData as ParsedMemberData);
      }
    }
  } catch (error) {
    result.errors.push({
      row: 0,
      field: "file",
      message: `Failed to parse CSV: ${
        error instanceof Error ? error.message : "Unknown error"
      }`,
    });
  }

  return result;
}

/**
 * Parses a single CSV row, handling quoted values
 */
function parseCSVRow(row: string): string[] {
  const result: string[] = [];
  let current = "";
  let inQuotes = false;

  for (let i = 0; i < row.length; i++) {
    const char = row[i];

    if (char === '"') {
      if (inQuotes && row[i + 1] === '"') {
        // Escaped quote
        current += '"';
        i++; // Skip next quote
      } else {
        // Toggle quote state
        inQuotes = !inQuotes;
      }
    } else if (char === "," && !inQuotes) {
      // End of field
      result.push(current);
      current = "";
    } else {
      current += char;
    }
  }

  // Add the last field
  result.push(current);

  return result;
}

/**
 * Validates member data for a single row
 */
function validateMemberData(
  data: Partial<ParsedMemberData>,
  rowNumber: number
): { errors: CSVParseResult["errors"]; warnings: CSVParseResult["warnings"] } {
  const errors: CSVParseResult["errors"] = [];
  const warnings: CSVParseResult["warnings"] = [];

  // Validate email
  if (!data.email) {
    errors.push({
      row: rowNumber,
      field: "email",
      message: "Email is required",
    });
  } else if (!isValidEmail(data.email)) {
    errors.push({
      row: rowNumber,
      field: "email",
      message: "Invalid email format",
    });
  }

  // Validate role
  if (!data.role) {
    errors.push({
      row: rowNumber,
      field: "role",
      message: "Role is required",
    });
  } else if (!VALID_ROLES.includes(data.role)) {
    errors.push({
      row: rowNumber,
      field: "role",
      message: `Invalid role. Must be one of: ${VALID_ROLES.join(", ")}`,
    });
  }

  // Warning for missing name
  if (!data.name) {
    warnings.push({
      row: rowNumber,
      field: "name",
      message: "Name is not provided - will use email as display name",
    });
  }

  return { errors, warnings };
}

/**
 * Simple email validation
 */
function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * Generates a sample CSV template
 */
export function generateCSVTemplate(
  customRoles?: Array<{ id: string; name: string }>
): string {
  const headers = ["email", "name", "role"];
  const sampleData = [
    ["<EMAIL>", "John Doe", "MEMBER"],
    ["<EMAIL>", "Jane Admin", "ADMIN"],
    ["<EMAIL>", "Custom User", "CUSTOM"],
  ];

  let csv = headers.join(",") + "\n";
  sampleData.forEach((row) => {
    csv +=
      row.map((cell) => (cell.includes(",") ? `"${cell}"` : cell)).join(",") +
      "\n";
  });

  // Add custom roles reference as comments if available
  if (customRoles && customRoles.length > 0) {
    csv += "\n# Available Custom Roles:\n";
    customRoles.forEach((role) => {
      csv += `# ${role.name}: ${role.id}\n`;
    });
  }

  return csv;
}
