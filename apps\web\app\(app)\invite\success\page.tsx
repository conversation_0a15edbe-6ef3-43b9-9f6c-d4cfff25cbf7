"use client";

import React, { useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  CheckCircle,
  Building,
  ArrowRight,
  Users,
  Sparkles,
} from "lucide-react";

export default function InviteSuccessPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const companyName = searchParams?.get("company") || "the company";
  const newToOrg = searchParams?.get("newToOrg") === "true";

  useEffect(() => {
    // Auto-redirect after 10 seconds
    const timer = setTimeout(() => {
      router.push("/dashboard");
    }, 10000);

    return () => clearTimeout(timer);
  }, [router]);

  return (
    <div
      className="min-h-screen flex items-center justify-center"
      style={{
        background: "linear-gradient(to bottom right, hsl(var(--primary) / 0.1), hsl(var(--accent) / 0.1))",
        backgroundColor: "hsl(var(--background))"
      }}
    >
      <div className="max-w-md w-full mx-4">
        <Card className="shadow-xl border-0">
          <CardContent className="pt-8 pb-8">
            <div className="text-center space-y-6">
              {/* Success Icon */}
              <div className="relative">
                <div
                  className="inline-flex items-center justify-center w-20 h-20 rounded-full"
                  style={{ backgroundColor: "hsl(var(--primary))" }}
                >
                  <CheckCircle
                    className="h-10 w-10"
                    style={{ color: "hsl(var(--primary-foreground))" }}
                  />
                </div>
                <div className="absolute -top-1 -right-1">
                  <Sparkles
                    className="h-6 w-6"
                    style={{ color: "hsl(var(--accent))" }}
                  />
                </div>
              </div>

              {/* Success Message */}
              <div className="space-y-2">
                <h1
                  className="text-2xl font-bold"
                  style={{ color: "hsl(var(--card-foreground))" }}
                >
                  Welcome to {companyName}!
                </h1>
                <p style={{ color: "hsl(var(--muted-foreground))" }}>
                  {newToOrg
                    ? "You've successfully joined the company and organization! You now have access to all shared resources and can collaborate with the team."
                    : "You've successfully joined the company and can now access all shared resources."}
                </p>
              </div>

              {/* Features */}
              <div
                className="space-y-3 text-left p-4 rounded-lg"
                style={{ backgroundColor: "hsl(var(--muted))" }}
              >
                <h3
                  className="font-semibold text-center mb-3"
                  style={{ color: "hsl(var(--card-foreground))" }}
                >
                  What you can do now:
                </h3>
                <div className="space-y-2">
                  <div className="flex items-center gap-3">
                    <Building
                      className="h-4 w-4"
                      style={{ color: "hsl(var(--primary))" }}
                    />
                    <span
                      className="text-sm"
                      style={{ color: "hsl(var(--card-foreground))" }}
                    >
                      Access company knowledge base
                    </span>
                  </div>
                  <div className="flex items-center gap-3">
                    <Users
                      className="h-4 w-4"
                      style={{ color: "hsl(var(--primary))" }}
                    />
                    <span
                      className="text-sm"
                      style={{ color: "hsl(var(--card-foreground))" }}
                    >
                      Collaborate with team members
                    </span>
                  </div>
                  <div className="flex items-center gap-3">
                    <Sparkles
                      className="h-4 w-4"
                      style={{ color: "hsl(var(--primary))" }}
                    />
                    <span
                      className="text-sm"
                      style={{ color: "hsl(var(--card-foreground))" }}
                    >
                      Use AI-powered tools and features
                    </span>
                  </div>
                  {newToOrg && (
                    <div className="flex items-center gap-3">
                      <CheckCircle
                        className="h-4 w-4"
                        style={{ color: "hsl(var(--accent))" }}
                      />
                      <span
                        className="text-sm"
                        style={{ color: "hsl(var(--card-foreground))" }}
                      >
                        Access to organization-wide resources
                      </span>
                    </div>
                  )}
                </div>
              </div>

              {/* Action Button */}
              <Button
                onClick={() => router.push("/dashboard")}
                className="w-full"
                size="lg"
              >
                Go to Dashboard
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>

              {/* Auto-redirect notice */}
              <p className="text-xs text-gray-500">
                You'll be automatically redirected to your dashboard in a few
                seconds
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
