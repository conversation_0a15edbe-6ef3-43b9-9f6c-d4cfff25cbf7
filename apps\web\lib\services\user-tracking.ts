import { NextRequest } from "next/server";
import crypto from "crypto";

export interface UserTrackingData {
  // Basic info
  ipAddress?: string;
  userAgent?: string;
  domain?: string;
  referrer?: string;

  // Geographic info
  country?: string;
  city?: string;
  region?: string;
  timezone?: string;

  // Device info
  deviceType?: string;
  operatingSystem?: string;
  browserName?: string;
  browserVersion?: string;
  screenWidth?: number;
  screenHeight?: number;
  viewportWidth?: number;
  viewportHeight?: number;

  // Network info
  connectionType?: string;
  isp?: string;
  organization?: string;

  // Page info
  pageUrl?: string;
  pageTitle?: string;
  language?: string;

  // UTM parameters
  utmSource?: string;
  utmMedium?: string;
  utmCampaign?: string;
  utmTerm?: string;
  utmContent?: string;

  // Behavior
  sessionDepth?: number;
  isReturning?: boolean;
  previousVisits?: number;

  // Additional metadata
  metadata?: Record<string, any>;
}

export interface GeolocationData {
  country?: string;
  city?: string;
  region?: string;
  timezone?: string;
  isp?: string;
  organization?: string;
  latitude?: number;
  longitude?: number;
}

// Helper function to hash IP address for privacy
export function hashIP(ip: string): string {
  const salt = process.env.IP_SALT || "default-salt";
  return crypto
    .createHash("sha256")
    .update(ip + salt)
    .digest("hex");
}

// Extract IP address from request
export function getClientIP(request: NextRequest): string {
  // Check various headers for the real IP
  const forwarded = request.headers.get("x-forwarded-for");
  const realIP = request.headers.get("x-real-ip");
  const cfConnectingIP = request.headers.get("cf-connecting-ip");

  console.log("IP headers:", {
    forwarded,
    realIP,
    cfConnectingIP,
    requestIP: request.ip,
  });

  if (forwarded) {
    const ip = forwarded.split(",")[0].trim();
    console.log("Using forwarded IP:", ip);
    return ip;
  }
  if (realIP) {
    console.log("Using real IP:", realIP);
    return realIP;
  }
  if (cfConnectingIP) {
    console.log("Using CF connecting IP:", cfConnectingIP);
    return cfConnectingIP;
  }

  // Fallback to connection remote address
  const fallbackIP = request.ip || "unknown";
  console.log("Using fallback IP:", fallbackIP);
  return fallbackIP;
}

// Parse user agent to extract device and browser info
export function parseUserAgent(userAgent: string): {
  deviceType: string;
  operatingSystem: string;
  browserName: string;
  browserVersion: string;
} {
  const ua = userAgent.toLowerCase();

  // Device type detection
  let deviceType = "desktop";
  if (/mobile|android|iphone|ipod|blackberry|iemobile|opera mini/i.test(ua)) {
    deviceType = "mobile";
  } else if (/tablet|ipad|playbook|silk/i.test(ua)) {
    deviceType = "tablet";
  }

  // Operating system detection
  let operatingSystem = "unknown";
  if (/windows nt/i.test(ua)) {
    operatingSystem = "Windows";
  } else if (/mac os x/i.test(ua)) {
    operatingSystem = "macOS";
  } else if (/linux/i.test(ua)) {
    operatingSystem = "Linux";
  } else if (/android/i.test(ua)) {
    operatingSystem = "Android";
  } else if (/iphone|ipad|ipod/i.test(ua)) {
    operatingSystem = "iOS";
  }

  // Browser detection
  let browserName = "unknown";
  let browserVersion = "unknown";

  if (/chrome/i.test(ua) && !/edge|edg/i.test(ua)) {
    browserName = "Chrome";
    const match = ua.match(/chrome\/([0-9.]+)/);
    browserVersion = match ? match[1] : "unknown";
  } else if (/firefox/i.test(ua)) {
    browserName = "Firefox";
    const match = ua.match(/firefox\/([0-9.]+)/);
    browserVersion = match ? match[1] : "unknown";
  } else if (/safari/i.test(ua) && !/chrome/i.test(ua)) {
    browserName = "Safari";
    const match = ua.match(/version\/([0-9.]+)/);
    browserVersion = match ? match[1] : "unknown";
  } else if (/edge|edg/i.test(ua)) {
    browserName = "Edge";
    const match = ua.match(/edge?\/([0-9.]+)/);
    browserVersion = match ? match[1] : "unknown";
  }

  return {
    deviceType,
    operatingSystem,
    browserName,
    browserVersion,
  };
}

// Get geolocation data from IP address
export async function getGeolocationData(ip: string): Promise<GeolocationData> {
  try {
    console.log("Getting geolocation for IP:", ip);

    // Skip geolocation for local/private IPs
    if (
      ip === "unknown" ||
      ip.startsWith("192.168.") ||
      ip.startsWith("10.") ||
      ip.startsWith("172.") ||
      ip === "127.0.0.1"
    ) {
      console.log("Skipping geolocation for local/private IP");
      return {};
    }

    // Use a free geolocation service (you can replace with your preferred service)
    const response = await fetch(
      `http://ip-api.com/json/${ip}?fields=status,country,regionName,city,timezone,isp,org,lat,lon`,
      {
        signal: AbortSignal.timeout(5000), // 5 second timeout
      }
    );

    if (!response.ok) {
      console.warn("Geolocation service unavailable, status:", response.status);
      return {};
    }

    const data = await response.json();
    console.log("Geolocation response:", data);

    if (data.status === "success") {
      return {
        country: data.country,
        city: data.city,
        region: data.regionName,
        timezone: data.timezone,
        isp: data.isp,
        organization: data.org,
        latitude: data.lat,
        longitude: data.lon,
      };
    } else {
      console.warn("Geolocation failed:", data);
    }
  } catch (error) {
    console.warn("Failed to get geolocation data:", error);
  }

  return {};
}

// Extract UTM parameters from URL
export function extractUTMParameters(url: string): {
  utmSource?: string;
  utmMedium?: string;
  utmCampaign?: string;
  utmTerm?: string;
  utmContent?: string;
} {
  try {
    const urlObj = new URL(url);
    const params = urlObj.searchParams;

    return {
      utmSource: params.get("utm_source") || undefined,
      utmMedium: params.get("utm_medium") || undefined,
      utmCampaign: params.get("utm_campaign") || undefined,
      utmTerm: params.get("utm_term") || undefined,
      utmContent: params.get("utm_content") || undefined,
    };
  } catch (error) {
    return {};
  }
}

// Main function to collect all user tracking data
export async function collectUserTrackingData(
  request: NextRequest,
  clientData?: Partial<UserTrackingData>
): Promise<UserTrackingData> {
  console.log("Starting user tracking data collection...");

  const ip = getClientIP(request);
  const hashedIP = hashIP(ip);
  const userAgent = request.headers.get("user-agent") || "unknown";
  const referrer = request.headers.get("referer") || undefined;
  const origin = request.headers.get("origin") || undefined;
  const acceptLanguage = request.headers.get("accept-language") || undefined;

  console.log("Basic request data:", {
    ip,
    hashedIP,
    userAgent,
    referrer,
    origin,
    acceptLanguage,
  });

  // Parse user agent
  const deviceInfo = parseUserAgent(userAgent);

  // Get geolocation data
  const geoData = await getGeolocationData(ip);

  // Extract language preference
  const language = acceptLanguage?.split(",")[0]?.split("-")[0] || undefined;

  // Extract UTM parameters from referrer if available
  const utmParams = referrer ? extractUTMParameters(referrer) : {};

  // Combine all data
  const trackingData: UserTrackingData = {
    // Basic info
    ipAddress: hashedIP,
    userAgent,
    domain: origin || clientData?.domain,
    referrer,

    // Geographic info
    country: geoData.country,
    city: geoData.city,
    region: geoData.region,
    timezone: geoData.timezone,

    // Device info
    deviceType: deviceInfo.deviceType,
    operatingSystem: deviceInfo.operatingSystem,
    browserName: deviceInfo.browserName,
    browserVersion: deviceInfo.browserVersion,

    // Network info
    isp: geoData.isp,
    organization: geoData.organization,

    // Page info
    language,

    // UTM parameters
    ...utmParams,

    // Merge client-provided data
    ...clientData,
  };

  console.log("Final tracking data:", trackingData);
  return trackingData;
}
