import { apiUrl, fetchJson } from "..";

export const createChatGroup = async (data, workspaceSlug = null) => {
  try {
    const headers = {
      "Content-Type": "application/json",
    };

    // Add workspace slug to headers if available
    if (workspaceSlug) {
      headers["x-workspace-slug"] = workspaceSlug;
    } else if (data.workspaceSlug) {
      headers["x-workspace-slug"] = data.workspaceSlug;
    }

    const response = await fetch(`${apiUrl}/chat/group`, {
      method: "POST",
      headers,
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error("Network response was not ok");
    }
    return await response.json();
  } catch (error) {
    console.log({ "Create ChatGroup api calling error": error });
    return { error: "Error Creating The ChatGroup" };
  }
};

export const updateChatGroup = async (data, workspaceSlug = null) => {
  try {
    const headers = {
      "Content-Type": "application/json",
    };

    // Add workspace slug to headers if available
    if (workspaceSlug) {
      headers["x-workspace-slug"] = workspaceSlug;
    } else if (data.workspaceSlug) {
      headers["x-workspace-slug"] = data.workspaceSlug;
    }

    const response = await fetch(`${apiUrl}/chat/group`, {
      method: "PUT",
      headers,
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error("Network response was not ok");
    }
    return await response.json();
  } catch (error) {
    console.log({ "update chat-groups api calling error": error });
    return { error: "Error Update The chat-groups" };
  }
};

export const getChatGroup = async ({
  userId,
  tenantId,
  workspaceSlug = null,
}) => {
  try {
    const response = await fetchJson(
      `${apiUrl}/chat/group?userId=${userId}&tenantId=${tenantId}`,
      userId,
      tenantId,
      workspaceSlug // Pass workspaceSlug as the fourth parameter for headers
    );
    return response;
  } catch (error) {
    console.error("Error in getChatGroup service:", error);
    throw error;
  }
};

export const deleteChatGroup = async (
  id,
  userId = null,
  tenantId = null,
  workspaceSlug = null
) => {
  try {
    const url = `${apiUrl}/chat/group?id=${id}`;
    const headers = {};

    // Add headers if available
    if (userId) {
      headers["x-user-id"] = userId;
    }

    if (tenantId) {
      headers["x-tenant-id"] = tenantId;
    }

    if (workspaceSlug) {
      headers["x-workspace-slug"] = workspaceSlug;
    }

    const response = await fetch(url, {
      method: "DELETE",
      headers,
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Network response was not ok");
    }
    return await response.json();
  } catch (error) {
    console.log({ "delete ChatGroup api error": error });
    return { error: error.message || "Error deleting ChatGroup" };
  }
};
