import { NextRequest, NextResponse } from "next/server";
import db from "@/lib/shared-db";
import crypto from "crypto";
import {
  collectUserTrackingData,
  UserTrackingData,
} from "@/lib/services/user-tracking";
import { checkUserBlockedInTenant } from "@/lib/services/user-blocking";

// Use shared utility function for user blocking checks
// This ensures consistency across all endpoints

interface SessionRequest {
  action: "create" | "end" | "feedback";
  sessionToken?: string;
  userTrackingData?: Partial<UserTrackingData>;
  metadata?: {
    domain?: string;
    userAgent?: string;
    referrer?: string;
    // Page information
    pageUrl?: string;
    pageTitle?: string;
    // Screen and viewport
    screenWidth?: number;
    screenHeight?: number;
    viewportWidth?: number;
    viewportHeight?: number;
    // Connection info
    connectionType?: string;
    // Behavior tracking
    sessionDepth?: number;
    isReturning?: boolean;
    previousVisits?: number;
    // UTM parameters
    utmSource?: string;
    utmMedium?: string;
    utmCampaign?: string;
    utmTerm?: string;
    utmContent?: string;
  };
  feedback?: {
    rating?: number; // 1-5
    comment?: string;
  };
}

interface SessionResponse {
  sessionToken?: string;
  status: "created" | "ended" | "updated";
  error?: string;
}

// Helper function to hash IP address for privacy
function hashIP(ip: string): string {
  return crypto
    .createHash("sha256")
    .update(ip + process.env.IP_SALT || "default-salt")
    .digest("hex");
}

// Helper function to get client IP
function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get("x-forwarded-for");
  const realIP = request.headers.get("x-real-ip");

  if (forwarded) {
    return forwarded.split(",")[0].trim();
  }

  if (realIP) {
    return realIP;
  }

  return "unknown";
}

// Validate domain access
async function validateDomainAccess(
  chatbot: any,
  origin: string
): Promise<boolean> {
  if (!chatbot.allowedDomains || chatbot.allowedDomains.length === 0) {
    return true;
  }

  try {
    const originDomain = new URL(origin).hostname;
    return chatbot.allowedDomains.some((domain: string) => {
      if (domain.startsWith("*.")) {
        const baseDomain = domain.slice(2);
        return originDomain.endsWith(baseDomain);
      }
      return originDomain === domain;
    });
  } catch {
    return false;
  }
}

// Handle CORS preflight requests
export async function OPTIONS(request: NextRequest) {
  const origin = request.headers.get("origin");

  return new NextResponse(null, {
    status: 200,
    headers: {
      "Access-Control-Allow-Origin": origin || "*",
      "Access-Control-Allow-Methods": "POST, OPTIONS",
      "Access-Control-Allow-Headers": "Content-Type, Authorization",
      "Access-Control-Max-Age": "86400",
    },
  });
}

export async function POST(
  request: NextRequest,
  { params }: { params: { chatbotId: string } }
) {
  try {
    const { chatbotId } = params;
    const body: SessionRequest = await request.json();
    const { action, sessionToken, userTrackingData, metadata, feedback } = body;

    // Validate required fields
    if (!action || !chatbotId) {
      return NextResponse.json(
        { error: "Missing required fields: action, chatbotId" },
        { status: 400 }
      );
    }

    // Get API key from Authorization header
    const authHeader = request.headers.get("authorization");
    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return NextResponse.json(
        { error: "Missing or invalid API key" },
        { status: 401 }
      );
    }

    const apiKey = authHeader.substring(7);

    // Find chatbot by API key
    const chatbot = await db.chatbot.findFirst({
      where: {
        apiKey: apiKey,
        id: chatbotId,
        isActive: true,
      },
    });

    if (!chatbot) {
      return NextResponse.json(
        { error: "Invalid API key or chatbot not found" },
        { status: 404 }
      );
    }

    // Get client IP and hash it
    const clientIP =
      request.headers.get("x-forwarded-for")?.split(",")[0].trim() ||
      request.headers.get("x-real-ip") ||
      "unknown";
    const hashedIP = hashIP(clientIP);

    // Check if user is blocked before proceeding
    const isUserBlocked = await checkUserBlockedInTenant(
      chatbot.tenantId,
      undefined, // No user ID in session creation
      undefined, // No email in session creation
      hashedIP
    );

    if (isUserBlocked.blocked) {
      return NextResponse.json(
        {
          error: "Access denied",
          message:
            isUserBlocked.reason ||
            "You have been blocked from using this chatbot",
          blockInfo: {
            type: isUserBlocked.blockType,
            expiresAt: isUserBlocked.expiresAt,
          },
        },
        { status: 403 }
      );
    }

    let response: SessionResponse;

    switch (action) {
      case "create":
        // Create new session with comprehensive user tracking
        const newSessionToken = crypto.randomUUID();

        // Collect comprehensive user tracking data
        const trackingData = await collectUserTrackingData(request, {
          ...userTrackingData,
          ...metadata,
        });

        const newSession = await db.chatbotSession.create({
          data: {
            sessionToken: newSessionToken,
            chatbotId: chatbot.id,
            tenantId: chatbot.tenantId, // Add tenantId for multi-tenant isolation

            // Basic session metadata
            domain:
              trackingData.domain || request.headers.get("origin") || "unknown",
            userAgent:
              trackingData.userAgent ||
              request.headers.get("user-agent") ||
              "unknown",
            ipAddress: trackingData.ipAddress || hashedIP,
            referrer: trackingData.referrer,

            // Geographic information
            country: trackingData.country,
            city: trackingData.city,
            region: trackingData.region,
            timezone: trackingData.timezone,

            // Device and browser information
            deviceType: trackingData.deviceType,
            operatingSystem: trackingData.operatingSystem,
            browserName: trackingData.browserName,
            browserVersion: trackingData.browserVersion,
            screenWidth: trackingData.screenWidth,
            screenHeight: trackingData.screenHeight,
            viewportWidth: trackingData.viewportWidth,
            viewportHeight: trackingData.viewportHeight,

            // Network information
            connectionType: trackingData.connectionType,
            isp: trackingData.isp,
            organization: trackingData.organization,

            // Page and language information
            pageUrl: trackingData.pageUrl,
            pageTitle: trackingData.pageTitle,
            language: trackingData.language,

            // UTM parameters
            utmSource: trackingData.utmSource,
            utmMedium: trackingData.utmMedium,
            utmCampaign: trackingData.utmCampaign,
            utmTerm: trackingData.utmTerm,
            utmContent: trackingData.utmContent,

            // User behavior tracking
            sessionDepth: trackingData.sessionDepth || 1,
            isReturning: trackingData.isReturning || false,
            previousVisits: trackingData.previousVisits || 0,

            // Session tracking
            messagesCount: 0,
            startedAt: new Date(),
            lastActivityAt: new Date(),

            // Additional metadata
            metadata: trackingData.metadata,
          },
        });

        response = {
          sessionToken: newSessionToken,
          status: "created",
        };
        break;

      case "end":
        if (!sessionToken) {
          return NextResponse.json(
            { error: "Session token required for end action" },
            { status: 400 }
          );
        }

        const sessionToEnd = await db.chatbotSession.findFirst({
          where: {
            sessionToken,
            tenantId: chatbot.tenantId, // Ensure tenant isolation
          },
        });

        if (sessionToEnd && sessionToEnd.chatbotId === chatbot.id) {
          const duration = sessionToEnd.startedAt
            ? Math.floor(
                (new Date().getTime() - sessionToEnd.startedAt.getTime()) / 1000
              )
            : 0;

          await db.chatbotSession.update({
            where: { sessionToken },
            data: {
              endedAt: new Date(),
              duration: duration,
            },
          });
        }

        response = {
          status: "ended",
        };
        break;

      case "feedback":
        if (!sessionToken) {
          return NextResponse.json(
            { error: "Session token required for feedback action" },
            { status: 400 }
          );
        }

        const sessionForFeedback = await db.chatbotSession.findUnique({
          where: { sessionToken },
        });

        if (sessionForFeedback && sessionForFeedback.chatbotId === chatbot.id) {
          await db.chatbotSession.update({
            where: { sessionToken },
            data: {
              satisfactionRating: feedback?.rating,
              feedback: feedback?.comment,
              lastActivityAt: new Date(),
            },
          });
        }

        response = {
          status: "updated",
        };
        break;

      default:
        return NextResponse.json({ error: "Invalid action" }, { status: 400 });
    }

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error handling session request:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
