import ThemeConfigPage from "@/components/wrapper-screens/organization-settings/theme-config/theme-config-page";
import { AdminOnlyGuard } from "@/components/auth/role-guard";
import { authOptions } from "@/lib/next-auth";
import { getServerSession } from "next-auth";
import { cookies } from "next/headers";
import { redirect } from "next/navigation";

export default async function Page() {
  const isPartnerConsole = process.env.NEXT_PUBLIC_IS_PARTNER_CONSOLE === "1";

  // Redirect if not partner console
  if (!isPartnerConsole) {
    return redirect("/settings");
  }

  const tenantId = cookies().get("currentOrganizationId")?.value;
  const session: any = await getServerSession(authOptions);

  if (!tenantId) {
    return redirect("/sign-in");
  }

  // Check if user is authenticated
  if (!session?.user) {
    return redirect("/sign-in");
  }

  try {
    const memberships = (session as any)?.memberships || [];
    const membership = memberships.find((m) => m.tenant.id === tenantId);
    const userRole = membership?.role;

    // Check if user has admin/owner role for this tenant
    if (!userRole || (userRole !== "ADMIN" && userRole !== "OWNER")) {
      return (
        <AdminOnlyGuard tenantId={tenantId}>
          <div />
        </AdminOnlyGuard>
      );
    }

    return (
      <AdminOnlyGuard tenantId={tenantId}>
        <ThemeConfigPage
          organization={membership ? membership.tenant : null}
          userRole={userRole}
        />
      </AdminOnlyGuard>
    );
  } catch (error) {
    console.error("Error loading theme configuration:", error);
    return redirect("/settings");
  }
}
