"use client";

import React, {
  createContext,
  useContext,
  useEffect,
  useState,
  ReactNode,
} from "react";
import { ThemeConfig } from "@/types/theme-config";
import { getCachedThemeConfig, updateThemeCache } from "@/lib/theme-cache";

interface ThemeConfigContextType {
  themeConfig: ThemeConfig | null;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
  updateConfig: (config: ThemeConfig) => void;
  resetConfig: () => void;
}

const ThemeConfigContext = createContext<ThemeConfigContextType | undefined>(
  undefined,
);

interface ThemeConfigProviderProps {
  children: ReactNode;
  tenantId?: string;
  initialConfig?: ThemeConfig;
}

export function ThemeConfigProvider({
  children,
  tenantId,
  initialConfig,
}: ThemeConfigProviderProps) {
  const [themeConfig, setThemeConfig] = useState<ThemeConfig | null>(
    initialConfig || null,
  );
  const [loading, setLoading] = useState(!initialConfig);
  const [error, setError] = useState<string | null>(null);

  const isPartnerConsole = process.env.NEXT_PUBLIC_IS_PARTNER_CONSOLE === "1";

  const fetchThemeConfig = async () => {
    if (!tenantId || !isPartnerConsole) {
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);
      const config = await getCachedThemeConfig(tenantId);
      setThemeConfig(config);
    } catch (err) {
      setError(
        err instanceof Error
          ? err.message
          : "Failed to load theme configuration",
      );
      console.error("Error fetching theme config:", err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (tenantId && !initialConfig && isPartnerConsole) {
      fetchThemeConfig();
    }
  }, [tenantId, initialConfig, isPartnerConsole]);

  // Listen for real-time theme updates
  useEffect(() => {
    if (!isPartnerConsole) return;

    const handleRealtimeThemeUpdate = (event: CustomEvent) => {
      const themeData = event.detail;
      console.log("Theme config context received real-time update:", themeData);

      if (themeData?.themeConfig && tenantId) {
        setThemeConfig(themeData.themeConfig);

        // Update cache with new theme config
        updateThemeCache(tenantId, themeData.themeConfig);

        // Sync with localStorage for persistence
        if (themeData.themeConfig.themePreset) {
          localStorage.setItem("theme", themeData.themeConfig.themePreset);
        }
      }
    };

    window.addEventListener("realtimeThemeUpdate", handleRealtimeThemeUpdate as EventListener);

    return () => {
      window.removeEventListener("realtimeThemeUpdate", handleRealtimeThemeUpdate as EventListener);
    };
  }, [isPartnerConsole]);

  const updateConfig = (config: ThemeConfig) => {
    if (isPartnerConsole && tenantId) {
      setThemeConfig(config);

      // Update cache with new theme config
      updateThemeCache(tenantId, config);

      // Sync with localStorage for persistence
      if (config.themePreset) {
        localStorage.setItem("theme", config.themePreset);
      }
    }
  };

  const resetConfig = () => {
    if (isPartnerConsole) {
      setThemeConfig(null);
      localStorage.removeItem("theme");
    }
  };

  const refetch = async () => {
    await fetchThemeConfig();
  };

  const value: ThemeConfigContextType = {
    themeConfig: isPartnerConsole ? themeConfig : null,
    loading,
    error,
    refetch,
    updateConfig,
    resetConfig,
  };

  return (
    <ThemeConfigContext.Provider value={value}>
      {children}
    </ThemeConfigContext.Provider>
  );
}

export function useThemeConfig() {
  const context = useContext(ThemeConfigContext);
  if (context === undefined) {
    throw new Error("useThemeConfig must be used within a ThemeConfigProvider");
  }
  return context;
}

export function useThemeConfigOptional() {
  const context = useContext(ThemeConfigContext);
  return context;
}

// Helper hook to get the current brand name
export function useBrandName() {
  const context = useThemeConfigOptional();
  const isPartnerConsole = process.env.NEXT_PUBLIC_IS_PARTNER_CONSOLE === "1";

  // If no context is available, return default
  if (!context) {
    return "Swiss Knowledge Hub";
  }

  const { themeConfig } = context;
  return isPartnerConsole
    ? themeConfig?.brandName || "Swiss Knowledge Hub"
    : "Swiss Knowledge Hub";
}

// Helper hook to get the current logo URLs
export function useLogos() {
  const { themeConfig } = useThemeConfig();
  const isPartnerConsole = process.env.NEXT_PUBLIC_IS_PARTNER_CONSOLE === "1";
  return {
    logoUrl: isPartnerConsole ? themeConfig?.logoUrl : undefined,
    faviconUrl: isPartnerConsole ? themeConfig?.faviconUrl : undefined,
  };
}

// Helper hook to get theme colors based on current theme mode
export function useThemeColors(currentTheme?: "light" | "dark") {
  const { themeConfig } = useThemeConfig();
  const isPartnerConsole = process.env.NEXT_PUBLIC_IS_PARTNER_CONSOLE === "1";

  if (!isPartnerConsole) {
    return {
      primaryColor: undefined,
      secondaryColor: undefined,
      accentColor: undefined,
      navigationBackgroundColor: undefined,
      contentBackgroundColor: undefined,
      foregroundColor: undefined,
    };
  }

  // Helper function to get the appropriate color based on theme mode
  const getColorValue = (lightColor?: string, darkColor?: string, legacyColor?: string): string | undefined => {
    // If we have theme-specific colors, use them
    if (currentTheme === "light" && lightColor) return lightColor;
    if (currentTheme === "dark" && darkColor) return darkColor;

    // Fallback to legacy color if available
    if (legacyColor) return legacyColor;

    // If no current theme specified, prefer light colors, then dark, then legacy
    return lightColor || darkColor || legacyColor;
  };

  return {
    primaryColor: getColorValue(themeConfig?.lightPrimaryColor, themeConfig?.darkPrimaryColor, themeConfig?.primaryColor),
    secondaryColor: getColorValue(themeConfig?.lightSecondaryColor, themeConfig?.darkSecondaryColor, themeConfig?.secondaryColor),
    accentColor: getColorValue(themeConfig?.lightAccentColor, themeConfig?.darkAccentColor, themeConfig?.accentColor),
    navigationBackgroundColor: getColorValue(
      themeConfig?.lightNavigationBackgroundColor,
      themeConfig?.darkNavigationBackgroundColor,
    ),
    contentBackgroundColor: getColorValue(
      themeConfig?.lightContentBackgroundColor,
      themeConfig?.darkContentBackgroundColor,
    ),
    foregroundColor: getColorValue(themeConfig?.lightForegroundColor, themeConfig?.darkForegroundColor, themeConfig?.foregroundColor),
  };
}

// Helper hook to determine if white-label customization is active
export function useIsWhiteLabeled() {
  const { themeConfig } = useThemeConfig();
  const isPartnerConsole = process.env.NEXT_PUBLIC_IS_PARTNER_CONSOLE === "1";
  return Boolean(
    isPartnerConsole &&
      (themeConfig?.brandName ||
        themeConfig?.logoUrl ||
        // Light theme color fields
        themeConfig?.lightPrimaryColor ||
        themeConfig?.lightSecondaryColor ||
        themeConfig?.lightAccentColor ||
        themeConfig?.lightNavigationBackgroundColor ||
        themeConfig?.lightContentBackgroundColor ||
        themeConfig?.lightForegroundColor ||
        // Dark theme color fields
        themeConfig?.darkPrimaryColor ||
        themeConfig?.darkSecondaryColor ||
        themeConfig?.darkAccentColor ||
        themeConfig?.darkNavigationBackgroundColor ||
        themeConfig?.darkContentBackgroundColor ||
        themeConfig?.darkForegroundColor ||
        (themeConfig?.themePreset && themeConfig.themePreset !== "light")),
  );
}
