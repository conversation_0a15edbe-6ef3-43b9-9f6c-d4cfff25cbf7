"use client";

import { DeleteConfirmationDialog } from "./delete-confirmation-dialog";
import { useLanguage } from "@/lib/language-context";

interface FolderDeleteConfirmationProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  folderName: string;
  itemCount?: number;
  isLoading?: boolean;
}

export function FolderDeleteConfirmation({
  isOpen,
  onClose,
  onConfirm,
  folderName,
  itemCount = 0,
  isLoading = false,
}: FolderDeleteConfirmationProps) {
  const { t } = useLanguage();

  return (
    <DeleteConfirmationDialog
      isOpen={isOpen}
      onClose={onClose}
      onConfirm={onConfirm}
      title={t("workspace.deleteFolderConfirm")}
      description={t("workspace.deleteFolderWarning", { 
        name: folderName, 
        count: itemCount 
      })}
      isLoading={isLoading}
      loadingText={t("workspace.deletingFolder")}
      destructiveActionText={t("common.delete")}
    />
  );
}
