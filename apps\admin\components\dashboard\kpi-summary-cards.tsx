"use client";

import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Building,
  Users,
  DollarSign,
  Server,
  TrendingUp,
  AlertTriangle,
  ArrowUpRight,
  ArrowDownRight,
  ExternalLink
} from "lucide-react";
import { cn } from "@/lib/utils";
import { useRouter } from "next/navigation";


interface KpiMetrics {
  activeTenants: number;
  activeUsers: number;
  mrr: number;
  infrastructureCost: number;
  grossMargin: number;
  alertsCount: number;
  trends: {
    activeTenants: number;
    activeUsers: number;
    mrr: number;
    infrastructureCost: number;
    grossMargin: number;
  };
}

interface KpiSummaryCardsProps {
  data?: KpiMetrics;
  loading?: boolean;
}

interface KpiCardProps {
  title: string;
  value: string;
  icon: React.ElementType;
  trend?: {
    value: number;
    isPositive: boolean;
  };
  color: "blue" | "green" | "purple" | "orange" | "red" | "indigo";
  badge?: {
    count: number;
    variant: "default" | "destructive" | "outline" | "secondary";
  };
  onClick?: () => void;
  clickable?: boolean;
}

function KpiCard({ title, value, icon: Icon, trend, color, badge, onClick, clickable }: KpiCardProps) {
  const colorStyles = {
    blue: {
      iconBg: "bg-blue-100 dark:bg-blue-950",
      iconColor: "text-blue-600 dark:text-blue-400",
      border: "border-blue-200 dark:border-blue-800",
    },
    green: {
      iconBg: "bg-green-100 dark:bg-green-950",
      iconColor: "text-green-600 dark:text-green-400",
      border: "border-green-200 dark:border-green-800",
    },
    purple: {
      iconBg: "bg-purple-100 dark:bg-purple-950",
      iconColor: "text-purple-600 dark:text-purple-400",
      border: "border-purple-200 dark:border-purple-800",
    },
    orange: {
      iconBg: "bg-orange-100 dark:bg-orange-950",
      iconColor: "text-orange-600 dark:text-orange-400",
      border: "border-orange-200 dark:border-orange-800",
    },
    red: {
      iconBg: "bg-red-100 dark:bg-red-950",
      iconColor: "text-red-600 dark:text-red-400",
      border: "border-red-200 dark:border-red-800",
    },
    indigo: {
      iconBg: "bg-indigo-100 dark:bg-indigo-950",
      iconColor: "text-indigo-600 dark:text-indigo-400",
      border: "border-indigo-200 dark:border-indigo-800",
    },
  };

  return (
    <Card
      className={cn(
        "transition-all duration-200",
        colorStyles[color].border,
        clickable && "cursor-pointer hover:bg-muted/50 hover:shadow-md"
      )}
      onClick={clickable ? onClick : undefined}
    >
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium text-muted-foreground flex items-center gap-1">
          {title}
          {clickable && (
            <ExternalLink className="h-3 w-3 opacity-50" />
          )}
        </CardTitle>
        <div className="flex items-center space-x-2">
          {badge && (
            <Badge variant={badge.variant} className="text-xs">
              {badge.count}
            </Badge>
          )}
          <div className={cn("p-2 rounded-full", colorStyles[color].iconBg)}>
            <Icon className={cn("h-4 w-4", colorStyles[color].iconColor)} />
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="flex items-center justify-between">
          <div>
            <div className="text-2xl font-bold">{value}</div>
            {trend && (
              <div className={cn(
                "flex items-center text-xs",
                trend.isPositive ? "text-green-600" : "text-red-600"
              )}>
                {trend.isPositive ? (
                  <ArrowUpRight className="h-3 w-3 mr-1" />
                ) : (
                  <ArrowDownRight className="h-3 w-3 mr-1" />
                )}
                {Math.abs(trend.value).toFixed(1)}%
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

export function KpiSummaryCards({ data, loading }: KpiSummaryCardsProps) {
  const router = useRouter();

  // Navigation handlers for clickable KPI cards
  const handleActiveTenantsClick = () => {
    router.push("/tenants?filter=active");
  };

  const handleAlertsClick = () => {
    // Scroll to Alert Summary section on the same page
    const alertSection = document.getElementById('alert-summary-section');
    if (alertSection) {
      alertSection.scrollIntoView({
        behavior: 'smooth',
        block: 'start'
      });
    }
  };

  const handleFinancialOverviewClick = () => {
    // Scroll to Financial Overview section on the same page
    const financialSection = document.getElementById('financial-overview-section');
    if (financialSection) {
      financialSection.scrollIntoView({
        behavior: 'smooth',
        block: 'start'
      });
    }
  };

  if (loading || !data) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6">
        {Array.from({ length: 6 }).map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <div className="h-4 bg-gray-200 rounded w-20"></div>
              <div className="h-8 w-8 bg-gray-200 rounded-full"></div>
            </CardHeader>
            <CardContent>
              <div className="h-8 bg-gray-200 rounded w-16 mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-12"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('de-CH', {
      style: 'currency',
      currency: 'CHF',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  const formatNumber = (value: number) => {
    return new Intl.NumberFormat('en-US').format(value);
  };

  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`;
  };

  const kpiCards: KpiCardProps[] = [
    {
      title: "Active Tenants",
      value: formatNumber(data.activeTenants),
      icon: Building,
      color: "blue",
      trend: {
        value: data.trends.activeTenants,
        isPositive: data.trends.activeTenants > 0,
      },
      clickable: true,
      onClick: handleActiveTenantsClick,
    },
    {
      title: "Active Users",
      value: formatNumber(data.activeUsers),
      icon: Users,
      color: "green",
      trend: {
        value: data.trends.activeUsers,
        isPositive: data.trends.activeUsers > 0,
      },
    },
    {
      title: "Monthly Recurring Revenue",
      value: formatCurrency(data.mrr),
      icon: DollarSign,
      color: "purple",
      trend: {
        value: data.trends.mrr,
        isPositive: data.trends.mrr > 0,
      },
      clickable: true,
      onClick: handleFinancialOverviewClick,
    },
    {
      title: "Infrastructure Cost",
      value: data.infrastructureCost.toFixed(3).toString(),
      icon: Server,
      color: "orange",
      trend: {
        value: data.trends.infrastructureCost,
        isPositive: data.trends.infrastructureCost < 0, // Lower cost is positive
      },
      clickable: true,
      onClick: handleFinancialOverviewClick,
    },
    {
      title: "Gross Margin",
      value: formatPercentage(data.grossMargin),
      icon: TrendingUp,
      color: data.grossMargin > 0 ? "green" : "red",
      trend: {
        value: data.trends.grossMargin,
        isPositive: data.trends.grossMargin > 0,
      },
      clickable: true,
      onClick: handleFinancialOverviewClick,
    },
    {
      title: "Alerts",
      value: formatNumber(data.alertsCount),
      icon: AlertTriangle,
      color: data.alertsCount > 0 ? "red" : "green",
      badge: data.alertsCount > 0 ? {
        count: data.alertsCount,
        variant: "destructive" as const,
      } : undefined,
      clickable: true,
      onClick: handleAlertsClick,
    },
  ];

  return (
    <div>
      <h2 className="text-lg font-semibold mb-4">Key Performance Indicators</h2>
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
        {kpiCards.map((card, index) => (
          <KpiCard key={index} {...card} />
        ))}
      </div>
    </div>
  );
}
