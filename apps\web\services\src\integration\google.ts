import toast from "react-hot-toast";

const CLIENT_ID = process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID || "";
const OAUTH_URL = "https://accounts.google.com/o/oauth2/auth";
const APP_URL = process.env.NEXT_PUBLIC_API_BASE_URL || "";

export const googleLogin = async ({ tenantId, userId }) => {
  const redirectUrl = `${APP_URL}/api/integration/google/callback`;
  const options = {
    client_id: CLIENT_ID,
    redirect_uri: redirectUrl,
    response_type: "code",
    scope: "https://www.googleapis.com/auth/drive",
    prompt: "consent",
    access_type: "offline",
    state: JSON.stringify({ tenantId, userId }),
  };

  const searchParams = new URLSearchParams(options).toString();
  window.open(`${OAUTH_URL}?${searchParams}`, "_blank");
};

interface CreateFolderParams {
  name: string;
  parentId?: string;
  tenantId: string;
  userId: string;
}

export interface ListFoldersParams {
  userId: string;
  tenantId: string;
  parentId?: string;
}

export interface ListFoldersResult {
  success: boolean;
  data?: Array<{
    id: string;
    name: string;
    mimeType: string;
  }>;
  error?: string;
}

interface UploadFileParams {
  file: File;
  tenantId: string;
  userId: string;
  slug: string;
}

interface DeleteItemParams {
  tenantId: string;
  fileId: string;
}

export interface SyncResult {
  synced: boolean;
  syncedFiles?: number;
  error?: string;
  folderId?: string;
  localFolderId?: string;
}

export interface SyncOptions {
  folderId: string;
  tenantId: string;
  userId: string;
  workspaceSlug: string;
  pageId?: string;
  recursive?: boolean;
  checkSubFiles?: boolean;
  parentFolderId?: string; // Added to track parent folder ID
}

export const googleDriveService = {
  createFolder: async ({
    name,
    parentId,
    tenantId,
    userId,
  }: CreateFolderParams) => {
    const response = await fetch(`${APP_URL}/api/integration/google/drive`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ name, parentId, tenantId, userId }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to create folder");
    }

    return await response.json();
  },

  uploadFile: async ({ file, tenantId, slug, userId }: UploadFileParams) => {
    const formData = new FormData();
    formData.append("file", file);
    formData.append("tenantId", tenantId);
    formData.append("slug", slug);
    formData.append("userId", userId);

    const response = await fetch(`${APP_URL}/api/integration/google/drive`, {
      method: "PUT",
      body: formData,
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to upload file");
    }

    return await response.json();
  },

  deleteItem: async ({ tenantId, fileId }: DeleteItemParams) => {
    const params = new URLSearchParams({ tenantId, fileId });
    const response = await fetch(
      `${APP_URL}/api/integration/google/drive?${params}`,
      {
        method: "DELETE",
      }
    );

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to delete item");
    }

    return await response.json();
  },
  async listFolders({
    tenantId,
    userId,
    parentId = "root",
  }: ListFoldersParams): Promise<ListFoldersResult> {
    try {
      const headers = {
        "Content-Type": "application/json",
      };

      // Add headers if available
      if (userId) {
        headers["x-user-id"] = userId;
      }

      if (tenantId) {
        headers["x-tenant-id"] = tenantId;
      }
      const response = await fetch(
        `${APP_URL}/api/integration/google/drive?tenantId=${encodeURIComponent(
          tenantId
        )}&parentId=${encodeURIComponent(parentId)}&userId=${encodeURIComponent(
          userId
        )}`,
        {
          method: "GET",
          headers,
        }
      );

      const data = await response.json();

      if (!response.ok) {
        toast.error(data.error || "Failed to list Google Drive folders");
        throw new Error(data.error || "Failed to list Google Drive folders");
      }

      return {
        success: true,
        data:
          data.data?.filter(
            (item) => item.mimeType === "application/vnd.google-apps.folder"
          ) || [],
      };
    } catch (error: any) {
      console.error("Google Drive list folders error:", error);
      return {
        success: false,
        error: error.message || "Failed to list Google Drive folders",
      };
    }
  },

  async checkAndSyncFolder(options: SyncOptions): Promise<SyncResult> {
    const {
      folderId,
      tenantId,
      workspaceSlug,
      pageId,
      recursive = true,
      checkSubFiles = true,
      parentFolderId,
    } = options;

    try {
      // Make API call to check and sync folder
      const headers = {
        "Content-Type": "application/json",
        "x-user-id": options.userId,
        "x-tenant-id": tenantId,
        "x-workspace-slug": workspaceSlug,
      };

      const response = await fetch(`${APP_URL}/api/integration/google/sync`, {
        method: "POST",
        headers,
        body: JSON.stringify({
          folderId,
          tenantId,
          pageId,
          workspaceSlug,
          recursive,
          checkSubFiles,
          parentFolderId,
          slug: workspaceSlug, // Ensure slug is passed for indexing
        }),
      });

      const data = await response.json();

      // Update toast based on result
      if (!response.ok) {
        toast.remove();
        toast.error(data.error || "Failed to sync with Google Drive");
        throw new Error(data.error || "Failed to sync with Google Drive");
      }

      return {
        synced: data.synced,
        syncedFiles: data.syncedFiles,
        error: data.error,
        folderId: data.folderId,
        localFolderId: data.localFolderId,
      };
    } catch (error: any) {
      console.error("Google Drive sync error:", error);
      toast.error(error.message || "Failed to sync with Google Drive");
      return {
        synced: false,
        error: error.message || "Failed to sync with Google Drive",
      };
    }
  },
};
