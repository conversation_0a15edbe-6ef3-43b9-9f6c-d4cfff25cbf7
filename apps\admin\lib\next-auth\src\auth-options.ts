import { PrismaAdapter } from "@next-auth/prisma-adapter";
import { type NextAuthOptions } from "next-auth";
import CredentialsProvider from "next-auth/providers/credentials";
import { compare } from "bcrypt";
import db from "@/lib/shared-db";
import crypto from "crypto";

export const authOptions: NextAuthOptions = {
  adapter: PrismaAdapter(db),
  pages: {
    signIn: "/auth/login",
  },
  secret: process.env.NEXTAUTH_SECRET,
  session: { strategy: "jwt" },
  providers: [
    CredentialsProvider({
      name: "Credentials",
      credentials: {
        email: { label: "Username", type: "text", placeholder: "jsmith" },
        password: { label: "Password", type: "password" },
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          throw new Error("Username and password are required.");
        }

        try {
          // Find user by email hash (since email is encrypted)
          const emailHash = crypto
            .createHash("sha256")
            .update(credentials.email)
            .digest("hex");
          const user = await db.user.findUnique({
            where: { emailHash },
            include: {
              membership: {
                include: {
                  tenant: true,
                },
              },
            },
          });

          if (!user) {
            throw new Error("User not found. Please check your email.");
          }

          // Check if user has admin privileges (ADMIN or OWNER role)
          const hasAdminAccess = user.membership.some(
            (membership: any) => membership.role === "ADMIN" || membership.role === "OWNER"
          );

          if (!hasAdminAccess) {
            throw new Error("Access denied. Admin privileges required.");
          }

          const passwordCorrect = await compare(
            credentials.password,
            user.password || ""
          );

          if (!passwordCorrect) {
            throw new Error("Invalid password. Please try again.");
          }

          return {
            id: user.id,
            email: user.email,
            name: user.name,
          }; // Success
        } catch (error) {
          console.error("Authorization Error:", error);
          throw new Error(error.message || "Internal Server Error");
        }
      },
    }),
  ],
  logger: {
    error(code, metadata) {
      console.error(code, metadata);
    },
    warn(code) {
      console.warn(code);
    },
    debug(code, metadata) {
      console.debug(code, metadata);
    },
  },
  cookies: {
    sessionToken: {
      name: `${
        process.env.NODE_ENV === "production" ? "__Secure-" : ""
      }next-auth.session-token`,
      options: {
        httpOnly: true,
        sameSite: "lax",
        path: "/",
        secure: process.env.NODE_ENV === "production",
      },
    },
  },
  callbacks: {
    session: async ({ session, token }) => {
      if (session?.user?.email && token?.email) {
        const emailHash = crypto
          .createHash("sha256")
          .update(session?.user?.email)
          .digest("hex");
        const user = await db.user.findUnique({
          where: {
            emailHash,
          },
          include: {
            membership: {
              include: {
                tenant: true,
              },
            },
          },
        });

        if (user) {
          // Add admin-specific data to session
          (session.user as any).id = user.id;
          (session.user as any).memberships = user.membership;
        }
      }

      return { ...session, ...token };
    },

    async signIn({ user }) {
      // If user object exists, authentication was successful in authorize()
      // The authorize() function already checked admin privileges and memberships
      return !!user;
    },

    async jwt({ token }) {
      if (token?.email) {
        const emailHash = crypto
          .createHash("sha256")
          .update(token?.email)
          .digest("hex");
        const user = await db.user.findUnique({
          where: {
            emailHash,
          },
          select: {
            id: true,
          },
        });

        if (user?.id) {
          token.userId = user.id;
        }
      }
      return token;
    },
  },
};
