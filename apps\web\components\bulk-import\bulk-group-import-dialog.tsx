"use client";

import React, { useState, useCallback } from "react";
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  Di<PERSON>Header,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Upload, Users, ArrowRight, ArrowLeft } from "lucide-react";
import toast from "react-hot-toast";
import { useLanguage } from "@/lib/language-context";
import { GroupCSVUploadZone } from "./group-csv-upload-zone";
import { GroupMemberPreviewTable } from "./group-member-preview-table";
import { ImportProgressComponent, ImportProgress } from "./import-progress";
import { parseGroupCSV, GroupCSVParseResult } from "@/lib/utils/group-csv-parser";

interface BulkGroupImportDialogProps {
  tenantId: string;
  onImportComplete?: () => void;
  trigger?: React.ReactNode;
}

type ImportStep = 'upload' | 'preview' | 'importing' | 'complete';

export function BulkGroupImportDialog({
  tenantId,
  onImportComplete,
  trigger
}: BulkGroupImportDialogProps) {
  const { t } = useLanguage();
  const [open, setOpen] = useState(false);
  const [currentStep, setCurrentStep] = useState<ImportStep>('upload');
  const [parseResult, setParseResult] = useState<GroupCSVParseResult | null>(null);
  const [importProgress, setImportProgress] = useState<ImportProgress | null>(null);
  const [uploadError, setUploadError] = useState<string>('');

  const handleFileSelect = useCallback(async (file: File) => {
    setUploadError('');
    
    try {
      const content = await file.text();
      const result = parseGroupCSV(content);
      
      setParseResult(result);
      
      if (result.errors.length > 0) {
        setUploadError(`Found ${result.errors.length} error${result.errors.length !== 1 ? 's' : ''} in CSV file`);
      } else {
        setCurrentStep('preview');
      }
    } catch (error) {
      setUploadError('Failed to read CSV file. Please try again.');
      console.error('CSV parsing error:', error);
    }
  }, []);

  const handleStartImport = useCallback(async () => {
    if (!parseResult || parseResult.data.length === 0) return;

    setCurrentStep('importing');
    
    const progress: ImportProgress = {
      total: parseResult.data.length,
      processed: 0,
      successful: 0,
      failed: 0,
      isComplete: false,
      errors: []
    };
    
    setImportProgress(progress);

    try {
      // Process assignments in batches
      const batchSize = 5;
      const assignments = parseResult.data;
      
      for (let i = 0; i < assignments.length; i += batchSize) {
        const batch = assignments.slice(i, i + batchSize);
        
        // Process batch
        const batchPromises = batch.map(async (assignment) => {
          try {
            const response = await fetch('/api/admin/bulk-import/groups', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                email: assignment.email,
                groupId: assignment.groupId,
                groupName: assignment.groupName,
                tenantId
              }),
            });

            if (!response.ok) {
              const errorData = await response.json();
              throw new Error(errorData.error || 'Failed to add user to group');
            }

            return { success: true, email: assignment.email };
          } catch (error) {
            return { 
              success: false, 
              email: assignment.email, 
              error: error instanceof Error ? error.message : 'Unknown error' 
            };
          }
        });

        const batchResults = await Promise.all(batchPromises);
        
        // Update progress
        const newProgress = { ...progress };
        
        batchResults.forEach((result) => {
          newProgress.processed++;
          if (result.success) {
            newProgress.successful++;
          } else {
            newProgress.failed++;
            newProgress.errors.push({
              email: result.email,
              error: result.error || 'Unknown error'
            });
          }
        });

        setImportProgress({ ...newProgress });
        
        // Small delay between batches to avoid overwhelming the server
        if (i + batchSize < assignments.length) {
          await new Promise(resolve => setTimeout(resolve, 500));
        }
      }

      // Mark as complete
      setImportProgress(prev => prev ? { ...prev, isComplete: true } : null);
      setCurrentStep('complete');
      
      // Show success toast
      const successCount = progress.successful;
      if (successCount > 0) {
        const plural = successCount !== 1 ? 's' : '';
        toast.success(t("bulkImport.dialog.messages.successfullyAdded", { count: successCount, plural }));
      }
      
      // Call completion callback
      onImportComplete?.();
      
    } catch (error) {
      console.error('Import error:', error);
      toast.error(t("bulkImport.dialog.messages.importFailed"));
      setCurrentStep('preview');
    }
  }, [parseResult, tenantId, onImportComplete]);

  const handleReset = useCallback(() => {
    setCurrentStep('upload');
    setParseResult(null);
    setImportProgress(null);
    setUploadError('');
  }, []);

  const handleClose = useCallback(() => {
    if (currentStep === 'importing') {
      // Don't allow closing during import
      return;
    }
    setOpen(false);
    // Reset after a delay to avoid visual glitch
    setTimeout(handleReset, 300);
  }, [currentStep, handleReset]);

  const canProceed = parseResult && parseResult.data.length > 0 && parseResult.errors.length === 0;

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {trigger || (
          <Button variant="outline" className="gap-2">
            <Upload className="h-4 w-4" />
            {t("bulkImport.dialog.triggerButton")}
          </Button>
        )}
      </DialogTrigger>
      
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            {t("bulkImport.dialog.title")}
          </DialogTitle>
          <DialogDescription>
            {t("bulkImport.dialog.description")}
          </DialogDescription>
        </DialogHeader>

        {/* Step Indicator */}
        <div className="flex items-center gap-2 py-4">
          <Badge variant={currentStep === 'upload' ? 'default' : 'secondary'}>
            1. {t("bulkImport.dialog.steps.upload")}
          </Badge>
          <ArrowRight className="h-4 w-4 text-muted-foreground" />
          <Badge variant={currentStep === 'preview' ? 'default' : 'secondary'}>
            2. {t("bulkImport.dialog.steps.preview")}
          </Badge>
          <ArrowRight className="h-4 w-4 text-muted-foreground" />
          <Badge variant={['importing', 'complete'].includes(currentStep) ? 'default' : 'secondary'}>
            3. {t("bulkImport.dialog.steps.import")}
          </Badge>
        </div>

        {/* Step Content */}
        <div className="min-h-[400px]">
          {currentStep === 'upload' && (
            <GroupCSVUploadZone
              onFileSelect={handleFileSelect}
              tenantId={tenantId}
              error={uploadError}
            />
          )}

          {currentStep === 'preview' && parseResult && (
            <GroupMemberPreviewTable parseResult={parseResult} />
          )}

          {(currentStep === 'importing' || currentStep === 'complete') && importProgress && (
            <ImportProgressComponent progress={importProgress} />
          )}
        </div>

        <DialogFooter className="gap-2">
          {currentStep === 'upload' && (
            <Button variant="outline" onClick={handleClose}>
              {t("bulkImport.dialog.buttons.cancel")}
            </Button>
          )}

          {currentStep === 'preview' && (
            <>
              <Button variant="outline" onClick={handleReset}>
                <ArrowLeft className="h-4 w-4 mr-2" />
                {t("bulkImport.dialog.buttons.back")}
              </Button>
              <Button
                onClick={handleStartImport}
                disabled={!canProceed}
                className="gap-2"
              >
                <Upload className="h-4 w-4" />
                {t("bulkImport.dialog.buttons.import", { count: parseResult?.data.length || 0 })}
              </Button>
            </>
          )}

          {currentStep === 'importing' && (
            <Button disabled>
              {t("bulkImport.dialog.buttons.importing")}
            </Button>
          )}

          {currentStep === 'complete' && (
            <>
              <Button variant="outline" onClick={handleReset}>
                {t("bulkImport.dialog.buttons.importMore")}
              </Button>
              <Button onClick={handleClose}>
                {t("bulkImport.dialog.buttons.done")}
              </Button>
            </>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
