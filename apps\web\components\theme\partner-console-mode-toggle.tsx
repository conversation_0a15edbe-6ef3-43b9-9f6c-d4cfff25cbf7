"use client";

import * as React from "react";
import { MoonIcon, SunIcon } from "@radix-ui/react-icons";
import { useTheme } from "next-themes";
import { useLanguage } from "@/lib/language-context";
import { useSession } from "next-auth/react";
import { updateThemeConfig, getThemeConfig, generateThemeVariables } from "@/services/theme-config";
import { getCookie } from "@/utils/cookies";
import toast from "react-hot-toast";
import { ThemeConfig } from "@/types/theme-config";

import { Button } from "@/components/ui/button";

export function PartnerConsoleModeToggle() {
  const { setTheme, resolvedTheme } = useTheme();
  const { t } = useLanguage();
  const { data: session } = useSession();
  const [isUpdating, setIsUpdating] = React.useState(false);
  const [themeConfig, setThemeConfig] = React.useState<ThemeConfig | null>(null);

  // Fetch theme configuration on component mount
  React.useEffect(() => {
    const fetchThemeConfig = async () => {
      const currentOrgId = getCookie("currentOrganizationId") ||
        (session as any)?.currentOrganization?.id ||
        (session as any)?.memberships?.[0]?.tenant?.id;

      if (currentOrgId) {
        try {
          const config = await getThemeConfig(currentOrgId);
          setThemeConfig(config);
        } catch (error) {
          console.error("Error fetching theme config:", error);
        }
      }
    };

    fetchThemeConfig();
  }, [session]);

  // Listen for real-time theme updates and apply colors
  React.useEffect(() => {
    const handleRealtimeThemeUpdate = (event: CustomEvent) => {
      const { themePreset, themeConfig: updatedConfig } = event.detail;

      if (updatedConfig) {
        setThemeConfig(updatedConfig);
        // Apply theme-mode-specific colors when receiving real-time updates
        applyThemeColors(updatedConfig, themePreset);
      }
    };

    // Listen for real-time theme updates
    window.addEventListener("realtimeThemeUpdate", handleRealtimeThemeUpdate as EventListener);

    return () => {
      window.removeEventListener("realtimeThemeUpdate", handleRealtimeThemeUpdate as EventListener);
    };
  }, []);

  // Apply colors when theme mode changes (from next-themes)
  React.useEffect(() => {
    if (themeConfig && resolvedTheme) {
      applyThemeColors(themeConfig, resolvedTheme as "light" | "dark");
    }
  }, [resolvedTheme, themeConfig]);

  // Function to apply theme-mode-specific colors
  const applyThemeColors = (config: ThemeConfig, themeMode: "light" | "dark") => {
    if (typeof window === "undefined") return;

    try {
      // Generate theme variables with the current theme mode
      const themeVariables = generateThemeVariables(config, themeMode);
      const root = document.documentElement;

      // Apply the generated CSS variables to the root element
      Object.entries(themeVariables).forEach(([property, value]) => {
        if (value) {
          root.style.setProperty(property, value);
        } else {
          // Remove the custom property to fall back to default
          root.style.removeProperty(property);
        }
      });

      console.log(`Applied ${themeMode} theme colors:`, themeVariables);
    } catch (error) {
      console.error("Error applying theme colors:", error);
    }
  };

  const handleSubmit = async () => {
    const newTheme = resolvedTheme === "dark" ? "light" : "dark";

    // Get current organization ID from cookies or session
    const currentOrgId = getCookie("currentOrganizationId") ||
      (session as any)?.currentOrganization?.id ||
      (session as any)?.memberships?.[0]?.tenant?.id;

    if (!currentOrgId) {
      // Fallback to local theme change if no organization context
      setTheme(newTheme);
      toast.error("No organization context found. Applied theme locally only.");
      return;
    }

    // Partner console: Save to database and sync across tenant members
    setIsUpdating(true);
    try {
      const result = await updateThemeConfig(currentOrgId, {
        themePreset: newTheme,
      });

      if (result.success) {
        // Fetch the updated theme configuration from database
        const updatedConfig = await getThemeConfig(currentOrgId);
        if (updatedConfig) {
          setThemeConfig(updatedConfig);

          // Apply theme-mode-specific colors immediately
          applyThemeColors(updatedConfig, newTheme);
        }

        // Set the theme mode (this will trigger next-themes to update)
        setTheme(newTheme);

        // Broadcast theme change event for real-time synchronization
        const event = new CustomEvent("realtimeThemeUpdate", {
          detail: {
            themePreset: newTheme,
            tenantId: currentOrgId,
            themeConfig: updatedConfig,
          },
        });
        window.dispatchEvent(event);

        console.log("Theme updated successfully via database with custom colors applied");
        toast.success(`Switched to ${newTheme} theme with custom colors for all team members`);
      } else {
        // Fallback to local theme change
        setTheme(newTheme);
        toast.error("Failed to sync theme with team. Applied locally only.");
      }
    } catch (error) {
      console.error("Error updating theme:", error);
      // Fallback to local theme change
      setTheme(newTheme);
      toast.error("Failed to sync theme with team. Applied locally only.");
    } finally {
      setIsUpdating(false);
    }
  };

  return (
    <>
      <Button
        onClick={handleSubmit}
        variant="outline"
        size="icon"
        disabled={isUpdating}
        title={`Switch to ${resolvedTheme === "dark" ? "light" : "dark"} theme with custom colors for all team members`}
      >
        <SunIcon className="h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0" />
        <MoonIcon className="absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
        <span className="sr-only">{t("theme.toggleTheme")}</span>
      </Button>
    </>
  );
}
