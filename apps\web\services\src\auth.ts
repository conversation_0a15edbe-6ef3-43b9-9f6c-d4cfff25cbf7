import { apiUrl } from "..";

/**
 * Register a new user with optional organization details
 */
export const register = async (data) => {
  try {
    const url = `${apiUrl}/auth/register`;
    const response = await fetch(url, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const errorData = await response.json();
      return { error: errorData.error || "Error registering the user" };
    }
    
    return await response.json();
  } catch (error) {
    console.error("Registration API error:", error);
    return { error: "Error registering the user. Please try again." };
  }
};

/**
 * Send a password reset email
 */
export const sendPasswordResetEmail = async (email) => {
  try {
    const url = `${apiUrl}/auth/forgot-password`;
    const response = await fetch(url, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ email }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      return { error: errorData.error || "Error sending password reset email" };
    }
    
    return await response.json();
  } catch (error) {
    console.error("Password reset API error:", error);
    return { error: "Error sending password reset email. Please try again." };
  }
};

/**
 * Verify a password reset token
 */
export const verifyResetToken = async (token) => {
  try {
    const url = `${apiUrl}/auth/verify-reset-token?token=${token}`;
    const response = await fetch(url, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      return { error: errorData.error || "Invalid or expired reset token" };
    }
    
    return await response.json();
  } catch (error) {
    console.error("Verify reset token API error:", error);
    return { error: "Error verifying reset token. Please try again." };
  }
};

/**
 * Reset password with token
 */
export const resetPassword = async (data) => {
  try {
    const url = `${apiUrl}/auth/reset-password`;
    const response = await fetch(url, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const errorData = await response.json();
      return { error: errorData.error || "Error resetting password" };
    }
    
    return await response.json();
  } catch (error) {
    console.error("Reset password API error:", error);
    return { error: "Error resetting password. Please try again." };
  }
};

/**
 * Verify email with token
 */
export const verifyEmail = async (token) => {
  try {
    const url = `${apiUrl}/auth/verify-email?token=${token}`;
    const response = await fetch(url, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      return { error: errorData.error || "Error verifying email" };
    }
    
    return await response.json();
  } catch (error) {
    console.error("Verify email API error:", error);
    return { error: "Error verifying email. Please try again." };
  }
};

/**
 * Resend verification email
 */
export const resendVerificationEmail = async (email) => {
  try {
    const url = `${apiUrl}/auth/resend-verification`;
    const response = await fetch(url, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ email }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      return { error: errorData.error || "Error sending verification email" };
    }
    
    return await response.json();
  } catch (error) {
    console.error("Resend verification API error:", error);
    return { error: "Error sending verification email. Please try again." };
  }
};

/**
 * Get all organizations for the current user
 */
export const getOrganizations = async () => {
  try {
    const url = `${apiUrl}/organizations`;
    const response = await fetch(url, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      return { error: errorData.error || "Error fetching organizations" };
    }
    
    return await response.json();
  } catch (error) {
    console.error("Get organizations API error:", error);
    return { error: "Error fetching organizations. Please try again." };
  }
};

/**
 * Create a new organization
 */
export const createOrganization = async (data) => {
  try {
    const url = `${apiUrl}/organizations`;
    const response = await fetch(url, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const errorData = await response.json();
      return { error: errorData.error || "Error creating organization" };
    }
    
    return await response.json();
  } catch (error) {
    console.error("Create organization API error:", error);
    return { error: "Error creating organization. Please try again." };
  }
};

/**
 * Set current active organization
 */
export const setCurrentOrganization = async (organizationId) => {
  try {
    const url = `${apiUrl}/organizations/set-current`;
    const response = await fetch(url, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ organizationId }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      return { error: errorData.error || "Error setting current organization" };
    }
    
    return await response.json();
  } catch (error) {
    console.error("Set current organization API error:", error);
    return { error: "Error setting current organization. Please try again." };
  }
};
