"use client";

import { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { KpiSummaryCards } from "./kpi-summary-cards";
import { UsageTrendsSection } from "./usage-trends-section";
import { FinancialOverviewSection } from "./financial-overview-section";
import { AlertSummarySection } from "./alert-summary-section";
import { QuickActionsSidebar } from "./quick-actions-sidebar";
import { DashboardFilters as DashboardFiltersComponent } from "./dashboard-filters";

import { Separator } from "@/components/ui/separator";

interface ExecutiveDashboardProps {
  initialData: ExecutiveDashboardData;
  initialFilters: DashboardFilters;
}

export interface DashboardFilters {
  timeRange: "7d" | "30d" | "90d";
  planTier?: string;
  region?: string;
}

export interface ExecutiveDashboardData {
  kpiMetrics: {
    activeTenants: number;
    activeUsers: number;
    mrr: number;
    infrastructureCost: number;
    grossMargin: number;
    alertsCount: number;
    allPlans: { label: string; value: string }[];
    trends: {
      activeTenants: number;
      activeUsers: number;
      mrr: number;
      infrastructureCost: number;
      grossMargin: number;
    };
  };
  usageTrends: {
    activeTenantsData: Array<{ name: string; value: number }>;
    tokenUsageData: Array<{
      name: string;
      inputTokens: number;
      outputTokens: number;
      totalTokens: number;
      value: number; // For backward compatibility
    }>;
    apiVolumeData: Array<{ name: string; success: number; errors: number }>;
    storageGrowthData: Array<{ name: string; value: number }>;
  };
  financialData: {
    mrrVsCostData: Array<{ name: string; mrr: number; cost: number }>;
    tenantProfitabilityData: Array<{ 
      name: string; 
      profit: number; 
      plan: string; 
      margin: number 
    }>;
  };
  alerts: Array<{
    id: string;
    type: "usage_spike" | "quota_risk" | "system_error" | "cost_alert";
    severity: "red" | "orange" | "yellow" | "green";
    title: string;
    description: string;
    tenantId?: string;
    tenantName?: string;
    timestamp: string;
    acknowledged: boolean;
  }>;
}

export function ExecutiveDashboard({ initialData, initialFilters }: ExecutiveDashboardProps) {
  const [data, setData] = useState<ExecutiveDashboardData>(initialData);
  const [loading, setLoading] = useState(false);
  const [filters, setFilters] = useState<DashboardFilters>(initialFilters);
  const router = useRouter();
  const searchParams = useSearchParams();

  // Update data when initialData changes (from server-side refetch)
  useEffect(() => {
    setData(initialData);
  }, [initialData]);

  // Update filters when URL search params change
  useEffect(() => {
    if (searchParams) {
      const timeRange = (searchParams.get('timeRange') as "7d" | "30d" | "90d") || "7d";
      const planTier = searchParams.get('planTier') || undefined;
      const region = searchParams.get('region') || undefined;

      setFilters({ timeRange, planTier, region });
    }
  }, [searchParams]);

  // Handle filter changes with client-side data fetching (fixed loading issue)
  const handleFiltersChange = async (newFilters: DashboardFilters) => {
    console.log("Filter change requested:", newFilters);

    setLoading(true);
    setFilters(newFilters);

    try {
      // Update URL without page refresh
      const params = new URLSearchParams();
      params.set('timeRange', newFilters.timeRange);
      if (newFilters.planTier && newFilters.planTier !== 'all') {
        params.set('planTier', newFilters.planTier);
      }
      if (newFilters.region && newFilters.region !== 'all') {
        params.set('region', newFilters.region);
      }

      // Update URL without refresh
      router.push(`/dashboard?${params.toString()}`, { scroll: false });

      console.log("Fetching data with params:", params.toString());

      // Fetch new data via API with timeout and better error handling
      const controller = new AbortController();
      const timeoutId = setTimeout(() => {
        controller.abort();
        console.error("Request timed out");
      }, 10000); // 10 second timeout

      const response = await fetch(`/api/dashboard/executive-overview?${params.toString()}`, {
        signal: controller.signal,
        headers: {
          'Cache-Control': 'no-cache',
          'Content-Type': 'application/json',
        },
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const newData = await response.json();
      console.log("New data received:", {
        activeTenants: newData.kpiMetrics?.activeTenants,
        hasCharts: !!newData.usageTrends
      });

      setData(newData);
    } catch (error: any) {
      console.error('Error updating dashboard data:', error);

      // Don't show error to user for timeout/abort - just keep old data
      if (error.name !== 'AbortError') {
        // Could add toast notification here
        console.error("Dashboard update failed:", error.message);
      }
    } finally {
      setLoading(false);
    }
  };

  // Don't show full loading screen for filter changes - show data with loading indicators instead

  return (
    <div className="space-y-6">
      {/* Dashboard Filters */}
      <DashboardFiltersComponent
        filters={filters}
        onFiltersChange={handleFiltersChange}
        loading={loading}
        planTierOptions={data.kpiMetrics.allPlans}
      />

      <div className="grid grid-cols-1 xl:grid-cols-4 gap-6">
        {/* Main Dashboard Content */}
        <div className="xl:col-span-3 space-y-6">
          {/* KPI Summary Cards */}
          <KpiSummaryCards
            data={data.kpiMetrics}
            loading={loading}
          />

          <Separator />

          {/* Usage Trends Section */}
          <UsageTrendsSection
            data={data.usageTrends}
            filters={filters}
            loading={loading}
          />

          <Separator />

          {/* Financial Overview Section */}
          <div id="financial-overview-section">
            <FinancialOverviewSection
              data={data.financialData}
              kpiMetrics={{
                mrr: data.kpiMetrics.mrr,
                infrastructureCost: data.kpiMetrics.infrastructureCost
              }}
              loading={loading}
            />
          </div>

          <Separator />

          {/* Alert Summary Section */}
          <div id="alert-summary-section">
            <AlertSummarySection
              alerts={data.alerts}
              loading={loading}
              timeRange={filters.timeRange} // ✅ Pass timeRange for System Errors modal
              onAlertAction={(alertId, action) => {
              // Enhanced alert action handling
              console.log(`Alert ${alertId} action: ${action}`);

              switch (action) {
                case "view":
                  // Navigate to detailed view for specific alert
                  if (alertId.includes("usage-spike")) {
                    const tenantId = alertId.split("-")[2];
                    router.push(`/tenants/${tenantId}/usage?tab=token-usage&timeRange=${searchParams?.get('timeRange') || '7d'}`);
                  } else if (alertId.includes("quota-risk")) {
                    const tenantId = alertId.split("-")[2];
                    router.push(`/tenants/${tenantId}/quotas`);
                  } else if (alertId.includes("system-error")) {
                    const tenantId = alertId.split("-")[2];
                    router.push(`/tenants/${tenantId}/logs?filter=errors`);
                  } else if (alertId.includes("cost-alert")) {
                    const tenantId = alertId.split("-")[2];
                    router.push(`/tenants/${tenantId}/billing`);
                  }
                  break;

                case "view_all":
                  // Navigate to comprehensive views
                  if (alertId === "view-all-usage") {
                    router.push(`/analytics/usage-spikes?timeRange=${searchParams?.get('timeRange') || '7d'}`);
                  } else if (alertId === "view-all-quotas") {
                    router.push(`/analytics/quota-monitoring`);
                  } else if (alertId === "view-all-errors") {
                    router.push(`/analytics/error-logs`);
                  } else if (alertId === "view-all-costs") {
                    router.push(`/analytics/financial-report`);
                  }
                  break;

                case "acknowledge":
                  // Mark alert as acknowledged
                  console.log(`Acknowledging alert: ${alertId}`);
                  // TODO: Implement alert acknowledgment API call
                  break;

                default:
                  console.log(`Unhandled alert action: ${action} for alert: ${alertId}`);
              }
            }}
          />
          </div>
        </div>

        {/* Quick Actions Sidebar */}
        <div className="xl:col-span-1">
          <QuickActionsSidebar
            dashboardData={data}
          />
        </div>
      </div>
    </div>
  );
}
