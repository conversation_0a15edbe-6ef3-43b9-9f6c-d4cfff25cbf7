import { authOptions } from "@/lib/next-auth";
import db from "@/lib/shared-db";
import { getServerSession } from "next-auth/next";
import { NextResponse } from "next/server";

export async function GET() {
  const session = await getServerSession(authOptions);

  if (!session) {
    return NextResponse.json(
      { error: "Unauthorized" },
      { status: 401 }
    );
  }

  try {
    // Get all active plans
    const plans = await db.plan.findMany({
      where: {
        isActive: true
      },
      select: {
        id: true,
        name: true,
        type: true,
        description: true,
        price: true,
        includedUsers: true,
        vectorStoreGB: true,
        webSearchLimit: true,
      },
      orderBy: [
        { type: "asc" },
        { price: "asc" }
      ]
    });

    return NextResponse.json(plans);
  } catch (error) {
    console.error("Error fetching plans:", error);
    return NextResponse.json(
      { error: "Failed to fetch plans" },
      { status: 500 }
    );
  }
}
