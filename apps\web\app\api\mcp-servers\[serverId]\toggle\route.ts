import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/next-auth";
import db from "@/lib/shared-db";

interface MCPServerResponse {
  id: string;
  name: string;
  description?: string;
  server_type: "STDIO" | "HTTP";
  command?: string;
  args?: string[];
  env?: Record<string, string>;
  url?: string;
  headers?: Record<string, string>;
  status: string;
  last_error?: string;
  timeout: number;
  auto_restart: boolean;
  is_public: boolean;
  user_id: string;
  tenant_id: string;
  created_at: string;
  updated_at: string;
}

/**
 * POST /api/mcp-servers/[serverId]/toggle - Toggle MCP server active status
 */
export async function POST(
  req: NextRequest,
  { params }: { params: { serverId: string } }
) {
  try {
    const session: any = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { serverId } = params;

    // Find server with ownership check
    const server = await db.mCPServer.findFirst({
      where: {
        id: serverId,
        tenantId: session.currentOrganization?.id,
        userId: session.userId, // Only owner can toggle
      },
    });

    if (!server) {
      return NextResponse.json(
        { error: "MCP server not found or access denied" },
        { status: 404 }
      );
    }

    // Determine new status based on current status
    let newStatus: string;
    if (server.status === "ACTIVE") {
      newStatus = "INACTIVE";
    } else if (server.status === "INACTIVE" || server.status === "ERROR") {
      newStatus = "ACTIVE";
    } else {
      // If status is TESTING, don't allow toggle
      return NextResponse.json(
        { error: "Cannot toggle server status while testing" },
        { status: 400 }
      );
    }

    // Update server status
    const updatedServer = await db.mCPServer.update({
      where: { id: serverId },
      data: {
        status: newStatus,
        lastError: newStatus === "ACTIVE" ? null : server.lastError, // Clear error when activating
        updatedAt: new Date(),
      },
    });

    // Convert to response format
    const response: MCPServerResponse = {
      id: updatedServer.id,
      name: updatedServer.name,
      description: updatedServer.description,
      server_type: updatedServer.serverType as "STDIO" | "HTTP",
      command: updatedServer.command,
      args: updatedServer.args as string[],
      env: updatedServer.env as Record<string, string>,
      url: updatedServer.url,
      headers: updatedServer.headers as Record<string, string>,
      status: updatedServer.status,
      last_error: updatedServer.lastError,
      timeout: updatedServer.timeout,
      auto_restart: updatedServer.autoRestart,
      is_public: updatedServer.isPublic,
      user_id: updatedServer.userId,
      tenant_id: updatedServer.tenantId,
      created_at: updatedServer.createdAt.toISOString(),
      updated_at: updatedServer.updatedAt.toISOString(),
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error toggling MCP server status:", error);
    return NextResponse.json(
      { error: "Failed to toggle server status" },
      { status: 500 }
    );
  }
}
