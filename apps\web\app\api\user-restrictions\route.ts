import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/next-auth";
import db from "@/lib/shared-db";

// Since we don't have a dedicated UserRestriction model, we'll store restrictions
// in the chatbot's customization field for now. In production, you'd want a dedicated model.

export async function GET(request: NextRequest) {
  try {
    const session: any = await getServerSession(authOptions);
    if (!session?.userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const tenantId = searchParams.get("tenantId");
    const chatbotId = searchParams.get("chatbotId");

    if (!tenantId) {
      return NextResponse.json(
        { error: "Tenant ID is required" },
        { status: 400 }
      );
    }

    // Get chatbots with their restrictions
    const chatbots = await db.chatbot.findMany({
      where: {
        tenantId: tenantId,
        userId: session.userId,
        ...(chatbotId && { id: chatbotId }),
      },
      select: {
        id: true,
        name: true,
        customization: true,
        allowedDomains: true,
      },
    });

    const restrictions = chatbots.map((chatbot) => {
      const customization = chatbot.customization as any;
      const userRestrictions = customization?.userRestrictions || {};

      return {
        chatbotId: chatbot.id,
        chatbotName: chatbot.name,
        allowedDomains: chatbot.allowedDomains,
        restrictions: {
          blockedIPs: userRestrictions.blockedIPs || [],
          blockedUserAgents: userRestrictions.blockedUserAgents || [],
          blockedDomains: userRestrictions.blockedDomains || [],
          blockedEmails: userRestrictions.blockedEmails || [],
          allowedIPs: userRestrictions.allowedIPs || [],
          allowedUserAgents: userRestrictions.allowedUserAgents || [],
          allowedDomains: userRestrictions.allowedDomains || [],
          allowedEmails: userRestrictions.allowedEmails || [],
          rateLimits: userRestrictions.rateLimits || {
            messagesPerMinute: 10,
            messagesPerHour: 100,
            messagesPerDay: 500,
          },
          requireAuth: userRestrictions.requireAuth || false,
          geoRestrictions: userRestrictions.geoRestrictions || {
            allowedCountries: [],
            blockedCountries: [],
          },
        },
      };
    });

    return NextResponse.json({ restrictions });
  } catch (error) {
    console.error("Error fetching user restrictions:", error);
    return NextResponse.json(
      { error: "Failed to fetch user restrictions" },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session: any = await getServerSession(authOptions);
    if (!session?.userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const { chatbotId, tenantId, restrictions } = body;

    if (!chatbotId || !tenantId) {
      return NextResponse.json(
        { error: "Chatbot ID and Tenant ID are required" },
        { status: 400 }
      );
    }

    // Verify chatbot belongs to user
    const chatbot = await db.chatbot.findUnique({
      where: {
        id: chatbotId,
        tenantId: tenantId,
        userId: session.userId,
      },
    });

    if (!chatbot) {
      return NextResponse.json({ error: "Chatbot not found" }, { status: 404 });
    }

    // Update chatbot with new restrictions
    const updatedChatbot = await db.chatbot.update({
      where: { id: chatbotId },
      data: {
        customization: {
          ...(chatbot.customization as any),
          userRestrictions: restrictions,
        },
        // Also update allowedDomains if provided in restrictions
        ...(restrictions.allowedDomains && {
          allowedDomains: restrictions.allowedDomains,
        }),
      },
    });

    return NextResponse.json({ 
      success: true, 
      message: "User restrictions updated successfully",
      chatbotId: updatedChatbot.id,
    });
  } catch (error) {
    console.error("Error updating user restrictions:", error);
    return NextResponse.json(
      { error: "Failed to update user restrictions" },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const session: any = await getServerSession(authOptions);
    if (!session?.userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const chatbotId = searchParams.get("chatbotId");
    const tenantId = searchParams.get("tenantId");
    const restrictionType = searchParams.get("type"); // 'ip', 'email', 'domain', 'userAgent'
    const value = searchParams.get("value");

    if (!chatbotId || !tenantId || !restrictionType || !value) {
      return NextResponse.json(
        { error: "Missing required parameters" },
        { status: 400 }
      );
    }

    // Verify chatbot belongs to user
    const chatbot = await db.chatbot.findUnique({
      where: {
        id: chatbotId,
        tenantId: tenantId,
        userId: session.userId,
      },
    });

    if (!chatbot) {
      return NextResponse.json({ error: "Chatbot not found" }, { status: 404 });
    }

    const customization = chatbot.customization as any;
    const userRestrictions = customization?.userRestrictions || {};

    // Remove the specific restriction
    const restrictionMap: Record<string, string> = {
      ip: 'blockedIPs',
      email: 'blockedEmails',
      domain: 'blockedDomains',
      userAgent: 'blockedUserAgents',
    };

    const restrictionField = restrictionMap[restrictionType];
    if (restrictionField && userRestrictions[restrictionField]) {
      userRestrictions[restrictionField] = userRestrictions[restrictionField].filter(
        (item: string) => item !== value
      );
    }

    // Update chatbot
    await db.chatbot.update({
      where: { id: chatbotId },
      data: {
        customization: {
          ...customization,
          userRestrictions,
        },
      },
    });

    return NextResponse.json({ 
      success: true, 
      message: "Restriction removed successfully" 
    });
  } catch (error) {
    console.error("Error removing user restriction:", error);
    return NextResponse.json(
      { error: "Failed to remove user restriction" },
      { status: 500 }
    );
  }
}
