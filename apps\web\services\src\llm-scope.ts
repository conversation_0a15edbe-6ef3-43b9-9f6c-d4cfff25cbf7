import { LLMScopeSettings, LLMScopeUpdateRequest } from "@/types/llm-scope";
import { addAuthHeaders } from "@/lib/api/auth-token";

const API_BASE_URL =
  process.env.NEXT_PUBLIC_API_BASE_URL || "http://localhost:3000";

export async function getLLMScopeSettings(
  tenantId: string,
  cookies: string = ""
): Promise<LLMScopeSettings | null> {
  try {
    const response = await fetch(
      `${API_BASE_URL}/api/organizations/llm-scope?tenantId=${tenantId}`,
      {
        method: "GET",
        headers: {
          Cookie: cookies,
        },
      }
    );

    if (!response.ok) {
      throw new Error(
        `Failed to fetch LLM scope settings: ${response.statusText}`
      );
    }

    const data = await response.json();
    return data.settings;
  } catch (error) {
    console.error("Error fetching LLM scope settings:", error);
    return null;
  }
}

export async function updateLLMScopeSettings(
  tenantId: string,
  settings: LLMScopeUpdateRequest
): Promise<{
  success: boolean;
  message?: string;
  settings?: LLMScopeSettings;
}> {
  try {
    const headers = await addAuthHeaders({
      "Content-Type": "application/json",
    });

    const response = await fetch(
      `${API_BASE_URL}/api/organizations/llm-scope`,
      {
        method: "PUT",
        headers,
        body: JSON.stringify({
          tenantId,
          ...settings,
        }),
      }
    );

    const data = await response.json();

    if (!response.ok) {
      return {
        success: false,
        message: data.error || "Failed to update LLM scope settings",
      };
    }

    // Broadcast the change for real-time synchronization
    if (data.settings && typeof window !== 'undefined') {
      // Dispatch custom event for same-tab synchronization
      window.dispatchEvent(new CustomEvent('llm-scope-updated', {
        detail: {
          llmScope: data.settings.llmScope,
          updatedAt: data.settings.updatedAt
        }
      }));

      // Store in localStorage for cross-tab synchronization
      localStorage.setItem(`llm-scope-${tenantId}`, JSON.stringify({
        llmScope: data.settings.llmScope,
        updatedAt: data.settings.updatedAt
      }));
    }

    return {
      success: true,
      message: data.message,
      settings: data.settings,
    };
  } catch (error) {
    console.error("Error updating LLM scope settings:", error);
    return {
      success: false,
      message: "An error occurred while updating LLM scope settings",
    };
  }
}
