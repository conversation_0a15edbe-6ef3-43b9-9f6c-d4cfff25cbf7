'use client';
import React from 'react'
import { UseFormReturn } from 'react-hook-form';
import { Button } from '../ui/button';
import { Eye } from 'lucide-react';
import { ThemeConfigFormValues } from '../wrapper-screens/organization-settings/theme-config/theme-config-page';

interface ThemeConfigHeaderProps {
    previewMode: boolean;
    setPreviewMode: React.Dispatch<React.SetStateAction<boolean>>;
    applyPreviewColors: (themeData: Partial<ThemeConfigFormValues>) => void;
    resetToSavedTheme: () => void;
    saving: boolean;
    form: UseFormReturn<ThemeConfigFormValues>;
}

const ThemeConfigHeader = ({ previewMode, setPreviewMode, applyPreviewColors, resetToSavedTheme, saving, form }: ThemeConfigHeaderProps) => {
    return (
        <div className="flex items-center justify-between">
            <div>
                <h1 className="text-2xl font-bold">Theme Configuration</h1>
                <p className="text-muted-foreground">
                    Customize your organization's branding and appearance
                </p>
            </div>
            <div className="flex items-center space-x-2">
                <Button
                    type="button"
                    variant={previewMode ? "default" : "outline"}
                    size="sm"
                    onClick={() => {
                        const newPreviewMode = !previewMode;
                        setPreviewMode(newPreviewMode);

                        if (newPreviewMode) {
                            // Apply current form values as preview
                            applyPreviewColors(form.getValues());
                        } else {
                            // Reset to saved theme configuration without page reload
                            resetToSavedTheme();
                        }
                    }}
                    disabled={saving}
                >
                    <Eye className="h-4 w-4 mr-2" />
                    {previewMode ? "Exit Preview" : "Live Preview"}
                </Button>
            </div>
        </div>
    )
}

export default ThemeConfigHeader