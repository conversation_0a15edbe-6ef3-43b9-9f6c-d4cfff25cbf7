"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { googleDriveService } from "@/services/src/integration/google";
import { Folder, ChevronRight, Loader2 } from "lucide-react";
import { useLanguage } from "@/lib/language-context";
import { getCookie } from "@/utils/cookies";

interface GoogleDriveFolderSelectorProps {
  tenantId: string;
  onFolderSelect: (folderId: string, folderName: string) => void;
  selectedFolderId?: string;
}

interface FolderItem {
  id: string;
  name: string;
  mimeType: string;
}

export function GoogleDriveFolderSelector({
  tenantId,
  onFolderSelect,
  selectedFolderId,
}: GoogleDriveFolderSelectorProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [folders, setFolders] = useState<FolderItem[]>([]);
  const [currentFolderId, setCurrentFolderId] = useState<string>("root");
  const [folderPath, setFolderPath] = useState<{ id: string; name: string }[]>([
    { id: "root", name: "My Drive" },
  ]);
  const [selectedFolder, setSelectedFolder] = useState<{
    id: string;
    name: string;
  } | null>(null);
  const { t } = useLanguage();
  const userId = getCookie("userId") ?? "";

  // Load folders when dialog opens or folder changes
  useEffect(() => {
    if (isOpen) {
      loadFolders(currentFolderId);
    }
  }, [isOpen, currentFolderId]);

  const loadFolders = async (parentId: string) => {
    setIsLoading(true);
    try {
      const result = await googleDriveService.listFolders({
        tenantId,
        parentId,
        userId,
      });

      if (result.data) {
        setFolders(result.data);
      } else {
        setFolders([]);
      }
    } catch (error) {
      console.error("Error loading folders:", error);
      setFolders([]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleFolderClick = (folder: FolderItem) => {
    setCurrentFolderId(folder.id);
    setFolderPath([...folderPath, { id: folder.id, name: folder.name }]);
  };

  const handleBreadcrumbClick = (index: number) => {
    const newPath = folderPath.slice(0, index + 1);
    setFolderPath(newPath);
    setCurrentFolderId(newPath[newPath.length - 1].id);
  };

  const handleSelectFolder = () => {
    if (currentFolderId) {
      const currentFolder = folderPath[folderPath.length - 1];
      setSelectedFolder({
        id: currentFolder.id,
        name: currentFolder.name,
      });
      onFolderSelect(currentFolder.id, currentFolder.name);
      setIsOpen(false);
    }
  };

  return (
    <div>
      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogTrigger asChild>
          <Button variant="outline" className="w-full justify-start gap-2">
            <Folder className="h-4 w-4" />
            {selectedFolder?.name || selectedFolderId ? (
              <div className="truncate">
                <span className="font-medium">
                  {selectedFolder?.name || "Selected Folder"}
                </span>
              </div>
            ) : (
              t("integration.selectFolder") || "Select Google Drive Folder"
            )}
          </Button>
        </DialogTrigger>
        <DialogContent className="sm:max-w-[525px]">
          <DialogHeader>
            <DialogTitle>
              {t("integration.selectGoogleDriveFolder") ||
                "Select Google Drive Folder"}
            </DialogTitle>
          </DialogHeader>

          <div className="flex flex-col gap-4 py-4">
            {/* Breadcrumb navigation */}
            <div className="flex flex-wrap items-center gap-1 text-sm">
              {folderPath.map((folder, index) => (
                <div key={folder.id} className="flex items-center">
                  {index > 0 && <ChevronRight className="h-3 w-3 mx-1" />}
                  <button
                    onClick={() => handleBreadcrumbClick(index)}
                    className="hover:underline"
                  >
                    {folder.name}
                  </button>
                </div>
              ))}
            </div>

            {/* Folder list */}
            <div className="max-h-[300px] overflow-y-auto border rounded-md">
              {isLoading ? (
                <div className="flex items-center justify-center p-4">
                  <Loader2 className="h-5 w-5 animate-spin" />
                </div>
              ) : folders.length === 0 ? (
                <div className="p-4 text-center text-sm text-muted-foreground">
                  {t("integration.noFoldersFound") || "No folders found"}
                </div>
              ) : (
                <div className="divide-y">
                  {folders.map((folder) => (
                    <button
                      key={folder.id}
                      className="flex w-full items-center gap-2 p-2 hover:bg-muted text-left"
                      onClick={() => handleFolderClick(folder)}
                    >
                      <Folder className="h-4 w-4" />
                      <span>{folder.name}</span>
                    </button>
                  ))}
                </div>
              )}
            </div>

            {/* Sync disclaimer */}
            <div className="bg-blue-50 dark:bg-blue-950 border border-blue-200 dark:border-blue-800 p-3 rounded-md">
              <div className="flex items-start">
                <div className="flex-shrink-0 pt-0.5">
                  <svg
                    className="h-5 w-5 text-blue-400"
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                    aria-hidden="true"
                  >
                    <path
                      fillRule="evenodd"
                      d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                      clipRule="evenodd"
                    />
                  </svg>
                </div>
                <div className="ml-3">
                  <p className="text-sm text-blue-700 dark:text-blue-300">
                    {folderPath.length > 1 ? (
                      <>
                        <span className="font-semibold">Selected path:</span>{" "}
                        {folderPath.map((folder) => folder.name).join(" / ")}
                      </>
                    ) : (
                      t("integration.syncDisclaimer") ||
                      "All files and subfolders within the selected folder will be synchronized and available in read-only mode."
                    )}
                  </p>
                </div>
              </div>
            </div>

            <div className="flex justify-end gap-2">
              <Button variant="outline" onClick={() => setIsOpen(false)}>
                {t("common.cancel") || "Cancel"}
              </Button>
              <Button onClick={handleSelectFolder}>
                {t("common.select") || "Select"}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
