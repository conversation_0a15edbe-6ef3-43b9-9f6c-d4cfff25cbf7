"use client";

import { Button } from "@/components/ui/button";
import { Ta<PERSON>, Ta<PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { RefreshCw } from "lucide-react";
import { useRouter, useSearchParams } from "next/navigation";
import { useTransition } from "react";

interface DashboardControlsProps {
  currentPeriod: string;
}

export function DashboardControls({ currentPeriod }: DashboardControlsProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [isPending, startTransition] = useTransition();

  const handlePeriodChange = (newPeriod: string) => {
    startTransition(() => {
      const params = new URLSearchParams(searchParams?.toString());
      params.set("period", newPeriod);
      router.push(`/dashboard?${params.toString()}`);
    });
  };

  const handleRefresh = () => {
    startTransition(() => {
      router.refresh();
    });
  };

  return (
    <div className="flex items-center gap-2">
      <Button
        variant="outline"
        size="sm"
        onClick={handleRefresh}
        disabled={isPending}
      >
        <RefreshCw
          className={`h-4 w-4 mr-2 ${isPending ? "animate-spin" : ""}`}
        />
        Refresh
      </Button>
      <Tabs
        value={currentPeriod}
        onValueChange={handlePeriodChange}
        className="w-full"
      >
        <TabsList>
          <TabsTrigger value="7days" disabled={isPending}>
            7 Days
          </TabsTrigger>
          <TabsTrigger value="30days" disabled={isPending}>
            30 Days
          </TabsTrigger>
          <TabsTrigger value="90days" disabled={isPending}>
            90 Days
          </TabsTrigger>
          <TabsTrigger value="6months" disabled={isPending}>
            6 Months
          </TabsTrigger>
          <TabsTrigger value="lifetime" disabled={isPending}>
            Lifetime
          </TabsTrigger>
        </TabsList>
      </Tabs>
    </div>
  );
}
