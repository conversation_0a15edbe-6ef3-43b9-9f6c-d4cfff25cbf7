"use client";

import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Server, ChevronDown, Check, Sparkles } from "lucide-react";
import { cn } from "@/lib/utils";
import { MCPServer } from "@/types/mcp-server";
import { getCookie } from "@/utils/cookies";
import { useLanguage } from "@/lib/language-context";

interface MCPServerSelectorProps {
  selectedServerIds: string[];
  onSelectionChange: (serverIds: string[]) => void;
  className?: string;
}

export const MCPServerSelector: React.FC<MCPServerSelectorProps> = ({
  selectedServerIds,
  onSelectionChange,
  className,
}) => {
  const { t } = useLanguage();
  const [servers, setServers] = useState<MCPServer[]>([]);
  const [loading, setLoading] = useState(true);
  const [open, setOpen] = useState(false);
  const tenantId = getCookie("currentOrganizationId") || "";

  // Fetch available MCP servers
  useEffect(() => {
    const fetchServers = async () => {
      try {
        const response = await fetch(`/api/mcp-servers?tenantId=${tenantId}`);
        if (response.ok) {
          const data = await response.json();
          setServers(data || []);
        }
      } catch (error) {
        console.error("Failed to fetch MCP servers:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchServers();
  }, []);

  const handleServerToggle = (serverId: string) => {
    const newSelection = selectedServerIds.includes(serverId)
      ? selectedServerIds.filter((id) => id !== serverId)
      : [serverId];
    onSelectionChange(newSelection);
  };

  const selectedServers = servers.filter((server) =>
    selectedServerIds.includes(server.id)
  );

  const getDisplayText = () => {
    if (selectedServerIds.length === 0) return "MCP";
    if (selectedServerIds.length === 1)
      return selectedServers[0]?.name || "1 selected";
    return `${selectedServerIds.length} selected`;
  };

  // Loading state
  if (loading) {
    return (
      <Button
        variant="ghost"
        size="sm"
        disabled
        className={cn("justify-start px-2 text-xs font-normal", className)}
      >
        <Server className="h-4 w-4 animate-pulse" />
        <span className="ml-1 truncate hidden md:inline">Loading...</span>
      </Button>
    );
  }

  // No servers state
  if (servers.length === 0) {
    return (
      <Button
        variant="ghost"
        size="sm"
        disabled
        className={cn(
          "justify-start px-2 text-xs font-normal opacity-50",
          className
        )}
      >
        <Server className="h-4 w-4" />
        <span className="ml-1 truncate hidden md:inline">No servers</span>
      </Button>
    );
  }

  return (
    <Popover open={open} modal={true} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          className={cn(
            "justify-start px-2 text-xs font-normal",
            selectedServerIds.length > 0 && "text-primary",
            className
          )}
        >
          <Server className="h-4 w-4 shrink-0" />
          <span className="ml-1 truncate hidden md:inline">
            {getDisplayText()}
          </span>
        </Button>
      </PopoverTrigger>

      <PopoverContent
        style={{ zIndex: 1000000 }}
        className="w-80 p-3"
        align="start"
      >
        <div className="space-y-3">
          {/* Header */}
          <div className="flex items-center justify-between">
            <h4 className="font-medium text-sm">Select Servers</h4>
            {/* <div className="flex gap-1">
           <Button
                variant="ghost"
                size="sm"
                onClick={() => onSelectionChange(servers.map((s) => s.id))}
                className="h-6 px-2 text-xs"
              >
                All
              </Button> 
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onSelectionChange([])}
                className="h-6 px-2 text-xs"
              >
                Clear
              </Button>
            </div> */}
          </div>

          {/* Server List */}
          <div className="space-y-2 max-h-48 overflow-y-auto">
            {servers.map((server) => {
              const isSelected = selectedServerIds.includes(server.id);
              return (
                <div
                  key={server.id}
                  className={cn(
                    "flex items-center space-x-3 p-2 rounded cursor-pointer hover:bg-muted/50",
                    isSelected && "bg-primary/5"
                  )}
                  onClick={() => handleServerToggle(server.id)}
                >
                  <Checkbox checked={isSelected} />
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2">
                      <span className="font-medium text-sm truncate">
                        {server.name}
                      </span>
                      <Badge
                        variant={
                          server.status === "ACTIVE" ? "default" : "secondary"
                        }
                        className="text-xs"
                      >
                        {server.status === "ACTIVE" ? t("mcpServers.statusReady") : t("mcpServers.statusConfigured")}
                      </Badge>
                    </div>
                    {server.description && (
                      <p className="text-xs text-muted-foreground truncate">
                        {server.description}
                      </p>
                    )}
                  </div>
                  {isSelected && <Check className="h-4 w-4 text-primary" />}
                </div>
              );
            })}
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
};
