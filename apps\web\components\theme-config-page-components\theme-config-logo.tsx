import React from 'react'
import { UseFormReturn } from 'react-hook-form';
import { Label } from '../ui/label';
import { Button } from '../ui/button';
import { X } from 'lucide-react';
import { Input } from '../ui/input';
import { ThemeConfigFormValues } from '../wrapper-screens/organization-settings/theme-config/theme-config-page';

interface ThemeConfigLogoProps {
  form: UseFormReturn<ThemeConfigFormValues>;
  isAdmin: boolean;
  uploadingLogo: boolean;
  uploadingFavicon: boolean;
  uploadingFullAppLogo: boolean;
  setLogoFile: React.Dispatch<React.SetStateAction<File | null>>;
  setFaviconFile: React.Dispatch<React.SetStateAction<File | null>>;
  setFullAppLogoFile: React.Dispatch<React.SetStateAction<File | null>>;
  handleFileUpload: (file: File, type: "logo" | "favicon" | "fullAppLogo") => Promise<void>;
  handleFileDelete: (type: "logo" | "favicon" | "fullAppLogo") => Promise<void>;
}

const ThemeConfigLogo: React.FC<ThemeConfigLogoProps> = ({
  form,
  isAdmin,
  uploadingLogo,
  uploadingFavicon,
  uploadingFullAppLogo,
  setLogoFile,
  setFaviconFile,
  setFullAppLogoFile,
  handleFileUpload,
  handleFileDelete,
}) => {
    return (
        <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4">
            {/* Logo */}
            <div className="space-y-2">
                <Label>Logo</Label>
                {/* Current Logo Preview or File Upload */}
                {form.watch("logoUrl") ? (
                    <div className="flex items-center space-x-2 p-2 border rounded-md bg-muted/50">
                        <img
                            src={form.watch("logoUrl")}
                            alt="Current logo"
                            className="h-8 w-8 object-contain"
                            onError={(e) => {
                                e.currentTarget.style.display = "none";
                            }}
                        />
                        <span className="text-sm text-muted-foreground flex-1 truncate">
                            Current logo
                        </span>
                        {isAdmin && (
                            <Button
                                type="button"
                                variant="ghost"
                                size="sm"
                                onClick={() => handleFileDelete("logo")}
                                disabled={uploadingLogo}
                                className="h-6 w-6 p-0 text-destructive hover:text-destructive"
                            >
                                <X className="h-3 w-3" />
                            </Button>
                        )}
                    </div>
                ) : (
                    <>
                        <div className="flex items-center space-x-2">
                            <Input
                                type="file"
                                accept="image/*"
                                onChange={(e) => {
                                    const file = e.target.files?.[0];
                                    if (file) {
                                        setLogoFile(file);
                                        handleFileUpload(file, "logo");
                                    }
                                }}
                                disabled={!isAdmin || uploadingLogo}
                                className="flex-1 cursor-pointer"
                            />
                            {uploadingLogo && (
                                <div className="flex items-center space-x-2 px-3 py-2 bg-blue-50 dark:bg-blue-950 rounded-md border border-blue-200 dark:border-blue-800">
                                    <div className="relative">
                                        <div className="w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
                                    </div>
                                    <span className="text-sm text-blue-700 dark:text-blue-300 font-medium">Uploading logo...</span>
                                </div>
                            )}
                        </div>
                    </>

                )}
            </div>

            {/* Favicon */}
            <div className="space-y-2">
                <Label>Favicon</Label>

                {/* Current Favicon Preview */}
                {form.watch("faviconUrl") ? (
                    <div className="flex items-center space-x-2 p-2 border rounded-md bg-muted/50">
                        <img
                            src={form.watch("faviconUrl")}
                            alt="Current favicon"
                            className="h-8 w-8 object-contain"
                            onError={(e) => {
                                e.currentTarget.style.display = "none";
                            }}
                        />
                        <span className="text-sm text-muted-foreground flex-1 truncate">
                            Current favicon
                        </span>
                        {isAdmin && (
                            <Button
                                type="button"
                                variant="ghost"
                                size="sm"
                                onClick={() => handleFileDelete("favicon")}
                                disabled={uploadingFavicon}
                                className="h-6 w-6 p-0 text-destructive hover:text-destructive"
                            >
                                <X className="h-3 w-3" />
                            </Button>
                        )}
                    </div>
                ) : (
                    <>
                        {/* File Upload */}
                        <div className="flex items-center space-x-2">
                            <Input
                                type="file"
                                accept="image/*"
                                onChange={(e) => {
                                    const file = e.target.files?.[0];
                                    if (file) {
                                        setFaviconFile(file);
                                        handleFileUpload(file, "favicon");
                                    }
                                }}
                                disabled={!isAdmin || uploadingFavicon}
                                className="flex-1 cursor-pointer"
                            />
                            {uploadingFavicon && (
                                <div className="flex items-center space-x-2 px-3 py-2 bg-green-50 dark:bg-green-950 rounded-md border border-green-200 dark:border-green-800">
                                    <div className="relative">
                                        <div className="w-4 h-4 border-2 border-green-600 border-t-transparent rounded-full animate-spin"></div>
                                    </div>
                                    <span className="text-sm text-green-700 dark:text-green-300 font-medium">Uploading favicon...</span>
                                </div>
                            )}
                        </div>
                    </>
                )
                }
            </div>

            {/* Full App Logo */}
            <div className="space-y-2">
                <Label>Full App Logo</Label>

                {/* Current Full App Logo Preview */}
                {(form.watch("fullAppLogoUrl") || form.getValues("fullAppLogoUrl")) ? (
                    <div className="flex items-center space-x-2 p-2 border rounded-md bg-muted/50">
                        <img
                            src={form.watch("fullAppLogoUrl") || form.getValues("fullAppLogoUrl")}
                            alt="Current full app logo"
                            className="h-8 w-8 object-contain"
                            onError={(e) => {
                                e.currentTarget.style.display = "none";
                            }}
                        />
                        <span className="text-sm text-muted-foreground flex-1 truncate">
                            Current full app logo
                        </span>
                        {isAdmin && (
                            <Button
                                type="button"
                                variant="ghost"
                                size="sm"
                                onClick={() => handleFileDelete("fullAppLogo")}
                                disabled={uploadingFullAppLogo}
                                className="h-6 w-6 p-0 text-destructive hover:text-destructive"
                            >
                                <X className="h-3 w-3" />
                            </Button>
                        )}
                    </div>
                ) : (
                    <>
                        {/* File Upload */}
                        <div className="flex items-center space-x-2 cursor-pointer">
                            <Input
                                type="file"
                                accept="image/*"
                                onChange={(e) => {
                                    const file = e.target.files?.[0];
                                    if (file) {
                                        setFullAppLogoFile(file);
                                        handleFileUpload(file, "fullAppLogo");
                                    }
                                }}
                                disabled={!isAdmin || uploadingFullAppLogo}
                                className="flex-1 cursor-pointer"
                            />
                            {uploadingFullAppLogo && (
                                <div className="flex items-center space-x-2 px-3 py-2 bg-purple-50 dark:bg-purple-950 rounded-md border border-purple-200 dark:border-purple-800">
                                    <div className="relative">
                                        <div className="w-4 h-4 border-2 border-purple-600 border-t-transparent rounded-full animate-spin"></div>
                                    </div>
                                    <span className="text-sm text-purple-700 dark:text-purple-300 font-medium">Uploading full app logo...</span>
                                </div>
                            )}
                        </div>
                    </>
                )
                }
            </div>
        </div>
    )
}

export default ThemeConfigLogo