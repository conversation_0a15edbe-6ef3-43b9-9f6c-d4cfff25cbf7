# Chart and Diagram Rendering Guide

## Overview

Swiss Knowledge Hub now supports comprehensive chart, graph, and diagram rendering in chat responses. The AI can automatically generate interactive visualizations to enhance data understanding and communication.

## Supported Visualization Types

### 📊 Charts and Graphs
- **Line Charts** - Trends and time series data
- **Bar Charts** - Comparisons and categorical data
- **Area Charts** - Volume and filled trends
- **Pie Charts** - Proportions and percentages
- **Scatter Plots** - Correlation analysis
- **Composed Charts** - Multiple chart types combined

### 🎨 Diagrams
- **Flowcharts** - Process flows and decision trees
- **Sequence Diagrams** - API flows and interactions
- **Gantt Charts** - Project timelines
- **Journey Maps** - User experience flows
- **Organization Charts** - Hierarchical structures

## How It Works

### Automatic Generation
The AI automatically detects when visualizations would be helpful and generates appropriate charts based on:
- **Data patterns** in your questions
- **Keywords** indicating visualization needs
- **Context** of the conversation
- **Available data** in responses

### Manual Requests
You can explicitly request charts by asking questions like:
- "Show me a chart of our sales data"
- "Create a flowchart for this process"
- "Visualize the comparison between departments"
- "Draw a diagram of the workflow"

## Example Interactions

### Business Analytics
**You:** "How did our revenue perform over the last 6 months?"

**AI Response:** 
Here's your revenue performance analysis:

```chart:line
{
  "data": [
    {"month": "Jan", "revenue": 120000},
    {"month": "Feb", "revenue": 135000},
    {"month": "Mar", "revenue": 148000},
    {"month": "Apr", "revenue": 162000},
    {"month": "May", "revenue": 155000},
    {"month": "Jun", "revenue": 178000}
  ],
  "config": {
    "title": "Monthly Revenue Performance",
    "xKey": "month",
    "yKey": "revenue"
  }
}
```

Your revenue shows a strong upward trend with 48% growth from January to June.

### Process Documentation
**You:** "Explain our customer onboarding process"

**AI Response:**
Here's our customer onboarding workflow:

```mermaid
graph TD
    A[New Customer Signup] --> B{Email Verification}
    B -->|Verified| C[Profile Setup]
    B -->|Not Verified| D[Send Reminder]
    D --> B
    C --> E[Document Upload]
    E --> F{Document Review}
    F -->|Approved| G[Account Activation]
    F -->|Rejected| H[Request Resubmission]
    H --> E
    G --> I[Welcome Email]
    I --> J[Onboarding Complete]
```

The process ensures proper verification before account activation.

### Comparative Analysis
**You:** "Compare our department performance"

**AI Response:**
Here's the department performance comparison:

```chart:bar
department,revenue,expenses,efficiency
Sales,450000,120000,78
Marketing,280000,95000,66
Engineering,520000,180000,74
Support,180000,65000,64
```

Engineering leads in revenue generation while maintaining good efficiency.

## Chart Syntax Reference

### Line Chart
```chart:line
{
  "data": [
    {"x": "Jan", "y": 100},
    {"x": "Feb", "y": 150}
  ],
  "config": {
    "title": "Chart Title",
    "xKey": "x",
    "yKey": "y"
  }
}
```

### Bar Chart (CSV Format)
```chart:bar
category,value1,value2
A,100,120
B,150,130
C,200,180
```

### Pie Chart
```chart:pie
{
  "data": [
    {"name": "Category A", "value": 35},
    {"name": "Category B", "value": 28},
    {"name": "Category C", "value": 37}
  ]
}
```

### Mermaid Flowchart
```mermaid
graph TD
    A[Start] --> B{Decision}
    B -->|Yes| C[Action 1]
    B -->|No| D[Action 2]
    C --> E[End]
    D --> E
```

### Mermaid Sequence Diagram
```mermaid
sequenceDiagram
    participant User
    participant System
    participant Database
    
    User->>System: Request
    System->>Database: Query
    Database-->>System: Results
    System-->>User: Response
```

## Interactive Features

### Chart Controls
- **Zoom** - Zoom in/out on chart data
- **Pan** - Navigate through large datasets
- **Hover** - View detailed data points
- **Legend** - Toggle data series visibility

### Diagram Controls
- **Zoom** - Zoom in/out on diagrams
- **Download** - Save as SVG or PNG
- **Copy** - Copy diagram code
- **Reset** - Return to original view

## Best Practices for Users

### Asking for Charts
- **Be specific** about the data you want visualized
- **Mention time periods** for trend analysis
- **Request comparisons** for bar/pie charts
- **Ask for processes** to get flowcharts

### Data Quality
- Provide **complete datasets** when possible
- Include **clear labels** and categories
- Specify **time ranges** for temporal data
- Mention **units of measurement**

### Examples of Good Requests
✅ "Show me quarterly sales trends for 2023"
✅ "Create a pie chart of our market share by region"
✅ "Draw a flowchart of the approval process"
✅ "Compare department budgets in a bar chart"

### Examples to Improve
❌ "Show me some data" → ✅ "Show me monthly user growth data"
❌ "Make a chart" → ✅ "Create a line chart of our revenue trends"
❌ "Visualize this" → ✅ "Create a flowchart of this process"

## Troubleshooting

### Charts Not Appearing
- Ensure your browser supports modern JavaScript
- Check if content blockers are interfering
- Refresh the page if charts don't load
- Try a different browser if issues persist

### Data Issues
- Verify data completeness in your request
- Check for consistent data formatting
- Ensure numerical data for quantitative charts
- Provide clear category labels

### Performance
- Large datasets may take longer to render
- Complex diagrams might need time to load
- Consider breaking large charts into smaller ones
- Use appropriate chart types for your data size

## Technical Details

### Supported Browsers
- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

### Data Formats
- **JSON** - Structured data objects
- **CSV-like** - Comma-separated values
- **Arrays** - Simple data arrays
- **Mixed** - Combination of formats

### Rendering Libraries
- **Recharts** - For charts and graphs
- **Mermaid** - For diagrams and flowcharts
- **Custom renderers** - For specialized visualizations

## Getting Help

If you encounter issues with chart rendering:

1. **Check the syntax** of your request
2. **Verify data completeness** and format
3. **Try different chart types** if one doesn't work
4. **Contact support** if problems persist

For more advanced chart customization or specific visualization needs, please reach out to your system administrator or the Swiss Knowledge Hub support team.

## Future Enhancements

Upcoming features include:
- **Real-time data updates** in charts
- **Advanced chart customization** options
- **Export capabilities** for presentations
- **Collaborative chart editing**
- **Integration with external data sources**
