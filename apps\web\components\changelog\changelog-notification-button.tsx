"use client";

import React from "react";
import { Button } from "@/components/ui/button";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Newspaper } from "lucide-react";
import { useLanguage } from "@/lib/language-context";

interface ChangelogNotificationButtonProps {
  size?: "sm" | "md" | "lg";
  variant?: "default" | "outline" | "ghost";
}

export const ChangelogNotificationButton: React.FC<
  ChangelogNotificationButtonProps
> = ({ size = "sm", variant = "ghost" }) => {
  const { t } = useLanguage();

  const handleClick = () => {
    window.open("/changelog", "_blank");
  };

  const buttonSize = size === "sm" ? "icon" : size === "md" ? "sm" : "default";
  const iconSize =
    size === "sm" ? "h-4 w-4" : size === "md" ? "h-5 w-5" : "h-6 w-6";

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant={variant}
            size={buttonSize}
            onClick={handleClick}
            className="relative"
          >
            <Newspaper className={iconSize} />
          </Button>
        </TooltipTrigger>
        <TooltipContent>{t("changelog.viewChangelog")}</TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};
