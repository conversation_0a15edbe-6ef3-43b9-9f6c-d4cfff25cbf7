"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { outlookDriveService } from "@/services/src/integration/microsoft";
import { Folder, ChevronRight, Loader2 } from "lucide-react";
import { useLanguage } from "@/lib/language-context";
import { getCookie } from "@/utils/cookies";

interface OneDriveFolderSelectorProps {
  tenantId: string;
  onFolderSelect: (folderId: string, folderName: string) => void;
  selectedFolderId?: string;
}

interface FolderItem {
  id: string;
  name: string;
  folder: any;
}

export function OneDriveFolderSelector({
  tenantId,
  onFolderSelect,
  selectedFolderId,
}: OneDriveFolderSelectorProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [folders, setFolders] = useState<FolderItem[]>([]);
  const [currentFolderId, setCurrentFolderId] = useState<string | undefined>(
    undefined
  );
  const [folderPath, setFolderPath] = useState<{ id: string; name: string }[]>([
    { id: "root", name: "My Drive" },
  ]);
  const [selectedFolder, setSelectedFolder] = useState<{
    id: string;
    name: string;
  } | null>(null);
  const { t } = useLanguage();
  const userId = getCookie("userId") ?? "";

  // Load folders when dialog opens or folder changes
  useEffect(() => {
    if (isOpen) {
      loadFolders(currentFolderId);
    }
  }, [isOpen, currentFolderId]);

  const loadFolders = async (parentId?: string) => {
    setIsLoading(true);
    try {
      const result = await outlookDriveService.listFolders({
        tenantId,
        parentFolderId: parentId,
        userId,
      });

      if (result.value) {
        // Filter to only include folders
        const folderItems = result.value.filter((item) => item.folder);
        setFolders(folderItems);
      } else {
        setFolders([]);
      }
    } catch (error) {
      console.error("Error loading folders:", error);
      setFolders([]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleFolderClick = (folder: FolderItem) => {
    setCurrentFolderId(folder.id);
    setFolderPath([...folderPath, { id: folder.id, name: folder.name }]);
  };

  const handleBreadcrumbClick = (index: number) => {
    const newPath = folderPath.slice(0, index + 1);
    setFolderPath(newPath);
    setCurrentFolderId(newPath[newPath.length - 1].id);
  };

  const handleSelectFolder = () => {
    if (currentFolderId) {
      const currentFolder = folderPath[folderPath.length - 1];
      setSelectedFolder({
        id: currentFolder.id,
        name: currentFolder.name,
      });
      onFolderSelect(currentFolder.id, currentFolder.name);
      setIsOpen(false);
    }
  };

  return (
    <div>
      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogTrigger asChild>
          <Button variant="outline" className="w-full justify-start gap-2">
            <Folder className="h-4 w-4" />
            {selectedFolder?.name || selectedFolderId
              ? selectedFolder?.name || "Selected Folder"
              : t("integration.selectFolder") || "Select OneDrive Folder"}
          </Button>
        </DialogTrigger>
        <DialogContent className="sm:max-w-[525px]">
          <DialogHeader>
            <DialogTitle>
              {t("integration.selectOneDriveFolder") ||
                "Select OneDrive Folder"}
            </DialogTitle>
          </DialogHeader>

          <div className="flex flex-col gap-4 py-4">
            {/* Breadcrumb navigation */}
            <div className="flex flex-wrap items-center gap-1 text-sm">
              {folderPath.map((folder, index) => (
                <div key={folder.id} className="flex items-center">
                  {index > 0 && <ChevronRight className="h-3 w-3 mx-1" />}
                  <button
                    onClick={() => handleBreadcrumbClick(index)}
                    className="hover:underline"
                  >
                    {folder.name}
                  </button>
                </div>
              ))}
            </div>

            {/* Folder list */}
            <div className="max-h-[300px] overflow-y-auto border rounded-md">
              {isLoading ? (
                <div className="flex items-center justify-center p-4">
                  <Loader2 className="h-5 w-5 animate-spin" />
                </div>
              ) : folders.length === 0 ? (
                <div className="p-4 text-center text-sm text-muted-foreground">
                  {t("integration.noFoldersFound") || "No folders found"}
                </div>
              ) : (
                <div className="divide-y">
                  {folders.map((folder) => (
                    <button
                      key={folder.id}
                      className="flex w-full items-center gap-2 p-2 hover:bg-muted text-left"
                      onClick={() => handleFolderClick(folder)}
                    >
                      <Folder className="h-4 w-4" />
                      <span>{folder.name}</span>
                    </button>
                  ))}
                </div>
              )}
            </div>

            <div className="flex justify-end gap-2">
              <Button variant="outline" onClick={() => setIsOpen(false)}>
                {t("common.cancel") || "Cancel"}
              </Button>
              <Button onClick={handleSelectFolder}>
                {t("common.select") || "Select"}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
