import { Message } from "../types";

/**
 * Processes messages to group regenerated messages with their original messages
 * @param messages Array of messages to process
 * @returns Processed messages with regenerated messages nested under their originals
 */
export const processMessages = (messages: Message[]): Message[] => {
  // Create a map to store message chains (original → regenerated → re-regenerated, etc.)
  const messageChains: Record<string, Message[]> = {};
  // Map to track which messages are regenerated versions of others
  const regenerationMap: Record<string, string> = {};
  // Set to track which messages are at the root of regeneration chains
  const rootMessages = new Set<string>();
  // Create a list to store the final processed messages
  const processedMessages: Message[] = [];

  // Map to preserve existing regenerated messages (including temporary ones)
  const existingRegeneratedMessages: Record<string, Message[]> = {};
  // Map to preserve existing edited messages (including temporary ones)
  const existingEditedMessages: Record<string, Message[]> = {};

  // First pass: preserve existing regenerated and edited messages from the UI state
  messages.forEach((message) => {
    if (message.id && message.regeneratedMessages) {
      existingRegeneratedMessages[message.id] = message.regeneratedMessages;
    }
    if (message.id && message.editedMessages) {
      existingEditedMessages[message.id] = message.editedMessages;
    }
  });

  // Second pass: identify regeneration chains from database messages (only for regenerated messages)
  messages.forEach((message) => {
    if (message.id) {
      if (message.originalMessageId && message.metadata?.regeneratedResponse) {
        // This is a regenerated message (not an edited message), track its relationship
        regenerationMap[message.id] = message.originalMessageId;

        // Find the root message of this regeneration chain
        let currentId = message.originalMessageId;
        while (regenerationMap[currentId]) {
          currentId = regenerationMap[currentId];
        }

        // Mark the root message
        rootMessages.add(currentId);

        // Add this message to its chain
        if (!messageChains[currentId]) {
          messageChains[currentId] = [];
        }
        messageChains[currentId].push(message);
      }
    }
  });

  // Third pass: add all original messages (no originalMessageId) to processed list
  messages.forEach((message) => {
    if (message.id && !message.originalMessageId) {
      // Only include original messages (not regenerated or edited messages)
      processedMessages.push(message);
    }
  });

  // Fourth pass: process each regeneration chain
  processedMessages.forEach((rootMessage) => {
    if (rootMessage.id && rootMessages.has(rootMessage.id)) {
      // This is a root message with regenerations
      const chain = messageChains[rootMessage.id] || [];

      // Sort all regenerated messages in the chain by creation time
      const sortedChain = [...chain].sort((a, b) => {
        if (!a.createdAt || !b.createdAt) return 0;
        return (
          new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
        );
      });

      // Organize the chain by regeneration level
      const regenerationLevels: Record<number, Message[]> = {};

      // Initialize level 0 with the root message
      regenerationLevels[0] = [rootMessage];

      // Process each regenerated message to determine its level
      sortedChain.forEach((message) => {
        if (message.originalMessageId) {
          // Find the level of the parent message
          let parentLevel = -1;

          // Check each level to find the parent
          Object.entries(regenerationLevels).forEach(([levelStr, messages]) => {
            const level = parseInt(levelStr);
            const foundParent = messages.find(
              (msg) => msg.id === message.originalMessageId
            );
            if (foundParent) {
              parentLevel = level;
            }
          });

          // If parent found, add this message to the next level
          if (parentLevel >= 0) {
            const nextLevel = parentLevel + 1;
            if (!regenerationLevels[nextLevel]) {
              regenerationLevels[nextLevel] = [];
            }
            regenerationLevels[nextLevel].push(message);
          }
        }
      });

      // Flatten the levels into a single array for the carousel
      const allRegeneratedMessages: Message[] = [];
      const maxLevel = Math.max(
        ...Object.keys(regenerationLevels).map((k) => parseInt(k))
      );

      // Start from level 1 (skip the root message which is at level 0)
      for (let level = 1; level <= maxLevel; level++) {
        if (regenerationLevels[level]) {
          // Add generation level to each message's metadata
          regenerationLevels[level].forEach((msg) => {
            // Add generation level to the message
            msg.metadata = {
              ...msg.metadata,
              generationLevel: level,
            };
            allRegeneratedMessages.push(msg);
          });
        }
      }

      // Update the root message with all regenerated messages
      const rootIndex = processedMessages.findIndex(
        (msg) => msg.id === rootMessage.id
      );
      if (rootIndex !== -1) {
        // Preserve existing regenerated messages (including temporary streaming ones)
        const existingRegenerated = existingRegeneratedMessages[rootMessage.id] || [];

        // Merge database regenerated messages with existing ones, avoiding duplicates
        const mergedRegeneratedMessages = [...allRegeneratedMessages];

        // Add any existing regenerated messages that are not in the database yet (e.g., temporary streaming messages)
        existingRegenerated.forEach((existingMsg) => {
          // Only add if it's not already in the merged list (check by ID or by being a temporary message)
          const isDuplicate = mergedRegeneratedMessages.some(
            (msg) => msg.id && existingMsg.id && msg.id === existingMsg.id
          );

          if (!isDuplicate) {
            mergedRegeneratedMessages.push(existingMsg);
          }
        });

        // Mark the root message as having regenerated versions
        processedMessages[rootIndex] = {
          ...processedMessages[rootIndex],
          metadata: {
            ...processedMessages[rootIndex].metadata,
            originalResponse: true,
          },
          regeneratedMessages: mergedRegeneratedMessages,
        };
      }
    }
  });

  // Fifth pass: process edited messages
  processedMessages.forEach((message, index) => {
    if (message.id) {
      // Find all edited messages for this original message from the database
      const editedMessages = messages.filter(
        (msg) => msg.originalMessageId === message.id && msg.metadata?.editedResponse
      );



      // Preserve existing edited messages (including temporary streaming ones)
      const existingEdited = existingEditedMessages[message.id] || [];

      if (editedMessages.length > 0 || existingEdited.length > 0) {
        // Sort database edited messages by creation time
        const sortedEditedMessages = [...editedMessages].sort((a, b) => {
          if (!a.createdAt || !b.createdAt) return 0;
          return (
            new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
          );
        });

        // Merge database edited messages with existing ones, avoiding duplicates
        const mergedEditedMessages = [...sortedEditedMessages];

        // Add any existing edited messages that are not in the database yet (e.g., temporary streaming messages)
        existingEdited.forEach((existingMsg) => {
          // Only add if it's not already in the merged list (check by ID or by being a temporary message)
          const isDuplicate = mergedEditedMessages.some(
            (msg) => msg.id && existingMsg.id && msg.id === existingMsg.id
          );

          if (!isDuplicate) {
            mergedEditedMessages.push(existingMsg);
          }
        });

        // Update the message with edited versions
        processedMessages[index] = {
          ...processedMessages[index],
          metadata: {
            ...processedMessages[index].metadata,
            originalResponse: true,
          },
          editedMessages: mergedEditedMessages,
        };
      }
    }
  });

  return processedMessages;
};

/**
 * Creates a temporary AI response message for edit-triggered generation
 * @returns A temporary message object
 */
export const createTemporaryAIResponseForEdit = (): Message => {
  return {
    role: "assistant",
    content: "",
    metadata: {
      editTriggeredResponse: true,
      isSelected: true,
      isStreaming: true,
    },
    sources: [],
  };
};

/**
 * Adds a temporary AI response message after an edit
 * @param messages Current messages array
 * @param afterIndex Index after which to insert the new message
 * @param tempMessage The temporary message to add
 * @returns Updated messages array
 */
export const addTemporaryAIResponseAfterEdit = (
  messages: Message[],
  afterIndex: number,
  tempMessage: Message
): Message[] => {
  const newMessages = [...messages];
  newMessages.splice(afterIndex + 1, 0, tempMessage);
  return newMessages;
};

/**
 * Updates a streaming AI response message triggered by edit
 * @param messages Current messages array
 * @param content New content to update with
 * @param sources Updated sources
 * @returns Updated messages array
 */
export const updateStreamingAIResponseForEdit = (
  messages: Message[],
  content: string,
  sources: any[]
): Message[] => {
  return messages.map((msg) => {
    // Find the temporary AI response message
    if (
      !msg.id &&
      msg.metadata?.editTriggeredResponse &&
      msg.metadata?.isStreaming
    ) {
      // Update the content and sources of the temporary message
      return {
        ...msg,
        content,
        sources,
        metadata: {
          ...msg.metadata,
          isStreaming: true, // Keep streaming flag during updates
        },
      };
    }
    return msg;
  });
};

/**
 * Replaces an existing assistant message with a new one after edit
 * @param messages Current messages array
 * @param targetAssistantMessage The assistant message to replace
 * @param newContent New content for the assistant message
 * @param sources New sources
 * @returns Updated messages array
 */
export const replaceAssistantMessageAfterEdit = (
  messages: Message[],
  targetAssistantMessage: Message,
  newContent: string,
  sources: any[]
): Message[] => {
  return messages.map((msg) => {
    if (msg.id === targetAssistantMessage.id) {
      // Create an edited version of the assistant message
      const editedAssistantMessage = {
        role: "assistant" as const,
        content: newContent,
        metadata: {
          editedResponse: true,
          isSelected: true,
        },
        originalMessageId: targetAssistantMessage.id,
        sources,
      };

      // Add the edited version to the original message
      const existingEditedMessages = msg.editedMessages || [];
      const updatedEditedMessages = [...existingEditedMessages, editedAssistantMessage];

      return {
        ...msg,
        metadata: {
          ...msg.metadata,
          originalResponse: true,
        },
        editedMessages: updatedEditedMessages,
      };
    }
    return msg;
  });
};

/**
 * Creates a temporary message for streaming
 * @param assistantMessage The original assistant message
 * @returns A temporary message object
 */
export const createTemporaryMessage = (assistantMessage: Message): Message => {
  return {
    role: "assistant",
    content: "",
    metadata: {
      regeneratedResponse: true,
      isSelected: true,
      isStreaming: true, // Mark this message as currently streaming
    },
    originalMessageId: assistantMessage.id,
    sources: [],
  };
};

/**
 * Adds a temporary message to the messages array
 * @param messages Current messages array
 * @param assistantMessage The original assistant message
 * @param tempMessage The temporary message to add
 * @returns Updated messages array
 */
export const addTemporaryMessage = (
  messages: Message[],
  assistantMessage: Message,
  tempMessage: Message
): Message[] => {
  return messages.map((msg) => {
    if (msg.id === assistantMessage.id) {
      // Get existing regenerated messages or initialize empty array
      const existingRegeneratedMessages = msg.regeneratedMessages || [];

      // Create a new array with the temporary message added
      const updatedRegeneratedMessages = [
        ...existingRegeneratedMessages,
        tempMessage,
      ];

      // Return the updated original message
      return {
        ...msg,
        metadata: {
          ...msg.metadata,
          originalResponse: true,
          isStreaming: true, // Add flag to indicate streaming is in progress
        },
        regeneratedMessages: updatedRegeneratedMessages,
      };
    }
    return msg;
  });
};

/**
 * Updates a streaming message with new content
 * @param messages Current messages array
 * @param assistantMessage The original assistant message
 * @param content New content to update with
 * @param sources Updated sources
 * @returns Updated messages array
 */
export const updateStreamingMessage = (
  messages: Message[],
  assistantMessage: Message,
  content: string,
  sources: any[]
): Message[] => {
  return messages.map((msg) => {
    // Find the original message
    if (msg.id === assistantMessage.id && msg.regeneratedMessages) {
      // Find and update the temporary message in the regeneratedMessages array
      const updatedRegeneratedMessages = msg.regeneratedMessages.map(
        (regMsg) => {
          // Find the temporary message by checking for missing ID and streaming flag
          if (
            !regMsg.id &&
            regMsg.metadata?.regeneratedResponse &&
            regMsg.metadata?.isStreaming
          ) {
            // Update the content and sources of the temporary message while preserving streaming state
            return {
              ...regMsg,
              content,
              sources,
              metadata: {
                ...regMsg.metadata,
                isStreaming: true, // Keep streaming flag during updates
              },
            };
          }
          return regMsg;
        }
      );

      // Return the updated original message
      return {
        ...msg,
        regeneratedMessages: updatedRegeneratedMessages,
      };
    }
    return msg;
  });
};
