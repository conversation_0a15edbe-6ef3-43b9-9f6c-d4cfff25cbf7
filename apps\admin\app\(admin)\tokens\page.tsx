import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/next-auth";
import { redirect } from "next/navigation";
import { TokensTableClient } from "./tokens-table-client";
import db from "@/lib/shared-db";

// Types for our data structures
interface TokenUsage {
  id: string;
  userId: string | null;
  userName: string;
  tenantId: string;
  tenantName: string;
  inputTokens: number;
  outputTokens: number;
  totalTokens: number;
  timestamp: string;
  requestType: string;
  modelUsed: string;
  cost: number;
}

interface MonthlyUsage {
  name: string;
  input: number;
  output: number;
  total: number;
}


interface ModelUsage {
  name: string;
  value: number;
}

interface TenantUsage {
  name: string;
  input: number;
  output: number;
  cost: number;
}

interface TokensData {
  tokenUsages: TokenUsage[];
  monthlyUsage: MonthlyUsage[];
  modelUsage: ModelUsage[];
  tenantUsage: TenantUsage[];
  allTanent: {
    id: string;
    name: string;
  }[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  searchQuery: string;
}

// Function to fetch token usage with manual joins to avoid Prisma relationship issues
async function fetchTokenUsageWithJoins(db: any) {
  try {

    // First, get all token usage records, selecting only the fields we need
    // This avoids issues with null values in fields we don't use
    const tokenUsages = await db.tokenUsage.findMany({
      select: {
        id: true,
        userId: true,
        tenantId: true,
        inputTokens: true,
        outputTokens: true,
        timestamp: true,
        requestType: true,
        modelUsed: true,
        cost: true,
      },
      orderBy: {
        timestamp: "desc",
      },
    });

    if (tokenUsages.length === 0) {
      return [];
    }

    // Get unique user and tenant IDs, filtering out null values
    const userIds = [...new Set(tokenUsages.map((t: any) => t.userId).filter(Boolean))];
    const tenantIds = [...new Set(tokenUsages.map((t: any) => t.tenantId).filter(Boolean))];

    // Fetch users and tenants that match our token usage references
    const [users, tenants] = await Promise.all([
      userIds.length > 0 ? db.user.findMany({
        where: { id: { in: userIds } },
        select: { id: true, name: true, email: true },
      }) : [],
      tenantIds.length > 0 ? db.tenant.findMany({
        where: { id: { in: tenantIds } },
        select: { id: true, name: true },
      }) : [],
    ]);

    // Create lookup maps for efficient joining
    const userMap = new Map(users.map((u: any) => [u.id, u]));
    const tenantMap = new Map(tenants.map((t: any) => [t.id, t]));

    // Process all token usages and provide placeholder data for missing relationships
    const allTokenUsages = tokenUsages
      .map((tokenUsage: any) => {
        const user = userMap.get(tokenUsage.userId);
        const tenant = tenantMap.get(tokenUsage.tenantId);

        // Provide placeholder data for missing relationships
        const placeholderUser = user || {
          id: tokenUsage.userId,
          name: tokenUsage.userId ? `Unknown User (${tokenUsage.userId.slice(-8)})` : "Anonymous",
          email: null
        };
        const placeholderTenant = tenant || {
          id: tokenUsage.tenantId,
          name: `Unknown Tenant (${tokenUsage.tenantId.slice(-8)})`
        };

        return {
          ...tokenUsage,
          user: placeholderUser,
          tenant: placeholderTenant,
        };
      });

    return allTokenUsages;
  } catch (error) {
    console.error('Error in fetchTokenUsageWithJoins:', error);
    return [];
  }
}

// Server-side data fetching function
async function fetchTokensData(
  searchQuery: string = "",
  page: number = 1,
  limit: number = 5,
  tenantId: string = ""
): Promise<TokensData> {
  try {


    // Fetch token usage with manual joins to handle relationship issues
    const allTokenUsages = await fetchTokenUsageWithJoins(db);
    const allTanent = await db.tenant.findMany({
      select: {
        id: true,
        name: true,
      },
    });

    if (allTokenUsages.length === 0) {
      return {
        tokenUsages: [],
        monthlyUsage: [],
        allTanent: [],
        modelUsage: [],
        tenantUsage: [],
        pagination: {
          page: 1,
          limit: 5,
          total: 0,
          totalPages: 0,
        },
        searchQuery: "",
      };
    }

    // Transform the data to match the expected structure
    let transformedTokenUsages = allTokenUsages.map((tokenUsage: any) => {
      const transformed = {
        id: tokenUsage.id,
        userId: tokenUsage.userId,
        userName: tokenUsage.user?.name || tokenUsage.user?.email || "Anonymous",
        tenantId: tokenUsage.tenantId,
        tenantName: tokenUsage.tenant?.name || "Unknown Tenant",
        inputTokens: tokenUsage.inputTokens || 0,
        outputTokens: tokenUsage.outputTokens || 0,
        totalTokens: (tokenUsage.inputTokens || 0) + (tokenUsage.outputTokens || 0),
        timestamp: tokenUsage.timestamp ? tokenUsage.timestamp.toISOString() : new Date().toISOString(),
        requestType: tokenUsage.requestType || "unknown",
        modelUsed: tokenUsage.modelUsed || "unknown",
        cost: tokenUsage.cost || 0,
      };
      return transformed;
    });

    // Apply search filter if search query is provided
    if (searchQuery && searchQuery.trim()) {
      const searchTerm = searchQuery.toLowerCase().trim();

      transformedTokenUsages = transformedTokenUsages.filter((tokenUsage: any) => {
        const userMatch = tokenUsage.userName.toLowerCase().includes(searchTerm);
        const tenantMatch = tokenUsage.tenantName.toLowerCase().includes(searchTerm);
        const requestTypeMatch = tokenUsage.requestType.toLowerCase().includes(searchTerm);
        const modelMatch = tokenUsage.modelUsed.toLowerCase().includes(searchTerm);

        return userMatch || tenantMatch || requestTypeMatch || modelMatch;
      });
    }

    // Apply tenant filter if tenant ID is provided
    if (tenantId) {
      transformedTokenUsages = transformedTokenUsages.filter(
        (usage: any) => usage.tenantName === tenantId
      );
    }

    // Get total count for pagination
    const total = transformedTokenUsages.length;

    // Apply pagination
    const startIndex = (page - 1) * limit;
    const paginatedTokenUsages = transformedTokenUsages.slice(startIndex, startIndex + limit);

    // Generate aggregation data for charts from real token usage data

    // Monthly usage aggregation
    const monthlyUsageMap = transformedTokenUsages.reduce((acc: any, usage: any) => {
      const date = new Date(usage.timestamp);
      const monthKey = date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });

      if (!acc[monthKey]) {
        acc[monthKey] = { input: 0, output: 0, total: 0 };
      }

      acc[monthKey].input += usage.inputTokens;
      acc[monthKey].output += usage.outputTokens;
      acc[monthKey].total += usage.totalTokens;

      return acc;
    }, {});

    const monthlyUsage = Object.entries(monthlyUsageMap)
      .map(([name, value]) => {
        const { input, output, total } = value as { input: number; output: number; total: number };
        return {
          name,
          input,
          output,
          total,
        };
      }) // Last 12 months

    // Model usage aggregation
    const modelUsageMap = transformedTokenUsages.reduce((acc: any, usage: any) => {
      acc[usage.modelUsed] = (acc[usage.modelUsed] || 0) + usage.totalTokens;
      return acc;
    }, {});

    const modelUsage = Object.entries(modelUsageMap)
      .map(([name, value]) => ({ name, value: value as number }))
      .sort((a, b) => b.value - a.value)
      .slice(0, 10); // Top 10 models

    // Tenant usage aggregation
    const tenantUsageMap = transformedTokenUsages.reduce((acc: any, usage: any) => {
      if (!acc[usage.tenantName]) {
        acc[usage.tenantName] = { input: 0, output: 0, cost: 0 };
      }
      acc[usage.tenantName].input += usage.inputTokens;
      acc[usage.tenantName].output += usage.outputTokens;
      acc[usage.tenantName].cost += usage.cost;
      return acc;
    }, {});

    const tenantUsage = Object.entries(tenantUsageMap)
      .map(([name, data]: [string, any]) => ({
        name,
        input: data.input,
        output: data.output,
        cost: data.cost
      }))
      .sort((a, b) => (b.input + b.output) - (a.input + a.output))
      .slice(0, 10); // Top 10 tenants

    // Calculate pagination info
    const totalPages = Math.ceil(total / limit);

    return {
      tokenUsages: paginatedTokenUsages,
      monthlyUsage,
      modelUsage,
      allTanent,
      tenantUsage,
      pagination: {
        page,
        limit,
        total,
        totalPages,
      },
      searchQuery,
    };
  } catch (error) {
    console.error('Error fetching tokens data:', error);
    // Return fallback data
    return {
      tokenUsages: [],
      monthlyUsage: [],
      modelUsage: [],
      allTanent:[],
      tenantUsage: [],
      pagination: {
        page: 1,
        limit: 5,
        total: 0,
        totalPages: 0,
      },
      searchQuery: "",
    };
  }
}

// Main server component
export default async function TokensPage({
  searchParams,
}: {
  searchParams: { search?: string; page?: string; limit?: string; tenant?: string };
}) {
  const session = await getServerSession(authOptions);
  if (!session) {
    redirect("/auth/signin");
  }

  // Parse search params with defaults
  const searchQuery = searchParams.search || "";
  const page = parseInt(searchParams.page || "1", 10);
  const limit = parseInt(searchParams.limit || "5", 10);
  const tenantId = searchParams.tenant || "";

  // Validate pagination parameters
  const validPage = Math.max(1, page);
  const validLimit = Math.min(Math.max(1, limit), 100); // Max 100 items per page

  // Fetch tokens data server-side
  const tokensData = await fetchTokensData(searchQuery, validPage, validLimit, tenantId);

  return <TokensTableClient data={tokensData} />;
}
