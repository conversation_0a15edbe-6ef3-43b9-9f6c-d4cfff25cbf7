from datetime import datetime, timed<PERSON>ta
from typing import Optional, Dict, Any
from bson import ObjectId
import tiktoken
import logging

logger = logging.getLogger(__name__)

class UsageTracker:
    def __init__(self, db_client):
        self.db_client = db_client
        self.encoding = tiktoken.get_encoding("cl100k_base")

    def count_tokens(self, text: str) -> int:
        """Count the number of tokens in a text string."""
        try:
            return len(self.encoding.encode(text))
        except Exception as e:
            logger.error(f"Error counting tokens: {e}")
            return 0

    async def log_token_usage(self,
        tenant_id: str,
        input_text: str,
        output_text: str,
        request_type: str,
        model_used: str,
        user_id: str=None,
    ):
        """Log token usage for a request."""
        try:
            input_tokens = self.count_tokens(input_text)
            output_tokens = self.count_tokens(output_text)

            # Calculate cost (example rates, adjust based on your models)
            cost_rates = {
                'gpt-4': {'input': 0.03, 'output': 0.06},
                'gpt-3.5-turbo': {'input': 0.0015, 'output': 0.002},
                'text-embedding-ada-002': {'input': 0.0001, 'output': 0.0001},
                'embed-v-4-0': {'input': 0.0001, 'output': 0.0001},
                'deepseek-coder': {'input': 0.002, 'output': 0.002},
                'deepseek-coder-v3': {'input': 0.0015, 'output': 0.0015}
            }

            rate = cost_rates.get(model_used, {'input': 0.0001, 'output': 0.0001})
            cost = (input_tokens * rate['input'] + output_tokens * rate['output']) / 1000
            payload = {
                'tenantId': ObjectId(tenant_id),
                'inputTokens': input_tokens,
                'outputTokens': output_tokens,
                'cost': cost,
                'modelUsed': model_used,
                'requestType': request_type,
                'timestamp': datetime.utcnow()
            }
            if user_id:
                payload['userId'] = ObjectId(user_id)


            await self.db_client.TokenUsage.insert_one(payload)

        except Exception as e:
            logger.error(f"Error logging token usage: {e}")

    async def log_api_request(self,
        user_id: str,
        tenant_id: str,
        endpoint: str,
        method: str,
        status_code: int,
        duration: int,
        error_message: Optional[str] = None
    ):
        """Log API request details."""
        try:
            await self.db_client.APIRequest.insert_one({
                'userId': ObjectId(user_id),
                'tenantId': ObjectId(tenant_id),
                'endpoint': endpoint,
                'method': method,
                'statusCode': status_code,
                'timestamp': datetime.utcnow(),
                'duration': duration,
                'success': 200 <= status_code < 300,
                'errorMessage': error_message
            })

        except Exception as e:
            logger.error(f"Error logging API request: {e}")

    async def get_user_usage_stats(self, user_id: str, start_date: datetime, end_date: datetime):
        """Get usage statistics for a user within a date range."""
        try:
            token_usage = await self.db_client.TokenUsage.aggregate([
                {
                    '$match': {
                        'userId': ObjectId(user_id),
                        'timestamp': {
                            '$gte': start_date,
                            '$lte': end_date
                        }
                    }
                },
                {
                    '$group': {
                        '_id': None,
                        'totalInputTokens': {'$sum': '$inputTokens'},
                        'totalOutputTokens': {'$sum': '$outputTokens'},
                        'totalCost': {'$sum': '$cost'},
                        'requestCount': {'$sum': 1}
                    }
                }
            ]).to_list(length=1)

            api_requests = await self.db_client.APIRequest.aggregate([
                {
                    '$match': {
                        'userId': ObjectId(user_id),
                        'timestamp': {
                            '$gte': start_date,
                            '$lte': end_date
                        }
                    }
                },
                {
                    '$group': {
                        '_id': None,
                        'totalRequests': {'$sum': 1},
                        'successfulRequests': {
                            '$sum': {'$cond': ['$success', 1, 0]}
                        },
                        'averageDuration': {'$avg': '$duration'}
                    }
                }
            ]).to_list(length=1)

            return {
                'token_usage': token_usage[0] if token_usage else None,
                'api_requests': api_requests[0] if api_requests else None
            }

        except Exception as e:
            logger.error(f"Error getting usage stats: {e}")
            return None

    async def get_tenant_usage_stats(self, tenant_id: str, start_date: datetime, end_date: datetime):
        """Get usage statistics for a tenant within a date range."""
        try:
            token_usage = await self.db_client.TokenUsage.aggregate([
                {
                    '$match': {
                        'tenantId': ObjectId(tenant_id),
                        'timestamp': {
                            '$gte': start_date,
                            '$lte': end_date
                        }
                    }
                },
                {
                    '$group': {
                        '_id': None,
                        'totalInputTokens': {'$sum': '$inputTokens'},
                        'totalOutputTokens': {'$sum': '$outputTokens'},
                        'totalCost': {'$sum': '$cost'},
                        'requestCount': {'$sum': 1},
                        'uniqueUsers': {'$addToSet': {'$toString': '$userId'}},
                        'uniqueWorkspaces': {'$addToSet': {'$toString': '$workspaceId'}}
                    }
                }
            ]).to_list(length=1)
            api_requests = await self.db_client.APIRequest.aggregate([
                {
                    '$match': {
                        'tenantId': ObjectId(tenant_id),
                        'timestamp': {
                            '$gte': start_date,
                            '$lte': end_date
                        }
                    }
                },
                {
                    '$group': {
                        '_id': None,
                        'totalRequests': {'$sum': 1},
                        'successfulRequests': {
                            '$sum': {'$cond': ['$success', 1, 0]}
                        },
                        'averageDuration': {'$avg': '$duration'},
                        'uniqueUsers': {'$addToSet': {'$toString': '$userId'}},
                        'uniqueWorkspaces': {'$addToSet': {'$toString': '$workspaceId'}}
                    }
                }
            ]).to_list(length=1)

            return {
                'token_usage': token_usage[0] if token_usage else None,
                'api_requests': api_requests[0] if api_requests else None
            }

        except Exception as e:
            logger.error(f"Error getting tenant usage stats: {e}")
            return None

    async def log_web_search_usage(self, tenant_id: str, user_id: str, query: str):
        """Log web search usage for a tenant."""
        try:
            await self.db_client.WebSearchUsage.insert_one({
                'tenantId': ObjectId(tenant_id),
                'userId': ObjectId(user_id),
                'query': query,
                'timestamp': datetime.utcnow()
            })
        except Exception as e:
            logger.error(f"Error logging web search usage: {e}")

    async def check_web_search_limit(self, tenant_id: str) -> Dict[str, Any]:
        """
        Check if a tenant has exceeded their daily web search limit.

        Args:
            tenant_id: The tenant ID

        Returns:
            Dictionary with limit information
        """
        try:
            # Get the tenant's subscription plan
            tenant = await self.db_client.Tenant.find_one({"_id": ObjectId(tenant_id)})

            if not tenant:
                return {
                    "limitExceeded": False,
                    "message": "Tenant not found"
                }

            # Get the subscription plan
            subscription_plan = tenant.get("subscriptionPlan", "free")

            # Define limits based on subscription plan
            plan_limits = {
                "free": 10,
                "basic": 50,
                "premium": 200,
                "enterprise": 1000
            }

            daily_limit = plan_limits.get(subscription_plan, 10)

            # Get today's usage
            today_start = datetime.utcnow().replace(hour=0, minute=0, second=0, microsecond=0)
            today_end = today_start + timedelta(days=1)

            # Count today's searches
            search_count = await self.db_client.WebSearchUsage.count_documents({
                'tenantId': ObjectId(tenant_id),
                'timestamp': {
                    '$gte': today_start,
                    '$lt': today_end
                }
            })

            # Check if limit is exceeded
            limit_exceeded = search_count >= daily_limit

            return {
                "limitExceeded": limit_exceeded,
                "currentCount": search_count,
                "dailyLimit": daily_limit,
                "subscriptionPlan": subscription_plan,
                "message": f"Daily web search limit exceeded: {search_count}/{daily_limit}" if limit_exceeded else None
            }
        except Exception as e:
            logger.error(f"Error checking web search limit: {e}")
            return {
                "limitExceeded": False,
                "error": str(e)
            }