import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/next-auth";
import db from "@/lib/shared-db";

export async function GET(request: Request) {
  const session = await getServerSession(authOptions);

  if (!session) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const { searchParams } = new URL(request.url);
    const period = searchParams.get("period") || "7days"; // '7days', '30days', '7months', etc.

    // Parse the period
    const match = period.match(/^(\d+)(days|months)$/);
    if (!match) {
      return NextResponse.json(
        {
          error: "Invalid period format. Use format like '7days' or '7months'",
        },
        { status: 400 }
      );
    }

    const amount = parseInt(match[1], 10);
    const unit = match[2];

    // Calculate the start date
    const startDate = new Date();
    if (unit === "days") {
      startDate.setDate(startDate.getDate() - amount);
    } else if (unit === "months") {
      startDate.setMonth(startDate.getMonth() - amount);
    }

    let result;

    if (unit === "days") {
      // Daily aggregation for days
      result = await db.tokenUsage.groupBy({
        by: ["timestamp"],
        where: {
          timestamp: {
            gte: startDate,
          },
        },
        _sum: {
          inputTokens: true,
          outputTokens: true,
          cost: true,
        },
      });

      // Format the result for daily view
      const dailyUsage = result.map((item) => ({
        name: item.timestamp.toISOString().split("T")[0], // Format as YYYY-MM-DD
        value: (item._sum.inputTokens || 0) + (item._sum.outputTokens || 0),
        cost: item._sum.cost || 0,
      }));

      return NextResponse.json({
        period,
        dailyUsage,
        totalTokens: dailyUsage.reduce((sum, item) => sum + item.value, 0),
        totalCost: dailyUsage.reduce((sum, item) => sum + item.cost, 0),
      });
    } else {
      // Monthly aggregation for months
      // This is more complex as we need to group by month
      const monthlyData: any = [];

      // Get all token usage in the period
      const tokenUsage = await db.tokenUsage.findMany({
        where: {
          timestamp: {
            gte: startDate,
          },
        },
        select: {
          timestamp: true,
          inputTokens: true,
          outputTokens: true,
          cost: true,
        },
      });

      // Group by month manually
      const monthlyGroups = {};

      tokenUsage.forEach((item) => {
        const monthYear = `${item.timestamp.getFullYear()}-${
          item.timestamp.getMonth() + 1
        }`;
        if (!monthlyGroups[monthYear]) {
          monthlyGroups[monthYear] = {
            inputTokens: 0,
            outputTokens: 0,
            cost: 0,
          };
        }

        monthlyGroups[monthYear].inputTokens += item.inputTokens || 0;
        monthlyGroups[monthYear].outputTokens += item.outputTokens || 0;
        monthlyGroups[monthYear].cost += item.cost || 0;
      });

      // Convert to array and format for chart
      const monthNames = [
        "Jan",
        "Feb",
        "Mar",
        "Apr",
        "May",
        "Jun",
        "Jul",
        "Aug",
        "Sep",
        "Oct",
        "Nov",
        "Dec",
      ];

      for (const [monthYear, data] of Object.entries(monthlyGroups)) {
        const [year, month] = monthYear.split("-").map(Number);
        const dataObj = data as any;
        monthlyData.push({
          name: monthNames[month - 1],
          value: dataObj.inputTokens + dataObj.outputTokens,
          cost: dataObj.cost,
        });
      }

      // Sort by date
      monthlyData.sort((a: any, b: any) => {
        const monthA = monthNames.indexOf(a.name);
        const monthB = monthNames.indexOf(b.name);
        return monthA - monthB;
      });

      return NextResponse.json({
        period,
        monthlyUsage: monthlyData,
        totalTokens: monthlyData.reduce(
          (sum: number, item: any) => sum + item.value,
          0
        ),
        totalCost: monthlyData.reduce(
          (sum: number, item: any) => sum + item.cost,
          0
        ),
      });
    }
  } catch (error) {
    console.error("Error fetching token usage metrics:", error);
    return NextResponse.json(
      { error: "Failed to fetch token usage metrics" },
      { status: 500 }
    );
  }
}
