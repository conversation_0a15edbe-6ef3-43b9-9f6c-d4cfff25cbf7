import { getChatIdOrCreate } from "@/services";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/next-auth";
import { cookies } from "next/headers";
import { redirect } from "next/navigation";

export default async function HomePage() {
  const session: any = await getServerSession(authOptions);
  const tenantId =
    cookies().get("currentOrganizationId")?.value ??
    session?.memberships[0]?.tenant?.id;
  const userId = session?.userId;

  const chatData = await getChatIdOrCreate({
    userId,
    tenantId,
  });
  const chatId = chatData?.id;

  return redirect(`/ask-ai/${chatId}`);
}
