"""
MCP Agent for Swiss Knowledge Hub

This agent integrates Model Context Protocol (MCP) servers with the CopilotKit chat system,
providing access to external tools and services through MCP.

Based on best practices for MCP client implementation with improved error handling,
resource management, and structured architecture.
"""

import asyncio
import json
import logging
import os
import shutil
from contextlib import AsyncExitStack
from typing import Any, AsyncGenerator, Dict, List, Optional
import aiohttp

from dotenv import load_dotenv
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client
from openai import AsyncAzureOpenAI
from typing import List, Dict

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)

load_dotenv()

logger = logging.getLogger(__name__)

# Translation dictionary for localized messages
TRANSLATIONS = {
    "en": {
        "no_server_connected": "No MCP server connected",
        "query_processing_failed": "Query processing failed",
        "tool_execution": "Executing tool: {tool_name}",
        "tool_error": "Error executing tool: {tool_name}",
        "final_response_failed": "Final response failed",
        "connecting_server": "Connecting to MCP server...",
        "health_check": "Checking server health...",
        "reconnecting": "Reconnecting to MCP server...",
        "reconnection_failed": "Reconnection failed",
        "processing_response": "Processing response...",
        "operation_completed": "MCP operation completed",
        "operation_failed": "MCP operation failed",
        "fetching_all_tools": "Fetching all tools from {count} servers...",
        "status_unknown": "MCP status unknown"
    },
    "de": {
        "no_server_connected": "Kein MCP-Server verbunden",
        "query_processing_failed": "Anfrageverarbeitung fehlgeschlagen",
        "tool_execution": "Tool wird ausgeführt: {tool_name}",
        "tool_error": "Fehler bei Tool-Ausführung: {tool_name}",
        "final_response_failed": "Finale Antwort fehlgeschlagen",
        "connecting_server": "Verbindung zu MCP-Server wird hergestellt...",
        "health_check": "Server-Statusprüfung...",
        "reconnecting": "Verbindung zu MCP-Server wird wiederhergestellt...",
        "reconnection_failed": "Wiederverbindung fehlgeschlagen",
        "processing_response": "Antwort wird verarbeitet...",
        "operation_completed": "MCP-Vorgang abgeschlossen",
        "operation_failed": "MCP-Vorgang fehlgeschlagen",
        "fetching_all_tools": "Alle Tools von {count} Servern werden abgerufen...",
        "status_unknown": "MCP-Status unbekannt"
    }
}

def get_translation(key: str, language: str = "en", **kwargs) -> str:
    """Get translated message with optional formatting parameters."""
    try:
        message = TRANSLATIONS.get(language, TRANSLATIONS["en"]).get(key, key)
        return message.format(**kwargs) if kwargs else message
    except (KeyError, ValueError):
        return TRANSLATIONS["en"].get(key, key)


class Configuration:
    """Manages configuration and environment variables for the MCP client."""

    def __init__(self) -> None:
        """Initialize configuration with environment variables."""
        self.load_env()
        self.api_key = os.getenv("DEEPSEEK_API_KEY")

    @staticmethod
    def load_env() -> None:
        """Load environment variables from .env file."""
        load_dotenv()

    @staticmethod
    def load_config(file_path: str) -> dict[str, Any]:
        """Load server configuration from JSON file.

        Args:
            file_path: Path to the JSON configuration file.

        Returns:
            Dict containing server configuration.

        Raises:
            FileNotFoundError: If configuration file doesn't exist.
            JSONDecodeError: If configuration file is invalid JSON.
        """
        with open(file_path, "r") as f:
            return json.load(f)

    @property
    def azure_api_key(self) -> str:
        """Get the Azure OpenAI API key.

        Returns:
            The API key as a string.

        Raises:
            ValueError: If the API key is not found in environment variables.
        """
        if not self.api_key:
            raise ValueError("DEEPSEEK_API_KEY not found in environment variables")
        return self.api_key


class Tool:
    """Represents a tool with its properties and formatting."""

    def __init__(
        self,
        name: str,
        description: str,
        input_schema: dict[str, Any],
        title: str | None = None,
    ) -> None:
        self.name: str = name
        self.title: str | None = title
        self.description: str = description
        self.input_schema: dict[str, Any] = input_schema

    def format_for_llm(self) -> str:
        """Format tool information for LLM.

        Returns:
            A formatted string describing the tool.
        """
        args_desc = []
        if "properties" in self.input_schema:
            for param_name, param_info in self.input_schema["properties"].items():
                arg_desc = (
                    f"- {param_name}: {param_info.get('description', 'No description')}"
                )
                if param_name in self.input_schema.get("required", []):
                    arg_desc += " (required)"
                args_desc.append(arg_desc)

        # Build the formatted output with title as a separate field
        output = f"Tool: {self.name}\n"

        # Add human-readable title if available
        if self.title:
            output += f"User-readable title: {self.title}\n"

        output += f"""Description: {self.description}
Arguments:
{chr(10).join(args_desc)}
"""

        return output



class HTTPServerTransport:
    """HTTP transport for MCP servers using SSE."""

    def __init__(self, url: str, headers: Optional[Dict[str, str]] = None):
        self.url = url
        self.headers = headers or {}
        self.session: Optional[aiohttp.ClientSession] = None
        self._read_queue: asyncio.Queue = asyncio.Queue()
        self._write_queue: asyncio.Queue = asyncio.Queue()
        self._sse_task: Optional[asyncio.Task] = None
        self._writer_task: Optional[asyncio.Task] = None
        self._closed = False

    async def __aenter__(self):
        """Async context manager entry."""
        self.session = aiohttp.ClientSession()

        # Start writer task (no SSE reader needed for direct HTTP)
        self._writer_task = asyncio.create_task(self._writer())

        # Return read/write stream objects
        return HTTPReadStream(self._read_queue), HTTPWriteStream(self._write_queue)

    async def __aexit__(self, exc_type=None, exc_val=None, exc_tb=None):
        """Async context manager exit."""
        self._closed = True

        # Signal shutdown
        await self._write_queue.put(None)

        # Cancel writer task
        if self._writer_task:
            self._writer_task.cancel()
            try:
                await self._writer_task
            except asyncio.CancelledError:
                pass

        if self.session:
            await self.session.close()


    async def _writer(self):
        """Write messages to the HTTP server."""
        logging.debug("HTTP writer task started")
        try:
            while not self._closed:
                logging.debug("Waiting for message in write queue...")
                message = await self._write_queue.get()
                if message is None:  # Shutdown signal
                    logging.debug("Received shutdown signal")
                    break

                logging.debug(f"Sending HTTP POST to {self.url} with message: {message}")

                # Send HTTP POST request with the message
                async with self.session.post(
                    self.url,
                    json=message,
                    headers=self.headers
                ) as response:
                    logging.debug(f"HTTP response status: {response.status}")
                    if response.status == 200:
                        # Parse response and put it in read queue
                        try:
                            response_data = await response.json()
                            logging.debug(f"Received response: {response_data}")
                            await self._read_queue.put(response_data)
                        except Exception as e:
                            logging.error(f"Failed to parse HTTP response: {e}")
                    else:
                        logging.warning(f"HTTP write failed with status {response.status}")
                        response_text = await response.text()
                        logging.warning(f"Response: {response_text}")
        except Exception as e:
            if not self._closed:
                logging.error(f"HTTP writer error: {e}")
        finally:
            logging.debug("HTTP writer task ended")


class HTTPReadStream:
    """Read stream for HTTP transport that implements the MCP message interface."""

    def __init__(self, queue: asyncio.Queue):
        self._queue = queue

    async def receive(self):
        """Receive a message from the queue."""
        message = await self._queue.get()
        if message is None:
            raise EOFError("Stream closed")
        return message

    def __aiter__(self):
        return self

    async def __anext__(self):
        try:
            message = await self.receive()
            return message
        except EOFError:
            raise StopAsyncIteration


class HTTPWriteStream:
    """Write stream for HTTP transport that implements the MCP message interface."""

    def __init__(self, queue: asyncio.Queue):
        self._queue = queue

    async def send(self, message) -> None:
        """Send a message to the queue."""
        logging.debug(f"HTTPWriteStream.send called with: {type(message)}")

        # Handle SessionMessage objects from MCP library
        if hasattr(message, 'message'):
            # Extract the actual JSON-RPC message
            actual_message = message.message
            if hasattr(actual_message, 'model_dump'):
                message_dict = actual_message.model_dump()
            elif hasattr(actual_message, 'dict'):
                message_dict = actual_message.dict()
            else:
                message_dict = actual_message
        elif hasattr(message, 'model_dump'):
            message_dict = message.model_dump()
        elif hasattr(message, 'dict'):
            message_dict = message.dict()
        else:
            message_dict = message

        logging.debug(f"Sending message to queue: {message_dict}")
        await self._queue.put(message_dict)

    async def aclose(self):
        """Close the write stream."""
        await self._queue.put(None)


class HTTPMCPSession:
    """Mock MCP session for HTTP-based servers."""

    def __init__(self, http_client: aiohttp.ClientSession, url: str, headers: Dict[str, str]):
        self.http_client = http_client
        self.url = url
        self.headers = headers
        self._request_id = 0

    def _get_next_id(self) -> int:
        """Get next request ID."""
        self._request_id += 1
        return self._request_id

    async def _send_request(self, method: str, params: Dict[str, Any] = None) -> Dict[str, Any]:
        """Send HTTP request to MCP server."""
        request_data = {
            "jsonrpc": "2.0",
            "id": self._get_next_id(),
            "method": method
        }

        if params:
            request_data["params"] = params

        logging.debug(f"Sending HTTP MCP request: {request_data}")

        # Prepare headers with required Accept header for MCP servers
        headers = dict(self.headers) if self.headers else {}
        headers["Accept"] = "application/json, text/event-stream"
        headers["Content-Type"] = "application/json"

        try:
            async with self.http_client.post(
                self.url,
                json=request_data,
                headers=headers,
                timeout=aiohttp.ClientTimeout(total=30)
            ) as response:
                if response.status == 200:
                    content_type = response.headers.get('content-type', '').lower()
                    logging.debug(f"Response content-type: {content_type}")

                    if 'text/event-stream' in content_type:
                        # Handle Server-Sent Events response
                        response_text = await response.text()
                        logging.debug(f"Received SSE response: {response_text}")

                        # Parse SSE data - look for JSON data in the stream
                        lines = response_text.strip().split('\n')
                        for line in lines:
                            if line.startswith('data: '):
                                data_content = line[6:]  # Remove 'data: ' prefix
                                if data_content and data_content != '[DONE]':
                                    try:
                                        response_data = json.loads(data_content)
                                        logging.debug(f"Parsed SSE JSON: {response_data}")

                                        if "error" in response_data:
                                            raise Exception(f"MCP error: {response_data['error']}")

                                        return response_data.get("result", response_data)
                                    except json.JSONDecodeError:
                                        continue

                        # If no valid JSON found in SSE, return empty result
                        logging.warning("No valid JSON data found in SSE response")
                        return {}
                    elif 'application/json' in content_type:
                        # Handle regular JSON response
                        try:
                            response_data = await response.json()
                            logging.debug(f"Received HTTP MCP response: {response_data}")

                            if "error" in response_data:
                                raise Exception(f"MCP error: {response_data['error']}")

                            return response_data.get("result", {})
                        except Exception as json_error:
                            logging.error(f"Failed to parse JSON response: {json_error}")
                            # Fallback to text parsing
                            response_text = await response.text()
                            logging.debug(f"Raw response text: {response_text}")
                            return {}
                    else:
                        # Handle other content types as text
                        response_text = await response.text()
                        logging.warning(f"Unexpected content type {content_type}, response: {response_text}")
                        return {}
                else:
                    response_text = await response.text()
                    raise Exception(f"HTTP request failed with status {response.status}: {response_text}")

        except Exception as e:
            logging.error(f"HTTP MCP request error: {e}")
            raise

    async def initialize(self) -> None:
        """Initialize the MCP session."""
        try:
            result = await self._send_request("initialize", {
                "protocolVersion": "2024-11-05",
                "capabilities": {},
                "clientInfo": {
                    "name": "swiss-knowledge-hub",
                    "version": "1.0.0"
                }
            })
            logging.info(f"MCP session initialized: {result}")
        except Exception as e:
            logging.warning(f"MCP initialize failed (may not be supported): {e}")

    async def list_tools(self):
        """List available tools."""
        try:
            result = await self._send_request("tools/list")
            return result.get("tools", [])

        except Exception as e:
            logging.error(f"Failed to list tools: {e}")
            return []

    async def call_tool(self, name: str, arguments: Dict[str, Any]):
        """Call a tool."""
        try:
            result = await self._send_request("tools/call", {
                "name": name,
                "arguments": arguments
            })

            return result

        except Exception as e:
            logging.error(f"Failed to call tool {name}: {e}")
            raise

    async def close(self) -> None:
        """Close the session."""
        # HTTP sessions don't need explicit closing
        pass


class Server:
    """Manages MCP server connections and tool execution."""

    def __init__(self, name: str, config: dict[str, Any]) -> None:
        self.name: str = name
        self.config: dict[str, Any] = config
        self.stdio_context: Any | None = None
        self.session: ClientSession | None = None
        self._cleanup_lock: asyncio.Lock = asyncio.Lock()
        self.exit_stack: AsyncExitStack = AsyncExitStack()
        self.server_type: str = config.get("server_type", "stdio")

    async def initialize(self) -> None:
        """Initialize the server connection."""
        logging.info(f"Initializing server {self.name} with type: {self.server_type}")

        if self.server_type.lower() == "http":
            logging.info(f"Using HTTP initialization for {self.name}")
            await self._initialize_http()
        else:
            logging.info(f"Using STDIO initialization for {self.name}")
            await self._initialize_stdio()

    async def _initialize_stdio(self) -> None:
        """Initialize stdio-based server connection."""
        command = (
            shutil.which("npx")
            if self.config.get("command") == "npx"
            else self.config.get("command")
        )
        if command is None:
            raise ValueError("The command must be a valid string and cannot be None.")

        server_params = StdioServerParameters(
            command=command,
            args=self.config["args"],
            env={**os.environ, **self.config["env"]}
            if self.config.get("env")
            else None,
        )
        try:
            stdio_transport = await self.exit_stack.enter_async_context(
                stdio_client(server_params)
            )
            read, write = stdio_transport
            session = await self.exit_stack.enter_async_context(
                ClientSession(read, write)
            )
            await session.initialize()
            self.session = session
        except Exception as e:
            logging.error(f"Error initializing stdio server {self.name}: {e}")
            await self.cleanup()
            raise

    async def _initialize_http(self) -> None:
        """Initialize HTTP-based server connection."""
        url = self.config.get("url")
        if not url:
            raise ValueError("HTTP server requires a URL")

        headers = self.config.get("headers", {})

        try:
            logging.info(f"Initializing HTTP server {self.name} at {url}")
            logging.debug(f"Headers: {list(headers.keys()) if headers else 'None'}")

            # Create HTTP client for direct requests
            self.http_client = aiohttp.ClientSession()
            self.http_url = url
            self.http_headers = headers

            # Test the connection with a simple request
            await self._test_http_connection()

            # Create a mock session for HTTP servers since they don't use the standard MCP transport
            self.session = HTTPMCPSession(self.http_client, self.http_url, self.http_headers)

            logging.info(f"HTTP server {self.name} initialized successfully")

        except Exception as e:
            logging.error(f"Error initializing HTTP server {self.name}: {e}")
            logging.exception("Full traceback:")
            await self.cleanup()
            raise

    async def _test_http_connection(self) -> None:
        """Test HTTP connection with a simple request."""
        # Prepare headers with required Accept header for MCP servers
        headers = dict(self.http_headers) if self.http_headers else {}
        headers["Accept"] = "application/json, text/event-stream"

        try:
            # Try a simple GET request first
            async with self.http_client.get(
                self.http_url,
                headers=headers,
                timeout=aiohttp.ClientTimeout(total=10)
            ) as response:
                logging.info(f"HTTP GET test: {response.status}")

        except Exception as e:
            logging.warning(f"HTTP GET test failed: {e}")

            # Try a POST request (more likely for MCP)
            try:
                test_message = {
                    "jsonrpc": "2.0",
                    "id": 1,
                    "method": "ping"
                }

                # Add Content-Type for POST requests
                post_headers = dict(headers)
                post_headers["Content-Type"] = "application/json"

                async with self.http_client.post(
                    self.http_url,
                    json=test_message,
                    headers=post_headers,
                    timeout=aiohttp.ClientTimeout(total=10)
                ) as response:
                    logging.info(f"HTTP POST test: {response.status}")
                    response_text = await response.text()
                    logging.debug(f"Response: {response_text}")

            except Exception as e2:
                logging.warning(f"HTTP POST test also failed: {e2}")
                # Don't fail initialization for now, just log the issue

    async def list_tools(self) -> list[Any]:
        """List available tools from the server.

        Returns:
            A list of available tools.

        Raises:
            RuntimeError: If the server is not initialized.
        """
        if not self.session:
            raise RuntimeError(f"Server {self.name} not initialized")

        tools_response = await self.session.list_tools()
        tools = []

        # Handle HTTP MCP session response
        if isinstance(self.session, HTTPMCPSession):
            # tools_response is a list of tool dictionaries
            for tool_data in tools_response:
                tools.append(Tool(
                    name=tool_data.get("name", "unknown"),
                    description=tool_data.get("description", ""),
                    input_schema=tool_data.get("inputSchema", {}),
                    title=tool_data.get("title", tool_data.get("name", "unknown"))
                ))
        else:
            # Handle standard MCP session response
            for item in tools_response:
                if isinstance(item, tuple) and item[0] == "tools":
                    tools.extend(
                        Tool(
                            name=tool.name,
                            description=tool.description,
                            input_schema=tool.inputSchema,
                            title=getattr(tool, 'title', None)
                        )
                        for tool in item[1]
                    )

        return tools

    async def execute_tool(
        self,
        tool_name: str,
        arguments: dict[str, Any],
        retries: int = 2,
        delay: float = 1.0,
    ) -> Any:
        """Execute a tool with retry mechanism.

        Args:
            tool_name: Name of the tool to execute.
            arguments: Tool arguments.
            retries: Number of retry attempts.
            delay: Delay between retries in seconds.

        Returns:
            Tool execution result.

        Raises:
            RuntimeError: If server is not initialized.
            Exception: If tool execution fails after all retries.
        """
        if not self.session:
            raise RuntimeError(f"Server {self.name} not initialized")

        attempt = 0
        while attempt < retries:
            try:
                logging.info(f"Executing {tool_name}...")
                result = await self.session.call_tool(tool_name, arguments)

                return result

            except Exception as e:
                attempt += 1
                logging.warning(
                    f"Error executing tool: {e}. Attempt {attempt} of {retries}."
                )
                if attempt < retries:
                    logging.info(f"Retrying in {delay} seconds...")
                    await asyncio.sleep(delay)
                else:
                    logging.error("Max retries reached. Failing.")
                    raise

    async def cleanup(self) -> None:
        """Clean up server resources."""
        async with self._cleanup_lock:
            try:
                # Clean up HTTP client if it exists
                if hasattr(self, 'http_client') and self.http_client:
                    await self.http_client.close()
                    self.http_client = None

                await self.exit_stack.aclose()
                self.session = None
                self.stdio_context = None
            except Exception as e:
                logging.error(f"Error during cleanup of server {self.name}: {e}")


class MCPAgent:
    """
    MCP Agent that connects to MCP servers and provides tool execution capabilities
    for the CopilotKit chat system.

    Enhanced with best practices for resource management, error handling, and tool execution.
    """

    def __init__(self, db_client=None):
        self.db_client = db_client
        self.config = Configuration()
        self.server: Optional[Server] = None
        self.available_tools = []
        self.server_info = {}
        self.tool_name_mapping = {}  # Maps shortened names to original names
        self._last_server_config = None  # Store config for reconnection
        self._last_connection_error = None  # Store last connection error for debugging

        # Initialize Azure OpenAI client
        self.azure_client = AsyncAzureOpenAI(
            api_key=self.config.azure_api_key,
            api_version=os.getenv("AZURE_OPENAI_API_VERSION", "2024-08-01-preview"),
            azure_endpoint=os.getenv("AZURE_OPENAI_ENDPOINT"),
            azure_deployment=os.getenv("AZURE_VISION_MODEL", "gpt-4o")
        )

        logger.info("MCP Agent initialized with enhanced architecture")

    def _is_identity_question(self, query: str) -> bool:
        """Check if the query is asking about the AI's identity or creator."""
        query_lower = query.lower().strip()
        identity_patterns = [
            # English patterns
            "who made you", "who made u", "who created you", "who created u",
            "who built you", "who built u", "who developed you", "who developed u",
            "what are you", "what are u", "who are you", "who are u",
            "what is your name", "who is your creator", "who is ur creator",
            "who designed you", "who designed u", "what company made you", "what company made u",
            "who owns you", "who owns u", "where do you come from", "where do u come from",
            "what organization created you", "what organization created u",
            "who is behind you", "who is behind u", "who programmed you", "who programmed u",
            "who trained you", "who trained u", "who's your creator", "whos your creator",

            # German patterns
            "wer hat dich gemacht", "wer hat dich erstellt", "wer hat dich entwickelt", "wer hat dich erschaffen",
            "von wem wurdest du gemacht", "von wem wurdest du erstellt", "von wem wurdest du entwickelt", "von wem wurdest du erschaffen",
            "wer bist du", "was bist du", "wie heißt du", "wie heisst du",
            "wer ist dein ersteller", "wer ist dein schöpfer", "wer ist dein entwickler", "wer ist dein erschaffer",
            "welche firma hat dich gemacht", "welche organisation hat dich erstellt", "welche firma hat dich erschaffen",
            "wer steht hinter dir", "wer hat dich programmiert", "wer hat dich trainiert",
            "von welcher firma kommst du", "von welcher organisation kommst du",
            "wer besitzt dich", "wem gehörst du", "woher kommst du"
        ]

        is_identity = any(pattern in query_lower for pattern in identity_patterns)
        logger.info(f"MCP IDENTITY CHECK: Query='{query}' | Lower='{query_lower}' | IsIdentity={is_identity}")
        if is_identity:
            matched_pattern = next((pattern for pattern in identity_patterns if pattern in query_lower), "unknown")
            logger.info(f"MCP IDENTITY MATCH: Pattern='{matched_pattern}'")
        return is_identity

    def _detect_question_language(self, query: str) -> str:
        """Detect the language of the question based on content."""
        query_lower = query.lower().strip()

        # German language indicators
        german_indicators = [
            "wer", "was", "wie", "wo", "warum", "wann", "welche", "welcher", "welches",
            "von wem", "hat dich", "bist du", "sind sie", "können sie", "ist dein",
            "heißt", "heisst", "gemacht", "erstellt", "entwickelt", "erschaffen",
            "firma", "organisation", "unternehmen", "schöpfer", "entwickler", "ersteller"
        ]

        # Count German indicators
        german_count = sum(1 for indicator in german_indicators if indicator in query_lower)

        # If we find German indicators and it's an identity question, assume German
        if german_count > 0 and self._is_identity_question(query):
            logger.info(f"MCP LANGUAGE DETECTION: Detected German in identity question: '{query}' (indicators: {german_count})")
            return "de"

        # Default to English
        return "en"

    def _get_identity_response(self, language: str = "en") -> str:
        """Get the appropriate identity response based on language."""
        if language == "de":
            return "Ich bin ein KI-Assistent von Swiss Knowledge Hub, der entwickelt wurde, um Ihnen bei der Suche nach Informationen und der Beantwortung von Fragen innerhalb der Wissensdatenbank Ihrer Organisation zu helfen. Ich bin hier, um Sie bei Ihren Anfragen zu unterstützen und hilfreiche Erkenntnisse basierend auf den Daten und Ressourcen Ihres Unternehmens zu liefern."
        else:
            return "I am an AI assistant created by Swiss Knowledge Hub, designed to help you find information and answer questions within your organization's knowledge base. I'm here to assist with your queries and provide helpful insights based on your company's data and resources."

    def get_markdown_system_message(self) -> Dict[str, str]:
        """Get the system message for Markdown formatting"""
        return {
            "role": "system",
            "content": """
            You are an AI assistant created by Swiss Knowledge Hub that helps users by using available tools to gather information and provide comprehensive answers.

                IMPORTANT IDENTITY INSTRUCTIONS:
                - You are created by Swiss Knowledge Hub, NOT by DeepSeek, OpenAI, or any other company
                - When asked about your identity, creator, or who made you, ALWAYS respond that you are created by Swiss Knowledge Hub
                - Your purpose is to help users within their organization's knowledge base
                - Never claim to be created by DeepSeek, OpenAI, Anthropic, or any other AI company

                Generate a comprehensive, accurate, and helpful response in the same language as the user's question
                - Respond in the same language as the user's question (English, Spanish, French, German, Chinese, Japanese, etc.)
                IMPORTANT: Format all your responses using proper Markdown syntax:
                - Use headers (# ## ###) to organize content
                - Use bullet points and numbered lists for clarity
                - Use code blocks (```) for code snippets
                - Use **bold** and *italic* for emphasis
                - Use tables when presenting structured data
                - Use blockquotes (>) for important notes
                - Ensure proper spacing and formatting for readability

                When using tools, explain what you're doing and present the results in a well-structured, readable format."""
            }

    async def connect_to_server(self, server_config: Dict[str, Any]) -> bool:
        """
        Connect to an MCP server using the enhanced Server class

        Args:
            server_config: Dictionary containing server configuration
                For STDIO servers:
                - command: Server command/script path
                - args: Command arguments
                - env: Environment variables
                For HTTP servers:
                - url: Server URL
                - headers: HTTP headers (e.g., Authorization)
                Common:
                - server_type: "stdio" or "http" (defaults to "stdio")
                - name: Server name
                - description: Server description

        Returns:
            bool: True if connection successful, False otherwise
        """
        try:
            # Store server config for potential reconnection
            self._last_server_config = server_config.copy()
            server_name = server_config.get("name", "Unknown")

            logger.info(f"Attempting to connect to MCP server '{server_name}'")

            # Disconnect from any existing server first
            await self.disconnect()

            # Create new server instance
            self.server = Server(server_name, server_config)

            # Initialize the server connection
            await self.server.initialize()

            # Get available tools
            try:
                tools = await self.server.list_tools()
                self.available_tools = tools
                logger.info(f"Retrieved {len(self.available_tools)} tools from MCP server")
            except Exception as e:
                logger.error(f"Failed to list tools from MCP server: {e}")
                self.available_tools = []

            # Store server info
            self.server_info = {
                "name": server_config.get("name", "Unknown"),
                "description": server_config.get("description", ""),
                "command": server_config.get("command"),
                "tools": [tool.name for tool in self.available_tools],
                "tools_count": len(self.available_tools)
            }

            logger.info(f"Connected to MCP server '{self.server_info['name']}' with {len(self.available_tools)} tools: {self.server_info['tools']}")

            # Log detailed tool information for debugging
            for tool in self.available_tools:
                logger.debug(f"Available tool: {tool.name} - {tool.description}")

            return True

        except Exception as e:
            error_msg = f"{type(e).__name__}: {e}"
            logger.error(f"Failed to connect to MCP server '{server_name}': {error_msg}")
            self._last_connection_error = error_msg

            # Clean up on failure
            if self.server:
                try:
                    await self.server.cleanup()
                except Exception as cleanup_error:
                    logger.warning(f"Error during cleanup after connection failure: {cleanup_error}")
                self.server = None

            return False

    async def disconnect(self):
        """Disconnect from the MCP server and clean up resources"""
        try:
            if self.server:
                await self.server.cleanup()
                self.server = None

            self.available_tools = []
            self.server_info = {}
            self.tool_name_mapping = {}
            logger.info("Disconnected from MCP server")
        except Exception as e:
            logger.error(f"Error during MCP server disconnect: {e}")

    def get_available_tools(self) -> List[Dict[str, Any]]:
        """Get list of available tools from connected MCP server"""
        if not self.available_tools:
            return []

        return [
            {
                "name": tool.name,
                "description": tool.description,
                "input_schema": tool.input_schema
            }
            for tool in self.available_tools
        ]

    async def is_server_healthy(self) -> bool:
        """
        Check if the MCP server is still healthy and responsive

        Returns:
            bool: True if server is healthy, False otherwise
        """
        if not self.server or not self.server.session:
            logger.debug("Health check failed: No server or session available")
            return False

        try:
            # Try to ping the server by listing tools (lightweight operation)
            await self.server.session.list_tools()
            logger.debug("Health check passed: Server is responsive")
            return True
        except Exception as e:
            error_msg = str(e) if str(e) else f"{type(e).__name__} (no message)"
            logger.warning(f"MCP server health check failed: {error_msg}")
            return False

    async def execute_tool(self, tool_name: str, tool_args: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute a tool on the connected MCP server with enhanced error handling and retry mechanism

        Args:
            tool_name: Name of the tool to execute
            tool_args: Arguments for the tool

        Returns:
            Dict containing tool execution result
        """
        if not self.server or not self.server.session:
            logger.error("No MCP server session available for tool execution")
            return {"error": "No MCP server connected", "success": False}

        # Check server health before executing tool
        logger.info(f"Checking MCP server health before executing tool '{tool_name}'")
        if not await self.is_server_healthy():
            logger.warning("MCP server health check failed - attempting to reconnect")

            # Try to reconnect if we have server config stored
            if hasattr(self, '_last_server_config') and self._last_server_config:
                logger.info("Attempting automatic reconnection to MCP server")

                reconnect_success = await self.connect_to_server(self._last_server_config)

                if reconnect_success:
                    logger.info("Successfully reconnected to MCP server")
                    # Verify health after reconnection
                    if not await self.is_server_healthy():
                        logger.error("Server still unhealthy after reconnection")
                        return {
                            "error": "MCP server reconnection failed. Please check server configuration and try again.",
                            "error_type": "ReconnectionFailed",
                            "success": False,
                            "tool_name": tool_name,
                            "args": tool_args
                        }
                else:
                    logger.error("Failed to reconnect to MCP server")
                    return {
                        "error": "MCP server connection lost and automatic reconnection failed. Please reconnect manually.",
                        "error_type": "ReconnectionFailed",
                        "success": False,
                        "tool_name": tool_name,
                        "args": tool_args
                    }
            else:
                logger.error("No server configuration available for reconnection")
                return {
                    "error": "MCP server is not responding and no configuration available for reconnection. Please reconnect manually.",
                    "error_type": "ServerUnhealthy",
                    "success": False,
                    "tool_name": tool_name,
                    "args": tool_args
                }

        # Validate tool exists
        available_tool_names = [tool.name for tool in self.available_tools]
        if tool_name not in available_tool_names:
            logger.error(f"Tool '{tool_name}' not found. Available tools: {available_tool_names}")
            return {
                "error": f"Tool '{tool_name}' not found. Available tools: {available_tool_names}",
                "success": False,
                "tool_name": tool_name,
                "args": tool_args
            }

        try:
            logger.info(f"Executing MCP tool '{tool_name}' with args: {tool_args}")
            # Use the enhanced Server class execute_tool method with retry mechanism
            result = await self.server.execute_tool(tool_name, tool_args, retries=2, delay=1.0)
            logger.info(f"Tool '{tool_name}' executed successfully")

            # Handle different result formats
            if hasattr(result, 'content'):
                # Standard MCP result format
                content = result.content
            elif isinstance(result, dict):
                # HTTP MCP result format
                content = result.get("content", result)
            else:
                # Direct result
                content = result

            return {
                "success": True,
                "content": content,
                "tool_name": tool_name,
                "args": tool_args
            }
        except Exception as e:
            error_type = type(e).__name__
            error_message = str(e) if str(e) else "Unknown error occurred"

            logger.error(f"Error executing tool {tool_name} with args {tool_args}: {error_message}")
            logger.error(f"Exception type: {error_type}")
            logger.error(f"Exception details: {error_message}")

            # Provide more user-friendly error messages based on error type
            if error_type == "ClosedResourceError":
                user_friendly_error = "MCP server connection was closed. Please reconnect to the server and try again."
            elif error_type == "ConnectionError":
                user_friendly_error = "Failed to connect to MCP server. Please check server configuration."
            elif error_type == "TimeoutError":
                user_friendly_error = "Tool execution timed out. The server may be unresponsive."
            elif error_type == "JSONDecodeError":
                user_friendly_error = "Invalid response from MCP server. The server may have returned malformed data."
            elif "not found" in error_message.lower():
                user_friendly_error = f"Tool '{tool_name}' is not available on this MCP server."
            elif "permission" in error_message.lower() or "unauthorized" in error_message.lower():
                user_friendly_error = "Permission denied. Check MCP server authentication and permissions."
            else:
                user_friendly_error = f"Tool execution failed: {error_message}"

            return {
                "success": False,
                "error": user_friendly_error,
                "error_type": error_type,
                "raw_error": error_message,
                "tool_name": tool_name,
                "args": tool_args
            }

    async def process_query(self, query: str, stream: bool = False, previous_message: Optional[str] = None, language: str = "en") -> AsyncGenerator[Dict[str, Any], None]:
        """
        Process a query using Azure OpenAI and available MCP tools

        Args:
            query: User query
            stream: Whether to stream the response
            previous_message: Previous message for context

        Yields:
            Dict containing response chunks or final result
        """
        # Handle identity questions immediately (before any other processing)
        if self._is_identity_question(query):
            # Detect the actual language of the question
            detected_language = self._detect_question_language(query)
            logger.info(f"MCP: Identity question detected, detected language: {detected_language}, returning Swiss Knowledge Hub response")
            yield {
                "answer": self._get_identity_response(detected_language),
                "tools_used": [],
                "done": True
            }
            return

        if not self.server or not self.server.session:
            yield {"error": get_translation("no_server_connected", language), "done": True}
            return

        try:
            # Build messages with context
            messages = []

            # Add previous message for context if provided
            if previous_message:
                logger.info(f"Adding previous message context: {previous_message[:100]}...")
                messages.append({"role": "assistant", "content": previous_message})

            # Add system message for Markdown formatting
            messages.insert(0, self.get_markdown_system_message())

            # Add current user query
            messages.append({"role": "user", "content": query})

            logger.info(f"Processing query with {len(messages)} messages (context: {'yes' if previous_message else 'no'})")
            logger.debug(f"Messages: {json.dumps(messages, indent=2, default=str)}")
            logger.debug(f"Available tools: {self.available_tools}")

            # Get available tools for Azure OpenAI
            available_tools = []
            self.tool_name_mapping = {}  # Reset mapping

            if self.available_tools:
                for tool in self.available_tools:
                    # Truncate tool name if it's too long for OpenAI (max 64 chars)
                    # Be more aggressive with shortening to avoid issues with MCP servers
                    original_tool_name = tool.name
                    tool_name = original_tool_name

                    # Use a more conservative limit to account for potential prefixes/suffixes
                    max_length = 50  # Conservative limit instead of 64

                    if len(tool_name) > max_length:
                        # Create a shorter name while keeping it meaningful
                        # Use a hash-based approach to ensure uniqueness
                        hash_suffix = str(abs(hash(original_tool_name)) % 1000)
                        max_prefix_len = max_length - len(hash_suffix) - 1  # -1 for underscore
                        tool_name = original_tool_name[:max_prefix_len] + "_" + hash_suffix
                        logger.warning(f"Tool name '{original_tool_name}' too long, shortened to '{tool_name}'")

                    # Store the mapping from shortened name to original name
                    self.tool_name_mapping[tool_name] = original_tool_name

                    available_tools.append({
                        "type": "function",
                        "function": {
                            "name": tool_name,
                            "description": tool.description,
                            "parameters": tool.input_schema
                        }
                    })
            logger.info(f"Available tools for stream: {stream}")

            # Initial Azure OpenAI API call
            response = await self.azure_client.chat.completions.create(
                model=os.getenv("AZURE_VISION_MODEL", "gpt-4o"),
                messages=messages,
                tools=available_tools if available_tools else None,
                temperature=0.7,
                stream=stream
            )

            if stream:
                # Handle streaming response
                async for chunk in self._handle_streaming_response(response, messages, language):
                    yield chunk
            else:
                # Handle non-streaming response
                result = await self._handle_non_streaming_response(response, messages, language)
                yield result

        except Exception as e:
            logger.error(f"Error processing query: {e}")
            yield {
                "error": f"{get_translation('query_processing_failed', language)}: {str(e)}",
                "answer": "",
                "tools_used": [],
                "done": True
            }

    async def process_query_with_tools(self, query: str,  tools: List[Dict[str, Any]],stream: bool = False,previous_message: Optional[str] = None, language: str = "en") -> AsyncGenerator[Dict[str, Any], None]:
        """
        Process a query using Azure OpenAI and available MCP tools

        Args:
            query: User query
            stream: Whether to stream the response
            previous_message: Previous message for context

        Yields:
            Dict containing response chunks or final result
        """
        # Handle identity questions immediately (before any other processing)
        if self._is_identity_question(query):
            # Detect the actual language of the question
            detected_language = self._detect_question_language(query)
            logger.info(f"MCP: Identity question detected in process_query_with_tools, detected language: {detected_language}, returning Swiss Knowledge Hub response")
            yield {
                "answer": self._get_identity_response(detected_language),
                "tools_used": [],
                "done": True
            }
            return

        if not self.server or not self.server.session:
            yield {"error": get_translation("no_server_connected", language), "done": True}
            return

        try:
            # Build messages with context
            messages = []

            # Add previous message for context if provided
            if previous_message:
                logger.info(f"Adding previous message context: {previous_message[:100]}...")
                messages.append({"role": "assistant", "content": previous_message})

            # Add system message for Markdown formatting
            messages.insert(0, self.get_markdown_system_message())

            self.tool_name_mapping = {}  # Reset mapping
            available_tools = []
            if tools:
                for tool in tools:
                    # Truncate tool name if it's too long for OpenAI (max 64 chars)
                    # Be more aggressive with shortening to avoid issues with MCP servers
                    original_tool_name = tool.name
                    tool_name = original_tool_name

                    # Use a more conservative limit to account for potential prefixes/suffixes
                    max_length = 50  # Conservative limit instead of 64

                    if len(tool_name) > max_length:
                        # Create a shorter name while keeping it meaningful
                        # Use a hash-based approach to ensure uniqueness
                        hash_suffix = str(abs(hash(original_tool_name)) % 1000)
                        max_prefix_len = max_length - len(hash_suffix) - 1  # -1 for underscore
                        tool_name = original_tool_name[:max_prefix_len] + "_" + hash_suffix
                        logger.warning(f"Tool name '{original_tool_name}' too long, shortened to '{tool_name}'")

                    # Store the mapping from shortened name to original name
                    self.tool_name_mapping[tool_name] = original_tool_name

                    available_tools.append({
                        "type": "function",
                        "function": {
                            "name": tool_name,
                            "description": tool.description,
                            "parameters": tool.input_schema
                        }
                    })
            logger.info(f"Available tools for stream: {stream}")

            # Initial Azure OpenAI API call
            response = await self.azure_client.chat.completions.create(
                model=os.getenv("AZURE_VISION_MODEL", "gpt-4o"),
                messages=messages,
                tools=available_tools if available_tools else None,
                temperature=0.7,
                stream=stream
            )

            if stream:
                # Handle streaming response
                async for chunk in self._handle_streaming_response(response, messages, language):
                    yield chunk
            else:
                # Handle non-streaming response
                result = await self._handle_non_streaming_response(response, messages, language)
                yield result

        except Exception as e:
            logger.error(f"Error processing query: {e}")
            yield {
                "error": f"{get_translation('query_processing_failed', language)}: {str(e)}",
                "answer": "",
                "tools_used": [],
                "done": True
            }

    async def process_query_with_tools(self, query: str,  tools: List[Dict[str, Any]],stream: bool = False,previous_message: Optional[str] = None, ) -> AsyncGenerator[Dict[str, Any], None]:
        """
        Process a query using Azure OpenAI and available MCP tools

        Args:
            query: User query
            stream: Whether to stream the response
            previous_message: Previous message for context

        Yields:
            Dict containing response chunks or final result
        """
        if not self.server or not self.server.session:
            yield {"error": "No MCP server connected", "done": True}
            return

        try:
            # Build messages with context
            messages = []

            # Add previous message for context if provided
            if previous_message:
                logger.info(f"Adding previous message context: {previous_message[:100]}...")
                messages.append({"role": "assistant", "content": previous_message})

            # Add system message for Markdown formatting
            messages.insert(0, self.get_markdown_system_message())

            self.tool_name_mapping = {}  # Reset mapping
            available_tools = []
            if tools:
                for tool in tools:
                    # Truncate tool name if it's too long for OpenAI (max 64 chars)
                    # Be more aggressive with shortening to avoid issues with MCP servers
                    original_tool_name = tool.name
                    tool_name = original_tool_name

                    # Use a more conservative limit to account for potential prefixes/suffixes
                    max_length = 50  # Conservative limit instead of 64

                    if len(tool_name) > max_length:
                        # Create a shorter name while keeping it meaningful
                        # Use a hash-based approach to ensure uniqueness
                        hash_suffix = str(abs(hash(original_tool_name)) % 1000)
                        max_prefix_len = max_length - len(hash_suffix) - 1  # -1 for underscore
                        tool_name = original_tool_name[:max_prefix_len] + "_" + hash_suffix
                        logger.warning(f"Tool name '{original_tool_name}' too long, shortened to '{tool_name}'")

                    # Store the mapping from shortened name to original name
                    self.tool_name_mapping[tool_name] = original_tool_name

                    available_tools.append({
                        "type": "function",
                        "function": {
                            "name": tool_name,
                            "description": tool.description,
                            "parameters": tool.input_schema
                        }
                    })
            logger.info(f"Available tools for stream: {stream}")

            # Initial Azure OpenAI API call
            response = await self.azure_client.chat.completions.create(
                model=os.getenv("AZURE_VISION_MODEL", "gpt-4o"),
                messages=messages,
                tools=available_tools if available_tools else None,
                temperature=0.7,
                stream=stream
            )

            if stream:
                # Handle streaming response
                async for chunk in self._handle_streaming_response(response, messages):
                    yield chunk
            else:
                # Handle non-streaming response
                result = await self._handle_non_streaming_response(response, messages)
                yield result

        except Exception as e:
            logger.error(f"Error processing query: {e}")
            yield {
                "error": f"Query processing failed: {str(e)}",
                "answer": "",
                "tools_used": [],
                "done": True
            }

    async def _handle_streaming_response(
        self,
        response,
        messages: List[Dict[str, Any]],
        language: str = "en"
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """Handle streaming response from Azure OpenAI"""
        content_buffer = ""
        tool_calls = []

        async for chunk in response:
            if chunk.choices and len(chunk.choices) > 0:
                choice = chunk.choices[0]
                delta = choice.delta

                # Handle content
                if delta.content:
                    content_buffer += delta.content
                    yield {
                        "type": "content",
                        "content": delta.content,
                        "done": False
                    }

                # Handle tool calls
                if delta.tool_calls:
                    for tool_call in delta.tool_calls:
                        # Handle incremental tool call building
                        if tool_call.index is not None:
                            # Extend tool_calls list if needed
                            while len(tool_calls) <= tool_call.index:
                                tool_calls.append(None)

                            if tool_calls[tool_call.index] is None:
                                tool_calls[tool_call.index] = tool_call
                            else:
                                # Merge incremental tool call data
                                existing = tool_calls[tool_call.index]
                                if tool_call.function and tool_call.function.arguments:
                                    if existing.function.arguments:
                                        existing.function.arguments += tool_call.function.arguments
                                    else:
                                        existing.function.arguments = tool_call.function.arguments

                # Check if response is finished
                if choice.finish_reason:
                    if choice.finish_reason == "tool_calls" and tool_calls:
                        # Execute tools and continue conversation
                        async for tool_result in self._execute_tools_and_continue(
                            tool_calls, messages, content_buffer, stream=True, language=language
                        ):
                            yield tool_result
                    else:
                        # Final response
                        yield {
                            "type": "final",
                            "answer": content_buffer,
                            "tools_used": [],
                            "done": True
                        }

    async def _handle_non_streaming_response(
        self,
        response,
        messages: List[Dict[str, Any]],
        language: str = "en"
    ) -> Dict[str, Any]:
        """Handle non-streaming response from Azure OpenAI"""
        try:
            choice = response.choices[0]
            message = choice.message

            # Check if there are tool calls
            if message.tool_calls:
                # Execute tools and get final response
                async for result in self._execute_tools_and_continue(
                    message.tool_calls, messages, message.content or "", stream=False, language=language
                ):
                    return result
            else:
                # Direct response without tool calls
                return {
                    "answer": message.content or "",
                    "tools_used": [],
                    "server_info": self.server_info,
                    "done": True
                }

        except Exception as e:
            logger.error(f"Error handling non-streaming response: {e}")
            return {
                "error": f"Response handling failed: {str(e)}",
                "answer": "",
                "tools_used": [],
                "done": True
            }

    async def _execute_tools_and_continue(
        self,
        tool_calls,
        messages: List[Dict[str, Any]],
        initial_content: str,
        stream: bool = False,
        language: str = "en"
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """Execute tool calls and continue the conversation"""
        tools_used = []
        tool_results = []

        # Add assistant message with tool calls to conversation
        assistant_message = {
            "role": "assistant",
            "content": initial_content or None,
            "tool_calls": []
        }

        # Convert tool_calls to the proper format for OpenAI
        for tool_call in tool_calls:
            if tool_call is not None and hasattr(tool_call, 'function'):
                assistant_message["tool_calls"].append({
                    "id": tool_call.id if hasattr(tool_call, 'id') else f"call_{len(assistant_message['tool_calls'])}",
                    "type": "function",
                    "function": {
                        "name": tool_call.function.name,
                        "arguments": tool_call.function.arguments or "{}"
                    }
                })

        messages.append(assistant_message)

        # Execute each tool call
        for i, tool_call in enumerate(tool_calls):
            if tool_call is None:
                continue

            if hasattr(tool_call, 'function') and tool_call.function:
                function = tool_call.function
                tool_name = function.name

                # Get the original tool name if it was shortened
                original_tool_name = self.tool_name_mapping.get(tool_name, tool_name)

                try:
                    tool_args = json.loads(function.arguments) if function.arguments else {}
                except json.JSONDecodeError as e:
                    logger.error(f"Failed to parse tool arguments: {e}")
                    tool_args = {}

                # Yield tool execution info
                yield {
                    "type": "tool_call",
                    "message": get_translation("tool_execution", language, tool_name=original_tool_name),
                    "tool_name": original_tool_name,
                    "tool_args": tool_args,
                    "done": False
                }

                # Execute the tool using the original name
                logger.info(f"Executing tool: {original_tool_name} with args: {tool_args}")
                result = await self.execute_tool(original_tool_name, tool_args)
                tools_used.append(original_tool_name)

                # Process tool result content
                tool_result_content = ""
                if result.get("success"):
                    content = result.get("content", [])
                    if isinstance(content, list):
                        # Handle MCP content format
                        text_parts = []
                        for item in content:
                            if isinstance(item, dict):
                                if item.get("type") == "text":
                                    text_parts.append(item.get("text", ""))
                                else:
                                    text_parts.append(str(item))
                            else:
                                text_parts.append(str(item))
                        tool_result_content = "\n".join(text_parts)
                    else:
                        tool_result_content = str(content)
                    logger.info(f"Tool {original_tool_name} executed successfully")
                else:
                    error_msg = result.get('error', 'Unknown error')
                    error_type = result.get('error_type', 'Unknown')
                    tool_result_content = f"Error executing tool {original_tool_name}: {error_msg} (Type: {error_type})"
                    logger.error(f"Tool execution failed: {tool_result_content}")

                    # Yield error information for frontend display
                    yield {
                        "type": "tool_error",
                        "message": get_translation("tool_error", language, tool_name=original_tool_name),
                        "tool_name": original_tool_name,
                        "error": error_msg,
                        "error_type": error_type,
                        "tool_args": tool_args,
                        "done": False
                    }

                tool_results.append(tool_result_content)

                # Get the tool_call_id from the assistant message we just added
                assistant_msg = messages[-1]
                tool_call_id = assistant_msg["tool_calls"][i]["id"] if i < len(assistant_msg["tool_calls"]) else f"call_{i}"

                # Add tool result to messages in the correct format for OpenAI
                messages.append({
                    "role": "tool",
                    "tool_call_id": tool_call_id,
                    "content": tool_result_content
                })

        # Get final response from Azure OpenAI with tool results
        try:
            # Debug: Log the messages structure
            logger.debug(f"Messages for final response: {json.dumps(messages, indent=2, default=str)}")

            # Add system message to format response as Markdown
            messages_with_format = messages.copy()
            messages_with_format.insert(0, self.get_markdown_system_message())

            final_response = await self.azure_client.chat.completions.create(
                model=os.getenv("AZURE_OPENAI_DEPLOYMENT", "gpt-4o"),
                messages=messages_with_format,
                temperature=0.7,
                stream=stream
            )

            if stream:
                # Stream the final response
                final_content = ""
                async for chunk in final_response:
                    if chunk.choices and len(chunk.choices) > 0:
                        choice = chunk.choices[0]
                        if choice.delta.content:
                            final_content += choice.delta.content
                            yield {
                                "type": "content",
                                "content": choice.delta.content,
                                "done": False
                            }

                        if choice.finish_reason:
                            yield {
                                "type": "final",
                                "answer": final_content,
                                "tools_used": tools_used,
                                "server_info": self.server_info,
                                "done": True
                            }
            else:
                # Non-streaming final response
                final_content = final_response.choices[0].message.content or ""
                yield {
                    "answer": final_content,
                    "tools_used": tools_used,
                    "server_info": self.server_info,
                    "done": True
                }

        except Exception as e:
            logger.error(f"Error getting final response: {e}")
            yield {
                "error": f"{get_translation('final_response_failed', language)}: {str(e)}",
                "answer": "",
                "tools_used": tools_used,
                "done": True
            }
