import {
  updateFile,
  deleteFile,
  createFile,
  recordVectorStoreUsage,
} from "@/services";
import { useLanguage } from "@/lib/language-context";
import { uploadPipeline } from "@/services/src/upload-pipeline";
import { workspaceChatService } from "@/services/workspace-chat";
import { getCookie } from "@/utils/cookies";
import { useRouter } from "next/navigation";
import { useTranslatedToast } from "@/hooks/use-translated-toast";
import { SupportedExtensions } from "@/lib/constant/supported-extensions";
import { getFileExtension } from "@/lib/utils/file-utils";
import { ParallelProcessor } from "@/lib/utils/parallel-processor";

interface FileOperationsProps {
  workspaceSlug: string;
  tenantId: string;
  pageId: string;
  setIsLoading: any;
  usageSummary: any;
  onFileDeleted?: (fileId: string) => void; // Callback for when file is deleted
  onFileRenamed?: (fileId: string, newName: string) => void; // Callback for when file is renamed
  onFileUploaded?: (newFiles: any[]) => void; // Callback for when files are uploaded
  onUploadProgress?: (progress: {
    total: number;
    processed: number;
    successful: number;
    failed: number;
    currentBatch: string[];
  }) => void; // Callback for upload progress
}

// Helper function to determine document type based on file extension
const getDocumentType = (filename: string): string => {
  const extension = getFileExtension(filename);

  switch (extension) {
    case "md":
    case "mdx":
    case "markdown":
      return "markitdown"; // Use markitdown for better markdown processing
    case "pdf":
      return "pdf";
    case "txt":
      return "text";
    case "csv":
      return "csv";
    case "docx":
    case "doc":
      return "word";
    case "xlsx":
    case "xls":
      return "excel";
    case "pptx":
    case "ppt":
      return "powerpoint";
    default:
      return "auto"; // Let the backend auto-detect
  }
};

export const useFileOperations = ({
  workspaceSlug,
  tenantId,
  pageId,
  setIsLoading,
  usageSummary,
  onFileDeleted,
  onFileRenamed,
  onFileUploaded,
  onUploadProgress,
}: FileOperationsProps) => {
  const userId = getCookie("userId") || "";
  const router = useRouter();
  const { t } = useLanguage();
  const translatedToast = useTranslatedToast();

  // Recovery mechanism for files stuck in PROCESSING status
  const resumeStuckProcessing = async (files: any[]) => {
    const stuckFiles = files.filter(
      (file) =>
        file.vectorizationStatus === "PROCESSING" &&
        SupportedExtensions?.includes(file?.extension) &&
        file.url // Ensure file has a URL
    );

    if (stuckFiles.length === 0) return;

    console.log(
      `Found ${stuckFiles.length} files stuck in PROCESSING status, resuming indexing...`
    );

    // Create a parallel processor for resuming indexing
    const resumeProcessor = new ParallelProcessor<any, any>({
      concurrency: 1, // Very conservative for recovery
      maxRetries: 2,
      retryDelay: 3000,
      batchDelay: 2000, // 2 second delay between batches for recovery
      onProgress: (progress) => {
        console.log(
          `Recovery progress: ${progress.processed}/${progress.total} files`
        );
      },
      onItemError: (item, error, attempt) => {
        console.warn(
          `Failed to resume indexing for ${item.name} (attempt ${attempt}):`,
          error.message
        );
      },
    });

    // Process stuck files
    const recoveryResults = await resumeProcessor.processAll(
      stuckFiles,
      async (item: any) => {
        const documentType = getDocumentType(item.name + "." + item.extension);

        return await workspaceChatService?.uploadForIndexing({
          userId: userId,
          document_path: item.url,
          document_type: documentType,
          workspaceSlug: workspaceSlug,
          tenantId,
          file_id: item?.id,
        });
      }
    );

    const recoveredCount = recoveryResults.filter((r) => r.success).length;
    const failedRecovery = recoveryResults.filter((r) => !r.success).length;

    if (recoveredCount > 0) {
      console.log(`Successfully resumed indexing for ${recoveredCount} files`);
      translatedToast.success("toast.resumedProcessingFiles", {
        count: recoveredCount,
      });
    }

    if (failedRecovery > 0) {
      console.warn(`Failed to resume indexing for ${failedRecovery} files`);
    }
  };

  // Helper function to truncate filename to ensure Azure Video Indexer compatibility
  const truncateFilename = (
    filename: string,
    maxLength: number = 50
  ): string => {
    if (filename.length <= maxLength) return filename;

    const extension = filename.substring(filename.lastIndexOf("."));
    const nameWithoutExt = filename.substring(0, filename.lastIndexOf("."));
    const maxNameLength = maxLength - extension.length;

    if (maxNameLength <= 0) return filename.substring(0, maxLength);

    return nameWithoutExt.substring(0, maxNameLength) + extension;
  };

  // Helper function to process a single file upload
  const processSingleFile = async (
    file: File,
    folderId: string
  ): Promise<any> => {
    // Truncate filename to ensure final blob name stays under 80 characters
    // Account for timestamp prefix (13 chars) + hyphen (1 char) = 14 chars
    // Leave some buffer for URL encoding, so limit filename to 50 chars
    const truncatedFilename = truncateFilename(file.name, 50);

    // Create a new File object with truncated name if needed
    const fileToUpload =
      file.name !== truncatedFilename
        ? new File([file], truncatedFilename, { type: file.type })
        : file;

    const formData = new FormData();
    formData.append("file", fileToUpload);
    formData.append("tenantId", tenantId);
    formData.append("workspaceSlug", workspaceSlug);
    formData.append("pageId", pageId);

    const response = await fetch("/api/upload", {
      method: "POST",
      body: formData,
      headers: {
        "x-user-id": userId,
        "x-tenant-id": tenantId,
        "x-workspace-slug": workspaceSlug,
      },
    });

    let gDriveFileId = "";
    let oneDriveFileId = "";

    // Use the upload pipeline to handle multiple uploads with retry and revert
    try {
      const uploadResults = await uploadPipeline.uploadFile(file, {
        workspaceSlug,
        tenantId,
      });

      // Extract file IDs from upload results
      uploadResults.forEach((result) => {
        if (result.success) {
          if (result.targetName === "googleDrive") {
            gDriveFileId = result.fileId || "";
          } else if (result.targetName === "outlookDrive") {
            oneDriveFileId = result.fileId || "";
          }
        }
      });
    } catch (error) {
      // Continue with the main upload as fallback
    }

    const data = await response.json();
    if (!response.ok) throw new Error(data.error);

    // Try to record vector store usage, but continue even if it fails
    try {
      const usageResult = await recordVectorStoreUsage(
        tenantId,
        file.size / (1024 * 1024 * 1024)
      );

      if (usageResult === null) {
        // Vector store usage recording failed, but continuing with upload
      }
    } catch (usageError) {
      // Failed to record vector store usage, but continuing with upload
    }

    return {
      name: fileToUpload.name.substring(0, fileToUpload.name.lastIndexOf(".")),
      type: "file" as const,
      ...(folderId && folderId.trim() !== "" ? { folderId } : {}),
      workspaceSlug: workspaceSlug,
      pageId: pageId,
      parentId: folderId && folderId.trim() !== "" ? folderId : pageId,
      extension: fileToUpload.name.split(".").pop() || "",
      size: `${Math.round(file.size / 1024)} KB`,
      oneDriveFileId,
      gDriveFileId,
      createdAt: new Date().toISOString(),
      url: data.url,
    };
  };

  const handleFileUpload = async (
    e: React.ChangeEvent<HTMLInputElement>,
    folderId: string,
    files: any[],
    setFiles: (files: any[]) => void,
    setIsUploadOpen: (isOpen: boolean) => void
  ) => {
    setIsLoading(true);
    const uploadedFiles = e.target.files;
    if (uploadedFiles && uploadedFiles.length > 0) {
      try {
        // Check vector store usage before uploading
        try {
          // Calculate total size of files to be uploaded in GB
          const totalUploadSizeGB = Array.from(uploadedFiles).reduce(
            (total, file) => total + file.size / (1024 * 1024 * 1024),
            0
          );

          // Check if upload would exceed the limit
          const currentUsageGB = usageSummary.totalUsage || 0;
          const limitGB = usageSummary.limit || 0;

          if (limitGB > 0 && currentUsageGB + totalUploadSizeGB > limitGB) {
            // Inform user about exceeding the limit
            const willExceedBy = (
              currentUsageGB +
              totalUploadSizeGB -
              limitGB
            ).toFixed(2);

            // Ask user for confirmation before proceeding
            const confirmMessage =
              t("subscription.vectorStorageWarning", { willExceedBy }) +
              "\n\n" +
              t("subscription.vectorStorageUsage", {
                currentUsageGB: currentUsageGB.toFixed(2),
                limitGB: limitGB,
              }) +
              "\n\n" +
              t("subscription.vectorStorageContinue");

            const confirmUpload = window.confirm(confirmMessage);

            if (!confirmUpload) {
              setIsLoading(false);
              return;
            }
          }
        } catch (usageError) {
          // Continue with upload even if we can't check usage
        }

        translatedToast.loading("toast.uploadingFiles");

        // Convert FileList to Array for parallel processing
        const fileArray = Array.from(uploadedFiles);

        // Create parallel processor for file uploads
        const processor = new ParallelProcessor<File, any>({
          concurrency: 3, // Process 3 files concurrently to balance speed and server load
          maxRetries: 2,
          retryDelay: 1000,
          batchDelay: 500,
          onProgress: (progress) => {
            // Update progress if callback is provided
            if (onUploadProgress) {
              onUploadProgress({
                total: progress.total,
                processed: progress.processed,
                successful: progress.successful,
                failed: progress.failed,
                currentBatch: progress.currentBatch.map((file) => file.name),
              });
            }
          },
          onItemError: (file, error, attempt) => {
            console.warn(
              `Failed to upload ${file.name} (attempt ${attempt}):`,
              error.message
            );
          },
        });

        // Process all files using parallel processor
        const uploadResults = await processor.processAll(fileArray, (file) =>
          processSingleFile(file, folderId)
        );

        // Extract successful uploads
        const newFiles = uploadResults
          .filter((result) => result.success)
          .map((result) => result.result);

        // Log failed uploads
        const failedUploads = uploadResults.filter((result) => !result.success);
        if (failedUploads.length > 0) {
          console.warn(
            `${failedUploads.length} files failed to upload:`,
            failedUploads.map((f) => f.item.name)
          );
        }
        const file = await createFile(
          {
            files: newFiles,
            workspaceSlug,
            tenantId,
          },
          tenantId,
          userId,
          workspaceSlug
        );

        // Use bulk index API for better performance and reliability
        const supportedFiles =
          file?.data?.filter(
            (item: any) => SupportedExtensions?.includes(item?.extension)
          ) || [];

        if (supportedFiles.length > 0) {
          console.log(
            `Starting bulk indexing for ${supportedFiles.length} files`
          );

          await workspaceChatService.bulkUploadForIndexing(
            workspaceSlug,
            supportedFiles,
            getDocumentType,
            userId,
            tenantId
          );
        }
        translatedToast.dismiss();
        setIsLoading(false);

        // Handle results based on success/failure counts
        const successCount = uploadResults.filter((r) => r.success).length;
        const failureCount = uploadResults.filter((r) => !r.success).length;

        if (file?.error) {
          translatedToast.error("toast.failedToUploadFiles");
          return;
        }

        // Update files list with successful uploads
        const createdFiles = file?.data ?? [];
        setFiles([...files, ...createdFiles]);
        setIsUploadOpen(false);
        translatedToast.dismiss();

        // Call the callback if provided
        if (onFileUploaded && createdFiles.length > 0) {
          onFileUploaded(createdFiles);
        }

        // Show appropriate success/warning message
        if (failureCount === 0) {
          translatedToast.success("toast.allFilesUploadedSuccessfully", {
            count: successCount,
          });
        } else if (successCount > 0) {
          translatedToast.success("toast.filesUploadedSuccessfully", {
            count: successCount,
          });
          translatedToast.error("toast.filesFailedToUpload", {
            count: failureCount,
          });
        } else {
          translatedToast.error("toast.allFilesFailedToUpload");
          return;
        }

        router.refresh();
      } catch (error) {
        translatedToast.dismiss();
        translatedToast.error("toast.failedToUploadFiles");
      }
    }
  };

  const handleRenameFile = async (fileId: string, newName: string) => {
    if (!newName) {
      translatedToast.error("toast.fileNameCannotBeEmpty");
      return false;
    }

    const loadingToast = translatedToast.loading("toast.renamingFile");
    setIsLoading(true);

    try {
      const updatedFile = await updateFile(
        {
          id: fileId,
          name: newName,
        },
        tenantId,
        userId,
        workspaceSlug
      );

      // Remove the specific loading toast
      translatedToast.dismiss();
      setIsLoading(false);

      if (updatedFile?.message) {
        translatedToast.success("toast.fileRenamedSuccessfully");

        // Call the callback to update UI state instead of reloading
        if (onFileRenamed) {
          onFileRenamed(fileId, newName);
        }

        return true;
      } else {
        translatedToast.error("toast.failedToRenameFile");
        return false;
      }
    } catch (error) {
      // Remove the specific loading toast
      translatedToast.dismiss();
      setIsLoading(false);

      translatedToast.error("toast.failedToRenameFile");
      return false;
    }
  };

  const handleDeleteFile = async (fileId: string) => {
    translatedToast.loading("toast.deletingFile");
    setIsLoading(true);

    try {
      // Then delete from database and storage
      const deletedFile = await deleteFile(
        fileId,
        tenantId,
        userId,
        workspaceSlug
      );

      // Remove loading toast
      translatedToast.dismiss();

      if (deletedFile?.message) {
        translatedToast.success("toast.fileDeletedSuccessfully");
        setIsLoading(false);

        // Call the callback to update UI state instead of reloading
        if (onFileDeleted) {
          onFileDeleted(fileId);
        }

        return true;
      } else {
        translatedToast.error("toast.failedToDeleteFile");
        setIsLoading(false);
        return false;
      }
    } catch (error) {
      // Remove loading toast
      translatedToast.dismiss();
      setIsLoading(false);

      translatedToast.error("toast.failedToDeleteFile");
      return false;
    }
  };

  return {
    handleFileUpload,
    handleRenameFile,
    handleDeleteFile,
    resumeStuckProcessing,
  };
};
