import { createFolder, deleteFolder, updateFolder } from "@/services";
import { getCookie } from "@/utils/cookies";
import { useTranslatedToast } from "@/hooks/use-translated-toast";

interface FolderOperationsProps {
  workspaceSlug: string;
  tenantId: string;
  pageId: string;
  setIsLoading: any;
  onFolderCreated?: (folder: any) => void; // Callback for when folder is created
  onFolderRenamed?: (folderId: string, newName: string) => void; // Callback for when folder is renamed
  onFolderDeleted?: (folderId: string) => void; // Callback for when folder is deleted
}

export const useFolderOperations = ({
  workspaceSlug,
  tenantId,
  pageId,
  setIsLoading,
  onFolderCreated,
  onFolderRenamed,
  onFolderDeleted,
}: FolderOperationsProps) => {
  const userId = getCookie("userId") || "";
  const translatedToast = useTranslatedToast();
  const handleCreateFolder = async (
    newFolderName: string,
    parentFolderId?: string
  ) => {
    if (newFolderName.trim()) {
      try {
        translatedToast.loading("toast.creatingFolder");
        setIsLoading(true);

        const parentIds = parentFolderId ? [parentFolderId] : [];

        const data = {
          name: newFolderName.trim(),
          workspaceSlug,
          tenantId,
          pageId,
          parentIds,
        };

        // Pass workspaceSlug as part of data object, it will be added to headers in the service
        const newFolder = await createFolder(data, tenantId, userId);
        translatedToast.dismiss();
        setIsLoading(false);
        if (newFolder?.folder?.id) {
          translatedToast.success(
            newFolder?.message
              ? "toast.folderCreatedSuccessfully"
              : "toast.folderCreatedSuccessfully"
          );

          // Call the callback to update UI state instead of reloading
          if (onFolderCreated) {
            onFolderCreated(newFolder.folder);
          }

          return true;
        } else {
          translatedToast.error(
            newFolder?.error
              ? "toast.failedToCreateFolder"
              : "toast.failedToCreateFolder"
          );
          return false;
        }
      } catch (error) {
        console.error("Error creating folder:", error);
        return false;
      }
    }
    translatedToast.error("toast.folderNameCannotBeEmpty");
    return false;
  };

  const handleRenameFolder = async (folderId: string, newName: string) => {
    if (!newName.trim()) {
      translatedToast.error("toast.folderNameCannotBeEmpty");
      return false;
    }
    setIsLoading(true);
    translatedToast.loading("toast.renamingFolder");
    const updatedFolder = await updateFolder(
      {
        id: folderId,
        name: newName,
        tenantId,
        workspaceSlug, // Add workspaceSlug to be used in headers
      },
      tenantId,
      userId
    );
    translatedToast.dismiss();
    setIsLoading(false);
    if (updatedFolder?.message) {
      translatedToast.success("toast.folderRenamedSuccessfully");

      // Call the callback to update UI state instead of reloading
      if (onFolderRenamed) {
        onFolderRenamed(folderId, newName);
      }

      return true;
    } else {
      translatedToast.error("toast.failedToRenameFolder");
      return false;
    }
  };

  const handleDeleteFolder = async (folderId: string) => {
    translatedToast.loading("toast.deletingFolder");
    setIsLoading(true);
    // Pass workspaceSlug as the fourth parameter to be used in headers
    const deletedFolder = await deleteFolder(
      folderId,
      tenantId,
      userId,
      workspaceSlug as any // Use type assertion to avoid type error
    );
    translatedToast.dismiss();
    setIsLoading(false);
    if (deletedFolder?.message) {
      translatedToast.success("toast.folderDeletedSuccessfully");

      // Call the callback to update UI state instead of reloading
      if (onFolderDeleted) {
        onFolderDeleted(folderId);
      }

      return true;
    } else {
      translatedToast.error("toast.failedToDeleteFolder");
      return false;
    }
  };

  return {
    handleCreateFolder,
    handleRenameFolder,
    handleDeleteFolder,
  };
};
