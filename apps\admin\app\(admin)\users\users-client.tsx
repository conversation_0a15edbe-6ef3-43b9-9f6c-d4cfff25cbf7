"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>s,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Treemap
} from "recharts";

interface UsersByTenant {
  name: string;
  users: number;
}

interface UsersByRole {
  name: string;
  users: number;
}

interface UsersData {
  users: any[];
  usersByTenant: UsersByTenant[];
  usersByRole: UsersByRole[];
}

interface UsersClientProps {
  usersData: UsersData;
}

export function UsersClient({ usersData }: UsersClientProps) {
  return (
    <div className="grid gap-4 md:grid-cols-2">
      <Card>
        <CardHeader>
          <CardTitle>Users by Tenant</CardTitle>
        </CardHeader>
        <CardContent className="h-[300px]">
          <ResponsiveContainer width="100%" height="100%">
            <Treemap
              data={usersData.usersByTenant}
              dataKey="users"
              nameKey="name"
              stroke="#fff"
              fill="#2E8B57"
            // content={<CustomTreeMapContent />}
            >
              <Tooltip
                content={({ active, payload }) => {
                  if (active && payload && payload.length) {
                    const { name, users } = payload[0].payload;
                    return (
                      <div
                        style={{
                          backgroundColor: "white",
                          // border: "1px solid #ccc",
                          padding: "6px 10px",
                          fontSize: "13px",
                          borderRadius: "4px",
                          color:"gray",
                          boxShadow: "0 1px 4px rgba(0,0,0,0.1)",
                        }}
                      >
                        <strong>{name}</strong>
                        <div>{users} users</div>
                      </div>
                    );
                  }
                  return null;
                }}
              />
            </Treemap>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Users by Role</CardTitle>
        </CardHeader>
        <CardContent className="h-[300px]">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart
              data={usersData.usersByRole}
              margin={{
                top: 20,
                right: 30,
                left: 20,
                bottom: 5,
              }}
            >
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip formatter={(value) => `${value} users`} />
              <Bar dataKey="users" name="Number of Users" fill="#82ca9d" />
            </BarChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>
    </div>
  );
}
