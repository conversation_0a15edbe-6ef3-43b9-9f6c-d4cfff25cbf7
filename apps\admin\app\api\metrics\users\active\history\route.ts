import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/next-auth";
import db from "@/lib/shared-db";

export async function GET(request: Request) {
  const session = await getServerSession(authOptions);

  if (!session) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const { searchParams } = new URL(request.url);
    const period = searchParams.get("period") || "6months"; // '30days', '6months', '12months', etc.

    // Parse the period
    const match = period.match(/^(\d+)(days|months)$/);
    if (!match) {
      return NextResponse.json(
        {
          error: "Invalid period format. Use format like '30days' or '6months'",
        },
        { status: 400 }
      );
    }

    const amount = parseInt(match[1], 10);
    const unit = match[2];

    // Calculate the start date
    const startDate = new Date();
    if (unit === "days") {
      startDate.setDate(startDate.getDate() - amount);
    } else if (unit === "months") {
      startDate.setMonth(startDate.getMonth() - amount);
    }

    // Get all API requests in the period
    const apiRequests = await db.aPIRequest.findMany({
      where: {
        timestamp: {
          gte: startDate,
        },
      },
      select: {
        userId: true,
        timestamp: true,
      },
      orderBy: {
        timestamp: "asc",
      },
    });

    // Group by month or day and count unique users
    const groupedData = {};
    const monthNames = [
      "Jan",
      "Feb",
      "Mar",
      "Apr",
      "May",
      "Jun",
      "Jul",
      "Aug",
      "Sep",
      "Oct",
      "Nov",
      "Dec",
    ];

    apiRequests.forEach((request) => {
      const date = new Date(request.timestamp);

      if (unit === "days") {
        // Format as YYYY-MM-DD
        const day = date.toISOString().split("T")[0];
        if (!groupedData[day]) {
          groupedData[day] = new Set();
        }
        groupedData[day].add(request.userId);
      } else {
        // Format as Month YYYY (e.g., "Jan 2023")
        const monthYear = `${
          monthNames[date.getMonth()]
        } ${date.getFullYear()}`;
        if (!groupedData[monthYear]) {
          groupedData[monthYear] = new Set();
        }
        groupedData[monthYear].add(request.userId);
      }
    });

    // Convert to array format for charts
    let timeSeriesData: any[] = [];

    if (unit === "days") {
      // For days, we want to ensure all days in the range are represented
      const endDate = new Date();
      let currentDate = new Date(startDate);

      while (currentDate <= endDate) {
        const day = currentDate.toISOString().split("T")[0];
        timeSeriesData.push({
          date: day,
          activeUsers: groupedData[day] ? (groupedData[day] as any).size : 0,
        });

        currentDate.setDate(currentDate.getDate() + 1);
      }
    } else {
      // For months, convert the grouped data to array
      for (const [monthYear, userSet] of Object.entries(groupedData)) {
        const userSetObj = userSet as any;
        timeSeriesData.push({
          month: monthYear,
          activeUsers: userSetObj.size,
        });
      }

      // Sort by date
      timeSeriesData.sort((a: any, b: any) => {
        const [aMonth, aYear] = a.month.split(" ");
        const [bMonth, bYear] = b.month.split(" ");

        if (aYear !== bYear) {
          return parseInt(aYear) - parseInt(bYear);
        }

        return monthNames.indexOf(aMonth) - monthNames.indexOf(bMonth);
      });
    }

    return NextResponse.json({
      period,
      dailyData: unit === "days" ? timeSeriesData : null,
      monthlyData: unit === "months" ? timeSeriesData : null,
    });
  } catch (error) {
    console.error("Error fetching active user history metrics:", error);
    return NextResponse.json(
      { error: "Failed to fetch active user history metrics" },
      { status: 500 }
    );
  }
}
