import { NextResponse } from "next/server";
import db from "@/lib/shared-db";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/next-auth";
import crypto from "crypto";

// Generate a secure API key
function generateApiKey(): string {
  const prefix = "skh_";
  const randomBytes = crypto.randomBytes(32).toString("hex");
  return prefix + randomBytes;
}

export async function GET(req: Request) {
  try {
    const session: any = await getServerSession(authOptions);
    if (!session) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const { searchParams } = new URL(req.url);
    const tenantId = searchParams.get("tenantId");

    if (!tenantId) {
      return NextResponse.json(
        { error: "Tenant ID is required" },
        { status: 400 }
      );
    }

    // Fetch chatbots from database
    const chatbots = await db.chatbot.findMany({
      where: {
        tenantId: tenantId,
        userId: session.userId,
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    return NextResponse.json({ chatbots }, { status: 200 });
  } catch (error) {
    console.error("[CHATBOTS_GET]", error);
    return NextResponse.json({ error: "Internal Error" }, { status: 500 });
  }
}

export async function POST(req: Request) {
  try {
    const session: any = await getServerSession(authOptions);
    if (!session) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const {
      name,
      description,
      type,
      access,
      tenantId,
      companyIds,
      allowedDomains,
      customization,
      llmScope,
      searchModes,
      maxTokens,
      temperature,
      rateLimitPerMinute,
      rateLimitPerHour,
      rateLimitPerDay,
    } = await req.json();

    // Validate required fields
    if (!name || !description || !type || !access || !tenantId) {
      return NextResponse.json(
        {
          error:
            "Missing required fields: name, description, type, access, tenantId",
        },
        { status: 400 }
      );
    }

    // Validate type
    const validTypes = ["web-snippet", "inline-embedding", "dedicated-page"];
    if (!validTypes.includes(type)) {
      return NextResponse.json(
        { error: "Invalid type. Must be one of: " + validTypes.join(", ") },
        { status: 400 }
      );
    }

    // Validate access
    const validAccess = ["public", "private"];
    if (!validAccess.includes(access)) {
      return NextResponse.json(
        { error: "Invalid access. Must be one of: " + validAccess.join(", ") },
        { status: 400 }
      );
    }

    // Validate private access has allowed users
    if (access === "private" && (!companyIds || companyIds.length === 0)) {
      return NextResponse.json(
        { error: "Private chatbots must have at least one company" },
        { status: 400 }
      );
    }

    // Generate API key for the chatbot
    const apiKey = generateApiKey();

    // Create chatbot in database
    const chatbot = await db.chatbot.create({
      data: {
        name,
        description,
        type,
        access,
        userId: session.userId,
        tenantId,
        companyIds: companyIds || [],
        // SDK-specific fields
        apiKey: apiKey,
        allowedDomains: allowedDomains || [],
        customization: customization || {},
        // LLM configuration
        llmScope: llmScope || ["INTERNAL_ONLY"],
        searchModes: searchModes || ["internal"],
        maxTokens: maxTokens || 4000,
        temperature: temperature || 0.7,
        // Rate limiting
        rateLimitPerMinute: rateLimitPerMinute || 60,
        rateLimitPerHour: rateLimitPerHour || 1000,
        rateLimitPerDay: rateLimitPerDay || 10000,
      },
    });

    return NextResponse.json({ chatbot }, { status: 201 });
  } catch (error) {
    console.error("[CHATBOTS_POST]", error);
    return NextResponse.json({ error: "Internal Error" }, { status: 500 });
  }
}
