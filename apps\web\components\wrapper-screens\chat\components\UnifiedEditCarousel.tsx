import React from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ChevronLeft, ChevronRight, Edit, MessageCircle } from "lucide-react";
import { Message } from '../types';
import { useLanguage } from "@/lib/language-context";

interface UnifiedEditCarouselProps {
  userMessage: Message;
  assistantMessage: Message;
  currentThreadIndex: number;
  totalThreads: number;
  updateDisplayIndex: (messageId: string, newIndex: number, isManuallySet: boolean) => void;
}

export const UnifiedEditCarousel: React.FC<UnifiedEditCarouselProps> = ({
  userMessage,
  assistantMessage,
  currentThreadIndex,
  totalThreads,
  updateDisplayIndex,
}) => {
  const { t } = useLanguage();

  const handleNavigateToThread = (threadIndex: number) => {
    if (!userMessage.id || !assistantMessage.id) return;

    // Update user message display index
    updateDisplayIndex(userMessage.id, threadIndex, true);

    // Update assistant message display index
    // Each thread represents a conversation pair:
    // Thread 0: Original user message (index 0) + Original assistant message (index 0)
    // Thread 1: Edited user message (index 1) + Corresponding assistant response (index 1)
    // Thread 2: Edited user message (index 2) + Corresponding assistant response (index 2)
    // etc.

    // The assistant message display index should match the thread index
    // because each edit-triggered AI response is stored at the corresponding index
    updateDisplayIndex(assistantMessage.id, threadIndex, true);
  };

  return (
    <div className="flex items-center gap-1">
      <Button
        variant="ghost"
        size="icon"
        className="h-7 w-7 rounded-full"
        onClick={() => {
          if (currentThreadIndex > 0) {
            handleNavigateToThread(currentThreadIndex - 1);
          }
        }}
        disabled={currentThreadIndex === 0}
        title={t("chat.previousEdit")}
      >
        <ChevronLeft className="h-4 w-4" />
      </Button>

      {/* Carousel indicator dots showing conversation threads */}
      <div className="flex items-center gap-1">
        {Array.from({ length: totalThreads }).map((_, i) => (
          <div
            key={i}
            className={`h-1.5 w-1.5 rounded-full transition-all duration-200 ${
              i === currentThreadIndex
                ? "bg-primary scale-110"
                : "bg-gray-300 dark:bg-gray-600 hover:bg-gray-400 dark:hover:bg-gray-500"
            }`}
            title={
              i === 0 
                ? t("chat.originalMessage") 
                : `${t("chat.editedMessage")} ${i}`
            }
            onClick={() => handleNavigateToThread(i)}
          >
          </div>
        ))}
      </div>

      <Button
        variant="ghost"
        size="icon"
        className="h-7 w-7 rounded-full"
        onClick={() => {
          if (currentThreadIndex < totalThreads - 1) {
            handleNavigateToThread(currentThreadIndex + 1);
          }
        }}
        disabled={currentThreadIndex === totalThreads - 1}
        title={t("chat.nextEdit")}
      >
        <ChevronRight className="h-4 w-4" />
      </Button>
    </div>
  );
};
