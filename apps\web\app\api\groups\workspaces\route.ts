import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import db from "@/lib/shared-db";
import { authOptions } from "@/lib/next-auth/src/auth-options";
import { withPermission } from "@/lib/permission-middleware";

// Assign a workspace to a group
async function assignWorkspaceToGroup(request: Request) {
  try {
    const session: any = await getServerSession(authOptions);

    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { groupId, workspaceId, tenantId } = await request.json();

    if (!groupId || !workspaceId || !tenantId) {
      return NextResponse.json(
        { error: "Group ID, Workspace ID, and Tenant ID are required" },
        { status: 400 }
      );
    }

    // Verify the group exists and belongs to the tenant
    const group = await db.group.findFirst({
      where: {
        id: groupId,
        tenantId,
      },
    });

    if (!group) {
      return NextResponse.json(
        { error: "Group not found or does not belong to this tenant" },
        { status: 404 }
      );
    }

    // Verify the workspace exists and belongs to the tenant
    const workspace = await db.workspace.findFirst({
      where: {
        id: workspaceId,
        tenantId,
      },
    });

    if (!workspace) {
      return NextResponse.json(
        { error: "Workspace not found or does not belong to this tenant" },
        { status: 404 }
      );
    }

    // Check if the association already exists
    const existingAssociation = await db.groupWorkspace.findFirst({
      where: {
        groupId,
        workspaceId,
      },
    });

    if (existingAssociation) {
      return NextResponse.json(
        { error: "This workspace is already assigned to this group" },
        { status: 400 }
      );
    }

    // Create the group-workspace association
    const groupWorkspace = await db.groupWorkspace.create({
      data: {
        groupId,
        workspaceId,
      },
    });

    // Get all members of the group
    const groupMembers = await db.groupMember.findMany({
      where: {
        groupId,
      },
      select: {
        userId: true,
      },
    });

    // For each group member, ensure they have access to the workspace
    for (const member of groupMembers) {
      // Check if the user already has direct access to the workspace
      const existingWorkspaceMember = await db.workspaceMember.findFirst({
        where: {
          userId: member.userId,
          workspaceId,
        },
      });

      // If the user doesn't have direct access, create a workspace member entry
      if (!existingWorkspaceMember) {
        // Get the user's membership in the tenant
        const membership = await db.membership.findFirst({
          where: {
            userId: member.userId,
            tenantId,
          },
        });

        if (membership) {
          await db.workspaceMember.create({
            data: {
              userId: member.userId,
              workspaceId,
              membershipId: membership.id,
              role: "MEMBER", // Default role for group-based access
            },
          });
        }
      }
    }

    return NextResponse.json({
      message: "Workspace assigned to group successfully",
      data: groupWorkspace,
    });
  } catch (error) {
    console.error("Error assigning workspace to group:", error);
    return NextResponse.json(
      { error: "Failed to assign workspace to group" },
      { status: 500 }
    );
  }
}

// Remove a workspace from a group
async function removeWorkspaceFromGroup(request: Request) {
  try {
    const session: any = await getServerSession(authOptions);

    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const url = new URL(request.url);
    const groupId = url.searchParams.get("groupId");
    const workspaceId = url.searchParams.get("workspaceId");
    const tenantId = url.searchParams.get("tenantId");

    if (!groupId || !workspaceId || !tenantId) {
      return NextResponse.json(
        { error: "Group ID, Workspace ID, and Tenant ID are required" },
        { status: 400 }
      );
    }

    // Verify the group exists and belongs to the tenant
    const group = await db.group.findFirst({
      where: {
        id: groupId,
        tenantId,
      },
    });

    if (!group) {
      return NextResponse.json(
        { error: "Group not found or does not belong to this tenant" },
        { status: 404 }
      );
    }

    // Verify the workspace exists and belongs to the tenant
    const workspace = await db.workspace.findFirst({
      where: {
        id: workspaceId,
        tenantId,
      },
    });

    if (!workspace) {
      return NextResponse.json(
        { error: "Workspace not found or does not belong to this tenant" },
        { status: 404 }
      );
    }

    // Find the association
    const association = await db.groupWorkspace.findFirst({
      where: {
        groupId,
        workspaceId,
      },
    });

    if (!association) {
      return NextResponse.json(
        { error: "This workspace is not assigned to this group" },
        { status: 404 }
      );
    }

    // Delete the association
    await db.groupWorkspace.delete({
      where: {
        id: association.id,
      },
    });

    return NextResponse.json({
      message: "Workspace removed from group successfully",
    });
  } catch (error) {
    console.error("Error removing workspace from group:", error);
    return NextResponse.json(
      { error: "Failed to remove workspace from group" },
      { status: 500 }
    );
  }
}

// Get all workspaces assigned to a group
async function getGroupWorkspaces(request: Request) {
  try {
    const url = new URL(request.url);
    const groupId = url.searchParams.get("groupId");
    const tenantId = url.searchParams.get("tenantId");

    if (!groupId || !tenantId) {
      return NextResponse.json(
        { error: "Group ID and Tenant ID are required" },
        { status: 400 }
      );
    }

    // Verify the group exists and belongs to the tenant
    const group = await db.group.findFirst({
      where: {
        id: groupId,
        tenantId,
      },
    });

    if (!group) {
      return NextResponse.json(
        { error: "Group not found or does not belong to this tenant" },
        { status: 404 }
      );
    }

    // Get all workspaces assigned to the group
    const groupWorkspaces = await db.groupWorkspace.findMany({
      where: {
        groupId,
      },
      include: {
        workspace: true,
      },
    });

    const workspaces = groupWorkspaces.map((gw) => gw.workspace);

    return NextResponse.json({
      data: workspaces,
      message: "Group workspaces retrieved successfully",
    });
  } catch (error) {
    console.error("Error fetching group workspaces:", error);
    return NextResponse.json(
      { error: "Failed to fetch group workspaces" },
      { status: 500 }
    );
  }
}

// Export the handlers with permission checks
export const POST = withPermission(
  assignWorkspaceToGroup,
  "UPDATE",
  "WORKSPACE"
);
export const DELETE = withPermission(
  removeWorkspaceFromGroup,
  "UPDATE",
  "WORKSPACE"
);
export const GET = withPermission(getGroupWorkspaces, "READ", "WORKSPACE");
