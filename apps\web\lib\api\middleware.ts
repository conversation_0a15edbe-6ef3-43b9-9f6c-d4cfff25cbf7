import { with<PERSON><PERSON><PERSON>ogger } from '@/lib/datadog/server';
import { NextRequest, NextResponse } from 'next/server';

/**
 * Middleware wrapper for API routes that adds Datadog logging
 */
export function withLogging(handler: (req: NextRequest, ...args: any[]) => Promise<NextResponse>) {
  return withApiLogger(handler);
}

/**
 * Combine multiple middleware functions
 */
export function withMiddleware(...middlewares: any[]) {
  return function(handler: Function) {
    return middlewares.reduceRight((acc, middleware) => middleware(acc), handler);
  };
}

/**
 * Example usage:
 * 
 * export const POST = withMiddleware(
 *   withLogging,
 *   withAuth,  // other middleware
 * )(async (req: NextRequest) => {
 *   // Your API handler code
 * });
 */
