"use client";

import React, { useState, useEffect } from "react";
import {
  Bell,
  MessageCircle,
  Share2,
  AtSign,
  Check,
  Check<PERSON>he<PERSON>,
  Wifi,
  WifiOff,
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { useLanguage } from "@/lib/language-context";
import {
  getNotifications,
  markNotificationsAsRead,
  Notification,
} from "@/services";
import { useRealTimeNotifications } from "@/components/providers/real-time-notification-provider";
import { useTranslatedToast } from "@/hooks/use-translated-toast";

export const NotificationBell: React.FC = () => {
  const { t } = useLanguage();
  const toast = useTranslatedToast();
  const [isOpen, setIsOpen] = useState(false);
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [loading, setLoading] = useState(false);

  const loadNotifications = async () => {
    try {
      setLoading(true);
      const data = await getNotifications({ status: "all", limit: 20 });
      setNotifications(data.notifications);
    } catch (error) {
      console.error("Error loading notifications:", error);
      toast.error("toast.notificationLoadFailed");
    } finally {
      setLoading(false);
    }
  };

  const loadUnreadCount = async () => {
    try {
      const data = await getNotifications({ status: "unread", limit: 1 });
      setUnreadCount(data.unreadCount);
    } catch (error) {
      console.error("Error loading unread count:", error);
    }
  };

  // Use the global real-time notification context instead of creating a new connection
  const { isConnected, connectionError, lastNotificationTime } =
    useRealTimeNotifications();

  // Refresh notifications when a new real-time notification is received
  useEffect(() => {
    if (lastNotificationTime > 0) {
      loadUnreadCount();
      if (isOpen) {
        loadNotifications();
      }
    }
  }, [lastNotificationTime, isOpen]);

  // Load notifications when popover opens
  useEffect(() => {
    if (isOpen) {
      loadNotifications();
    }
  }, [isOpen]);

  // Load initial unread count on mount
  useEffect(() => {
    loadUnreadCount();
  }, []);

  // Listen for real-time notifications and refresh
  useEffect(() => {
    if (lastNotificationTime > 0) {
      loadNotifications();
      loadUnreadCount();
    }
  }, [lastNotificationTime]);

  const handleMarkAsRead = async (notificationId: string) => {
    try {
      await markNotificationsAsRead({ notificationIds: [notificationId] });
      setNotifications((prev) =>
        prev.map((n) =>
          n.id === notificationId ? { ...n, status: "READ" as const } : n
        )
      );
      setUnreadCount((prev) => Math.max(0, prev - 1));
    } catch (error) {
      console.error("Error marking notification as read:", error);
      toast.error("Failed to mark as read");
    }
  };

  const handleMarkAllAsRead = async () => {
    try {
      await markNotificationsAsRead({ markAllAsRead: true });
      setNotifications((prev) =>
        prev.map((n) => ({ ...n, status: "READ" as const }))
      );
      setUnreadCount(0);
      toast.success("All notifications marked as read");
    } catch (error) {
      console.error("Error marking all as read:", error);
      toast.error("Failed to mark all as read");
    }
  };

  const handleNotificationClick = (notification: Notification) => {
    // Mark as read if unread
    if (notification.status === "UNREAD") {
      handleMarkAsRead(notification.id);
    }

    if (notification.chat?.ThreadShare?.[0]?.shareToken) {
      const url = `/shared/thread/${
        notification.chat.ThreadShare[0].shareToken
      }${
        notification.relatedMessage?.id
          ? `?messageId=${notification.relatedMessage.id}`
          : ""
      }`;
      window.location.href = url;
    }
  };

  const getNotificationIcon = (type: Notification["type"]) => {
    switch (type) {
      case "MENTION":
        return <AtSign className="h-4 w-4 text-blue-500" />;
      case "COMMENT_REPLY":
        return <MessageCircle className="h-4 w-4 text-green-500" />;
      case "THREAD_SHARED":
        return <Share2 className="h-4 w-4 text-purple-500" />;
      default:
        return <Bell className="h-4 w-4 text-gray-500" />;
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 1) {
      return "Just now";
    } else if (diffInHours < 24) {
      return `${Math.floor(diffInHours)}h ago`;
    } else {
      return date.toLocaleDateString();
    }
  };

  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase()
      .slice(0, 2);
  };

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          className="relative h-8 w-8 p-0 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
          title={
            isConnected
              ? t("notifications.connectedTitle")
              : connectionError || t("notifications.disconnectedTitle")
          }
        >
          <Bell className="h-4 w-4 text-gray-600 dark:text-gray-400" />
          {unreadCount > 0 && (
            <Badge
              variant="destructive"
              className="absolute -top-1 -right-1 h-4 w-4 rounded-full p-0 text-xs flex items-center justify-center animate-pulse shadow-lg"
            >
              {unreadCount > 99 ? "99+" : unreadCount}
            </Badge>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent
        className="w-96 p-0 shadow-2xl border-0 rounded-xl"
        align="end"
      >
        <div className="flex items-center justify-between p-4 border-b border-gray-100 dark:border-gray-800 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-t-xl">
          <div className="flex items-center gap-2">
            <Bell className="h-4 w-4 text-blue-600 dark:text-blue-400" />
            <h3 className="font-semibold text-sm text-gray-900 dark:text-gray-100">
              {t("notifications.title")}
            </h3>
            {unreadCount > 0 && (
              <Badge variant="secondary" className="text-xs">
                {t("notifications.newCount", { count: unreadCount })}
              </Badge>
            )}
            {/* Real-time connection status */}
            <div className="flex items-center gap-1 ml-2">
              {isConnected ? (
                <div className="flex items-center gap-1 text-green-600 dark:text-green-400">
                  <Wifi className="h-3 w-3" />
                  <span className="text-xs">{t("notifications.live")}</span>
                </div>
              ) : (
                <div className="flex items-center gap-1 text-red-600 dark:text-red-400">
                  <WifiOff className="h-3 w-3" />
                  <span className="text-xs">{t("notifications.offline")}</span>
                </div>
              )}
            </div>
          </div>
          {unreadCount > 0 && (
            <Button
              variant="ghost"
              size="sm"
              onClick={handleMarkAllAsRead}
              className="text-xs h-auto p-2 hover:bg-white/50 dark:hover:bg-gray-800/50 rounded-lg"
            >
              <CheckCheck className="h-3 w-3 mr-1" />
              {t("notifications.markAllRead")}
            </Button>
          )}
        </div>

        <ScrollArea className="h-96">
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <div className="flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400">
                <div className="animate-spin rounded-full h-4 w-4 border-2 border-blue-500 border-t-transparent"></div>
                {t("notifications.loading")}
              </div>
            </div>
          ) : notifications.length === 0 ? (
            <div className=" flex flex-col items-center justify-center py-12 text-center">
              <Bell className="h-12 w-12 text-gray-300 dark:text-gray-600 mb-3" />
              <div className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">
                {t("notifications.noNotifications")}
              </div>
              <div className="text-xs text-gray-400 dark:text-gray-500">
                {t("notifications.noNotificationsDescription")}
              </div>
            </div>
          ) : (
            <div className="py-2">
              {notifications.map((notification) => (
                <div
                  key={notification.id}
                  className={`w-[370px] mx-2 mb-2 p-3 rounded-lg cursor-pointer transition-all duration-200 border ${
                    notification.status === "UNREAD"
                      ? "bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 border-blue-200 dark:border-blue-800 shadow-sm hover:shadow-md"
                      : "bg-gray-50 dark:bg-gray-800/50 border-gray-200 dark:border-gray-700 hover:bg-gray-100 dark:hover:bg-gray-800"
                  }`}
                  onClick={() => handleNotificationClick(notification)}
                >
                  <div className="flex items-start gap-3">
                    <div className="flex-shrink-0 mt-0.5">
                      {getNotificationIcon(notification.type)}
                    </div>

                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-2">
                        {notification.triggerUser && (
                          <Avatar className="h-6 w-6 ring-2 ring-white dark:ring-gray-800">
                            <AvatarImage src={notification.triggerUser.image} />
                            <AvatarFallback className="text-xs bg-gradient-to-br from-blue-500 to-purple-600 text-white">
                              {getInitials(notification.triggerUser.name)}
                            </AvatarFallback>
                          </Avatar>
                        )}
                        <div className="flex-1 min-w-0">
                          <span className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate block">
                            {notification.title}
                          </span>
                          <span className="text-xs text-gray-500 dark:text-gray-400">
                            {formatDate(notification.createdAt)}
                          </span>
                        </div>
                        {notification.status === "UNREAD" && (
                          <div className="flex items-center gap-1">
                            <div className="h-2 w-2 bg-blue-500 rounded-full animate-pulse" />
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleMarkAsRead(notification.id);
                              }}
                              className="h-6 w-6 p-0 hover:bg-blue-100 dark:hover:bg-blue-900/50 rounded-full"
                            >
                              <Check className="h-3 w-3" />
                            </Button>
                          </div>
                        )}
                      </div>

                      <p className="text-sm text-gray-700 dark:text-gray-300 line-clamp-2 leading-relaxed">
                        {notification.content}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </ScrollArea>
      </PopoverContent>
    </Popover>
  );
};
