import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import db from "@/lib/shared-db";
import { authOptions } from "@/lib/next-auth/src/auth-options";
import crypto from "crypto";
import {
  checkActiveSubscription,
  checkUserLimit,
} from "@/lib/subscription-check";
import { withPermission } from "@/lib/permission-middleware";
import { hasPermission } from "@/lib/permissions";
// Wrap the POST handler with permission middleware to check CREATE permission for MEMBER resource
export const POST = withPermission(
  async (request: Request) => {
    try {
      const session: any = await getServerSession(authOptions);

      if (!session?.user?.email) {
        return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
      }

      const { email, role, slug: rawSlug } = await request.json();

      if (!email || !role || !rawSlug) {
        return NextResponse.json(
          { error: "Email, role, and workspaceId are required" },
          { status: 400 }
        );
      }
      const slug = decodeURI(rawSlug);

      // Get the workspace and verify it exists
      const workspace = await db.workspace.findFirst({
        where: { slug },
        include: {
          workspaceMember: true,
        },
      });

      if (workspace?.slug !== slug) {
        return NextResponse.json(
          { error: "Workspace not found" },
          { status: 404 }
        );
      }

      // Check if tenant has an active subscription
      const { hasActiveSubscription } = await checkActiveSubscription(
        workspace.tenantId
      );

      if (!hasActiveSubscription) {
        return NextResponse.json(
          { error: "You need an active subscription to invite members" },
          { status: 403 }
        );
      }

      // Verify the current user has admin access to this workspace
      const currentUserMember = workspace?.workspaceMember?.find(
        (member: any) => member.userId === session.userId
      );

      if (
        !currentUserMember ||
        !(
          currentUserMember.role === "ADMIN" ||
          currentUserMember.role === "OWNER"
        )
      ) {
        return NextResponse.json(
          {
            error: "You don't have permission to add members to this workspace",
          },
          { status: 403 }
        );
      }

      const emailHash = crypto.createHash("sha256").update(email).digest("hex");
      // Check if user exists
      const user = await db.user.findUnique({
        where: {
          emailHash,
        },
        include: {
          membership: {
            where: {
              tenantId: workspace.tenantId,
            },
          },
        },
      });

      if (!user) {
        return NextResponse.json(
          { error: "User not found. Add the user first" },
          { status: 404 }
        );
      }

      // Verify user is a member of the organization
      if (user.membership.length === 0) {
        return NextResponse.json(
          { error: "User must be a member of the organization first" },
          { status: 403 }
        );
      }

      // Check if user is already a member of the workspace
      const existingMember = await db.workspaceMember.findFirst({
        where: {
          userId: user.id,
          workspaceId: workspace?.id,
        },
      });

      if (existingMember) {
        return NextResponse.json(
          { error: "User is already a member of this workspace" },
          { status: 400 }
        );
      }

      // Create workspace member
      const workspaceMember = await db.workspaceMember.create({
        data: {
          userId: user.id,
          workspaceId: workspace?.id,
          membershipId: user.membership[0].id,
          role: role,
        },
        include: {
          user: true,
          workspace: true,
        },
      });

      return NextResponse.json({
        message: "Workspace member added successfully",
        data: workspaceMember,
      });
    } catch (error) {
      console.error("Error adding workspace member:", error);
      return NextResponse.json(
        { error: "Failed to add workspace member" },
        { status: 500 }
      );
    }
  },
  "CREATE",
  "MEMBER"
);

// Wrap the GET handler with permission middleware to check READ permission for MEMBER resource
export const GET = withPermission(
  async (request: Request) => {
    try {
      const url = new URL(request.url);
      const tenantId = request.headers.get("x-tenant-id");
      const rawWorkspaceSlug = url.searchParams.get("workspaceSlug");
      const workspaceSlug = rawWorkspaceSlug
        ? decodeURI(rawWorkspaceSlug)
        : rawWorkspaceSlug;

      if (!workspaceSlug || !tenantId) {
        return NextResponse.json(
          { error: "WorkspaceId is required" },
          { status: 400 }
        );
      }

      // Get the workspace and verify it exists
      const workspace = await db.workspace.findFirst({
        where: { slug: workspaceSlug, tenantId },
        include: {
          workspaceMember: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                  image: true,
                },
              },
            },
          },
        },
      });

      if (!workspace) {
        return NextResponse.json(
          { error: "Workspace not found" },
          { status: 404 }
        );
      }

      return NextResponse.json({
        data: workspace?.workspaceMember,
        message: "Workspace members retrieved successfully",
      });
    } catch (error) {
      console.error("Error fetching workspace members:", error);
      return NextResponse.json(
        { error: "Failed to fetch workspace members" },
        { status: 500 }
      );
    }
  },
  "READ",
  "MEMBER"
);

// Wrap the DELETE handler with permission middleware to check DELETE permission for MEMBER resource
export const DELETE = withPermission(
  async (request: Request) => {
    try {
      const session: any = await getServerSession(authOptions);

      if (!session?.user?.email) {
        return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
      }

      const url = new URL(request.url);
      const rawWorkspaceSlug = url.searchParams.get("workspaceSlug");
      const workspaceSlug = rawWorkspaceSlug
        ? decodeURI(rawWorkspaceSlug)
        : rawWorkspaceSlug;
      const memberId = url.searchParams.get("memberId");

      if (!workspaceSlug || !memberId) {
        return NextResponse.json(
          { error: "Workspace slug and user ID are required" },
          { status: 400 }
        );
      }

      // Get the workspace and verify it exists
      const workspace = await db.workspace.findFirst({
        where: { slug: workspaceSlug },
        include: {
          workspaceMember: true,
        },
      });

      if (!workspace) {
        return NextResponse.json(
          { error: "Workspace not found" },
          { status: 404 }
        );
      }

      // Verify the current user has admin access to this workspace
      const currentUserMember = workspace.workspaceMember.find(
        (member: any) => member.userId === session.userId
      );

      if (
        !currentUserMember ||
        !(
          currentUserMember.role === "ADMIN" ||
          currentUserMember.role === "OWNER"
        )
      ) {
        return NextResponse.json(
          {
            error:
              "You don't have permission to remove members from this workspace",
          },
          { status: 403 }
        );
      }

      // Find the member to be removed
      const memberToRemove = workspace.workspaceMember.find(
        (member: any) => member?.id === memberId
      );

      if (!memberToRemove) {
        return NextResponse.json(
          { error: "Member not found in this workspace" },
          { status: 404 }
        );
      }

      // Prevent removing the last owner
      if (memberToRemove.role === "OWNER") {
        const ownerCount = workspace.workspaceMember.filter(
          (member: any) => member.role === "OWNER"
        ).length;

        if (ownerCount <= 1) {
          return NextResponse.json(
            { error: "Cannot remove the last owner of the workspace" },
            { status: 400 }
          );
        }
      }

      // Delete the workspace member
      await db.workspaceMember.delete({
        where: {
          id: memberToRemove.id,
        },
      });

      return NextResponse.json({
        message: "Workspace member removed successfully",
      });
    } catch (error) {
      console.error("Error removing workspace member:", error);
      return NextResponse.json(
        { error: "Failed to remove workspace member" },
        { status: 500 }
      );
    }
  },
  "DELETE",
  "MEMBER"
);
