"use server";

import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/next-auth";
import dbPromise from "@/lib/shared-db";
import { revalidatePath } from "next/cache";

interface Plan {
  id: string;
  name: string;
  type: string;
  description?: string;
  price?: number;
  includedUsers: number;
  vectorStoreGB: number;
  webSearchLimit: number;
  additionalUserFee?: number;
}

interface ChangePlanResult {
  success: boolean;
  message: string;
  subscription?: {
    id: string;
    planName: string;
    planType: string;
    startDate: Date;
    isActive: boolean;
  };
  error?: string;
}

interface GetAvailablePlansResult {
  success: boolean;
  currentPlan?: any;
  availablePlans?: Plan[];
  error?: string;
}

export async function getAvailablePlans(tenantId: string): Promise<GetAvailablePlansResult> {
  const session = await getServerSession(authOptions);

  if (!session) {
    return {
      success: false,
      error: "Unauthorized"
    };
  }

  try {
    const db = await dbPromise;

    // Get tenant's current plan
    const tenant = await db.tenant.findUnique({
      where: { id: tenantId },
      include: {
        Subscription: {
          where: { isActive: true },
          include: { plan: true }
        }
      }
    });

    if (!tenant) {
      return {
        success: false,
        error: "Tenant not found"
      };
    }

    const currentPlanId = tenant.Subscription[0]?.planId;

    // Get all active plans except the current one
    const availablePlans = await db.plan.findMany({
      where: {
        isActive: true,
        ...(currentPlanId && { id: { not: currentPlanId } })
      },
      select: {
        id: true,
        name: true,
        type: true,
        description: true,
        price: true,
        includedUsers: true,
        vectorStoreGB: true,
        webSearchLimit: true,
        additionalUserFee: true
      },
      orderBy: [
        { type: "asc" },
        { price: "asc" }
      ]
    });

    return {
      success: true,
      currentPlan: tenant.Subscription[0]?.plan || null,
      availablePlans
    };

  } catch (error) {
    console.error("Error fetching available plans:", error);
    return {
      success: false,
      error: "Failed to fetch available plans"
    };
  }
}

export async function changeTenantPlan(
  tenantId: string, 
  planId: string, 
  activateAccount: boolean = false
): Promise<ChangePlanResult> {
  const session = await getServerSession(authOptions);

  if (!session) {
    return {
      success: false,
      message: "Unauthorized access",
      error: "Unauthorized"
    };
  }

  try {
    const db = await dbPromise;

    // Validate required fields
    if (!planId) {
      return {
        success: false,
        message: "Plan ID is required",
        error: "Plan ID is required"
      };
    }

    // Verify tenant exists
    const tenant = await db.tenant.findUnique({
      where: { id: tenantId },
      include: {
        Subscription: {
          where: { isActive: true },
          include: { plan: true }
        }
      }
    });

    if (!tenant) {
      return {
        success: false,
        message: "Tenant not found",
        error: "Tenant not found"
      };
    }

    // Verify plan exists and is active
    const newPlan = await db.plan.findUnique({
      where: { id: planId }
    });

    if (!newPlan || !newPlan.isActive) {
      return {
        success: false,
        message: "Invalid or inactive plan selected",
        error: "Invalid or inactive plan selected"
      };
    }

    // Check if tenant already has this plan
    const currentSubscription = tenant.Subscription[0];
    if (currentSubscription && currentSubscription.planId === planId) {
      return {
        success: false,
        message: "Tenant is already on this plan",
        error: "Tenant is already on this plan"
      };
    }

    // Use transaction to ensure atomicity
    const result = await db.$transaction(async (prisma) => {
      // Deactivate current subscription if exists
      if (currentSubscription) {
        await prisma.subscription.update({
          where: { id: currentSubscription.id },
          data: {
            isActive: false,
            endDate: new Date()
          }
        });
      }

      // Create new subscription
      const newSubscription = await prisma.subscription.create({
        data: {
          tenantId,
          planId,
          isActive: true,
          startDate: new Date(),
          additionalUsers: 0,
          additionalStorageGB: 0
        },
        include: {
          plan: true,
          tenant: true
        }
      });

      return newSubscription;
    });

    // Log the plan change for audit purposes
    console.log(`Plan changed for tenant ${tenantId}: ${currentSubscription?.plan?.name || 'No Plan'} -> ${newPlan.name}`);

    // Revalidate the tenants page and tenant detail page
    revalidatePath('/tenants');
    revalidatePath(`/tenants/${tenantId}`);

    return {
      success: true,
      message: "Plan changed successfully",
      subscription: {
        id: result.id,
        planName: result.plan.name,
        planType: result.plan.type,
        startDate: result.startDate,
        isActive: result.isActive
      }
    };

  } catch (error) {
    console.error("Error changing plan:", error);
    return {
      success: false,
      message: "Failed to change plan",
      error: "Failed to change plan"
    };
  }
}

export async function suspendTenant(tenantId: string): Promise<{ success: boolean; message?: string; error?: string }> {
  const session = await getServerSession(authOptions);

  if (!session) {
    return {
      success: false,
      error: "Unauthorized"
    };
  }

  try {
    // TODO: Implement tenant suspension logic
    // This would typically involve deactivating subscriptions, blocking access, etc.
    console.log(`Suspending tenant: ${tenantId}`);
    
    // Revalidate the tenants page
    revalidatePath('/tenants');
    revalidatePath(`/tenants/${tenantId}`);

    return {
      success: true,
      message: "Tenant suspended successfully"
    };

  } catch (error) {
    console.error("Error suspending tenant:", error);
    return {
      success: false,
      error: "Failed to suspend tenant"
    };
  }
}

export async function reactivateTenant(tenantId: string): Promise<{ success: boolean; message?: string; error?: string }> {
  const session = await getServerSession(authOptions);

  if (!session) {
    return {
      success: false,
      error: "Unauthorized"
    };
  }

  try {
    // TODO: Implement tenant reactivation logic
    // This would typically involve reactivating subscriptions, restoring access, etc.
    console.log(`Reactivating tenant: ${tenantId}`);
    
    // Revalidate the tenants page
    revalidatePath('/tenants');
    revalidatePath(`/tenants/${tenantId}`);

    return {
      success: true,
      message: "Tenant reactivated successfully"
    };

  } catch (error) {
    console.error("Error reactivating tenant:", error);
    return {
      success: false,
      error: "Failed to reactivate tenant"
    };
  }
}
