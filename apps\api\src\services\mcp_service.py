"""
MCP Service for Swiss Knowledge Hub

This service manages MCP agent instances and provides integration with the
existing workspace management system.
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional, AsyncGenerator
from bson import ObjectId
from src.agents.mcp_agent import MCPAgent, get_translation
import datetime

logger = logging.getLogger(__name__)

class MCPService:
    """
    Service for managing MCP agents and their lifecycle.
    Provides integration with workspace management and user authentication.
    """

    def __init__(self):
        self.agents: Dict[str, MCPAgent] = {}  # tenant_id -> MCPAgent
        self.db_client = None
        logger.info("MCP Service initialized")

    def _is_identity_question(self, query: str) -> bool:
        """Check if the query is asking about the AI's identity or creator."""
        query_lower = query.lower().strip()
        identity_patterns = [
            # English patterns
            "who made you", "who made u", "who created you", "who created u",
            "who built you", "who built u", "who developed you", "who developed u",
            "what are you", "what are u", "who are you", "who are u",
            "what is your name", "who is your creator", "who is ur creator",
            "who designed you", "who designed u", "what company made you", "what company made u",
            "who owns you", "who owns u", "where do you come from", "where do u come from",
            "what organization created you", "what organization created u",
            "who is behind you", "who is behind u", "who programmed you", "who programmed u",
            "who trained you", "who trained u", "who's your creator", "whos your creator",

            # German patterns
            "wer hat dich gemacht", "wer hat dich erstellt", "wer hat dich entwickelt", "wer hat dich erschaffen",
            "von wem wurdest du gemacht", "von wem wurdest du erstellt", "von wem wurdest du entwickelt", "von wem wurdest du erschaffen",
            "wer bist du", "was bist du", "wie heißt du", "wie heisst du",
            "wer ist dein ersteller", "wer ist dein schöpfer", "wer ist dein entwickler", "wer ist dein erschaffer",
            "welche firma hat dich gemacht", "welche organisation hat dich erstellt", "welche firma hat dich erschaffen",
            "wer steht hinter dir", "wer hat dich programmiert", "wer hat dich trainiert",
            "von welcher firma kommst du", "von welcher organisation kommst du",
            "wer besitzt dich", "wem gehörst du", "woher kommst du"
        ]

        is_identity = any(pattern in query_lower for pattern in identity_patterns)
        logger.info(f"MCP SERVICE IDENTITY CHECK: Query='{query}' | Lower='{query_lower}' | IsIdentity={is_identity}")
        if is_identity:
            matched_pattern = next((pattern for pattern in identity_patterns if pattern in query_lower), "unknown")
            logger.info(f"MCP SERVICE IDENTITY MATCH: Pattern='{matched_pattern}'")
        return is_identity

    def _detect_question_language(self, query: str) -> str:
        """Detect the language of the question based on content."""
        query_lower = query.lower().strip()

        # German language indicators
        german_indicators = [
            "wer", "was", "wie", "wo", "warum", "wann", "welche", "welcher", "welches",
            "von wem", "hat dich", "bist du", "sind sie", "können sie", "ist dein",
            "heißt", "heisst", "gemacht", "erstellt", "entwickelt", "erschaffen",
            "firma", "organisation", "unternehmen", "schöpfer", "entwickler", "ersteller"
        ]

        # Count German indicators
        german_count = sum(1 for indicator in german_indicators if indicator in query_lower)

        # If we find German indicators and it's an identity question, assume German
        if german_count > 0 and self._is_identity_question(query):
            logger.info(f"MCP SERVICE LANGUAGE DETECTION: Detected German in identity question: '{query}' (indicators: {german_count})")
            return "de"

        # Default to English
        return "en"

    def _get_identity_response(self, language: str = "en") -> str:
        """Get the appropriate identity response based on language."""
        if language == "de":
            return "Ich bin ein KI-Assistent von Swiss Knowledge Hub, der entwickelt wurde, um Ihnen bei der Suche nach Informationen und der Beantwortung von Fragen innerhalb der Wissensdatenbank Ihrer Organisation zu helfen. Ich bin hier, um Sie bei Ihren Anfragen zu unterstützen und hilfreiche Erkenntnisse basierend auf den Daten und Ressourcen Ihres Unternehmens zu liefern."
        else:
            return "I am an AI assistant created by Swiss Knowledge Hub, designed to help you find information and answer questions within your organization's knowledge base. I'm here to assist with your queries and provide helpful insights based on your company's data and resources."

    async def get_or_create_agent(self, tenant_id: str) -> MCPAgent:
        """Get existing agent for tenant or create a new one"""
        if tenant_id not in self.agents:
            agent = MCPAgent(db_client=self.db_client)
            self.agents[tenant_id] = agent
            logger.info(f"Created new MCP agent for tenant {tenant_id}")
        
        return self.agents[tenant_id]

    async def connect_agent_to_server(
        self, 
        tenant_id: str, 
        server_config: Dict[str, Any]
    ) -> bool:
        """Connect an agent to an MCP server"""
        try:
            agent = await self.get_or_create_agent(tenant_id)
            
            # Disconnect from any existing server first
            await agent.disconnect()
            
            # Connect to the new server
            success = await agent.connect_to_server(server_config)
            
            if success:
                logger.info(f"Agent for tenant {tenant_id} connected to server {server_config.get('name', 'Unknown')}")
            else:
                logger.error(f"Failed to connect agent for tenant {tenant_id} to server")
            
            return success
            
        except Exception as e:
            logger.error(f"Error connecting agent to server: {e}")
            return False

    async def disconnect_agent(self, tenant_id: str):
        """Disconnect agent from MCP server"""
        if tenant_id in self.agents:
            try:
                await self.agents[tenant_id].disconnect()
                logger.info(f"Disconnected agent for tenant {tenant_id}")
            except Exception as e:
                logger.error(f"Error disconnecting agent for tenant {tenant_id}: {e}")

    async def get_available_tools(self, tenant_id: str) -> List[Dict[str, Any]]:
        """Get available tools for a tenant's agent"""
        if tenant_id not in self.agents:
            return []
        
        return self.agents[tenant_id].get_available_tools()

    async def query_with_mcp(
        self,
        tenant_id: str,
        question: str,
        tools: List[Dict[str, Any]],
        stream: bool = False,
        previous_message: Optional[str] = None,
        language: str = "en"
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """
        Query using MCP tools from multiple servers

        Args:
            tenant_id: Tenant ID
            question: User question
            tools: List of tools from multiple servers
            stream: Whether to stream the response
            previous_message: Previous message for context
        """
        # Handle identity questions immediately (before any other processing)
        if self._is_identity_question(question):
            # Detect the actual language of the question
            detected_language = self._detect_question_language(question)
            logger.info(f"MCP SERVICE: Identity question detected, detected language: {detected_language}, returning Swiss Knowledge Hub response")
            yield {
                "answer": self._get_identity_response(detected_language),
                "tools_used": [],
                "done": True
            }
            return

        start_time = datetime.datetime.now(datetime.timezone.utc)

        try:
            # Use the multi-server query processing
            async for chunk in self.process_query_with_multi_server_tools(
                tenant_id=tenant_id,
                question=question,
                tools=tools,
                stream=stream,
                previous_message=previous_message,
                language=language
            ):
                # Add timing information to final chunk
                if chunk.get("done"):
                    elapsed_time = (datetime.datetime.now(datetime.timezone.utc) - start_time).total_seconds()
                    chunk["elapsed_time"] = elapsed_time

                yield chunk

        except Exception as e:
            logger.error(f"Error in MCP query: {e}")
            elapsed_time = (datetime.datetime.now(datetime.timezone.utc) - start_time).total_seconds()
            yield {
                "type": "error",
                "error": f"MCP query failed: {str(e)}",
                "answer": "",
                "tools_used": [],
                "elapsed_time": elapsed_time,
                "done": True
            }

    async def process_query_with_multi_server_tools(
        self,
        tenant_id: str,
        question: str,
        tools: List[Dict[str, Any]],
        stream: bool = False,
        previous_message: Optional[str] = None,
        language: str = "en"
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """
        Process a query using tools from multiple MCP servers

        Args:
            tenant_id: Tenant ID
            question: User question
            tools: List of tools with server information
            stream: Whether to stream the response
            previous_message: Previous message for context
        """
        # Handle identity questions immediately (before any other processing)
        if self._is_identity_question(question):
            # Detect the actual language of the question
            detected_language = self._detect_question_language(question)
            logger.info(f"MCP SERVICE: Identity question detected in multi-server processing, detected language: {detected_language}, returning Swiss Knowledge Hub response")
            yield {
                "answer": self._get_identity_response(detected_language),
                "tools_used": [],
                "done": True
            }
            return

        from openai import AsyncAzureOpenAI
        import os
        import json

        try:
            # Initialize Azure OpenAI client
            azure_client = AsyncAzureOpenAI(
                api_key=os.getenv("DEEPSEEK_API_KEY"),
                api_version=os.getenv("AZURE_OPENAI_API_VERSION", "2024-02-01"),
                azure_endpoint=os.getenv("AZURE_OPENAI_ENDPOINT"),
                azure_deployment=os.getenv("AZURE_VISION_MODEL")
            )

            # Build messages
            messages = []

            # Add previous message for context if provided
            if previous_message:
                logger.info(f"Adding previous message context: {previous_message[:100]}...")
                messages.append({"role": "assistant", "content": previous_message})

            # Add current user query
            messages.append({"role": "user", "content": question})

            messages.insert(0,{
                        "role": "system",
                        "content": """You are an AI assistant created by Swiss Knowledge Hub that helps users by using available tools to gather information and provide comprehensive answers.

            IMPORTANT IDENTITY INSTRUCTIONS:
            - You are created by Swiss Knowledge Hub, NOT by DeepSeek, OpenAI, or any other company
            - When asked about your identity, creator, or who made you, ALWAYS respond that you are created by Swiss Knowledge Hub
            - Your purpose is to help users within their organization's knowledge base
            - Never claim to be created by DeepSeek, OpenAI, Anthropic, or any other AI company

            IMPORTANT: Format all your responses using proper Markdown syntax:
            - Use headers (# ## ###) to organize content
            - Use bullet points and numbered lists for clarity
            - Use code blocks (```) for code snippets
            - Use **bold** and *italic* for emphasis
            - Use tables when presenting structured data
            - Use blockquotes (>) for important notes
            - Ensure proper spacing and formatting for readability

            When using tools, explain what you're doing and present the results in a well-structured, readable format."""
                    })

            # Convert tools to OpenAI format
            available_tools = []
            tool_server_mapping = {}  # Map tool names to their server info

            for tool in tools:
                logger.debug(f"Processing tool: {tool}")
                tool_name = tool.get("name", "unknown")
                # Store server mapping for tool execution
                tool_server_mapping[tool_name] = {
                    "server_name": tool.get("server_name"),
                    "server_id": tool.get("server_id"),
                    "server_type": tool.get("server_type", "stdio")
                }

                # Debug logging for first few tools
                if len(available_tools) < 3:
                    logger.info(f"Processing tool {tool_name}: server_name={tool.get('server_name')}, server_id={tool.get('server_id')}")

                available_tools.append({
                    "type": "function",
                    "function": {
                        "name": tool_name,
                        "description": tool.get("description", ""),
                        "parameters": tool.get("input_schema", {})
                    }
                })

            logger.info(f"Processing query with {len(available_tools)} tools from multiple servers")

            # Make initial API call
            response = await azure_client.chat.completions.create(
                model=os.getenv("AZURE_VISION_MODEL", "gpt-4o"),
                messages=messages,
                tools=available_tools if available_tools else None,
                temperature=0.7,
                stream=stream
            )

            if stream:
                async for chunk in self._handle_streaming_multi_server_response(
                    response, messages, tool_server_mapping, tenant_id, language
                ):
                    yield chunk
            else:
                result = await self._handle_non_streaming_multi_server_response(
                    response, messages, tool_server_mapping, tenant_id, language
                )
                yield result

        except Exception as e:
            import traceback
            logger.error(f"Error processing multi-server query: {e}")
            logger.error(f"Traceback: {traceback.format_exc()}")
            yield {
                "type": "error",
                "error": f"Multi-server query processing failed: {str(e)}",
                "answer": "",
                "tools_used": [],
                "done": True
            }

    async def get_tools_for_server(
        self,
        tenant_id: str,
        server_configs: List[Dict[str, Any]],
        language: str = "en"
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """Get tools for a list of servers in parallel"""
        logger.info(f"Fetching tools for {len(server_configs)} servers for tenant {tenant_id} in parallel")

        all_tools = []
        completed_servers = 0
        total_servers = len(server_configs)

        async def fetch_server_tools(server_config: Dict[str, Any],language: str = "en") -> Dict[str, Any]:
            """Fetch tools from a single server"""
            server_name = server_config.get("name", "Unknown")

            try:
                logger.info(f"Connecting to server: {server_name}")

                # Create a separate agent instance for this server to avoid conflicts
                # Since each server connection replaces the previous one in the agent
                agent = MCPAgent(db_client=self.db_client)

                # Connect to the server
                success = await agent.connect_to_server(server_config)

                if not success:
                    logger.error(f"Failed to connect to server: {server_name}")
                    return {
                        "type": "server_error",
                        "server_name": server_name,
                        "error": f"Failed to connect to server: {server_name}",
                        "success": False
                    }

                # Get tools from the connected server
                tools = agent.get_available_tools()

                # Add server information to each tool
                server_tools = []
                for tool in tools:
                    tool_with_server = {
                        **tool,
                        "server_name": server_name,
                        "server_id": server_config.get("id"),
                        "server_type": server_config.get("server_type", "stdio")
                    }
                    server_tools.append(tool_with_server)

                logger.info(f"Retrieved {len(tools)} tools from server: {server_name}")

                # Clean up the agent connection
                await agent.disconnect()

                return {
                    "type": "server_tools",
                    "server_name": server_name,
                    "server_id": server_config.get("id"),
                    "tools": server_tools,
                    "tools_count": len(server_tools),
                    "success": True
                }

            except Exception as e:
                logger.error(f"Error processing server {server_name}: {e}")
                return {
                    "type": "server_error",
                    "server_name": server_name,
                    "error": f"Error processing server {server_name}: {str(e)}",
                    "success": False
                }

        # Start all server connections in parallel
        tasks = [fetch_server_tools(config, language) for config in server_configs]

        # Process results as they complete
        for coro in asyncio.as_completed(tasks):
            try:
                result = await coro
                completed_servers += 1

                # Yield status update for frontend
                yield {
                    "type": "server_status",
                    "status": "processing",
                    "server_name": result.get("server_name", "Unknown"),
                    "message": get_translation("fetching_all_tools", language, count=completed_servers, total=total_servers),
                    "progress": completed_servers / total_servers,
                    "done": False
                }

                if result["success"]:
                    # Add tools to the overall collection
                    all_tools.extend(result["tools"])

                    # Yield tools for this server
                    yield {
                        "type": "server_tools",
                        "server_name": result["server_name"],
                        "server_id": result.get("server_id"),
                        "tools": result["tools"],
                        "tools_count": result["tools_count"],
                        "done": False
                    }
                else:
                    # Yield error for this server
                    yield {
                        "type": "server_error",
                        "server_name": result["server_name"],
                        "error": result["error"],
                        "done": False
                    }

            except Exception as e:
                logger.error(f"Error processing server task: {e}")
                completed_servers += 1
                yield {
                    "type": "server_error",
                    "server_name": "Unknown",
                    "error": f"Task processing error: {str(e)}",
                    "done": False
                }

        # Yield final summary
        yield {
            "type": "tools_summary",
            "total_servers": total_servers,
            "total_tools": len(all_tools),
            "all_tools": all_tools,
            "done": True
        }
 

    async def get_mcp_servers_for_tenant(self, tenant_id: str) -> List[Dict[str, Any]]:
        """Get available MCP servers for a tenant from database"""
        if self.db_client is None:
            return []
        
        try:
            # Query MCP servers from database
            servers_cursor = self.db_client.MCPServer.find({

                "tenantId": ObjectId(tenant_id),
                "status": {"$in": ["ACTIVE", "INACTIVE"]}  # Exclude ERROR status
            })
            
            servers = []
            async for server in servers_cursor:
                config = {
                    "id": str(server["_id"]),
                    "name": server["name"],
                    "description": server.get("description", ""),
                    "server_type": server.get("serverType", "STDIO").lower(),
                    "status": server["status"]
                }

                # Add server-type specific fields
                if config["server_type"] == "stdio":
                    config.update({
                        "command": server["command"],
                        "args": server.get("args", []),
                        "env": server.get("env", {})
                    })
                elif config["server_type"] == "http":
                    config.update({
                        "url": server["url"],
                        "headers": server.get("headers", {})
                    })

                servers.append(config)
            
            return servers
            
        except Exception as e:
            logger.error(f"Error fetching MCP servers for tenant {tenant_id}: {e}")
            return []

    async def cleanup_agent(self, tenant_id: str):
        """Clean up agent resources for a tenant"""
        if tenant_id in self.agents:
            try:
                await self.agents[tenant_id].disconnect()
                del self.agents[tenant_id]
                logger.info(f"Cleaned up agent for tenant {tenant_id}")
            except Exception as e:
                logger.error(f"Error cleaning up agent for tenant {tenant_id}: {e}")

    async def cleanup_all_agents(self):
        """Clean up all agent resources"""
        for tenant_id in list(self.agents.keys()):
            await self.cleanup_agent(tenant_id)

    async def _handle_streaming_multi_server_response(
        self,
        response,
        messages: List[Dict[str, Any]],
        tool_server_mapping: Dict[str, Dict[str, Any]],
        tenant_id: str,
        language: str = "en"
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """Handle streaming response for multi-server tools"""
        import json

        content_buffer = ""
        tool_calls = []

        async for chunk in response:
            if not chunk.choices:
                continue

            choice = chunk.choices[0]
            delta = choice.delta

            # Handle content
            if delta.content:
                content_buffer += delta.content
                yield {
                    "type": "content",
                    "content": delta.content,
                    "done": False
                }

            # Handle tool calls
            if delta.tool_calls:
                for tool_call_delta in delta.tool_calls:
                    # Ensure we have enough tool_calls in our list
                    while len(tool_calls) <= tool_call_delta.index:
                        tool_calls.append({
                            "id": "",
                            "function": {"name": "", "arguments": ""}
                        })

                    # Update the tool call at the specified index
                    if tool_call_delta.id:
                        tool_calls[tool_call_delta.index]["id"] = tool_call_delta.id

                    if tool_call_delta.function:
                        if tool_call_delta.function.name:
                            tool_calls[tool_call_delta.index]["function"]["name"] = tool_call_delta.function.name
                        if tool_call_delta.function.arguments:
                            tool_calls[tool_call_delta.index]["function"]["arguments"] += tool_call_delta.function.arguments

            # Check if response is finished
            if choice.finish_reason:
                if choice.finish_reason == "tool_calls" and tool_calls:
                    # Execute tools and continue conversation
                    async for tool_result in self._execute_multi_server_tools_and_continue(
                        tool_calls, messages, content_buffer, tool_server_mapping, tenant_id, stream=True, language=language
                    ):
                        yield tool_result
                else:
                    # Final response
                    yield {
                        "type": "final",
                        "answer": content_buffer,
                        "tools_used": [],
                        "done": True
                    }

    async def _handle_non_streaming_multi_server_response(
        self,
        response,
        messages: List[Dict[str, Any]],
        tool_server_mapping: Dict[str, Dict[str, Any]],
        tenant_id: str,
        language: str = "en"
    ) -> Dict[str, Any]:
        """Handle non-streaming response for multi-server tools"""
        try:
            choice = response.choices[0]
            message = choice.message

            # Check if there are tool calls
            if message.tool_calls:
                # Execute tools and get final response
                async for result in self._execute_multi_server_tools_and_continue(
                    message.tool_calls, messages, message.content or "",
                    tool_server_mapping, tenant_id, stream=False, language=language
                ):
                    return result
            else:
                # Direct response without tool calls
                return {
                    "type": "final",
                    "answer": message.content or "",
                    "tools_used": [],
                    "done": True
                }

        except Exception as e:
            logger.error(f"Error handling non-streaming multi-server response: {e}")
            return {
                "type": "error",
                "error": f"Response handling failed: {str(e)}",
                "answer": "",
                "tools_used": [],
                "done": True
            }

    async def _execute_multi_server_tools_and_continue(
        self,
        tool_calls,
        messages: List[Dict[str, Any]],
        initial_content: str,
        tool_server_mapping: Dict[str, Dict[str, Any]],
        tenant_id: str,
        stream: bool = False,
        language: str = "en"
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """Execute tool calls from multiple servers and continue the conversation"""
        import json
        from openai import AsyncAzureOpenAI
        import os

        tools_used = []
        tool_results = []

        # Add assistant message with tool calls to conversation
        assistant_message = {
            "role": "assistant",
            "content": initial_content or None,
            "tool_calls": []
        }

        # Convert tool calls to the format expected by OpenAI
        for i, tool_call in enumerate(tool_calls):
            if hasattr(tool_call, 'id'):
                call_id = tool_call.id
                function_name = tool_call.function.name
                function_args = tool_call.function.arguments
            else:
                # Handle dict format
                call_id = tool_call.get("id", f"call_{i}")
                function_name = tool_call.get("function", {}).get("name", "")
                function_args = tool_call.get("function", {}).get("arguments", "{}")

            assistant_message["tool_calls"].append({
                "id": call_id,
                "type": "function",
                "function": {
                    "name": function_name,
                    "arguments": function_args
                }
            })

        messages.append(assistant_message)

        # Execute each tool call
        for i, tool_call in enumerate(tool_calls):
            if hasattr(tool_call, 'function') and tool_call.function:
                function = tool_call.function
                tool_name = function.name
                call_id = tool_call.id
            else:
                # Handle dict format
                function_data = tool_call.get("function", {})
                tool_name = function_data.get("name", "")
                call_id = tool_call.get("id", f"call_{i}")
                function = type('obj', (object,), {
                    'name': tool_name,
                    'arguments': function_data.get("arguments", "{}")
                })

            try:
                tool_args = json.loads(function.arguments) if function.arguments else {}
            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse tool arguments: {e}")
                tool_args = {}

            # Get server info for this tool
            server_info = tool_server_mapping.get(tool_name, {})
            server_name = server_info.get("server_name", "Unknown")
            server_id = server_info.get("server_id")

            # Debug logging
            logger.info(f"Tool {tool_name} server mapping: {server_info}")
            logger.info(f"Available tool mappings: {list(tool_server_mapping.keys())}")

            # Yield tool execution info
            yield {
                "type": "tool_call",
                "message": f"{get_translation('tool_execution', language, tool_name=tool_name)} on {server_name}",
                "tool_name": tool_name,
                "tool_args": tool_args,
                "server_name": server_name,
                "done": False
            }

            # Execute the tool by connecting to the specific server
            logger.info(f"Executing tool: {tool_name} on server: {server_name}")
            result = await self._execute_tool_on_server(tool_name, tool_args, server_info, tenant_id)
            tools_used.append(tool_name)

            # Process tool result content
            tool_result_content = ""
            if result.get("success"):
                content = result.get("content", [])
                if isinstance(content, list):
                    # Handle MCP content format
                    text_parts = []
                    for item in content:
                        if isinstance(item, dict):
                            if item.get("type") == "text":
                                text_parts.append(item.get("text", ""))
                            else:
                                text_parts.append(str(item))
                        else:
                            text_parts.append(str(item))
                    tool_result_content = "\n".join(text_parts)
                else:
                    tool_result_content = str(content)
                logger.info(f"Tool {tool_name} executed successfully on {server_name}")
            else:
                error_msg = result.get('error', 'Unknown error')
                tool_result_content = f"Error executing tool {tool_name} on {server_name}: {error_msg}"
                logger.error(f"Tool execution failed: {tool_result_content}")

                # Yield error information for frontend display
                yield {
                    "type": "tool_error",
                    "message": get_translation("tool_error", language, tool_name=tool_name),
                    "tool_name": tool_name,
                    "error": error_msg,
                    "server_name": server_name,
                    "tool_args": tool_args,
                    "done": False
                }

            tool_results.append(tool_result_content)

            # Add tool result to messages in the correct format for OpenAI
            messages.append({
                "role": "tool",
                "tool_call_id": call_id,
                "content": tool_result_content
            })

        # Continue conversation with tool results
        try:
            azure_client = AsyncAzureOpenAI(
                api_key=os.getenv("DEEPSEEK_API_KEY"),
                api_version=os.getenv("AZURE_OPENAI_API_VERSION", "2024-02-01"),
                azure_endpoint=os.getenv("AZURE_OPENAI_ENDPOINT"),
                azure_deployment=os.getenv("AZURE_VISION_MODEL")
            )

            # Get final response from OpenAI
            final_response = await azure_client.chat.completions.create(
                model=os.getenv("AZURE_VISION_MODEL", "gpt-4o"),
                messages=messages,
                temperature=0.7,
                stream=stream
            )

            if stream:
                content_buffer = ""
                async for chunk in final_response:
                    if chunk.choices and chunk.choices[0].delta.content:
                        content = chunk.choices[0].delta.content
                        content_buffer += content
                        yield {
                            "type": "content",
                            "content": content,
                            "done": False
                        }

                # Final response
                yield {
                    "type": "final",
                    "answer": content_buffer,
                    "tools_used": tools_used,
                    "done": True
                }
            else:
                # Non-streaming final response
                final_content = final_response.choices[0].message.content or ""
                yield {
                    "type": "final",
                    "answer": final_content,
                    "tools_used": tools_used,
                    "done": True
                }

        except Exception as e:
            logger.error(f"Error getting final response: {e}")
            yield {
                "type": "error",
                "error": f"Failed to get final response: {str(e)}",
                "answer": "",
                "tools_used": tools_used,
                "done": True
            }

    async def _execute_tool_on_server(
        self,
        tool_name: str,
        tool_args: Dict[str, Any],
        server_info: Dict[str, Any],
        tenant_id: str
    ) -> Dict[str, Any]:
        """Execute a tool on a specific MCP server"""
        try:
            # Get server configuration from database
            server_id = server_info.get("server_id")
            server_name = server_info.get("server_name", "Unknown")

            if not server_id:
                logger.warning(f"No server ID provided for tool {tool_name} on server {server_name}, attempting to find by name")

                # Try to find the server by name if ID is missing
                servers = await self.get_mcp_servers_for_tenant(tenant_id)
                matching_servers = [s for s in servers if s.get("name") == server_name]

                if matching_servers:
                    server_id = matching_servers[0].get("id")
                    logger.info(f"Found server ID {server_id} for server {server_name}")
                else:
                    return {
                        "success": False,
                        "error": f"No server ID provided for tool execution and could not find server by name: {server_name}"
                    }

            # Get full server config from database
            server_config = await self._get_server_config_by_id(server_id, tenant_id)
            if not server_config:
                return {
                    "success": False,
                    "error": f"Server configuration not found for ID: {server_id}"
                }

            # Create a temporary agent for this tool execution
            temp_agent = MCPAgent(db_client=self.db_client)

            try:
                # Connect to the server
                success = await temp_agent.connect_to_server(server_config)
                if not success:
                    return {
                        "success": False,
                        "error": f"Failed to connect to server: {server_config.get('name', 'Unknown')}"
                    }

                # Execute the tool
                result = await temp_agent.execute_tool(tool_name, tool_args)
                return result

            finally:
                # Always clean up the temporary agent
                await temp_agent.disconnect()

        except Exception as e:
            logger.error(f"Error executing tool {tool_name} on server: {e}")
            return {
                "success": False,
                "error": f"Tool execution failed: {str(e)}"
            }

    async def _get_server_config_by_id(self, server_id: str, tenant_id: str) -> Optional[Dict[str, Any]]:
        """Get MCP server configuration by ID"""
        try:
            server = await self.db_client.MCPServer.find_one({
                "_id": ObjectId(server_id),
                "tenantId": ObjectId(tenant_id)
            })

            logger.info(f"Database query result: {server is not None}")

            if not server:
                return None

            config = {
                "id": str(server["_id"]),
                "name": server["name"],
                "description": server.get("description", ""),
                "server_type": server.get("serverType", "STDIO").lower(),
                "status": server["status"]
            }

            # Add server-type specific fields
            if config["server_type"] == "stdio":
                config.update({
                    "command": server["command"],
                    "args": server.get("args", []),
                    "env": server.get("env", {})
                })
            elif config["server_type"] == "http":
                config.update({
                    "url": server["url"],
                    "headers": server.get("headers", {})
                })

            return config

        except Exception as e:
            logger.error(f"Error fetching server config for ID {server_id}: {e}")
            return None

# Global service instance
mcp_service = MCPService()
