/**
 * CopilotKit Chat Service
 *
 * Service for interacting with the new high-performance CopilotKit agentic RAG system.
 * Provides both streaming and non-streaming chat capabilities with intelligent tool selection.
 */

import { addAuthHeaders } from "@/lib/api/auth-token";
import { ImageAttachment } from "@/components/wrapper-screens/chat/types";

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || "http://localhost:8000";
const API_VERSION = "/api/v1";

export interface CopilotKitChatQuery {
  question: string;
  stream?: boolean;
  previous_message?: string;
  search_mode?: "internal" | "web" | "hybrid" | "deep_research" | "mcp" | "";
  include_web_results?: boolean;
  selected_mcp_servers?: string[]; // Selected MCP server IDs
  max_tokens?: number;
  temperature?: number;
  images?: string[]; // Base64 encoded images
  image_context?: string; // Image context from CopilotKit
  audio_context?: string; // Audio transcription context
  video_context?: string; // Video analysis context (NEW)
  audio_files?: string[]; // Audio file URLs for transcription
  deep_answer?: boolean; // Use DeepSeek R1 for expanded research-grade answers
  language?: string; // User's preferred language for AI responses
}

export interface CopilotKitChatResponse {
  answer: string;
  title: string;
  sources: Array<{
    type: string;
    title: string;
    content: string;
    metadata: Record<string, any>;
  }>;
  tools_used: string[];
  elapsed_time: number;
  thinking?: string; // For DeepSeek R1 compatibility
  status: number;
}

export interface CopilotKitStreamChunk {
  status?: string;
  message?: string;
  tools_used?: string[];
  answer?: string;
  content?: string;
  answer_chunk?: string; // Individual content chunks for real-time streaming
  partial_answer?: string; // Accumulated partial response
  title?: string;
  sources?: Array<any>;
  elapsed_time?: number;
  thinking?: string;
  done?: boolean;
  error?: string;
  // Deep Research Agent fields
  research_data?: {
    phase?: string;
    message?: string;
    progress?: number;
    iteration?: number;
    confidence?: number;
    gaps_identified?: string[];
    research_plan?: {
      main_topic?: string;
      subtopics?: string[];
      research_questions?: string[];
    };
    iterations?: Array<{
      iteration: number;
      phase: string;
      confidence: number;
      gaps_identified: string[];
      findings_preview: string;
    }>;
  };
  research_summary?: {
    iterations_conducted?: number;
    final_confidence?: number;
    quality_score?: number;
    subtopics_covered?: number;
    sources_consulted?: number;
  };
  iterations?: Array<{
    iteration: number;
    phase: string;
    confidence: number;
    gaps_identified: string[];
    findings_preview: string;
  }>;
  progress?: number; // Overall progress for deep research
}

export interface CopilotKitToolsInfo {
  tools: Array<{
    name: string;
    description: string;
    capabilities: string[];
  }>;
  search_modes: string[];
  features: string[];
}

export interface CopilotKitBenchmarkResult {
  performance_metrics: {
    total_time_seconds: number;
    tools_used: string[];
    sources_found: number;
    answer_length: number;
    status: number;
  };
  result: CopilotKitChatResponse;
  benchmark_timestamp: string;
}

export const copilotKitChatService = {
  /**
   * Send a chat query to the CopilotKit agentic RAG system
   */
  async chat(
    query: CopilotKitChatQuery,
    userId: string,
    tenantId: string,
    userName: string
  ): Promise<CopilotKitChatResponse> {
    try {
      const headers = await addAuthHeaders({
        "Content-Type": "application/json",
      });

      const response = await fetch(
        `${API_BASE_URL}${API_VERSION}/copilotkit-chat/chat?current_user=${userId}&tenant_id=${tenantId}&user_name=${encodeURIComponent(
          userName
        )}`,
        {
          method: "POST",
          headers,
          body: JSON.stringify(query),
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error("Error in CopilotKit chat:", error);
      throw error;
    }
  },
  async imageAnalysis(images: File[]) {
    try {
      const formData = new FormData();
      images.forEach((image) => {
        formData.append("images", image, image.name);
      });
      const headers = await addAuthHeaders({});

      const response = await fetch(
        `${API_BASE_URL}${API_VERSION}/copilotkit-chat/image-analysis`,
        {
          method: "POST",
          headers,
          body: formData,
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error("Error in CopilotKit chat:", error);
      throw error;
    }
  },

  async audioAnalysis(audioFiles: ImageAttachment[]) {
    try {
      // Extract blob URLs and metadata from uploaded files
      const audioFileInfos = audioFiles.map((file) => ({
        url: file.url,
        name: file.name,
        type: file.type,
        size: file.size,
      }));

      const headers = await addAuthHeaders({
        "Content-Type": "application/json",
      });

      const response = await fetch(
        `${API_BASE_URL}${API_VERSION}/copilotkit-chat/audio-analysis`,
        {
          method: "POST",
          headers,
          body: JSON.stringify({
            audio_files: audioFileInfos,
          }),
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error("Error in CopilotKit audio analysis:", error);
      throw error;
    }
  },

  async getAudioAnalysisStatus(jobId: string) {
    try {
      const headers = await addAuthHeaders({
        "Content-Type": "application/json",
      });

      const response = await fetch(
        `${API_BASE_URL}${API_VERSION}/copilotkit-chat/audio-analysis/${jobId}/status`,
        {
          method: "GET",
          headers,
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error("Error in getAudioAnalysisStatus:", error);
      throw error;
    }
  },

  startAudioProcessingStatusLogging(
    jobId: string,
    _fileId: string,
    userId: string,
    tenantId: string
  ) {
    let checkCount = 0;
    const maxChecks = 20; // Monitor for 20 minutes

    const checkStatus = async () => {
      checkCount++;

      try {
        const status = await this.getAudioAnalysisStatus(jobId);

        console.log(
          `🎵 Audio Processing Status Check ${checkCount}/${maxChecks}:`,
          {
            jobId,
            status: status.status,
            filesProcessed: status.files_processed,
            userId,
            tenantId,
          }
        );

        if (status.status === "completed") {
          console.log("✅ Audio processing completed successfully:", status);
          return; // Stop polling
        } else if (status.status === "failed") {
          console.error("❌ Audio processing failed:", status.error_message);
          return; // Stop polling
        }

        // Continue polling if still processing and haven't reached max checks
        if (checkCount < maxChecks && status.status === "processing") {
          setTimeout(checkStatus, 60000); // Check every minute (consistent with hook polling)
        } else if (checkCount >= maxChecks) {
          console.warn(
            `⏰ Audio processing monitoring stopped after ${maxChecks} checks for job ${jobId}`
          );
        }
      } catch (error) {
        console.error(
          `❌ Error checking audio processing status (attempt ${checkCount}):`,
          error
        );

        // Continue polling on error unless we've reached max attempts
        if (checkCount < maxChecks) {
          setTimeout(checkStatus, 60000); // Consistent 60-second polling
        }
      }
    };

    // Start the first check after 30 seconds
    setTimeout(checkStatus, 30000);
  },

  async videoAnalysis(videoFiles: ImageAttachment[]) {
    try {
      // Extract blob URLs and metadata from uploaded files
      const videoFileInfos = videoFiles.map((file) => ({
        url: file.url,
        name: file.name,
        type: file.type,
        size: file.size,
      }));

      const headers = await addAuthHeaders({
        "Content-Type": "application/json",
      });

      const response = await fetch(
        `${API_BASE_URL}${API_VERSION}/copilotkit-chat/video-analysis`,
        {
          method: "POST",
          headers,
          body: JSON.stringify({
            video_files: videoFileInfos,
          }),
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();

      // For async processing, return the job info
      if (result.job_id) {
        return {
          job_id: result.job_id,
          status: result.status,
          message: result.message,
          files_queued: result.files_queued,
          estimated_time: result.estimated_time,
          async: true,
        };
      }

      // Fallback for any synchronous responses
      return result;
    } catch (error) {
      console.error("Error in CopilotKit video analysis:", error);
      throw error;
    }
  },

  async getVideoAnalysisStatus(jobId: string) {
    try {
      const headers = await addAuthHeaders({
        "Content-Type": "application/json",
      });

      const response = await fetch(
        `${API_BASE_URL}${API_VERSION}/copilotkit-chat/video-analysis/${jobId}/status`,
        {
          method: "GET",
          headers,
        }
      );
      console.log(
        `Status response from copilot-chat.ts: ${JSON.stringify(response)}`
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error("Error getting video analysis status:", error);
      throw error;
    }
  },

  /**
   * Stream chat responses from the CopilotKit agentic RAG system
   */
  async *streamChat(
    query: CopilotKitChatQuery,
    userId: string,
    tenantId: string,
    userName: string
  ): AsyncGenerator<CopilotKitStreamChunk, void, unknown> {
    try {
      const headers = await addAuthHeaders({
        "Content-Type": "application/json",
      });

      const url = `${API_BASE_URL}${API_VERSION}/copilotkit-chat/chat?current_user=${userId}&tenant_id=${tenantId}&user_name=${encodeURIComponent(
        userName
      )}`;

      const response = await fetch(url, {
        method: "POST",
        headers,
        body: JSON.stringify({ ...query, stream: true }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(
          `HTTP error! status: ${response.status}, body: ${errorText}`
        );
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error("No response body reader available");
      }

      const decoder = new TextDecoder();
      let buffer = "";

      try {
        while (true) {
          const { done, value } = await reader.read();

          if (done) break;

          buffer += decoder.decode(value, { stream: true });
          const lines = buffer.split("\n");
          buffer = lines.pop() || "";

          for (const line of lines) {
            const trimmedLine = line.trim();

            if (trimmedLine.startsWith("data: ")) {
              try {
                const jsonData = trimmedLine.slice(6);
                if (jsonData) {
                  const data = JSON.parse(jsonData);
                  yield data;

                  if (data.done) {
                    return;
                  }
                }
              } catch (e) {
                console.warn("Failed to parse streaming data:", trimmedLine, e);
              }
            } else if (trimmedLine === "") {
              // Empty line, continue
              continue;
            } else if (
              trimmedLine.startsWith("event:") ||
              trimmedLine.startsWith("id:")
            ) {
              // SSE metadata, ignore
              continue;
            } else {
              // Might be plain text data without "data:" prefix
              try {
                const data = JSON.parse(trimmedLine);
                yield data;
                if (data.done) {
                  return;
                }
              } catch (e) {
                // Not JSON, might be plain text chunk
                if (trimmedLine.length > 0) {
                  yield {
                    answer_chunk: trimmedLine,
                    done: false,
                    status: "streaming",
                  };
                }
              }
            }
          }
        }
      } finally {
        reader.releaseLock();
      }
    } catch (error) {
      console.error("Error in CopilotKit streaming chat:", error);
      yield {
        error: `Streaming failed: ${
          error instanceof Error ? error.message : String(error)
        }`,
        done: true,
      };
    }
  },

  /**
   * Get information about available tools and capabilities
   */
  async getToolsInfo(): Promise<CopilotKitToolsInfo> {
    try {
      const headers = await addAuthHeaders({
        "Content-Type": "application/json",
      });

      const response = await fetch(
        `${API_BASE_URL}${API_VERSION}/copilotkit-chat/tools`,
        {
          method: "GET",
          headers,
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error("Error getting CopilotKit tools info:", error);
      throw error;
    }
  },

  /**
   * Run a performance benchmark
   */
  async benchmark(
    query: CopilotKitChatQuery,
    userId: string,
    tenantId: string,
    userName: string
  ): Promise<CopilotKitBenchmarkResult> {
    try {
      const headers = await addAuthHeaders({
        "Content-Type": "application/json",
      });

      const response = await fetch(
        `${API_BASE_URL}${API_VERSION}/copilotkit-chat/benchmark?current_user=${userId}&tenant_id=${tenantId}&user_name=${encodeURIComponent(
          userName
        )}`,
        {
          method: "POST",
          headers,
          body: JSON.stringify(query),
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error("Error in CopilotKit benchmark:", error);
      throw error;
    }
  },

  /**
   * Determine optimal search mode based on query content
   */
  getOptimalSearchMode(
    query: string
  ): "internal" | "web" | "hybrid" | "deep_research" {
    const webIndicators = [
      "current",
      "recent",
      "latest",
      "news",
      "today",
      "2024",
      "2025",
      "trending",
      "now",
      "update",
      "new",
      "breaking",
    ];

    const hasWebIndicators = webIndicators.some((indicator) =>
      query.toLowerCase().includes(indicator)
    );

    return hasWebIndicators ? "hybrid" : "internal";
  },

  /**
   * Determine if web search should be used based on query content
   */
  shouldUseWebSearch(query: string): boolean {
    const webIndicators = [
      "current",
      "recent",
      "latest",
      "news",
      "today",
      "2024",
      "2025",
      "trending",
      "now",
      "update",
      "new",
      "breaking",
      "weather",
      "stock",
    ];

    return webIndicators.some((indicator) =>
      query.toLowerCase().includes(indicator)
    );
  },

  /**
   * Request a deep answer using DeepSeek R1 for expanded research-grade responses
   */
  async requestDeepAnswer(
    originalQuery: CopilotKitChatQuery,
    userId: string,
    tenantId: string,
    userName: string
  ): Promise<CopilotKitChatResponse> {
    try {
      const deepQuery: CopilotKitChatQuery = {
        ...originalQuery,
        deep_answer: true,
        stream: false, // Deep answers are non-streaming for now
      };

      return await this.chat(deepQuery, userId, tenantId, userName);
    } catch (error) {
      console.error("Error in CopilotKit deep answer:", error);
      throw error;
    }
  },

  /**
   * Stream a deep answer using DeepSeek R1 for expanded research-grade responses
   */
  async *streamDeepAnswer(
    originalQuery: CopilotKitChatQuery,
    userId: string,
    tenantId: string,
    userName: string
  ): AsyncGenerator<CopilotKitStreamChunk, void, unknown> {
    try {
      const deepQuery: CopilotKitChatQuery = {
        ...originalQuery,
        deep_answer: true,
        stream: true,
      };

      yield* this.streamChat(deepQuery, userId, tenantId, userName);
    } catch (error) {
      console.error("Error in CopilotKit deep answer streaming:", error);
      throw error;
    }
  },

  /**
   * Check health status of the CopilotKit service
   */
  async healthCheck(): Promise<{
    status: string;
    service: string;
    timestamp: string;
  }> {
    try {
      const response = await fetch(
        `${API_BASE_URL}${API_VERSION}/copilotkit-chat/health`,
        {
          method: "GET",
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error("Error in CopilotKit health check:", error);
      throw error;
    }
  },
};
