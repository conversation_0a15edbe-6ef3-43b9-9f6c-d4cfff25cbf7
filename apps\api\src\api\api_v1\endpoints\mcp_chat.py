"""
MCP Chat API Endpoint for Swiss Knowledge Hub

This endpoint provides MCP (Model Context Protocol) integration with the CopilotKit chat system,
allowing users to interact with external tools and services through MCP servers.
"""

from fastapi import APIRouter, Depends, HTTPException, Query, status
from typing import Dict, Any, Optional, List
from pydantic import BaseModel
from src.services.mcp_service import mcp_service
from src.api.deps import get_db
from starlette.responses import StreamingResponse
import json
import logging
from bson import ObjectId

logger = logging.getLogger(__name__)

router = APIRouter()

class MCPChatQuery(BaseModel):
    """Request model for MCP chat queries."""
    question: str
    stream: bool = True
    server_ids: List[str]  # List of MCP server IDs to use
    previous_message: Optional[str] = None
    max_tokens: Optional[int] = None
    temperature: Optional[float] = None
    language: str = "en"  # User's preferred language for AI responses

class MCPServerSelection(BaseModel):
    """Request model for MCP server selection."""
    server_id: str

@router.post("/chat", response_model=Dict[str, Any], status_code=200)
async def mcp_chat(
    query: MCPChatQuery,
    current_user: str=Query(..., description="The user ID"),
    user_name: str=Query(..., description="The user name"),
    tenant_id: str=Query(..., description="The tenant ID"),
    db = Depends(get_db)
):
    """
    Chat with MCP servers through the MCP agent.
    Provides access to external tools and services via Model Context Protocol.
    """
    # Set the database client
    mcp_service.db_client = db
    
    try:
        logger.info(f"MCP chat request from user {current_user}: {query.question}")
        logger.info(f"Requested MCP servers: {query.server_ids}")

        # Get configurations for all selected MCP servers
        server_configs = []
        for server_id in query.server_ids:
            server_config = await _get_server_config(server_id, tenant_id, db)
            if server_config:
                server_configs.append(server_config)
            else:
                logger.warning(f"MCP server with ID {server_id} not found or not accessible")

        if not server_configs:
            raise HTTPException(
                status_code=404,
                detail=f"None of the requested MCP servers were found or accessible"
            )
        
        # Execute query with MCP agents
        if query.stream:
            return StreamingResponse(
                _stream_mcp_response(
                    query.question,
                    current_user,
                    tenant_id,
                    user_name,
                    server_configs,
                    query.previous_message,
                    query.language
                ),
                media_type="text/plain",
                headers={
                    "Cache-Control": "no-cache",
                    "Connection": "keep-alive",
                    "X-Accel-Buffering": "no"  # Disable nginx buffering
                }
            )
        else:
            # For non-streaming, get the first (and only) result from the generator
            async for result in mcp_service.query_with_mcp(
                tenant_id=tenant_id,
                question=query.question,
                tools=server_configs,
                stream=False,
                previous_message=query.previous_message,
                language=query.language
            ):
                return result
                
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in MCP chat: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"MCP chat failed: {str(e)}"
        )
    
@router.get("/{server_id}/tools", response_model=List[Dict[str, Any]], status_code=200)
async def get_server_tools(
    server_id: str,
    tenant_id: str=Query(..., description="The tenant ID"),
    db = Depends(get_db)
):
    """
    Get available tools for a specific MCP server.
    This will temporarily connect to the server to fetch tool information.
    """
    try:
        mcp_service.db_client = db
        
        # Get server configuration
        server_config = await _get_server_config(server_id, tenant_id, db)
        if not server_config:
            raise HTTPException(
                status_code=404,
                detail=f"MCP server with ID {server_id} not found or not accessible"
            )
        
        # Connect to server and get tools
        success = await mcp_service.connect_agent_to_server(tenant_id, server_config)
        if not success:
            raise HTTPException(
                status_code=500,
                detail=f"Failed to connect to MCP server: {server_config['name']}"
            )
        
        tools = await mcp_service.get_available_tools(tenant_id)
        return tools
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching server tools: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to fetch server tools: {str(e)}"
        )

@router.post("/{server_id}/test-connection", response_model=Dict[str, Any], status_code=200)
async def test_mcp_connection(
    server_id: str,
    tenant_id: str=Query(..., description="The tenant ID"),
    db = Depends(get_db)
):
    """
    Test connection to an MCP server.
    """
    try:
        mcp_service.db_client = db
        
        # Get server configuration
        server_config = await _get_server_config(server_id, tenant_id, db)
        if not server_config:
            raise HTTPException(
                status_code=404,
                detail=f"MCP server with ID {server_id} not found or not accessible"
            )
        
        # Test connection
        success = await mcp_service.connect_agent_to_server(tenant_id, server_config)
        
        if success:
            tools = await mcp_service.get_available_tools(tenant_id)
            return {
                "success": True,
                "message": f"Successfully connected to {server_config['name']}",
                "server_info": server_config,
                "available_tools": tools
            }
        else:
            return {
                "success": False,
                "message": f"Failed to connect to {server_config['name']}",
                "server_info": server_config,
                "available_tools": []
            }
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error testing MCP connection: {e}")
        return {
            "success": False,
            "message": f"Connection test failed: {str(e)}",
            "server_info": {},
            "available_tools": []
        }

async def _get_server_config(server_id: str, tenant_id: str, db) -> Optional[Dict[str, Any]]:
    """Get MCP server configuration from database"""
    try:
        server = await db.MCPServer.find_one({
            "_id": ObjectId(server_id),
            "tenantId": ObjectId(tenant_id)
        })
        
        if not server:
            return None
        
        config = {
            "id": str(server["_id"]),
            "name": server["name"],
            "description": server.get("description", ""),
            "server_type": server.get("serverType", "STDIO").lower(),
            "status": server["status"]
        }

        # Add server-type specific fields
        if config["server_type"] == "stdio":
            config.update({
                "command": server["command"],
                "args": server.get("args", []),
                "env": server.get("env", {})
            })
        elif config["server_type"] == "http":
            config.update({
                "url": server["url"],
                "headers": server.get("headers", {})
            })

        return config
        
    except Exception as e:
        logger.error(f"Error fetching server config: {e}")
        return None

async def _stream_mcp_response(
    question: str,
    user_id: str,
    tenant_id: str,
    user_name: str,
    server_configs: List[Dict[str, Any]],
    previous_message: Optional[str] = None,
    language: str = "en"
):
    """Stream the MCP agent response from multiple servers."""
    try:
        # Get the streaming generator from the service
        stream_generator = mcp_service.query_with_mcp(
            tenant_id=tenant_id,
            question=question,
            tools=server_configs,
            stream=True,
            previous_message=previous_message,
            language=language
        )
        
        async for chunk in stream_generator:
            # Convert chunk to JSON and yield
            chunk_json = json.dumps(chunk, default=str)
            yield f"data: {chunk_json}\n\n"
            
            # If this is the final chunk, break
            if chunk.get("done"):
                break
                
    except Exception as e:
        logger.error(f"Error in MCP streaming response: {e}")
        error_chunk = {
            "error": f"Streaming failed: {str(e)}",
            "answer": "",
            "tools_used": [],
            "done": True
        }
        error_json = json.dumps(error_chunk, default=str)
        yield f"data: {error_json}\n\n"
