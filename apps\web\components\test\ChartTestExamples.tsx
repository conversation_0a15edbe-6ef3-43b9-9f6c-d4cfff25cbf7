"use client";

import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, Card<PERSON>itle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Tabs, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { MessageContent } from "@/components/wrapper-screens/chat/components/MessageContent";
import { Copy } from "lucide-react";
import { useTranslatedToast } from "@/hooks/use-translated-toast";

/**
 * Test examples for chart and diagram rendering
 */
export const ChartTestExamples: React.FC = () => {
  const [selectedExample, setSelectedExample] = useState("line-chart");
  const toast = useTranslatedToast();

  const examples = {
    "line-chart": {
      title: "Line Chart - Sales Trend",
      content: `Here's the sales trend for the last 6 months:

\`\`\`chart:line
{
  "data": [
    {"month": "Jan", "sales": 12000, "profit": 2400},
    {"month": "Feb", "sales": 15000, "profit": 3000},
    {"month": "Mar", "sales": 18000, "profit": 3600},
    {"month": "Apr", "sales": 22000, "profit": 4400},
    {"month": "May", "sales": 25000, "profit": 5000},
    {"month": "Jun", "sales": 28000, "profit": 5600}
  ],
  "config": {
    "title": "Monthly Sales and Profit",
    "xKey": "month",
    "showLegend": true,
    "height": 350
  }
}
\`\`\`

The data shows a consistent upward trend in both sales and profit margins.`,
    },

    "bar-chart": {
      title: "Bar Chart - Department Performance",
      content: `Department performance comparison:

\`\`\`chart:bar
department,revenue,expenses,employees
Sales,450000,120000,25
Marketing,280000,95000,18
Engineering,520000,180000,35
Support,180000,65000,12
HR,95000,45000,8
\`\`\`

Engineering leads in revenue generation, while HR maintains the lowest operational costs.`,
    },

    "pie-chart": {
      title: "Pie Chart - Market Share",
      content: `Current market share distribution:

\`\`\`chart:pie
{
  "data": [
    {"company": "Our Company", "share": 35},
    {"company": "Competitor A", "share": 28},
    {"company": "Competitor B", "share": 18},
    {"company": "Competitor C", "share": 12},
    {"company": "Others", "share": 7}
  ],
  "config": {
    "title": "Market Share Analysis",
    "height": 400,
    "showLegend": true
  }
}
\`\`\`

We maintain a strong market position with 35% share.`,
    },

    "area-chart": {
      title: "Area Chart - Website Traffic",
      content: `Website traffic analysis over the past week:

\`\`\`chart:area
{
  "data": [
    {"day": "Mon", "visitors": 1200, "pageviews": 3600},
    {"day": "Tue", "visitors": 1450, "pageviews": 4200},
    {"day": "Wed", "visitors": 1680, "pageviews": 4800},
    {"day": "Thu", "visitors": 1520, "pageviews": 4400},
    {"day": "Fri", "visitors": 1890, "pageviews": 5200},
    {"day": "Sat", "visitors": 980, "pageviews": 2800},
    {"day": "Sun", "visitors": 850, "pageviews": 2400}
  ],
  "config": {
    "title": "Daily Website Traffic",
    "description": "Unique visitors and total page views",
    "xKey": "day",
    "showLegend": true
  }
}
\`\`\`

Peak traffic occurs on Friday, with weekend showing expected decline.`,
    },

    "mermaid-flowchart": {
      title: "Mermaid Flowchart - Process Flow",
      content: `Here's the customer onboarding process:

\`\`\`mermaid
graph TD
    A[Customer Registration] --> B{Email Verification}
    B -->|Verified| C[Profile Setup]
    B -->|Not Verified| D[Send Reminder]
    D --> B
    C --> E[Document Upload]
    E --> F{Document Review}
    F -->|Approved| G[Account Activation]
    F -->|Rejected| H[Request Resubmission]
    H --> E
    G --> I[Welcome Email]
    I --> J[Onboarding Complete]
\`\`\`

The process ensures proper verification before account activation.`,
    },

    "mermaid-architecture": {
      title: "Mermaid Architecture - System Design",
      content: `Here's a distributed system architecture:

\`\`\`mermaid
graph TD
    A[User Devices] -->|HTTP Requests| B[Load Balancer]
    B --> C[Web Servers]
    C --> D[Application Layer]
    D --> E[Database Cluster]
    D --> F[Cache Layer]
    F --> G[Redis Cache]
    E --> H[Primary Database]
    E --> I[Replica Database]
    D --> J[File Storage]
    J --> K[Cloud Storage]
\`\`\`

This architecture provides scalability and high availability.`,
    },

    "mermaid-sequence": {
      title: "Mermaid Sequence Diagram - API Flow",
      content: `API authentication sequence:

\`\`\`mermaid
sequenceDiagram
    participant Client
    participant API Gateway
    participant Auth Service
    participant Database
    
    Client->>API Gateway: Request with credentials
    API Gateway->>Auth Service: Validate credentials
    Auth Service->>Database: Check user data
    Database-->>Auth Service: User information
    Auth Service-->>API Gateway: JWT Token
    API Gateway-->>Client: Authenticated response
    
    Note over Client,Database: Subsequent requests use JWT
    
    Client->>API Gateway: API request + JWT
    API Gateway->>Auth Service: Validate JWT
    Auth Service-->>API Gateway: Token valid
    API Gateway-->>Client: API response
\`\`\`

This ensures secure API access through JWT tokens.`,
    },

    "mixed-content": {
      title: "Mixed Content - Charts with Text",
      content: `# Quarterly Business Review

Our Q3 performance shows significant growth across all metrics.

## Revenue Analysis

\`\`\`chart:line
quarter,revenue,target
Q1,850000,800000
Q2,920000,850000
Q3,1050000,900000
\`\`\`

We exceeded our Q3 target by 16.7%, demonstrating strong market performance.

## Department Breakdown

\`\`\`chart:pie
{
  "data": [
    {"department": "Sales", "contribution": 45},
    {"department": "Services", "contribution": 30},
    {"department": "Products", "contribution": 25}
  ]
}
\`\`\`

## Process Optimization

We've streamlined our workflow:

\`\`\`mermaid
graph LR
    A[Lead Generation] --> B[Qualification]
    B --> C[Proposal]
    C --> D[Negotiation]
    D --> E[Closing]
    E --> F[Delivery]
\`\`\`

This optimization reduced our sales cycle by 23%.

## Key Takeaways

- Revenue growth exceeded expectations
- Sales department leads contribution
- Process improvements show measurable results
- Q4 outlook remains positive`,
    },
  };

  const copyToClipboard = async (content: string) => {
    try {
      await navigator.clipboard.writeText(content);
      toast.success("Example copied to clipboard");
    } catch (err) {
      toast.error("Failed to copy example");
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Chart & Diagram Test Examples</CardTitle>
          <p className="text-sm text-muted-foreground">
            Test various chart types and diagram rendering capabilities
          </p>
        </CardHeader>
        <CardContent>
          <Tabs value={selectedExample} onValueChange={setSelectedExample}>
            <TabsList className="grid w-full grid-cols-4 lg:grid-cols-8">
              <TabsTrigger value="line-chart">Line</TabsTrigger>
              <TabsTrigger value="bar-chart">Bar</TabsTrigger>
              <TabsTrigger value="pie-chart">Pie</TabsTrigger>
              <TabsTrigger value="area-chart">Area</TabsTrigger>
              <TabsTrigger value="mermaid-flowchart">Flow</TabsTrigger>
              <TabsTrigger value="mermaid-architecture">Arch</TabsTrigger>
              <TabsTrigger value="mermaid-sequence">Seq</TabsTrigger>
              <TabsTrigger value="mixed-content">Mixed</TabsTrigger>
            </TabsList>

            {Object.entries(examples).map(([key, example]) => (
              <TabsContent key={key} value={key} className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-medium">{example.title}</h3>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => copyToClipboard(example.content)}
                  >
                    <Copy className="h-4 w-4 mr-2" />
                    Copy Example
                  </Button>
                </div>

                {/* Rendered Output */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-base">Rendered Output</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <MessageContent
                      content={example.content}
                      sources={[]}
                      onCitationClick={() => {}}
                    />
                  </CardContent>
                </Card>

                {/* Source Code */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-base">Source Code</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <pre className="bg-muted p-4 rounded-lg overflow-auto text-sm">
                      <code>{example.content}</code>
                    </pre>
                  </CardContent>
                </Card>
              </TabsContent>
            ))}
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
};
