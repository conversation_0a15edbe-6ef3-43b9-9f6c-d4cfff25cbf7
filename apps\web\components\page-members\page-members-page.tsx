"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { <PERSON>L<PERSON>t, Users, Settings, Shield } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { PageMembersManager } from "@/components/page-membership/page-members-manager";
import { SharePointPageGuard } from "@/components/ui/sharepoint-page-guard";
import { useLanguage } from "@/lib/language-context";

interface PageMembersPageProps {
  tenantId: string;
  page: any;
  workspaceSlug: string;
  pageId: string;
  sharePointAccess: any;
  currentUserRole: "MEMBER" | "EDITOR" | "ADMIN";
}

export default function PageMembersPage({
  tenantId,
  page,
  workspaceSlug,
  pageId,
  sharePointAccess,
  currentUserRole,
}: PageMembersPageProps) {
  const router = useRouter();
  const { t } = useLanguage();
  const [membershipChanged, setMembershipChanged] = useState(false);

  const handleBackToPage = () => {
    router.push(`/workspace/${workspaceSlug}/page/${pageId}`);
  };

  const handleMembershipChange = () => {
    setMembershipChanged(!membershipChanged);
  };

  return (
    <div className="min-h-screen">
      <div className="px-4 py-6">
        {/* Header */}
        <div className="mb-6">
          <div className="flex items-center gap-4 mb-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleBackToPage}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              {t("common.back")}
            </Button>
            <div className="flex items-center gap-2">
              <Users className="h-5 w-5 text-blue-600" />
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                {t("page.memberManagement")}
              </h1>
            </div>
          </div>

          <div className="flex items-center gap-3 mb-2">
            <h2 className="text-lg font-medium text-gray-700 dark:text-gray-300">
              {page.name}
            </h2>
            <Badge variant="secondary" className="flex items-center gap-1">
              <Shield className="h-3 w-3" />
              {t(`role.${currentUserRole.toLowerCase()}`)}
            </Badge>
          </div>

          <p className="text-gray-600 dark:text-gray-400">
            {t("page.manageMembersDescription")}
          </p>
        </div>

        <Separator className="mb-6" />

        {/* SharePoint Access Guard */}
        {sharePointAccess && !sharePointAccess.hasAccess && (
          <div className="mb-6">
            <SharePointPageGuard
              sharePointAccess={sharePointAccess}
              pageId={pageId}
              pageName={page.name}
            >
              <div />
            </SharePointPageGuard>
          </div>
        )}

        {/* Page Members Management */}

        <PageMembersManager
          pageId={pageId}
          tenantId={tenantId}
          currentUserRole={currentUserRole}
          onMembershipChange={handleMembershipChange}
        />

        {/* Page Information */}
        {/* <Card className="mt-6">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              {t("common.pageInformation")}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  {t("common.pageName")}
                </label>
                <p className="text-gray-900 dark:text-white">{page.name}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  {t("common.workspace")}
                </label>
                <p className="text-gray-900 dark:text-white">{workspaceSlug}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  {t("common.createdAt")}
                </label>
                <p className="text-gray-900 dark:text-white">
                  {new Date(page.createdAt).toLocaleDateString()}
                </p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  {t("common.updatedAt")}
                </label>
                <p className="text-gray-900 dark:text-white">
                  {new Date(page.updatedAt).toLocaleDateString()}
                </p>
              </div>
            </div>
          </CardContent>
        </Card> */}
      </div>
    </div>
  );
}
