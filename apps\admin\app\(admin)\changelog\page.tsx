import { getServerSession } from "next-auth/next";
import { redirect } from "next/navigation";
import { authOptions } from "@/lib/next-auth";
import db from "@/lib/shared-db";
import { ChangelogTableClient } from "./changelog-table-client";

// TypeScript interfaces
interface Changelog {
  id: string;
  title: string;
  content: string;
  version?: string;
  type: "RELEASE" | "HOTFIX" | "MAINTENANCE" | "ANNOUNCEMENT";
  priority: "LOW" | "MEDIUM" | "HIGH" | "CRITICAL";
  isActive: boolean;
  targetTenants: string[];
  targetEnvironment?: string;
  publishedAt: Date;
  expiresAt?: Date;
  githubCommitSha?: string;
  deploymentId?: string;
  authorId?: string;
  createdAt: Date;
  updatedAt: Date;
}

interface ChangelogData {
  changelogs: Changelog[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  searchQuery: string;
}

// Server-side data fetching function
async function fetchChangelogData(
  searchQuery: string = ""
): Promise<ChangelogData> {
  try {
    

    // Build search filter - MongoDB doesn't support mode: "insensitive" in the same way
    // We'll use regex for case-insensitive search
    const searchFilter = searchQuery
      ? {
          OR: [
            { title: { contains: searchQuery } },
            { content: { contains: searchQuery } },
            { version: { contains: searchQuery } },
          ],
        }
      : {};

    // Get total count
    const total = await db.changelog.count({
      where: searchFilter,
    });

    // Fetch all changelogs without pagination
    const changelogs = await db.changelog.findMany({
      where: searchFilter,
      orderBy: { publishedAt: "desc" }, // Newest first
    });

    return {
      changelogs: changelogs.map((changelog) => ({
        ...changelog,
        publishedAt: changelog.publishedAt,
        expiresAt: changelog.expiresAt,
        createdAt: changelog.createdAt,
        updatedAt: changelog.updatedAt,
      })),
      pagination: {
        page: 1,
        limit: total,
        total,
        totalPages: 1,
      },
      searchQuery,
    };
  } catch (error) {
    console.error("Error fetching changelog data:", error);

    // Return fallback data
    return {
      changelogs: [],
      pagination: {
        page: 1,
        limit: 0,
        total: 0,
        totalPages: 1,
      },
      searchQuery: "",
    };
  }
}

// Main server component
export default async function ChangelogPage({
  searchParams,
}: {
  searchParams: { search?: string };
}) {
  const session = await getServerSession(authOptions);
  if (!session) {
    redirect("/auth/signin");
  }

  // Parse search params
  const searchQuery = searchParams.search || "";

  // Fetch changelog data server-side
  const changelogData = await fetchChangelogData(searchQuery);

  return <ChangelogTableClient data={changelogData} />;
}
