aiofiles==24.1.0 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
aiohappyeyeballs==2.6.1 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
aiohttp==3.10.11 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
aiosignal==1.3.2 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
annotated-types==0.7.0 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
anyio==4.9.0 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
asgiref==3.8.1 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
async-timeout==4.0.3 ; python_version >= "3.10" and python_version < "3.11"
attrs==25.3.0 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
azure-ai-documentintelligence==1.0.2 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
azure-core==1.33.0 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
azure-identity==1.21.0 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
backoff==2.2.1 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
bcrypt==4.3.0 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
beautifulsoup4==4.13.4 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
build==1.2.2.post1 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
cachetools==5.5.2 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
certifi==2025.1.31 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
cffi==1.17.1 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
chardet==5.2.0 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
charset-normalizer==3.4.1 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
chroma-hnswlib==0.7.6 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
chromadb==0.6.3 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
click==8.1.8 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
cobble==0.1.4 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
cohere==5.15.0 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
colorama==0.4.6 ; python_version >= "3.10" and python_version <= "3.11" and platform_system == "Windows" or python_version >= "3.12" and python_version < "3.13" and platform_system == "Windows" or python_version >= "3.10" and python_version <= "3.11" and sys_platform == "win32" or python_version >= "3.12" and python_version < "3.13" and sys_platform == "win32" or python_version >= "3.10" and python_version <= "3.11" and os_name == "nt" or python_version >= "3.12" and python_version < "3.13" and os_name == "nt"
coloredlogs==15.0.1 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
cryptography==42.0.8 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
dataclasses-json==0.6.7 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
defusedxml==0.7.1 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
deprecated==1.2.18 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
deprecation==2.1.0 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
diskcache==5.6.3 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
distro==1.9.0 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
dnspython==2.7.0 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
docstring-parser==0.16 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
durationpy==0.9 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
email-validator==2.2.0 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
emoji==2.14.1 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
environs==14.1.1 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
et-xmlfile==2.0.0 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
eval-type-backport==0.2.2 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
exceptiongroup==1.2.2 ; python_version >= "3.10" and python_version < "3.11"
fastapi-cors==0.0.6 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
fastapi==0.109.2 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
fastavro==1.10.0 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
filelock==3.18.0 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
filetype==1.2.0 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
flatbuffers==25.2.10 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
frozenlist==1.5.0 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
fsspec==2025.3.2 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
gitdb==4.0.12 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
gitpython==3.1.44 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
google-auth==2.39.0 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
googleapis-common-protos==1.70.0 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
greenlet==3.2.0 ; python_version >= "3.10" and python_version <= "3.11" and (platform_machine == "aarch64" or platform_machine == "ppc64le" or platform_machine == "x86_64" or platform_machine == "amd64" or platform_machine == "AMD64" or platform_machine == "win32" or platform_machine == "WIN32") or python_version >= "3.12" and python_version < "3.13" and (platform_machine == "aarch64" or platform_machine == "ppc64le" or platform_machine == "x86_64" or platform_machine == "amd64" or platform_machine == "AMD64" or platform_machine == "win32" or platform_machine == "WIN32")
grpcio==1.71.0 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
h11==0.14.0 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
html5lib==1.1 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
httpcore==1.0.8 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
httptools==0.6.4 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
httpx-sse==0.4.0 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
httpx==0.28.1 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
huggingface-hub==0.30.2 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
humanfriendly==10.0 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
idna==3.10 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
importlib-metadata==8.6.1 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
importlib-resources==6.5.2 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
iniconfig==2.1.0 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
isodate==0.7.2 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
jinja2==3.1.6 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
jiter==0.9.0 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
joblib==1.4.2 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
jsonpatch==1.33 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
jsonpointer==3.0.0 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
jsonschema-specifications==2025.4.1 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
jsonschema==4.24.0 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
kubernetes==32.0.1 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
lancedb==0.19.0 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
langchain-community==0.3.21 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
langchain-core==0.3.52 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
langchain-deepseek==0.1.3 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
langchain-openai==0.3.9 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
langchain-pinecone==0.2.3 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
langchain-tests==0.3.18 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
langchain-text-splitters==0.3.8 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
langchain==0.3.23 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
langdetect==1.0.9 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
langgraph-checkpoint==2.1.0 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
langgraph-sdk==0.1.70 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
langgraph==0.2.76 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
langsmith==0.3.31 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
lxml==5.3.2 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
magika==0.6.1 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
mammoth==1.9.0 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
markdown-it-py==3.0.0 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
markdownify==1.1.0 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
markitdown[all]==0.1.1 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
markupsafe==3.0.2 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
marshmallow==3.26.1 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
mcp==1.10.1 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
mdurl==0.1.2 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
mmh3==5.1.0 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
monotonic==1.6 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
motor==3.7.0 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
mpmath==1.3.0 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
msal-extensions==1.3.1 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
msal==1.32.3 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
multidict==6.4.3 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
mypy-extensions==1.0.0 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
nest-asyncio==1.6.0 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
nltk==3.9.1 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
nodeenv==1.9.1 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
numpy==1.26.4 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
oauthlib==3.2.2 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
olefile==0.47 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
onnxruntime==1.20.1 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
openai==1.75.0 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
openpyxl==3.1.5 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
opentelemetry-api==1.32.1 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
opentelemetry-exporter-otlp-proto-common==1.32.1 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
opentelemetry-exporter-otlp-proto-grpc==1.32.1 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
opentelemetry-instrumentation-asgi==0.53b1 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
opentelemetry-instrumentation-fastapi==0.53b1 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
opentelemetry-instrumentation==0.53b1 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
opentelemetry-proto==1.32.1 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
opentelemetry-sdk==1.32.1 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
opentelemetry-semantic-conventions==0.53b1 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
opentelemetry-util-http==0.53b1 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
orjson==3.10.16 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
ormsgpack==1.10.0 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
overrides==7.7.0 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
packaging==24.2 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
pandas==2.2.3 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
pdfminer-six==20250416 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
phi==0.6.7 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
phidata==2.7.10 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
pillow==11.2.1 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
pinecone-plugin-inference==3.1.0 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
pinecone-plugin-interface==0.0.7 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
pinecone==5.4.2 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
pluggy==1.5.0 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
posthog==3.25.0 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
prisma==0.15.0 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
propcache==0.3.1 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
protobuf==5.29.4 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
psutil==7.0.0 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
pyarrow==19.0.1 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
pyasn1-modules==0.4.2 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
pyasn1==0.6.1 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
pycparser==2.22 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
pydantic-core==2.33.1 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
pydantic-settings==2.8.1 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
pydantic==2.11.3 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
pydub==0.25.1 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
pygments==2.19.1 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
pyjwt==2.10.1 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
pylance==0.23.0 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
pymongo==4.12.0 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
pypdf==5.4.0 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
pypika==0.48.9 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
pyproject-hooks==1.2.0 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
pyreadline3==3.5.4 ; python_version >= "3.10" and python_version <= "3.11" and sys_platform == "win32" or python_version >= "3.12" and python_version < "3.13" and sys_platform == "win32"
pytest-asyncio==0.26.0 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
pytest-socket==0.7.0 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
pytest==8.3.5 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
python-dateutil==2.9.0.post0 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
python-docx==1.1.2 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
python-dotenv==1.1.0 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
python-iso639==2025.2.18 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
python-magic==0.4.27 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
python-multipart==0.0.20 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
python-oxmsg==0.0.2 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
python-pptx==1.0.2 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
pytz==2025.2 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
pyyaml==6.0.2 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
rapidfuzz==3.13.0 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
referencing==0.36.2 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
regex==2024.11.6 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
requests-oauthlib==2.0.0 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
requests-toolbelt==1.0.0 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
requests==2.32.3 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
rich==14.0.0 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
rpds-py==0.25.1 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
rsa==4.9.1 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
shellingham==1.5.4 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
six==1.17.0 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
smmap==5.0.2 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
sniffio==1.3.1 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
soupsieve==2.6 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
speechrecognition==3.14.2 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
sqlalchemy==2.0.40 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
sse-starlette==2.3.6 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
sseclient-py==1.8.0 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
starlette==0.36.3 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
strenum==0.4.15 ; python_version >= "3.10" and python_version < "3.11"
sympy==1.13.3 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
syrupy==4.9.1 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
tantivy==0.22.2 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
tenacity==9.1.2 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
tiktoken==0.8.0 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
tokenizers==0.21.1 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
tomli==2.2.1 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
tomlkit==0.13.2 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
tqdm==4.67.1 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
typer==0.15.2 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
types-requests==2.32.0.20250328 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
typing-extensions==4.13.2 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
typing-inspect==0.9.0 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
typing-inspection==0.4.0 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
tzdata==2025.2 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
unstructured-client==0.32.3 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
unstructured==0.16.25 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
urllib3==2.4.0 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
uvicorn==0.27.1 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
uvloop==0.21.0 ; python_version >= "3.10" and python_version <= "3.11" and platform_python_implementation != "PyPy" and (sys_platform != "win32" and sys_platform != "cygwin") or python_version >= "3.12" and python_version < "3.13" and platform_python_implementation != "PyPy" and (sys_platform != "win32" and sys_platform != "cygwin")
watchfiles==1.0.5 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
webencodings==0.5.1 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
websocket-client==1.8.0 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
websockets==15.0.1 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
wrapt==1.17.2 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
xlrd==2.0.1 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
xlsxwriter==3.2.3 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
yarl==1.20.0 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
youtube-transcript-api==1.0.3 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
zipp==3.21.0 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
zstandard==0.23.0 ; python_version >= "3.10" and python_version <= "3.11" or python_version >= "3.12" and python_version < "3.13"
