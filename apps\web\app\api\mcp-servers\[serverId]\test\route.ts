import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/next-auth";
import db from "@/lib/shared-db";
import jwt from "jsonwebtoken";

const PYTHON_API_URL =
  process.env.NEXT_PUBLIC_API_URL || "http://localhost:8000";

/**
 * Call Python backend for MCP server operations
 */
async function callPythonBackend(
  endpoint: string,
  data: any,
  session: any,
  method: string = "POST"
) {
  const payload = {
    userId: session.userId,
    email: session.user?.email,
    name: session.user?.name,
    organizationId: session.currentOrganization?.id,
    iat: Math.floor(Date.now() / 1000),
  };

  const secret = process.env.NEXTAUTH_SECRET;
  if (!secret) {
    throw new Error("NEXTAUTH_SECRET is not defined");
  }

  const token = jwt.sign(payload, secret, { expiresIn: "1h" });

  const headers: Record<string, string> = {
    "Content-Type": "application/json",
    Authorization: `Bearer ${token}`,
    "x-user-id": session.userId,
    "x-tenant-id": session.currentOrganization?.id || "",
  };

  const requestOptions: RequestInit = {
    method,
    headers,
  };

  if (method !== "GET" && data) {
    requestOptions.body = JSON.stringify(data);
  }

  const response = await fetch(
    `${PYTHON_API_URL}/api/v1/mcp-chat${endpoint}`,
    requestOptions
  );

  if (!response.ok) {
    const errorText = await response.text();
    let errorData: any;
    try {
      errorData = JSON.parse(errorText);
    } catch {
      errorData = { detail: errorText || "Unknown error" };
    }
    console.error(
      `Error calling Python backend (${endpoint}):`,
      JSON.stringify(errorData)
    );
    throw new Error(
      errorData.detail ||
        errorData.message ||
        `HTTP ${response.status}: ${errorText}`
    );
  }

  return await response.json();
}

/**
 * POST /api/mcp-servers/[serverId]/test - Test an MCP server
 */
export async function POST(
  req: NextRequest,
  { params }: { params: { serverId: string } }
) {
  try {
    const session: any = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { serverId } = params;

    const { searchParams } = new URL(req.url);
    const tenantId =
      searchParams.get("currentOrganizationId") ||
      session.currentOrganization?.id;

    // Check if server exists and user has access
    const server = await db.mCPServer.findFirst({
      where: {
        id: serverId,
        tenantId,
        OR: [{ userId: session.userId }, { isPublic: true }],
      },
    });

    if (!server) {
      return NextResponse.json(
        { error: "MCP server not found" },
        { status: 404 }
      );
    }

    try {
      // Call Python backend to test the server
      const result = await callPythonBackend(
        `/${serverId}/test-connection?tenant_id=${tenantId}`,
        null,
        session,
        "POST"
      );

      // Update status based on test result
      await db.mCPServer.update({
        where: { id: serverId },
        data: {
          status: result.status || "ACTIVE",
          lastError: null,
          updatedAt: new Date(),
        },
      });

      // Add debug information to response
      const enhancedResult = {
        ...result,
        debug_info: {
          server_name: server.name,
          server_command: server.command,
          server_args: server.args,
          test_timestamp: new Date().toISOString(),
        },
      };

      return NextResponse.json(enhancedResult);
    } catch (testError: any) {
      // Update status to error if test failed
      await db.mCPServer.update({
        where: { id: serverId },
        data: {
          status: "ERROR",
          lastError: testError.message,
          updatedAt: new Date(),
        },
      });
      throw testError;
    }
  } catch (error: any) {
    console.error("[MCP_SERVER_TEST]", error);
    return NextResponse.json(
      { error: error.message || "Failed to test MCP server" },
      { status: 500 }
    );
  }
}
