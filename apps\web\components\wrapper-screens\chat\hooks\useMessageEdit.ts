import { useState } from "react";
import { toast } from "react-hot-toast";
import { Message } from "../types";
import { useLanguage } from "@/lib/language-context";
import { createMessage, updateMessage } from "@/services/src/message";
import {
  canEditMessage,
  createTemporaryEditedMessage,
  addTemporaryEditedMessage,
  removeTemporaryEditedMessage,
  findAssistantMessageToRegenerate,
  buildContextForEditedMessage,
} from "../utils/edit-utils";
import {
  copilotKitChatService,
  CopilotKitChatQuery,
} from "@/services/copilotkit-chat";

export const useMessageEdit = (
  messages: Message[],
  setMessages: React.Dispatch<React.SetStateAction<Message[]>>,
  chatId: string | null,
  userId: string,
  tenantId: string,
  userName: string,
  headers: Record<string, string>,
  includeWebResults: boolean,
  updateDisplayIndex: (
    messageId: string,
    newIndex: number,
    isManuallySet: boolean
  ) => void,
  imageContext: string,
  selectedSearchMode: any
) => {
  const { t, language } = useLanguage();
  const [isEditing, setIsEditing] = useState<Record<string, boolean>>({});
  const [editContent, setEditContent] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(false);

  const API_BASE_URL =
    process.env.NEXT_PUBLIC_API_URL || "http://localhost:8000";
  const API_VERSION = "/api/v1";

  // Helper function to handle user message edits (with AI response generation)
  const handleUserMessageEdit = async (
    message: Message,
    newContent: string,
    messageIndex: number
  ) => {
    if (!chatId || !message.id) {
      toast.error(t("chat.editError"));
      return;
    }

    // Step 1: Edit the user message
    const tempEditedMessage = createTemporaryEditedMessage(message, newContent);
    setMessages((prev) =>
      addTemporaryEditedMessage(prev, message, tempEditedMessage)
    );

    // Create the edited user message in the database
    const editedUserMessage = await createMessage({
      chatId,
      userId,
      content: newContent,
      role: message.role,
      metadata: {
        editedResponse: true,
        isSelected: true,
        // Preserve image metadata
        hasImages: message.metadata?.hasImages,
        images: message.metadata?.images,
        imageContext,
        searchMode: selectedSearchMode,
      },
      originalMessageId: message.id,
      sources: message.sources,
    });

    if (!editedUserMessage || editedUserMessage.error) {
      setMessages((prev) => removeTemporaryEditedMessage(prev, message));
      toast.error(t("chat.editFailed"));
      return;
    }

    // Update the original user message
    await updateMessage({
      chatId,
      messageId: message.id,
      metadata: {
        ...message.metadata,
        originalResponse: true,
      },
    });

    // Step 2: Find the assistant message that should be regenerated
    const assistantMessageToRegenerate = findAssistantMessageToRegenerate(
      messages,
      messageIndex
    );

    if (!assistantMessageToRegenerate) {
      // No assistant message to regenerate, just show success for user edit
      toast.success(t("chat.editSuccess"));
      return;
    }

    // Step 3: Generate new AI response based on edited user message
    try {
      toast.loading(t("chat.regenerating"));

      // Build conversation context up to the edited message
      const { contextMessages, images } = buildContextForEditedMessage(
        messages,
        messageIndex,
        newContent
      );

      // Prepare CopilotKit query for edit regeneration
      const query: CopilotKitChatQuery = {
        question: newContent,
        stream: true,
        search_mode: selectedSearchMode,
        include_web_results: includeWebResults,
        language: language || "en",
        images:
          images.length > 0
            ? images
                .map((img) =>
                  typeof img === "string" ? img : img.data || img.preview || ""
                )
                .filter(Boolean)
            : undefined,
        image_context: imageContext,
      };

      // Handle streaming response
      await handleStreamingAIResponse(query, assistantMessageToRegenerate);

      toast.success(t("chat.editSuccess"));
    } catch (error) {
      console.error("Error generating AI response after edit:", error);
      toast.remove();
      toast.error(t("chat.editError"));
    }
  };

  // Helper function to handle streaming AI response after user message edit
  const handleStreamingAIResponse = async (
    query: CopilotKitChatQuery,
    assistantMessage: Message
  ) => {
    let streamedContent = "";
    let regeneratedSources: any[] = [];
    let currentToolsUsed: string[] = [];
    let responseTime = 0;
    let thinkingContent = "";

    try {
      // Create a temporary edited assistant message for streaming
      const tempAIMessage = {
        role: "assistant" as const,
        content: "",
        metadata: {
          editedResponse: true,
          isSelected: true,
          isStreaming: true,
        },
        originalMessageId: assistantMessage.id,
        sources: [],
      };

      // Add the temporary AI response as an edited version of the assistant message
      setMessages((prev) =>
        prev.map((msg) => {
          if (msg.id === assistantMessage.id) {
            const existingEditedMessages = msg.editedMessages || [];
            const updatedEditedMessages = [
              ...existingEditedMessages,
              tempAIMessage,
            ];

            return {
              ...msg,
              metadata: {
                ...msg.metadata,
                originalResponse: true,
              },
              editedMessages: updatedEditedMessages,
            };
          }
          return msg;
        })
      );

      // Update display index to show the streaming AI response
      if (assistantMessage.id) {
        const editVersionCount =
          (assistantMessage.editedMessages?.length || 0) + 1;
        updateDisplayIndex(assistantMessage.id, editVersionCount, false);
      }

      // Remove the loading toast after the streaming setup is complete
      toast.remove();

      // Stream the response using CopilotKit
      for await (const chunk of copilotKitChatService.streamChat(
        query,
        userId,
        tenantId,
        userName
      )) {
        if (chunk.error) {
          throw new Error(chunk.error);
        }

        // Handle different chunk types
        if (chunk.sources) {
          regeneratedSources = chunk.sources;
        }

        if (chunk.tools_used) {
          currentToolsUsed = chunk.tools_used;
        }

        if (chunk.thinking) {
          thinkingContent = chunk.thinking;
        }

        if (chunk.answer_chunk) {
          streamedContent += chunk.answer_chunk;
        } else if (chunk.answer) {
          streamedContent = chunk.answer;
        }

        if (chunk.elapsed_time) {
          responseTime = chunk.elapsed_time;
        }

        // Update the streaming message
        setMessages((prev) =>
          prev.map((msg) => {
            if (msg.id === assistantMessage.id && msg.editedMessages) {
              const updatedEditedMessages = msg.editedMessages.map(
                (editedMsg, index) => {
                  if (
                    index === msg.editedMessages!.length - 1 &&
                    editedMsg.metadata?.isStreaming
                  ) {
                    return {
                      ...editedMsg,
                      content: streamedContent,
                      sources: regeneratedSources,
                      metadata: {
                        ...editedMsg.metadata,
                        tools_used: currentToolsUsed,
                        elapsed_time: responseTime,
                        thinking: thinkingContent,
                      },
                    };
                  }
                  return editedMsg;
                }
              );

              return {
                ...msg,
                editedMessages: updatedEditedMessages,
              };
            }
            return msg;
          })
        );

        if (chunk.done) {
          break;
        }
      }
    } catch (error) {
      console.error("Error in CopilotKit streaming:", error);
    } finally {
      // Clean up final content
      let finalContent = streamedContent;
      if (finalContent.startsWith("Answer:")) {
        finalContent = finalContent.replace("Answer:", "").trim();
      }
      if (finalContent.startsWith("ERROR:")) {
        finalContent = "Error: " + finalContent.replace("ERROR:", "").trim();
      }

      // Create the final AI response message in the database
      if (chatId && assistantMessage.id) {
        try {
          const newAIMessage = await createMessage({
            chatId,
            userId,
            content: finalContent,
            role: "assistant",
            metadata: {
              editedResponse: true,
              isSelected: true,
              tools_used: currentToolsUsed,
              elapsed_time: responseTime,
              thinking: thinkingContent,
            },
            originalMessageId: assistantMessage.id,
            sources: regeneratedSources,
          });

          if (newAIMessage && !newAIMessage.error) {
            // Update the temporary message with the real ID
            setMessages((prev) =>
              prev.map((msg) => {
                if (msg.id === assistantMessage.id && msg.editedMessages) {
                  const updatedEditedMessages = msg.editedMessages.map(
                    (editedMsg, index) => {
                      if (
                        index === msg.editedMessages!.length - 1 &&
                        editedMsg.metadata?.isStreaming
                      ) {
                        return {
                          ...editedMsg,
                          id: newAIMessage.id,
                          content: finalContent,
                          sources: regeneratedSources,
                          metadata: {
                            ...editedMsg.metadata,
                            isStreaming: false,
                            tools_used: currentToolsUsed,
                            elapsed_time: responseTime,
                            thinking: thinkingContent,
                          },
                        };
                      }
                      return editedMsg;
                    }
                  );

                  return {
                    ...msg,
                    editedMessages: updatedEditedMessages,
                  };
                }
                return msg;
              })
            );
          }
        } catch (error) {
          console.error("Error saving AI response:", error);
        }
      }
    }
  };

  // Function to start editing a message
  const startEdit = (messageIndex: number, displayedContent?: string) => {
    const message = messages[messageIndex];
    if (!canEditMessage(message, messages)) {
      if (message.role !== "user") {
        toast.error("Only user messages can be edited");
      } else {
        toast.error("Only the last 5 user messages can be edited");
      }
      return;
    }

    setIsEditing((prev) => ({ ...prev, [message.id!]: true }));
    // Use displayed content if provided, otherwise fall back to original message content
    // Use ?? instead of || to properly handle empty strings
    const contentToEdit = displayedContent ?? message.content;
    setEditContent((prev) => ({ ...prev, [message.id!]: contentToEdit }));
  };

  // Function to cancel editing
  const cancelEdit = (messageIndex: number) => {
    const message = messages[messageIndex];
    if (!message.id) return;

    setIsEditing((prev) => {
      const newState = { ...prev };
      delete newState[message.id!];
      return newState;
    });

    setEditContent((prev) => {
      const newState = { ...prev };
      delete newState[message.id!];
      return newState;
    });

    // Remove any temporary edited message
    setMessages((prev) => removeTemporaryEditedMessage(prev, message));
  };

  // Function to update edit content
  const updateEditContent = (messageId: string, content: string) => {
    setEditContent((prev) => ({ ...prev, [messageId]: content }));
  };

  // Function to save the edit
  const saveEdit = async (messageIndex: number) => {
    const message = messages[messageIndex];
    if (!message.id || !chatId) return;

    const newContent = editContent[message.id];
    // Allow empty content but check if content has actually changed
    if (newContent === undefined || newContent === message.content) {
      cancelEdit(messageIndex);
      return;
    }

    try {
      setIsLoading(true);

      // Only user messages can be edited
      if (message.role === "user") {
        // Don't trim the content here to preserve user's intention (including empty strings)
        await handleUserMessageEdit(message, newContent, messageIndex);
        return;
      }

      // This should never happen since canEditMessage prevents non-user messages from being edited
      console.error("Attempted to edit non-user message:", message.role);
      toast.error(t("chat.editError"));
    } catch (error) {
      console.error("Error editing message:", error);
      // Remove any temporary messages on error
      setMessages((prev) => removeTemporaryEditedMessage(prev, message));
      toast.error(t("chat.editError"));
    } finally {
      setIsLoading(false);
      // Clear editing state
      setIsEditing((prev) => {
        const newState = { ...prev };
        delete newState[message.id!];
        return newState;
      });
      setEditContent((prev) => {
        const newState = { ...prev };
        delete newState[message.id!];
        return newState;
      });
    }
  };

  // Function to check if a message can be edited
  // Note: processedMessageIndex refers to the index in processedMessages, not the original messages array
  const canEdit = (
    processedMessageIndex: number,
    processedMessages: Message[]
  ): boolean => {
    const messageFromProcessed = processedMessages[processedMessageIndex];
    if (!messageFromProcessed || !messageFromProcessed.id) return false;

    // Find the message in the original messages array by ID
    const originalMessageIndex = messages.findIndex(
      (msg) => msg.id === messageFromProcessed.id
    );
    if (originalMessageIndex === -1) return false;

    const originalMessage = messages[originalMessageIndex];
    return canEditMessage(originalMessage, messages);
  };

  // Function to check if a message is currently being edited
  const isMessageEditing = (messageId: string): boolean => {
    return isEditing[messageId] || false;
  };

  // Function to get edit content for a message
  const getEditContent = (messageId: string): string => {
    // Use ?? instead of || to properly handle empty strings
    return editContent[messageId] ?? "";
  };

  return {
    startEdit,
    cancelEdit,
    saveEdit,
    updateEditContent,
    canEdit,
    isMessageEditing,
    getEditContent,
    isLoading,
  };
};
