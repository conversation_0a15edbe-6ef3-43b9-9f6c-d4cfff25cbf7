"use client";

import { cn } from "@/lib/utils";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { useTranslatedToast } from "@/hooks/use-translated-toast";
import { useRouter, useSearchParams } from "next/navigation";
import { signIn } from "next-auth/react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { setCookie } from "@/utils/cookies";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { useState } from "react";
import Link from "next/link";
import {
  Building,
  ChevronLeft,
  ChevronRight,
  Eye,
  EyeOff,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
} from "lucide-react";
import { register } from "@/services";
import { <PERSON><PERSON>, <PERSON>alogContent, DialogHeader } from "@/components/ui/dialog";
import { useLanguage } from "@/lib/language-context";
import { Checkbox } from "@/components/ui/checkbox";

// We need to define the schemas inside the component to access the translation function
const createSchemas = (t: any) => {
  // Password reset form schema
  const passwordResetSchema = z
    .object({
      newPassword: z.string().min(8, {
        message: t("auth.passwordMinLength"),
      }),
      confirmPassword: z.string(),
    })
    .refine((data) => data.newPassword === data.confirmPassword, {
      message: t("auth.passwordsDoNotMatch"),
      path: ["confirmPassword"],
    });

  // Login form schema
  const loginFormSchema = z.object({
    email: z.string().email({
      message: t("auth.validEmail"),
    }),
    password: z.string().min(6, {
      message: t("auth.passwordMinLength"),
    }),
    rememberMe: z.boolean().optional().default(false),
  });

  // Step 1 schema (user info)
  const userInfoSchema = z
    .object({
      name: z.string().min(2, {
        message: t("auth.nameMinLength"),
      }),
      email: z.string().email({
        message: t("auth.validEmail"),
      }),
      password: z.string().min(8, {
        message: t("auth.passwordMinLength"),
      }),
      confirmPassword: z.string(),
      agreeToTerms: z.boolean().refine((value) => value === true, {
        message: t("auth.agreeToTerms"),
      }),
    })
    .refine((data) => data.password === data.confirmPassword, {
      message: t("auth.passwordsDoNotMatch"),
      path: ["confirmPassword"],
    });

  // Step 2 schema (organization info)
  const orgInfoSchema = z.object({
    orgName: z
      .string()
      .min(2, {
        message: t("auth.organizationNameMinLength"),
      })
      .default(""),
    orgUrl: z.string().url().optional().or(z.literal("")).default(""),
    orgDescription: z.string().optional().or(z.literal("")).default(""),
  });

  return {
    passwordResetSchema,
    loginFormSchema,
    userInfoSchema,
    orgInfoSchema,
  };
};

export function LoginForm({ className = "", isSignIn, ...props }) {
  const { t } = useLanguage(); // Use language context for translations
  const toast = useTranslatedToast();

  // Create schemas with translations
  const {
    loginFormSchema,
    userInfoSchema,
    orgInfoSchema,
    passwordResetSchema,
  } = createSchemas(t);

  // Define types based on schemas
  type LoginFormValues = z.infer<typeof loginFormSchema>;
  type PasswordResetFormValues = z.infer<typeof passwordResetSchema>;
  type UserInfoValues = z.infer<typeof userInfoSchema>;
  type OrgInfoValues = z.infer<typeof orgInfoSchema>;

  const [isView, setIsView] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [registrationStep, setRegistrationStep] = useState(1); // 1: User info, 2: Organization info
  const [passwordResetRequired, setPasswordResetRequired] = useState(false);
  const [registrationComplete, setRegistrationComplete] = useState(false);
  const [registeredEmail, setRegisteredEmail] = useState("");

  const router = useRouter();
  const searchParams = useSearchParams();
  const callbackUrl = searchParams?.get("callbackUrl") || "/";
  const inviteSlug = searchParams?.get("invite");

  // Login form
  const loginForm = useForm<LoginFormValues>({
    resolver: zodResolver(loginFormSchema as any),
    defaultValues: {
      email: "",
      password: "",
      rememberMe: false,
    },
  });

  // Step 1 form - user details
  const step1Form = useForm<UserInfoValues>({
    resolver: zodResolver(userInfoSchema as any),
    defaultValues: {
      name: "",
      email: "",
      password: "",
      confirmPassword: "",
      agreeToTerms: false,
    },
  });

  // Step 2 form - recreated every time the step changes
  const [step2FormKey, setStep2FormKey] = useState(0);
  const step2Form = useForm<OrgInfoValues>({
    resolver: zodResolver(orgInfoSchema as any),
    defaultValues: {
      orgName: "",
      orgUrl: "",
      orgDescription: "",
    },
    // Set empty values imperatively to ensure no carryover
    values: {
      orgName: "",
      orgUrl: "",
      orgDescription: "",
    },
  });

  // Password reset form
  const resetForm = useForm<PasswordResetFormValues>({
    resolver: zodResolver(passwordResetSchema as any),
    defaultValues: {
      newPassword: "",
      confirmPassword: "",
    },
  });

  const moveToNextStep = async () => {
    // Validate user fields before proceeding
    const isValid = await step1Form.trigger();

    if (!isValid) {
      return;
    }

    // Reset the form entirely by incrementing the key
    setStep2FormKey((prev) => prev + 1);

    // Reset the form values programmatically
    step2Form.reset({
      orgName: "",
      orgUrl: "",
      orgDescription: "",
    });

    // Use setTimeout to ensure reset happens before form is shown
    setTimeout(() => {
      setRegistrationStep(2);
    }, 0);
  };

  const moveToPreviousStep = () => {
    setRegistrationStep(1);
  };

  const onResetSubmit = async (_data: PasswordResetFormValues) => {
    // This is a placeholder for password reset functionality
    setIsLoading(true);
    toast.loading(t("auth.resettingPassword"));

    // Simulate API call
    setTimeout(() => {
      toast.dismiss();
      setIsLoading(false);
      setPasswordResetRequired(false);

      toast.success(t("auth.passwordResetSuccess"));
      router.push("/dashboard");
    }, 1500);
  };

  const onSignUpSubmit = async (formData: OrgInfoValues) => {
    setIsLoading(true);
    toast.loading(t("organization.creatingOrganization"));

    // Get user data from step 1
    const userData = step1Form.getValues();

    try {
      // Create user and organization in one call
      const response = await register({
        // User data
        name: userData.name,
        email: userData.email,
        password: userData.password,
        verifyEmail: true, // Request email verification
        // Organization data (only if not invite registration)
        organization: inviteSlug
          ? undefined
          : {
              name: formData.orgName,
              slug: formData.orgName.toLowerCase().replace(/\s+/g, "-"),
              url: formData.orgUrl || undefined,
              description: formData.orgDescription || undefined,
            },
        // Invite data
        inviteSlug: inviteSlug || undefined,
      });

      toast.dismiss();
      setIsLoading(false);

      if (response?.error) {
        toast.error(t("auth.registrationError", { error: response?.error }));
        return;
      }

      // Show registration success and email verification info
      setRegisteredEmail(userData.email);
      setRegistrationComplete(true);
    } catch (error: any) {
      toast.dismiss();
      setIsLoading(false);
      console.error("Registration Failed:", error);
      toast.error(t("auth.registrationFailed"));
    }
  };

  const onSigninSubmit = async (data: LoginFormValues) => {
    toast.loading(t("auth.signingIn"));
    setIsLoading(true);
    const { email, password } = data;

    try {
      const response: any = await signIn("credentials", {
        username: email,
        password,
        redirect: false,
      });

      toast.dismiss();
      setIsLoading(false);

      if (!response?.error) {
        // Successful login
        toast.success(t("auth.signInSuccessful"));

        // Get the session to access userId and currentOrganization
        const session = await fetch("/api/auth/session").then((res) =>
          res.json()
        );

        // Set userId and currentOrganization cookies
        if (session?.userId) {
          setCookie("userId", session.userId);
        }

        if (session?.currentOrganization?.id) {
          setCookie("currentOrganizationId", session.memberships[0].tenant.id);
        }

        router.push(callbackUrl);
        router.refresh();
        return;
      }

      // Check for password reset required
      if (response.error === "PASSWORD_RESET_REQUIRED") {
        setPasswordResetRequired(true);
        return;
      }

      // Check for email verification required
      if (
        response.error === "EMAIL_NOT_VERIFIED" ||
        response?.error?.toLowerCase()?.includes("email not verified")
      ) {
        toast.error(t("auth.verifyEmailBeforeSignIn"));
        router.push(`/email-verification?email=${encodeURIComponent(email)}`);
        return;
      }

      // Handle other errors
      if (response?.error) {
        toast.apiError(response.error);
      } else {
        toast.error("auth.signInFailedCheckCredentials");
      }
    } catch (error: any) {
      toast.dismiss();
      setIsLoading(false);
      console.error("Sign In Failed:", error);
      toast.error(t("auth.signInFailedTryAgain"));
    }
  };

  // If registration is complete, show success message with verification instructions
  if (registrationComplete) {
    return (
      <div className={cn("flex flex-col gap-6", className)} {...props}>
        <Card>
          <CardHeader>
            <div className="flex justify-center mb-4">
              <div className="bg-primary/10 p-3 rounded-full">
                <MailCheck className="h-8 w-8 text-primary" />
              </div>
            </div>
            <CardTitle className="text-2xl text-center">
              {t("verification.verifyEmail")}
            </CardTitle>
            <CardDescription className="text-center">
              {t("verification.registrationSuccessful")}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-center space-y-4">
              <p className="text-sm text-muted-foreground">
                {t("verification.sentVerificationEmailTo", {
                  email: registeredEmail,
                })}
              </p>

              <div className="bg-muted/50 rounded p-4 text-sm">
                <p className="font-medium mb-2">
                  {t("verification.cantFindEmail")}
                </p>
                <ul className="text-xs text-muted-foreground space-y-1 text-left list-disc list-inside">
                  <li>{t("verification.checkSpam")}</li>
                  <li>{t("verification.allowTime")}</li>
                  <li>
                    <Link
                      href={`/email-verification?email=${encodeURIComponent(
                        registeredEmail
                      )}`}
                      className="text-primary hover:underline"
                    >
                      {t("verification.clickToResend")}
                    </Link>
                  </li>
                </ul>
              </div>

              <Button className="w-full mt-4" asChild>
                <Link href="/sign-in">{t("verification.goToSignIn")}</Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className={cn("flex flex-col gap-6", className)} {...props}>
      <Card>
        <CardHeader>
          <CardTitle className="text-2xl">
            {isSignIn ? t("common.signIn") : t("auth.signUp")}
          </CardTitle>
          <CardDescription>
            {isSignIn
              ? t("auth.loginTitle")
              : registrationStep === 1
                ? t("auth.registerTitle")
                : t("organization.tellUsAboutOrganization")}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isSignIn ? (
            // Sign In Form
            <Form {...loginForm}>
              <form
                onSubmit={loginForm.handleSubmit(onSigninSubmit)}
                className="space-y-6"
              >
                <div className="flex flex-col gap-6">
                  <FormField
                    control={loginForm.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t("common.email")}</FormLabel>
                        <FormControl>
                          <Input
                            placeholder={t("auth.emailPlaceholder")}
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={loginForm.control}
                    name="password"
                    render={({ field }) => (
                      <FormItem>
                        <div className="flex items-center justify-between">
                          <FormLabel>{t("common.password")}</FormLabel>
                          <Link
                            href="/forgot-password"
                            className="text-xs text-primary hover:underline"
                          >
                            {t("common.forgotPassword")}
                          </Link>
                        </div>
                        <FormControl>
                          <div className="relative">
                            <Input
                              type={isView ? "text" : "password"}
                              id="password"
                              placeholder={t("auth.passwordPlaceholder")}
                              {...field}
                            />
                            {isView ? (
                              <Eye
                                className="absolute right-4 top-2 z-10 cursor-pointer text-gray-500"
                                onClick={() => {
                                  setIsView(!isView);
                                }}
                              />
                            ) : (
                              <EyeOff
                                className="absolute right-4 top-2 z-10 cursor-pointer text-gray-500"
                                onClick={() => setIsView(!isView)}
                              />
                            )}
                          </div>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* <FormField
                    control={loginForm.control}
                    name="rememberMe"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                        <FormControl>
                          <Checkbox
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                        <div className="space-y-1 leading-none">
                          <FormLabel className="text-sm font-normal">
                            {t("auth.rememberMe")}
                          </FormLabel>
                        </div>
                      </FormItem>
                    )}
                  /> */}

                  <Button type="submit" disabled={isLoading} className="w-full">
                    {isLoading ? (
                      <Loader2 className="animate-spin mr-2" />
                    ) : null}
                    {t("common.signIn")}
                  </Button>
                </div>
                <div className="mt-4 text-center text-sm">
                  {t("auth.dontHaveAccount")}{" "}
                  <Link
                    href="/register"
                    className="text-primary hover:underline"
                  >
                    {t("auth.signUp")}
                  </Link>
                </div>
              </form>
            </Form>
          ) : // Registration Forms - Multi-step
          registrationStep === 1 ? (
            // Step 1: User Details
            <Form {...step1Form}>
              <form className="space-y-6">
                <div className="flex flex-col gap-6">
                  <div className="flex items-center gap-2 mb-2">
                    <User className="h-5 w-5 text-primary" />
                    <h3 className="text-sm font-medium">
                      {t("auth.accountInformation")}
                    </h3>
                  </div>

                  <FormField
                    control={step1Form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t("common.name")}</FormLabel>
                        <FormControl>
                          <Input
                            placeholder={
                              t("profile.firstNamePlaceholder") +
                              " " +
                              t("profile.lastNamePlaceholder")
                            }
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={step1Form.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t("common.email")}</FormLabel>
                        <FormControl>
                          <Input
                            placeholder={t("auth.emailPlaceholder")}
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={step1Form.control}
                    name="password"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t("common.password")}</FormLabel>
                        <FormControl>
                          <div className="relative">
                            <Input
                              type={isView ? "text" : "password"}
                              id="password"
                              placeholder={t("auth.passwordPlaceholder")}
                              {...field}
                            />
                            {isView ? (
                              <Eye
                                className="absolute right-4 top-2 z-10 cursor-pointer text-gray-500"
                                onClick={() => {
                                  setIsView(!isView);
                                }}
                              />
                            ) : (
                              <EyeOff
                                className="absolute right-4 top-2 z-10 cursor-pointer text-gray-500"
                                onClick={() => setIsView(!isView)}
                              />
                            )}
                          </div>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={step1Form.control}
                    name="confirmPassword"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t("auth.confirmPassword")}</FormLabel>
                        <FormControl>
                          <Input
                            type={isView ? "text" : "password"}
                            placeholder={t("auth.passwordPlaceholder")}
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={step1Form.control}
                    name="agreeToTerms"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-start space-x-3 space-y-0 mt-2">
                        <FormControl>
                          <Checkbox
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                        <div className="space-y-1 leading-none">
                          <FormLabel className="text-sm font-normal">
                            {t("auth.iAgreeToThe")}{" "}
                            <Link
                              href="/terms"
                              className="text-primary hover:underline"
                              about="noopener noreferrer"
                              target="_blank"
                            >
                              {t("auth.termsOfService")}
                            </Link>{" "}
                            {t("auth.and")}{" "}
                            <Link
                              href="/privacy-policy"
                              className="text-primary hover:underline"
                              about="noopener noreferrer"
                              target="_blank"
                            >
                              {t("auth.privacyPolicy")}
                            </Link>
                          </FormLabel>
                          <FormMessage />
                        </div>
                      </FormItem>
                    )}
                  />

                  <Button
                    type="button"
                    onClick={moveToNextStep}
                    className="w-full flex items-center justify-center gap-1"
                  >
                    {t("auth.continue")}
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </div>

                <div className="mt-4 text-center text-sm">
                  {t("auth.alreadyHaveAccount")}{" "}
                  <Link
                    href="/sign-in"
                    className="text-primary hover:underline"
                  >
                    {t("common.signIn")}
                  </Link>
                </div>
              </form>
            </Form>
          ) : (
            // Step 2: Organization Details
            <Form {...step2Form} key={step2FormKey}>
              <form
                onSubmit={step2Form.handleSubmit(onSignUpSubmit)}
                className="space-y-6"
              >
                <div className="flex flex-col gap-6">
                  <div className="flex items-center gap-2 mb-2">
                    <Building className="h-5 w-5 text-primary" />
                    <h3 className="text-sm font-medium">
                      {t("organization.organizationInfo")}
                    </h3>
                  </div>

                  <FormField
                    control={step2Form.control}
                    name="orgName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t("organization.nameLabel")}</FormLabel>
                        <FormControl>
                          <Input
                            placeholder={t("organization.namePlaceholder")}
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={step2Form.control}
                    name="orgUrl"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          {t("organization.urlLabel")} ({t("common.optional")})
                        </FormLabel>
                        <FormControl>
                          <Input
                            placeholder={t("organization.urlPlaceholder")}
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={step2Form.control}
                    name="orgDescription"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          {t("common.description")} ({t("common.optional")})
                        </FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder={t(
                              "organization.descriptionPlaceholder"
                            )}
                            className="resize-none"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="flex gap-3">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={moveToPreviousStep}
                      className="flex-1 flex items-center justify-center gap-1"
                    >
                      <ChevronLeft className="h-4 w-4" />
                      {t("common.back")}
                    </Button>
                    <Button
                      type="submit"
                      disabled={isLoading}
                      className="flex-1"
                    >
                      {isLoading ? (
                        <Loader2 className="animate-spin mr-2" />
                      ) : null}
                      {t("auth.completeSignUp")}
                    </Button>
                  </div>
                </div>

                <div className="mt-4 text-center text-sm">
                  {t("auth.alreadyHaveAccount")}{" "}
                  <Link
                    href="/sign-in"
                    className="text-primary hover:underline"
                  >
                    {t("common.signIn")}
                  </Link>
                </div>
              </form>
            </Form>
          )}
        </CardContent>
      </Card>

      {/* Password Reset Dialog */}
      {isSignIn && (
        <Dialog
          open={passwordResetRequired}
          onOpenChange={setPasswordResetRequired}
        >
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <CardTitle className="text-xl">
                {t("auth.resetPassword")}
              </CardTitle>
              <CardDescription>
                {t("auth.setNewPasswordBeforeContinuing")}
              </CardDescription>
            </DialogHeader>
            <Form {...resetForm}>
              <form
                onSubmit={resetForm.handleSubmit(onResetSubmit)}
                className="space-y-4"
              >
                <FormField
                  control={resetForm.control}
                  name="newPassword"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t("auth.newPassword")}</FormLabel>
                      <FormControl>
                        <div className="relative">
                          <Input
                            type={isView ? "text" : "password"}
                            placeholder={t("auth.createNewPassword")}
                            {...field}
                          />
                          {isView ? (
                            <Eye
                              className="absolute right-4 top-2 z-10 cursor-pointer text-gray-500"
                              onClick={() => setIsView(!isView)}
                            />
                          ) : (
                            <EyeOff
                              className="absolute right-4 top-2 z-10 cursor-pointer text-gray-500"
                              onClick={() => setIsView(!isView)}
                            />
                          )}
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={resetForm.control}
                  name="confirmPassword"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t("auth.confirmNewPassword")}</FormLabel>
                      <FormControl>
                        <Input
                          type={isView ? "text" : "password"}
                          placeholder={t("auth.confirmNewPasswordPlaceholder")}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <Button disabled={isLoading} type="submit" className="w-full">
                  {isLoading ? <Loader2 className="animate-spin mr-2" /> : null}
                  {t("auth.resetPassword")}
                </Button>
              </form>
            </Form>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
}
