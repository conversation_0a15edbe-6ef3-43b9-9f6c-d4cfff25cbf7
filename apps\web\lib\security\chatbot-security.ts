/**
 * Chatbot Security Middleware
 *
 * Comprehensive security layer for chatbot SDK endpoints
 */

import { NextRequest } from "next/server";
import db from "@/lib/shared-db";
import crypto from "crypto";
import {
  rateLimiter,
  RATE_LIMIT_CONFIGS,
  createRateLimitKey,
  hashIdentifier,
} from "@/lib/rate-limiter";

export interface SecurityContext {
  chatbotId: string;
  apiKey: string;
  clientIP: string;
  hashedIP: string;
  origin?: string;
  userAgent?: string;
  referer?: string;
  userToken?: string; // For private chatbot access
}

export interface SecurityValidationResult {
  success: boolean;
  chatbot?: any;
  user?: any;
  error?: string;
  statusCode?: number;
  headers?: Record<string, string>;
  securityFlags?: {
    suspiciousActivity: boolean;
    rateLimited: boolean;
    domainBlocked: boolean;
    apiKeyInvalid: boolean;
  };
}

/**
 * Comprehensive security validation for chatbot requests
 */
export async function validateChatbotSecurity(
  request: NextRequest,
  chatbotId: string,
  apiKey: string
): Promise<SecurityValidationResult> {
  const context = extractSecurityContext(request, chatbotId, apiKey);

  try {
    // 1. Basic input validation
    const inputValidation = validateInputs(context);
    if (!inputValidation.success) {
      return inputValidation;
    }

    // 2. Rate limiting check
    const rateLimitResult = checkRateLimits(context);
    if (!rateLimitResult.success) {
      return rateLimitResult;
    }

    // 3. Database validation
    const dbValidation = await validateChatbotAccess(context);
    if (!dbValidation.success) {
      return dbValidation;
    }

    // 4. Domain validation
    const domainValidation = await validateDomainAccess(
      dbValidation.chatbot!,
      context
    );
    if (!domainValidation.success) {
      return domainValidation;
    }

    // 5. Suspicious activity detection
    const activityValidation = detectSuspiciousActivity(
      context,
      dbValidation.chatbot!
    );
    if (!activityValidation.success) {
      return activityValidation;
    }

    // 6. Private chatbot access validation
    const privateAccessValidation = await validatePrivateChatbotAccess(
      dbValidation.chatbot!,
      context
    );
    if (!privateAccessValidation.success) {
      return privateAccessValidation;
    }

    // 7. Tenant status validation
    const tenantValidation = validateTenantStatus(dbValidation.chatbot!);
    if (!tenantValidation.success) {
      return tenantValidation;
    }

    return {
      success: true,
      chatbot: dbValidation.chatbot,
      user: privateAccessValidation.user,
      securityFlags: {
        suspiciousActivity: false,
        rateLimited: false,
        domainBlocked: false,
        apiKeyInvalid: false,
      },
    };
  } catch (error) {
    console.error("Security validation error:", error);
    return {
      success: false,
      error: "Security validation failed",
      statusCode: 500,
    };
  }
}

/**
 * Extract security context from request
 */
function extractSecurityContext(
  request: NextRequest,
  chatbotId: string,
  apiKey: string
): SecurityContext {
  const clientIP = getClientIP(request);
  const hashedIP = hashIdentifier(clientIP);

  return {
    chatbotId,
    apiKey,
    clientIP,
    hashedIP,
    origin: request.headers.get("origin") || undefined,
    userAgent: request.headers.get("user-agent") || undefined,
    referer: request.headers.get("referer") || undefined,
    userToken: request.headers.get("x-user-token") || undefined,
  };
}

/**
 * Get client IP from request headers
 */
function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get("x-forwarded-for");
  const realIP = request.headers.get("x-real-ip");
  const cfConnectingIP = request.headers.get("cf-connecting-ip");

  if (cfConnectingIP) return cfConnectingIP;
  if (forwarded) return forwarded.split(",")[0].trim();
  if (realIP) return realIP;

  return "unknown";
}

/**
 * Validate basic inputs
 */
function validateInputs(context: SecurityContext): SecurityValidationResult {
  if (!context.chatbotId || !context.apiKey) {
    return {
      success: false,
      error: "Missing required parameters",
      statusCode: 400,
      securityFlags: {
        suspiciousActivity: true,
        rateLimited: false,
        domainBlocked: false,
        apiKeyInvalid: true,
      },
    };
  }

  // Validate API key format
  if (!context.apiKey.startsWith("skh_") || context.apiKey.length < 40) {
    return {
      success: false,
      error: "Invalid API key format",
      statusCode: 401,
      securityFlags: {
        suspiciousActivity: true,
        rateLimited: false,
        domainBlocked: false,
        apiKeyInvalid: true,
      },
    };
  }

  // Validate chatbot ID format (MongoDB ObjectId)
  if (!/^[0-9a-fA-F]{24}$/.test(context.chatbotId)) {
    return {
      success: false,
      error: "Invalid chatbot ID format",
      statusCode: 400,
      securityFlags: {
        suspiciousActivity: true,
        rateLimited: false,
        domainBlocked: false,
        apiKeyInvalid: false,
      },
    };
  }

  return { success: true };
}

/**
 * Check rate limits
 */
function checkRateLimits(context: SecurityContext): SecurityValidationResult {
  const checks = [
    { key: "minute", config: RATE_LIMIT_CONFIGS.CHATBOT_PER_MINUTE },
    { key: "hour", config: RATE_LIMIT_CONFIGS.CHATBOT_PER_HOUR },
    { key: "day", config: RATE_LIMIT_CONFIGS.CHATBOT_PER_DAY },
  ];

  for (const check of checks) {
    const rateLimitKey = createRateLimitKey(
      context.chatbotId,
      context.hashedIP,
      check.key
    );
    const result = rateLimiter.checkLimit(rateLimitKey, check.config);

    if (!result.allowed) {
      return {
        success: false,
        error: `Rate limit exceeded (${check.key})`,
        statusCode: 429,
        headers: {
          "Retry-After": result.retryAfter?.toString() || "60",
          "X-RateLimit-Remaining": "0",
          "X-RateLimit-Reset": Math.ceil(result.resetTime / 1000).toString(),
        },
        securityFlags: {
          suspiciousActivity: false,
          rateLimited: true,
          domainBlocked: false,
          apiKeyInvalid: false,
        },
      };
    }
  }

  return { success: true };
}

/**
 * Validate chatbot access in database
 */
async function validateChatbotAccess(
  context: SecurityContext
): Promise<SecurityValidationResult> {
  const chatbot = await db.chatbot.findFirst({
    where: {
      id: context.chatbotId,
      apiKey: context.apiKey,
      isActive: true,
    },
    include: {
      tenant: {
        select: {
          id: true,
          name: true,
        },
      },
      user: {
        select: {
          id: true,
          name: true,
        },
      },
    },
  });

  if (!chatbot) {
    return {
      success: false,
      error: "Invalid chatbot ID or API key",
      statusCode: 401,
      securityFlags: {
        suspiciousActivity: true,
        rateLimited: false,
        domainBlocked: false,
        apiKeyInvalid: true,
      },
    };
  }

  return {
    success: true,
    chatbot,
  };
}

/**
 * Validate domain access
 */
async function validateDomainAccess(
  chatbot: any,
  context: SecurityContext
): Promise<SecurityValidationResult> {
  if (!context.origin) {
    // Allow requests without origin (e.g., server-to-server)
    return { success: true };
  }

  // If no allowed domains specified, allow all
  if (!chatbot.allowedDomains || chatbot.allowedDomains.length === 0) {
    return { success: true };
  }

  try {
    const originDomain = new URL(context.origin).hostname;

    const isAllowed = chatbot.allowedDomains.some((domain: string) => {
      // Support wildcard subdomains
      if (domain.startsWith("*.")) {
        const baseDomain = domain.slice(2);
        return originDomain.endsWith(baseDomain);
      }

      // Exact domain match
      return domain === originDomain;
    });

    if (!isAllowed) {
      return {
        success: false,
        error: "Domain not allowed to access this chatbot",
        statusCode: 403,
        securityFlags: {
          suspiciousActivity: true,
          rateLimited: false,
          domainBlocked: true,
          apiKeyInvalid: false,
        },
      };
    }

    return { success: true };
  } catch {
    return {
      success: false,
      error: "Invalid origin header",
      statusCode: 400,
      securityFlags: {
        suspiciousActivity: true,
        rateLimited: false,
        domainBlocked: true,
        apiKeyInvalid: false,
      },
    };
  }
}

/**
 * Detect suspicious activity
 */
function detectSuspiciousActivity(
  context: SecurityContext,
  chatbot: any
): SecurityValidationResult {
  const suspiciousPatterns: any = [];

  // Check for suspicious user agents
  if (context.userAgent) {
    const suspiciousAgents = ["bot", "crawler", "spider", "scraper"];
    if (
      suspiciousAgents.some((agent) =>
        context.userAgent!.toLowerCase().includes(agent)
      )
    ) {
      suspiciousPatterns.push("suspicious_user_agent");
    }
  }

  // Check for missing standard headers
  if (!context.userAgent || !context.origin) {
    suspiciousPatterns.push("missing_headers");
  }

  // For now, just log suspicious activity but don't block
  if (suspiciousPatterns.length > 0) {
    console.warn(
      `Suspicious activity detected for chatbot ${context.chatbotId}:`,
      {
        patterns: suspiciousPatterns,
        context: {
          ip: context.hashedIP,
          userAgent: context.userAgent,
          origin: context.origin,
        },
      }
    );
  }

  return { success: true };
}

/**
 * Validate tenant status
 */
function validateTenantStatus(chatbot: any): SecurityValidationResult {
  // if (chatbot.tenant.status !== 'active') {
  //   return {
  //     success: false,
  //     error: "Service temporarily unavailable",
  //     statusCode: 503,
  //     securityFlags: {
  //       suspiciousActivity: false,
  //       rateLimited: false,
  //       domainBlocked: false,
  //       apiKeyInvalid: false,
  //     }
  //   };
  // }

  return { success: true };
}

/**
 * Validate private chatbot access based on user authentication
 */
async function validatePrivateChatbotAccess(
  chatbot: any,
  context: SecurityContext
): Promise<SecurityValidationResult> {
  // Public chatbots are always accessible
  if (chatbot.access === "public") {
    return { success: true };
  }

  // Private chatbots require user authentication
  if (chatbot.access === "private") {
    if (!context.userToken) {
      return {
        success: false,
        error: "Authentication required for private chatbot access",
        statusCode: 401,
        securityFlags: {
          suspiciousActivity: false,
          rateLimited: false,
          domainBlocked: false,
          apiKeyInvalid: false,
        },
      };
    }

    try {
      // Verify user token and get user information
      const userInfo = await verifyUserToken(context.userToken);
      if (!userInfo) {
        return {
          success: false,
          error: "Invalid user authentication token",
          statusCode: 401,
          securityFlags: {
            suspiciousActivity: true,
            rateLimited: false,
            domainBlocked: false,
            apiKeyInvalid: false,
          },
        };
      }

      // Check if user has access through company membership
      if (!chatbot.companyIds || chatbot.companyIds.length === 0) {
        return {
          success: false,
          error: "Private chatbot has no authorized companies configured",
          statusCode: 403,
          securityFlags: {
            suspiciousActivity: false,
            rateLimited: false,
            domainBlocked: false,
            apiKeyInvalid: false,
          },
        };
      }

      // Check user's company memberships
      const userCompanyMemberships = await db.companyMember.findMany({
        where: {
          userId: userInfo.userId,
          companyId: { in: chatbot.companyIds },
          status: "ACTIVE",
        },
      });

      if (userCompanyMemberships.length === 0) {
        return {
          success: false,
          error:
            "You do not have access to this private chatbot. Contact your administrator to be added to an authorized company.",
          statusCode: 403,
          securityFlags: {
            suspiciousActivity: false,
            rateLimited: false,
            domainBlocked: false,
            apiKeyInvalid: false,
          },
        };
      }

      return { success: true, user: userInfo };
    } catch (error) {
      console.error("Error validating private chatbot access:", error);
      return {
        success: false,
        error: "Failed to validate chatbot access",
        statusCode: 500,
        securityFlags: {
          suspiciousActivity: false,
          rateLimited: false,
          domainBlocked: false,
          apiKeyInvalid: false,
        },
      };
    }
  }

  return { success: true };
}

/**
 * Verify user token and return user information
 */
async function verifyUserToken(
  token: string
): Promise<{ userId: string; email: string } | null> {
  try {
    // For now, we'll use a simple JWT-like approach
    // In production, you might want to use proper JWT verification
    const decoded = Buffer.from(token, "base64").toString("utf-8");
    const userInfo = JSON.parse(decoded);

    const emailHash = crypto
      .createHash("sha256")
      .update(userInfo.email)
      .digest("hex");
    // Verify the user exists and is active
    const user = await db.user.findFirst({
      where: {
        id: userInfo.userId,
        emailHash,
      },
      select: {
        id: true,
        email: true,
      },
    });

    return user ? { userId: user.id, email: user.email } : null;
  } catch (error) {
    console.error("Error verifying user token:", error);
    return null;
  }
}
