import { NextResponse } from "next/server";
import { getToken } from "next-auth/jwt";
import db from "@/lib/shared-db";
import <PERSON><PERSON> from "stripe";

// Initialize Stripe with the secret key
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY as string, {
  apiVersion: "2023-10-16" as any,
});

// Helper function to find a product by name
async function findProductByName(name: string): Promise<Stripe.Product | null> {
  try {
    const products = await stripe.products.list({
      active: true,
      limit: 100,
    });

    const product = products.data.find((p) => p.name === name);
    return product || null;
  } catch (error) {
    console.error(`Error finding product by name ${name}:`, error);
    return null;
  }
}

// Helper function to find a price for a product
async function findPriceForProduct(
  productId: string
): Promise<Stripe.Price | null> {
  try {
    const prices = await stripe.prices.list({
      product: productId,
      active: true,
      limit: 1,
    });

    return prices.data.length > 0 ? prices.data[0] : null;
  } catch (error) {
    console.error(`Error finding price for product ${productId}:`, error);
    return null;
  }
}

// Helper function to create a subscription for a specific component
async function createComponentSubscription(
  customerId: string,
  productName: string,
  productDescription: string,
  priceAmount: number,
  quantity: number = 1,
  metadata: Record<string, string> = {}
): Promise<string> {
  try {
    // Create or find the product
    let product = await findProductByName(productName);
    if (!product) {
      product = await stripe.products.create({
        name: productName,
        description: productDescription,
        active: true,
      });
    }

    // Create or find the price
    let price = await findPriceForProduct(product.id);
    if (!price) {
      price = await stripe.prices.create({
        product: product.id,
        currency: "chf",
        unit_amount: Math.round(priceAmount * 100), // Convert to cents
        recurring: { interval: "month" },
        active: true,
      });
    }

    // Create the subscription
    const subscription = await stripe.subscriptions.create({
      customer: customerId,
      items: [
        {
          price: price.id,
          quantity,
        },
      ],
      metadata,
    });

    return subscription.id;
  } catch (error) {
    console.error(`Error creating component subscription:`, error);
    throw error;
  }
}

export async function POST(request: Request) {
  try {
    const token = await getToken({ req: request as any });
    if (!token) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const {
      tenantId,
      planId,
      additionalUsers,
      additionalStorageGB,
      updateType,
    } = body;

    // Validate required fields
    if (!tenantId) {
      return NextResponse.json(
        { error: "Tenant ID is required" },
        { status: 400 }
      );
    }

    // Verify user has admin access to this tenant
    const membership = await db.membership.findFirst({
      where: {
        userId: token.sub,
        tenantId,
        role: { in: ["ADMIN", "OWNER"] },
      },
    });

    if (!membership) {
      return NextResponse.json(
        { error: "You don't have admin permissions for this tenant" },
        { status: 403 }
      );
    }

    // Get the active subscription
    const subscription = await db.subscription.findFirst({
      where: {
        tenantId,
        isActive: true,
      },
      include: {
        plan: true,
      },
    });

    if (!subscription) {
      return NextResponse.json(
        { error: "No active subscription found" },
        { status: 404 }
      );
    }

    // Get the tenant's Stripe customer ID
    if (!subscription.stripeCustomerId) {
      return NextResponse.json(
        { error: "Subscription is not linked to Stripe" },
        { status: 400 }
      );
    }

    // Get the current plan
    const currentPlan = subscription.plan;
    const newPlan =
      planId && planId !== subscription.planId
        ? await db.plan.findUnique({ where: { id: planId } })
        : currentPlan;

    if (planId && !newPlan) {
      return NextResponse.json({ error: "Invalid plan ID" }, { status: 400 });
    }

    // Track which subscriptions were updated
    const updatedSubscriptions: Record<string, any> = {};

    // Handle different update types
    const updateTypeValue = updateType || "all";
    console.log(`Update type: ${updateTypeValue}`);

    // Update base plan subscription if needed
    if (
      (updateTypeValue === "all" || updateTypeValue === "plan") &&
      planId &&
      planId !== subscription.planId
    ) {
      try {
        // Cancel the existing plan subscription if it exists
        if (subscription.stripePlanSubscriptionId) {
          await stripe.subscriptions.update(
            subscription.stripePlanSubscriptionId,
            {
              cancel_at_period_end: true,
              proration_behavior: "always_invoice",
            }
          );
          console.log(
            `Scheduled existing plan subscription ${subscription.stripePlanSubscriptionId} for cancellation`
          );
        }

        // Create a new subscription for the base plan
        const planSubscriptionId = await createComponentSubscription(
          subscription.stripeCustomerId,
          `${newPlan.name} Plan`,
          `Base plan with ${newPlan.includedUsers} users and ${newPlan.vectorStoreGB} GB storage`,
          newPlan.price,
          1,
          { type: "base_plan", planId, tenantId }
        );

        updatedSubscriptions.stripePlanSubscriptionId = planSubscriptionId;
        console.log(`Created new plan subscription: ${planSubscriptionId}`);
      } catch (error) {
        console.error("Error updating plan subscription:", error);
        return NextResponse.json(
          { error: "Failed to update plan subscription" },
          { status: 500 }
        );
      }
    }

    // Update additional users subscription if needed
    if (
      (updateTypeValue === "all" || updateTypeValue === "users") &&
      additionalUsers !== undefined &&
      additionalUsers !== subscription.additionalUsers
    ) {
      try {
        // Check if we need to create a new subscription or update an existing one
        if (subscription.stripeUsersSubscriptionId) {
          // If users are reduced to 0, cancel the subscription
          if (additionalUsers === 0) {
            await stripe.subscriptions.update(
              subscription.stripeUsersSubscriptionId,
              {
                cancel_at_period_end: true,
                proration_behavior: "always_invoice",
              }
            );
            console.log(
              `Scheduled users subscription ${subscription.stripeUsersSubscriptionId} for cancellation`
            );
            updatedSubscriptions.stripeUsersSubscriptionId = null as any;
          } else {
            // Update the existing subscription
            await stripe.subscriptions.update(
              subscription.stripeUsersSubscriptionId,
              {
                items: [
                  {
                    id: (
                      await stripe.subscriptions.retrieve(
                        subscription.stripeUsersSubscriptionId
                      )
                    ).items.data[0].id,
                    quantity: additionalUsers,
                  },
                ],
                proration_behavior: "always_invoice",
              }
            );
            console.log(
              `Updated users subscription ${subscription.stripeUsersSubscriptionId} with quantity ${additionalUsers}`
            );
          }
        } else if (additionalUsers > 0) {
          // Create a new subscription for additional users
          const usersSubscriptionId = await createComponentSubscription(
            subscription.stripeCustomerId,
            "Additional Users",
            `Additional users beyond the ${newPlan.includedUsers} included in your plan`,
            newPlan.additionalUserFee,
            additionalUsers,
            { type: "additional_users", tenantId }
          );

          updatedSubscriptions.stripeUsersSubscriptionId = usersSubscriptionId;
          console.log(`Created new users subscription: ${usersSubscriptionId}`);
        }

        // Update the additionalUsers count in our database
        updatedSubscriptions.additionalUsers = additionalUsers;
      } catch (error) {
        console.error("Error updating users subscription:", error);
        return NextResponse.json(
          { error: "Failed to update users subscription" },
          { status: 500 }
        );
      }
    }

    // Update additional storage subscription if needed
    if (
      (updateTypeValue === "all" || updateTypeValue === "storage") &&
      additionalStorageGB !== undefined &&
      additionalStorageGB !== subscription.additionalStorageGB
    ) {
      try {
        // Determine storage tier and price
        let storagePrice = 0;
        let tierName = "";

        if (additionalStorageGB <= 0) {
          // No additional storage
          storagePrice = 0;
          tierName = "0GB";
        } else if (additionalStorageGB <= 10) {
          storagePrice = 29;
          tierName = "10GB";
        } else if (additionalStorageGB <= 50) {
          storagePrice = 79;
          tierName = "50GB";
        } else {
          storagePrice = 149;
          tierName = "100GB";
        }

        // Check if we need to create a new subscription or update an existing one
        if (subscription.stripeStorageSubscriptionId) {
          // If storage is reduced to 0, cancel the subscription
          if (additionalStorageGB === 0) {
            await stripe.subscriptions.update(
              subscription.stripeStorageSubscriptionId,
              {
                cancel_at_period_end: true,
                proration_behavior: "always_invoice",
              }
            );
            console.log(
              `Scheduled storage subscription ${subscription.stripeStorageSubscriptionId} for cancellation`
            );
            updatedSubscriptions.stripeStorageSubscriptionId = null as any;
          } else {
            // For storage, we need to create a new subscription with the new tier
            // and cancel the old one, since the product is different
            await stripe.subscriptions.update(
              subscription.stripeStorageSubscriptionId,
              {
                cancel_at_period_end: true,
                proration_behavior: "always_invoice",
              }
            );
            console.log(
              `Scheduled old storage subscription ${subscription.stripeStorageSubscriptionId} for cancellation`
            );

            // Create a new subscription for the new storage tier
            const storageSubscriptionId = await createComponentSubscription(
              subscription.stripeCustomerId,
              `Additional Vector Storage (${tierName})`,
              `Additional vector storage beyond the ${newPlan.vectorStoreGB} GB included in your plan`,
              storagePrice,
              1,
              { type: "additional_storage", tier: tierName, tenantId }
            );

            updatedSubscriptions.stripeStorageSubscriptionId =
              storageSubscriptionId;
            console.log(
              `Created new storage subscription: ${storageSubscriptionId}`
            );
          }
        } else if (additionalStorageGB > 0) {
          // Create a new subscription for additional storage
          const storageSubscriptionId = await createComponentSubscription(
            subscription.stripeCustomerId,
            `Additional Vector Storage (${tierName})`,
            `Additional vector storage beyond the ${newPlan.vectorStoreGB} GB included in your plan`,
            storagePrice,
            1,
            { type: "additional_storage", tier: tierName, tenantId }
          );

          updatedSubscriptions.stripeStorageSubscriptionId =
            storageSubscriptionId;
          console.log(
            `Created new storage subscription: ${storageSubscriptionId}`
          );
        }

        // Update the additionalStorageGB count in our database
        updatedSubscriptions.additionalStorageGB = additionalStorageGB;
      } catch (error) {
        console.error("Error updating storage subscription:", error);
        return NextResponse.json(
          { error: "Failed to update storage subscription" },
          { status: 500 }
        );
      }
    }

    // Update the subscription in our database
    const updatedSubscription = await db.subscription.update({
      where: {
        id: subscription.id,
      },
      data: {
        planId: planId || subscription.planId,
        ...updatedSubscriptions,
      },
      include: {
        plan: true,
      },
    });

    console.log(`Updated subscription in database:`, {
      id: updatedSubscription.id,
      planId: updatedSubscription.planId,
      additionalUsers: updatedSubscription.additionalUsers,
      additionalStorageGB: updatedSubscription.additionalStorageGB,
      stripePlanSubscriptionId: updatedSubscription.stripePlanSubscriptionId,
      stripeUsersSubscriptionId: updatedSubscription.stripeUsersSubscriptionId,
      stripeStorageSubscriptionId:
        updatedSubscription.stripeStorageSubscriptionId,
    });

    // Create a Stripe customer portal session to show the updated subscriptions
    const session = await stripe.billingPortal.sessions.create({
      customer: subscription.stripeCustomerId,
      return_url: `${request.headers.get("origin")}/billing`,
    });

    return NextResponse.json({
      subscription: updatedSubscription,
      portalUrl: session.url,
    });
  } catch (error) {
    console.error("Error updating subscription components:", error);
    return NextResponse.json(
      { error: "Failed to update subscription components" },
      { status: 500 }
    );
  }
}
