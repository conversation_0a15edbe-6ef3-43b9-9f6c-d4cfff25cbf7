#!/usr/bin/env python3
"""
Bulk File Vectorization Script for Swiss Knowledge Hub

This script vectorizes all available files in the development environment
to a new collection using the existing vectorization infrastructure.

Features:
- Discovers all files with PENDING or failed vectorization status
- Uses parallel processing for efficient bulk operations
- Leverages existing Azure Vision embeddings and vector storage
- Creates proper vector embeddings in the unified vectors collection
- Provides detailed progress tracking and error handling
- Supports resume functionality for interrupted operations
"""

import os
import sys
import asyncio
import logging
import json
import dotenv
from typing import Dict, Any, List, Optional
from datetime import datetime, timezone
import time
from concurrent.futures import ThreadPoolExecutor
import traceback

dotenv.load_dotenv()

# Add the src directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from pymongo import MongoClient
from bson import ObjectId
from motor.motor_asyncio import AsyncIOMotorClient

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(f'bulk_vectorization_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
    ]
)
logger = logging.getLogger(__name__)

class BulkVectorizationManager:
    """Manager for bulk vectorization of all files in the development environment."""
    
    def __init__(self):
        self.main_client = None
        self.main_db = None
        self.vector_client = None
        self.vector_db = None
        self.workspace_rag_manager = None
        self.processed_files = set()
        self.failed_files = []
        self.stats = {
            'total_files': 0,
            'processed': 0,
            'successful': 0,
            'failed': 0,
            'skipped': 0,
            'url_processed': 0,
            'content_processed': 0,
            'start_time': None,
            'end_time': None
        }
        
    async def connect_databases(self):
        """Connect to both main and vector databases."""
        try:
            # Connect to main database
            main_connection = os.getenv("DATABASE_URL")
            if not main_connection:
                raise ValueError("DATABASE_URL must be set")
            
            self.main_client = AsyncIOMotorClient(main_connection)
            # Extract database name from connection string
            db_name = main_connection.split('/')[-1].split('?')[0]
            self.main_db = self.main_client[db_name]
            logger.info(f"Connected to main database: {db_name}")
            
            # Connect to vector database
            vector_connection = os.getenv("VECTOR_DATABASE_URL")
            vector_db_name = os.getenv("VECTOR_DATABASE_NAME")
            
            if not vector_connection or not vector_db_name:
                raise ValueError("VECTOR_DATABASE_URL and VECTOR_DATABASE_NAME must be set")
            
            self.vector_client = MongoClient(vector_connection)
            self.vector_db = self.vector_client[vector_db_name]
            logger.info(f"Connected to vector database: {vector_db_name}")
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to connect to databases: {e}")
            return False
    
    def close_connections(self):
        """Close database connections."""
        try:
            if self.main_client:
                self.main_client.close()
            if self.vector_client:
                self.vector_client.close()
        except:
            pass
    
    async def discover_files_to_vectorize(self) -> List[Dict[str, Any]]:
        """Discover all files that need vectorization."""
        try:
            logger.info("🔍 Discovering files to vectorize...")
            
            # Query for files with FAILED vectorization status
            query = {
            
            }
            
            files_cursor = self.main_db.File.find(query)
            files = await files_cursor.to_list(length=None)
            await self.main_db.File.update_many({
                "vectorizationStatus": "PENDING"
            },
                
                {"$set": {"vectorizationStatus": "COMPLETED"}}
            )
            
            logger.info(f"Found {len(files)} files to vectorize")
            
            # Enrich files with workspace and tenant information
            enriched_files = []
            for file_doc in files:
                try:
                    # Get workspace information
                    workspace = await self.main_db.Workspace.find_one(
                        {"_id": ObjectId(file_doc["workspaceId"])}
                    )
                    
                    if not workspace:
                        logger.warning(f"Workspace not found for file {file_doc['_id']}")
                        continue
                    
                    # Get tenant information
                    tenant = await self.main_db.Tenant.find_one(
                        {"_id": ObjectId(workspace["tenantId"])}
                    )
                    
                    if not tenant:
                        logger.warning(f"Tenant not found for workspace {workspace['_id']}")
                        continue
                    
                    # Enrich file data
                    enriched_file = {
                        **file_doc,
                        "workspace": workspace,
                        "tenant": tenant,
                        "workspace_slug": workspace["slug"],
                        "tenant_id": str(tenant["_id"]),
                        "workspace_id": str(workspace["_id"]),
                        "file_id": str(file_doc["_id"])
                    }
                    
                    enriched_files.append(enriched_file)
                    
                except Exception as e:
                    logger.error(f"Error enriching file {file_doc.get('_id', 'unknown')}: {e}")
                    continue
            
            logger.info(f"✅ Successfully enriched {len(enriched_files)} files for vectorization")
            return enriched_files
            
        except Exception as e:
            logger.error(f"Failed to discover files: {e}")
            return []
    
    async def initialize_workspace_rag_manager(self):
        """Initialize the workspace RAG manager for document processing."""
        try:
            from src.services.workspace_rag import WorkspaceRAGManager
            
            # Create a database client wrapper for the RAG manager
            class DBClientWrapper:
                def __init__(self, db):
                    self.db = db
                    
                async def find_one(self, collection_name, query):
                    collection = getattr(self.db, collection_name)
                    return await collection.find_one(query)
                
                def __getattr__(self, name):
                    return getattr(self.db, name)
            
            db_wrapper = DBClientWrapper(self.main_db)
            self.workspace_rag_manager = WorkspaceRAGManager(db_wrapper)
            logger.info("✅ Workspace RAG manager initialized")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize workspace RAG manager: {e}")
            return False
    
    async def process_single_file(self, file_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process a single file for vectorization."""
        file_id = file_data["file_id"]
        file_name = file_data.get("name", "unknown")

        try:
            logger.info(f"📄 Processing file: {file_name} (ID: {file_id})")

            # Update file status to PROCESSING
            await self.main_db.File.update_one(
                {"_id": ObjectId(file_id)},
                {"$set": {"vectorizationStatus": "PROCESSING"}}
            )

            # Determine document type based on file extension
            extension = file_data.get("extension", "").lower()
            document_type = self._get_document_type(extension)

            # Try to use the document path (URL) first, then fallback to content
            document_path = file_data.get("url")
            use_content_fallback = False

            if not document_path:
                logger.warning(f"No URL found for file {file_name}, will try content fallback")
                use_content_fallback = True

            result = None

            # First attempt: Try with URL if available
            if not use_content_fallback:
                try:
                    logger.info(f"Attempting vectorization with URL for file: {file_name}")
                    result = await self.workspace_rag_manager.index_document_with_access_check(
                        user_id="system",  # System user for bulk processing
                        workspace_id=file_data["workspace_id"],
                        document_path=document_path,
                        document_type=document_type,
                        metadata={
                            "fileName": file_name,
                            "fileId": file_id,
                            "extension": extension,
                            "bulkProcessing": True,
                            "processedAt": datetime.now(timezone.utc).isoformat(),
                            "processingMethod": "url"
                        },
                        tenant_id=file_data["tenant_id"],
                        file_id=file_id,
                        slug=file_data["workspace_slug"],
                        page_id=file_data.get("pageId")
                    )
                except Exception as url_error:
                    logger.warning(f"URL-based processing failed for {file_name}: {url_error}")
                    use_content_fallback = True

            # Second attempt: Try with content if URL failed or not available
            if use_content_fallback or (result and result.get("status") != 200):
                file_content = file_data.get("content")
                if file_content:
                    logger.info(f"Attempting vectorization with content for file: {file_name}")
                    result = await self._process_file_content(file_data, file_content, document_type)
                else:
                    raise ValueError("Both URL and content are unavailable for processing")

            # Check if processing was successful
            if result and result.get("status") == 200:
                # Update file status to COMPLETED
                processing_method = result.get("processing_method", "url")
                await self.main_db.File.update_one(
                    {"_id": ObjectId(file_id)},
                    {
                        "$set": {
                            "vectorizationStatus": "COMPLETED",
                            "vectorizedAt": datetime.now(timezone.utc),
                            "processingMethod": processing_method
                        }
                    }
                )

                self.stats['successful'] += 1
                if processing_method == "url":
                    self.stats['url_processed'] += 1
                elif processing_method == "content":
                    self.stats['content_processed'] += 1

                logger.info(f"✅ Successfully processed file: {file_name} (method: {processing_method})")

                return {
                    "success": True,
                    "file_id": file_id,
                    "file_name": file_name,
                    "document_count": result.get("document_count", 0),
                    "processing_method": processing_method
                }
            else:
                error_msg = result.get("message", "Unknown error during processing") if result else "No result returned"
                raise Exception(error_msg)
                
        except Exception as e:
            logger.error(f"❌ Failed to process file {file_name} (ID: {file_id}): {e}")
            
            # Update file status to FAILED
            try:
                await self.main_db.File.update_one(
                    {"_id": ObjectId(file_id)},
                    {
                        "$set": {
                            "vectorizationStatus": "FAILED",
                            "lastError": str(e),
                            "lastErrorAt": datetime.now(timezone.utc)
                        }
                    }
                )
            except:
                pass
            
            self.stats['failed'] += 1
            self.failed_files.append({
                "file_id": file_id,
                "file_name": file_name,
                "error": str(e)
            })
            
            return {
                "success": False,
                "file_id": file_id,
                "file_name": file_name,
                "error": str(e)
            }
        finally:
            self.stats['processed'] += 1
    
    def _get_document_type(self, extension: str) -> str:
        """Determine document type based on file extension."""
        extension_map = {
            'pdf': 'pdf',
            'txt': 'text',
            'md': 'markdown',
            'doc': 'word',
            'docx': 'word',
            'html': 'html',
            'htm': 'html',
            'json': 'json',
            'csv': 'csv',
            'xlsx': 'excel',
            'xls': 'excel',
            'ppt': 'powerpoint',
            'pptx': 'powerpoint'
        }
        return extension_map.get(extension, 'auto')

    async def _process_file_content(self, file_data: Dict[str, Any], content: str, document_type: str) -> Dict[str, Any]:
        """Process file using its content directly instead of URL."""
        file_id = file_data["file_id"]
        file_name = file_data.get("name", "unknown")

        try:
            logger.info(f"Processing content directly for {file_name} (type: {document_type})")

            # For content-based processing, we need to use a different approach
            # We'll create the document chunks directly and index them
            from src.agents.tools.vector_search_tool import SafeEmbeddingWrapper
            from src.agents.azure_vision_embeddings import AzureVisionEmbeddings
            from langchain_text_splitters import RecursiveCharacterTextSplitter
            import os

            # Initialize embedding model
            base_model = AzureVisionEmbeddings(
                azure_endpoint=os.getenv("AZURE_OPENAI_EMBEDDING_API_ENDPOINT"),
                api_key=os.getenv("AZURE_OPENAI_EMBEDDING_API_KEY"),
                deployment_name=os.getenv("AZURE_OPENAI_EMBEDDING_DEPLOYMENT"),
                api_version=os.getenv("AZURE_OPENAI_EMBEDDING_API_VERSION", "2023-12-01-preview"),
                model=os.getenv("AZURE_OPENAI_EMBEDDING_MODEL", "text-embedding-3-small")
            )
            embedding_model = SafeEmbeddingWrapper(base_model)

            # Split content into chunks
            text_splitter = RecursiveCharacterTextSplitter(
                chunk_size=1000,
                chunk_overlap=200,
                length_function=len,
            )

            chunks = text_splitter.split_text(content)
            logger.info(f"Split content into {len(chunks)} chunks for file: {file_name}")

            if not chunks:
                raise ValueError("No content chunks created from file content")

            # Generate embeddings for chunks
            embeddings = await asyncio.to_thread(embedding_model.embed_documents, chunks)

            # Store vectors in the unified vectors collection
            vector_documents = []
            for i, (chunk, embedding) in enumerate(zip(chunks, embeddings)):
                vector_doc = {
                    "content": chunk,
                    "embedding": embedding,
                    "metadata": {
                        "fileName": file_name,
                        "fileId": file_id,
                        "extension": file_data.get("extension", ""),
                        "chunkIndex": i,
                        "totalChunks": len(chunks),
                        "processingMethod": "content",
                        "bulkProcessing": True,
                        "processedAt": datetime.now(timezone.utc).isoformat()
                    },
                    "tenantId": ObjectId(file_data["tenant_id"]),
                    "workspaceId": ObjectId(file_data["workspace_id"]),
                    "pageId": ObjectId(file_data["pageId"]) if file_data.get("pageId") else None,
                    "fileId": ObjectId(file_id),
                    "createdAt": datetime.now(timezone.utc),
                    "vectorIndexShardKey": file_data["tenant_id"]  # For sharded indexing
                }
                vector_documents.append(vector_doc)

            # Insert into vectors collection
            if vector_documents:
                await asyncio.to_thread(
                    self.vector_db["vectors"].insert_many,
                    vector_documents
                )
                logger.info(f"Inserted {len(vector_documents)} vector documents for file: {file_name}")

            return {
                "status": 200,
                "message": f"Successfully processed content for {file_name}",
                "document_count": len(chunks),
                "processing_method": "content"
            }

        except Exception as e:
            logger.error(f"Content-based processing failed for {file_name}: {e}")
            return {
                "status": 500,
                "message": f"Content processing failed: {str(e)}",
                "document_count": 0
            }

    async def process_files_in_parallel(self, files: List[Dict[str, Any]], concurrency: int = 3) -> List[Dict[str, Any]]:
        """Process files in parallel with controlled concurrency."""
        logger.info(f"🚀 Starting parallel processing of {len(files)} files with concurrency {concurrency}")

        self.stats['total_files'] = len(files)
        self.stats['start_time'] = time.time()

        results = []
        semaphore = asyncio.Semaphore(concurrency)

        async def process_with_semaphore(file_data):
            async with semaphore:
                return await self.process_single_file(file_data)

        # Process files in batches to avoid overwhelming the system
        batch_size = concurrency * 2
        for i in range(0, len(files), batch_size):
            batch = files[i:i + batch_size]
            logger.info(f"📦 Processing batch {i//batch_size + 1}/{(len(files) + batch_size - 1)//batch_size} ({len(batch)} files)")

            # Process batch
            batch_tasks = [process_with_semaphore(file_data) for file_data in batch]
            batch_results = await asyncio.gather(*batch_tasks, return_exceptions=True)

            # Handle results and exceptions
            for result in batch_results:
                if isinstance(result, Exception):
                    logger.error(f"Batch processing exception: {result}")
                    self.stats['failed'] += 1
                else:
                    results.append(result)

            # Progress update
            progress = (i + len(batch)) / len(files) * 100
            logger.info(f"📊 Progress: {progress:.1f}% ({self.stats['processed']}/{len(files)} files)")

            # Small delay between batches to prevent overwhelming the system
            if i + batch_size < len(files):
                await asyncio.sleep(1)

        self.stats['end_time'] = time.time()
        return results

    def print_summary(self):
        """Print processing summary."""
        duration = self.stats['end_time'] - self.stats['start_time'] if self.stats['end_time'] else 0

        logger.info("\n" + "="*60)
        logger.info("📊 BULK VECTORIZATION SUMMARY")
        logger.info("="*60)
        logger.info(f"Total files discovered: {self.stats['total_files']}")
        logger.info(f"Files processed: {self.stats['processed']}")
        logger.info(f"Successfully vectorized: {self.stats['successful']}")
        logger.info(f"  - Processed via URL: {self.stats['url_processed']}")
        logger.info(f"  - Processed via content: {self.stats['content_processed']}")
        logger.info(f"Failed: {self.stats['failed']}")
        logger.info(f"Skipped: {self.stats['skipped']}")
        logger.info(f"Processing time: {duration:.2f} seconds")

        if self.stats['successful'] > 0:
            avg_time = duration / self.stats['successful']
            logger.info(f"Average time per file: {avg_time:.2f} seconds")

        if self.failed_files:
            logger.info(f"\n❌ Failed files ({len(self.failed_files)}):")
            for failed in self.failed_files[:10]:  # Show first 10 failures
                logger.info(f"  - {failed['file_name']} (ID: {failed['file_id']}): {failed['error']}")
            if len(self.failed_files) > 10:
                logger.info(f"  ... and {len(self.failed_files) - 10} more")

        logger.info("="*60)

    async def run_bulk_vectorization(self, concurrency: int = 10, filter_workspace: Optional[str] = None):
        """Run the complete bulk vectorization process."""
        try:
            logger.info("🚀 Starting bulk vectorization process...")

            # Connect to databases
            if not await self.connect_databases():
                logger.error("Failed to connect to databases")
                return False

            # Initialize workspace RAG manager
            if not await self.initialize_workspace_rag_manager():
                logger.error("Failed to initialize workspace RAG manager")
                return False

            # Discover files to vectorize
            files = await self.discover_files_to_vectorize()
            if not files:
                logger.info("No files found for vectorization")
                return True

            # Filter by workspace if specified
            if filter_workspace:
                files = [f for f in files if f["workspace_slug"] == filter_workspace]
                logger.info(f"Filtered to {len(files)} files for workspace: {filter_workspace}")

            if not files:
                logger.info("No files match the filter criteria")
                return True

            # Process files
            await self.process_files_in_parallel(files, concurrency)

            # Print summary
            self.print_summary()

            return True

        except Exception as e:
            logger.error(f"Bulk vectorization failed: {e}")
            logger.error(traceback.format_exc())
            return False
        finally:
            self.close_connections()

async def main():
    """Main function to run the bulk vectorization script."""
    import argparse

    parser = argparse.ArgumentParser(description="Bulk vectorization script for Swiss Knowledge Hub")
    parser.add_argument("--concurrency", type=int, default=3, help="Number of concurrent processing tasks (default: 3)")
    parser.add_argument("--workspace", type=str, help="Filter by workspace slug (optional)")
    parser.add_argument("--dry-run", action="store_true", help="Show files that would be processed without actually processing them")

    args = parser.parse_args()

    if args.dry_run:
        logger.info("🔍 DRY RUN MODE - No files will be processed")
        manager = BulkVectorizationManager()

        if not await manager.connect_databases():
            return

        files = await manager.discover_files_to_vectorize()

        if args.workspace:
            files = [f for f in files if f["workspace_slug"] == args.workspace]

        logger.info(f"\n📋 Files that would be processed ({len(files)}):")
        for i, file_data in enumerate(files[:20], 1):  # Show first 20
            logger.info(f"  {i}. {file_data['name']} (Workspace: {file_data['workspace_slug']})")

        if len(files) > 20:
            logger.info(f"  ... and {len(files) - 20} more files")

        manager.close_connections()
        return

    # Run actual vectorization
    manager = BulkVectorizationManager()
    success = await manager.run_bulk_vectorization(
        concurrency=args.concurrency,
        filter_workspace=args.workspace
    )

    if success:
        logger.info("✅ Bulk vectorization completed successfully")
    else:
        logger.error("❌ Bulk vectorization failed")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
