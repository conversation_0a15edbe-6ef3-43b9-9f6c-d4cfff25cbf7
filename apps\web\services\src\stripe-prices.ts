import axios from "axios";
import { apiUrl } from "..";

export interface StripePrice {
  id: string;
  unitAmount: number | null;
  currency: string;
  interval: string | null;
  productId: string;
  productName: string | null;
}

export interface PlanPrice {
  planId: string;
  planName: string;
  planType: string;
  includedUsers: number;
  vectorStoreGB: number;
  displayPrice: number | null;
  monthlyPrice: StripePrice | null;
  yearlyPrice: StripePrice | null;
  userMonthlyPrice: StripePrice | null;
  userYearlyPrice: StripePrice | null;
}

export interface StorageTierPrice {
  tierId: string;
  tierName: string;
  sizeGB: number;
  displayPrice: number;
  monthlyPrice: StripePrice | null;
  yearlyPrice: StripePrice | null;
}

export interface StripePricesResponse {
  plans: PlanPrice[];
  storageTiers: StorageTierPrice[];
}

// Fetch all prices from Stripe
export const getStripePrices = async (): Promise<StripePricesResponse> => {
  try {
    const response = await axios.get<StripePricesResponse>(`${apiUrl}/stripe/prices`);
    return response.data;
  } catch (error) {
    console.error("Error fetching Stripe prices:", error);
    throw error;
  }
};
