"use client";

import React from "react";
import { useRouter, usePathname } from "next/navigation";
import { UsageChart } from "@/components/dashboard/usage-chart";
import { TimePeriodFilter } from "./time-period-filter";

interface StorageUsageData {
  name: string;
  value: number;
}

interface UserStorageChartProps {
  storageUsageData: StorageUsageData[];
  initialPeriod?: string;
}

export function UserStorageChart({
  storageUsageData,
  initialPeriod = "12months",
}: UserStorageChartProps) {
  const router = useRouter();
  const pathname = usePathname();
  const [period, setPeriod] = React.useState(initialPeriod);
  const [isClient, setIsClient] = React.useState(false);
  
  // This ensures hydration mismatch is avoided
  React.useEffect(() => {
    setIsClient(true);
  }, []);

  // Handle period change
  const handlePeriodChange = (newPeriod: string) => {
    setPeriod(newPeriod);
    // Update URL with the new period parameter
    router.push(`${pathname}?period=${newPeriod}`);
  };

  if (!isClient) {
    return null;
  }

  return (
    <div className="space-y-4">
      <TimePeriodFilter period={period} onChange={handlePeriodChange} />
      <UsageChart
        title="Storage Usage"
        data={storageUsageData}
        dataKey="value"
        valueFormatter={(value) => `${value} MB`}
        description={`Storage usage over time (${period === "lifetime" ? "All time" : period === "7days" ? "Last week" : period === "30days" ? "Last month" : period === "90days" ? "Last quarter" : "Last year"})`}
        color="green"
      />
    </div>
  );
}