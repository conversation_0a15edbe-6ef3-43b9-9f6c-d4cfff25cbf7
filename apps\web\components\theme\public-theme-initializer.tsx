"use client";

import { useEffect, useState } from "react";
import { useTheme } from "next-themes";
import { generateThemeVariables } from "@/services/theme-config";
import { usePathname } from "next/navigation";
import { getCachedThemeConfig, updateThemeCache } from "@/lib/theme-cache";

interface ThemeConfig {
  id?: string;
  tenantId?: string;
  brandName?: string;
  logoUrl?: string;
  faviconUrl?: string;
  fullAppLogoUrl?: string;

  // Light theme color fields
  lightPrimaryColor?: string;
  lightSecondaryColor?: string;
  lightAccentColor?: string;
  lightNavigationBackgroundColor?: string;
  lightContentBackgroundColor?: string;
  lightForegroundColor?: string;

  // Dark theme color fields
  darkPrimaryColor?: string;
  darkSecondaryColor?: string;
  darkAccentColor?: string;
  darkNavigationBackgroundColor?: string;
  darkContentBackgroundColor?: string;
  darkForegroundColor?: string;

  themePreset?: "light" | "dark";
  version?: number;
  updatedAt?: string;
}

// Function to apply comprehensive theme overrides for public pages
function applyPublicPageOverrides() {
  // Create a style element for public page overrides
  let styleElement = document.getElementById('public-theme-overrides') as HTMLStyleElement;
  if (!styleElement) {
    styleElement = document.createElement('style');
    styleElement.id = 'public-theme-overrides';
    document.head.appendChild(styleElement);
  }

  // Generate comprehensive CSS overrides with higher specificity
  const overrides: string[] = [];

  // Always apply base overrides to ensure theme variables are used
  overrides.push(`
    /* Base theme variable overrides for public pages */
    .min-h-screen.bg-gray-50.dark\\:bg-gray-900,
    .min-h-screen.bg-gray-50,
    body.bg-gray-50.dark\\:bg-gray-900 {
      background-color: hsl(var(--background)) !important;
    }

    /* Content area background overrides */
    .bg-white.dark\\:bg-gray-800.rounded-lg,
    .bg-white.dark\\:bg-gray-800.border,
    .bg-white.dark\\:bg-gray-800.p-6,
    .bg-white.dark\\:bg-gray-800.p-8,
    .bg-white.rounded-lg.border,
    .bg-white.dark\\:bg-gray-800 {
      background-color: hsl(var(--card)) !important;
    }

    /* Navigation/Header background overrides */
    .bg-white.dark\\:bg-gray-800.border-b,
    .bg-white.border-b {
      background-color: hsl(var(--sidebar-background)) !important;
    }

    /* Text color overrides */
    .text-gray-900.dark\\:text-white,
    .text-3xl.font-bold.text-gray-900.dark\\:text-white,
    .text-xl.font-semibold.text-gray-900.dark\\:text-white,
    .text-2xl.font-bold.text-gray-900.dark\\:text-white,
    h1.text-gray-900.dark\\:text-white,
    h2.text-gray-900.dark\\:text-white,
    h3.text-gray-900.dark\\:text-white {
      color: hsl(var(--foreground)) !important;
    }

    /* Secondary text color overrides */
    .text-lg.text-gray-600.dark\\:text-gray-400,
    .text-gray-600.dark\\:text-gray-400,
    .text-sm.text-gray-600.dark\\:text-gray-400,
    p.text-gray-600.dark\\:text-gray-400 {
      color: hsl(var(--muted-foreground)) !important;
    }

    /* Muted text color overrides */
    .text-sm.text-gray-500.dark\\:text-gray-400,
    .text-gray-500.dark\\:text-gray-400,
    .text-xs.text-gray-500.dark\\:text-gray-400 {
      color: hsl(var(--muted-foreground)) !important;
    }

    /* Primary color overrides */
    .bg-blue-50.dark\\:bg-blue-900\\/20,
    .bg-blue-100.text-blue-800.dark\\:bg-blue-900.dark\\:text-blue-200 {
      background-color: hsl(var(--primary) / 0.1) !important;
      color: hsl(var(--primary)) !important;
    }

    .text-blue-700.dark\\:text-blue-300,
    .text-blue-600.hover\\:text-blue-700 {
      color: hsl(var(--primary)) !important;
    }

    .border-blue-200.dark\\:border-blue-800,
    .border.border-blue-200.dark\\:border-blue-800 {
      border-color: hsl(var(--primary) / 0.3) !important;
    }

    /* Button overrides */
    .bg-blue-600.hover\\:bg-blue-700.text-white,
    .bg-blue-600.hover\\:bg-blue-700 {
      background-color: hsl(var(--primary)) !important;
      color: hsl(var(--primary-foreground)) !important;
    }

    .bg-blue-600.hover\\:bg-blue-700:hover {
      background-color: hsl(var(--primary) / 0.9) !important;
    }

    /* Border overrides */
    .border-gray-200.dark\\:border-gray-700,
    .border.border-gray-200.dark\\:border-gray-700 {
      border-color: hsl(var(--border)) !important;
    }

    /* Input field overrides */
    .border-gray-300.dark\\:border-gray-600.bg-white.dark\\:bg-gray-700,
    input.border-gray-300.dark\\:border-gray-600 {
      background-color: hsl(var(--input)) !important;
      border-color: hsl(var(--border)) !important;
      color: hsl(var(--foreground)) !important;
    }

    /* Placeholder text overrides */
    .placeholder-gray-500.dark\\:placeholder-gray-400::placeholder,
    input::placeholder {
      color: hsl(var(--muted-foreground)) !important;
    }

    /* Badge and tag overrides */
    .bg-gray-100.dark\\:bg-gray-600.text-gray-600.dark\\:text-gray-300,
    .bg-gray-100.text-gray-800.dark\\:bg-gray-900.dark\\:text-gray-200 {
      background-color: hsl(var(--secondary)) !important;
      color: hsl(var(--secondary-foreground)) !important;
    }

    /* Shared Thread View specific overrides */
    .flex.h-screen.bg-gray-50.dark\\:bg-gray-900 {
      background-color: hsl(var(--background)) !important;
    }

    /* Comprehensive text color overrides for all text elements */
    .text-foreground,
    .text-card-foreground,
    .text-popover-foreground,
    h1, h2, h3, h4, h5, h6,
    .font-semibold,
    .font-bold,
    .font-medium,
    label,
    .text-sm.font-medium,
    .text-sm.font-semibold {
      color: hsl(var(--foreground)) !important;
    }

    /* Muted and secondary text comprehensive overrides */
    .text-muted-foreground,
    .text-secondary-foreground,
    .text-sm.text-muted-foreground,
    .text-xs.text-muted-foreground,
    p.text-muted-foreground,
    .text-gray-500,
    .text-gray-600,
    .text-slate-500,
    .text-slate-600,
    .text-xs,
    .text-sm:not(.bg-primary):not(.bg-secondary):not(.bg-destructive),
    .description,
    .helper-text {
      color: hsl(var(--muted-foreground)) !important;
    }

    /* Shared Thread Header overrides */
    .border-b.border-gray-200.dark\\:border-gray-700.bg-white.dark\\:bg-gray-800 {
      background-color: hsl(var(--sidebar-background)) !important;
      border-color: hsl(var(--border)) !important;
    }

    /* Chat Message container overrides */
    .bg-secondary.rounded-lg {
      background-color: hsl(var(--card)) !important;
      color: hsl(var(--card-foreground)) !important;
    }

    /* Message content area overrides */
    .my-2.sm\\:my-4.p-2.sm\\:p-4.flex.h-fit.min-h-full.flex-col.gap-3.sm\\:gap-4.overflow-y-auto {
      background-color: transparent !important;
    }

    /* Shared thread messages container */
    .flex-1.overflow-y-auto.px-4.py-6 {
      background-color: hsl(var(--background)) !important;
    }

    /* Individual message wrapper overrides */
    .my-2.sm\\:my-4.p-2.sm\\:p-4.flex.h-fit.min-h-full.flex-col.gap-3.sm\\:gap-4.overflow-y-auto,
    .my-2.p-2.flex.h-fit.min-h-full.flex-col.gap-3.overflow-y-auto {
      background-color: transparent !important;
    }

    /* Chat message content background overrides */
    .bg-secondary.rounded-lg.relative.overflow-hidden {
      background-color: hsl(var(--card)) !important;
      color: hsl(var(--card-foreground)) !important;
    }

    /* Message content area with max-width */
    .w-auto.px-4.text-xs.sm\\:text-sm.data-\\[role\\=assistant\\]\\:self-start.max-w-\\[75vw\\].sm\\:max-w-\\[85\\%\\].overflow-hidden {
      background-color: transparent !important;
    }

    /* User message containers */
    .w-auto.px-4.text-xs.sm\\:text-sm.max-w-\\[75vw\\].sm\\:max-w-\\[85\\%\\].overflow-hidden {
      background-color: transparent !important;
    }

    /* Markdown content styling overrides for shared threads */
    .markdown {
      color: hsl(var(--foreground)) !important;
    }

    .markdown th {
      background-color: hsl(var(--muted)) !important;
      color: hsl(var(--muted-foreground)) !important;
    }

    .markdown td {
      border-color: hsl(var(--border)) !important;
      color: hsl(var(--foreground)) !important;
    }

    .markdown pre {
      background-color: hsl(var(--muted)) !important;
      border-color: hsl(var(--border)) !important;
      color: hsl(var(--foreground)) !important;
    }

    .markdown code {
      background-color: hsl(var(--muted)) !important;
      color: hsl(var(--foreground)) !important;
    }

    .markdown tbody tr:nth-child(even) {
      background-color: hsl(var(--muted) / 0.3) !important;
    }

    /* Accent color overrides for different badge types */
    .bg-green-100.text-green-800.dark\\:bg-green-900.dark\\:text-green-200 {
      background-color: hsl(var(--accent) / 0.1) !important;
      color: hsl(var(--accent)) !important;
    }

    .bg-red-100.text-red-800.dark\\:bg-red-900.dark\\:text-red-200 {
      background-color: hsl(var(--destructive) / 0.1) !important;
      color: hsl(var(--destructive)) !important;
    }

    .bg-purple-100.text-purple-800.dark\\:bg-purple-900.dark\\:text-purple-200 {
      background-color: hsl(var(--secondary) / 0.1) !important;
      color: hsl(var(--secondary-foreground)) !important;
    }
  `);

  // Apply the comprehensive overrides
  styleElement.textContent = overrides.join('\n');
}

interface PublicThemeInitializerProps {
  initialTenantId?: string | null;
  initialThemeConfig?: ThemeConfig | null;
}

export function PublicThemeInitializer({
  initialTenantId,
  initialThemeConfig
}: PublicThemeInitializerProps) {
  const { setTheme, resolvedTheme } = useTheme();
  const pathname = usePathname();
  const isPartnerConsole = process.env.NEXT_PUBLIC_IS_PARTNER_CONSOLE === "1";

  const [themeConfig, setThemeConfig] = useState<ThemeConfig | null>(initialThemeConfig || null);
  const [tenantId, setTenantId] = useState<string | null>(initialTenantId || null);

  // Function to extract tenant ID from shared thread
  const extractTenantFromSharedThread = async (shareToken: string): Promise<string | null> => {
    try {
      const response = await fetch(`/api/shared/thread/${shareToken}`);
      if (response.ok) {
        const data = await response.json();
        return data.tenant?.id || null;
      }
    } catch (error) {
      console.error("Failed to fetch tenant from shared thread:", error);
    }
    return null;
  };

  // Function to fetch theme config for a tenant (using cached approach)
  const fetchThemeConfig = async (tenantId: string): Promise<ThemeConfig | null> => {
    try {
      console.log("PublicThemeInitializer: Fetching theme config for tenant:", tenantId);

      // Use cached theme config to reduce API calls
      const config = await getCachedThemeConfig(tenantId);

      if (config) {
        console.log("PublicThemeInitializer: Theme config loaded from cache:", config.brandName || "unnamed");
      } else {
        console.log("PublicThemeInitializer: No theme config found for tenant:", tenantId);
      }

      return config;
    } catch (error) {
      console.error("PublicThemeInitializer: Failed to fetch theme config:", error);
      return null;
    }
  };

  // Detect tenant ID from URL if not available from session
  useEffect(() => {
    if (!isPartnerConsole) return;

    const detectTenantId = async () => {
      // If we already have tenant ID and theme config, no need to detect
      if (tenantId && themeConfig) return;

      let detectedTenantId: string | null = null;

      // Check if this is a shared thread URL
      const sharedThreadMatch = pathname?.match(/\/shared\/thread\/([^\/]+)/);
      if (sharedThreadMatch) {
        const shareToken = sharedThreadMatch[1];
        console.log("PublicThemeInitializer: Detecting tenant from shared thread:", shareToken);

        detectedTenantId = await extractTenantFromSharedThread(shareToken);
        if (detectedTenantId) {
          console.log("PublicThemeInitializer: Detected tenant ID from shared thread:", detectedTenantId);
        }
      } else {
        // For other public pages (like changelog, docs), use the test tenant ID
        console.log("PublicThemeInitializer: Using test tenant for public page:", pathname);
        detectedTenantId = "686aa81188c3cd31d8035002";
      }

      if (detectedTenantId) {
        setTenantId(detectedTenantId);

        // Fetch theme config for the detected tenant
        console.log("PublicThemeInitializer: Fetching theme config for tenant:", detectedTenantId);
        const config = await fetchThemeConfig(detectedTenantId);
        if (config) {
          console.log("PublicThemeInitializer: Fetched theme config:", config);
          setThemeConfig(config);
        } else {
          console.log("PublicThemeInitializer: No theme config found for tenant:", detectedTenantId);
        }
      }
    };

    detectTenantId();
  }, [pathname, tenantId, themeConfig, isPartnerConsole]);

  // Apply theme preset (light/dark mode)
  useEffect(() => {
    if (!isPartnerConsole) return;
    if (typeof window === "undefined") return;

    if (themeConfig?.themePreset) {
      console.log("PublicThemeInitializer: Applying theme preset:", themeConfig.themePreset);

      // Always update localStorage and apply theme to ensure consistency
      localStorage.setItem("theme", themeConfig.themePreset);

      // Apply the theme using next-themes
      setTheme(themeConfig.themePreset);

      // Force apply the theme class to the HTML element
      const root = document.documentElement;
      root.classList.remove("light", "dark", "system");
      root.classList.add(themeConfig.themePreset);

      console.log("PublicThemeInitializer: Theme preset applied successfully");
    }
  }, [themeConfig?.themePreset, setTheme, isPartnerConsole]);

  // Apply custom colors from theme configuration
  useEffect(() => {
    if (!isPartnerConsole) return;
    if (typeof window === "undefined") return;

    if (themeConfig && resolvedTheme) {
      console.log("PublicThemeInitializer: Applying custom colors for", resolvedTheme, "theme:", themeConfig);

      // Generate and apply CSS variables for custom colors with current theme mode
      const themeVariables = generateThemeVariables(themeConfig, resolvedTheme as "light" | "dark");
      const root = document.documentElement;

      // Apply color variables to the root element
      Object.entries(themeVariables).forEach(([property, value]) => {
        if (value) {
          root.style.setProperty(property, value);
        } else {
          // Remove the custom property to fall back to default
          root.style.removeProperty(property);
        }
      });

      // Apply theme-specific overrides for public pages with hardcoded classes
      applyPublicPageOverrides();

      console.log("PublicThemeInitializer: Custom colors applied successfully for", resolvedTheme, "theme");
    }
  }, [themeConfig, resolvedTheme, isPartnerConsole]);

  // Listen for real-time theme updates
  useEffect(() => {
    if (!isPartnerConsole) return;

    const handleRealtimeThemeUpdate = (event: CustomEvent) => {
      const { themeConfig: updatedConfig, themePreset, tenantId: updateTenantId } = event.detail;

      console.log("PublicThemeInitializer: Received real-time theme update:", event.detail);

      if (updatedConfig) {
        setThemeConfig(updatedConfig);

        // Update cache if we have tenant ID
        const targetTenantId = updateTenantId || tenantId;
        if (targetTenantId) {
          updateThemeCache(targetTenantId, updatedConfig);
        }

        // Apply theme mode if specified
        if (themePreset) {
          setTheme(themePreset);
        }
      }
    };

    // Listen for real-time theme updates
    window.addEventListener("realtimeThemeUpdate", handleRealtimeThemeUpdate as EventListener);

    return () => {
      window.removeEventListener("realtimeThemeUpdate", handleRealtimeThemeUpdate as EventListener);
    };
  }, [isPartnerConsole, setTheme]);

  return null;
}
