"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON><PERSON>Header,
  Di<PERSON>Title,
  DialogTrigger,
} from "@/components/ui/dialog";
import { outlookDriveService } from "@/services/src/integration/microsoft";
import { Globe, ChevronRight, Loader2 } from "lucide-react";
import { useLanguage } from "@/lib/language-context";
import { getCookie } from "@/utils/cookies";

interface SharePointSiteSelectorProps {
  tenantId: string;
  onSiteSelect: (siteId: string, siteName: string) => void;
  selectedSiteId?: string;
}

interface SiteItem {
  id: string;
  name: string;
  displayName?: string;
  webUrl: string;
}

export function SharePointSiteSelector({
  tenantId,
  onSiteSelect,
  selectedSiteId,
}: SharePointSiteSelectorProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [sites, setSites] = useState<SiteItem[]>([]);
  const [selectedSite, setSelectedSite] = useState<{
    id: string;
    name: string;
  } | null>(null);
  const { t } = useLanguage();
  const userId = getCookie("userId") ?? "";

  // Load sites when dialog opens
  useEffect(() => {
    if (isOpen) {
      loadSites();
    }
  }, [isOpen]);

  const loadSites = async () => {
    setIsLoading(true);
    try {
      const result = await outlookDriveService.listSites({
        tenantId,
        userId,
      });

      if (result.value) {
        setSites(result.value);
      } else {
        setSites([]);
      }
    } catch (error) {
      console.error("Error loading SharePoint sites:", error);
      setSites([]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSiteClick = (site: SiteItem) => {
    setSelectedSite({
      id: site.id,
      name: site.displayName || site.name,
    });
    onSiteSelect(site.id, site.displayName || site.name);
    setIsOpen(false);
  };

  return (
    <div>
      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogTrigger asChild>
          <Button variant="outline" className="w-full justify-start gap-2">
            <Globe className="h-4 w-4" />
            {selectedSite?.name || selectedSiteId
              ? selectedSite?.name ||
                t("integration.selectedSite") ||
                "Selected Site"
              : t("integration.selectSite") || "Select SharePoint Site"}
          </Button>
        </DialogTrigger>
        <DialogContent className="sm:max-w-[525px]">
          <DialogHeader>
            <DialogTitle>
              {t("integration.selectSharePointSite") ||
                "Select SharePoint Site"}
            </DialogTitle>
          </DialogHeader>

          {/* Site list */}
          <div className="max-h-[300px] overflow-y-auto border rounded-md">
            {isLoading ? (
              <div className="flex items-center justify-center p-4">
                <Loader2 className="h-5 w-5 animate-spin" />
              </div>
            ) : sites.length === 0 ? (
              <div className="p-4 text-center text-sm text-muted-foreground">
                {t("integrations.noSitesFound") || "No sites found"}
              </div>
            ) : (
              <div className="divide-y">
                {sites.map((site) => (
                  <button
                    key={site.id}
                    className="flex w-full items-center gap-2 p-2 hover:bg-muted text-left"
                    onClick={() => handleSiteClick(site)}
                  >
                    <Globe className="h-4 w-4" />
                    <span>{site.displayName || site.name}</span>
                  </button>
                ))}
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
