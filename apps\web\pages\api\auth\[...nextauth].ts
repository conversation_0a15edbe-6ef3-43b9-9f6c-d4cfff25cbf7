import { authOptions } from "@/lib/next-auth";
import NextAuth from "next-auth";

export default async (req, res) => {
  if (req.method === "HEAD") {
    return res.status(200).end();
  }
  return await NextAuth(req, res, {
    ...authOptions,
    events: {
      async signOut({ token, session }) {
        res.setHeader("Set-Cookie", "");
        token = {};
        session = { expires: "Thu, 01 Jan 1970 00:00:00 UTC" } as any;
      },
    },
  });
};
