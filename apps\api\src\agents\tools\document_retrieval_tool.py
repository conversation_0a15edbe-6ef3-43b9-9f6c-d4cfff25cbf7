"""
High-Performance Document Retrieval Tool for CopilotKit Agent

This tool provides optimized async document retrieval with intelligent query routing,
batch processing, and relevance scoring for the Swiss Knowledge Hub.
"""

import asyncio
import logging
from typing import List, Dict, Any, Optional, Type
from langchain_core.tools import BaseTool
from langchain_core.callbacks import CallbackManagerForToolRun
from pydantic import BaseModel, Field
from bson import ObjectId
from pymongo import MongoClient
from langchain_community.vectorstores.azure_cosmos_db import (
    AzureCosmosDBVectorSearch,
    CosmosDBSimilarityType,
    CosmosDBVectorSearchType,
)
from langchain_openai import AzureOpenAIEmbeddings
import os
import time

logger = logging.getLogger(__name__)

# Import the shared connection pool from vector_search_tool
from .vector_search_tool import mongo_pool

class SafeEmbeddingWrapper:
    """
    A wrapper around embedding models that ensures proper input validation
    to prevent tokenization issues that cause Azure OpenAI API errors.
    """

    def __init__(self, base_embedding_model):
        self.base_model = base_embedding_model

    def embed_query(self, text):
        logger.debug(f"Embedding query: {text}")
        """Safely embed a query with input validation."""
        if not isinstance(text, str):
            logger.error(f"Expected string for query, got {type(text)}: {text}")
            if isinstance(text, list):
                logger.error("Query appears to be tokenized - this will cause embedding API errors")
                # Check if this looks like token IDs (list of integers)
                if all(isinstance(x, int) for x in text):
                    logger.error(f"Detected token IDs: {text}")
                    raise ValueError(f"Query appears to be tokenized (list of token IDs: {text}) instead of a string. This indicates a bug in the query processing pipeline.")
                raise ValueError("Query appears to be tokenized (list of tokens) instead of a string")
            text = str(text)

        if not text.strip():
            logger.warning("Empty query provided to embedding model")
            # Return a zero vector for empty queries
            return [0.0] * getattr(self.base_model, 'dimension', 1536)

        logger.debug(f"Calling base model embed_query with text: {text[:100]}...")
        return self.base_model.embed_query(text)

    def embed_documents(self, texts):
        logger.debug(f"Embedding documents: {texts}")
        """Safely embed documents with input validation."""
        if not isinstance(texts, list):
            logger.error(f"Expected list of texts, got {type(texts)}: {texts}")
            raise ValueError(f"Expected list of texts, got {type(texts)}")

        validated_texts = []
        for i, text in enumerate(texts):
            if not isinstance(text, str):
                logger.error(f"Text at index {i} is not a string: {type(text)} - {text}")
                if isinstance(text, list):
                    logger.error("Text appears to be tokenized - this will cause embedding API errors")
                    raise ValueError(f"Text at index {i} appears to be tokenized (list of tokens) instead of a string")
                text = str(text)
            validated_texts.append(text)

        return self.base_model.embed_documents(validated_texts)

    def __getattr__(self, name):
        """Delegate other attributes to the base model."""
        return getattr(self.base_model, name)

class DocumentRetrievalInput(BaseModel):
    """Input schema for document retrieval tool."""
    query: str = Field(description="The search query to find relevant documents")
    workspace_ids: List[str] = Field(description="List of workspace IDs to search in")
    top_k: int = Field(default=8, description="Number of top documents to retrieve")
    min_relevance_score: float = Field(default=0.6, description="Minimum relevance score threshold")
    is_follow_up: bool = Field(default=False, description="Whether this is a follow-up question that doesn't need new document retrieval")

class DocumentRetrievalTool(BaseTool):
    """
    High-performance async document retrieval tool that searches across multiple workspaces
    with intelligent query routing and optimized vector search.
    """

    name: str = "document_retrieval_tool"
    description: str = """
    Retrieve relevant documents from the knowledge base across multiple workspaces.
    Use this tool when users ask questions that require information from uploaded documents,
    PDFs, or other content in their workspaces. This tool automatically searches across
    all accessible workspaces and returns the most relevant documents with citations.
    """
    args_schema: Type[BaseModel] = DocumentRetrievalInput

    # Pydantic v2 requires explicit field definitions
    db_client: Any = Field(default=None, exclude=True)
    embedding_model: Any = Field(default=None, exclude=True)
    vector_stores: Dict[str, Any] = Field(default_factory=dict, exclude=True)

    def __init__(self, db_client=None, **kwargs):
        super().__init__(**kwargs)
        self.db_client = db_client
        self.embedding_model = None
        self.vector_stores = {}  # Cache for vector stores
        self._initialize_embedding_model()
    
    def _initialize_embedding_model(self):
        """Initialize the embedding model for vector search."""
        try:
            # Use AzureOpenAIEmbeddings for Azure Cosmos DB compatibility
            from langchain_openai import AzureOpenAIEmbeddings

            base_model = AzureOpenAIEmbeddings(
                azure_endpoint=os.getenv("AZURE_OPENAI_EMBEDDING_API_ENDPOINT"),
                api_key=os.getenv("AZURE_OPENAI_EMBEDDING_API_KEY"),
                azure_deployment=os.getenv("AZURE_OPENAI_EMBEDDING_DEPLOYMENT"),
                api_version=os.getenv("AZURE_OPENAI_EMBEDDING_API_VERSION", "2023-12-01-preview"),
                model=os.getenv("AZURE_OPENAI_EMBEDDING_MODEL", "text-embedding-ada-002"),
                chunk_size=1
            )

            # Wrap it with our validation wrapper
            self.embedding_model = SafeEmbeddingWrapper(base_model)
            logger.info("Azure Cosmos DB document retrieval embedding model initialized successfully with safety wrapper")
            return self.embedding_model
        except Exception as e:
            logger.error(f"Failed to initialize embedding model: {e}")
            raise

    async def _enhance_document_metadata(self, original_metadata: Dict[str, Any]) -> Dict[str, Any]:
        """Enhance document metadata by fetching complete file information from database."""
        enhanced_metadata = original_metadata.copy()

        # Try to fetch complete file information from database if fileId is available
        file_id = enhanced_metadata.get("fileId")
        if file_id and self.db_client:
            try:
                from bson import ObjectId
                # Fetch file information from database
                file_info = await self.db_client.File.find_one({"_id": ObjectId(file_id)})
                if file_info:
                    enhanced_metadata.update({
                        "file_name": file_info.get("name", "Unknown"),
                        "file_type": file_info.get("type", "Unknown"),
                        "file_extension": file_info.get("extension", ""),
                        "file_size": file_info.get("size", ""),
                        "file_url": file_info.get("url", ""),
                        "created_at": file_info.get("createdAt", ""),
                        "updated_at": file_info.get("updatedAt", "")
                    })

                    # Also fetch workspace information if available
                    workspace_id = file_info.get("workspaceId")
                    if workspace_id:
                        workspace_info = await self.db_client.Workspace.find_one({"_id": ObjectId(workspace_id)})
                        if workspace_info:
                            enhanced_metadata["workspace"] = {
                                "id": str(workspace_id),
                                "name": workspace_info.get("name", ""),
                                "slug": workspace_info.get("slug", "")
                            }

                    logger.debug(f"Enhanced metadata with file info: {enhanced_metadata.get('file_name', 'Unknown')}")
                else:
                    logger.warning(f"File not found in database: {file_id}")
            except Exception as e:
                logger.warning(f"Failed to fetch file metadata for {file_id}: {e}")

        # Fallback to existing metadata fields if database lookup failed
        if "file_name" not in enhanced_metadata:
            # Use existing metadata fields that should be present in vector embeddings
            source_name = enhanced_metadata.get("source", "")
            if source_name:
                enhanced_metadata.update({
                    "file_name": source_name.split("/")[-1] if "/" in source_name else source_name,
                    "source": source_name
                })
            else:
                enhanced_metadata["file_name"] = "Unknown Document"

        # Ensure workspace information is available
        if "workspace" not in enhanced_metadata:
            enhanced_metadata["workspace"] = {
                "id": enhanced_metadata.get("workspace_id", ""),
                "slug": enhanced_metadata.get("slug", ""),
                "name": enhanced_metadata.get("workspaceName", "")
            }

        return enhanced_metadata



    def _normalize_query_for_search(self, query: str) -> str:
        """
        Conservative query normalization for better semantic search.

        This method applies minimal normalization to preserve semantic meaning
        while improving search consistency.
        """
        if not query or not isinstance(query, str):
            return query

        # Preserve the original query structure for better semantic matching
        # Only apply minimal normalization to avoid losing important context
        normalized = query.strip()

        # Remove extra whitespace but preserve case for proper nouns and technical terms
        normalized = ' '.join(normalized.split())

        # Only convert to lowercase if the query is all uppercase (likely shouting)
        if normalized.isupper():
            normalized = normalized.lower()

        # Don't remove stop words as they can be important for semantic context
        # Modern embedding models handle stop words appropriately

        logger.debug(f"Conservative query normalization: '{query}' -> '{normalized}'")
        return normalized

    async def _get_vector_store(self, workspace_id: str) -> Optional[AzureCosmosDBVectorSearch]:
        """Get or create a vector store for a specific workspace."""
        if workspace_id in self.vector_stores:
            return self.vector_stores[workspace_id]
        
        try:
            # First try to get workspace-specific vector configuration
            vector_config = await self.db_client.VectorDBSettings.find_one({
                "workspaceId": ObjectId(workspace_id)
            })

            # If no workspace-specific config, try to get tenant-level config
            if not vector_config:
                # Get workspace to find tenant
                workspace = await self.db_client.Workspace.find_one({
                    "_id": ObjectId(workspace_id)
                })

                if workspace:
                    tenant_id = workspace.get("tenantId")
                    if tenant_id:
                        # Look for any vector config for this tenant
                        vector_config = await self.db_client.VectorDBSettings.find_one({
                            "tenantId": ObjectId(tenant_id)
                        })
                        logger.info(f"Using tenant-level vector config for workspace {workspace_id}")

            if not vector_config:
                # Use default configuration from environment variables as fallback
                logger.info(f"No vector configuration found, using default environment config for workspace {workspace_id}")
                vector_config = {
                    "localUri": os.getenv("VECTOR_DATABASE_URL"),
                    "environment": os.getenv("VECTOR_DATABASE_NAME"),
                    "provider": "MONGODB"  # Default provider
                }

                # Validate that we have the required environment variables
                if not vector_config["localUri"] or not vector_config["environment"]:
                    logger.error(f"Missing required environment variables: VECTOR_DATABASE_URL or VECTOR_DATABASE_NAME")
                    return None
            
            # Use connection pool for optimized MongoDB connection
            logger.debug(f"Getting pooled MongoDB connection for unified vectors collection")
            connection_string = vector_config.get("localUri", os.getenv("VECTOR_DATABASE_URL"))
            db_name = vector_config.get("environment", os.getenv("VECTOR_DATABASE_NAME"))

            client = mongo_pool.get_client(connection_string)
            db = client[db_name]
            collection = db["vectors"]  # Use unified vectors collection

            # Create Azure Cosmos DB vector store with sharded index
            vector_store = AzureCosmosDBVectorSearch(
                collection=collection,
                embedding=self.embedding_model,
                index_name="sharded_diskann_vector_index",
                text_key="content",
                embedding_key="embedding"
            )
            
            # Cache the vector store
            self.vector_stores[workspace_id] = vector_store
            logger.info(f"Vector store created for workspace {workspace_id}")
            return vector_store
            
        except Exception as e:
            logger.error(f"Failed to create vector store for workspace {workspace_id}: {e}")
            return None
    
    async def _search_workspace(self, workspace_id: str, query: str, top_k: int) -> List[Dict[str, Any]]:
        """Search for documents in a specific workspace with fallback to text search."""
        vector_store = await self._get_vector_store(workspace_id)
        if not vector_store:
            return await self._fallback_text_search(workspace_id, query, top_k)

        try:
            # Ensure query is a string and not tokenized
            if not isinstance(query, str):
                logger.error(f"Query is not a string: {type(query)} - {query}")
                if isinstance(query, list):
                    logger.error("Query appears to be tokenized - this will cause embedding API errors")
                    return []
                query = str(query)

            # Normalize query for case-insensitive search
            normalized_query = self._normalize_query_for_search(query)

            logger.debug(f"Performing document search for workspace {workspace_id} with query: '{query[:100]}...'")
            logger.debug(f"Normalized query: '{normalized_query[:100]}...'")

            # Perform vector search with additional debugging
            logger.debug(f"About to call similarity_search_with_score with query type: {type(normalized_query)}")
            logger.debug(f"Query content: {normalized_query}")

            # Ensure query is definitely a string before calling vector store
            if not isinstance(normalized_query, str):
                logger.error(f"Query is not a string before vector store call: {type(normalized_query)} - {normalized_query}")
                normalized_query = str(normalized_query)

            # Retrieve more documents initially for better filtering
            initial_k = min(top_k * 3, 50)  # Get 3x more documents for better selection

            # Create filter for workspace-specific search in unified collection
            workspace_filter = {
                "workspaceId": workspace_id
            }

            docs = await asyncio.to_thread(
                vector_store.similarity_search_with_score,
                normalized_query,
                k=initial_k,
                filter=workspace_filter
            )

            # Format results with enhanced scoring
            results = []
            for doc, score in docs:
                # Enhance metadata using existing vector embedding metadata
                enhanced_metadata = await self._enhance_document_metadata(doc.metadata)

                # Enhanced similarity score calculation
                # MongoDB Atlas returns cosine distance (0 = identical, 2 = opposite)
                # Convert to similarity score (0-1 range, higher = more similar)

                # More lenient scoring for better recall
                if score <= 2.0:  # Increased from 1.0 to 2.0 for better coverage
                    # For cosine distance: similarity = 1 - (distance / 2)
                    similarity_score = max(0.0, 1.0 - (float(score) / 2.0))
                else:
                    # Even for higher distances, give some score for potential matches
                    similarity_score = max(0.0, 1.0 / (1.0 + float(score)))  # Inverse relationship

                # Special boost for person name queries
                content_lower = doc.page_content.lower()
                query_lower = query.lower()

                # Check if this looks like a person name match
                if any(term in content_lower for term in query_lower.split() if len(term) > 2):
                    # Boost score for potential person name matches
                    similarity_score = min(1.0, similarity_score * 1.5)

                results.append({
                    "content": doc.page_content,
                    "metadata": {
                        **enhanced_metadata,
                        "workspace_id": workspace_id,
                        "similarity_score": similarity_score,
                        "raw_distance": float(score),
                        "relevance_score": similarity_score,  # Use vector similarity as primary relevance
                        "search_method": "vector"
                    },
                    "score": similarity_score  # Add score at top level for filtering
                })

            logger.info(f"Vector search retrieved {len(results)} documents from workspace {workspace_id}")

            # If vector search returns no results, try text-based fallback
            if not results:
                logger.info(f"Vector search returned no results for workspace {workspace_id}, trying text search fallback")
                results = await self._fallback_text_search(workspace_id, query, top_k)

            return results

        except Exception as e:
            logger.error(f"Error in vector search for workspace {workspace_id}: {e}")
            logger.info(f"Falling back to text search for workspace {workspace_id}")
            return await self._fallback_text_search(workspace_id, query, top_k)
    
    async def _fallback_text_search(self, workspace_id: str, query: str, top_k: int) -> List[Dict[str, Any]]:
        """Fallback text-based search when vector search fails or returns no results."""
        try:
            logger.info(f"Performing text-based search fallback for workspace {workspace_id}")

            # Get vector store to access the collection
            vector_store = await self._get_vector_store(workspace_id)
            if not vector_store:
                logger.warning(f"No vector store available for text search fallback in workspace {workspace_id}")
                return []

            collection = vector_store.collection

            # Perform text-based regex search
            search_filter = {
                "content": {"$regex": query, "$options": "i"}  # Case-insensitive regex search
            }

            # Find documents matching the text search
            cursor = collection.find(search_filter).limit(top_k)
            docs = await asyncio.to_thread(list, cursor)

            results = []
            for i, doc in enumerate(docs):
                content = doc.get("content", "")
                metadata = doc.get("metadata", {})

                # Calculate a simple text relevance score based on query matches
                query_lower = query.lower()
                content_lower = content.lower()

                # Count occurrences of query terms
                query_terms = query_lower.split()
                relevance_score = 0.0
                for term in query_terms:
                    if term in content_lower:
                        relevance_score += content_lower.count(term) / len(content_lower)

                # Normalize score to 0-1 range
                relevance_score = min(1.0, relevance_score * 10)  # Scale up and cap at 1.0

                # Enhance metadata
                enhanced_metadata = await self._enhance_document_metadata(metadata)

                results.append({
                    "content": content,
                    "metadata": {
                        **enhanced_metadata,
                        "workspace_id": workspace_id,
                        "similarity_score": relevance_score,
                        "relevance_score": relevance_score,
                        "search_method": "text_fallback",
                        "text_rank": i + 1
                    },
                    "score": relevance_score
                })

            logger.info(f"Text search fallback found {len(results)} documents for workspace {workspace_id}")
            return results

        except Exception as e:
            logger.error(f"Error in text search fallback for workspace {workspace_id}: {e}")
            return []

    async def _parallel_search(self, workspace_ids: List[str], query: str, top_k: int) -> List[Dict[str, Any]]:
        """Perform parallel searches across multiple workspaces."""
        # Create search tasks for each workspace
        search_tasks = [
            self._search_workspace(workspace_id, query, top_k)
            for workspace_id in workspace_ids
        ]

        logger.debug(f"Executing searches in parallel for query: '{query} {search_tasks}...'")

        # Execute searches in parallel
        search_results = await asyncio.gather(*search_tasks, return_exceptions=True)

        # Combine results
        all_documents = []
        for i, result in enumerate(search_results):
            if isinstance(result, Exception):
                logger.error(f"Search failed for workspace {workspace_ids[i]}: {result}")
                continue
            all_documents.extend(result)

        return all_documents

    def _enhanced_rank_and_filter_documents(self, documents: List[Dict[str, Any]],
                                          top_k: int, min_relevance_score: float) -> List[Dict[str, Any]]:
        """Enhanced ranking and filtering using vector similarity scores with better fallback."""
        if not documents:
            logger.info("No documents to filter")
            return []

        logger.info(f"Starting document filtering with {len(documents)} documents, min_relevance_score={min_relevance_score}")

        # Sort documents by score (highest first)
        sorted_docs = sorted(documents, key=lambda x: x.get("score", 0), reverse=True)

        # Log score distribution for debugging
        scores = [doc.get("score", 0) for doc in sorted_docs]
        if scores:
            logger.info(f"Document score distribution: min={min(scores):.3f}, max={max(scores):.3f}, avg={sum(scores)/len(scores):.3f}")

        # Filter by minimum relevance score using vector similarity
        filtered_docs = [
            doc for doc in sorted_docs
            if doc.get("score", 0) >= min_relevance_score
        ]

        logger.info(f"Filtered {len(documents)} documents to {len(filtered_docs)} based on vector similarity threshold {min_relevance_score}")

        # If no documents pass the threshold, but we have documents, use a more lenient approach
        if not filtered_docs and documents:
            logger.warning(f"No documents passed threshold {min_relevance_score}, using top documents with lower threshold")
            # Use a much lower threshold as fallback
            fallback_threshold = min_relevance_score * 0.5  # Use half the original threshold
            filtered_docs = [
                doc for doc in sorted_docs
                if doc.get("score", 0) >= fallback_threshold
            ]
            logger.info(f"Fallback filtering with threshold {fallback_threshold}: {len(filtered_docs)} documents")

            # If still no results, take the top documents regardless of score
            if not filtered_docs:
                logger.warning("Even fallback threshold failed, taking top 3 documents regardless of score")
                filtered_docs = sorted_docs[:3]

        if not filtered_docs:
            logger.info(f"No documents passed any relevance threshold")
            return []

        # Return top_k documents
        final_docs = filtered_docs[:top_k]
        logger.info(f"Returning {len(final_docs)} documents after filtering and ranking")
        return final_docs

    def _apply_diversity_filtering(self, documents: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Apply diversity filtering to avoid returning too similar documents."""
        if len(documents) <= 3:
            return documents

        diverse_docs = [documents[0]]  # Always include the top document

        for doc in documents[1:]:
            # Check similarity with already selected documents
            is_diverse = True
            doc_content = doc.get("content", "").lower()

            for selected_doc in diverse_docs:
                selected_content = selected_doc.get("content", "").lower()

                # Simple content similarity check
                if len(doc_content) > 0 and len(selected_content) > 0:
                    # Calculate Jaccard similarity of words
                    doc_words = set(doc_content.split())
                    selected_words = set(selected_content.split())

                    if doc_words and selected_words:
                        intersection = len(doc_words.intersection(selected_words))
                        union = len(doc_words.union(selected_words))
                        similarity = intersection / union if union > 0 else 0

                        # If documents are too similar (>70% word overlap), skip
                        if similarity > 0.7:
                            is_diverse = False
                            break

            if is_diverse:
                diverse_docs.append(doc)

        logger.info(f"Diversity filtering: {len(documents)} -> {len(diverse_docs)} documents")
        return diverse_docs

    def _rank_and_filter_documents(self, documents: List[Dict[str, Any]],
                                 top_k: int, min_relevance_score: float) -> List[Dict[str, Any]]:
        """Rank documents by relevance and filter by minimum score."""
        if not documents:
            return []

        # Filter by minimum relevance score using vector similarity
        filtered_docs = [
            doc for doc in documents
            if doc.get("score", 0) >= min_relevance_score
        ]

        # Documents are already sorted by vector similarity from vector search
        # Return top_k documents
        return filtered_docs[:top_k]
    
    async def _arun(
        self,
        query: str,
        workspace_ids: List[str],
        top_k: int = 8,
        min_relevance_score: float = 0.6,
        is_follow_up: bool = False,
        run_manager: Optional[CallbackManagerForToolRun] = None,
    ) -> Dict[str, Any]:
        """Async implementation of the document retrieval tool."""
        start_time = time.time()
        
        try:
            # Validate query input early to catch tokenization issues
            if not isinstance(query, str):
                logger.error(f"Document retrieval received non-string query: {type(query)} - {query}")
                if isinstance(query, list):
                    logger.error("Query appears to be tokenized - this will cause embedding API errors")
                    return {
                        "content": "Error: Query was tokenized instead of being a string. This indicates a bug in the query processing pipeline.",
                        "documents": []
                    }
                query = str(query)

            if not query.strip():
                logger.warning("Empty query provided to document retrieval")
                return {
                    "content": "No query provided for document retrieval.",
                    "documents": []
                }

            # Skip document retrieval for follow-up questions
            if is_follow_up:
                logger.info(f"Skipping document retrieval for follow-up question: '{query[:100]}...'")
                return {
                    "content": "Follow-up question detected - using existing conversation context instead of retrieving new documents.",
                    "documents": []
                }

            # For production, always attempt document retrieval
            # Users might ask "hi, can you find my resume?" etc.
            logger.info(f"Starting document retrieval for query: '{query[:100]}...'")
            logger.debug(f"Full query: {query}")
            
            # Perform parallel search across workspaces with enhanced parameters
            logger.info(f"Searching {len(workspace_ids)} workspaces for query: '{query}'")

            # First, let's check workspace accessibility
            accessible_workspaces = []
            for workspace_id in workspace_ids:
                vector_store = await self._get_vector_store(workspace_id)
                if vector_store:
                    try:
                        collection = vector_store.collection
                        doc_count = collection.count_documents({})
                        accessible_workspaces.append(workspace_id)
                        logger.info(f"Workspace {workspace_id}: {doc_count} documents in vector store")
                    except Exception as e:
                        logger.warning(f"Workspace {workspace_id}: Error accessing vector store - {e}")
                else:
                    logger.warning(f"Workspace {workspace_id}: No vector store available")

            logger.info(f"Accessible workspaces: {len(accessible_workspaces)} out of {len(workspace_ids)}")

            documents = await self._parallel_search(workspace_ids, query, top_k * 3)  # Get 3x more initially for better selection

            logger.info(f"Found {len(documents)} documents from parallel search")

            if not documents:
                logger.warning(f"No documents found in any workspace for query: '{query}'")

                # Try text-based search as fallback
                logger.info("Attempting text-based search fallback")
                text_search_results = []
                for workspace_id in accessible_workspaces:
                    try:
                        text_results = await self._fallback_text_search(workspace_id, query, 3)
                        text_search_results.extend(text_results)
                        logger.debug(f"Text search in workspace {workspace_id}: {len(text_results)} results")
                    except Exception as e:
                        logger.error(f"Text search failed for workspace {workspace_id}: {e}")

                debug_info = {
                    "accessible_workspaces": accessible_workspaces,
                    "total_workspaces": len(workspace_ids),
                    "text_search_results": len(text_search_results)
                }

                return {
                    "content": "No relevant documents found in the knowledge base.",
                    "documents": [],
                    "debug_info": debug_info
                }

            logger.info(f"Found {len(documents)} documents before filtering")

            # Enhanced ranking and filtering with vector similarity scores
            # final_documents = self._enhanced_rank_and_filter_documents(
            #     documents, top_k, min_relevance_score
            # )

            if not documents:
                logger.warning(f"No documents passed relevance threshold {min_relevance_score}")
                logger.info(f"Original document count: {len(documents)}")

                # Log some sample scores for debugging
                sample_scores = [doc.get("score", 0) for doc in documents[:5]]
                logger.info(f"Sample document scores: {sample_scores}")

                return {
                    "content": "No sufficiently relevant documents found.",
                    "documents": [],
                    "debug_info": {
                        "original_count": len(documents),
                        "min_relevance_score": min_relevance_score,
                        "sample_scores": sample_scores
                    }
                }
            
            # Format response
            response_parts = [f"Found {len(documents)} relevant documents:"]
            
            for i, doc in enumerate(documents, 1):
                metadata = doc.get("metadata", {})
                relevance = metadata.get("relevanceScore", 0)
                source = metadata.get("source", "Unknown")
                relevant_text = metadata.get("relevantText", doc.get("content", ""))
                
                response_parts.append(
                    f"\n[{i}] Source: {source} (Relevance: {relevance:.3f})\n"
                    f"Content: {relevant_text}..."
                )
            
            elapsed_time = time.time() - start_time
            logger.info(f"Document retrieval completed in {elapsed_time:.2f} seconds {len(documents)} results")
            
            return {"content":"\n".join(response_parts), "documents": documents}
            
        except Exception as e:
            logger.error(f"Error in document retrieval: {e}")
            return {
                "content": f"Error retrieving documents: {str(e)}",
                "documents": []
            }
    
    def _run(
        self,
        query: str,
        workspace_ids: List[str],
        top_k: int = 8,
        min_relevance_score: float = 0.6,
        is_follow_up: bool = False,
        run_manager: Optional[CallbackManagerForToolRun] = None,
    ) -> Dict[str, Any]:
        """Sync wrapper for async implementation."""
        return asyncio.run(self._arun(query, workspace_ids, top_k, min_relevance_score, is_follow_up, run_manager))
