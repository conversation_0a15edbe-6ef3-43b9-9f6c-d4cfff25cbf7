"use client";

import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Footer,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Loader2, <PERSON><PERSON><PERSON>riangle } from "lucide-react";
import { useTranslatedToast } from "@/hooks/use-translated-toast";

// TypeScript interfaces
interface Changelog {
  id: string;
  title: string;
  content: string;
  version?: string;
  type: "RELEASE" | "HOTFIX" | "MAINTENANCE" | "ANNOUNCEMENT";
  priority: "LOW" | "MEDIUM" | "HIGH" | "CRITICAL";
  isActive: boolean;
  targetTenants: string[];
  targetEnvironment?: string;
  publishedAt: Date;
  expiresAt?: Date;
  githubCommitSha?: string;
  deploymentId?: string;
  authorId?: string;
  createdAt: Date;
  updatedAt: Date;
}

interface ChangelogDeleteModalProps {
  changelog: Changelog | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess: () => void;
}

export function ChangelogDeleteModal({
  changelog,
  open,
  onOpenChange,
  onSuccess,
}: ChangelogDeleteModalProps) {
  const toast = useTranslatedToast();
  const [isDeleting, setIsDeleting] = useState(false);

  const handleDelete = async () => {
    if (!changelog) return;

    setIsDeleting(true);

    try {
      const response = await fetch(`/api/changelog/${changelog.id}`, {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to delete changelog");
      }

      // Show success toast
      toast.success("toast.changelogDeletedSuccessfully");

      onOpenChange(false);
      onSuccess();
    } catch (error) {
      console.error("Error deleting changelog:", error);

      // Show error toast
      toast.rawError(
        error instanceof Error
          ? error.message
          : "Failed to delete changelog entry"
      );
    } finally {
      setIsDeleting(false);
    }
  };

  const handleCancel = () => {
    if (!isDeleting) {
      onOpenChange(false);
    }
  };

  if (!changelog) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md">
        <DialogHeader className="space-y-4">
          <div className="flex items-center space-x-3">
            <div className="flex-shrink-0">
              <AlertTriangle className="h-6 w-6 text-red-600" />
            </div>
            <div>
              <DialogTitle className="text-lg font-semibold">
                Delete Changelog Entry
              </DialogTitle>
            </div>
          </div>
        </DialogHeader>

        <div className="space-y-4">
          <p className="text-sm text-muted-foreground">
            Are you sure you want to delete this changelog entry? This action
            cannot be undone.
          </p>

          <div className="bg-muted rounded-lg p-3 space-y-2">
            <div className="font-medium text-sm">{changelog.title}</div>
            {changelog.version && (
              <div className="text-xs text-muted-foreground">
                Version: {changelog.version}
              </div>
            )}
            <div className="text-xs text-muted-foreground">
              Type: {changelog.type} • Priority: {changelog.priority}
            </div>
          </div>
        </div>

        <DialogFooter className="gap-2">
          <Button
            type="button"
            variant="outline"
            onClick={handleCancel}
            disabled={isDeleting}
          >
            Cancel
          </Button>
          <Button
            type="button"
            variant="destructive"
            onClick={handleDelete}
            disabled={isDeleting}
          >
            {isDeleting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Delete
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
