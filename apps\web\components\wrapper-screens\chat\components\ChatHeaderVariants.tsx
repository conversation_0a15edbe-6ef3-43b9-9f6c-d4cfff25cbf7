"use client";

import React, { useState, useRef, useEffect } from "react";
import { ShareButton } from "./ShareButton";
import { useLanguage } from "@/lib/language-context";
import {
  MessageSquare,
  Clock,
  Edit3,
  Download,
  Trash2,
  Info,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import toast from "react-hot-toast";
import { useRouter } from "next/navigation";

interface ChatHeaderProps {
  chatId: string | null;
  chatTitle?: string;
  hasMessages: boolean;
  messageCount?: number;
  lastActivity?: string;
}

// Helper function to format last activity
const formatLastActivity = (lastActivity: string): string => {
  const date = new Date(lastActivity);
  const now = new Date();
  const diffInMinutes = Math.floor(
    (now.getTime() - date.getTime()) / (1000 * 60)
  );

  if (diffInMinutes < 1) return "Just now";
  if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
  if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
  return `${Math.floor(diffInMinutes / 1440)}d ago`;
};

// Floating Info Panel
export const ChatFloatingInfoPanel: React.FC<ChatHeaderProps> = ({
  chatId,
  chatTitle,
  hasMessages,
  messageCount = 0,
  lastActivity,
}) => {
  const { t } = useLanguage();
  const router = useRouter();
  const [isOpen, setIsOpen] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [editTitle, setEditTitle] = useState(chatTitle || "");
  const [isUpdating, setIsUpdating] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const panelRef = useRef<HTMLDivElement>(null);

  // Early return after all hooks to avoid violating Rules of Hooks
  if (!chatId || !hasMessages) {
    return null;
  }

  // Handle title update
  const handleTitleUpdate = async () => {
    if (!editTitle.trim() || editTitle === chatTitle) {
      setIsEditing(false);
      return;
    }

    try {
      setIsUpdating(true);
      const response = await fetch(`/api/chat/${chatId}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          title: editTitle.trim(),
        }),
      });

      if (response.ok) {
        toast.success("Chat title updated");
        setIsEditing(false);
      } else {
        toast.error("Failed to update title");
      }
    } catch (error) {
      console.error("Error updating title:", error);
      toast.error("Failed to update title");
    } finally {
      setIsUpdating(false);
    }
  };

  // Handle chat export
  const handleExportChat = async () => {
    try {
      const response = await fetch(`/api/chat/${chatId}/export`);
      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = `${chatTitle || "chat"}-export.json`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
        toast.success("Chat exported successfully");
      } else {
        toast.error("Failed to export chat");
      }
    } catch (error) {
      console.error("Error exporting chat:", error);
      toast.error("Failed to export chat");
    }
  };

  // Handle chat deletion
  const handleDeleteChat = async () => {
    try {
      setIsDeleting(true);
      const response = await fetch(`/api/chat/${chatId}`, {
        method: "DELETE",
      });

      if (response.ok) {
        router.refresh();
        toast.success("Chat deleted successfully");
        window.location.href = "/ask-ai";
      } else {
        toast.error("Failed to delete chat");
      }
    } catch (error) {
      console.error("Error deleting chat:", error);
      toast.error("Failed to delete chat");
    } finally {
      setIsDeleting(false);
      setShowDeleteDialog(false);
    }
  };
  return (
    <div className="fixed top-16 right-4 z-20" ref={panelRef}>
      <Collapsible open={isOpen} onOpenChange={setIsOpen}>
        <CollapsibleTrigger asChild>
          <Button
            variant="outline"
            size="sm"
            className="bg-white/95 dark:bg-gray-900/95 backdrop-blur-sm shadow-lg"
          >
            <Info className="h-4 w-4 " />
          </Button>
        </CollapsibleTrigger>

        <CollapsibleContent className="absolute top-full right-0 mt-2 z-30">
          <div className="bg-white/95 dark:bg-gray-900/95 backdrop-blur-sm border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg p-4 min-w-[280px]">
            <div className="space-y-3">
              {/* Title Section */}
              <div>
                <label className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">
                  {t("chat.title")}
                </label>
                {isEditing ? (
                  <div className="flex items-center gap-2 mt-1">
                    <Input
                      value={editTitle}
                      onChange={(e) => setEditTitle(e.target.value)}
                      onKeyDown={(e) => {
                        if (e.key === "Enter") {
                          handleTitleUpdate();
                        } else if (e.key === "Escape") {
                          setIsEditing(false);
                          setEditTitle(chatTitle || "");
                        }
                      }}
                      className="text-sm"
                      disabled={isUpdating}
                      autoFocus
                    />
                    <Button
                      size="sm"
                      onClick={handleTitleUpdate}
                      disabled={isUpdating}
                      className="h-7"
                    >
                      {isUpdating ? "..." : t("common.save")}
                    </Button>
                  </div>
                ) : (
                  <div className="flex items-center justify-between mt-1">
                    <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                      {chatTitle || t("chat.untitledChat") || "Untitled Chat"}
                    </p>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                        setEditTitle(chatTitle || "");
                        setIsEditing(true);
                      }}
                      className="h-6 w-6 p-0"
                    >
                      <Edit3 className="h-3 w-3" />
                    </Button>
                  </div>
                )}
              </div>

              {/* Stats Section */}
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-xs text-gray-500 dark:text-gray-400">
                    {t("chat.messages")}
                  </span>
                  <div className="flex items-center gap-1 text-sm">
                    <MessageSquare className="h-3 w-3" />
                    <span>{messageCount}</span>
                  </div>
                </div>

                {lastActivity && (
                  <div className="flex items-center justify-between">
                    <span className="text-xs text-gray-500 dark:text-gray-400">
                      {t("chat.lastActivity")}
                    </span>
                    <div className="flex items-center gap-1 text-sm">
                      <Clock className="h-3 w-3" />
                      <span>{formatLastActivity(lastActivity)}</span>
                    </div>
                  </div>
                )}
              </div>

              {/* Actions Section */}
              <div className="flex items-center gap-2 pt-2 border-t border-gray-200 dark:border-gray-700">
                <ShareButton chatId={chatId} chatTitle={chatTitle} />

                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleExportChat}
                  className="flex-1"
                >
                  <Download className="h-3 w-3 mr-2" />
                  {t("chat.export")}
                </Button>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setShowDeleteDialog(true);
                  }}
                  className="text-red-600 hover:text-red-700"
                >
                  <Trash2 className="h-3 w-3" />
                </Button>
              </div>
            </div>
          </div>
        </CollapsibleContent>
      </Collapsible>
      {/* Delete Confirmation Dialog */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{t("chat.deleteChat")}</AlertDialogTitle>
            <AlertDialogDescription>
              {t("chat.deleteChatConfirmation")}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isDeleting}>
              {t("common.cancel")}
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteChat}
              disabled={isDeleting}
              className="bg-red-600 hover:bg-red-700 focus:ring-red-600"
            >
              {isDeleting ? t("chat.deleting") : t("chat.deleteChat")}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};
