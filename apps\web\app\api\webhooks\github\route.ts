import { NextRequest, NextResponse } from "next/server";
import { headers } from "next/headers";
import crypto from "crypto";
import db from "@/lib/shared-db";

// GitHub webhook secret for verification
const GITHUB_WEBHOOK_SECRET = process.env.GITHUB_WEBHOOK_SECRET;

function verifyGitHubSignature(payload: string, signature: string): boolean {
  if (!GITHUB_WEBHOOK_SECRET) {
    console.warn("GitHub webhook secret not configured");
    return false;
  }

  const expectedSignature = crypto
    .createHmac("sha256", GITHUB_WEBHOOK_SECRET)
    .update(payload, "utf8")
    .digest("hex");

  const expectedSignatureWithPrefix = `sha256=${expectedSignature}`;
  
  return crypto.timingSafeEqual(
    Buffer.from(signature),
    Buffer.from(expectedSignatureWithPrefix)
  );
}

function extractChangelogFromCommits(commits: any[]): string | null {
  // Look for commits with changelog indicators
  const changelogKeywords = [
    "changelog:",
    "release:",
    "feat:",
    "fix:",
    "breaking:",
    "update:",
    "add:",
    "remove:",
    "improve:",
    "refactor:"
  ];

  const changelogCommits = commits.filter(commit => {
    const message = commit.message.toLowerCase();
    return changelogKeywords.some(keyword => message.includes(keyword));
  });

  if (changelogCommits.length === 0) return null;

  // Generate changelog content from commits
  let content = "## Changes in this release:\n\n";
  
  changelogCommits.forEach(commit => {
    const message = commit.message.split('\n')[0]; // First line only
    const author = commit.author?.name || commit.committer?.name || "Unknown";
    content += `- ${message} (by ${author})\n`;
  });

  return content;
}

export async function POST(request: NextRequest) {
  try {
    const headersList = headers();
    const signature = headersList.get("x-hub-signature-256");
    const event = headersList.get("x-github-event");
    
    if (!signature) {
      return NextResponse.json(
        { error: "Missing signature" },
        { status: 401 }
      );
    }

    const payload = await request.text();
    
    // Verify GitHub signature
    if (!verifyGitHubSignature(payload, signature)) {
      return NextResponse.json(
        { error: "Invalid signature" },
        { status: 401 }
      );
    }

    const data = JSON.parse(payload);

    // Handle different GitHub events
    switch (event) {
      case "push":
        await handlePushEvent(data);
        break;
      case "release":
        await handleReleaseEvent(data);
        break;
      case "deployment":
        await handleDeploymentEvent(data);
        break;
      default:
        console.log(`Unhandled GitHub event: ${event}`);
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("GitHub webhook error:", error);
    return NextResponse.json(
      { error: "Webhook processing failed" },
      { status: 500 }
    );
  }
}

async function handlePushEvent(data: any) {
  // Only process pushes to main/master branch
  const ref = data.ref;
  if (!ref.endsWith("/main") && !ref.endsWith("/master")) {
    return;
  }

  const commits = data.commits || [];
  const changelogContent = extractChangelogFromCommits(commits);
  
  if (!changelogContent) {
    return; // No changelog-worthy commits
  }

  const repository = data.repository;
  const pusher = data.pusher;
  const headCommit = data.head_commit;

  await createChangelog({
    title: `Code Update - ${repository.name}`,
    content: changelogContent,
    type: "MAINTENANCE",
    priority: "LOW",
    githubCommitSha: headCommit?.id,
    targetEnvironment: "dev", // Assume dev for regular pushes
    metadata: {
      repository: repository.full_name,
      pusher: pusher.name,
      branch: ref.split('/').pop(),
    }
  });
}

async function handleReleaseEvent(data: any) {
  const release = data.release;
  const action = data.action;

  // Only process published releases
  if (action !== "published") {
    return;
  }

  const repository = data.repository;
  
  await createChangelog({
    title: `${repository.name} ${release.tag_name} Released`,
    content: release.body || `New release ${release.tag_name} is now available.`,
    version: release.tag_name,
    type: "RELEASE",
    priority: release.prerelease ? "MEDIUM" : "HIGH",
    githubCommitSha: release.target_commitish,
    targetEnvironment: release.prerelease ? "qa" : "prod",
    metadata: {
      repository: repository.full_name,
      releaseUrl: release.html_url,
      author: release.author?.login,
    }
  });
}

async function handleDeploymentEvent(data: any) {
  const deployment = data.deployment;
  const repository = data.repository;

  // Only process successful deployments
  if (deployment.task !== "deploy") {
    return;
  }

  const environment = deployment.environment || "production";
  const priority = environment === "production" ? "HIGH" : "MEDIUM";

  await createChangelog({
    title: `Deployment to ${environment}`,
    content: `${repository.name} has been deployed to ${environment} environment.`,
    type: "MAINTENANCE",
    priority,
    deploymentId: deployment.id.toString(),
    githubCommitSha: deployment.sha,
    targetEnvironment: environment,
    metadata: {
      repository: repository.full_name,
      environment: deployment.environment,
      creator: deployment.creator?.login,
    }
  });
}

async function createChangelog(changelogData: {
  title: string;
  content: string;
  version?: string;
  type: "RELEASE" | "HOTFIX" | "MAINTENANCE" | "ANNOUNCEMENT";
  priority: "LOW" | "MEDIUM" | "HIGH" | "CRITICAL";
  githubCommitSha?: string;
  deploymentId?: string;
  targetEnvironment?: string;
  metadata?: any;
}) {
  try {
    await db.changelog.create({
      data: {
        title: changelogData.title,
        content: changelogData.content,
        version: changelogData.version,
        type: changelogData.type,
        priority: changelogData.priority,
        githubCommitSha: changelogData.githubCommitSha,
        deploymentId: changelogData.deploymentId,
        targetEnvironment: changelogData.targetEnvironment,
        targetTenants: [], // Empty means all tenants
        publishedAt: new Date(),
        // Store additional metadata in a JSON field if your schema supports it
      }
    });

    console.log(`Changelog created: ${changelogData.title}`);
  } catch (error) {
    console.error("Error creating changelog:", error);
    throw error;
  }
}
