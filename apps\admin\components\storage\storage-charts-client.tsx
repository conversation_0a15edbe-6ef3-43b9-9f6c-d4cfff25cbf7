"use client";

import {
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    Pie,
    Cell,
    Tooltip,
    Legend,
    <PERSON><PERSON>hart,
    CartesianGrid,
    XAxis,
    YAxis,
    Bar,
} from "recharts";
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { useState, useMemo, useEffect, useCallback, useTransition } from "react";
import { Button } from "../ui/button";
import { Search, X } from "lucide-react";
import { Input } from "../ui/input";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import { Badge } from "../ui/badge";
import { useRouter, useSearchParams } from "next/navigation";

const COLORS = ["#0088FE", "#00C49F", "#FFBB28", "#FF8042"];

export function StorageChartsClient({
    tenantData,
}: {
    tenantData: Array<{ name: string; used: number; allocated: number }>;
}) {
    const router = useRouter();
    const searchParams = useSearchParams();
    const [isPending, startTransition] = useTransition();
    const [currentPage, setCurrentPage] = useState(1);
    const [searchInput, setSearchInput] = useState("");
    const [selectedTenant, setSelectedTenant] = useState<string | undefined>();

    // Effect to get tenant from URL parameters
    useEffect(() => {
        const tenantFromURL = searchParams?.get("tenant") || "";
        setSelectedTenant(tenantFromURL || undefined);

        const pageFromURL = searchParams?.get("page");
        if (pageFromURL) {
            setCurrentPage(parseInt(pageFromURL, 10));
        }

        const searchFromURL = searchParams?.get("search") || "";
        setSearchInput(searchFromURL);
    }, [searchParams]);

    const pageSize = 5;

    const filteredData = useMemo(() => {
        let result = tenantData;

        if (selectedTenant) {
            result = result.filter((tenant) => tenant.name === selectedTenant);
        }

        if (searchInput.trim()) {
            result = result.filter((tenant) =>
                tenant.name.toLowerCase().includes(searchInput.toLowerCase())
            );
        }

        return result;
    }, [tenantData, selectedTenant, searchInput]);

    const totalResults = filteredData.length;
    const totalPages = Math.ceil(totalResults / pageSize);

    const paginatedData = filteredData.slice(
        (currentPage - 1) * pageSize,
        currentPage * pageSize
    );

    const startEntry = (currentPage - 1) * pageSize + 1;
    const endEntry = Math.min(currentPage * pageSize, totalResults);

    // Handle search with URL update
    const handleSearch = useCallback((query: string) => {
        const params = new URLSearchParams(searchParams?.toString());
        if (query.trim()) {
            params.set("search", query.trim());
        } else {
            params.delete("search");
        }
        params.set("page", "1"); // Reset to first page on search

        startTransition(() => {
            router.push(`/storage?${params.toString()}`);
        });
    }, [searchParams, router]);

    useEffect(() => {
        const tenantIdFromURL = searchParams?.get("tenant") || "";
        setSelectedTenant(tenantIdFromURL);
    }, [searchParams]);

    // Handle pagination with URL update
    const handlePageChange = (newPage: number) => {
        const params = new URLSearchParams(searchParams?.toString());
        params.set("page", newPage.toString());

        startTransition(() => {
            router.push(`/storage?${params.toString()}`);
        });
    };

    const handleClearSearch = () => {
        setSearchInput("");
        const params = new URLSearchParams(searchParams?.toString());
        params.delete("search");
        params.set("page", "1");

        startTransition(() => {
            router.push(`/storage?${params.toString()}`);
        });
    };

    const handleClearTenant = () => {
        setSelectedTenant("");
        const params = new URLSearchParams(searchParams?.toString());
        params.delete("tenant");
        params.set("page", "1");

        startTransition(() => {
            router.push(`/storage?${params.toString()}`);
        });
    };

    const totalUsedStorage = tenantData.reduce((sum, tenant) => sum + tenant.used, 0);

    return (
        <>
            {/* Bar Chart */}
            <Card>
                <CardHeader>
                    <div className="flex justify-between items-center ">
                        <CardTitle>Storage Usage by Tenant</CardTitle>
                        <Badge>Total Storage Used by all Tanent : {totalUsedStorage} GB</Badge>
                    </div>
                </CardHeader>
                <CardContent className="h-[400px]">
                    <ResponsiveContainer width="100%" height="100%">
                        <BarChart
                            data={filteredData}
                            margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                        >
                            <CartesianGrid strokeDasharray="3 3" />
                            <XAxis dataKey="name" />
                            <YAxis />
                            <Tooltip formatter={(value) => `${value} GB`} />
                            <Legend />
                            <Bar dataKey="allocated" name="Allocated Storage (GB)" fill="#8884d8" />
                            <Bar dataKey="used" name="Used Storage (GB)" fill="#82ca9d" />
                        </BarChart>
                    </ResponsiveContainer>
                </CardContent>
            </Card>

            {/* Table with Pagination, Search, and Tenant Filter */}
            <Card>
                <CardHeader>
                    <div className="flex items-center justify-between gap-4 flex-wrap">
                        <CardTitle>Storage Utilization</CardTitle>
                        <div className="flex gap-2 w-full md:w-auto">
                            {/* Tenant Dropdown */}
                            <div className="relative w-[180px]">
                                <Select
                                    value={selectedTenant}
                                    onValueChange={(value) => {
                                        setSelectedTenant(value);
                                        const params = new URLSearchParams(searchParams?.toString());

                                        if (value) {
                                            params.set("tenant", value);
                                        } else {
                                            params.delete("tenant");
                                        }

                                        params.set("page", "1");

                                        startTransition(() => {
                                            router.push(`/storage?${params.toString()}`);
                                        });
                                    }}
                                >
                                    <SelectTrigger className={`${selectedTenant ? "pr-8" : ""}`}>
                                        <SelectValue placeholder="Select tenant" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {[...new Set(tenantData.map((t) => t.name))].map((name) => (
                                            <SelectItem key={name} value={name}>
                                                {name}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                                {selectedTenant && (
                                    <button
                                        type="button"
                                        onClick={handleClearTenant}
                                        className="absolute right-2 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground"
                                    >
                                        <X className="h-4 w-4" />
                                    </button>
                                )}
                            </div>

                            {/* Search Input */}
                            <div className="relative w-[200px]">
                                <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                                <Input
                                    placeholder="Search tenants..."
                                    value={searchInput}
                                    onChange={(e) => {
                                        setSearchInput(e.target.value);
                                        handleSearch(e.target.value);
                                    }}
                                    className="pl-9 pr-9"
                                />
                                {searchInput && (
                                    <button
                                        onClick={handleClearSearch}
                                        className="absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground"
                                        type="button"
                                    >
                                        <X className="h-4 w-4" />
                                    </button>
                                )}
                            </div>
                        </div>
                    </div>
                </CardHeader>

                <CardContent>
                    <div className="overflow-x-auto">
                        <table className="w-full text-sm">
                            <thead>
                                <tr className="border-b">
                                    <th className="px-4 py-3 text-left font-medium">Tenant</th>
                                    <th className="px-4 py-3 text-left font-medium">Allocated (GB)</th>
                                    <th className="px-4 py-3 text-left font-medium">Used (GB)</th>
                                    <th className="px-4 py-3 text-left font-medium">Utilization (%)</th>
                                    <th className="px-4 py-3 text-left font-medium">Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                {paginatedData.map((tenant, index) => {
                                    const utilization = (tenant.used / tenant.allocated) * 100;
                                    let status = "Good";
                                    let statusClass = "text-green-600";

                                    if (utilization > 90) {
                                        status = "Critical";
                                        statusClass = "text-red-600";
                                    } else if (utilization > 75) {
                                        status = "Warning";
                                        statusClass = "text-yellow-600";
                                    }

                                    return (
                                        <tr key={index} className="border-b">
                                            <td className="px-4 py-3">{tenant.name}</td>
                                            <td className="px-4 py-3">{tenant.allocated}</td>
                                            <td className="px-4 py-3">{tenant.used}</td>
                                            <td className="px-4 py-3">{utilization.toFixed(1)}%</td>
                                            <td className={`px-4 py-3 ${statusClass}`}>{status}</td>
                                        </tr>
                                    );
                                })}
                            </tbody>
                        </table>

                        {/* Footer */}
                        <div className="flex items-center justify-between mt-4">
                            <div className="text-sm text-muted-foreground">
                                Showing {startEntry} to {endEntry} of {totalResults} results
                            </div>

                            <div className="flex items-center space-x-1">
                                <Button
                                    onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
                                    disabled={currentPage === 1}
                                    variant="outline"
                                    size="sm"
                                >
                                    Previous
                                </Button>

                                {Array.from({ length: Math.min(totalPages, 5) }, (_, i) => {
                                    const page = i + 1;
                                    return (
                                        <Button
                                            key={page}
                                            onClick={() => setCurrentPage(page)}
                                            size="sm"
                                            variant={page === currentPage ? "default" : "outline"}
                                        >
                                            {page}
                                        </Button>
                                    );
                                })}

                                <Button
                                    onClick={() => setCurrentPage((prev) => Math.min(prev + 1, totalPages))}
                                    disabled={currentPage === totalPages}
                                    variant="outline"
                                    size="sm"
                                >
                                    Next
                                </Button>
                            </div>
                        </div>
                    </div>
                </CardContent>
            </Card>
        </>
    );
}
