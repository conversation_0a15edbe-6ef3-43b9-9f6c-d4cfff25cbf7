"use client";

import { useSession } from "next-auth/react";
import { useEffect, useState } from "react";

export default function DebugPage() {
  const { data: session, status } = useSession();
  const [authTest, setAuthTest] = useState<any>(null);

  useEffect(() => {
    // Test the auth API endpoint
    fetch('/api/test-auth')
      .then(res => res.json())
      .then(data => setAuthTest(data))
      .catch(err => setAuthTest({ error: err.message }));
  }, []);

  return (
    <div className="p-8 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">Authentication Debug</h1>
      
      <div className="space-y-6">
        <div className="border p-4 rounded">
          <h2 className="text-lg font-semibold mb-2">Client Session Status</h2>
          <p><strong>Status:</strong> {status}</p>
          <p><strong>Session exists:</strong> {session ? 'Yes' : 'No'}</p>
          {session && (
            <div className="mt-2">
              <p><strong>User:</strong> {session.user?.email}</p>
              <p><strong>Name:</strong> {session.user?.name}</p>
              <p><strong>Expires:</strong> {session.expires}</p>
            </div>
          )}
        </div>

        <div className="border p-4 rounded">
          <h2 className="text-lg font-semibold mb-2">Server Auth Test</h2>
          {authTest ? (
            <pre className="bg-gray-100 p-2 rounded text-sm overflow-auto">
              {JSON.stringify(authTest, null, 2)}
            </pre>
          ) : (
            <p>Loading...</p>
          )}
        </div>

        <div className="border p-4 rounded">
          <h2 className="text-lg font-semibold mb-2">Environment Check</h2>
          <p><strong>Current URL:</strong> {typeof window !== 'undefined' ? window.location.href : 'N/A'}</p>
          <p><strong>User Agent:</strong> {typeof navigator !== 'undefined' ? navigator.userAgent : 'N/A'}</p>
        </div>
      </div>
    </div>
  );
}
