import { apiUrl, fetchJson } from "..";

export const createLLMSettings = async (data) => {
  try {
    const response = await fetch(`${apiUrl}/llm-settings`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error("Network response was not ok");
    }
    return await response.json();
  } catch (error) {
    console.log({ "Create LLMSettings api calling error": error });
    return { error: "Error Creating The LLMSettings" };
  }
};

export const updateLLMSettings = async (data) => {
  try {
    const response = await fetch(`${apiUrl}/llm-settings`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error("Network response was not ok");
    }
    return await response.json();
  } catch (error) {
    console.log({ "update llm-settings api calling error": error });
    return { error: "Error Update The llm-settings" };
  }
};

export const getLLMSettings = async ({
  tenantId,
  workspaceSlug = "",
  id = "",
}) => {
  try {
    if (id) {
      const response = await fetchJson(
        `${apiUrl}/llm-settings?id=${id}&tenantId=${tenantId}`
      );
      return response;
    }
    if (workspaceSlug) {
      const response = await fetchJson(
        `${apiUrl}/llm-settings?slug=${workspaceSlug}&tenantId=${tenantId}`
      );
      return response;
    }
    const response = await fetchJson(
      `${apiUrl}/llm-settings?tenantId=${tenantId}`
    );

    return response;
  } catch (error) {
    console.error("Error in getLLMSettings service:", error);
    throw error;
  }
};

export const deleteLLMSettings = async (id) => {
  try {
    const url = `${apiUrl}/llm-settings?id=${id}`;
    const response = await fetch(url, {
      method: "DELETE",
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Network response was not ok");
    }
    return await response.json();
  } catch (error) {
    console.log({ "delete LLMSettings api error": error });
    return { error: error.message || "Error deleting LLMSettings" };
  }
};
