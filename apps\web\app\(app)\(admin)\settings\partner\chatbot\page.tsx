"use client";

import React, { useState, useEffect, useRef } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Bot,
  ArrowRight,
  ArrowLeft,
  Check,
  Globe,
  Code,
  Monitor,
  Lock,
  Users,
  Settings,
  CheckCircle,
  Palette,
  Sliders,
} from "lucide-react";
import { useLanguage } from "@/lib/language-context";
import {
  chatbotService,
  ChatbotConfig,
  getCompaniesForDropdown,
} from "@/services";
import { getCookie } from "@/utils/cookies";
import { toast } from "react-hot-toast";
import { useRouter } from "next/navigation";

// Step components
const ConfigurationStep = ({
  config,
  updateConfig,
  onNext,
  companies,
}: {
  config: ChatbotConfig;
  updateConfig: (updates: Partial<ChatbotConfig>) => void;
  onNext: () => void;
  companies: { id: string; name: string }[];
}) => {
  const { t } = useLanguage();
  const isValid =
    config.name.trim() &&
    config.description.trim() &&
    config.type &&
    config.access &&
    (config.access === "public" ||
      (config.access === "private" &&
        config.companyIds &&
        config.companyIds.length > 0));

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold mb-2">
          {t("chatbot.basicConfiguration")}
        </h3>
        <p className="text-muted-foreground text-sm">
          {t("chatbot.basicConfigurationDescription")}
        </p>
      </div>

      <div className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="name">
            {t("chatbot.chatbotName")} {t("chatbot.required")}
          </Label>
          <Input
            id="name"
            placeholder={t("chatbot.chatbotNamePlaceholder")}
            value={config.name}
            onChange={(e) => updateConfig({ name: e.target.value })}
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="description">
            {t("chatbot.description")} {t("chatbot.required")}
          </Label>
          <Textarea
            id="description"
            placeholder={t("chatbot.descriptionPlaceholder")}
            value={config.description}
            onChange={(e) => updateConfig({ description: e.target.value })}
            rows={3}
          />
        </div>

        <div className="space-y-3">
          <Label>
            {t("chatbot.chatbotType")} {t("chatbot.required")}
          </Label>
          <RadioGroup
            value={config.type}
            onValueChange={(value) =>
              updateConfig({ type: value as ChatbotConfig["type"] })
            }
          >
            <div className="flex items-center space-x-2 p-3 border rounded-lg">
              <RadioGroupItem value="web-snippet" id="web-snippet" />
              <div className="flex-1">
                <div className="flex items-center gap-2">
                  <Code className="h-4 w-4" />
                  <Label htmlFor="web-snippet" className="font-medium">
                    {t("chatbot.webSnippet")}
                  </Label>
                </div>
                <p className="text-sm text-muted-foreground">
                  {t("chatbot.webSnippetDescription")}
                </p>
              </div>
            </div>

            <div className="flex items-center space-x-2 p-3 border rounded-lg">
              <RadioGroupItem value="inline-embedding" id="inline-embedding" />
              <div className="flex-1">
                <div className="flex items-center gap-2">
                  <Monitor className="h-4 w-4" />
                  <Label htmlFor="inline-embedding" className="font-medium">
                    {t("chatbot.inlineEmbedding")}
                  </Label>
                </div>
                <p className="text-sm text-muted-foreground">
                  {t("chatbot.inlineEmbeddingDescription")}
                </p>
              </div>
            </div>

            <div className="flex items-center space-x-2 p-3 border rounded-lg">
              <RadioGroupItem value="dedicated-page" id="dedicated-page" />
              <div className="flex-1">
                <div className="flex items-center gap-2">
                  <Globe className="h-4 w-4" />
                  <Label htmlFor="dedicated-page" className="font-medium">
                    {t("chatbot.dedicatedPage")}
                  </Label>
                </div>
                <p className="text-sm text-muted-foreground">
                  {t("chatbot.dedicatedPageDescription")}
                </p>
              </div>
            </div>
          </RadioGroup>
        </div>

        <div className="space-y-3">
          <Label>
            {t("chatbot.accessControl")} {t("chatbot.required")}
          </Label>
          <RadioGroup
            value={config.access}
            onValueChange={(value) =>
              updateConfig({ access: value as ChatbotConfig["access"] })
            }
          >
            <div className="flex items-center space-x-2 p-3 border rounded-lg">
              <RadioGroupItem value="public" id="public" />
              <div className="flex-1">
                <div className="flex items-center gap-2">
                  <Globe className="h-4 w-4" />
                  <Label htmlFor="public" className="font-medium">
                    {t("chatbot.public")}
                  </Label>
                </div>
                <p className="text-sm text-muted-foreground">
                  {t("chatbot.publicDescription")}
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-2 p-3 border rounded-lg">
              <RadioGroupItem value="private" id="private" />
              <div className="flex-1">
                <div className="flex items-center gap-2">
                  <Lock className="h-4 w-4" />
                  <Label htmlFor="private" className="font-medium">
                    {t("chatbot.private")}
                  </Label>
                </div>
                <p className="text-sm text-muted-foreground">
                  {t("chatbot.privateDescription")}
                </p>
              </div>
            </div>
          </RadioGroup>
        </div>
      </div>

      {config.access === "private" && (
        <div className="space-y-2">
          <Label htmlFor="companies">Companies {t("chatbot.required")}</Label>
          <Select
            value=""
            onValueChange={(value) => {
              if (value && value !== "none") {
                const currentCompanies = config.companyIds || [];
                if (!currentCompanies.includes(value)) {
                  updateConfig({
                    companyIds: [...currentCompanies, value],
                  });
                }
              }
            }}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select companies to grant access" />
            </SelectTrigger>
            <SelectContent>
              {companies
                .filter(
                  (company) => !(config.companyIds || []).includes(company.id)
                )
                .map((company) => (
                  <SelectItem key={company.id} value={company.id}>
                    {company.name}
                  </SelectItem>
                ))}
              {companies.filter(
                (company) => !(config.companyIds || []).includes(company.id)
              ).length === 0 && (
                <SelectItem value="none" disabled>
                  All companies selected
                </SelectItem>
              )}
            </SelectContent>
          </Select>

          {/* Selected Companies */}
          {config.companyIds && config.companyIds.length > 0 && (
            <div className="space-y-2">
              <Label className="text-sm">Selected Companies:</Label>
              <div className="flex flex-wrap gap-2">
                {config.companyIds.map((companyId) => {
                  const company = companies.find((c) => c.id === companyId);
                  return company ? (
                    <Badge
                      key={companyId}
                      variant="secondary"
                      className="flex items-center gap-1"
                    >
                      {company.name}
                      <button
                        type="button"
                        onClick={() => {
                          updateConfig({
                            companyIds:
                              config.companyIds?.filter(
                                (id) => id !== companyId
                              ) || [],
                          });
                        }}
                        className="ml-1 hover:bg-gray-300 rounded-full w-4 h-4 flex items-center justify-center text-xs"
                      >
                        ×
                      </button>
                    </Badge>
                  ) : null;
                })}
              </div>
            </div>
          )}

          <p className="text-sm text-muted-foreground">
            Select companies whose members will have access to this private
            chatbot. All users in the selected companies will automatically have
            access.
          </p>
        </div>
      )}

      <div className="flex justify-end">
        <Button onClick={onNext} disabled={!isValid}>
          {t("chatbot.nextCompanyAccess")}
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </div>
  );
};

const UserAccessStep = ({
  config,
  updateConfig,
  onNext,
  onBack,
}: {
  config: ChatbotConfig;
  updateConfig: (updates: Partial<ChatbotConfig>) => void;
  onNext: () => void;
  onBack: () => void;
}) => {
  const { t } = useLanguage();
  const [newUser, setNewUser] = useState("");

  const addUser = () => {
    if (newUser.trim() && !config.allowedUsers.includes(newUser.trim())) {
      updateConfig({
        allowedUsers: [...config.allowedUsers, newUser.trim()],
      });
      setNewUser("");
    }
  };

  const removeUser = (userToRemove: string) => {
    updateConfig({
      allowedUsers: config.allowedUsers.filter((user) => user !== userToRemove),
    });
  };

  const isValid =
    config.access === "public" ||
    (config.access === "private" && config.allowedUsers.length > 0);

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold mb-2">
          {t("chatbot.companyAccessLinking")}
        </h3>
        <p className="text-muted-foreground text-sm">
          {config.access === "public"
            ? t("chatbot.publicAccessEnabled")
            : t("chatbot.privateAccessEnabled")}
        </p>
      </div>

      {config.access === "private" && (
        <div className="space-y-4">
          <div className="space-y-2">
            <Label>{t("chatbot.linkCompanies")}</Label>
            <div className="flex gap-2">
              <Input
                placeholder="Enter company name"
                value={newUser}
                onChange={(e) => setNewUser(e.target.value)}
                onKeyDown={(e) => e.key === "Enter" && addUser()}
              />
              <Button onClick={addUser} disabled={!newUser.trim()}>
                {t("common.link")}
              </Button>
            </div>
          </div>

          {config.allowedUsers.length > 0 && (
            <div className="space-y-2">
              <Label>
                {t("chatbot.linkedCompanies", {
                  count: config.allowedUsers.length.toString(),
                })}
              </Label>
              <div className="space-y-2 max-h-40 overflow-y-auto">
                {config.allowedUsers.map((user, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between p-2 bg-muted rounded"
                  >
                    <span className="text-sm">{user}</span>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeUser(user)}
                    >
                      {t("common.unlink")}
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          )}

          {config.allowedUsers.length === 0 && (
            <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
              <p className="text-sm text-yellow-800">
                ⚠️ {t("chatbot.linkAtLeastOneCompany")}
              </p>
            </div>
          )}
        </div>
      )}

      {config.access === "public" && (
        <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
          <div className="flex items-center gap-2">
            <Globe className="h-4 w-4 text-green-600" />
            <p className="text-sm text-green-800 font-medium">
              {t("chatbot.publicAccessEnabled")}
            </p>
          </div>
          <p className="text-sm text-green-700 mt-1"></p>
        </div>
      )}

      <div className="flex justify-between">
        <Button variant="outline" onClick={onBack}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          {t("common.back")}
        </Button>
        <Button onClick={onNext} disabled={!isValid}>
          {t("chatbot.nextReviewAndFinish")}
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </div>
  );
};

const CustomizationStep = ({
  config,
  updateConfig,
  onNext,
  onBack,
}: {
  config: ChatbotConfig;
  updateConfig: (updates: Partial<ChatbotConfig>) => void;
  onNext: () => void;
  onBack: () => void;
}) => {
  console.log("CustomizationStep rendered with config:", config);

  const { t } = useLanguage();
  const updateCustomization = (
    updates: Partial<ChatbotConfig["customization"]>
  ) => {
    updateConfig({
      customization: {
        ...config.customization,
        ...updates,
      },
    });
  };

  const updateTheme = (
    themeUpdates: Partial<NonNullable<ChatbotConfig["customization"]>["theme"]>
  ) => {
    updateCustomization({
      theme: {
        ...config.customization?.theme,
        ...themeUpdates,
      },
    });
  };

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold mb-2">
          {t("chatbot.customizationAndSettings")}
        </h3>
        <p className="text-muted-foreground text-sm">
          {t("chatbot.customizationAndSettingsDescription")}
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Theme Customization */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Palette className="h-4 w-4" />
              {t("chatbot.themeAndAppearance")}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="primaryColor">{t("chatbot.primaryColor")}</Label>
              <div className="flex items-center gap-2">
                <Input
                  id="primaryColor"
                  type="color"
                  value={config.customization?.theme?.primaryColor || "#007bff"}
                  onChange={(e) =>
                    updateTheme({ primaryColor: e.target.value })
                  }
                  className="w-16 h-10"
                />
                <Input
                  value={config.customization?.theme?.primaryColor || "#007bff"}
                  onChange={(e) =>
                    updateTheme({ primaryColor: e.target.value })
                  }
                  placeholder="#007bff"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="secondaryColor">
                {t("chatbot.secondaryColor")}
              </Label>
              <div className="flex items-center gap-2">
                <Input
                  id="secondaryColor"
                  type="color"
                  value={
                    config.customization?.theme?.secondaryColor || "#6c757d"
                  }
                  onChange={(e) =>
                    updateTheme({ secondaryColor: e.target.value })
                  }
                  className="w-16 h-10"
                />
                <Input
                  value={
                    config.customization?.theme?.secondaryColor || "#6c757d"
                  }
                  onChange={(e) =>
                    updateTheme({ secondaryColor: e.target.value })
                  }
                  placeholder="#6c757d"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="fontFamily">{t("chatbot.fontFamily")}</Label>
              <Input
                id="fontFamily"
                value={
                  config.customization?.theme?.fontFamily || "Inter, sans-serif"
                }
                onChange={(e) => updateTheme({ fontFamily: e.target.value })}
                placeholder="Inter, sans-serif"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="borderRadius">{t("chatbot.borderRadius")}</Label>
              <Input
                id="borderRadius"
                value={config.customization?.theme?.borderRadius || "8px"}
                onChange={(e) => updateTheme({ borderRadius: e.target.value })}
                placeholder="8px"
              />
            </div>
          </CardContent>
        </Card>

        {/* Widget Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-4 w-4" />
              {t("chatbot.widgetSettings")}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label>{t("chatbot.position")}</Label>
              <RadioGroup
                value={config.customization?.position || "bottom-right"}
                onValueChange={(value) =>
                  updateCustomization({ position: value as any })
                }
              >
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="bottom-right" id="bottom-right" />
                  <Label htmlFor="bottom-right">
                    {t("chatbot.bottomRight")}
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="bottom-left" id="bottom-left" />
                  <Label htmlFor="bottom-left">{t("chatbot.bottomLeft")}</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="top-right" id="top-right" />
                  <Label htmlFor="top-right">{t("chatbot.topRight")}</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="top-left" id="top-left" />
                  <Label htmlFor="top-left">{t("chatbot.topLeft")}</Label>
                </div>
              </RadioGroup>
            </div>

            <div className="space-y-2">
              <Label>{t("chatbot.size")}</Label>
              <RadioGroup
                value={config.customization?.size || "medium"}
                onValueChange={(value) =>
                  updateCustomization({ size: value as any })
                }
              >
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="small" id="small" />
                  <Label htmlFor="small">{t("chatbot.small")}</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="medium" id="medium" />
                  <Label htmlFor="medium">{t("chatbot.medium")}</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="large" id="large" />
                  <Label htmlFor="large">{t("chatbot.large")}</Label>
                </div>
              </RadioGroup>
            </div>

            <div className="space-y-2">
              <Label htmlFor="greeting">{t("chatbot.greetingMessage")}</Label>
              <Textarea
                id="greeting"
                value={
                  config.customization?.greeting || t("chatbot.defaultGreeting")
                }
                onChange={(e) =>
                  updateCustomization({ greeting: e.target.value })
                }
                placeholder={t("chatbot.defaultGreeting")}
                rows={2}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="placeholder">
                {t("chatbot.inputPlaceholder")}
              </Label>
              <Input
                id="placeholder"
                value={
                  config.customization?.placeholder ||
                  t("chatbot.defaultPlaceholder")
                }
                onChange={(e) =>
                  updateCustomization({ placeholder: e.target.value })
                }
                placeholder={t("chatbot.defaultPlaceholder")}
              />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Advanced Settings */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* LLM & Search Configuration */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Bot className="h-4 w-4" />
              AI Configuration
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label>LLM Scope</Label>
              <div className="space-y-2">
                {["INTERNAL_ONLY", "WEB_SEARCH", "HYBRID", "FULL_LLM"].map(
                  (scope) => (
                    <div key={scope} className="flex items-center space-x-2">
                      <Checkbox
                        id={scope}
                        checked={config.llmScope?.includes(scope) || false}
                        onCheckedChange={(checked) => {
                          const currentScope = config.llmScope || [];
                          if (checked) {
                            updateConfig({
                              llmScope: [...currentScope, scope],
                            });
                          } else {
                            updateConfig({
                              llmScope: currentScope.filter((s) => s !== scope),
                            });
                          }
                        }}
                      />
                      <Label htmlFor={scope} className="text-sm">
                        {scope
                          .replace("_", " ")
                          .toLowerCase()
                          .replace(/\b\w/g, (l) => l.toUpperCase())}
                      </Label>
                    </div>
                  )
                )}
              </div>
            </div>

            <div className="space-y-2">
              <Label>Search Modes</Label>
              <div className="space-y-2">
                {["internal", "web", "hybrid"].map((mode) => (
                  <div key={mode} className="flex items-center space-x-2">
                    <Checkbox
                      id={mode}
                      checked={config.searchModes?.includes(mode) || false}
                      onCheckedChange={(checked) => {
                        const currentModes = config.searchModes || [];
                        if (checked) {
                          updateConfig({
                            searchModes: [...currentModes, mode],
                          });
                        } else {
                          updateConfig({
                            searchModes: currentModes.filter((m) => m !== mode),
                          });
                        }
                      }}
                    />
                    <Label htmlFor={mode} className="text-sm">
                      {mode.charAt(0).toUpperCase() + mode.slice(1)} Search
                    </Label>
                  </div>
                ))}
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="maxTokens">Max Tokens</Label>
                <Input
                  id="maxTokens"
                  type="number"
                  value={config.maxTokens || 4000}
                  onChange={(e) =>
                    updateConfig({
                      maxTokens: parseInt(e.target.value) || 4000,
                    })
                  }
                  min={100}
                  max={8000}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="temperature">Temperature</Label>
                <Input
                  id="temperature"
                  type="number"
                  step="0.1"
                  value={config.temperature || 0.7}
                  onChange={(e) =>
                    updateConfig({
                      temperature: parseFloat(e.target.value) || 0.7,
                    })
                  }
                  min={0}
                  max={2}
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Security & Rate Limiting */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Lock className="h-4 w-4" />
              {t("chatbot.securityAndRateLimiting")}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="allowedDomains">
                {t("chatbot.allowedDomains")}
              </Label>
              <Textarea
                id="allowedDomains"
                value={config.allowedDomains?.join("\n") || ""}
                onChange={(e) =>
                  updateConfig({
                    allowedDomains: e.target.value
                      .split("\n")
                      .map((domain) => domain.trim())
                      .filter(Boolean),
                  })
                }
                placeholder="example.com&#10;*.subdomain.com&#10;localhost:3000"
                rows={3}
              />
              <p className="text-xs text-muted-foreground">
                {t("chatbot.allowedDomainsDescription")}
              </p>
            </div>

            <div className="grid grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="rateLimitPerMinute">
                  {t("chatbot.perMinute")}
                </Label>
                <Input
                  id="rateLimitPerMinute"
                  type="number"
                  value={config.rateLimitPerMinute || 60}
                  onChange={(e) =>
                    updateConfig({
                      rateLimitPerMinute: parseInt(e.target.value) || 60,
                    })
                  }
                  min={1}
                  max={1000}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="rateLimitPerHour">{t("chatbot.perHour")}</Label>
                <Input
                  id="rateLimitPerHour"
                  type="number"
                  value={config.rateLimitPerHour || 1000}
                  onChange={(e) =>
                    updateConfig({
                      rateLimitPerHour: parseInt(e.target.value) || 1000,
                    })
                  }
                  min={1}
                  max={10000}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="rateLimitPerDay">{t("chatbot.perDay")}</Label>
                <Input
                  id="rateLimitPerDay"
                  type="number"
                  value={config.rateLimitPerDay || 10000}
                  onChange={(e) =>
                    updateConfig({
                      rateLimitPerDay: parseInt(e.target.value) || 10000,
                    })
                  }
                  min={1}
                  max={100000}
                />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="flex justify-between">
        <Button variant="outline" onClick={onBack}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          {t("chatbot.back")}
        </Button>
        <Button onClick={onNext}>
          {t("chatbot.nextFinish")}
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </div>
  );
};

const FinishStep = ({
  config,
  onBack,
  onFinish,
  isLoading,
}: {
  config: ChatbotConfig;
  onBack: () => void;
  onFinish: () => void;
  isLoading?: boolean;
}) => {
  const { t } = useLanguage();
  const getTypeLabel = (type: string) => {
    switch (type) {
      case "web-snippet":
        return t("chatbot.webSnippet");
      case "inline-embedding":
        return t("chatbot.inlineEmbedding");
      case "dedicated-page":
        return t("chatbot.dedicatedPage");
      default:
        return type;
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "web-snippet":
        return <Code className="h-4 w-4" />;
      case "inline-embedding":
        return <Monitor className="h-4 w-4" />;
      case "dedicated-page":
        return <Globe className="h-4 w-4" />;
      default:
        return <Bot className="h-4 w-4" />;
    }
  };

  // All chatbot types use the same finish step
  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold mb-2">
          {t("chatbot.reviewAndFinish")}
        </h3>
        <p className="text-muted-foreground text-sm">
          {t("chatbot.reviewDescription")}
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Bot className="h-5 w-5" />
            {config.name}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label className="text-sm font-medium">
              {t("chatbot.description")}
            </Label>
            <p className="text-sm text-muted-foreground mt-1">
              {config.description}
            </p>
          </div>

          <Separator />

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label className="text-sm font-medium">{t("chatbot.type")}</Label>
              <div className="flex items-center gap-2 mt-1">
                {getTypeIcon(config.type)}
                <span className="text-sm">{getTypeLabel(config.type)}</span>
              </div>
            </div>

            <div>
              <Label className="text-sm font-medium">
                {t("chatbot.access")}
              </Label>
              <div className="flex items-center gap-2 mt-1">
                {config.access === "public" ? (
                  <Globe className="h-4 w-4" />
                ) : (
                  <Lock className="h-4 w-4" />
                )}
                <span className="text-sm capitalize">
                  {config.access === "public"
                    ? t("chatbot.public")
                    : t("chatbot.private")}
                </span>
              </div>
            </div>
          </div>

          {config.access === "private" && config.allowedUsers.length > 0 && (
            <>
              <Separator />
              <div>
                <Label className="text-sm font-medium">
                  {t("chatbot.linkedCompanies", {
                    count: config.allowedUsers.length.toString(),
                  })}
                </Label>
                <div className="flex flex-wrap gap-1 mt-2">
                  {config.allowedUsers.slice(0, 3).map((user, index) => (
                    <Badge key={index} variant="secondary" className="text-xs">
                      {user}
                    </Badge>
                  ))}
                  {config.allowedUsers.length > 3 && (
                    <Badge variant="secondary" className="text-xs">
                      {t("chatbot.moreCompanies", {
                        count: (config.allowedUsers.length - 3).toString(),
                      })}
                    </Badge>
                  )}
                </div>
              </div>
            </>
          )}
        </CardContent>
      </Card>

      <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <div className="flex items-center gap-2">
          <CheckCircle className="h-4 w-4 text-blue-600" />
          <p className="text-sm text-blue-800 font-medium">
            {t("chatbot.readyToCreate")}
          </p>
        </div>
        <p className="text-sm text-blue-700 mt-1">
          {t("chatbot.readyToCreateDescription")}
        </p>
      </div>

      <div className="flex justify-between">
        <Button variant="outline" onClick={onBack} disabled={isLoading}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          {t("chatbot.back")}
        </Button>
        <Button
          onClick={() => {
            console.log("Finish button clicked");
            onFinish();
          }}
          className="bg-green-600 hover:bg-green-700"
          disabled={isLoading}
        >
          {isLoading ? (
            <>
              <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent" />
              {t("common.creating")}
            </>
          ) : (
            <>
              <Check className="mr-2 h-4 w-4" />
              {t("chatbot.createChatbot")}
            </>
          )}
        </Button>
      </div>
    </div>
  );
};

export default function PartnerChatbotPage() {
  const { t } = useLanguage();
  const router = useRouter();
  const [currentStep, setCurrentStep] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [isMounted, setIsMounted] = useState(false);
  const [hasError, setHasError] = useState(false);
  const [companies, setCompanies] = useState<{ id: string; name: string }[]>(
    []
  );
  const mountedRef = useRef(true);
  const [config, setConfig] = useState<ChatbotConfig>({
    name: "",
    description: "",
    type: "web-snippet",
    access: "public",
    allowedUsers: [],
    tenantId: "",
    userId: "",
    companyIds: [],
    isActive: true,

    // SDK-specific fields
    allowedDomains: [],
    customization: {
      theme: {
        primaryColor: "#007bff",
        secondaryColor: "#6c757d",
        fontFamily: "Inter, sans-serif",
        borderRadius: "8px",
      },
      position: "bottom-right",
      size: "medium",
      greeting: t("chatbot.defaultGreeting"),
      placeholder: t("chatbot.defaultPlaceholder"),
      showBranding: true,
    },
    usageCount: 0,
    monthlyUsage: 0,

    // LLM configuration
    llmScope: ["INTERNAL_ONLY"],
    searchModes: ["internal"],
    maxTokens: 4000,
    temperature: 0.7,

    // Rate limiting
    rateLimitPerMinute: 60,
    rateLimitPerHour: 1000,
    rateLimitPerDay: 10000,

    createdAt: "",
    updatedAt: "",
  });

  // Load companies for dropdown
  useEffect(() => {
    const loadCompanies = async () => {
      try {
        const result = await getCompaniesForDropdown();
        if ("companies" in result) {
          setCompanies(result.companies);
        } else {
          console.error("Failed to load companies:", result.error);
        }
      } catch (error) {
        console.error("Error loading companies:", error);
      }
    };

    loadCompanies();
  }, []);

  // Handle component mounting to prevent hydration issues
  useEffect(() => {
    try {
      setIsMounted(true);

      // Add global error handler for DOM errors
      const handleError = (event: ErrorEvent) => {
        if (
          event.message.includes("removeChild") ||
          event.message.includes("Node")
        ) {
          console.warn("DOM manipulation error caught:", event.message);
          event.preventDefault();
          return false;
        }
      };

      window.addEventListener("error", handleError);

      return () => {
        window.removeEventListener("error", handleError);
      };
    } catch (error) {
      console.error("Mount error:", error);
      setHasError(true);
    }
    return () => {
      mountedRef.current = false;
    };
  }, []);

  const updateConfig = (updates: Partial<ChatbotConfig>) => {
    setConfig((prev) => ({ ...prev, ...updates }));
  };

  const nextStep = () => {
    const newStep = Math.min(currentStep + 1, 3); // Reduced from 4 to 3 steps
    console.log(`Moving from step ${currentStep} to step ${newStep}`);
    setCurrentStep(newStep);
  };
  const prevStep = () => setCurrentStep((prev) => Math.max(prev - 1, 1));

  const handleFinish = async () => {
    if (!mountedRef.current) return;

    // Validate required fields
    if (!config.name.trim()) {
      toast.error("Please enter a chatbot name");
      return;
    }
    if (!config.description.trim()) {
      toast.error("Please enter a chatbot description");
      return;
    }

    setIsLoading(true);
    try {
      // Get tenant ID from cookie
      const tenantId = getCookie("currentOrganizationId");
      if (!tenantId) {
        if (mountedRef.current) {
          toast.error("Organization not found. Please select an organization.");
        }
        return;
      }

      // Create chatbot using the API
      console.log("Calling createChatbot with:", { ...config, tenantId });
      const result = await chatbotService.createChatbot({
        ...config,
        tenantId,
      });

      console.log("createChatbot result:", result);
      if (!mountedRef.current) return;

      if ("error" in result) {
        console.error("API error:", result.error);
        toast.error(result.error);
        return;
      }

      toast.success(t("chatbot.chatbotCreatedSuccess"));

      // Small delay to ensure toast is shown before redirect
      setTimeout(() => {
        if (mountedRef.current) {
          try {
            // Use Next.js router for safer navigation
            router.push("/settings/partner/chatbots");
          } catch (error) {
            console.error("Router navigation error:", error);
            // Fallback to window.location if router fails
            if (typeof window !== "undefined") {
              window.location.href = "/settings/partner/chatbots";
            }
          }
        }
      }, 1500);
    } catch (error) {
      console.error("Error creating chatbot:", error);
      if (mountedRef.current) {
        setHasError(true);
        toast.error(t("chatbot.failedToCreateChatbot"));
      }
    } finally {
      if (mountedRef.current) {
        setIsLoading(false);
      }
    }
  };

  const steps = [
    { number: 1, title: t("chatbot.configuration"), icon: Settings },
    { number: 2, title: t("chatbot.companyAccessLinking"), icon: Users },
    { number: 3, title: "Customization & Settings", icon: Sliders },
    { number: 4, title: t("chatbot.finish"), icon: CheckCircle },
  ];

  // Handle errors
  if (hasError) {
    return (
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Bot className="h-5 w-5" />
              Error
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center py-8">
              <p className="text-muted-foreground mb-4">
                Something went wrong. Please try refreshing the page.
              </p>
              <Button onClick={() => window.location.reload()}>
                Refresh Page
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Prevent hydration mismatches by not rendering until mounted
  if (!isMounted) {
    return (
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Bot className="h-5 w-5" />
              Loading...
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-center py-8">
              <div className="h-8 w-8 animate-spin rounded-full border-2 border-blue-500 border-t-transparent" />
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  try {
    return (
      <div className="space-y-6">
        {/* Progress Steps */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Bot className="h-5 w-5" />
              {t("chatbot.title")}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between mb-8">
              {steps.map((step, index) => {
                const Icon = step.icon;
                const isActive = currentStep === step.number;
                const isCompleted = currentStep > step.number;

                return (
                  <div key={step.number} className="flex items-center">
                    <div
                      className={`flex items-center justify-center w-10 h-10 rounded-full border-2 ${
                        isCompleted
                          ? "bg-green-500 border-green-500 text-white"
                          : isActive
                            ? "border-blue-500 text-blue-500"
                            : "border-gray-300 text-gray-400"
                      }`}
                    >
                      {isCompleted ? (
                        <Check className="h-5 w-5" />
                      ) : (
                        <Icon className="h-5 w-5" />
                      )}
                    </div>
                    <div className="ml-3">
                      <p
                        className={`text-sm font-medium ${
                          isActive
                            ? "text-blue-600"
                            : isCompleted
                              ? "text-green-600"
                              : "text-gray-500"
                        }`}
                      >
                        {t("chatbot.step")} {step.number}
                      </p>
                      <p
                        className={`text-xs ${
                          isActive
                            ? "text-blue-500"
                            : isCompleted
                              ? "text-green-500"
                              : "text-gray-400"
                        }`}
                      >
                        {step.title}
                      </p>
                    </div>
                    {index < steps.length - 1 && (
                      <div
                        className={`flex-1 h-0.5 mx-4 ${
                          currentStep > step.number
                            ? "bg-green-500"
                            : "bg-gray-300"
                        }`}
                      />
                    )}
                  </div>
                );
              })}
            </div>

            {/* Step Content */}
            {currentStep === 1 && isMounted && (
              <ConfigurationStep
                config={config}
                updateConfig={updateConfig}
                onNext={nextStep}
                companies={companies}
              />
            )}

            {currentStep === 2 && isMounted && (
              <CustomizationStep
                config={config}
                updateConfig={updateConfig}
                onNext={nextStep}
                onBack={prevStep}
              />
            )}

            {currentStep === 3 && isMounted && (
              <>
                {console.log("Rendering FinishStep, currentStep:", currentStep)}
                <FinishStep
                  config={config}
                  onBack={prevStep}
                  onFinish={handleFinish}
                  isLoading={isLoading}
                />
              </>
            )}
          </CardContent>
        </Card>
      </div>
    );
  } catch (error) {
    console.error("Component render error:", error);
    return (
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Bot className="h-5 w-5" />
              Error
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center py-8">
              <p className="text-muted-foreground mb-4">
                Something went wrong while loading the chatbot creation form.
              </p>
              <Button onClick={() => window.location.reload()}>
                Refresh Page
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }
}
