import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/next-auth";
import { redirect } from "next/navigation";
import { ApiUsageTableClient } from "./api-usage-table-client";
import db from "@/lib/shared-db";

// Types for our data structures
interface ApiRequest {
  id: string;
  userId: string | null;
  userName: string;
  tenantId: string;
  tenantName: string;
  endpoint: string;
  method: string;
  statusCode: number;
  timestamp: string;
  duration: number;
  success: boolean;
  errorMessage: string | null;
}

type DailyUsage = {
  name: string;
  [endpoint: string]: number | string;
};

interface MethodUsage {
  name: string;
  value: number;
}
interface DailyMethodUsage {
  name: string; // e.g., "Jun 19"
  [method: string]: number | string; // dynamic keys like GET, POST, etc.
};

interface EndpointUsage {
  name: string;
  value: number;
}

interface StatusCodeUsage {
  name: string;
  value: number;
  color: string;
}

interface ApiUsageData {
  apiRequests: ApiRequest[];
  dailyUsage: DailyUsage[];
  methodUsage: MethodUsage[];
  endpointUsage: EndpointUsage[];
  statusCodeUsage: StatusCodeUsage[];
  dailyHttpRequestCount:DailyMethodUsage[];
  metrics: {
    totalRequests: number;
    successRate: number;
    averageResponseTime: number;
    errorRate: number;
  };
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  searchQuery: string;
}

// Function to fetch API requests with manual joins to avoid Prisma relationship issues
async function fetchApiRequestsWithJoins(db: any) {
  try {
    // First, get all API request records, selecting only the fields we need
    // This avoids issues with null values in fields we don't use
    const apiRequests = await db.aPIRequest.findMany({
      select: {
        id: true,
        userId: true,
        tenantId: true,
        endpoint: true,
        method: true,
        statusCode: true,
        timestamp: true,
        duration: true,
        success: true,
        errorMessage: true,
      },
      orderBy: {
        timestamp: "desc",
      },
    });

    if (apiRequests.length === 0) {
      return [];
    }

    // Get unique user and tenant IDs, filtering out null values
    const userIds = [...new Set(apiRequests.map((r: any) => r.userId).filter(Boolean))];
    const tenantIds = [...new Set(apiRequests.map((r: any) => r.tenantId).filter(Boolean))];

    // Fetch users and tenants that match our API request references
    const [users, tenants] = await Promise.all([
      userIds.length > 0 ? db.user.findMany({
        where: { id: { in: userIds } },
        select: { id: true, name: true, email: true },
      }) : [],
      tenantIds.length > 0 ? db.tenant.findMany({
        where: { id: { in: tenantIds } },
        select: { id: true, name: true },
      }) : [],
    ]);

    // Create lookup maps for efficient joining
    const userMap = new Map(users.map((u: any) => [u.id, u]));
    const tenantMap = new Map(tenants.map((t: any) => [t.id, t]));

    // Process all API requests and provide placeholder data for missing relationships
    const allApiRequests = apiRequests
      .map((apiRequest: any) => {
        const user = userMap.get(apiRequest.userId);
        const tenant = tenantMap.get(apiRequest.tenantId);

        // Provide placeholder data for missing relationships
        const placeholderUser = user || {
          id: apiRequest.userId,
          name: apiRequest.userId ? `Unknown User (${apiRequest.userId.slice(-8)})` : "Anonymous",
          email: null
        };
        const placeholderTenant = tenant || {
          id: apiRequest.tenantId,
          name: `Unknown Tenant (${apiRequest.tenantId.slice(-8)})`
        };

        return {
          ...apiRequest,
          user: placeholderUser,
          tenant: placeholderTenant,
        };
      });

    return allApiRequests;
  } catch (error) {
    console.error('Error in fetchApiRequestsWithJoins:', error);
    return [];
  }
}

// Helper function to get status code color
function getStatusCodeColor(statusCode: number): string {
  if (statusCode >= 200 && statusCode < 300) return "#22c55e"; // green
  if (statusCode >= 300 && statusCode < 400) return "#f59e0b"; // yellow
  if (statusCode >= 400 && statusCode < 500) return "#ef4444"; // red
  if (statusCode >= 500) return "#dc2626"; // dark red
  return "#6b7280"; // gray
}

// Server-side data fetching function
async function fetchApiUsageData(
  searchQuery: string = "",
  page: number = 1,
  limit: number = 5,
  endpointFilter: string = ""
): Promise<ApiUsageData> {
  try {
    // Fetch API requests with manual joins to handle relationship issues
    const allApiRequests = await fetchApiRequestsWithJoins(db);
    if (allApiRequests.length === 0) {
      return {
        apiRequests: [],
        dailyUsage: [],
        methodUsage: [],
        endpointUsage: [],
        statusCodeUsage: [],
        dailyHttpRequestCount:[],
        metrics: {
          totalRequests: 0,
          successRate: 0,
          averageResponseTime: 0,
          errorRate: 0,
        },
        pagination: {
          page: 1,
          limit: 5,
          total: 0,
          totalPages: 0,
        },
        searchQuery: "",
      };
    }
    // Transform the data to match the expected structure
    let transformedApiRequests = allApiRequests.map((apiRequest: any) => {
      return {
        id: apiRequest.id,
        userId: apiRequest.userId,
        userName: apiRequest.user?.name || apiRequest.user?.email || "Anonymous",
        tenantId: apiRequest.tenantId,
        tenantName: apiRequest.tenant?.name || "Unknown Tenant",
        endpoint: apiRequest.endpoint || "unknown",
        method: apiRequest.method || "unknown",
        statusCode: apiRequest.statusCode || 0,
        timestamp: apiRequest.timestamp ? apiRequest.timestamp.toISOString() : new Date().toISOString(),
        duration: apiRequest.duration || 0,
        success: apiRequest.success || false,
        errorMessage: apiRequest.errorMessage || null,
      };
    });

    // Apply search filter if search query is provided
    if (searchQuery && searchQuery.trim()) {
      const searchTerm = searchQuery.toLowerCase().trim();
      transformedApiRequests = transformedApiRequests.filter((apiRequest: any) => {
        const userMatch = apiRequest.userName.toLowerCase().includes(searchTerm);
        const tenantMatch = apiRequest.tenantName.toLowerCase().includes(searchTerm);
        const endpointMatch = apiRequest.endpoint.toLowerCase().includes(searchTerm);
        const methodMatch = apiRequest.method.toLowerCase().includes(searchTerm);
        const statusMatch = apiRequest.statusCode.toString().includes(searchTerm);
        return userMatch || tenantMatch || endpointMatch || methodMatch || statusMatch;
      });
    }

    // Apply endpoint filter if provided
    if (endpointFilter && endpointFilter.trim()) {
      transformedApiRequests = transformedApiRequests.filter((apiRequest: any) => {
        return apiRequest.endpoint === endpointFilter;
      });
    }

    // Get total count for pagination
    const total = transformedApiRequests.length;

    // Apply pagination
    const startIndex = (page - 1) * limit;
    const paginatedApiRequests = transformedApiRequests.slice(startIndex, startIndex + limit);

    // If no results after search, return empty data structure to prevent calculation errors
    if (total === 0) {
      return {
        apiRequests: [],
        dailyUsage: [],
        methodUsage: [],
        endpointUsage: [],
        statusCodeUsage: [],
        dailyHttpRequestCount:[],
        metrics: {
          totalRequests: 0,
          successRate: 0,
          averageResponseTime: 0,
          errorRate: 0,
        },
        pagination: {
          page: 1,
          limit,
          total: 0,
          totalPages: 0,
        },
        searchQuery,
      };
    }

    // Daily usage aggregation with safe data validation
    const dailyUsageMap = transformedApiRequests.reduce((acc: any, request: any) => {
      const date = new Date(request.timestamp);
      const dayKey = date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
      const endpoint = request.endpoint || "Unknown";

      if (!acc[dayKey]) {
        acc[dayKey] = {};
      }

      acc[dayKey][endpoint] = (acc[dayKey][endpoint] || 0) + 1;
      return acc;
    }, {});

    const dailyUsage = Object.entries(dailyUsageMap)
      .map(([name, endpointCounts]) => ({
        name,
        ...(endpointCounts as Record<string, number>),
      }))
      .sort((a, b) => {
        // Convert 'Jun 18' to Date for sorting
        return new Date(`${a.name} ${new Date().getFullYear()}`).getTime() -
          new Date(`${b.name} ${new Date().getFullYear()}`).getTime();
      })
      .slice(-14); // Last 14 days

    // Method usage aggregation with safe data validation
    const methodUsageMap = transformedApiRequests.reduce((acc: any, request: any) => {
      acc[request.method] = (acc[request.method] || 0) + 1;
      return acc;
    }, {});

    const methodUsage = Object.entries(methodUsageMap)
      .map(([name, value]) => ({
        name,
        value: typeof value === 'number' && !isNaN(value) && isFinite(value) ? value : 0
      }))
      .filter(item => item.value > 0) // Remove zero values
      .sort((a, b) => b.value - a.value);

    // Endpoint usage aggregation with safe data validation
    const endpointUsageMap = transformedApiRequests.reduce((acc: any, request: any) => {
      acc[request.endpoint] = (acc[request.endpoint] || 0) + 1;
      return acc;
    }, {});

    const endpointUsage = Object.entries(endpointUsageMap)
      .map(([name, value]) => ({
        name,
        value: typeof value === 'number' && !isNaN(value) && isFinite(value) ? value : 0
      }))
      .filter(item => item.value > 0) // Remove zero values
      .sort((a, b) => b.value - a.value)
      .slice(0, 10); // Top 10 endpoints

    const dailyMethodMap: Record<string, Record<string, number>> = {};
    transformedApiRequests.forEach((req) => {
      const date = new Date(req.timestamp);
      const dayKey = date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });

      if (!dailyMethodMap[dayKey]) {
        dailyMethodMap[dayKey] = {};
      }

      const method = req.method.toUpperCase();
      dailyMethodMap[dayKey][method] = (dailyMethodMap[dayKey][method] || 0) + 1;
    });

    const dailyHttpRequestCount = Object.entries(dailyMethodMap)
      .map(([name, counts]) => ({
        name,
        ...counts, // GET, POST, DELETE, etc. become separate keys
      }))
      .sort((a, b) =>
        new Date(`${a.name} ${new Date().getFullYear()}`).getTime() -
        new Date(`${b.name} ${new Date().getFullYear()}`).getTime()
      )
      .slice(-14); // last 14 days

    // Status code usage aggregation with safe data validation
    const statusCodeUsageMap = transformedApiRequests.reduce((acc: any, request: any) => {
      const statusRange = `${Math.floor(request.statusCode / 100)}xx`;
      acc[statusRange] = (acc[statusRange] || 0) + 1;
      return acc;
    }, {});

    const statusCodeUsage = Object.entries(statusCodeUsageMap)
      .map(([name, value]) => ({
        name,
        value: typeof value === 'number' && !isNaN(value) && isFinite(value) ? value : 0,
        color: getStatusCodeColor(parseInt(name) * 100)
      }))
      .filter(item => item.value > 0) // Remove zero values
      .sort((a, b) => b.value - a.value);

    // Calculate metrics with proper null checks to prevent NaN errors
    const totalRequests = transformedApiRequests.length;
    const successfulRequests = transformedApiRequests.filter((r: any) => r.success).length;

    // Ensure we never return NaN values that could cause Decimal.js errors
    const successRate = totalRequests > 0 ? Math.round((successfulRequests / totalRequests) * 100 * 100) / 100 : 0;
    const errorRate = totalRequests > 0 ? Math.round((100 - successRate) * 100) / 100 : 0;

    // Calculate average response time with proper null checks
    let averageResponseTime = 0;
    if (totalRequests > 0) {
      const totalDuration = transformedApiRequests.reduce((sum: number, r: any) => {
        const duration = typeof r.duration === 'number' && !isNaN(r.duration) ? r.duration : 0;
        return sum + duration;
      }, 0);
      averageResponseTime = Math.round((totalDuration / totalRequests) * 100) / 100;
    }

    // Calculate pagination info
    const totalPages = Math.ceil(total / limit);

    return {
      apiRequests: paginatedApiRequests,
      dailyUsage,
      methodUsage,
      endpointUsage,
      statusCodeUsage,
      dailyHttpRequestCount,
      metrics: {
        totalRequests,
        successRate,
        averageResponseTime,
        errorRate,
      },
      pagination: {
        page,
        limit,
        total,
        totalPages,
      },
      searchQuery,
    };
  } catch (error) {
    console.error('Error fetching API usage data:', error);
    // Return fallback data
    return {
      apiRequests: [],
      dailyUsage: [],
      methodUsage: [],
      endpointUsage: [],
      statusCodeUsage: [],
      dailyHttpRequestCount:[],
      metrics: {
        totalRequests: 0,
        successRate: 0,
        averageResponseTime: 0,
        errorRate: 0,
      },
      pagination: {
        page: 1,
        limit: 5,
        total: 0,
        totalPages: 0,
      },
      searchQuery: "",
    };
  }
}

// Main server component
export default async function ApiUsagePage({
  searchParams,
}: {
  searchParams: { search?: string; page?: string; limit?: string; endpoint?: string };
}) {
  const session = await getServerSession(authOptions);
  if (!session) {
    redirect("/auth/signin");
  }

  // Parse search params with defaults
  const searchQuery = searchParams.search || "";
  const page = parseInt(searchParams.page || "1", 10);
  const limit = parseInt(searchParams.limit || "5", 10);
  const endpointFilter = searchParams.endpoint || "";

  // Validate pagination parameters
  const validPage = Math.max(1, page);
  const validLimit = Math.min(Math.max(1, limit), 100); // Max 100 items per page

  // Fetch API usage data server-side
  const apiUsageData = await fetchApiUsageData(searchQuery, validPage, validLimit, endpointFilter);

  return <ApiUsageTableClient data={apiUsageData} />;
}
