"use client";

import React from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  useChatbotAccess, 
  useChatbotAccessDetails, 
  getAccessMessage, 
  canAccessChatbot 
} from '@/hooks/useChatbotAccess';
import { 
  Lock, 
  Users, 
  Building2, 
  AlertCircle, 
  CheckCircle, 
  Loader2,
  Shield
} from 'lucide-react';

interface ChatbotAccessGuardProps {
  chatbotId: string;
  children: React.ReactNode;
  showDetails?: boolean;
  fallbackComponent?: React.ReactNode;
}

export const ChatbotAccessGuard: React.FC<ChatbotAccessGuardProps> = ({
  chatbotId,
  children,
  showDetails = false,
  fallbackComponent
}) => {
  const { accessResult, loading: accessLoading, error: accessError } = useChatbotAccess(chatbotId);
  const { details, loading: detailsLoading } = useChatbotAccessDetails(showDetails ? chatbotId : null);

  // Show loading state
  if (accessLoading) {
    return (
      <Card className="w-full max-w-md mx-auto">
        <CardContent className="flex items-center justify-center p-6">
          <Loader2 className="h-6 w-6 animate-spin mr-2" />
          <span>Checking access permissions...</span>
        </CardContent>
      </Card>
    );
  }

  // Show error state
  if (accessError) {
    return (
      <Card className="w-full max-w-md mx-auto border-red-200">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-red-600">
            <AlertCircle className="h-5 w-5" />
            Access Check Failed
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground mb-4">
            Unable to verify your access permissions for this chatbot.
          </p>
          <Button 
            variant="outline" 
            onClick={() => window.location.reload()}
            className="w-full"
          >
            Try Again
          </Button>
        </CardContent>
      </Card>
    );
  }

  // If user has access, render children
  if (canAccessChatbot(accessResult)) {
    return (
      <div>
        {showDetails && details && (
          <AccessDetailsCard details={details} />
        )}
        {children}
      </div>
    );
  }

  // If no access, show access denied message or fallback
  if (fallbackComponent) {
    return <>{fallbackComponent}</>;
  }

  return <AccessDeniedCard accessResult={accessResult} details={details} />;
};

// Component to show access denied message
const AccessDeniedCard: React.FC<{
  accessResult: any;
  details: any;
}> = ({ accessResult, details }) => {
  return (
    <Card className="w-full max-w-md mx-auto border-orange-200">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-orange-600">
          <Lock className="h-5 w-5" />
          Access Restricted
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <p className="text-sm text-muted-foreground">
          {getAccessMessage(accessResult)}
        </p>
        
        {details?.chatbot?.access === 'private' && details?.companies?.length > 0 && (
          <div className="space-y-2">
            <p className="text-sm font-medium">Authorized Companies:</p>
            <div className="flex flex-wrap gap-1">
              {details.companies.map((company: any) => (
                <Badge key={company.id} variant="outline" className="text-xs">
                  <Building2 className="h-3 w-3 mr-1" />
                  {company.name}
                </Badge>
              ))}
            </div>
            <p className="text-xs text-muted-foreground">
              Contact your administrator to be added to one of these companies.
            </p>
          </div>
        )}
        
        <Button 
          variant="outline" 
          onClick={() => window.history.back()}
          className="w-full"
        >
          Go Back
        </Button>
      </CardContent>
    </Card>
  );
};

// Component to show access details (for admin/debug purposes)
const AccessDetailsCard: React.FC<{
  details: any;
}> = ({ details }) => {
  if (!details) return null;

  return (
    <Card className="mb-4 border-green-200">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-green-600 text-sm">
          <CheckCircle className="h-4 w-4" />
          Access Granted
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        <div className="flex items-center gap-2">
          <Shield className="h-4 w-4 text-muted-foreground" />
          <span className="text-sm">
            Access Type: <Badge variant="outline">{details.chatbot.access}</Badge>
          </span>
        </div>
        
        {details.chatbot.access === 'private' && (
          <>
            <div className="flex items-center gap-2">
              <Building2 className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm">
                Authorized Companies: {details.companies.length}
              </span>
            </div>
            
            <div className="flex items-center gap-2">
              <Users className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm">
                Total Authorized Users: {details.totalAuthorizedUsers}
              </span>
            </div>
            
            {details.userAccess.companies && (
              <div className="space-y-1">
                <p className="text-xs text-muted-foreground">Your access through:</p>
                <div className="flex flex-wrap gap-1">
                  {details.userAccess.companies.map((company: any) => (
                    <Badge key={company.id} variant="secondary" className="text-xs">
                      {company.name}
                    </Badge>
                  ))}
                </div>
              </div>
            )}
          </>
        )}
      </CardContent>
    </Card>
  );
};

export default ChatbotAccessGuard;
