/**
 * Chatbot Authentication Utilities
 * 
 * Provides authentication and authorization for public chatbot endpoints
 */

import db from "@/lib/shared-db";
import { rateLimiter, RATE_LIMIT_CONFIGS, createRateLimitKey, hashIdentifier, getClientIP } from "./rate-limiter";

export interface ChatbotAuthResult {
  success: boolean;
  chatbot?: any;
  error?: string;
  rateLimitInfo?: {
    remaining: number;
    resetTime: number;
    retryAfter?: number;
  };
}

export interface AuthContext {
  chatbotId: string;
  apiKey: string;
  origin?: string;
  userAgent?: string;
  ipAddress: string;
}

/**
 * Authenticate and authorize a chatbot request
 */
export async function authenticateChatbotRequest(
  chatbotId: string,
  apiKey: string,
  headers: Headers
): Promise<ChatbotAuthResult> {
  try {
    // Basic validation
    if (!chatbotId || !apiKey) {
      return {
        success: false,
        error: "Missing chatbot ID or API key"
      };
    }

    // Validate API key format
    if (!apiKey.startsWith('skh_') || apiKey.length < 10) {
      return {
        success: false,
        error: "Invalid API key format"
      };
    }

    // Get client information
    const clientIP = getClientIP(headers);
    const hashedIP = hashIdentifier(clientIP);
    const origin = headers.get('origin');
    const userAgent = headers.get('user-agent');

    // Check rate limits first (before database query)
    const rateLimitResult = checkRateLimits(chatbotId, hashedIP);
    if (!rateLimitResult.allowed) {
      return {
        success: false,
        error: rateLimitResult.error,
        rateLimitInfo: {
          remaining: 0,
          resetTime: rateLimitResult.resetTime,
          retryAfter: rateLimitResult.retryAfter
        }
      };
    }

    // Find chatbot in database
    const chatbot = await db.chatbot.findFirst({
      where: {
        id: chatbotId,
        apiKey: apiKey,
        isActive: true,
      },
      include: {
        tenant: {
          select: {
            id: true,
            name: true,
            status: true
          }
        },
        user: {
          select: {
            id: true,
            name: true
          }
        }
      },
    });

    if (!chatbot) {
      return {
        success: false,
        error: "Invalid chatbot ID or API key"
      };
    }

    // Check if tenant is active
    if (chatbot.tenant.status !== 'active') {
      return {
        success: false,
        error: "Chatbot is temporarily unavailable"
      };
    }

    // Validate domain access
    if (origin) {
      const domainAllowed = await validateDomainAccess(chatbot, origin);
      if (!domainAllowed) {
        return {
          success: false,
          error: "Domain not allowed to access this chatbot"
        };
      }
    }

    // Check access type restrictions
    if (chatbot.access === 'private') {
      // For private chatbots, we might need additional validation
      // This could be extended to check user authentication, etc.
      // For now, we rely on the allowedDomains restriction
    }

    return {
      success: true,
      chatbot,
      rateLimitInfo: {
        remaining: rateLimitResult.remaining,
        resetTime: rateLimitResult.resetTime
      }
    };

  } catch (error) {
    console.error("Error in chatbot authentication:", error);
    return {
      success: false,
      error: "Authentication service error"
    };
  }
}

/**
 * Check rate limits for a chatbot request
 */
function checkRateLimits(chatbotId: string, hashedIP: string): {
  allowed: boolean;
  error?: string;
  remaining: number;
  resetTime: number;
  retryAfter?: number;
} {
  // Check per-minute limit
  const minuteKey = createRateLimitKey(chatbotId, hashedIP, 'minute');
  const minuteResult = rateLimiter.checkLimit(minuteKey, RATE_LIMIT_CONFIGS.CHATBOT_PER_MINUTE);
  
  if (!minuteResult.allowed) {
    return {
      allowed: false,
      error: "Rate limit exceeded (per minute)",
      remaining: minuteResult.remaining,
      resetTime: minuteResult.resetTime,
      retryAfter: minuteResult.retryAfter
    };
  }

  // Check per-hour limit
  const hourKey = createRateLimitKey(chatbotId, hashedIP, 'hour');
  const hourResult = rateLimiter.checkLimit(hourKey, RATE_LIMIT_CONFIGS.CHATBOT_PER_HOUR);
  
  if (!hourResult.allowed) {
    return {
      allowed: false,
      error: "Rate limit exceeded (per hour)",
      remaining: hourResult.remaining,
      resetTime: hourResult.resetTime,
      retryAfter: hourResult.retryAfter
    };
  }

  // Check per-day limit
  const dayKey = createRateLimitKey(chatbotId, hashedIP, 'day');
  const dayResult = rateLimiter.checkLimit(dayKey, RATE_LIMIT_CONFIGS.CHATBOT_PER_DAY);
  
  if (!dayResult.allowed) {
    return {
      allowed: false,
      error: "Rate limit exceeded (per day)",
      remaining: dayResult.remaining,
      resetTime: dayResult.resetTime,
      retryAfter: dayResult.retryAfter
    };
  }

  return {
    allowed: true,
    remaining: Math.min(minuteResult.remaining, hourResult.remaining, dayResult.remaining),
    resetTime: Math.min(minuteResult.resetTime, hourResult.resetTime, dayResult.resetTime)
  };
}

/**
 * Validate domain access for a chatbot
 */
async function validateDomainAccess(chatbot: any, origin: string): Promise<boolean> {
  // If no allowed domains specified, allow all
  if (!chatbot.allowedDomains || chatbot.allowedDomains.length === 0) {
    return true;
  }
  
  try {
    const originDomain = new URL(origin).hostname;
    
    return chatbot.allowedDomains.some((domain: string) => {
      // Support wildcard subdomains
      if (domain.startsWith('*.')) {
        const baseDomain = domain.slice(2);
        return originDomain.endsWith(baseDomain);
      }
      
      // Exact domain match
      if (domain === originDomain) {
        return true;
      }
      
      // Support protocol-less domains
      if (domain.includes('/')) {
        try {
          const allowedDomain = new URL(domain).hostname;
          return allowedDomain === originDomain;
        } catch {
          return false;
        }
      }
      
      return domain === originDomain;
    });
  } catch {
    return false;
  }
}

/**
 * Get rate limit headers for response
 */
export function getRateLimitHeaders(rateLimitInfo?: {
  remaining: number;
  resetTime: number;
  retryAfter?: number;
}): Record<string, string> {
  if (!rateLimitInfo) {
    return {};
  }

  const headers: Record<string, string> = {
    'X-RateLimit-Remaining': rateLimitInfo.remaining.toString(),
    'X-RateLimit-Reset': Math.ceil(rateLimitInfo.resetTime / 1000).toString(),
  };

  if (rateLimitInfo.retryAfter) {
    headers['Retry-After'] = rateLimitInfo.retryAfter.toString();
  }

  return headers;
}

/**
 * Create CORS headers for chatbot responses
 */
export function getCORSHeaders(origin?: string): Record<string, string> {
  return {
    'Access-Control-Allow-Origin': origin || '*',
    'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With',
    'Access-Control-Max-Age': '86400', // 24 hours
    'Access-Control-Expose-Headers': 'X-RateLimit-Remaining, X-RateLimit-Reset, Retry-After',
  };
}

/**
 * Log chatbot usage for analytics
 */
export async function logChatbotUsage(
  chatbotId: string,
  action: 'chat' | 'config' | 'session',
  metadata?: {
    domain?: string;
    userAgent?: string;
    ipAddress?: string;
    responseTime?: number;
    error?: string;
  }
): Promise<void> {
  try {
    // Update chatbot usage count
    await db.chatbot.update({
      where: { id: chatbotId },
      data: {
        usageCount: { increment: 1 },
        monthlyUsage: { increment: 1 },
        lastUsedAt: new Date(),
      },
    });

    // Log detailed usage (could be extended to a separate analytics table)
    console.log(`Chatbot usage: ${chatbotId} - ${action}`, metadata);
  } catch (error) {
    console.error("Error logging chatbot usage:", error);
  }
}
