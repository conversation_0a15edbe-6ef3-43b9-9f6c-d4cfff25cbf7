from fastapi import APIRouter, Depends, HTTPException, status, Query
from typing import Dict, Any, Optional, List
from pydantic import BaseModel
from src.api.deps import get_db
import json
import datetime
from src.services.usage_tracker import UsageTracker
from bson import ObjectId
import logging
import os
import requests
import aiohttp
from langchain_openai import AzureOpenAI, AzureChatOpenAI
from langchain_core.messages import HumanMessage, SystemMessage

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

router = APIRouter()

# Google Search API configuration
GOOGLE_API_KEY = os.getenv("GOOGLE_API_KEY")
GOOGLE_SEARCH_ENGINE_ID = os.getenv("GOOGLE_SEARCH_ENGINE_ID")
GOOGLE_SEARCH_API_URL = "https://www.googleapis.com/customsearch/v1"

# In-memory cache for web search results
# In production, this should be replaced with Redis or another distributed cache
search_cache = {}
CACHE_TTL = 24 * 60 * 60  # 24 hours in seconds

class WebSearchRequest(BaseModel):
    query: str
    max_results: Optional[int] = 5

class WebSearchResponse(BaseModel):
    results: List[Dict[str, Any]]
    cached: bool = False

async def generate_search_queries(query: str, tenant_id: str) -> List[str]:
    """
    Use LLM to generate optimized search queries based on the user's question.

    Args:
        query: The user's original query
        tenant_id: The tenant ID for retrieving LLM settings

    Returns:
        List of optimized search queries
    """
    try:
        # Get LLM settings from database
        db_generator = get_db()
        db = await anext(db_generator)
        llm_settings = await db.LLMSettings.find_one({"tenantId": ObjectId(tenant_id)})

        if not llm_settings:
            logger.warning(f"No LLM settings found for tenant {tenant_id}, using default")
            return [query]

        # Initialize Azure OpenAI client
        llm = AzureChatOpenAI(
            deployment_name=llm_settings.get("azureOpenAIDeployment", os.getenv("AZURE_OPENAI_DEPLOYMENT")),
            azure_endpoint=llm_settings.get("azureOpenAIEndpoint", os.getenv("AZURE_OPENAI_API_ENDPOINT")),
            api_version=llm_settings.get("azureOpenAIApiVersion", os.getenv("AZURE_OPENAI_API_VERSION")),
            api_key=llm_settings.get("apiKey", os.getenv("AZURE_OPENAI_API_KEY")),
            temperature=0.7
        )

        # Create system and user messages
        system_message = SystemMessage(
            content="You are a search query optimizer. Your task is to generate 2-3 effective search queries based on the user's question. "
                   "The queries should be diverse to cover different aspects of the question and formatted to work well with search engines. "
                   "Return only the search queries as a JSON array of strings, nothing else."
        )

        user_message = HumanMessage(content=query)

        # Generate optimized search queries
        response = await llm.ainvoke([system_message, user_message])
        content = response.content

        try:
            # Parse the response as JSON
            parsed_content = json.loads(content)
            if isinstance(parsed_content, list):
                return parsed_content
            elif isinstance(parsed_content, dict) and "queries" in parsed_content:
                return parsed_content["queries"]
            else:
                logger.warning(f"Unexpected response format from LLM: {content}")
                return [query]
        except json.JSONDecodeError:
            # If the response is not valid JSON, try to extract queries using regex
            import re
            queries = re.findall(r'"([^"]+)"', content)
            if queries:
                return queries
            else:
                logger.warning(f"Could not parse LLM response as JSON: {content}")
                return [query]
    except Exception as e:
        logger.error(f"Error generating search queries with LLM: {e}")
        return [query]  # Fall back to the original query

async def perform_web_search(query: str, tenant_id: str, user_id: str, max_results: int = 5) -> Dict[str, Any]:
    """
    Perform a web search using Google Custom Search API with LLM-optimized queries.

    Args:
        query: The search query
        tenant_id: The tenant ID
        user_id: The user ID
        max_results: Maximum number of results to return

    Returns:
        Dictionary with search results and metadata
    """
    try:
        # Check if the tenant has exceeded their daily web search limit
        db_generator = get_db()
        db = await anext(db_generator)
        usage_tracker = UsageTracker(db)
        # limit_check = await usage_tracker.check_web_search_limit(tenant_id)

        # if limit_check.get("limitExceeded", False):
        #     return {
        #         "results": [],
        #         "error": "Daily web search limit exceeded",
        #         "limitExceeded": True,
        #         "limitInfo": limit_check
        #     }

        # Check cache first
        cache_key = f"{query}:{max_results}"
        if cache_key in search_cache:
            cache_entry = search_cache[cache_key]
            # Check if cache entry is still valid
            if datetime.datetime.now().timestamp() - cache_entry["timestamp"] < CACHE_TTL:
                return {
                    "results": cache_entry["results"],
                    "cached": True
                }

        # Generate optimized search queries using LLM
        search_queries = await generate_search_queries(query, tenant_id)
        logger.info(f"Generated search queries: {search_queries}")

        all_results = []

        # Check if Google Search API is configured
        if not GOOGLE_API_KEY or not GOOGLE_SEARCH_ENGINE_ID:
            logger.warning("Google Search API configuration is missing. Using dummy results for testing.")
            # Return dummy results for testing
            return {
                "results": [
                    {
                        "title": "Dummy Search Result 1",
                        "link": "https://example.com/1",
                        "snippet": "This is a dummy search result because Google Search API is not configured.",
                        "displayLink": "example.com",
                        "source": "web",
                        "searchQuery": query
                    },
                    {
                        "title": "Dummy Search Result 2",
                        "link": "https://example.com/2",
                        "snippet": "Please configure GOOGLE_API_KEY and GOOGLE_SEARCH_ENGINE_ID environment variables.",
                        "displayLink": "example.com",
                        "source": "web",
                        "searchQuery": query
                    }
                ],
                "cached": False
            }

        # Perform searches for each generated query
        for search_query in search_queries:

            response = requests.get(
                GOOGLE_SEARCH_API_URL,
                params={
                    "key": GOOGLE_API_KEY,
                    "cx": GOOGLE_SEARCH_ENGINE_ID,
                    "q": search_query,
                    "num": 3  # Reduced number per query since we're doing multiple queries
                }
            )

            if response.status_code != 200:
                logger.error(f"Search API returned status {response.status_code}")
                continue

            # Extract and format the search results
            data = response.json()
            items = data.get("items", [])

            for item in items:
                all_results.append({
                    "title": item.get("title", ""),
                    "link": item.get("link", ""),
                    "snippet": item.get("snippet", ""),
                    "displayLink": item.get("displayLink", ""),
                    "source": "web",
                    "searchQuery": search_query  # Include the query that generated this result
                })

        # Remove duplicates based on URL
        unique_results = []
        seen_urls = set()

        for result in all_results:
            if result["link"] not in seen_urls:
                seen_urls.add(result["link"])
                unique_results.append(result)

        # Limit to max_results
        final_results = unique_results[:max_results]

        # Update cache
        search_cache[cache_key] = {
            "results": final_results,
            "timestamp": datetime.datetime.now().timestamp()
        }

        # Log usage
        # We already have the usage_tracker initialized with the proper db connection
        await usage_tracker.log_web_search_usage(tenant_id, user_id, query)

        return {
            "results": final_results,
            "cached": False
        }
    except Exception as e:
        logger.error(f"Error performing web search: {e}")
        return {
            "results": [],
            "error": str(e)
        }

@router.get("/search", response_model=WebSearchResponse)
async def web_search(
    query: str = Query(..., description="The search query"),
    tenant_id: str = Query(..., description="The tenant ID"),
    user_id: str = Query(..., description="The user ID"),
    max_results: int = Query(5, description="Maximum number of results to return"),
    db = Depends(get_db)
):
    """
    Perform a web search using Google Custom Search API with LLM-optimized queries.
    """
    result = await perform_web_search(query, tenant_id, user_id, max_results)

    if "limitExceeded" in result and result["limitExceeded"]:
        raise HTTPException(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail={
                "error": result.get("error", "Daily web search limit exceeded"),
                "limitExceeded": True,
                "limitInfo": result.get("limitInfo", {})
            }
        )

    return {
        "results": result.get("results", []),
        "cached": result.get("cached", False)
    }
