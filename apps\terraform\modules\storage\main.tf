resource "azurerm_storage_account" "storage" {
  name                     = var.storage_account_name
  resource_group_name      = var.resource_group_name
  location                 = var.location
  account_tier             = var.account_tier
  account_replication_type = var.replication_type

  tags = {
    environment = var.environment
  }

  blob_properties {
    versioning_enabled = true
  }

  lifecycle {
    ignore_changes = [
      blob_properties[0].cors_rule,
    ]
  }
}
