"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useLanguage } from "@/lib/language-context";
import { Search, X } from "lucide-react";

interface User {
  id: string;
  name: string;
  email: string;
  image?: string;
}

interface Workspace {
  id: string;
  name: string;
  slug: string;
}

interface Role {
  id: string;
  name: string;
}

interface Group {
  id: string;
  name: string;
}

interface UserWorkspaceFiltersProps {
  users: User[];
  workspaces: Workspace[];
  roles: Role[];
  groups: Group[];
  onFilterChange: (filters: {
    userId?: string;
    workspaceId?: string;
    role?: string;
    groupId?: string;
    searchTerm?: string;
  }) => void;
}

export function UserWorkspaceFilters({
  users,
  workspaces,
  roles,
  groups,
  onFilterChange,
}: UserWorkspaceFiltersProps) {
  const { t } = useLanguage();
  const [selectedUser, setSelectedUser] = useState<string>("all");
  const [selectedWorkspace, setSelectedWorkspace] = useState<string>("all");
  const [selectedRole, setSelectedRole] = useState<string>("all");
  const [selectedGroup, setSelectedGroup] = useState<string>("all");
  const [searchTerm, setSearchTerm] = useState<string>("");

  const handleFilterChange = () => {
    onFilterChange({
      userId: selectedUser && selectedUser !== "all" ? selectedUser : undefined,
      workspaceId:
        selectedWorkspace && selectedWorkspace !== "all"
          ? selectedWorkspace
          : undefined,
      role: selectedRole && selectedRole !== "all" ? selectedRole : undefined,
      groupId:
        selectedGroup && selectedGroup !== "all" ? selectedGroup : undefined,
      searchTerm: searchTerm || undefined,
    });
  };

  const clearFilters = () => {
    setSelectedUser("all");
    setSelectedWorkspace("all");
    setSelectedRole("all");
    setSelectedGroup("all");
    setSearchTerm("");
    onFilterChange({});
  };

  return (
    <div className="space-y-4 p-4 border rounded-lg bg-card">
      <h3 className="text-lg font-medium">{t("common.filters")}</h3>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
        {/* User filter */}
        <div className="space-y-2">
          <Label htmlFor="user-filter">{t("common.user")}</Label>
          <Select
            value={selectedUser}
            onValueChange={(value) => {
              setSelectedUser(value);
              // Clear group filter if user is selected
              if (value && value !== "all") setSelectedGroup("all");
            }}
          >
            <SelectTrigger id="user-filter">
              <SelectValue placeholder={t("common.selectUser")} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">{t("common.all")}</SelectItem>
              {users.map((user) => (
                <SelectItem key={user.id} value={user.id}>
                  {user.name || user.email}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Workspace filter */}
        <div className="space-y-2">
          <Label htmlFor="workspace-filter">{t("workspace.workspace")}</Label>
          <Select
            value={selectedWorkspace}
            onValueChange={setSelectedWorkspace}
          >
            <SelectTrigger id="workspace-filter">
              <SelectValue placeholder={t("workspace.selectWorkspace")} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">{t("common.all")}</SelectItem>
              {workspaces.map((workspace) => (
                <SelectItem key={workspace.id} value={workspace.id}>
                  {workspace.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Role filter */}
        <div className="space-y-2">
          <Label htmlFor="role-filter">{t("roles.role")}</Label>
          <Select value={selectedRole} onValueChange={setSelectedRole}>
            <SelectTrigger id="role-filter">
              <SelectValue placeholder={t("roles.selectRole")} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">{t("common.all")}</SelectItem>
              <SelectItem value="OWNER">Owner</SelectItem>
              <SelectItem value="ADMIN">Admin</SelectItem>
              <SelectItem value="MEMBER">Member</SelectItem>
              <SelectItem value="CUSTOM">Custom</SelectItem>
              {roles.map((role) => (
                <SelectItem key={role.id} value={`CUSTOM:${role.id}`}>
                  {role.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Group filter */}
        <div className="space-y-2">
          <Label htmlFor="group-filter">{t("groups.group")}</Label>
          <Select
            value={selectedGroup}
            onValueChange={(value) => {
              setSelectedGroup(value);
              // Clear user filter if group is selected
              if (value && value !== "all") setSelectedUser("all");
            }}
          >
            <SelectTrigger id="group-filter">
              <SelectValue placeholder={t("groups.selectGroup")} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">{t("common.all")}</SelectItem>
              {groups.map((group) => (
                <SelectItem key={group.id} value={group.id}>
                  {group.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Search filter */}
        <div className="space-y-2">
          <Label htmlFor="search-filter">{t("common.search")}</Label>
          <div className="flex gap-2">
            <Input
              id="search-filter"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder={t("common.searchPlaceholder")}
            />
          </div>
        </div>
      </div>

      <div className="flex justify-end gap-2">
        <Button
          variant="outline"
          onClick={clearFilters}
          className="flex items-center gap-1"
        >
          <X className="h-4 w-4" />
          {t("common.clear")}
        </Button>
        <Button
          onClick={handleFilterChange}
          className="flex items-center gap-1"
        >
          <Search className="h-4 w-4" />
          {t("common.filter")}
        </Button>
      </div>
    </div>
  );
}
