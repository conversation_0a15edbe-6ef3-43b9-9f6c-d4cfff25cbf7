"use client";

import { useState, useEffect } from "react";
import { useLanguage } from "@/lib/language-context";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import toast from "react-hot-toast";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { SheetFooter } from "@/components/ui/sheet";

// Define interfaces for our data structures
interface Permission {
  id: string;
  action: string;
  resource: string;
  description?: string;
}

interface RolePermission {
  permissionId: string;
  permission?: Permission;
}

interface CustomRole {
  id: string;
  name: string;
  description?: string;
  permissions: RolePermission[];
  tenantId: string;
  createdAt?: string;
  updatedAt?: string;
}

interface RoleFormData {
  name: string;
  description?: string;
  permissionIds: string[];
}

interface RoleFormProps {
  permissions: Permission[];
  initialData?: CustomRole | null;
  onSubmit: (data: RoleFormData) => Promise<void>;
  onCancel: () => void;
}

export default function RoleForm({
  permissions = [],
  initialData = null,
  onSubmit,
  onCancel,
}: RoleFormProps) {
  const { t } = useLanguage();
  const [name, setName] = useState(initialData?.name || "");
  const [description, setDescription] = useState(
    initialData?.description || ""
  );
  const [selectedPermissions, setSelectedPermissions] = useState<string[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Group permissions by resource
  const groupedPermissions = permissions.reduce((acc, permission) => {
    const resource = permission.resource;
    if (!acc[resource]) {
      acc[resource] = [];
    }
    acc[resource].push(permission);
    return acc;
  }, {});

  // Set initial selected permissions
  useEffect(() => {
    if (initialData?.permissions) {
      const permissionIds = initialData.permissions.map((p) => p.permissionId);
      setSelectedPermissions(permissionIds);
    }
  }, [initialData]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      await onSubmit({
        name,
        description,
        permissionIds: selectedPermissions,
      });
    } catch (error) {
      console.error("Error submitting role:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Helper function to find permission by ID
  const findPermissionById = (id: string): Permission | undefined => {
    return permissions.find((p) => p.id === id);
  };

  // Helper function to get all permissions for a specific action and resource
  const getPermissionsByActionAndResource = (
    action: string,
    resource: string
  ): Permission[] => {
    return permissions.filter(
      (p) => p.action === action && p.resource === resource
    );
  };

  // Helper function to get child resources based on hierarchy
  const getChildResources = (resource: string): string[] => {
    const hierarchy = {
      WORKSPACE: ["PAGE", "FOLDER", "FILE"],
      PAGE: ["FOLDER", "FILE"],
      FOLDER: ["FILE"],
      FILE: [],
    };
    return hierarchy[resource] || [];
  };

  // Helper function to apply permission to child resources
  const applyPermissionToChildren = (
    action: string,
    resource: string
  ): string[] => {
    const childResources = getChildResources(resource);
    const childPermissionIds: string[] = [];

    childResources.forEach((childResource) => {
      const childPermissions = getPermissionsByActionAndResource(
        action,
        childResource
      );
      childPermissionIds.push(...childPermissions.map((p) => p.id));
    });

    return childPermissionIds;
  };

  // Helper function to check if a parent resource permission is required
  const requiresParentPermission = (
    permission: Permission
  ): {
    required: boolean;
    parentResource: string;
    parentPermissionId?: string;
  } => {
    const { action, resource } = permission;

    // Define parent relationships
    const parentMap = {
      FILE: "FOLDER",
      FOLDER: "PAGE",
      PAGE: "WORKSPACE",
    };

    const parentResource = parentMap[resource];
    if (!parentResource) return { required: false, parentResource: "" };

    // Find the corresponding parent permission with the same action
    const parentPermission = permissions.find(
      (p) => p.action === action && p.resource === parentResource
    );

    if (!parentPermission) return { required: false, parentResource };

    // Check if the parent permission is selected
    const hasParentPermission = selectedPermissions.includes(
      parentPermission.id
    );

    return {
      required: !hasParentPermission,
      parentResource,
      parentPermissionId: parentPermission.id,
    };
  };

  // Helper function to check if all required parent permissions are selected
  const hasAllRequiredParentPermissions = (permission: Permission): boolean => {
    // If it's a workspace, no parent permissions are required
    if (permission.resource === "WORKSPACE") return true;

    // Check direct parent
    const { required, parentPermissionId } =
      requiresParentPermission(permission);

    // If direct parent permission is required but not selected, return false
    if (required) return false;

    // If parent permission exists, recursively check if its parent permissions are selected
    if (parentPermissionId) {
      const parentPermission = findPermissionById(parentPermissionId);
      if (parentPermission) {
        return hasAllRequiredParentPermissions(parentPermission);
      }
    }

    return true;
  };

  // Helper function to get all affected child permissions when removing a permission
  const getAffectedChildPermissions = (permission: Permission): string[] => {
    // Get direct child permissions
    const childPermissionIds = applyPermissionToChildren(
      permission.action,
      permission.resource
    );

    // For each child resource, find all permissions with the same action
    const allAffectedIds: string[] = [...childPermissionIds];

    // Return all affected permission IDs
    return allAffectedIds;
  };

  const handlePermissionChange = (
    permissionId: string,
    checked: boolean | string
  ) => {
    // Convert to boolean if it's a string
    const isChecked =
      typeof checked === "string" ? checked === "true" : checked;

    const permission = findPermissionById(permissionId);
    if (!permission) return;

    if (isChecked) {
      // Check if all required parent permissions are selected
      if (!hasAllRequiredParentPermissions(permission)) {
        // Get the direct parent resource
        const { parentResource } = requiresParentPermission(permission);

        // Show toast notification about the parent permission requirement
        toast.error(
          t("roles.errorMessages.parentPermissionRequired", {
            action: permission.action,
            parentResource: parentResource
          })
        );
        return;
      }

      // Get child permissions to apply based on hierarchy
      const childPermissionIds = applyPermissionToChildren(
        permission.action,
        permission.resource
      );

      // Add the current permission and all child permissions
      const newPermissions = [permissionId, ...childPermissionIds].filter(
        (id) => !selectedPermissions.includes(id)
      );

      setSelectedPermissions([...selectedPermissions, ...newPermissions]);
    } else {
      // If unchecking a parent permission, also uncheck all child permissions
      const permission = findPermissionById(permissionId);
      if (!permission) return;

      // Get all affected child permissions
      const affectedPermissionIds = getAffectedChildPermissions(permission);

      // Show toast notification about removing child permissions
      if (affectedPermissionIds.length > 0) {
        toast.success(
          t("roles.errorMessages.permissionRemovedWithCascade", {
            action: permission.action,
            resource: permission.resource
          })
        );
      }

      // Remove this permission and all its child permissions
      setSelectedPermissions(
        selectedPermissions.filter(
          (id) => id !== permissionId && !affectedPermissionIds.includes(id)
        )
      );
    }
  };

  // Toggle all permissions for a resource
  const toggleResourcePermissions = (
    resourcePermissions: Permission[],
    checked: boolean | string
  ) => {
    // Convert to boolean if it's a string
    const isChecked =
      typeof checked === "string" ? checked === "true" : checked;

    // Get the resource type from the first permission
    const resource = resourcePermissions[0]?.resource;
    if (!resource) return;

    const permissionIds = resourcePermissions.map((p: Permission) => p.id);

    if (isChecked) {
      // For each permission in this resource, get child permissions
      const allChildPermissionIds: string[] = [];

      resourcePermissions.forEach((permission) => {
        // Check if all required parent permissions are selected
        if (!hasAllRequiredParentPermissions(permission)) {
          // Get the direct parent resource
          const { parentResource } = requiresParentPermission(permission);

          // Show toast notification about the parent permission requirement
          toast.error(
            t("roles.errorMessages.cannotGrantWithoutParent", {
              action: permission.action,
              resource: resource,
              parentResource: parentResource
            })
          );
          return;
        }

        const childPermissionIds = applyPermissionToChildren(
          permission.action,
          permission.resource
        );
        allChildPermissionIds.push(...childPermissionIds);
      });

      // Add all permissions that aren't already selected (including child permissions)
      const newPermissions = [
        ...permissionIds,
        ...allChildPermissionIds,
      ].filter((id: string) => !selectedPermissions.includes(id));

      setSelectedPermissions([...selectedPermissions, ...newPermissions]);
    } else {
      // Get all affected child permissions for each permission in this resource
      const allAffectedPermissionIds: string[] = [];

      resourcePermissions.forEach((permission) => {
        const affectedPermissionIds = getAffectedChildPermissions(permission);
        allAffectedPermissionIds.push(...affectedPermissionIds);
      });

      // Show toast notification about removing child permissions
      if (allAffectedPermissionIds.length > 0) {
        toast.success(
          t("roles.errorMessages.allPermissionsRemovedWithCascade", {
            resource: resource
          })
        );
      }

      // Remove all permissions for this resource and its children
      setSelectedPermissions(
        selectedPermissions.filter(
          (id: string) =>
            !permissionIds.includes(id) &&
            !allAffectedPermissionIds.includes(id)
        )
      );
    }
  };

  // Check if all permissions for a resource are selected
  const areAllResourcePermissionsSelected = (
    resourcePermissions: Permission[]
  ) => {
    const permissionIds = resourcePermissions.map((p: Permission) => p.id);
    return permissionIds.every((id: string) =>
      selectedPermissions.includes(id)
    );
  };

  // Check if a permission can be granted (has all required parent permissions)
  const canGrantPermission = (permission: Permission): boolean => {
    return hasAllRequiredParentPermissions(permission);
  };

  return (
    <form onSubmit={handleSubmit} className="h-full flex flex-col">
      <div className="grid gap-4 py-4 flex-grow overflow-y-auto">
        <div className="grid grid-cols-1 md:grid-cols-4 items-center gap-4">
          <Label htmlFor="name" className="md:text-right">
            {t("roles.name")}
          </Label>
          <Input
            id="name"
            value={name}
            onChange={(e) => setName(e.target.value)}
            className="col-span-1 md:col-span-3"
            required
          />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-4 items-center gap-4">
          <Label htmlFor="description" className="md:text-right">
            {t("roles.description")}
          </Label>
          <Textarea
            id="description"
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            className="col-span-1 md:col-span-3"
          />
        </div>

        <div className="grid grid-cols-1 gap-4 mt-4">
          <div className="flex items-center justify-between">
            <Label>{t("roles.permissions")}</Label>
            <div
              className="text-xs text-blue-600 dark:text-blue-400 cursor-help"
              title={t("roles.tooltips.hierarchyInfo")}
            >
              {t("roles.hierarchy.info")} ℹ️
            </div>
          </div>
          <div className="text-xs text-muted-foreground mb-2 p-2 bg-blue-50 dark:bg-blue-950 border border-blue-200 dark:border-blue-800 rounded-md">
            <div className="flex items-center justify-between">
              <p className="font-medium">
                <span className="text-blue-600 dark:text-blue-400">ℹ️</span>{" "}
                {t("roles.hierarchy.title")}
              </p>
            </div>

            <div className="mt-2">
              <div className="flex flex-col gap-1">
                <div className="flex items-center gap-1">
                  <span className="text-blue-600 dark:text-blue-400">↓</span>
                  <p>
                    <strong>{t("common.inheritance")}:</strong> {t("roles.hierarchy.inheritance")}
                  </p>
                </div>
                <div className="flex items-center gap-1">
                  <span className="text-red-600 dark:text-red-400">✕</span>
                  <p>
                    <strong>{t("common.restriction")}:</strong> {t("roles.hierarchy.restriction")}
                  </p>
                </div>
                <div className="flex items-center gap-1">
                  <span className="text-amber-600 dark:text-amber-400">⚠️</span>
                  <p>
                    <strong>{t("common.required")}:</strong> {t("roles.hierarchy.required")}
                  </p>
                </div>
                <div className="flex items-center gap-1">
                  <span className="text-purple-600 dark:text-purple-400">
                    🔄
                  </span>
                  <p>
                    <strong>{t("common.cascadingRemoval")}:</strong> {t("roles.hierarchy.cascadingRemoval")}
                  </p>
                </div>
              </div>

              <p className="mt-2 text-blue-600 dark:text-blue-400 italic border-t border-blue-200 dark:border-blue-800 pt-1">
                {t("roles.hierarchy.workspaceRemovalNote")}
              </p>
            </div>
          </div>
          <div className="border rounded-md p-2 max-h-[400px] overflow-y-auto">
            <Accordion type="single" className="w-full" collapsible>
              {Object.entries(groupedPermissions).map(
                ([resource, resourcePermissions]) => (
                  <AccordionItem key={resource} value={resource}>
                    <AccordionTrigger className="px-4">
                      <div className="flex items-center gap-2">
                        <Checkbox
                          id={`resource-${resource}`}
                          checked={areAllResourcePermissionsSelected(
                            resourcePermissions as Permission[]
                          )}
                          disabled={
                            resource !== "WORKSPACE" &&
                            (resourcePermissions as Permission[]).some(
                              (p) =>
                                !canGrantPermission(p) &&
                                !selectedPermissions.includes(p.id)
                            )
                          }
                          onCheckedChange={(checked) =>
                            toggleResourcePermissions(
                              resourcePermissions as Permission[],
                              checked
                            )
                          }
                          onClick={(e) => e.stopPropagation()}
                        />
                        <Label
                          htmlFor={`resource-${resource}`}
                          onClick={(e) => e.stopPropagation()}
                          className={
                            resource !== "WORKSPACE" &&
                            (resourcePermissions as Permission[]).some(
                              (p) =>
                                !canGrantPermission(p) &&
                                !selectedPermissions.includes(p.id)
                            )
                              ? "text-muted-foreground"
                              : ""
                          }
                        >
                          {t(`roles.resources.${resource}`)}
                          {resource === "WORKSPACE" && (
                            <span className="ml-2 text-xs text-blue-600 dark:text-blue-400">
                              (L1)
                            </span>
                          )}
                          {resource === "PAGE" && (
                            <span className="ml-2 text-xs text-green-600 dark:text-green-400">
                              (L2)
                            </span>
                          )}
                          {resource === "FOLDER" && (
                            <span className="ml-2 text-xs text-amber-600 dark:text-amber-400">
                              (L3)
                            </span>
                          )}
                          {resource === "FILE" && (
                            <span className="ml-2 text-xs text-purple-600 dark:text-purple-400">
                              (L4)
                            </span>
                          )}
                          {resource !== "WORKSPACE" &&
                            (resourcePermissions as Permission[]).some(
                              (p) =>
                                !canGrantPermission(p) &&
                                !selectedPermissions.includes(p.id)
                            ) && (
                              <span
                                className="ml-2 text-xs text-red-600 dark:text-red-400"
                                title={t("roles.tooltips.missingParentPermissions")}
                              >
                                🔒
                              </span>
                            )}
                        </Label>
                      </div>
                    </AccordionTrigger>
                    <AccordionContent className="px-2 pt-1 pb-0">
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-1 mb-2">
                        {(resourcePermissions as Permission[]).map(
                          (permission: Permission) => (
                            <div
                              key={permission.id}
                              className="flex items-center space-x-2"
                            >
                              <Checkbox
                                id={permission.id}
                                checked={selectedPermissions.includes(
                                  permission.id
                                )}
                                disabled={
                                  !canGrantPermission(permission) &&
                                  !selectedPermissions.includes(permission.id)
                                }
                                onCheckedChange={(checked) =>
                                  handlePermissionChange(permission.id, checked)
                                }
                              />
                              <Label
                                htmlFor={permission.id}
                                className={
                                  !canGrantPermission(permission) &&
                                  !selectedPermissions.includes(permission.id)
                                    ? "text-muted-foreground"
                                    : ""
                                }
                              >
                                {permission.action ? t(`roles.actions.${permission.action}`) : "Invalid Action"}
                                {(permission.resource === "PAGE" ||
                                  permission.resource === "FOLDER" ||
                                  permission.resource === "FILE") && (
                                  <span className="ml-1 text-xs text-blue-600 dark:text-blue-400">
                                    ↓
                                  </span>
                                )}
                                {!canGrantPermission(permission) &&
                                  !selectedPermissions.includes(
                                    permission.id
                                  ) && (
                                    <span
                                      className="ml-1 text-xs text-red-600 dark:text-red-400"
                                      title={t("roles.tooltips.missingParentPermission")}
                                    >
                                      🔒
                                    </span>
                                  )}
                              </Label>
                            </div>
                          )
                        )}
                      </div>
                    </AccordionContent>
                  </AccordionItem>
                )
              )}
            </Accordion>
          </div>
        </div>
      </div>
      <div className="mt-6 sticky bottom-0 bg-background pt-2 border-t">
        <SheetFooter>
          <Button type="button" variant="outline" onClick={onCancel}>
            {t("common.cancel")}
          </Button>
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting
              ? t("common.saving")
              : initialData
                ? t("common.save")
                : t("common.create")}
          </Button>
        </SheetFooter>
      </div>
    </form>
  );
}
