import { addAuthHeaders } from "@/lib/api/auth-token";

/**
 * Fetch JSON data with authentication
 *
 * @param url - The URL to fetch
 * @returns The parsed JSON response
 */
export const fetchJsonWithAuth = async (url: string) => {
  try {
    // Get authentication headers
    const headers = await addAuthHeaders();

    // Make the request with authentication headers
    const response = await fetch(url, {
      method: "GET",
      headers,
      credentials: "include", // Important for cookies to be sent
    });

    if (!response.ok) {
      throw new Error(
        `Network error: ${response.status} ${response.statusText}`
      );
    }

    return await response.json();
  } catch (error) {
    console.log(`Error fetching data from ${url}`, { error });
    throw error;
  }
};

/**
 * Make a POST request with authentication
 *
 * @param url - The URL to post to
 * @param data - The data to send
 * @returns The parsed JSON response
 */
export const postWithAuth = async (url: string, data: any) => {
  try {
    // Get authentication headers
    const headers = await addAuthHeaders({
      "Content-Type": "application/json",
    });

    // Make the request with authentication headers
    const response = await fetch(url, {
      method: "POST",
      headers,
      body: JSON.stringify(data),
      credentials: "include", // Important for cookies to be sent
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(
        errorData.error ||
          `Network error: ${response.status} ${response.statusText}`
      );
    }

    return await response.json();
  } catch (error) {
    console.log(`Error posting data to ${url}`, { error });
    throw error;
  }
};

/**
 * Make a PUT request with authentication
 *
 * @param url - The URL to put to
 * @param data - The data to send
 * @returns The parsed JSON response
 */
export const putWithAuth = async (url: string, data: any) => {
  try {
    // Get authentication headers
    const headers = await addAuthHeaders({
      "Content-Type": "application/json",
    });

    // Make the request with authentication headers
    const response = await fetch(url, {
      method: "PUT",
      headers,
      body: JSON.stringify(data),
      credentials: "include", // Important for cookies to be sent
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(
        errorData.error ||
          `Network error: ${response.status} ${response.statusText}`
      );
    }

    return await response.json();
  } catch (error) {
    console.log(`Error putting data to ${url}`, { error });
    throw error;
  }
};

/**
 * Make a DELETE request with authentication
 *
 * @param url - The URL to delete
 * @returns The parsed JSON response
 */
export const deleteWithAuth = async (url: string) => {
  try {
    // Get authentication headers
    const headers = await addAuthHeaders();

    // Make the request with authentication headers
    const response = await fetch(url, {
      method: "DELETE",
      headers,
      credentials: "include", // Important for cookies to be sent
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(
        errorData.error ||
          `Network error: ${response.status} ${response.statusText}`
      );
    }

    return await response.json();
  } catch (error) {
    console.log(`Error deleting data from ${url}`, { error });
    throw error;
  }
};
