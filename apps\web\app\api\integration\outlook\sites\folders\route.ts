import { NextResponse } from "next/server";
import { getAuthenticatedClient } from "@/lib/microsoft-graph";

// List folders in a SharePoint site
export async function GET(request: Request) {
  try {
    const url = new URL(request.url);
    const tenantId = url.searchParams.get("tenantId");
    const siteId = url.searchParams.get("siteId");
    const driveId = url.searchParams.get("driveId");
    const folderId = url.searchParams.get("folderId");
    const userId = url.searchParams.get("userId") ?? "";
    const skipToken = url.searchParams.get("skipToken");

    if (!tenantId || !siteId) {
      return NextResponse.json(
        { error: "Tenant ID and Site ID are required" },
        { status: 400 }
      );
    }

    const client = await getAuthenticatedClient(tenantId, userId);

    // If driveId is not provided, get the default document library
    if (!driveId) {
      // Get the default document library for the site
      const siteResponse = await client.api(`/sites/${siteId}/drive`).get();

      const defaultDriveId = siteResponse.id;

      // Now list folders in the default document library
      let apiPath = folderId
        ? `/sites/${siteId}/drives/${defaultDriveId}/items/${folderId}/children`
        : `/sites/${siteId}/drives/${defaultDriveId}/root/children`;

      if (skipToken) {
        apiPath += `?$skiptoken=${skipToken}`;
      }

      const response = await client
        .api(apiPath)
        .select("id,name,folder,parentReference,size,lastModifiedDateTime")
        .filter("folder ne null")
        .get();

      return NextResponse.json(response, { status: 200 });
    } else {
      // Use the provided driveId
      let apiPath = folderId
        ? `/sites/${siteId}/drives/${driveId}/items/${folderId}/children`
        : `/sites/${siteId}/drives/${driveId}/root/children`;

      if (skipToken) {
        apiPath += `?$skiptoken=${skipToken}`;
      }

      const response = await client
        .api(apiPath)
        .select("id,name,folder,parentReference,size,lastModifiedDateTime")
        .filter("folder ne null")
        .get();

      return NextResponse.json(response, { status: 200 });
    }
  } catch (error) {
    console.error("Error listing site folders:", error);
    return NextResponse.json(
      { error: "Failed to list site folders" },
      { status: 500 }
    );
  }
}

// Create a folder in a SharePoint site
export async function POST(request: Request) {
  try {
    const { name, siteId, driveId, folderId, tenantId, userId } =
      await request.json();

    if (!name || !tenantId || !siteId) {
      return NextResponse.json(
        { error: "Folder name, Site ID, and Tenant ID are required" },
        { status: 400 }
      );
    }

    const client = await getAuthenticatedClient(tenantId, userId);

    // If driveId is not provided, get the default document library
    const targetDriveId =
      driveId || (await client.api(`/sites/${siteId}/drive`).get()).id;

    const driveItem = {
      name,
      folder: {},
      "@microsoft.graph.conflictBehavior": "rename",
    };

    // Create folder in the specified location
    const apiPath = folderId
      ? `/sites/${siteId}/drives/${targetDriveId}/items/${folderId}/children`
      : `/sites/${siteId}/drives/${targetDriveId}/root/children`;

    const folder = await client.api(apiPath).post(driveItem);

    return NextResponse.json({ folder }, { status: 201 });
  } catch (error) {
    console.error("Error creating folder:", error);
    return NextResponse.json(
      { error: "Failed to create folder" },
      { status: 500 }
    );
  }
}
