import aiohttp
import asyncio
import logging
import traceback
from typing import Dict, Any, List, Optional
from urllib.parse import urljoin, urlparse, urlunsplit
from bs4 import BeautifulSoup
import xml.etree.ElementTree as ET
from urllib.robotparser import RobotFileParser
import markdownify
import re
import ssl
from aiohttp import ClientResponseError, ClientConnectorError, ClientConnectorDNSError

# Configure logger with more detailed formatting
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
)
logger = logging.getLogger(__name__)

class WebScraperService:
    """
    Advanced web scraping service with robots.txt handling, 
    content extraction, and format conversion capabilities.
    """
    
    def __init__(self):
        self.session = None
        self.user_agent = "Swiss Knowledge Hub Bot/1.0"
        
    async def __aenter__(self):
        """Async context manager entry"""
        # Create SSL context that's more permissive for web scraping
        ssl_context = ssl.create_default_context()
        ssl_context.check_hostname = False
        ssl_context.verify_mode = ssl.CERT_NONE

        # Create connector with SSL context and connection limits
        connector = aiohttp.TCPConnector(
            ssl=ssl_context,
            limit=100,
            limit_per_host=10,
            ttl_dns_cache=300,
            use_dns_cache=True,
        )

        self.session = aiohttp.ClientSession(
            connector=connector,
            timeout=aiohttp.ClientTimeout(
                total=60,  # Increased total timeout
                connect=10,  # Connection timeout
                sock_read=30  # Socket read timeout
            ),
            headers={
                "User-Agent": self.user_agent,
                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
                "Accept-Language": "en-US,en;q=0.5",
                "Accept-Encoding": "gzip, deflate",
                "Connection": "keep-alive",
                "Upgrade-Insecure-Requests": "1",
            }
        )
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.session:
            await self.session.close()
    
    async def check_robots_txt(self, url: str) -> Dict[str, Any]:
        """
        Check if the URL is allowed by robots.txt
        Returns dict with allowed status and robots.txt content
        """
        try:
            parsed_url = urlparse(url)
            if not parsed_url.scheme or not parsed_url.netloc:
                logger.warning(f"Invalid URL format for robots.txt check: {url}")
                return {
                    "robots_exists": False,
                    "can_fetch": True,
                    "robots_content": None,
                    "robots_url": None,
                    "error": "Invalid URL format"
                }

            robots_url = f"{parsed_url.scheme}://{parsed_url.netloc}/robots.txt"
            logger.debug(f"Checking robots.txt at: {robots_url}")

            async with self.session.get(robots_url) as response:
                if response.status == 200:
                    robots_content = await response.text()
                    logger.debug(f"Found robots.txt for {url}")

                    # Parse robots.txt
                    rp = RobotFileParser()
                    rp.set_url(robots_url)
                    rp.read = lambda: None  # Override read method
                    rp.parse(robots_content.splitlines())

                    # Check if our user agent can fetch the URL
                    can_fetch = rp.can_fetch(self.user_agent, url)
                    logger.debug(f"Robots.txt allows fetching {url}: {can_fetch}")

                    return {
                        "robots_exists": True,
                        "can_fetch": can_fetch,
                        "robots_content": robots_content,
                        "robots_url": robots_url
                    }
                else:
                    # No robots.txt found, assume allowed
                    logger.debug(f"No robots.txt found for {url} (status: {response.status})")
                    return {
                        "robots_exists": False,
                        "can_fetch": True,
                        "robots_content": None,
                        "robots_url": robots_url
                    }

        except (ClientConnectorError, ClientConnectorDNSError) as e:
            logger.warning(f"Connection error checking robots.txt for {url}: {e}")
            return {
                "robots_exists": False,
                "can_fetch": True,
                "robots_content": None,
                "robots_url": None,
                "error": f"Connection error: {str(e)}"
            }
        except asyncio.TimeoutError as e:
            logger.warning(f"Timeout checking robots.txt for {url}: {e}")
            return {
                "robots_exists": False,
                "can_fetch": True,
                "robots_content": None,
                "robots_url": None,
                "error": f"Timeout: {str(e)}"
            }
        except Exception as e:
            logger.warning(f"Unexpected error checking robots.txt for {url}: {e}")
            logger.debug(f"Full traceback: {traceback.format_exc()}")
            # If we can't check robots.txt, assume allowed
            return {
                "robots_exists": False,
                "can_fetch": True,
                "robots_content": None,
                "robots_url": None,
                "error": str(e)
            }
    
    async def extract_content(
        self,
        url: str,
        content_cleaning_level: str = "basic",
        output_format: str = "html",
        bypass_robots: bool = False,
        crawl_depth: int = 1
    ) -> Dict[str, Any]:
        """
        Extract content from a URL with advanced title extraction and format conversion
        """
        step = "initialization"
        try:
            logger.info(f"Starting content extraction for URL: {url}")

            # Validate URL format
            step = "url_validation"
            parsed_url = urlparse(url)
            if not parsed_url.scheme or not parsed_url.netloc:
                error_msg = f"Invalid URL format: {url}"
                logger.error(error_msg)
                return {
                    "success": False,
                    "error": error_msg,
                    "step_failed": step
                }

            # Check robots.txt first
            step = "robots_check"
            logger.debug(f"Checking robots.txt for {url}")
            robots_check = await self.check_robots_txt(url)

            if not robots_check["can_fetch"] and not bypass_robots:
                error_msg = "URL is blocked by robots.txt"
                logger.warning(f"{error_msg}: {url}")
                return {
                    "success": False,
                    "error": error_msg,
                    "robots_info": robots_check,
                    "step_failed": step
                }

            # Fetch the page
            step = "http_request"
            logger.debug(f"Fetching content from {url}")
            try:
                async with self.session.get(url) as response:
                    if response.status != 200:
                        error_msg = f"HTTP {response.status}: {response.reason}"
                        logger.error(f"HTTP error for {url}: {error_msg}")
                        return {
                            "success": False,
                            "error": error_msg,
                            "step_failed": step,
                            "http_status": response.status
                        }

                    # Check content type
                    content_type = response.headers.get('content-type', '').lower()
                    if not any(ct in content_type for ct in ['text/html', 'application/xhtml', 'text/plain']):
                        error_msg = f"Unsupported content type: {content_type}"
                        logger.warning(f"Unsupported content type for {url}: {content_type}")
                        return {
                            "success": False,
                            "error": error_msg,
                            "step_failed": step,
                            "content_type": content_type
                        }

                    html_content = await response.text()
                    logger.debug(f"Successfully fetched {len(html_content)} characters from {url}")

            except asyncio.TimeoutError as e:
                error_msg = f"Request timeout: {str(e)}"
                logger.error(f"Timeout error for {url}: {error_msg}")
                return {
                    "success": False,
                    "error": error_msg,
                    "step_failed": step,
                    "error_type": "timeout"
                }
            except (ClientConnectorError, ClientConnectorDNSError) as e:
                error_msg = f"Connection error: {str(e)}"
                logger.error(f"Connection error for {url}: {error_msg}")
                return {
                    "success": False,
                    "error": error_msg,
                    "step_failed": step,
                    "error_type": "connection"
                }
            except ClientResponseError as e:
                error_msg = f"HTTP response error: {e.status} {e.message}"
                logger.error(f"Response error for {url}: {error_msg}")
                return {
                    "success": False,
                    "error": error_msg,
                    "step_failed": step,
                    "error_type": "http_response",
                    "http_status": e.status
                }

            # Parse with BeautifulSoup
            step = "html_parsing"
            logger.debug(f"Parsing HTML content for {url}")
            try:
                soup = BeautifulSoup(html_content, 'html.parser')
                if not soup:
                    raise ValueError("BeautifulSoup returned empty soup object")
            except Exception as e:
                error_msg = f"HTML parsing error: {str(e)}"
                logger.error(f"HTML parsing failed for {url}: {error_msg}")
                return {
                    "success": False,
                    "error": error_msg,
                    "step_failed": step,
                    "error_type": "parsing"
                }

            # Extract content components
            step = "content_extraction"
            logger.debug(f"Extracting content components for {url}")
            try:
                title = self._extract_title(soup, url)
                description = self._extract_description(soup)
                content = self._extract_main_content(soup, content_cleaning_level)
                headings = self._extract_headings(soup)

                # Validate extracted content quality
                validation = self._validate_extracted_content(title, content)
                if not validation["is_valid"]:
                    logger.warning(f"Content quality issues for {url}: {validation['issues']}")
                    # Don't fail completely, but log the issues

                if not content or len(content.strip()) < 50:
                    error_msg = "Insufficient content extracted from page"
                    logger.error(f"{error_msg}: {url}")
                    return {
                        "success": False,
                        "error": error_msg,
                        "step_failed": step,
                        "error_type": "insufficient_content",
                        "validation": validation
                    }

            except Exception as e:
                error_msg = f"Content extraction error: {str(e)}"
                logger.error(f"Content extraction failed for {url}: {error_msg}")
                return {
                    "success": False,
                    "error": error_msg,
                    "step_failed": step,
                    "error_type": "content_extraction"
                }

            # Check for sitemap
            step = "sitemap_check"
            logger.debug(f"Checking sitemap for {url}")
            sitemap_info = await self._check_sitemap(url)

            # Handle crawl depth > 1 by discovering links and checking sitemap
            additional_urls = []
            if crawl_depth > 1:
                # First, try to discover links from the main page content
                try:
                    discovered_urls = self._discover_links_from_content(soup, url, crawl_depth)
                    additional_urls.extend(discovered_urls)
                    logger.debug(f"Found {len(discovered_urls)} additional URLs from page content")
                except Exception as e:
                    logger.warning(f"Failed to discover links from page content: {e}")

                # Then, try sitemap as additional source
                if sitemap_info.get("has_sitemap"):
                    try:
                        sitemap_urls = await self.extract_sitemap_urls(sitemap_info["sitemap_url"])
                        # Limit sitemap URLs and avoid duplicates
                        for sitemap_url in sitemap_urls:
                            if sitemap_url not in additional_urls and len(additional_urls) < 20:
                                additional_urls.append(sitemap_url)
                        logger.debug(f"Added {len(sitemap_urls)} URLs from sitemap (total: {len(additional_urls)})")
                    except Exception as e:
                        logger.warning(f"Failed to extract sitemap URLs for crawl depth: {e}")

                # Limit total URLs based on crawl depth
                max_urls = min(crawl_depth * 5, 20)
                additional_urls = additional_urls[:max_urls]

            # Format content based on output format
            step = "content_formatting"
            logger.debug(f"Formatting content for {url} as {output_format}")
            try:
                if output_format == "markdown":
                    formatted_content = self._convert_to_markdown(content, title, description)
                    file_extension = "md"
                    content_type = "text/markdown"
                else:
                    formatted_content = self._create_html_document(content, title, description)
                    file_extension = "html"
                    content_type = "text/html"
            except Exception as e:
                error_msg = f"Content formatting error: {str(e)}"
                logger.error(f"Content formatting failed for {url}: {error_msg}")
                return {
                    "success": False,
                    "error": error_msg,
                    "step_failed": step,
                    "error_type": "formatting"
                }

            logger.info(f"Successfully extracted content from {url}")
            return {
                "success": True,
                "url": url,
                "title": title,
                "description": description,
                "content": formatted_content,
                "headings": headings,
                "format": output_format,
                "file_extension": file_extension,
                "content_type": content_type,
                "content_cleaning_applied": content_cleaning_level,
                "crawl_depth": crawl_depth,
                "additional_urls": additional_urls,
                "robots_info": robots_check,
                **sitemap_info
            }

        except Exception as e:
            error_msg = f"Unexpected error in step '{step}': {str(e)}"
            logger.error(f"Unexpected error extracting content from {url}: {error_msg}")
            logger.error(f"Full traceback: {traceback.format_exc()}")
            return {
                "success": False,
                "error": error_msg,
                "step_failed": step,
                "error_type": "unexpected",
                "traceback": traceback.format_exc() if logger.isEnabledFor(logging.DEBUG) else None
            }
    
    def _extract_title(self, soup: BeautifulSoup, url: str) -> str:
        """Extract title with multiple fallback methods"""
        # Try title tag first
        title_tag = soup.find('title')
        if title_tag and title_tag.get_text().strip():
            return title_tag.get_text().strip()
        
        # Try h1 tag
        h1_tag = soup.find('h1')
        if h1_tag and h1_tag.get_text().strip():
            return h1_tag.get_text().strip()
        
        # Try og:title meta tag
        og_title = soup.find('meta', property='og:title')
        if og_title and og_title.get('content', '').strip():
            return og_title.get('content').strip()
        
        # Try twitter:title meta tag
        twitter_title = soup.find('meta', attrs={'name': 'twitter:title'})
        if twitter_title and twitter_title.get('content', '').strip():
            return twitter_title.get('content').strip()
        
        # Last fallback: extract from URL
        try:
            parsed_url = urlparse(url)
            path_segments = [seg for seg in parsed_url.path.split('/') if seg]
            if path_segments:
                title = path_segments[-1]
                # Clean up the title
                title = re.sub(r'\.(html?|php|aspx?)$', '', title, flags=re.IGNORECASE)
                title = re.sub(r'[-_]', ' ', title)
                title = ' '.join(word.capitalize() for word in title.split())
                return title
        except:
            pass
        
        return "Untitled"
    
    def _extract_description(self, soup: BeautifulSoup) -> str:
        """Extract meta description"""
        # Try meta description
        meta_desc = soup.find('meta', attrs={'name': 'description'})
        if meta_desc and meta_desc.get('content', '').strip():
            return meta_desc.get('content').strip()
        
        # Try og:description
        og_desc = soup.find('meta', property='og:description')
        if og_desc and og_desc.get('content', '').strip():
            return og_desc.get('content').strip()
        
        return ""
    
    def _extract_headings(self, soup: BeautifulSoup) -> List[str]:
        """Extract all headings from the page"""
        headings = []
        for heading in soup.find_all(['h1', 'h2', 'h3', 'h4', 'h5', 'h6']):
            text = heading.get_text().strip()
            if text:
                headings.append(text)
        return headings
    
    def _extract_main_content(self, soup: BeautifulSoup, cleaning_level: str) -> str:
        """Extract main content with cleaning based on level"""
        try:
            # Make a copy to avoid modifying the original
            soup_copy = BeautifulSoup(str(soup), 'html.parser')

            # Remove unwanted elements
            unwanted_tags = ['script', 'style', 'nav', 'header', 'footer', 'aside', 'noscript']
            for tag in unwanted_tags:
                for element in soup_copy.find_all(tag):
                    element.decompose()

            # Try to find main content area with multiple strategies
            main_content = None

            # Strategy 1: Look for semantic HTML5 elements
            main_content = soup_copy.find('main')
            if not main_content:
                main_content = soup_copy.find('article')

            # Strategy 2: Look for common content class names
            if not main_content:
                content_selectors = [
                    'div[class*="content"]',
                    'div[class*="main"]',
                    'div[class*="article"]',
                    'div[id*="content"]',
                    'div[id*="main"]',
                    'div[class*="post"]',
                    'div[class*="entry"]'
                ]
                for selector in content_selectors:
                    main_content = soup_copy.select_one(selector)
                    if main_content:
                        break

            # Strategy 3: Find the div with the most text content
            if not main_content:
                divs = soup_copy.find_all('div')
                if divs:
                    main_content = max(divs, key=lambda div: len(div.get_text().strip()))

            # Strategy 4: Fallback to body
            if not main_content:
                main_content = soup_copy.find('body')

            # Final fallback
            if not main_content:
                main_content = soup_copy

            # Apply cleaning based on level
            if cleaning_level == "medium":
                # Remove navigation, ads, and other non-content elements
                unwanted_elements = ['nav', 'aside', 'footer', 'header']
                for tag in unwanted_elements:
                    for element in main_content.find_all(tag):
                        element.decompose()

                # Remove elements with unwanted class names
                unwanted_classes = re.compile(r'nav|menu|sidebar|ad|advertisement|social|share|cookie|popup')
                for element in main_content.find_all(class_=unwanted_classes):
                    element.decompose()

            elif cleaning_level == "aggressive":
                # More aggressive cleaning
                unwanted_elements = ['nav', 'aside', 'footer', 'header', 'form', 'iframe', 'embed', 'object']
                for tag in unwanted_elements:
                    for element in main_content.find_all(tag):
                        element.decompose()

                # Remove elements with unwanted class names or IDs
                unwanted_patterns = re.compile(r'nav|menu|sidebar|ad|advertisement|social|share|comment|related|widget|popup|modal|cookie|banner')
                for element in main_content.find_all(class_=unwanted_patterns):
                    element.decompose()
                for element in main_content.find_all(id=unwanted_patterns):
                    element.decompose()

                # Remove elements with very little text (but keep images and media)
                for element in main_content.find_all():
                    if element.name not in ['img', 'video', 'audio', 'picture', 'figure']:
                        text_content = element.get_text().strip()
                        if len(text_content) < 20 and not element.find_all(['img', 'video', 'audio']):
                            element.decompose()

            # Clean up empty elements
            for element in main_content.find_all():
                if not element.get_text().strip() and not element.find_all(['img', 'video', 'audio', 'br', 'hr']):
                    element.decompose()

            result = str(main_content)

            # Validate that we have some content
            if not result or len(result.strip()) < 10:
                logger.warning("Very little content extracted after cleaning")
                # Return the original soup as fallback
                return str(soup)

            return result

        except Exception as e:
            logger.error(f"Error in content extraction: {e}")
            # Return the original soup as fallback
            return str(soup)
    
    def _convert_to_markdown(self, html_content: str, title: str, description: str) -> str:
        """Convert HTML content to Markdown"""
        # Use markdownify for better conversion
        markdown_content = markdownify.markdownify(html_content, heading_style="ATX")
        
        # Create final markdown document
        final_content = f"# {title}\n\n"
        if description:
            final_content += f"{description}\n\n"
        final_content += markdown_content
        
        return final_content
    
    def _create_html_document(self, content: str, title: str, description: str) -> str:
        """Create a complete HTML document"""
        return f"""<!DOCTYPE html>
<html>
<head>
    <title>{title}</title>
    <meta name="description" content="{description}">
    <meta charset="UTF-8">
</head>
<body>
    <h1>{title}</h1>
    {f'<p>{description}</p>' if description else ''}
    {content}
</body>
</html>"""
    
    async def _check_sitemap(self, url: str) -> Dict[str, Any]:
        """Check if the site has a sitemap with multiple common locations"""
        try:
            parsed_url = urlparse(url)
            if not parsed_url.scheme or not parsed_url.netloc:
                return {
                    "has_sitemap": False,
                    "sitemap_url": None
                }

            # Common sitemap locations to check
            sitemap_locations = [
                f"{parsed_url.scheme}://{parsed_url.netloc}/sitemap.xml",
                f"{parsed_url.scheme}://{parsed_url.netloc}/sitemap_index.xml",
                f"{parsed_url.scheme}://{parsed_url.netloc}/sitemaps.xml",
                f"{parsed_url.scheme}://{parsed_url.netloc}/sitemap/sitemap.xml",
            ]

            for sitemap_url in sitemap_locations:
                try:
                    logger.debug(f"Checking sitemap at: {sitemap_url}")
                    async with self.session.get(sitemap_url) as response:
                        if response.status == 200:
                            # Verify it's actually XML content
                            content_type = response.headers.get('content-type', '').lower()
                            if 'xml' in content_type or 'text' in content_type:
                                logger.debug(f"Found sitemap at: {sitemap_url}")
                                return {
                                    "has_sitemap": True,
                                    "sitemap_url": sitemap_url
                                }
                except Exception as e:
                    logger.debug(f"Error checking sitemap at {sitemap_url}: {e}")
                    continue

            logger.debug(f"No sitemap found for {url}")
            return {
                "has_sitemap": False,
                "sitemap_url": None
            }

        except Exception as e:
            logger.warning(f"Error checking sitemap for {url}: {e}")
            return {
                "has_sitemap": False,
                "sitemap_url": None
            }
    
    async def extract_sitemap_urls(self, sitemap_url: str) -> List[str]:
        """Extract URLs from a sitemap with improved error handling"""
        try:
            logger.debug(f"Extracting URLs from sitemap: {sitemap_url}")

            async with self.session.get(sitemap_url) as response:
                if response.status != 200:
                    logger.warning(f"Sitemap request failed with status {response.status}: {sitemap_url}")
                    return []

                # Check content type
                content_type = response.headers.get('content-type', '').lower()
                if 'xml' not in content_type and 'text' not in content_type:
                    logger.warning(f"Unexpected content type for sitemap {sitemap_url}: {content_type}")

                sitemap_content = await response.text()

                if not sitemap_content.strip():
                    logger.warning(f"Empty sitemap content from {sitemap_url}")
                    return []

            # Parse XML sitemap
            try:
                root = ET.fromstring(sitemap_content)
            except ET.ParseError as e:
                logger.error(f"XML parsing error for sitemap {sitemap_url}: {e}")
                return []

            urls = []

            # Handle different sitemap formats
            # Try with namespace first
            namespace = '{http://www.sitemaps.org/schemas/sitemap/0.9}'
            for url_elem in root.findall(f'.//{namespace}url'):
                loc_elem = url_elem.find(f'{namespace}loc')
                if loc_elem is not None and loc_elem.text:
                    url = loc_elem.text.strip()
                    if url:
                        urls.append(url)

            # If no URLs found, try without namespace
            if not urls:
                for url_elem in root.findall('.//url'):
                    loc_elem = url_elem.find('loc')
                    if loc_elem is not None and loc_elem.text:
                        url = loc_elem.text.strip()
                        if url:
                            urls.append(url)

            # Check for sitemap index (sitemaps containing other sitemaps)
            if not urls:
                for sitemap_elem in root.findall(f'.//{namespace}sitemap'):
                    loc_elem = sitemap_elem.find(f'{namespace}loc')
                    if loc_elem is not None and loc_elem.text:
                        nested_sitemap_url = loc_elem.text.strip()
                        if nested_sitemap_url:
                            logger.debug(f"Found nested sitemap: {nested_sitemap_url}")
                            # Recursively extract from nested sitemap (limit depth to avoid infinite loops)
                            try:
                                nested_urls = await self.extract_sitemap_urls(nested_sitemap_url)
                                urls.extend(nested_urls[:50])  # Limit nested URLs
                            except Exception as e:
                                logger.warning(f"Error extracting from nested sitemap {nested_sitemap_url}: {e}")

            # Remove duplicates and validate URLs
            unique_urls = []
            seen = set()
            for url in urls:
                if url not in seen and self._is_valid_url(url):
                    unique_urls.append(url)
                    seen.add(url)

            logger.debug(f"Extracted {len(unique_urls)} unique URLs from sitemap {sitemap_url}")
            return unique_urls

        except asyncio.TimeoutError as e:
            logger.error(f"Timeout extracting sitemap URLs from {sitemap_url}: {e}")
            return []
        except (ClientConnectorError, ClientConnectorDNSError) as e:
            logger.error(f"Connection error extracting sitemap URLs from {sitemap_url}: {e}")
            return []
        except Exception as e:
            logger.error(f"Unexpected error extracting sitemap URLs from {sitemap_url}: {e}")
            logger.debug(f"Full traceback: {traceback.format_exc()}")
            return []

    def _is_valid_url(self, url: str) -> bool:
        """Validate if a URL is properly formatted"""
        try:
            parsed = urlparse(url)
            return bool(parsed.scheme and parsed.netloc)
        except Exception:
            return False

    def _discover_links_from_content(self, soup: BeautifulSoup, base_url: str, crawl_depth: int) -> List[str]:
        """Discover additional URLs from page content for deep crawling"""
        try:
            discovered_urls = []
            base_parsed = urlparse(base_url)
            base_domain = f"{base_parsed.scheme}://{base_parsed.netloc}"

            # Find all links in the page
            links = soup.find_all('a', href=True)

            for link in links:
                href = link.get('href', '').strip()
                if not href:
                    continue

                # Convert relative URLs to absolute
                if href.startswith('/'):
                    full_url = base_domain + href
                elif href.startswith('http'):
                    full_url = href
                elif not href.startswith('#') and not href.startswith('mailto:') and not href.startswith('tel:'):
                    # Relative URL
                    full_url = urljoin(base_url, href)
                else:
                    continue

                # Only include URLs from the same domain
                try:
                    parsed_url = urlparse(full_url)
                    if parsed_url.netloc != base_parsed.netloc:
                        continue

                    # Remove fragments and query parameters for cleaner URLs
                    clean_url = f"{parsed_url.scheme}://{parsed_url.netloc}{parsed_url.path}"

                    # Avoid duplicates and the original URL
                    if clean_url != base_url and clean_url not in discovered_urls:
                        # Filter out common non-content URLs and file extensions
                        path_lower = parsed_url.path.lower()
                        skip_patterns = [
                            '/admin', '/login', '/logout', '/register', '/search',
                            '/contact', '/privacy', '/terms', '/sitemap', '/api/',
                            '/wp-admin', '/wp-login', '/wp-content', '/assets/',
                            '/static/', '/css/', '/js/', '/images/', '/img/',
                            '.pdf', '.doc', '.docx', '.zip', '.exe', '.dmg',
                            '.jpg', '.jpeg', '.png', '.gif', '.svg', '.ico',
                            '.css', '.js', '.xml', '.json', '.rss', '.atom'
                        ]
                        if not any(skip in path_lower for skip in skip_patterns):
                            # Additional validation: prefer URLs that look like content pages
                            link_text = link.get_text().strip().lower()

                            # Boost priority for content-like URLs
                            is_content_url = any(indicator in path_lower or indicator in link_text for indicator in [
                                'article', 'post', 'blog', 'news', 'story', 'guide', 'tutorial',
                                'about', 'service', 'product', 'page', 'content'
                            ])

                            # Skip URLs that are clearly navigation or utility
                            is_utility_url = any(indicator in link_text for indicator in [
                                'home', 'back', 'next', 'previous', 'menu', 'navigation',
                                'skip', 'top', 'bottom', 'sidebar'
                            ]) and len(link_text) < 20

                            if not is_utility_url:
                                if is_content_url:
                                    # Insert content URLs at the beginning
                                    discovered_urls.insert(0, clean_url)
                                else:
                                    discovered_urls.append(clean_url)

                                # Limit based on crawl depth
                                if len(discovered_urls) >= crawl_depth * 3:
                                    break

                except Exception as e:
                    logger.debug(f"Error processing link {href}: {e}")
                    continue

            logger.debug(f"Discovered {len(discovered_urls)} valid URLs from page content")
            return discovered_urls

        except Exception as e:
            logger.warning(f"Error discovering links from content: {e}")
            return []

    def _validate_extracted_content(self, title: str, content: str) -> Dict[str, Any]:
        """Validate the quality of extracted content"""
        issues = []

        # Check title
        if not title or title == "Untitled":
            issues.append("No meaningful title found")
        elif len(title) > 200:
            issues.append("Title is unusually long")

        # Check content length
        text_content = BeautifulSoup(content, 'html.parser').get_text().strip()
        if len(text_content) < 100:
            issues.append("Very little text content extracted")
        elif len(text_content) < 500:
            issues.append("Limited text content extracted")

        # Check for common error pages
        error_indicators = [
            "404", "not found", "page not found", "error", "access denied",
            "forbidden", "unauthorized", "maintenance", "temporarily unavailable"
        ]

        title_lower = title.lower()
        content_lower = text_content.lower()

        for indicator in error_indicators:
            if indicator in title_lower or indicator in content_lower[:500]:
                issues.append(f"Content may be an error page (contains '{indicator}')")
                break

        # Check content-to-HTML ratio (detect pages with too much markup)
        if len(text_content) > 0:
            markup_ratio = len(content) / len(text_content)
            if markup_ratio > 10:
                issues.append("High markup-to-content ratio detected")

        return {
            "is_valid": len(issues) == 0,
            "issues": issues,
            "text_length": len(text_content),
            "html_length": len(content),
            "title_length": len(title)
        }
