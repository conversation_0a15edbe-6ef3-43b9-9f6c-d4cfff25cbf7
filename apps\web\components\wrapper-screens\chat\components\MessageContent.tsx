import React from "react";
import ReactMarkdown from "react-markdown";
import { Source } from "../types";
import { CitationProcessor } from "@/lib/utils/citation-processor";
import { CodeBlock } from "./CodeBlock";
import { DownloadableContent } from "./DownloadableContent";

interface MessageContentProps {
  content: string;
  sources?: Source[];
  onCitationClick: (source: Source, highlightedText?: string) => void;
}

/**
 * Component that renders markdown content with processed citations
 */
export const MessageContent: React.FC<MessageContentProps> = ({
  content,
  sources = [],
  onCitationClick,
}) => {
  // Create citation processor
  const processor = new CitationProcessor(sources, onCitationClick);

  // Check if content has citations (supports [D1], [W1], and legacy [1] formats)
  const hasCitations = /\[(D|W)?\d+\]/.test(content);

  // Check if content has downloadable files (data URLs) - more flexible pattern
  const hasDownloadableContent = React.useMemo(() => {
    // Check for markdown link format: [Download filename](data:...)
    const markdownLinkPattern = /\[Download [^[\]]+\]\(data:[^)]+\)/;
    // Check for data URLs anywhere in content
    const dataUrlPattern = /data:[^;\s]+;[^,\s]+,[A-Za-z0-9+/=]+/;
    // Check for download text followed by data URL (more flexible)
    const flexiblePattern =
      /\[Download [^[\]]+\][\s\S]*?data:[^;\s]+;[^,\s]+,[A-Za-z0-9+/=]+/;

    return (
      markdownLinkPattern.test(content) ||
      (content.includes("[Download") && dataUrlPattern.test(content)) ||
      flexiblePattern.test(content)
    );
  }, [content]);

  if (!hasCitations || sources.length === 0) {
    // No citations, check for downloadable content
    if (hasDownloadableContent) {
      return (
        <DownloadableContent content={content?.replace(/\\n/g, "\n") || ""} />
      );
    }

    // Regular markdown with code block support
    return (
      <ReactMarkdown
        className="markdown overflow-x-auto max-w-full"
        components={{
          code: ({ inline, children, className, ...props }: any) => (
            <CodeBlock inline={inline} className={className} {...props}>
              {children}
            </CodeBlock>
          ),
          pre: ({ children }: any) => <>{children}</>, // Let CodeBlock handle the pre wrapper
        }}
      >
        {content?.replace(/\\n/g, "\n")}
      </ReactMarkdown>
    );
  }

  // Helper function to process children recursively
  const processChildren = (children: React.ReactNode): React.ReactNode => {
    return React.Children.map(children, (child) => {
      if (typeof child === "string") {
        const segments = processor.processText(child);
        return processor.renderProcessedText(segments);
      }
      return child;
    });
  };

  // For content with citations, we need to process each text node
  // Custom components for ReactMarkdown to handle citations
  const components = {
    // Process text nodes to convert citation markers in all text-containing elements
    p: ({ children, ...props }: any) => (
      <p {...props}>{processChildren(children)}</p>
    ),
    span: ({ children, ...props }: any) => (
      <span {...props}>{processChildren(children)}</span>
    ),
    li: ({ children, ...props }: any) => (
      <li {...props}>{processChildren(children)}</li>
    ),
    h1: ({ children, ...props }: any) => (
      <h1 {...props}>{processChildren(children)}</h1>
    ),
    h2: ({ children, ...props }: any) => (
      <h2 {...props}>{processChildren(children)}</h2>
    ),
    h3: ({ children, ...props }: any) => (
      <h3 {...props}>{processChildren(children)}</h3>
    ),
    h4: ({ children, ...props }: any) => (
      <h4 {...props}>{processChildren(children)}</h4>
    ),
    h5: ({ children, ...props }: any) => (
      <h5 {...props}>{processChildren(children)}</h5>
    ),
    h6: ({ children, ...props }: any) => (
      <h6 {...props}>{processChildren(children)}</h6>
    ),
    strong: ({ children, ...props }: any) => (
      <strong {...props}>{processChildren(children)}</strong>
    ),
    em: ({ children, ...props }: any) => (
      <em {...props}>{processChildren(children)}</em>
    ),
    blockquote: ({ children, ...props }: any) => (
      <blockquote {...props}>{processChildren(children)}</blockquote>
    ),
    td: ({ children, ...props }: any) => (
      <td {...props}>{processChildren(children)}</td>
    ),
    th: ({ children, ...props }: any) => (
      <th {...props}>{processChildren(children)}</th>
    ),
    // Add code block support with copy functionality
    code: ({ inline, children, className, ...props }: any) => (
      <CodeBlock inline={inline} className={className} {...props}>
        {children}
      </CodeBlock>
    ),
    pre: ({ children }: any) => <>{children}</>, // Let CodeBlock handle the pre wrapper
  };

  // Handle content with both citations and downloadable content
  if (hasDownloadableContent) {
    return (
      <DownloadableContent content={content?.replace(/\\n/g, "\n") || ""} />
    );
  }

  return (
    <ReactMarkdown
      className="markdown overflow-x-auto max-w-full"
      components={components}
    >
      {content?.replace(/\\n/g, "\n")}
    </ReactMarkdown>
  );
};
