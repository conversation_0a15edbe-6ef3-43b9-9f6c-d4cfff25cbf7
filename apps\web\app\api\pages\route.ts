import { NextResponse } from "next/server";
import { getToken } from "next-auth/jwt";
import { NextRequest } from "next/server";
import db from "@/lib/shared-db";
import { withPermission } from "@/lib/permission-middleware";
import { hasPermission } from "@/lib/permissions";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/next-auth/src/auth-options";
import { BlobServiceClient } from "@azure/storage-blob";
import { getTranslations } from "@/lib/server-i18n";
import jwt from "jsonwebtoken";

// Create a new page
const createPage = async (request: NextRequest) => {
  try {
    // Get userId and tenantId from headers
    const userIdFromHeader = request.headers.get("x-user-id");

    // Fallback to session if headers are not available
    const session: any = await getServerSession(authOptions);
    const userId = userIdFromHeader || session?.userId || session?.user?.email;

    if (!userId) {
      return NextResponse.json(
        { error: "Unauthorized - userId is required" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { name, tenantId, workspaceSlug: rawWorkspaceSlug } = body;
    const workspaceSlug = rawWorkspaceSlug
      ? decodeURI(rawWorkspaceSlug)
      : rawWorkspaceSlug;

    if (!name || !tenantId) {
      return NextResponse.json(
        { error: "Title, content, and tenantId are required" },
        { status: 400 }
      );
    }

    // Verify user has access to this tenant
    const membership = await db.membership.findFirst({
      where: {
        userId,
        tenantId,
      },
    });

    if (!membership) {
      return NextResponse.json(
        { error: "You don't have access to this tenant" },
        { status: 403 }
      );
    }

    const workspaceId = await db.workspace.findFirst({
      where: {
        slug: workspaceSlug,
        tenantId: tenantId,
      },
    });

    if (!workspaceId?.id) {
      return NextResponse.json(
        { error: "Workspace Not Found" },
        { status: 403 }
      );
    }
    const page = await db.page.create({
      data: {
        name,
        workspaceId: workspaceId?.id,
      },
    });

    return NextResponse.json({
      data: page,
      message: "Page created successfully",
    });
  } catch (error) {
    console.error("Create page error:", error);
    return NextResponse.json(
      { error: "Failed to create page" },
      { status: 500 }
    );
  }
};

// Get all pages or a specific page
const getPages = async (request: NextRequest) => {
  try {
    // Get userId and tenantId from headers
    const userIdFromHeader = request.headers.get("x-user-id");
    const tenantIdFromHeader = request.headers.get("x-tenant-id");

    // Fallback to session if headers are not available
    const session: any = await getServerSession(authOptions);
    const userId = userIdFromHeader || session?.userId || session?.user?.email;

    const { searchParams } = new URL(request.url);
    const pageId = searchParams.get("pageId");
    const tenantIdFromParams = searchParams.get("tenantId");

    // Use tenantId from header or fallback to query parameter
    const tenantId = tenantIdFromHeader || tenantIdFromParams;

    const rawWorkspaceSlug = searchParams.get("workspaceSlug");
    const workspaceSlug = rawWorkspaceSlug
      ? decodeURI(rawWorkspaceSlug)
      : rawWorkspaceSlug;

    if (!tenantId) {
      return NextResponse.json(
        {
          error:
            "TenantId is required in x-tenant-id header or query parameters",
        },
        { status: 400 }
      );
    }

    if (!userId) {
      return NextResponse.json(
        { error: "Unauthorized - userId is required" },
        { status: 401 }
      );
    }
    if (!workspaceSlug) {
      return NextResponse.json(
        { error: "Workspace slug is required" },
        { status: 400 }
      );
    }

    if (pageId) {
      const [page, workspaceMember, membership] = await Promise.all([
        db.page.findFirst({
          where: {
            id: pageId,
            workspace: {
              tenantId: tenantId,
              slug: workspaceSlug,
            },
          },
          include: {
            folders: {
              include: {
                childRelations: true,
                parentRelations: true,
              },
              where: {
                childRelations: {
                  none: {},
                },
              },
            },
            files: {
              where: {
                parentId: pageId,
              },
            },
          },
        }),

        db.workspaceMember.findMany({
          where: {
            userId,
            workspace: {
              slug: workspaceSlug,
            },
          },
          include: {
            customRole: {
              include: {
                permissions: {
                  include: {
                    permission: true,
                  },
                },
              },
            },
          },
        }),
        db.membership.findMany({
          where: {
            userId,
            tenantId,
          },
        }),
      ]);
      const isOwnerOrAdmin =
        membership?.[0]?.role === "OWNER" || membership?.[0]?.role === "ADMIN";

      if (!page) {
        return NextResponse.json({ error: "Page not found" }, { status: 404 });
      }

      // Use the updated hasPermission function that respects built-in role configurations
      const workspacePermissions = {
        create: await hasPermission(userId, tenantId, "CREATE", "WORKSPACE", workspaceSlug),
        read: await hasPermission(userId, tenantId, "READ", "WORKSPACE", workspaceSlug),
        update: await hasPermission(userId, tenantId, "UPDATE", "WORKSPACE", workspaceSlug),
        delete: await hasPermission(userId, tenantId, "DELETE", "WORKSPACE", workspaceSlug),
      };

      return NextResponse.json({
        data: page,
        permission: {
          workspace: workspacePermissions,
          folder: {
            create: await hasPermission(userId, tenantId, "CREATE", "FOLDER", workspaceSlug),
            read: await hasPermission(userId, tenantId, "READ", "FOLDER", workspaceSlug),
            update: await hasPermission(userId, tenantId, "UPDATE", "FOLDER", workspaceSlug),
            delete: await hasPermission(userId, tenantId, "DELETE", "FOLDER", workspaceSlug),
          },
          file: {
            create: await hasPermission(userId, tenantId, "CREATE", "FILE", workspaceSlug),
            read: await hasPermission(userId, tenantId, "READ", "FILE", workspaceSlug),
            update: await hasPermission(userId, tenantId, "UPDATE", "FILE", workspaceSlug),
            delete: await hasPermission(userId, tenantId, "DELETE", "FILE", workspaceSlug),
          },
          page: {
            create: await hasPermission(userId, tenantId, "CREATE", "PAGE", workspaceSlug),
            read: await hasPermission(userId, tenantId, "READ", "PAGE", workspaceSlug),
            update: await hasPermission(userId, tenantId, "UPDATE", "PAGE", workspaceSlug),
            delete: await hasPermission(userId, tenantId, "DELETE", "PAGE", workspaceSlug),
          },
        },
      });
    }

    const [pages, workspaceMember, membership] = await Promise.all([
      db.page.findMany({
        where: {
          workspace: {
            slug: workspaceSlug,
            tenantId: tenantId,
          },
        },
        orderBy: {
          createdAt: "desc",
        },
      }),
      db.workspaceMember.findMany({
        where: {
          userId,
          workspace: {
            slug: workspaceSlug,
          },
        },
        include: {
          customRole: {
            include: {
              permissions: {
                include: {
                  permission: true,
                },
              },
            },
          },
        },
      }),
      db.membership.findMany({
        where: {
          userId,
          tenantId,
        },
      }),
    ]);

    const isOwnerOrAdmin =
      membership?.[0]?.role === "OWNER" || membership?.[0]?.role === "ADMIN";

    // Use the updated hasPermission function that respects built-in role configurations
    const listPermissions = {
      workspace: {
        create: await hasPermission(userId, tenantId, "CREATE", "WORKSPACE", workspaceSlug),
        read: await hasPermission(userId, tenantId, "READ", "WORKSPACE", workspaceSlug),
        update: await hasPermission(userId, tenantId, "UPDATE", "WORKSPACE", workspaceSlug),
        delete: await hasPermission(userId, tenantId, "DELETE", "WORKSPACE", workspaceSlug),
      },
      page: {
        create: await hasPermission(userId, tenantId, "CREATE", "PAGE", workspaceSlug),
        read: await hasPermission(userId, tenantId, "READ", "PAGE", workspaceSlug),
        update: await hasPermission(userId, tenantId, "UPDATE", "PAGE", workspaceSlug),
        delete: await hasPermission(userId, tenantId, "DELETE", "PAGE", workspaceSlug),
      },
      folder: {
        create: await hasPermission(userId, tenantId, "CREATE", "FOLDER", workspaceSlug),
        read: await hasPermission(userId, tenantId, "READ", "FOLDER", workspaceSlug),
        update: await hasPermission(userId, tenantId, "UPDATE", "FOLDER", workspaceSlug),
        delete: await hasPermission(userId, tenantId, "DELETE", "FOLDER", workspaceSlug),
      },
      file: {
        create: await hasPermission(userId, tenantId, "CREATE", "FILE", workspaceSlug),
        read: await hasPermission(userId, tenantId, "READ", "FILE", workspaceSlug),
        update: await hasPermission(userId, tenantId, "UPDATE", "FILE", workspaceSlug),
        delete: await hasPermission(userId, tenantId, "DELETE", "FILE", workspaceSlug),
      },
    };

    return NextResponse.json({
      data: pages,
      permission: listPermissions,
    });
  } catch (error) {
    console.error("Get pages error:", error);
    return NextResponse.json(
      { error: "Failed to fetch pages" },
      { status: 500 }
    );
  }
};

// Update a page
const updatePage = async (request: NextRequest) => {
  try {
    // Get userId and tenantId from headers
    const userIdFromHeader = request.headers.get("x-user-id");
    const tenantIdFromHeader = request.headers.get("x-tenant-id");

    // Fallback to session if headers are not available
    const session: any = await getServerSession(authOptions);
    const userId = userIdFromHeader || session?.userId || session?.user?.email;

    if (!userId) {
      return NextResponse.json(
        { error: "Unauthorized - userId is required" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const {
      id,
      tenantId: tenantIdFromBody,
      workspaceSlug: rawWorkspaceSlug,
      ...data
    } = body;
    const workspaceSlug = rawWorkspaceSlug
      ? decodeURI(rawWorkspaceSlug)
      : rawWorkspaceSlug;

    // Use tenantId from header or fallback to body parameter
    const tenantId = tenantIdFromHeader || tenantIdFromBody;

    if (!id || !tenantId) {
      return NextResponse.json(
        {
          error:
            "Page ID and tenantId are required in x-tenant-id header or request body",
        },
        { status: 400 }
      );
    }

    // Verify user has access to this tenant
    const membership = await db.membership.findFirst({
      where: {
        userId,
        tenantId: tenantId,
      },
    });

    if (!membership) {
      return NextResponse.json(
        { error: "You don't have access to this tenant" },
        { status: 403 }
      );
    }

    // Check if page exists and belongs to the tenant
    const existingPage = await db.page.findFirst({
      where: {
        id,
        workspace: {
          tenantId: tenantId,
          slug: workspaceSlug,
        },
      },
    });

    if (!existingPage) {
      return NextResponse.json({ error: "Page not found" }, { status: 404 });
    }

    const updatedPage = await db.page.update({
      where: { id },
      data,
    });

    return NextResponse.json({
      data: updatedPage,
      message: "Page updated successfully",
    });
  } catch (error) {
    console.error("Update page error:", error);
    return NextResponse.json(
      { error: "Failed to update page" },
      { status: 500 }
    );
  }
};

// Delete a page
const deletePage = async (request: NextRequest) => {
  try {
    // Get userId and tenantId from headers
    const userIdFromHeader = request.headers.get("x-user-id");
    const tenantIdFromHeader = request.headers.get("x-tenant-id");
    const workspaceSlug = request.headers.get("x-workspace-slug");

    // Fallback to session if headers are not available
    const session: any = await getServerSession(authOptions);
    const userId = userIdFromHeader || session?.userId || session?.user?.email;

    if (!userId) {
      return NextResponse.json(
        { error: "Unauthorized - userId is required" },
        { status: 401 }
      );
    }
    const payload = {
      userId: session.userId,
      email: session.user?.email,
      name: session.user?.name,
      // Include the current organization if available
      organizationId: session.currentOrganization?.id,
      // Add any other information you need
      iat: Math.floor(Date.now() / 1000),
    };

    // Generate a token directly
    const secret = process.env.NEXTAUTH_SECRET;
    if (!secret) {
      throw new Error("NEXTAUTH_SECRET is not defined");
    }

    // Create a token that expires in 1 hour
    const token = jwt.sign(payload, secret, { expiresIn: "1h" });

    const { searchParams } = new URL(request.url);
    const pageId = searchParams.get("id");
    const tenantIdFromParams = searchParams.get("tenantId");
    // Use tenantId from header or fallback to query parameter
    const tenantId = tenantIdFromHeader || tenantIdFromParams;

    if (!pageId || !tenantId) {
      return NextResponse.json(
        {
          error:
            "Page ID and tenantId are required in x-tenant-id header or query parameters",
        },
        { status: 400 }
      );
    }

    // Verify user has access to this tenant
    const membership = await db.membership.findFirst({
      where: {
        userId,
        tenantId,
      },
    });

    if (!membership) {
      return NextResponse.json(
        { error: "You don't have access to this tenant" },
        { status: 403 }
      );
    }

    // Check if page exists and belongs to the tenant
    const existingPage = await db.page.findFirst({
      where: {
        id: pageId,
      },
      include: {
        files: true,
      },
    });

    if (!existingPage) {
      return NextResponse.json({ error: "Page not found" }, { status: 404 });
    }
    const files = existingPage.files;
    await Promise.all(
      files.map(async (file) => {
        // Delete from multiple storage platforms
        try {
          const vectorDeleteResponse = await fetch(
            `${
              process.env.NEXT_PUBLIC_API_BASE_URL || "http://localhost:8000"
            }/api/v1/workspace-chat/delete-file?file_id=${
              file.id
            }&workspaceSlug=${encodeURIComponent(
              workspaceSlug || ""
            )}&tenant_id=${tenantId}`,
            {
              method: "GET",
              headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${token}`,
              },
            }
          );

          if (!vectorDeleteResponse.ok) {
            console.error(`Failed to delete vectors for file ${file.id}`);
          }

          // Delete from Azure Blob Storage
          const blobServiceClient = BlobServiceClient.fromConnectionString(
            process.env.AZURE_STORAGE_CONNECTION_STRING || ""
          );
          const containerClient = blobServiceClient.getContainerClient(
            process.env.AZURE_STORAGE_CONTAINER_NAME || ""
          );
          const parts = decodeURIComponent(
            file?.url?.split("/").slice(-3)?.join("/")
          );
          const blobClient = containerClient.getBlobClient(parts || "");
          await blobClient.delete();
        } catch (storageError) {
          console.error("Error deleting file from storage:", storageError);
          const { t } = getTranslations();
          return NextResponse.json(
            { error: t("api.errors.failedToDeleteFile") },
            { status: 500 }
          );
        }
      })
    );

    const deletedItem = await db.$transaction(async (tx) => {
      const page = await tx.page.delete({
        where: { id: pageId },
      });
      await tx.pageMember.deleteMany({
        where: { pageId },
      });
      await tx.folder.deleteMany({
        where: { pageId },
      });
      await tx.file.deleteMany({
        where: { pageId },
      });
      return { page };
    });
    if (!deletedItem) {
      console.error("Error deleting page:", deletedItem);
      const { t } = getTranslations();
      return NextResponse.json(
        { error: t("api.errors.failedToDeletePage") },
        { status: 500 }
      );
    }

    return NextResponse.json({
      message: "Page deleted successfully",
    });
  } catch (error) {
    console.error("Delete page error:", error);
    return NextResponse.json(
      { error: "Failed to delete page" },
      { status: 500 }
    );
  }
};

// Export the handlers with permission checks
export const GET = withPermission(getPages, "READ", "PAGE");
export const POST = withPermission(createPage, "CREATE", "PAGE");
export const PUT = withPermission(updatePage, "UPDATE", "PAGE");
export const DELETE = withPermission(deletePage, "DELETE", "PAGE");
