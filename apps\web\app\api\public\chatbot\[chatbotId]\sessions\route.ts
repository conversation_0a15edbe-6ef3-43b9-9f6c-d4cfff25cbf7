import { NextRequest, NextResponse } from "next/server";
import db from "@/lib/shared-db";
import { validateChatbotSecurity } from "@/lib/security/chatbot-security";
import {
  applyCORSHeaders,
  handleCORSPreflight,
  createChatbotCORSConfig,
} from "@/lib/security/cors";

interface ChatbotSessionsResponse {
  sessions: {
    id: string;
    sessionToken: string;
    createdAt: string;
    updatedAt: string;
    messagesCount: number;
    messages?: {
      role: string;
      content: string;
      createdAt: string;
    }[];
  }[];
}

/**
 * Get user's chat sessions for a specific chatbot
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { chatbotId: string } }
) {
  try {
    const { chatbotId } = params;

    // Get API key from Authorization header
    const authHeader = request.headers.get("authorization");
    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      const response = NextResponse.json(
        { error: "Missing or invalid API key" },
        { status: 401 }
      );
      return applyCORSHeaders(response, request, createChatbotCORSConfig({}));
    }

    const apiKey = authHeader.substring(7);

    // Validate security and authenticate the request
    const securityResult = await validateChatbotSecurity(
      request,
      chatbotId,
      apiKey
    );

    if (!securityResult.success) {
      const response = NextResponse.json(
        { error: securityResult.error },
        { status: securityResult.statusCode || 500 }
      );
      return applyCORSHeaders(response, request, createChatbotCORSConfig({}));
    }

    const { chatbot } = securityResult;

    // For private chatbots, ensure user is authenticated
    if (chatbot.access === "private" && !securityResult.user) {
      const response = NextResponse.json(
        { error: "Authentication required for private chatbot sessions" },
        { status: 401 }
      );
      return applyCORSHeaders(response, request, createChatbotCORSConfig(chatbot));
    }

    // Get user's chat sessions
    const sessions = await db.chatbotSession.findMany({
      where: {
        chatbotId: chatbotId,
        tenantId: chatbot.tenantId, // Ensure tenant isolation
        userId: securityResult.user?.userId || securityResult.user?.id,
      },
      select: {
        id: true,
        sessionToken: true,
        createdAt: true,
        updatedAt: true,
        messagesCount: true,
        messages: {
          select: {
            role: true,
            content: true,
            createdAt: true,
          },
          orderBy: {
            createdAt: 'desc'
          },
          take: 1, // Only get the last message for preview
        },
      },
      orderBy: {
        updatedAt: 'desc'
      },
      take: 20, // Limit to last 20 sessions
    });

    const response: ChatbotSessionsResponse = {
      sessions: sessions.map(session => ({
        id: session.id,
        sessionToken: session.sessionToken,
        createdAt: session.createdAt.toISOString(),
        updatedAt: session.updatedAt.toISOString(),
        messagesCount: session.messagesCount,
        messages: session.messages,
      })),
    };

    const nextResponse = NextResponse.json(response);
    return applyCORSHeaders(nextResponse, request, createChatbotCORSConfig(chatbot));
  } catch (error) {
    console.error("Error fetching user sessions:", error);
    const response = NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
    return applyCORSHeaders(response, request, createChatbotCORSConfig({}));
  }
}

// Handle CORS preflight requests
export async function OPTIONS(request: NextRequest) {
  return handleCORSPreflight(request, {
    allowedMethods: ["GET", "OPTIONS"],
    allowedHeaders: [
      "Content-Type",
      "Authorization",
      "X-Requested-With",
      "X-User-Token",
    ],
    maxAge: 86400,
  });
}
