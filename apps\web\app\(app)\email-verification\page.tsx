"use client";

import { useState } from "react";
import { useSearch<PERSON>ara<PERSON>, useRouter } from "next/navigation";
import { Mail, Loader2 } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter,
} from "@/components/ui/card";
import Link from "next/link";
import { resendVerificationEmail } from "@/services/src/auth";
import toast from "react-hot-toast";
import { useLanguage } from "@/lib/language-context";

export default function EmailVerificationPage() {
  const [isResending, setIsResending] = useState(false);
  const searchParams = useSearchParams();
  const router = useRouter();
  const { t } = useLanguage();

  const email = searchParams?.get("email") || "";

  const handleResendVerification = async () => {
    if (!email) {
      toast.error(t("verification.emailRequired"));
      return;
    }

    setIsResending(true);
    toast.loading(t("verification.sendingEmail"));

    try {
      const response = await resendVerificationEmail(email);

      toast.dismiss();
      setIsResending(false);

      if (response?.error) {
        toast.error(response.error);
        return;
      }

      toast.success(t("verification.emailSentSuccess"));
    } catch (error) {
      toast.dismiss();
      setIsResending(false);
      console.error("Resend verification error:", error);
      toast.error(t("verification.resendFailed"));
    }
  };

  return (
    <div className="flex min-h-svh w-full items-center justify-center p-6 md:p-10">
      <div className="w-full max-w-md">
        <Card>
          <CardHeader>
            <div className="flex justify-center mb-4">
              <div className="bg-primary/10 p-3 rounded-full">
                <Mail className="h-10 w-10 text-primary" />
              </div>
            </div>
            <CardTitle className="text-2xl text-center">
              {t("verification.verifyEmail")}
            </CardTitle>
            <CardDescription className="text-center">
              {email
                ? t("verification.sentEmailTo", { email })
                : t("verification.checkEmail")}
            </CardDescription>
          </CardHeader>
          <CardContent className="text-center">
            <div className="space-y-4">
              <p className="text-sm text-muted-foreground">
                {t("verification.instructions")}
              </p>

              <div className="bg-muted/50 rounded p-4 text-sm">
                <p className="font-medium mb-2">
                  {t("verification.cantFindEmail")}
                </p>
                <ul className="text-xs text-muted-foreground space-y-1 text-left list-disc list-inside">
                  <li>{t("verification.checkSpam")}</li>
                  <li>{t("verification.checkEmailCorrect")}</li>
                  <li>{t("verification.allowTime")}</li>
                </ul>
              </div>
            </div>
          </CardContent>
          <CardFooter className="flex flex-col gap-2">
            {email && (
              <Button
                variant="outline"
                onClick={handleResendVerification}
                disabled={isResending}
                className="w-full"
              >
                {isResending ? (
                  <Loader2 className="animate-spin mr-2" />
                ) : (
                  <Mail className="mr-2 h-4 w-4" />
                )}
                {t("verification.resendEmail")}
              </Button>
            )}
            <Button
              asChild
              className="w-full"
              variant={email ? "default" : "outline"}
            >
              <Link href="/sign-in">{t("verification.backToSignIn")}</Link>
            </Button>
          </CardFooter>
        </Card>
      </div>
    </div>
  );
}
