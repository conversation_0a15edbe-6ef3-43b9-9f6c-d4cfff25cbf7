import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/next-auth";
import db from "@/lib/shared-db";
import { objectsToCSV } from "@/lib/export-utils";

export async function GET(request: Request) {
  const session = await getServerSession(authOptions);

  if (!session) {
    return NextResponse.json(
      { error: "Unauthorized" },
      { status: 401 }
    );
  }

  try {
    const { searchParams } = new URL(request.url);
    const fromDate = searchParams.get("from") ? new Date(searchParams.get("from") as string) : new Date(new Date().setDate(new Date().getDate() - 30));
    const toDate = searchParams.get("to") ? new Date(searchParams.get("to") as string) : new Date();
    
    // Get all users with their details
    const users = await db.user.findMany({
      where: {
        createdAt: {
          lte: toDate
        }
      },
      include: {
        Membership: {
          include: {
            tenant: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });
    
    // Get API requests for each user
    const userIds = users.map(user => user.id);
    const apiRequests = await db.aPIRequest.groupBy({
      by: ['userId'],
      where: {
        userId: {
          in: userIds
        },
        timestamp: {
          gte: fromDate,
          lte: toDate
        }
      },
      _count: {
        _all: true
      }
    });
    
    // Get token usage for each user
    const tokenUsage = await db.tokenUsage.groupBy({
      by: ['userId'],
      where: {
        userId: {
          in: userIds
        },
        timestamp: {
          gte: fromDate,
          lte: toDate
        }
      },
      _sum: {
        inputTokens: true,
        outputTokens: true,
        cost: true
      }
    });
    
    // Format the data for CSV export
    const exportData = users.map(user => {
      // Find API requests for this user
      const apiRequest = apiRequests.find(req => req.userId === user.id);
      
      // Find token usage for this user
      const tokens = tokenUsage.find(usage => usage.userId === user.id);
      
      // Get primary membership
      const primaryMembership = user.Membership[0];
      
      return {
        id: user.id,
        name: user.name,
        email: user.email,
        createdAt: user.createdAt.toISOString(),
        lastLogin: user.lastLogin ? user.lastLogin.toISOString() : null,
        primaryTenant: primaryMembership?.tenant?.name || 'None',
        role: primaryMembership?.role || 'None',
        membershipCount: user.Membership.length,
        apiRequestCount: apiRequest?._count._all || 0,
        inputTokens: tokens?._sum.inputTokens || 0,
        outputTokens: tokens?._sum.outputTokens || 0,
        totalTokens: (tokens?._sum.inputTokens || 0) + (tokens?._sum.outputTokens || 0),
        tokenCost: tokens?._sum.cost || 0
      };
    });
    
    // Define CSV headers
    const headers = [
      'id',
      'name',
      'email',
      'createdAt',
      'lastLogin',
      'primaryTenant',
      'role',
      'membershipCount',
      'apiRequestCount',
      'inputTokens',
      'outputTokens',
      'totalTokens',
      'tokenCost'
    ];
    
    // Generate CSV content
    const csvContent = objectsToCSV(exportData, headers);
    
    // Return CSV content
    return new NextResponse(csvContent, {
      headers: {
        'Content-Type': 'text/csv',
        'Content-Disposition': `attachment; filename="user_metrics.csv"`
      }
    });
  } catch (error) {
    console.error("Error exporting user metrics:", error);
    return NextResponse.json(
      { error: "Failed to export user metrics" },
      { status: 500 }
    );
  }
}
