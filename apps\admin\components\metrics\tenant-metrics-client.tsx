"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { formatNumber } from "@/lib/utils";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  XAxis,
  <PERSON>A<PERSON>s,
  <PERSON><PERSON>ianGrid,
  <PERSON>ltip,
  <PERSON><PERSON>ons<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
} from "recharts";

// Types (matching the server component)
interface TenantMetrics {
  totalActiveTenants: number;
  totalTenants: number;
  newTenants: number;
  growthRate: number;
}

interface TenantActivity {
  totalTenants: number;
  activeTenants: number;
  inactiveTenants: number;
  onboardedTenants: number;
  onboardingRate: number;
  activeTenantsCount: number;
  activityRate: number;
  churnRate: number;
  tenantHealthScore: number;
  canceledSubscriptions: number;
}

interface PlanDistribution {
  name: string;
  value: number;
}

interface NewTenantsOverTime {
  date?: string;
  month?: string;
  count: number;
}

interface TenantMetricsData {
  tenantMetrics: TenantMetrics;
  tenantActivity: TenantActivity;
  planDistribution: PlanDistribution[];
  tierDistribution: PlanDistribution[];
  trialSubscriptions: number;
  newTenantsOverTime: NewTenantsOverTime[];
  period: string;
}

interface TenantMetricsClientProps {
  data: TenantMetricsData;
}

export function TenantMetricsClient({ data }: TenantMetricsClientProps) {
  const {
    tenantMetrics,
    tenantActivity,
    planDistribution,
    tierDistribution,
    trialSubscriptions,
    newTenantsOverTime,
    period,
  } = data;

  // Colors for the pie chart
  const COLORS = ["#0088FE", "#00C49F", "#FFBB28", "#FF8042", "#8884d8"];

  // Helper function to get the correct dataKey for the chart
  const getChartDataKey = () => {
    if (newTenantsOverTime.length > 0) {
      return newTenantsOverTime[0].date ? "date" : "month";
    }
    return "month";
  };

  // Helper function to get chart title based on data type
  const getChartTitle = () => {
    const dataKey = getChartDataKey();
    return dataKey === "date" ? "New Tenants Over Time (Daily)" : "New Tenants Over Time (Monthly)";
  };

  return (
    <div className="space-y-6">
      {/* First row of metrics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Total Tenants</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatNumber(tenantMetrics.totalTenants)}
            </div>
            <p className="text-xs text-muted-foreground">
              {tenantMetrics.growthRate > 0 ? "+" : ""}
              {tenantMetrics.growthRate}% from last month
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Active Tenants</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatNumber(tenantMetrics.totalActiveTenants)}
            </div>
            <p className="text-xs text-muted-foreground">
              {tenantMetrics.totalTenants > 0
                ? `${Math.round(
                    (tenantMetrics.totalActiveTenants / tenantMetrics.totalTenants) * 100
                  )}% of total`
                : "0% of total"}
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">New Tenants</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatNumber(tenantMetrics.newTenants)}
            </div>
            <p className="text-xs text-muted-foreground">
              {tenantMetrics.totalTenants > 0
                ? `${Math.round(
                    (tenantMetrics.newTenants / tenantMetrics.totalTenants) * 100
                  )}% growth rate`
                : "0% growth rate"}
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Churn Rate</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {tenantActivity.churnRate}%
            </div>
            <p className="text-xs text-muted-foreground">
              {tenantActivity.canceledSubscriptions} cancellations in period
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Second row of metrics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Tenant Health Score</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {tenantActivity.tenantHealthScore}
            </div>
            <p className="text-xs text-muted-foreground">
              Based on activity and onboarding
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Active vs Inactive</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {tenantActivity.activeTenants}/{tenantActivity.inactiveTenants}
            </div>
            <p className="text-xs text-muted-foreground">
              Active/Inactive tenants
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Onboarding Completion</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {tenantActivity.onboardingRate}%
            </div>
            <p className="text-xs text-muted-foreground">
              {tenantActivity.onboardedTenants} of {tenantMetrics.totalTenants} tenants
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Activity Rate</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {tenantActivity.activityRate}%
            </div>
            <p className="text-xs text-muted-foreground">
              {tenantActivity.activeTenantsCount} active in period
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Charts - First Row */}
      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>{getChartTitle()}</CardTitle>
          </CardHeader>
          <CardContent className="h-[300px]">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={newTenantsOverTime}
                margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey={getChartDataKey()} />
                <YAxis />
                <Tooltip formatter={(value) => [`${value} tenants`, "New Tenants"]} />
                <Bar dataKey="count" fill="#8884d8" name="New Tenants" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Tenants by Plan</CardTitle>
          </CardHeader>
          <CardContent className="h-[300px]">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={planDistribution}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {planDistribution.map((_, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip formatter={(value) => [`${value} tenants`, "Count"]} />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Charts - Second Row */}
      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Subscription Tier Breakdown</CardTitle>
            <p className="text-sm text-muted-foreground">
              Distribution across free, basic, premium plans
            </p>
          </CardHeader>
          <CardContent className="h-[300px]">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={tierDistribution}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {tierDistribution.map((_, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip formatter={(value) => [`${value} tenants`, "Count"]} />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Trial Subscriptions</CardTitle>
            <p className="text-sm text-muted-foreground">
              Tenants currently on trial period
            </p>
          </CardHeader>
          <CardContent className="h-[300px] flex flex-col items-center justify-center">
            <div className="text-5xl font-bold mb-4">
              {formatNumber(trialSubscriptions)}
            </div>
            <p className="text-lg text-muted-foreground">
              {trialSubscriptions === 1 ? "Active Trial" : "Active Trials"}
            </p>
            {tenantMetrics.totalTenants > 0 && trialSubscriptions > 0 && (
              <p className="text-sm text-muted-foreground mt-2">
                {Math.round((trialSubscriptions / tenantMetrics.totalTenants) * 100)}% of total tenants
              </p>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
