import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/next-auth/src/auth-options";
import {
  getPageMembers,
  createPageMembership,
  updatePageMembershipRole,
  removePageMembership,
  hasPagePermission,
  PageMemberRole,
  PageMemberSource,
} from "@/lib/page-membership";
import db from "@/lib/shared-db";
import crypto from "crypto";

/**
 * API endpoints for page membership management
 * GET: Get all page members
 * POST: Add new page member
 * PUT: Update member role
 * DELETE: Remove member
 */

export async function GET(
  request: NextRequest,
  { params }: { params: { pageId: string } }
) {
  try {
    const session: any = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { pageId } = params;
    const userId = session.userId;

    // Check if user has permission to view page members (MEMBER or higher)
    const hasPermission = await hasPagePermission(
      userId,
      pageId,
      PageMemberRole.MEMBER
    );
    if (!hasPermission) {
      return NextResponse.json(
        { error: "Insufficient permissions to view page members" },
        { status: 403 }
      );
    }

    const members = await getPageMembers(pageId);
    return NextResponse.json({ members });
  } catch (error) {
    console.error("Error getting page members:", error);
    return NextResponse.json(
      { error: "Failed to get page members" },
      { status: 500 }
    );
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: { pageId: string } }
) {
  try {
    const session: any = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { pageId } = params;
    const userId = session.userId;
    const body = await request.json();
    const { targetUserId, role, tenantId } = body;

    if (!targetUserId || !role || !tenantId) {
      return NextResponse.json(
        { error: "targetUserId, role, and tenantId are required" },
        { status: 400 }
      );
    }

    // Check if user has permission to add members (ADMIN only)
    const hasPermission = await hasPagePermission(
      userId,
      pageId,
      PageMemberRole.ADMIN
    );
    if (!hasPermission) {
      return NextResponse.json(
        { error: "Insufficient permissions to add page members" },
        { status: 403 }
      );
    }

    // Validate role
    if (!Object.values(PageMemberRole).includes(role)) {
      return NextResponse.json({ error: "Invalid role" }, { status: 400 });
    }
    const emailHash = crypto
      .createHash("sha256")
      .update(targetUserId)
      .digest("hex");
    const user = await db.user.findUnique({
      where: {
        emailHash,
        membership: {
          some: {
            tenantId,
          },
        },
      },
    });

    if (!user) {
      return NextResponse.json(
        { error: "User not found. Add the user first" },
        { status: 404 }
      );
    }

    const membership = await createPageMembership({
      pageId,
      userId: user.id,
      tenantId,
      role,
      source: PageMemberSource.MANUAL,
    });

    if (!membership) {
      return NextResponse.json(
        { error: "Failed to create page membership" },
        { status: 500 }
      );
    }

    return NextResponse.json({ membership });
  } catch (error) {
    console.error("Error creating page membership:", error);
    return NextResponse.json(
      { error: "Failed to create page membership" },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { pageId: string } }
) {
  try {
    const session: any = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { pageId } = params;
    const userId = session.userId;
    const body = await request.json();
    const { targetUserId, role } = body;

    if (!targetUserId || !role) {
      return NextResponse.json(
        { error: "targetUserId and role are required" },
        { status: 400 }
      );
    }

    // Check if user has permission to update member roles (ADMIN only)
    const hasPermission = await hasPagePermission(
      userId,
      pageId,
      PageMemberRole.ADMIN
    );
    if (!hasPermission) {
      return NextResponse.json(
        { error: "Insufficient permissions to update member roles" },
        { status: 403 }
      );
    }

    // Validate role
    if (!Object.values(PageMemberRole).includes(role)) {
      return NextResponse.json({ error: "Invalid role" }, { status: 400 });
    }

    const membership = await updatePageMembershipRole(
      targetUserId,
      pageId,
      role
    );

    if (!membership) {
      return NextResponse.json(
        { error: "Failed to update page membership" },
        { status: 500 }
      );
    }

    return NextResponse.json({ membership });
  } catch (error) {
    console.error("Error updating page membership:", error);
    return NextResponse.json(
      { error: "Failed to update page membership" },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { pageId: string } }
) {
  try {
    const session: any = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { pageId } = params;
    const userId = session.userId;
    const url = new URL(request.url);
    const targetUserId = url.searchParams.get("userId");

    if (!targetUserId) {
      return NextResponse.json(
        { error: "userId query parameter is required" },
        { status: 400 }
      );
    }

    // Check if user has permission to remove members (ADMIN only)
    const hasPermission = await hasPagePermission(
      userId,
      pageId,
      PageMemberRole.ADMIN
    );
    if (!hasPermission) {
      return NextResponse.json(
        { error: "Insufficient permissions to remove page members" },
        { status: 403 }
      );
    }

    const success = await removePageMembership(targetUserId, pageId);

    if (!success) {
      return NextResponse.json(
        { error: "Failed to remove page membership" },
        { status: 500 }
      );
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error removing page membership:", error);
    return NextResponse.json(
      { error: "Failed to remove page membership" },
      { status: 500 }
    );
  }
}
