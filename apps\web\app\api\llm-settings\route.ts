import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import db from "@/lib/shared-db";
import { authOptions } from "@/lib/next-auth";

// GET /api/llm-settings
export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const tenantId = searchParams.get("tenantId");
    const id = searchParams.get("id");
    const rawSlug = searchParams.get("slug");
    const slug = rawSlug ? decodeURI(rawSlug) : rawSlug;

    if (!tenantId) {
      return NextResponse.json(
        { error: "Missing tenantId parameter" },
        { status: 400 }
      );
    }

    if (id) {
      const settings = await db.llmSettings.findFirst({
        where: {
          id,
          tenantId,
        },
      });

      return NextResponse.json({ settings }, { status: 200 });
    }

    if (slug) {
      const workspace = await db.workspace.findFirst({
        where: {
          slug,
          tenantId,
        },
      });
      const settings = await db.llmSettings.findFirst({
        where: {
          workspaceId: workspace?.id,
          tenantId,
        },
      });

      return NextResponse.json({ settings }, { status: 200 });
    }

    const settings = await db.llmSettings.findMany({
      where: {
        tenantId,
      },
    });

    return NextResponse.json(settings);
  } catch (error) {
    console.error("Error fetching LLM settings:", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}

// POST /api/llm-settings
export async function POST(req: NextRequest) {
  try {
    const session: any = await getServerSession(authOptions);
    if (!session) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const data = await req.json();
    const settings = await db.llmSettings.create({
      data: {
        ...data,
      },
    });

    return NextResponse.json(settings);
  } catch (error) {
    console.error("Error creating LLM settings:", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}

// PUT /api/llm-settings
export async function PUT(req: NextRequest) {
  try {
    const session: any = await getServerSession(authOptions);
    if (!session) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const data = await req.json();
    const settings = await db.llmSettings.update({
      where: {
        id: data.id,
      },
      data,
    });

    return NextResponse.json(settings);
  } catch (error) {
    console.error("Error updating LLM settings:", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}

// DELETE /api/llm-settings
export async function DELETE(req: NextRequest) {
  try {
    const session: any = await getServerSession(authOptions);
    if (!session) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const { searchParams } = new URL(req.url);
    const id = searchParams.get("id");

    if (!id) {
      return new NextResponse("Missing id parameter", { status: 400 });
    }

    await db.llmSettings.delete({
      where: {
        id,
      },
    });

    return new NextResponse(null, { status: 204 });
  } catch (error) {
    console.error("Error deleting LLM settings:", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}
