import { fetchWorkspaceBySlug } from "./workspace";

/**
 * Generate breadcrumbs based on the current path
 * @param pathname Current path
 * @param t Translation function
 * @param workspaceData Optional pre-fetched workspace data
 * @param brandName Optional custom brand name to use instead of default app name
 * @returns Array of breadcrumb items
 */
export const generateBreadcrumbs = async (
  pathname: string,
  t: (key: string) => string,
  workspaceData?: any,
  brandName?: string
) => {
  // Skip if we're on the home page
  if (pathname === "/" || pathname === "/dashboard") {
    return [
      {
        label: brandName || t("workspace.appName"),
        href: "/",
        active: true,
      },
    ];
  }

  // Split path into segments
  const segments = pathname.split("/").filter(Boolean);

  // Special case for organization settings
  if (
    (segments.length >= 1 && segments[0] === "settings") ||
    (segments.length >= 2 &&
      segments[0] === "dashboard" &&
      segments[1] === "settings")
  ) {
    return [
      {
        label: t("common.settings"),
        href: "/settings",
        active: true,
      },
    ];
  }

  // Special case for changelog page
  if (segments.length === 1 && segments[0] === "changelog") {
    return [
      {
        label: brandName || t("workspace.appName"),
        href: "/",
        active: false,
      },
      {
        label: "📋 Changelog",
        href: "/changelog",
        active: true,
      },
    ];
  }

  // Create breadcrumb items
  const breadcrumbs = [
    {
      label: brandName || t("workspace.appName"),
      href: "/",
      active: false,
    },
  ];

  // Build up breadcrumb path
  let currentPath = "";

  // Process each segment sequentially to handle async operations
  for (let index = 0; index < segments.length; index++) {
    const segment = segments[index];
    currentPath += `/${segment}`;

    // Format the label for display
    let label = segment;
    let skipSegment = false;

    // Check if segment looks like a MongoDB ObjectId (24 character hex string)
    if (/^[0-9a-f]{24}$/i.test(segment)) {
      skipSegment = true;
    }
    // Handle special cases
    else if (segment === "workspace" && index === 0) {
      label = t("common.workspaces");
    } else if (
      segment === "page" &&
      segments[index - 1] &&
      segments[index + 1]
    ) {
      // This is a page segment in /workspace/[slug]/page/[pageId]
      // Skip this segment in breadcrumbs
      skipSegment = true;
    } else if (
      segment === "folder" &&
      segments[index - 1] &&
      segments[index + 1]
    ) {
      // This is a folder segment in /workspace/[slug]/folder/[folderId]
      // Skip this segment in breadcrumbs
      skipSegment = true;
    } else if (
      segment === "file" &&
      segments[index - 1] &&
      segments[index + 1]
    ) {
      // This is a file segment in /workspace/[slug]/file/[fileId]
      // Skip this segment in breadcrumbs
      skipSegment = true;
    } else if (index === 1 && segments[0] === "workspace") {
      // This is a workspace slug
      // Try to get the actual workspace name from the provided data or fetch it
      if (workspaceData && workspaceData.name) {
        label = workspaceData.name;
      } else {
        // Fetch workspace data if not provided
        const workspace = await fetchWorkspaceBySlug(segment);
        if (workspace && workspace.name) {
          label = workspace.name;
        } else {
          // Fallback to formatting the slug if workspace data is not available
          label = segment
            .split("-")
            .map((word: string) => word.charAt(0).toUpperCase() + word.slice(1))
            .join(" ");
        }
      }
    } else if (segment.includes("-")) {
      // Format other slugs
      const translatedLabel = segment
        .split("-")
        .map((word: string, index: number) =>
          index === 0 ? word : word.charAt(0).toUpperCase() + word.slice(1)
        )
        .join("");
      label = t(`sidebar.${translatedLabel}`);
    } else {
      // Capitalize first letter for other segments
      label = segment.charAt(0).toUpperCase() + segment.slice(1);
    }

    // Add breadcrumb only if not skipping
    if (!skipSegment) {
      breadcrumbs.push({
        label,
        href: currentPath,
        active: index === segments.length - 1,
      });
    }
  }

  return breadcrumbs;
};
