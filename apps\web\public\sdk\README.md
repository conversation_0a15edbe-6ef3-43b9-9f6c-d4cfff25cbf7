# Swiss Knowledge Hub Chatbot SDK

A lightweight JavaScript SDK for embedding Swiss Knowledge Hub chatbots into external websites.

## Features

- 🚀 **Easy Integration** - Simple script tag integration
- 🎨 **Customizable** - Full theme and positioning control
- 🔒 **Secure** - API key authentication and domain restrictions
- 📱 **Responsive** - Mobile-friendly design
- 🌐 **Cross-Origin** - Proper CORS handling
- ⚡ **Lightweight** - Minimal footprint
- 🔧 **Framework Support** - Works with React, Vue, Angular, and vanilla JS

## Quick Start

### 1. Basic Integration

Add this script to your website's HTML:

```html
<!-- Swiss Knowledge Hub Chatbot -->
<script src="https://your-domain.com/sdk/chatbot-sdk.min.js"></script>
<script>
  SwissKnowledgeHub.init({
    chatbotId: 'your-chatbot-id',
    apiKey: 'your-api-key',
    position: 'bottom-right'
  });
</script>
```

### 2. Inline Integration

For embedding the chat directly in a container:

```html
<div id="chatbot-container"></div>
<script src="https://your-domain.com/sdk/chatbot-sdk.min.js"></script>
<script>
  SwissKnowledgeHub.init({
    chatbotId: 'your-chatbot-id',
    apiKey: 'your-api-key',
    mode: 'inline',
    container: '#chatbot-container'
  });
</script>
```

## Configuration Options

### Basic Configuration

```javascript
SwissKnowledgeHub.init({
  // Required
  chatbotId: 'your-chatbot-id',
  apiKey: 'your-api-key',
  
  // Optional
  position: 'bottom-right', // 'bottom-right', 'bottom-left', 'top-right', 'top-left'
  size: 'medium', // 'small', 'medium', 'large'
  mode: 'widget', // 'widget' or 'inline'
  container: '#chatbot-container', // Required for inline mode
  autoOpen: false, // Auto-open chat on load
  debug: false // Enable debug logging
});
```

### Theme Customization

```javascript
SwissKnowledgeHub.init({
  chatbotId: 'your-chatbot-id',
  apiKey: 'your-api-key',
  theme: {
    primaryColor: '#007bff',
    secondaryColor: '#6c757d',
    fontFamily: 'Inter, sans-serif',
    borderRadius: '8px'
  },
  greeting: 'Hello! How can I help you today?',
  placeholder: 'Type your message...',
  showBranding: true
});
```

## Dynamic API Base Configuration

The SDK automatically detects the API base URL from the script source, making deployment seamless across different environments.

### API Base URL Resolution

The SDK resolves the API base URL in the following order:

1. **Explicit `apiBase` in config** - If you provide `apiBase` in the configuration, it will be used
2. **Auto-detected from script source** - The SDK automatically detects the domain from the script tag source URL
3. **Current domain fallback** - If script source detection fails, it defaults to the current domain

### Auto-Detection Example

```html
<!-- SDK automatically uses http://localhost:3000 as API base -->
<script src="http://localhost:3000/sdk/chatbot-sdk.js"></script>
<script>
  SwissKnowledgeHub.init({
    chatbotId: 'your-chatbot-id',
    apiKey: 'your-api-key'
    // No apiBase needed - automatically detected!
  });
</script>
```

```html
<!-- SDK automatically uses https://api.yourapp.com as API base -->
<script src="https://api.yourapp.com/sdk/chatbot-sdk.js"></script>
<script>
  SwissKnowledgeHub.init({
    chatbotId: 'your-chatbot-id',
    apiKey: 'your-api-key'
    // API base automatically detected from script source
  });
</script>
```

### Environment-Specific Examples

#### Development vs Production

```javascript
// No need for environment detection - automatically handled!
SwissKnowledgeHub.init({
    chatbotId: 'your-chatbot-id',
    apiKey: 'your-api-key',
    mode: 'inline',
    container: '#my-chatbot'
    // API base automatically detected from script source
});
```

#### Override Auto-Detection

```javascript
// You can still override if needed
SwissKnowledgeHub.init({
    chatbotId: 'your-chatbot-id',
    apiKey: 'your-api-key',
    apiBase: 'https://custom-api-endpoint.com', // Explicit override
    mode: 'widget'
});
```

## Framework Integration

### React

```jsx
import { useEffect } from 'react';

export function ChatbotWidget() {
  useEffect(() => {
    const script = document.createElement('script');
    script.src = 'https://your-domain.com/sdk/chatbot-sdk.min.js';
    script.onload = () => {
      window.SwissKnowledgeHub.init({
        chatbotId: 'your-chatbot-id',
        apiKey: 'your-api-key',
        position: 'bottom-right'
      });
    };
    document.head.appendChild(script);

    return () => {
      if (window.SwissKnowledgeHub) {
        window.SwissKnowledgeHub.destroyAll();
      }
    };
  }, []);

  return null;
}
```

### Vue.js

```vue
<template>
  <div></div>
</template>

<script>
export default {
  name: 'ChatbotWidget',
  mounted() {
    this.loadChatbot();
  },
  beforeDestroy() {
    if (window.SwissKnowledgeHub) {
      window.SwissKnowledgeHub.destroyAll();
    }
  },
  methods: {
    loadChatbot() {
      const script = document.createElement('script');
      script.src = 'https://your-domain.com/sdk/chatbot-sdk.min.js';
      script.onload = () => {
        window.SwissKnowledgeHub.init({
          chatbotId: 'your-chatbot-id',
          apiKey: 'your-api-key',
          position: 'bottom-right'
        });
      };
      document.head.appendChild(script);
    }
  }
}
</script>
```

### Angular

```typescript
import { Component, OnInit, OnDestroy } from '@angular/core';

@Component({
  selector: 'app-chatbot',
  template: ''
})
export class ChatbotComponent implements OnInit, OnDestroy {
  
  ngOnInit() {
    this.loadChatbot();
  }

  ngOnDestroy() {
    if ((window as any).SwissKnowledgeHub) {
      (window as any).SwissKnowledgeHub.destroyAll();
    }
  }

  private loadChatbot() {
    const script = document.createElement('script');
    script.src = 'https://your-domain.com/sdk/chatbot-sdk.min.js';
    script.onload = () => {
      (window as any).SwissKnowledgeHub.init({
        chatbotId: 'your-chatbot-id',
        apiKey: 'your-api-key',
        position: 'bottom-right'
      });
    };
    document.head.appendChild(script);
  }
}
```

## API Reference

### SwissKnowledgeHub.init(config)

Initializes a new chatbot instance.

**Parameters:**
- `config` (Object): Configuration options

**Returns:**
- `ChatbotInstance`: The created chatbot instance

### SwissKnowledgeHub.destroy(instanceId)

Destroys a specific chatbot instance.

**Parameters:**
- `instanceId` (String): The instance ID to destroy

### SwissKnowledgeHub.destroyAll()

Destroys all chatbot instances.

## Security

### API Keys

- Keep your API keys secure and never expose them in client-side code
- API keys are used for authentication and rate limiting
- Regenerate API keys if compromised

### Domain Restrictions

Configure allowed domains in your chatbot settings to restrict access:

```javascript
// Only these domains can use your chatbot
allowedDomains: [
  'example.com',
  '*.subdomain.com',
  'localhost:3000'
]
```

### Rate Limiting

The SDK respects rate limits configured for your chatbot:
- Per-minute limits
- Per-hour limits  
- Per-day limits

## Troubleshooting

### Common Issues

1. **Chatbot not loading**
   - Check API key validity
   - Verify domain is allowed
   - Check browser console for errors

2. **CORS errors**
   - Ensure your domain is in the allowed domains list
   - Check that the chatbot is active

3. **Rate limit exceeded**
   - Wait for the rate limit window to reset
   - Contact support to increase limits

### Debug Mode

Enable debug mode for detailed logging:

```javascript
SwissKnowledgeHub.init({
  chatbotId: 'your-chatbot-id',
  apiKey: 'your-api-key',
  debug: true
});
```

## Browser Support

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## License

This SDK is proprietary software. See your license agreement for terms of use.

## Support

For technical support, please contact:
- Email: <EMAIL>
- Documentation: https://docs.swissknowledgehub.com
- Status Page: https://status.swissknowledgehub.com
