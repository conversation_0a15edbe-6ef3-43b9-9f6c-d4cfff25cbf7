import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/next-auth";
import db from "@/lib/shared-db";

export async function GET(request: Request) {
  const session = await getServerSession(authOptions);

  if (!session) {
    return NextResponse.json(
      { error: "Unauthorized" },
      { status: 401 }
    );
  }

  try {
    
    const { searchParams } = new URL(request.url);

    // Parse query parameters
    const searchQuery = searchParams.get("search") || "";
    const page = parseInt(searchParams.get("page") || "1", 10);
    const limit = parseInt(searchParams.get("limit") || "5", 10);

    // Validate pagination parameters
    const validPage = Math.max(1, page);
    const validLimit = Math.min(Math.max(1, limit), 100); // Max 100 items per page

    // Fetch ALL subscriptions with their tenant and plan information using includes
    // This follows the same pattern as the users page
    const subscriptions = await db.subscription.findMany({
      include: {
        tenant: {
          select: {
            id: true,
            name: true,
          },
        },
        plan: {
          select: {
            id: true,
            name: true,
            price: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    // Filter out any subscriptions with missing relationships (just in case)
    const validSubscriptions = subscriptions.filter((subscription: any) => {
      const hasValidTenant = subscription.tenant && subscription.tenant.name;
      const hasValidPlan = subscription.plan && subscription.plan.name;

      if (!hasValidTenant || !hasValidPlan) {
        console.log(`API: Skipping subscription ${subscription.id} - missing tenant: ${!hasValidTenant}, missing plan: ${!hasValidPlan}`);
        return false;
      }

      return true;
    });

    // Transform the data to match the expected structure
    let transformedSubscriptions = validSubscriptions.map((subscription: any) => {
      // Determine status based on subscription properties
      let status = "inactive";
      if (subscription.isActive) {
        if (subscription.isOnTrial) {
          status = "trial";
        } else {
          status = "active";
        }
      }

      // For now, ignore amount calculation as requested
      const totalAmount = 0;

      return {
        id: subscription.id,
        tenantName: subscription.tenant.name,
        tenantId: subscription.tenantId,
        plan: subscription.plan.name,
        startDate: subscription.startDate.toISOString(),
        endDate: subscription.endDate ? subscription.endDate.toISOString() : null,
        status,
        billingInterval: subscription.billingInterval || "month",
        amount: totalAmount,
        additionalUsers: subscription.additionalUsers,
        additionalStorage: subscription.additionalStorageGB,
      };
    });
    
    // Apply search filter if search query is provided
    if (searchQuery && searchQuery.trim()) {
      const searchTerm = searchQuery.toLowerCase().trim();
      
      transformedSubscriptions = transformedSubscriptions.filter((subscription: any) => {
        const tenantMatch = subscription.tenantName.toLowerCase().includes(searchTerm);
        const planMatch = subscription.plan.toLowerCase().includes(searchTerm);
        const statusMatch = subscription.status.toLowerCase().includes(searchTerm);
        const billingMatch = subscription.billingInterval.toLowerCase().includes(searchTerm);
        
        return tenantMatch || planMatch || statusMatch || billingMatch;
      });
    }
    
    // Get total count for pagination
    const total = transformedSubscriptions.length;
    
    // Apply pagination
    const startIndex = (validPage - 1) * validLimit;
    const paginatedSubscriptions = transformedSubscriptions.slice(startIndex, startIndex + validLimit);

    // Get aggregation data for charts from real subscription data
    const planCounts = transformedSubscriptions
      .filter((sub: any) => sub.status === "active")
      .reduce((acc: any, sub: any) => {
        acc[sub.plan] = (acc[sub.plan] || 0) + 1;
        return acc;
      }, {});

    const billingCounts = transformedSubscriptions
      .filter((sub: any) => sub.status === "active")
      .reduce((acc: any, sub: any) => {
        const billing = sub.billingInterval === "year" ? "Yearly" : "Monthly";
        acc[billing] = (acc[billing] || 0) + 1;
        return acc;
      }, {});

    const planStats = Object.entries(planCounts).map(([name, value]) => ({
      name,
      value: value as number,
    }));

    const billingStats = Object.entries(billingCounts).map(([name, value]) => ({
      name,
      value: value as number,
    }));

    // Calculate pagination info
    const totalPages = Math.ceil(total / validLimit);
    
    return NextResponse.json({
      data: {
        subscriptions: paginatedSubscriptions,
        planDistribution: planStats,
        billingDistribution: billingStats,
        pagination: {
          page: validPage,
          limit: validLimit,
          total,
          totalPages,
        },
        searchQuery,
      },
      message: "Subscriptions retrieved successfully",
    });
  } catch (error) {
    console.error("Error fetching subscriptions:", error);
    return NextResponse.json(
      { error: "Failed to fetch subscriptions" },
      { status: 500 }
    );
  }
}
