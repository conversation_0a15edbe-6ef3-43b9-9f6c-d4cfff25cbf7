import { apiUrl, fetchJson } from "..";

export const createPage = async (data, tenantId, userId) => {
  try {
    const headers = {
      "Content-Type": "application/json",
      "x-user-id": userId,
      "x-tenant-id": tenantId,
    };

    // Add workspace slug to headers if available in data
    if (data.workspaceSlug) {
      headers["x-workspace-slug"] = data.workspaceSlug;
    }

    const response = await fetch(`${apiUrl}/pages`, {
      method: "POST",
      headers,
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error("Network response was not ok");
    }
    return await response.json();
  } catch (error) {
    console.log({ "Create page api calling error": error });
    return { error: "Error Creating The Page" };
  }
};

export const updatePage = async (data, tenantId, userId) => {
  try {
    // Create headers with workspace slug if available
    const headers = {
      "Content-Type": "application/json",
      "x-user-id": userId,
      "x-tenant-id": tenantId,
    };

    // Add workspace slug to headers if available in data
    if (data.workspaceSlug) {
      headers["x-workspace-slug"] = data.workspaceSlug;
    }

    // Use the new API endpoint for removing sync
    if (data.gDriveFolderId === null || data.oneDriveFolderId === null) {
      const response = await fetch(`${apiUrl}/pages/update`, {
        method: "POST",
        headers,
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Network response was not ok");
      }
      return await response.json();
    }

    // Use the existing endpoint for other updates
    const response = await fetch(`${apiUrl}/pages`, {
      method: "PUT",
      headers,
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error("Network response was not ok");
    }
    return await response.json();
  } catch (error) {
    console.log({ "Update page api calling error": error });
    return { error: "Error Update The Page" };
  }
};

export const deletePage = async (
  pageId,
  tenantId,
  userId,
  workspaceSlug = null
) => {
  try {
    const url = `${apiUrl}/pages?id=${pageId}`;
    const headers = {
      "x-user-id": userId,
      "x-tenant-id": tenantId,
    };

    // Add workspace slug to headers if available
    if (workspaceSlug) {
      headers["x-workspace-slug"] = workspaceSlug;
    }

    const response = await fetch(url, {
      method: "DELETE",
      headers,
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Network response was not ok");
    }
    return await response.json();
  } catch (error) {
    console.log({ "delete page api error": error });
    return { error: error.message || "Error deleting page" };
  }
};

export const revokeAndDeletePageSync = async (
  pageId: string,
  tenantId: string,
  userId: string
) => {
  try {
    const response = await fetch(`${apiUrl}/pages/revoke-and-delete`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "x-user-id": userId,
        "x-tenant-id": tenantId,
      },
      body: JSON.stringify({ id: pageId, tenantId }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Network response was not ok");
    }
    return await response.json();
  } catch (error) {
    console.log({ "revoke and delete page sync api error": error });
    return {
      error: error.message || "Error revoking sync and deleting content",
    };
  }
};

export const getPage = async ({
  workspaceSlug,
  tenantId,
  userId,
  pageId = null,
}) => {
  if (pageId) {
    const response = await fetchJson(
      `${apiUrl}/pages?workspaceSlug=${encodeURIComponent(
        workspaceSlug
      )}&pageId=${pageId}`,
      userId,
      tenantId,
      workspaceSlug // Pass the raw workspaceSlug as the fourth parameter for headers
    );
    return response;
  }
  const response = await fetchJson(
    `${apiUrl}/pages?workspaceSlug=${encodeURIComponent(workspaceSlug)}`,
    userId,
    tenantId,
    workspaceSlug // Pass the raw workspaceSlug as the fourth parameter for headers
  );
  return response;
};
