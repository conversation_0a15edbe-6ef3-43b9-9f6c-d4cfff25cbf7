from fastapi import APIRouter, Depends, HTTPException, status, Query, Request
from typing import Dict, Any, Optional, List
from pydantic import BaseModel, HttpUrl
from src.services.web_scraper import WebScraperService
from src.api.deps import get_db
from src.api.auth import get_current_user_id
from bson import ObjectId
import logging

logger = logging.getLogger(__name__)

router = APIRouter()

def get_tenant_id(request: Request) -> str:
    """Extract tenant ID from request headers"""
    tenant_id = request.headers.get("X-Tenant-ID")
    if not tenant_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="X-Tenant-ID header is required"
        )
    return tenant_id

class UrlImportRequest(BaseModel):
    url: HttpUrl
    action: str  # "fetch", "sitemap", "check-duplicate", "batch-import"
    workspace_id: Optional[str] = None
    options: Optional[Dict[str, Any]] = None

class UrlImportResponse(BaseModel):
    success: bool
    url: Optional[str] = None
    title: Optional[str] = None
    description: Optional[str] = None
    content: Optional[str] = None
    headings: Optional[List[str]] = None
    format: Optional[str] = None
    file_extension: Optional[str] = None
    content_type: Optional[str] = None
    content_cleaning_applied: Optional[str] = None
    crawl_depth: Optional[int] = None
    additional_urls: Optional[List[str]] = None
    robots_info: Optional[Dict[str, Any]] = None
    has_sitemap: Optional[bool] = None
    sitemap_url: Optional[str] = None
    error: Optional[str] = None

class SitemapResponse(BaseModel):
    sitemap_url: str
    urls: List[str]

class DuplicateCheckResponse(BaseModel):
    is_duplicate: bool

class IndexRequest(BaseModel):
    file_id: str
    workspace_slug: str
    document_path: str
    document_type: Optional[str] = "auto"
    metadata: Optional[Dict[str, Any]] = None
    page_id: Optional[str] = None

class IndexResponse(BaseModel):
    success: bool
    message: Optional[str] = None
    document_count: Optional[int] = None
    error: Optional[str] = None

class ImportAndIndexRequest(BaseModel):
    url: HttpUrl
    workspace_slug: str
    file_id: str
    document_path: str
    options: Optional[Dict[str, Any]] = None
    page_id: Optional[str] = None

class ImportAndIndexResponse(BaseModel):
    import_success: bool
    index_success: bool
    url: Optional[str] = None
    title: Optional[str] = None
    content: Optional[str] = None
    message: Optional[str] = None
    document_count: Optional[int] = None
    error: Optional[str] = None

@router.post("/fetch", response_model=UrlImportResponse)
async def fetch_url_content(
    url_request: UrlImportRequest,
    request: Request,
    current_user: str = Depends(get_current_user_id),
    db = Depends(get_db)
):
    """
    Fetch and extract content from a URL with advanced processing
    """
    try:

        if url_request.action != "fetch":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid action for this endpoint"
            )

        # Extract options
        options = url_request.options or {}
        content_cleaning_level = options.get("contentCleaningLevel", "basic")
        output_format = options.get("format", "html")
        bypass_robots = options.get("bypassRobots", False)
        crawl_depth = options.get("crawlDepth", 1)

        # Use the web scraper service
        async with WebScraperService() as scraper:
            result = await scraper.extract_content(
                url=str(url_request.url),
                content_cleaning_level=content_cleaning_level,
                output_format=output_format,
                bypass_robots=bypass_robots,
                crawl_depth=crawl_depth
            )

        if not result["success"]:
            # Check if it's a robots.txt issue
            if "robots.txt" in result.get("error", ""):
                return UrlImportResponse(
                    success=False,
                    error=result["error"],
                    robots_info=result.get("robots_info")
                )
            else:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=result["error"]
                )
        return UrlImportResponse(**result)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching URL content: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to fetch URL content: {str(e)}"
        )

@router.post("/sitemap", response_model=SitemapResponse)
async def fetch_sitemap(
    url_request: UrlImportRequest,
    request: Request,
    current_user: str = Depends(get_current_user_id),
    db = Depends(get_db)
):
    """
    Fetch and parse a sitemap to extract URLs
    """
    try:
        tenant_id = get_tenant_id(request)

        if url_request.action != "sitemap":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid action for this endpoint"
            )

        # Use the web scraper service
        async with WebScraperService() as scraper:
            urls = await scraper.extract_sitemap_urls(str(url_request.url))

        return SitemapResponse(
            sitemap_url=str(url_request.url),
            urls=urls
        )
        
    except Exception as e:
        logger.error(f"Error fetching sitemap: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to fetch sitemap: {str(e)}"
        )

@router.post("/check-duplicate", response_model=DuplicateCheckResponse)
async def check_duplicate_url(
    url_request: UrlImportRequest,
    request: Request,
    current_user: str = Depends(get_current_user_id),
    db = Depends(get_db)
):
    """
    Check if a URL has already been imported in the workspace
    """
    try:
        tenant_id = get_tenant_id(request)

        if url_request.action != "check-duplicate":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid action for this endpoint"
            )

        if not url_request.workspace_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Workspace ID is required for duplicate check"
            )

        # Check if URL already exists in the workspace
        existing_file = await db.File.find_one({
            "workspaceId": url_request.workspace_id,
            "metadata.sourceUrl": str(url_request.url)
        })
        
        return DuplicateCheckResponse(
            is_duplicate=existing_file is not None
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error checking duplicate URL: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to check duplicate URL: {str(e)}"
        )

@router.post("/batch-import")
async def batch_import_urls(
    urls: List[HttpUrl],
    workspace_id: str,
    request: Request,
    options: Optional[Dict[str, Any]] = None,
    current_user: str = Depends(get_current_user_id),
    db = Depends(get_db)
):
    """
    Batch import multiple URLs (placeholder for future implementation)
    """
    try:
        tenant_id = get_tenant_id(request)

        # This is a placeholder for batch import functionality
        # The actual implementation would process multiple URLs
        # and create files for each one

        return {
            "message": "Batch import functionality will be implemented in the frontend",
            "urls_count": len(urls),
            "workspace_id": workspace_id,
            "tenant_id": tenant_id
        }
        
    except Exception as e:
        logger.error(f"Error in batch import: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to perform batch import: {str(e)}"
        )

@router.get("/robots-check")
async def check_robots_txt(
    request: Request,
    url: HttpUrl = Query(..., description="URL to check robots.txt for"),
    current_user: str = Depends(get_current_user_id)
):
    """
    Check robots.txt for a given URL
    """
    try:
        tenant_id = get_tenant_id(request)

        async with WebScraperService() as scraper:
            robots_info = await scraper.check_robots_txt(str(url))

        return {
            "url": str(url),
            "robots_info": robots_info,
            "tenant_id": tenant_id
        }
        
    except Exception as e:
        logger.error(f"Error checking robots.txt: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to check robots.txt: {str(e)}"
        )

@router.post("/index", response_model=IndexResponse)
async def index_imported_file(
    index_request: IndexRequest,
    request: Request,
    current_user: str = Depends(get_current_user_id),
    db = Depends(get_db)
):
    """
    Index an imported file for search and retrieval
    """
    try:
        tenant_id = get_tenant_id(request)


        # Update file status to PROCESSING
        await db.File.update_one(
            {"_id": ObjectId(index_request.file_id)},
            {"$set": {"vectorizationStatus": "PROCESSING"}}
        )

        # Get workspace by slug
        workspace = await db.Workspace.find_one({
            "slug": index_request.workspace_slug,
        })

        if not workspace:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Workspace not found"
            )

        # Process the indexing with background processing
        from src.services.document_processing_service import document_processing_service
        document_processing_service.db_client = db

        # Create document processing job
        job_id = await document_processing_service.create_document_processing_job(
            document_path=index_request.document_path,
            document_type=index_request.document_type,
            user_id=current_user,
            tenant_id=tenant_id,
            workspace_id=str(workspace["_id"]),
            file_id=index_request.file_id,
            workspace_slug=index_request.workspace_slug,
            metadata=index_request.metadata,
            page_id=index_request.page_id
        )

        return IndexResponse(
            success=True,
            message=f"Document submitted for background processing (Job ID: {job_id})",
            document_count=0  # Will be updated when processing completes
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error indexing imported file: {e}")

        # Update file status to FAILED
        try:
            await db.File.update_one(
                {"_id": ObjectId(index_request.file_id)},
                {"$set": {"vectorizationStatus": "FAILED"}}
            )
        except Exception as update_error:
            logger.error(f"Error updating file status to FAILED: {update_error}")

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to index imported file: {str(e)}"
        )

@router.post("/import-and-index", response_model=ImportAndIndexResponse)
async def import_and_index_url(
    import_request: ImportAndIndexRequest,
    request: Request,
    current_user: str = Depends(get_current_user_id),
    db = Depends(get_db)
):
    """
    Import URL content and immediately index it for search
    """
    try:
        tenant_id = get_tenant_id(request)

        # Step 1: Extract content from URL
        options = import_request.options or {}
        content_cleaning_level = options.get("contentCleaningLevel", "basic")
        output_format = options.get("format", "html")
        bypass_robots = options.get("bypassRobots", False)
        crawl_depth = options.get("crawlDepth", 1)

        async with WebScraperService() as scraper:
            import_result = await scraper.extract_content(
                url=str(import_request.url),
                content_cleaning_level=content_cleaning_level,
                output_format=output_format,
                bypass_robots=bypass_robots,
                crawl_depth=crawl_depth
            )

        if not import_result["success"]:
            return ImportAndIndexResponse(
                import_success=False,
                index_success=False,
                error=import_result.get("error", "Failed to import URL content")
            )

        # Step 2: Index the content
        try:


            # Update file status to PROCESSING
            await db.File.update_one(
                {"_id": ObjectId(import_request.file_id)},
                {"$set": {"vectorizationStatus": "PROCESSING"}}
            )

            # Get workspace by slug
            workspace = await db.Workspace.find_one({
                "slug": import_request.workspace_slug,
            })

            if not workspace:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Workspace not found"
                )

            # Prepare metadata
            metadata = {
                "sourceUrl": str(import_request.url),
                "importedAt": import_result.get("importedAt", ""),
                "contentCleaningLevel": content_cleaning_level,
                "format": output_format,
                "crawlDepth": crawl_depth,
                **(options.get("metadata", {}))
            }

            # Process the indexing with background processing
            from src.services.document_processing_service import document_processing_service
            document_processing_service.db_client = db

            # Create document processing job
            job_id = await document_processing_service.create_document_processing_job(
                document_path=import_request.document_path,
                document_type=output_format if output_format in ["html", "markdown"] else "auto",
                user_id=current_user,
                tenant_id=tenant_id,
                workspace_id=str(workspace["_id"]),
                file_id=import_request.file_id,
                workspace_slug=import_request.workspace_slug,
                metadata=metadata,
                page_id=import_request.page_id
            )

            return ImportAndIndexResponse(
                import_success=True,
                index_success=True,
                url=str(import_request.url),
                title=import_result.get("title"),
                content=import_result.get("content"),
                message=f"Successfully imported content from {import_request.url} and submitted for background processing (Job ID: {job_id})",
                document_count=0  # Will be updated when processing completes
            )

        except Exception as index_error:
            logger.error(f"Error indexing imported content: {index_error}")

            # Update file status to FAILED
            try:
                await db.File.update_one(
                    {"_id": ObjectId(import_request.file_id)},
                    {"$set": {"vectorizationStatus": "FAILED"}}
                )
            except Exception as update_error:
                logger.error(f"Error updating file status to FAILED: {update_error}")

            return ImportAndIndexResponse(
                import_success=True,
                index_success=False,
                url=str(import_request.url),
                title=import_result.get("title"),
                content=import_result.get("content"),
                error=f"Content imported but indexing failed: {str(index_error)}"
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in import and index: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to import and index URL: {str(e)}"
        )

# Health check endpoint
@router.get("/health")
async def health_check():
    """
    Health check endpoint for the URL import service
    """
    return {
        "status": "healthy",
        "service": "url_import",
        "features": [
            "content_extraction",
            "robots_txt_checking",
            "sitemap_parsing",
            "markdown_conversion",
            "duplicate_detection",
            "document_indexing"
        ]
    }
