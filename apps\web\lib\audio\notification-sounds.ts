/**
 * Audio notification system for real-time notifications
 */

export type NotificationSoundType = "mention" | "reply" | "general";

interface AudioNotificationOptions {
  volume?: number; // 0.0 to 1.0
  enabled?: boolean;
}

class AudioNotificationManager {
  private audioContext: AudioContext | null = null;
  private audioBuffers: Map<NotificationSoundType, AudioBuffer> = new Map();
  private isInitialized = false;
  private userInteracted = false;
  private options: AudioNotificationOptions = {
    volume: 0.3,
    enabled: true,
  };

  constructor() {
    // Listen for user interaction to enable audio
    this.setupUserInteractionListener();
    // Load user preferences
    this.loadUserPreferences();
  }

  private setupUserInteractionListener() {
    const enableAudio = () => {
      this.userInteracted = true;
      this.initializeAudioContext();
      // Remove listeners after first interaction
      document.removeEventListener("click", enableAudio);
      document.removeEventListener("keydown", enableAudio);
      document.removeEventListener("touchstart", enableAudio);
    };

    document.addEventListener("click", enableAudio, { once: true });
    document.addEventListener("keydown", enableAudio, { once: true });
    document.addEventListener("touchstart", enableAudio, { once: true });
  }

  private async initializeAudioContext() {
    if (this.isInitialized || !this.userInteracted) return;

    try {
      this.audioContext = new (window.AudioContext ||
        (window as any).webkitAudioContext)();

      if (this.audioContext.state === "suspended") {
        await this.audioContext.resume();
      }

      await this.loadAudioFiles();
      this.isInitialized = true;
    } catch (error) {
      console.warn("Failed to initialize audio context:", error);
    }
  }

  private async loadAudioFiles() {
    if (!this.audioContext) return;

    const soundConfigs = {
      mention: { frequencies: [800, 1000], duration: 0.15, volume: 0.3 },
      reply: { frequencies: [600, 800], duration: 0.12, volume: 0.25 },
      general: { frequencies: [500, 650], duration: 0.1, volume: 0.2 },
    };

    for (const [type, config] of Object.entries(soundConfigs)) {
      try {
        const audioBuffer = await this.createAudioBuffer(
          config.frequencies,
          config.duration,
          config.volume
        );
        if (audioBuffer) {
          this.audioBuffers.set(type as NotificationSoundType, audioBuffer);
        }
      } catch (error) {
        console.warn(`Failed to create ${type} sound:`, error);
      }
    }
  }

  private async createAudioBuffer(
    frequencies: number[],
    duration: number,
    volume: number
  ): Promise<AudioBuffer | null> {
    if (!this.audioContext) return null;

    const sampleRate = this.audioContext.sampleRate;
    const numSamples = Math.floor(sampleRate * duration);
    const audioBuffer = this.audioContext.createBuffer(
      1,
      numSamples,
      sampleRate
    );
    const channelData = audioBuffer.getChannelData(0);

    for (let i = 0; i < numSamples; i++) {
      const t = i / sampleRate;
      let sample = 0;

      // Create a pleasant chime by combining frequencies
      frequencies.forEach((freq) => {
        const envelope = Math.exp(-t * 3); // Exponential decay
        sample +=
          (Math.sin(2 * Math.PI * freq * t) * envelope * volume) /
          frequencies.length;
      });

      channelData[i] = sample;
    }

    return audioBuffer;
  }

  async playNotificationSound(type: NotificationSoundType): Promise<void> {
    if (!this.options.enabled || !this.userInteracted) {
      return;
    }

    if (!this.isInitialized) {
      await this.initializeAudioContext();
    }

    if (!this.audioContext || !this.audioBuffers.has(type)) {
      console.warn(`Audio not available for notification type: ${type}`);
      return;
    }

    try {
      const audioBuffer = this.audioBuffers.get(type)!;
      const source = this.audioContext.createBufferSource();
      const gainNode = this.audioContext.createGain();

      source.buffer = audioBuffer;
      gainNode.gain.value = this.options.volume || 0.3;

      source.connect(gainNode);
      gainNode.connect(this.audioContext.destination);

      source.start();
    } catch (error) {
      console.warn("Failed to play notification sound:", error);
    }
  }

  setVolume(volume: number) {
    this.options.volume = Math.max(0, Math.min(1, volume));
    this.saveUserPreferences();
  }

  setEnabled(enabled: boolean) {
    this.options.enabled = enabled;
    this.saveUserPreferences();
  }

  getVolume(): number {
    return this.options.volume || 0.3;
  }

  isEnabled(): boolean {
    return this.options.enabled !== false;
  }

  hasUserInteracted(): boolean {
    return this.userInteracted;
  }

  private loadUserPreferences() {
    try {
      const saved = localStorage.getItem("notification-audio-preferences");
      if (saved) {
        const preferences = JSON.parse(saved);
        this.options = { ...this.options, ...preferences };
      }
    } catch (error) {
      console.warn("Failed to load audio preferences:", error);
    }
  }

  private saveUserPreferences() {
    try {
      localStorage.setItem(
        "notification-audio-preferences",
        JSON.stringify(this.options)
      );
    } catch (error) {
      console.warn("Failed to save audio preferences:", error);
    }
  }

  // Test method for user preference UI
  async testSound(type: NotificationSoundType = "general"): Promise<void> {
    await this.playNotificationSound(type);
  }
}

// Global instance
export const audioNotificationManager = new AudioNotificationManager();

// Convenience functions
export const playMentionSound = () =>
  audioNotificationManager.playNotificationSound("mention");
export const playReplySound = () =>
  audioNotificationManager.playNotificationSound("reply");
export const playGeneralSound = () =>
  audioNotificationManager.playNotificationSound("general");

// Settings functions
export const setNotificationVolume = (volume: number) =>
  audioNotificationManager.setVolume(volume);
export const setNotificationSoundsEnabled = (enabled: boolean) =>
  audioNotificationManager.setEnabled(enabled);
export const getNotificationVolume = () => audioNotificationManager.getVolume();
export const areNotificationSoundsEnabled = () =>
  audioNotificationManager.isEnabled();
export const hasUserInteractedForAudio = () =>
  audioNotificationManager.hasUserInteracted();
