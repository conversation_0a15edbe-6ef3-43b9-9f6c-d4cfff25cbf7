"use client";

import { useEffect, useState } from "react";
import { useLanguage, Language } from "@/lib/language-context";

export const useLanguageDetection = () => {
  const { language, setLanguage } = useLanguage();
  const [detectedLanguage, setDetectedLanguage] = useState<Language | null>(
    null
  );

  useEffect(() => {
    // Auto-detect language based on browser settings if not already set
    if (!language && typeof window !== "undefined") {
      const browserLang = navigator.language.toLowerCase();
      let detected: Language = "en"; // Default to English

      // Check for supported languages
      if (browserLang.startsWith("de")) {
        detected = "de";
      } else if (browserLang.startsWith("en")) {
        detected = "en";
      }
      // Add more language detection logic here as needed

      setDetectedLanguage(detected);

      // Optionally auto-set the language
      if (!language) {
        setLanguage(detected);
      }
    }
  }, [language, setLanguage]);

  return {
    detectedLanguage,
    currentLanguage: language || detectedLanguage || "en",
  };
};
