/**
 * Rate Limiter for Chatbot SDK
 * 
 * Provides rate limiting functionality for public chatbot endpoints
 * In production, this should be replaced with Redis-based rate limiting
 */

interface RateLimitRecord {
  count: number;
  resetTime: number;
  blocked?: boolean;
  blockUntil?: number;
}

interface RateLimitConfig {
  windowMs: number;
  maxRequests: number;
  blockDurationMs?: number; // How long to block after exceeding limit
}

class RateLimiter {
  private store = new Map<string, RateLimitRecord>();
  private cleanupInterval: NodeJS.Timeout;

  constructor() {
    // Clean up expired records every 5 minutes
    this.cleanupInterval = setInterval(() => {
      this.cleanup();
    }, 5 * 60 * 1000);
  }

  /**
   * Check if a request should be rate limited
   */
  checkLimit(
    key: string, 
    config: RateLimitConfig
  ): { allowed: boolean; remaining: number; resetTime: number; retryAfter?: number } {
    const now = Date.now();
    const record = this.store.get(key);

    // Check if currently blocked
    if (record?.blocked && record.blockUntil && now < record.blockUntil) {
      return {
        allowed: false,
        remaining: 0,
        resetTime: record.resetTime,
        retryAfter: Math.ceil((record.blockUntil - now) / 1000)
      };
    }

    // If no record or window expired, create new record
    if (!record || now > record.resetTime) {
      const newRecord: RateLimitRecord = {
        count: 1,
        resetTime: now + config.windowMs,
        blocked: false
      };
      this.store.set(key, newRecord);
      
      return {
        allowed: true,
        remaining: config.maxRequests - 1,
        resetTime: newRecord.resetTime
      };
    }

    // Check if limit exceeded
    if (record.count >= config.maxRequests) {
      // Block for specified duration if configured
      if (config.blockDurationMs) {
        record.blocked = true;
        record.blockUntil = now + config.blockDurationMs;
        this.store.set(key, record);
        
        return {
          allowed: false,
          remaining: 0,
          resetTime: record.resetTime,
          retryAfter: Math.ceil(config.blockDurationMs / 1000)
        };
      }
      
      return {
        allowed: false,
        remaining: 0,
        resetTime: record.resetTime
      };
    }

    // Increment count
    record.count++;
    this.store.set(key, record);

    return {
      allowed: true,
      remaining: config.maxRequests - record.count,
      resetTime: record.resetTime
    };
  }

  /**
   * Get current rate limit status without incrementing
   */
  getStatus(key: string, config: RateLimitConfig): { count: number; remaining: number; resetTime: number } {
    const now = Date.now();
    const record = this.store.get(key);

    if (!record || now > record.resetTime) {
      return {
        count: 0,
        remaining: config.maxRequests,
        resetTime: now + config.windowMs
      };
    }

    return {
      count: record.count,
      remaining: Math.max(0, config.maxRequests - record.count),
      resetTime: record.resetTime
    };
  }

  /**
   * Reset rate limit for a specific key
   */
  reset(key: string): void {
    this.store.delete(key);
  }

  /**
   * Block a key for a specific duration
   */
  block(key: string, durationMs: number): void {
    const now = Date.now();
    const record = this.store.get(key) || {
      count: 0,
      resetTime: now + durationMs
    };
    
    record.blocked = true;
    record.blockUntil = now + durationMs;
    this.store.set(key, record);
  }

  /**
   * Clean up expired records
   */
  private cleanup(): void {
    const now = Date.now();
    for (const [key, record] of this.store.entries()) {
      // Remove if window expired and not blocked, or if block expired
      if ((now > record.resetTime && !record.blocked) || 
          (record.blocked && record.blockUntil && now > record.blockUntil)) {
        this.store.delete(key);
      }
    }
  }

  /**
   * Get statistics about current rate limiting
   */
  getStats(): { totalKeys: number; blockedKeys: number; memoryUsage: number } {
    const now = Date.now();
    let blockedKeys = 0;
    
    for (const record of this.store.values()) {
      if (record.blocked && record.blockUntil && now < record.blockUntil) {
        blockedKeys++;
      }
    }

    return {
      totalKeys: this.store.size,
      blockedKeys,
      memoryUsage: JSON.stringify([...this.store.entries()]).length
    };
  }

  /**
   * Destroy the rate limiter and clean up resources
   */
  destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
    this.store.clear();
  }
}

// Global rate limiter instance
export const rateLimiter = new RateLimiter();

// Rate limit configurations for different scenarios
export const RATE_LIMIT_CONFIGS = {
  // Per-minute limits
  CHATBOT_PER_MINUTE: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 60,
    blockDurationMs: 5 * 60 * 1000 // Block for 5 minutes if exceeded
  },
  
  // Per-hour limits
  CHATBOT_PER_HOUR: {
    windowMs: 60 * 60 * 1000, // 1 hour
    maxRequests: 1000,
    blockDurationMs: 30 * 60 * 1000 // Block for 30 minutes if exceeded
  },
  
  // Per-day limits
  CHATBOT_PER_DAY: {
    windowMs: 24 * 60 * 60 * 1000, // 24 hours
    maxRequests: 10000,
    blockDurationMs: 60 * 60 * 1000 // Block for 1 hour if exceeded
  },
  
  // API key generation limits
  API_KEY_GENERATION: {
    windowMs: 60 * 60 * 1000, // 1 hour
    maxRequests: 5, // Max 5 API key operations per hour
    blockDurationMs: 60 * 60 * 1000 // Block for 1 hour
  },
  
  // Configuration access limits
  CONFIG_ACCESS: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 30, // More lenient for config access
  }
};

/**
 * Create a rate limit key for a specific chatbot and IP
 */
export function createRateLimitKey(chatbotId: string, identifier: string, type: string): string {
  return `${type}:${chatbotId}:${identifier}`;
}

/**
 * Hash an IP address for privacy
 */
export function hashIdentifier(identifier: string): string {
  const crypto = require('crypto');
  return crypto.createHash('sha256').update(identifier + (process.env.RATE_LIMIT_SALT || 'default-salt')).digest('hex');
}

/**
 * Get client IP from request headers
 */
export function getClientIP(headers: Headers): string {
  const forwarded = headers.get('x-forwarded-for');
  const realIP = headers.get('x-real-ip');
  
  if (forwarded) {
    return forwarded.split(',')[0].trim();
  }
  
  if (realIP) {
    return realIP;
  }
  
  return 'unknown';
}
