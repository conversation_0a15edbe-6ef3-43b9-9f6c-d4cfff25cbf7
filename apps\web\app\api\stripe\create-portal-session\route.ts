import { NextResponse } from "next/server";
import { getToken } from "next-auth/jwt";
import Strip<PERSON> from "stripe";
import db from "@/lib/shared-db";

// Initialize Stripe with the secret key
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY as string, {
  apiVersion: "2023-10-16" as any,
});

export async function POST(request: Request) {
  try {
    const token = await getToken({ req: request as any });
    if (!token) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const { tenantId, returnUrl } = body;

    // Validate required fields
    if (!tenantId || !returnUrl) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      );
    }

    // Verify user has access to this tenant
    const membership = await db.membership.findFirst({
      where: {
        userId: token.sub,
        tenantId,
        role: { in: ["ADMIN", "OWNER"] },
      },
    });

    if (!membership) {
      return NextResponse.json(
        { error: "You don't have admin permissions for this tenant" },
        { status: 403 }
      );
    }

    // Get the tenant's Stripe customer ID
    const tenant = await db.tenant.findUnique({
      where: {
        id: tenantId,
      },
      select: {
        stripeCustomerId: true,
      },
    });

    if (!tenant || !tenant.stripeCustomerId) {
      return NextResponse.json(
        { error: "Tenant has no associated Stripe customer" },
        { status: 404 }
      );
    }

    // Create a Stripe customer portal session with configuration
    const session = await stripe.billingPortal.sessions.create({
      customer: tenant.stripeCustomerId,
      return_url: returnUrl,
      configuration: process.env.STRIPE_PORTAL_CONFIGURATION_ID || undefined,
    });

    console.log(
      `Created portal session for customer ${tenant.stripeCustomerId}:`,
      session
    );

    return NextResponse.json({ url: session.url });
  } catch (error) {
    console.error("Error creating portal session:", error);
    return NextResponse.json(
      { error: "Failed to create portal session" },
      { status: 500 }
    );
  }
}
