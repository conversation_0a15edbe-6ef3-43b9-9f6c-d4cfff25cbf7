import { NextRequest, NextResponse } from "next/server";

import axios from "axios";
import db from "@/lib/shared-db";

const APP_URL = process.env.NEXT_PUBLIC_API_BASE_URL;
export async function GET(req: NextRequest) {
  const url = req.url;
  const urlParams = new URLSearchParams(url.split("?")[1]);
  const refresh_token = urlParams.get("refresh_token");
  const id = urlParams.get("id") ?? "";

  if (!refresh_token) {
    return NextResponse.json(
      { message: "Authorization code is missing" },
      { status: 400 }
    );
  }

  const tokenEndpoint = `https://login.microsoftonline.com/common/oauth2/v2.0/token`;
  const params = new URLSearchParams();

  params.append("client_id", process.env.NEXT_PUBLIC_MICROSOFT_CLIENT_ID ?? "");
  params.append(
    "scope",
    "offline_access Files.ReadWrite Files.ReadWrite.All Sites.ReadWrite.All User.Read"
  ); // Requested scopes
  params.append("grant_type", "refresh_token");
  params.append("refresh_token", refresh_token);
  params.append("client_secret", process.env.MICROSOFT_CLIENT_SECRET ?? "");

  try {
    const response = await axios.post(tokenEndpoint, params, {
      headers: { "Content-Type": "application/x-www-form-urlencoded" },
    });
    if (response.data?.access_token) {
      const integration = await db.integration.update({
        where: { id },
        data: { accessToken: response.data?.access_token },
      });
      if (integration.id) {
        return NextResponse.json({ integration }, { status: 200 });
      } else {
        return NextResponse.json(
          { error: "Failed to fetch access token" },
          { status: 500 }
        );
      }
    } else {
      return NextResponse.json(
        { error: "Failed to fetch access token" },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error("Token acquisition failed:", error?.response?.data);
    if ([700082]?.includes(error?.response?.data?.error_codes)) {
      await db.integration.delete({
        where: { id },
      });
    }
    return NextResponse.json(
      { error: "Failed to fetch access token" },
      { status: 500 }
    );
  }
}
