"use client";

import { useState, useEffect } from "react";
import { Loader2, <PERSON><PERSON>ard, AlertTriangle } from "lucide-react";
import { PricingCard } from "@/components/pricing/pricing-card";
import { ManageSubscriptionAddons } from "@/components/pricing/manage-subscription-addons";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import {
  getStripePrices,
  PlanPrice,
  StorageTierPrice,
} from "@/services/src/stripe-prices";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { createCustomerPortalSession } from "@/services";
import toast from "react-hot-toast";
import { Progress } from "@/components/ui/progress";
import { getCookie, setCookie } from "@/utils/cookies";
import { useRouter, useSearchParams } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { useLanguage } from "@/lib/language-context";
import { Alert, AlertDescription, AlertTitle } from "../ui/alert";
import { Plan } from "@/services/src/plans";
import { SubscriptionWithPlan } from "@/services/src/subscriptions";
import { VectorStoreUsageSummary } from "@/services/src/vector-store-usage";

interface BillingScreenProps {
  plans: Plan[];
  subscription: SubscriptionWithPlan | null;
  usageSummary: VectorStoreUsageSummary | null;
}

export default function BillingScreen({
  plans,
  subscription,
  usageSummary,
}: BillingScreenProps) {
  const router = useRouter();
  const searchParams = useSearchParams() ?? null;
  const tenantId = getCookie("currentOrganizationId") ?? "";
  const [loading, setLoading] = useState(false);
  const [billingInterval, setBillingInterval] = useState<"month" | "year">(
    (subscription?.billingInterval as any) || "month"
  );
  const [stripePrices, setStripePrices] = useState<{
    plans: PlanPrice[];
    storageTiers: StorageTierPrice[];
  } | null>(null);
  const [loadingPrices, setLoadingPrices] = useState(false);
  const { t } = useLanguage();
  const additionalStorageGB =
    subscription?.storageTierItems?.reduce(
      (total, item) => total + item.size * item.quantity,
      0
    ) || 0;
  // Fetch prices from Stripe
  useEffect(() => {
    const fetchPrices = async () => {
      try {
        setLoadingPrices(true);
        const prices = await getStripePrices();

        // Check if we got valid price data
        const hasValidPrices = prices.plans.some(
          (plan) =>
            plan.monthlyPrice?.unitAmount || plan.yearlyPrice?.unitAmount
        );

        if (hasValidPrices) {
          setStripePrices(prices);
        } else {
          console.warn(
            "No valid Stripe prices found, using fallback prices from database"
          );
          // We'll use the fallback prices from the database
          setStripePrices(null);
        }
      } catch (error) {
        console.error("Error fetching Stripe prices:", error);
        toast.error(t("billing.errorFetchingPrices"));
        // Use fallback prices from database
        setStripePrices(null);
      } finally {
        setLoadingPrices(false);
      }
    };

    fetchPrices();
  }, [t]);

  // Check for success or canceled query parameters from Stripe checkout
  useEffect(() => {
    const success = searchParams?.get("success");
    const canceled = searchParams?.get("canceled");

    if (success === "true") {
      toast.success(t("billing.subscriptionUpdated"));
      // Remove query parameters
      router.replace("/billing");
    } else if (canceled === "true") {
      toast.error(t("billing.subscriptionCanceled"));
      // Remove query parameters
      router.replace("/billing");
    }

    // Set a cookie to indicate if the user has an active subscription
    if (subscription?.isActive) {
      setCookie("hasActiveSubscription", "true", 30); // 30 days expiry
    } else {
      setCookie("hasActiveSubscription", "", -1); // Remove cookie
    }
  }, [searchParams, router, subscription]);

  // Handle opening Stripe customer portal
  const handleManageSubscription = async () => {
    if (!tenantId) return;

    try {
      setLoading(true);
      const portalUrl = await createCustomerPortalSession(
        tenantId,
        `${window.location.origin}/billing`
      );

      // Redirect to Stripe customer portal
      window.location.href = portalUrl;
    } catch (error) {
      console.error("Error opening customer portal:", error);
      toast.error(t("billing.portalOpenFailed"));
    } finally {
      setLoading(false);
    }
  };

  if (!tenantId) {
    return (
      <div className="flex h-full items-center justify-center">
        <p>{t("billing.noTenantSelected")}</p>
      </div>
    );
  }

  const usagePercentage =
    usageSummary && usageSummary.limit > 0
      ? Math.min(100, (usageSummary.totalUsage / usageSummary.limit) * 100)
      : 0;

  return (
    <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8">
      <div className="mb-6 sm:mb-8">
        <h1 className="text-2xl sm:text-3xl font-bold break-words">{t("billing.title")}</h1>
        <p className="text-muted-foreground break-words">{t("billing.subtitle")}</p>
      </div>

      {subscription ? (
        <>
          <div className="grid gap-6 mb-8 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>{t("billing.currentSubscription")}</CardTitle>
                <CardDescription>
                  {t("billing.currentSubscriptionDesc")}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h3 className="font-medium">{t("billing.plan")}</h3>
                  <div className="flex items-center gap-2">
                    <p className="text-2xl font-bold">
                      {subscription.plan.name}
                    </p>
                    {subscription.isOnTrial && (
                      <span className="bg-blue-100 text-blue-800 text-xs px-2 py-0.5 rounded-full dark:bg-blue-900 dark:text-blue-100">
                        {t("billing.trialBadge")}
                      </span>
                    )}
                  </div>

                  {subscription.isOnTrial && subscription.trialEndDate && (
                    <p className="text-sm text-blue-600 dark:text-blue-400 mt-1">
                      {t("billing.trialEndsOn", {
                        date: new Date(
                          subscription.trialEndDate
                        ).toLocaleDateString(),
                      })}
                    </p>
                  )}

                  <p className="text-sm text-muted-foreground mt-2">
                    {subscription.additionalUsers > 0
                      ? t("billing.includedPlusAdditionalUsers", {
                          included: subscription.plan.includedUsers,
                          additional: subscription.additionalUsers,
                        })
                      : t("billing.includedUsers", {
                          count: subscription.plan.includedUsers,
                        })}
                  </p>
                  <p className="text-sm text-muted-foreground">
                    {(additionalStorageGB ?? 0) > 0
                      ? t("billing.includedPlusAdditionalStorage", {
                          included: subscription.plan.vectorStoreGB,
                          additional: additionalStorageGB,
                        })
                      : t("billing.includedStorage", {
                          count: subscription.plan.vectorStoreGB,
                        })}
                  </p>
                </div>
                <div>
                  <h3 className="font-medium">
                    {t("billing.vectorStoreUsage")}
                  </h3>
                  <div className="mt-2 space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>
                        {t("billing.usageOf", {
                          used: usageSummary?.totalUsage.toFixed(2),
                          total: usageSummary?.limit,
                        })}
                      </span>
                      <span>{usagePercentage.toFixed(0)}%</span>
                    </div>
                    <Progress value={usagePercentage} className="h-2" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <ManageSubscriptionAddons
              stripePrices={stripePrices}
              subscription={subscription}
              tenantId={tenantId}
              usageSummary={usageSummary ?? undefined}
            />
          </div>
        </>
      ) : (
        <Alert variant="destructive" className="mb-6">
          <AlertTriangle className="h-4 w-4" />
          <AlertTitle>
            {t("subscription.noActiveSubscription") || "No Active Subscription"}
          </AlertTitle>
          <AlertDescription>
            {t("subscription.noSubscriptionMessage") ||
              "You don't have an active subscription. You won't be able to create workspaces or invite members."}
          </AlertDescription>
        </Alert>
      )}

      <div className="mb-4 sm:mb-6">
        <h2 className="text-xl sm:text-2xl font-bold break-words">{t("billing.availablePlans")}</h2>
        <p className="text-muted-foreground break-words">{t("billing.choosePlan")}</p>

        <div className="mt-4 flex justify-center">
          <RadioGroup
            value={billingInterval}
            onValueChange={(value) =>
              setBillingInterval(value as "month" | "year")
            }
            className="flex items-center space-x-4"
          >
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="month" id="month" />
              <Label htmlFor="month">{t("billing.monthly")}</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="year" id="year" />
              <Label htmlFor="year">
                {t("billing.yearly")}
                <span className="ml-2 inline-block bg-green-100 text-green-800 text-xs px-2 py-0.5 rounded-full dark:bg-green-900 dark:text-green-100">
                  {t("billing.save2Months")}
                </span>
              </Label>
            </div>
          </RadioGroup>
        </div>
      </div>

      {subscription && (
        <div className="mb-4 flex justify-end">
          <Button
            onClick={handleManageSubscription}
            variant="outline"
            disabled={loading}
          >
            {loading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                {t("common.loading")}
              </>
            ) : (
              <>
                <CreditCard className="mr-2 h-4 w-4" />
                {t("billing.manageSubscription")}
              </>
            )}
          </Button>
        </div>
      )}

      <div className="grid gap-4 sm:gap-6 sm:grid-cols-1 md:grid-cols-1 lg:grid-cols-2 xl:grid-cols-4">
        {loadingPrices ? (
          <div className="col-span-4 flex justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
          </div>
        ) : (
          plans.map((plan: Plan) => {
            // Find the Stripe price data for this plan
            const stripePlanData = stripePrices?.plans.find(
              (p) => p.planId === plan.id
            );
            return (
              <PricingCard
                key={plan.id}
                plan={plan}
                isCurrentPlan={
                  subscription?.plan.id === plan.id &&
                  subscription?.billingInterval === billingInterval
                }
                billingInterval={billingInterval}
                stripePriceData={stripePlanData}
              />
            );
          })
        )}
      </div>
    </div>
  );
}
