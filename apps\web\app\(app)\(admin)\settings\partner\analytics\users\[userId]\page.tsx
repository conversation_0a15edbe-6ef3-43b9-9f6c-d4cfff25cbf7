"use client";

import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  User,
  MapPin,
  Monitor,
  Globe,
  MessageSquare,
  Star,
  AlertTriangle,
  ArrowLeft,
  Smartphone,
  Wifi,
  Target,
  Activity,
  Ban,
  Bot,
} from "lucide-react";
import { getCookie } from "@/utils/cookies";
import { useLanguage } from "@/lib/language-context";
import toast from "react-hot-toast";

interface UserChatbot {
  id: string;
  name: string;
  type: string;
  access: string;
  createdAt: string;
  isActive: boolean;
  usageCount: number;
  monthlyUsage: number;
  lastUsedAt?: string;
  sessionsCount: number;
  messagesCount: number;
  userMessagesCount: number;
  assistantMessagesCount: number;
  averageResponseTime?: number;
  errorCount: number;
}

interface UserDetails {
  userId?: string;
  name: string;
  email?: string;
  totalSessions: number;
  totalMessages: number;
  totalUserMessages: number;
  totalAssistantMessages: number;
  totalDuration: number;
  averageDuration: number;
  firstSeen: string;
  lastSeen: string;
  location: {
    country?: string;
    city?: string;
    region?: string;
    timezone?: string;
  };
  devices: {
    types: string[];
    operatingSystems: string[];
    browsers: string[];
    screenResolutions: string[];
  };
  network: {
    ipAddresses: string[];
    isps: string[];
    organizations: string[];
    connectionTypes: string[];
  };
  web: {
    domains: string[];
    referrers: string[];
    languages: string[];
    userAgents: string[];
  };
  marketing: {
    utmSources: string[];
    utmMediums: string[];
    utmCampaigns: string[];
    utmTerms: string[];
    utmContents: string[];
  };
  behavior: {
    isReturning: boolean;
    maxPreviousVisits: number;
    averageSessionDepth: number;
    totalErrors: number;
    errorRate: number;
  };
  satisfaction: {
    ratings: number[];
    averageRating?: number;
    feedback: string[];
  };
  performance: {
    averageResponseTime?: number;
    chatbots: Array<{ id: string; name: string; access: string }>;
  };
  recentSessions: Array<{
    id: string;
    sessionToken: string;
    chatbotName: string;
    startedAt: string;
    endedAt?: string;
    duration?: number;
    messagesCount: number;
    userMessagesCount: number;
    assistantMessagesCount: number;
    domain?: string;
    pageUrl?: string;
    pageTitle?: string;
    deviceType?: string;
    browserName?: string;
    country?: string;
    city?: string;
    satisfactionRating?: number;
    errorCount: number;
    metadata?: any;
  }>;
  userChatbots: UserChatbot[];
}

export default function UserDetailsPage() {
  const params = useParams() ?? {};
  const router = useRouter();
  const userId = params.userId as string;
  const tenantId = getCookie("currentOrganizationId");
  const { t } = useLanguage();

  const [user, setUser] = useState<UserDetails | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isBlocked, setIsBlocked] = useState(false);
  const [blockLoading, setBlockLoading] = useState(false);

  useEffect(() => {
    const loadUserDetails = async () => {
      if (!tenantId || !userId) return;

      setLoading(true);
      setError(null);

      try {
        const response = await fetch(
          `/api/analytics/users/${encodeURIComponent(
            userId
          )}?tenantId=${tenantId}`
        );

        if (!response.ok) {
          throw new Error(`Failed to fetch user details: ${response.status}`);
        }

        const data = await response.json();
        setUser(data.user);
      } catch (error) {
        console.error("Error loading user details:", error);
        setError(
          error instanceof Error ? error.message : "Failed to load user details"
        );
      } finally {
        setLoading(false);
      }
    };

    loadUserDetails();
    checkBlockStatus();
  }, [userId, tenantId]);

  const checkBlockStatus = async () => {
    if (!tenantId || !userId) return;

    try {
      const response = await fetch(
        `/api/analytics/users/${encodeURIComponent(
          userId
        )}/block?tenantId=${tenantId}`
      );

      if (response.ok) {
        const data = await response.json();
        setIsBlocked(data.isBlocked);
      }
    } catch (error) {
      console.error("Error checking block status:", error);
    }
  };

  const handleBlockUser = async () => {
    if (!tenantId || !userId) return;

    setBlockLoading(true);
    try {
      const blockType = userId.startsWith("user_")
        ? "user"
        : userId.startsWith("email_")
          ? "email"
          : "ip";

      const response = await fetch(
        `/api/analytics/users/${encodeURIComponent(
          userId
        )}/block?tenantId=${tenantId}`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            blockType,
            reason: "Blocked by administrator",
          }),
        }
      );

      if (response.ok) {
        setIsBlocked(true);
        toast.success("User blocked successfully");
        // Refresh block status to ensure UI is in sync
        await checkBlockStatus();
      } else {
        const errorData = await response.json();
        toast.error(`Failed to block user: ${errorData.error}`);
      }
    } catch (error) {
      console.error("Error blocking user:", error);
      toast.error("Failed to block user");
    } finally {
      setBlockLoading(false);
    }
  };

  const handleUnblockUser = async () => {
    if (!tenantId || !userId) return;

    setBlockLoading(true);
    try {
      const response = await fetch(
        `/api/analytics/users/${encodeURIComponent(
          userId
        )}/block?tenantId=${tenantId}`,
        {
          method: "DELETE",
        }
      );

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setIsBlocked(false);
          toast.success(
            `User unblocked successfully (${data.deletedCount} records deleted)`
          );
          // Refresh block status to ensure UI is in sync
          await checkBlockStatus();
        } else {
          toast.error(data.message || "No active blocks found for this user");
        }
      } else {
        const errorData = await response.json();
        toast.error(
          `Failed to unblock user: ${errorData.error || errorData.message}`
        );
      }
    } catch (error) {
      console.error("Error unblocking user:", error);
      toast.error("Failed to unblock user");
    } finally {
      setBlockLoading(false);
    }
  };

  const formatDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const remainingSeconds = seconds % 60;

    if (hours > 0) {
      return `${hours}h ${minutes}m ${remainingSeconds}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${remainingSeconds}s`;
    } else {
      return `${remainingSeconds}s`;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-4"></div>
          <span>{t("analytics.loadingUserDetails")}</span>
        </div>
      </div>
    );
  }

  if (error || !user) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button variant="outline" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            {t("common.back")}
          </Button>
          <div>
            <h1 className="text-3xl font-bold">{t("analytics.userDetails")}</h1>
            <p className="text-muted-foreground">
              {t("analytics.detailedUserInformationAndAnalytics")}
            </p>
          </div>
        </div>
        <Card>
          <CardContent className="pt-6">
            <div className="text-center py-12">
              <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">
                {t("analytics.failedToLoadUserDetails")}
              </h3>
              <p className="text-muted-foreground mb-4">
                {error || "User not found"}
              </p>
              <Button onClick={() => router.back()}>
                <ArrowLeft className="h-4 w-4 mr-2" />
                {t("common.goBack")}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button variant="outline" onClick={() => router.back()}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          {t("common.back")}
        </Button>
        <div className="flex-1">
          <h1 className="text-3xl font-bold">{user.name}</h1>
          <p className="text-muted-foreground">
            {user.email || t("analytics.anonymousUser")} • {user.totalSessions}{" "}
            {t("analytics.sessions")}
          </p>
        </div>
        <div className="flex gap-2">
          {/* <Button variant="outline" size="sm">
            <Eye className="h-4 w-4 mr-2" />
            View Sessions
          </Button> */}
          {isBlocked ? (
            <Button
              variant="outline"
              size="sm"
              onClick={handleUnblockUser}
              disabled={blockLoading}
            >
              <Ban className="h-4 w-4 mr-2" />
              {blockLoading
                ? t("analytics.unblocking")
                : t("analytics.unblockUser")}
            </Button>
          ) : (
            <Button
              variant="destructive"
              size="sm"
              onClick={handleBlockUser}
              disabled={blockLoading}
            >
              <Ban className="h-4 w-4 mr-2" />
              {blockLoading
                ? t("analytics.blocking")
                : t("analytics.blockUser")}
            </Button>
          )}
        </div>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {t("analytics.totalSessions")}
            </CardTitle>
            <MessageSquare className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{user.totalSessions}</div>
            <div className="space-y-1 text-xs text-muted-foreground">
              <p className="">
                {user.totalMessages} {t("analytics.totalMessages")}
              </p>
              <div className="flex gap-4 text-xs text-muted-foreground">
                <span>
                  {user.totalUserMessages} {t("analytics.userMessages")}
                </span>
              </div>
              <span>
                {user.totalAssistantMessages} {t("analytics.assistantMessages")}
              </span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {t("analytics.chatbot")}
            </CardTitle>
            <Bot className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{user.userChatbots.length}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {t("analytics.location")}
            </CardTitle>
            <MapPin className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {user.location.city ||
                user.location.country ||
                t("common.unknown")}
            </div>
            <p className="text-xs text-muted-foreground">
              {user.location.region && user.location.country
                ? `${user.location.region}, ${user.location.country}`
                : user.location.country || t("analytics.unknownLocation")}
            </p>
          </CardContent>
        </Card>

        {/* <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Risk Level</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <Badge variant={riskLevel.color as any} className="text-sm">
                {riskLevel.level.toUpperCase()}
              </Badge>
            </div>
            <p className="text-xs text-muted-foreground">
              {user.behavior.errorRate.toFixed(1)}% error rate
            </p>
          </CardContent>
        </Card> */}
      </div>

      {/* Detailed Information Tabs */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">{t("common.overview")}</TabsTrigger>
          <TabsTrigger value="sessions">{t("common.sessions")}</TabsTrigger>
          <TabsTrigger value="chatbots">{t("common.chatbots")}</TabsTrigger>
          <TabsTrigger value="technical">{t("common.technical")}</TabsTrigger>
          <TabsTrigger value="marketing">{t("common.marketing")}</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* User Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User className="h-5 w-5" />
                  {t("analytics.userInformation")}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      {t("common.name")}
                    </label>
                    <p className="text-sm">{user.name}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      {t("common.email")}
                    </label>
                    <p className="text-sm">{user.email || "Not provided"}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      {t("analytics.firstSeen")}
                    </label>
                    <p className="text-sm">{formatDate(user.firstSeen)}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      {t("analytics.lastSeen")}
                    </label>
                    <p className="text-sm">{formatDate(user.lastSeen)}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      {t("analytics.returningUser")}
                    </label>
                    <p className="text-sm">
                      {user.behavior.isReturning
                        ? t("common.yes")
                        : t("common.no")}
                      {user.behavior.maxPreviousVisits > 0 &&
                        ` (${user.behavior.maxPreviousVisits} previous visits)`}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      {t("analytics.timezone")}
                    </label>
                    <p className="text-sm">
                      {user.location.timezone || "Unknown"}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Behavior Analytics */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Activity className="h-5 w-5" />
                  {t("analytics.behaviorAnalytics")}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      {t("analytics.avgSessionDepth")}
                    </label>
                    <p className="text-sm">
                      {user.behavior.averageSessionDepth.toFixed(1)} pages
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      {t("analytics.errorRate")}
                    </label>
                    <p className="text-sm">
                      {user.behavior.errorRate.toFixed(1)}%
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      {t("analytics.averageResponseTime")}
                    </label>
                    <p className="text-sm">
                      {user.performance.averageResponseTime
                        ? `${user.performance.averageResponseTime.toFixed(2)}s`
                        : "N/A"}
                    </p>
                  </div>
                  {user.satisfaction.averageRating && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        {t("analytics.satisfaction")}
                      </label>
                      <div className="flex items-center gap-1">
                        <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                        <span className="text-sm">
                          {user.satisfaction.averageRating.toFixed(1)}
                        </span>
                        <span className="text-xs text-muted-foreground">
                          ({user.satisfaction.ratings.length} ratings)
                        </span>
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="sessions" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>{t("analytics.recentSessions")}</CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>{t("analytics.chatbot")}</TableHead>
                    <TableHead>{t("analytics.startedAt")}</TableHead>
                    <TableHead>{t("analytics.duration")}</TableHead>
                    <TableHead>{t("analytics.messages")}</TableHead>
                    <TableHead>{t("analytics.device")}</TableHead>
                    <TableHead>{t("analytics.location")}</TableHead>
                    {/* <TableHead>Rating</TableHead> */}
                    <TableHead>{t("analytics.errors")}</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {user.recentSessions.map((session) => (
                    <TableRow key={session.id}>
                      <TableCell className="font-medium">
                        {session.chatbotName}
                      </TableCell>
                      <TableCell>{formatDate(session.startedAt)}</TableCell>
                      <TableCell>
                        {session.duration
                          ? formatDuration(session.duration)
                          : "Ongoing"}
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1">
                          <div className="font-medium">
                            {session.messagesCount}
                          </div>
                          <div className="flex gap-2 text-xs text-muted-foreground">
                            <span className="flex items-center gap-1">
                              {" "}
                              <User className="h-3 w-3" />{" "}
                              {session.userMessagesCount}
                            </span>
                            <span className="flex items-center gap-1">
                              {" "}
                              <Bot className="h-3 w-3" />{" "}
                              {session.assistantMessagesCount}
                            </span>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          <Smartphone className="h-3 w-3" />
                          {session.deviceType || "Unknown"}
                        </div>
                      </TableCell>
                      <TableCell>
                        {session.city && session.country
                          ? `${session.city}, ${session.country}`
                          : session.country || "Unknown"}
                      </TableCell>
                      {/* <TableCell>
                        {session.satisfactionRating ? (
                          <div className="flex items-center gap-1">
                            <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                            {session.satisfactionRating}
                          </div>
                        ) : (
                          "N/A"
                        )}
                      </TableCell> */}
                      <TableCell>
                        {session.errorCount > 0 ? (
                          <Badge variant="destructive">
                            {session.errorCount}
                          </Badge>
                        ) : (
                          <Badge variant="outline">0</Badge>
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="technical" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Device Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Monitor className="h-5 w-5" />
                  {t("analytics.deviceInformation")}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    {t("analytics.deviceTypes")}
                  </label>
                  <div className="flex flex-wrap gap-1 mt-1">
                    {user.devices.types.map((type) => (
                      <Badge key={type} variant="outline">
                        {type}
                      </Badge>
                    ))}
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    {t("analytics.operatingSystems")}
                  </label>
                  <div className="flex flex-wrap gap-1 mt-1">
                    {user.devices.operatingSystems.map((os) => (
                      <Badge key={os} variant="outline">
                        {os}
                      </Badge>
                    ))}
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    {t("analytics.browsers")}
                  </label>
                  <div className="flex flex-wrap gap-1 mt-1">
                    {user.devices.browsers.map((browser) => (
                      <Badge key={browser} variant="outline">
                        {browser}
                      </Badge>
                    ))}
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    {t("analytics.screenResolutions")}
                  </label>
                  <div className="flex flex-wrap gap-1 mt-1">
                    {user.devices.screenResolutions.map((resolution) => (
                      <Badge key={resolution} variant="outline">
                        {resolution}
                      </Badge>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Network Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Wifi className="h-5 w-5" />
                  {t("analytics.networkInformation")}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    {t("analytics.ipAddresses")}
                  </label>
                  <div className="flex flex-wrap gap-1 mt-1">
                    {user.network.ipAddresses.slice(0, 3).map((ip, index) => (
                      <Badge key={index} variant="outline">
                        {ip.substring(0, 8)}...
                      </Badge>
                    ))}
                    {user.network.ipAddresses.length > 3 && (
                      <Badge variant="outline">
                        +{user.network.ipAddresses.length - 3} more
                      </Badge>
                    )}
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    {t("analytics.isps")}
                  </label>
                  <div className="flex flex-wrap gap-1 mt-1">
                    {user.network.isps.map((isp) => (
                      <Badge key={isp} variant="outline">
                        {isp}
                      </Badge>
                    ))}
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    {t("analytics.organizations")}
                  </label>
                  <div className="flex flex-wrap gap-1 mt-1">
                    {user.network.organizations.map((org) => (
                      <Badge key={org} variant="outline">
                        {org}
                      </Badge>
                    ))}
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    {t("analytics.connectionTypes")}
                  </label>
                  <div className="flex flex-wrap gap-1 mt-1">
                    {user.network.connectionTypes.map((type) => (
                      <Badge key={type} variant="outline">
                        {type}
                      </Badge>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Web Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Globe className="h-5 w-5" />
                  {t("analytics.webInformation")}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    {t("analytics.domains")}
                  </label>
                  <div className="flex flex-wrap gap-1 mt-1">
                    {user.web.domains.map((domain) => (
                      <Badge key={domain} variant="outline">
                        {domain}
                      </Badge>
                    ))}
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    {t("analytics.languages")}
                  </label>
                  <div className="flex flex-wrap gap-1 mt-1">
                    {user.web.languages.map((lang) => (
                      <Badge key={lang} variant="outline">
                        {lang}
                      </Badge>
                    ))}
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    {t("analytics.referrers")}
                  </label>
                  <div className="flex flex-wrap gap-1 mt-1">
                    {user.web.referrers.slice(0, 3).map((referrer) => (
                      <Badge key={referrer} variant="outline">
                        {new URL(referrer).hostname}
                      </Badge>
                    ))}
                    {user.web.referrers.length > 3 && (
                      <Badge variant="outline">
                        +{user.web.referrers.length - 3} {t("common.more")}
                      </Badge>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Chatbots Used */}
            {/* <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MessageSquare className="h-5 w-5" />
                  Chatbots Used ({user.performance.chatbots.length})
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {uniqueChatbots.map((chatbot) => {
                    // Count sessions for this chatbot
                    const chatbotSessions = user.recentSessions.filter(
                      (session) => session.chatbotName === chatbot.name
                    ).length;

                    return (
                      <div
                        key={chatbot.id}
                        className="flex items-center justify-between p-3 border rounded-lg hover:bg-muted/50 transition-colors"
                      >
                        <div className="flex flex-col">
                          <span className="font-medium">{chatbot.name}</span>
                          <span className="text-sm text-muted-foreground">
                            {chatbotSessions} session
                            {chatbotSessions !== 1 ? "s" : ""}
                          </span>
                        </div>
                        <Badge
                          variant={
                            chatbot.access === "public"
                              ? "default"
                              : "secondary"
                          }
                        >
                          {chatbot.access}
                        </Badge>
                      </div>
                    );
                  })}
                  {user.performance.chatbots.length === 0 && (
                    <div className="text-center py-4 text-muted-foreground">
                      No chatbots used yet
                    </div>
                  )}
                </div>
              </CardContent>
            </Card> */}
          </div>
        </TabsContent>

        <TabsContent value="chatbots" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MessageSquare className="h-5 w-5" />
                {t("analytics.usersChatbots")}
              </CardTitle>
            </CardHeader>
            <CardContent>
              {user.userChatbots && user.userChatbots.length > 0 ? (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>{t("analytics.name")}</TableHead>
                      <TableHead>{t("analytics.type")}</TableHead>
                      <TableHead>{t("analytics.access")}</TableHead>
                      <TableHead>{t("analytics.status")}</TableHead>
                      <TableHead>{t("analytics.sessions")}</TableHead>
                      <TableHead>{t("analytics.messages")}</TableHead>
                      <TableHead>{t("analytics.usage")}</TableHead>
                      <TableHead>{t("analytics.lastUsed")}</TableHead>
                      <TableHead>{t("analytics.created")}</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {user.userChatbots.map((chatbot) => (
                      <TableRow key={chatbot.id}>
                        <TableCell className="font-medium">
                          {chatbot.name}
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline">
                            {chatbot.type.replace("-", " ")}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Badge
                            variant={
                              chatbot.access === "public"
                                ? "default"
                                : "secondary"
                            }
                          >
                            {chatbot.access}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Badge
                            variant={
                              chatbot.isActive ? "default" : "destructive"
                            }
                          >
                            {chatbot.isActive
                              ? t("common.active")
                              : t("common.inactive")}
                          </Badge>
                        </TableCell>
                        <TableCell>{chatbot.sessionsCount}</TableCell>
                        <TableCell>
                          <div className="space-y-1">
                            <div className="font-medium">
                              {chatbot.messagesCount}
                            </div>
                            <div className="flex gap-2 text-xs text-muted-foreground">
                              <span className="flex items-center gap-1">
                                {" "}
                                <User className="h-3 w-3" />{" "}
                                {chatbot.userMessagesCount}
                              </span>
                              <span className="flex items-center gap-1">
                                {" "}
                                <Bot className="h-3 w-3" />{" "}
                                {chatbot.assistantMessagesCount}
                              </span>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="text-sm">
                            <div>
                              {chatbot.usageCount} {t("analytics.total")}
                            </div>
                            <div className="text-muted-foreground">
                              {chatbot.monthlyUsage} {t("analytics.monthly")}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          {chatbot.lastUsedAt ? (
                            <div className="text-sm">
                              {new Date(
                                chatbot.lastUsedAt
                              ).toLocaleDateString()}
                              <div className="text-muted-foreground">
                                {new Date(
                                  chatbot.lastUsedAt
                                ).toLocaleTimeString()}
                              </div>
                            </div>
                          ) : (
                            <span className="text-muted-foreground">
                              {t("common.never")}
                            </span>
                          )}
                        </TableCell>
                        <TableCell>
                          <div className="text-sm">
                            {new Date(chatbot.createdAt).toLocaleDateString()}
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  <MessageSquare className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>{t("analytics.thisUserHasNotCreatedAnyChatbotsYet")}</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="marketing" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="h-5 w-5" />
                {t("analytics.marketingAttribution")}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    {t("analytics.utmSources")}
                  </label>
                  <div className="flex flex-wrap gap-1 mt-1">
                    {user.marketing.utmSources.length > 0 ? (
                      user.marketing.utmSources.map((source) => (
                        <Badge key={source} variant="outline">
                          {source}
                        </Badge>
                      ))
                    ) : (
                      <span className="text-sm text-muted-foreground">
                        {t("analytics.noUtmSourcesTracked")}
                      </span>
                    )}
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    {t("analytics.utmMediums")}
                  </label>
                  <div className="flex flex-wrap gap-1 mt-1">
                    {user.marketing.utmMediums.length > 0 ? (
                      user.marketing.utmMediums.map((medium) => (
                        <Badge key={medium} variant="outline">
                          {medium}
                        </Badge>
                      ))
                    ) : (
                      <span className="text-sm text-muted-foreground">
                        {t("analytics.noUtmMediumsTracked")}
                      </span>
                    )}
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    {t("analytics.utmCampaigns")}
                  </label>
                  <div className="flex flex-wrap gap-1 mt-1">
                    {user.marketing.utmCampaigns.length > 0 ? (
                      user.marketing.utmCampaigns.map((campaign) => (
                        <Badge key={campaign} variant="outline">
                          {campaign}
                        </Badge>
                      ))
                    ) : (
                      <span className="text-sm text-muted-foreground">
                        {t("analytics.noUtmCampaignsTracked")}
                      </span>
                    )}
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    {t("analytics.utmTerms")}
                  </label>
                  <div className="flex flex-wrap gap-1 mt-1">
                    {user.marketing.utmTerms.length > 0 ? (
                      user.marketing.utmTerms.map((term) => (
                        <Badge key={term} variant="outline">
                          {term}
                        </Badge>
                      ))
                    ) : (
                      <span className="text-sm text-muted-foreground">
                        {t("analytics.noUtmTermsTracked")}
                      </span>
                    )}
                  </div>
                </div>
              </div>

              {user.marketing.utmContents.length > 0 && (
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    {t("analytics.utmContent")}
                  </label>
                  <div className="flex flex-wrap gap-1 mt-1">
                    {user.marketing.utmContents.map((content) => (
                      <Badge key={content} variant="outline">
                        {content}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}

              {user.satisfaction.feedback.length > 0 && (
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    {t("analytics.userFeedback")}
                  </label>
                  <div className="space-y-2 mt-2">
                    {user.satisfaction.feedback.map((feedback, index) => (
                      <div key={index} className="p-3 bg-muted rounded-lg">
                        <p className="text-sm">{feedback}</p>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
