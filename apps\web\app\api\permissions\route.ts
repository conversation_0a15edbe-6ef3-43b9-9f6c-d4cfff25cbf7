import { NextResponse } from "next/server";
import { getToken } from "next-auth/jwt";
import db from "@/lib/shared-db";

/**
 * GET /api/permissions
 * Get all available permissions
 */
export async function GET(request: Request) {
  try {
    const token = await getToken({ req: request as any });
    if (!token) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get all permissions
    const permissions = await db.permission.findMany({
      orderBy: [{ resource: "asc" }, { action: "asc" }],
    });

    return NextResponse.json({ permissions });
  } catch (error) {
    console.error("Error fetching permissions:", error);
    return NextResponse.json(
      { error: "Failed to fetch permissions" },
      { status: 500 }
    );
  }
}

/**
 * POST /api/permissions
 * Initialize default permissions if they don't exist
 * This should be called during system setup or first run
 */
export async function POST(request: Request) {
  try {
    const token = await getToken({ req: request as any });
    if (!token) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if user is a system admin (you may need to implement this check)
    // For now, we'll just check if the user exists
    const user = await db.user.findUnique({
      where: { id: token.sub },
    });

    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Define all possible permission combinations
    interface PermissionCombination {
      action: string;
      resource: string;
      description: string;
    }

    const permissionCombinations: PermissionCombination[] = [];

    // Generate all combinations of actions and resources
    // Use explicit values instead of Object.values to avoid enum issues
    const actions = ["CREATE", "READ", "UPDATE", "DELETE"];
    const resources = ["WORKSPACE", "PAGE", "FOLDER", "FILE", "MEMBER"];

    for (const action of actions) {
      for (const resource of resources) {
        permissionCombinations.push({
          action,
          resource,
          description: `${action} ${resource.toLowerCase()}`,
        });
      }
    }

    // Create permissions if they don't exist
    const createdPermissions: any[] = [];

    for (const perm of permissionCombinations) {
      const existingPermission = await db.permission.findFirst({
        where: {
          action: perm.action,
          resource: perm.resource,
        },
      });

      if (!existingPermission) {
        const newPermission = await db.permission.create({
          data: {
            action: perm.action,
            resource: perm.resource,
            description: perm.description,
          },
        });
        createdPermissions.push(newPermission);
      }
    }

    return NextResponse.json({
      message: "Permissions initialized successfully",
      createdCount: createdPermissions.length,
    });
  } catch (error) {
    console.error("Error initializing permissions:", error);
    return NextResponse.json(
      { error: "Failed to initialize permissions" },
      { status: 500 }
    );
  }
}
