# Swiss Knowledge Hub

A modern Retrieval Augmented Generation (RAG) system that allows users to upload documents and chat with their content using AI.

## Features

- **Document Management**: Upload, store, and search PDF documents
- **RAG-powered Chat**: Interact with document content through natural language
- **Multi-provider Support**: Works with OpenAI and Azure OpenAI
- **Vector Database Integration**: Compatible with ChromaDB, LanceDB, and Pinecone
- **Modern UI**: Responsive design built with Next.js and TailwindCSS

## Architecture

Swiss Knowledge Hub uses a monorepo structure managed with Turborepo and PNPM:

- **Frontend**: Next.js application with TailwindCSS and Radix UI components
- **Backend**: FastAPI service providing document processing and AI integration
- **Infrastructure**: Terraform scripts for cloud deployment
- **Documentation**: Mintlify-powered documentation site

## Tech Stack

- **Frontend**: Next.js, React, TailwindCSS, Radix UI
- **Backend**: FastAPI, Python, LangChain
- **Database**: Prisma ORM
- **Vector Stores**: ChromaDB, LanceDB, Pinecone
- **AI Integration**: OpenAI, Azure OpenAI
- **Authentication**: NextAuth
- **DevOps**: Turborepo, PNPM, Terraform

## Getting Started

### Prerequisites

- Node.js (v18+)
- Python (v3.10+)
- PNPM

### Installation

1. Clone the repository
```bash
git clone https://github.com/increscotech/swiss-knowledge-hub
cd swiss-knowledge-hub
```

2. Install dependencies
```bash
pnpm install
```

3. Configure environment variables
   - Create `.env` files in both `apps/api` and `apps/web` directories
   - See example environment files for required variables

4. Start the development servers
```bash
pnpm dev
```

## Running Components Individually

### API (Backend)
```bash
cd apps/api
pip install -r requirements.txt
python run.py
```

### Web (Frontend)
```bash
cd apps/web
pnpm dev
```

## Database Management

Swiss Knowledge Hub uses Prisma ORM with MongoDB for database management. Here are useful commands:

### Generate Prisma Client
```bash
pnpm run generate
```

### Push Schema Changes to Database
```bash
pnpm run db-push
```

### Format Prisma Schema
```bash
pnpm run format
```

### Environment Setup
Create a `.env` file in `apps/web` with:
```
DATABASE_URL="mongodb+srv://username:<EMAIL>/database?retryWrites=true&w=majority"
```

## License

See the [LICENSE](./LICENCE) file for more information.
