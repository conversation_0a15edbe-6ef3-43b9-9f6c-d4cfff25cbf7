{"name": "web", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "dev:turbo": "next dev --turbo", "prisma:generate": "prisma generate --schema=./lib/shared-db/schema.prisma", "prisma:format": "prisma format --schema=./lib/shared-db/schema.prisma", "prisma:studio": "prisma studio --schema=./lib/shared-db/schema.prisma", "prisma:db-push": "prisma db push --schema=./lib/shared-db/schema.prisma", "build": "NODE_OPTIONS='--max-old-space-size=8192' next build && cp -r .next/static .next/standalone/.next/ && cp -r public .next/standalone/", "build:turbo": "turbo build", "start": "next start", "lint": "eslint . --max-warnings 0", "ui:add": "pnpm dlx shadcn-ui@latest add", "generate-client": "openapi --input https://next-fast-turbo-api.vercel.app/openapi.json --output ./lib/api/client --client axios --useOptions --useUnionTypes", "generate-client:dev": "openapi --input http://*********:8000/openapi.json --output ./lib/api/client --client axios --useOptions --useUnionTypes", "seed-plans": "ts-node --compiler-options '{\"module\":\"CommonJS\"}' scripts/seed-plans.ts", "seed-storage-tiers": "ts-node --compiler-options '{\"module\":\"CommonJS\"}' scripts/seed-storage-tiers.ts", "seed-permissions": "ts-node --compiler-options '{\"module\":\"CommonJS\"}' scripts/init-permissions.ts", "minify-sdk": "node scripts/minify-sdk.js", "minify-sdk:advanced": "node scripts/advanced-minify.js", "build-sdk": "npm run minify-sdk:advanced", "build-sdk:production": "node scripts/build-production-sdk.js"}, "packageManager": "pnpm@8.9.0", "dependencies": {"@47ng/cloak": "^1.2.0", "@ai-sdk/react": "^1.1.23", "@azure/communication-email": "^1.0.0", "@azure/storage-blob": "^12.26.0", "@camped-layout/sub-base": "^1.0.3", "@datadog/browser-rum": "^6.6.3", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@hookform/resolvers": "^3.3.4", "@microsoft/microsoft-graph-client": "^3.0.7", "@next-auth/prisma-adapter": "^1.0.7", "@prisma/client": "^5.7.0", "@radix-ui/react-accordion": "^1.2.8", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-dialog": "^1.0.2", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.1.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-tooltip": "^1.0.7", "@stripe/react-stripe-js": "^3.6.0", "@stripe/stripe-js": "^7.0.0", "@tanstack/react-query": "4.32.1", "@types/jsonwebtoken": "^9.0.9", "axios": "^1.6.7", "bcrypt": "^5.1.1", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "cmdk": "^1.0.4", "date-fns": "^4.1.0", "form-data": "^4.0.0", "framer-motion": "^11.0.5", "googleapis": "^146.0.0", "hamburger-react": "^2.5.0", "js-cookie": "^3.0.5", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.220.0", "nanoid": "^5.1.5", "next": "^14.1.0", "next-auth": "^4.24.11", "next-themes": "^0.2.1", "nextra": "^2.13.3", "nextra-theme-docs": "^2.13.3", "pdfjs-dist": "^4.10.38", "prisma-field-encryption": "^1.6.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.50.1", "react-hot-toast": "^2.5.2", "react-markdown": "^8.0.7", "react-pdf": "^9.2.1", "react-use": "^17.5.0", "recharts": "^2.12.0", "remark-breaks": "^4.0.0", "remark-gfm": "^4.0.1", "stripe": "^18.0.0", "tailwind-merge": "^2.2.1", "tailwindcss-animate": "^1.0.7", "tailwindcss-text-fill": "^0.2.0", "uuid": "^11.1.0", "zod": "^3.22.4", "zustand": "^5.0.3"}, "prisma": {"schema": "lib/shared-db/schema.prisma"}, "devDependencies": {"@next/eslint-plugin-next": "^14.0.4", "@repo/eslint-config": "*", "@repo/typescript-config": "*", "@types/eslint": "^8.56.1", "@types/node": "^20.10.6", "@types/react": "^18.2.46", "@types/react-dom": "^18.2.18", "@types/uuid": "^10.0.0", "autoprefixer": "^10.4.17", "cross-env": "^7.0.3", "eslint": "^8.56.0", "eslint-config-turbo": "^1.11.3", "openapi-typescript-codegen": "^0.27.0", "postcss": "^8.4.35", "prisma": "^6.4.1", "tailwindcss": "^3.4.1", "ts-node": "^10.9.1", "typescript": "^5.3.3"}}