// https://lucide.dev/icons/
// Country icons: https://www.svgrepo.com/collection/countrys-flags/
// LinkedIn/Facebook/Twitter: https://icons8.com/icons/

import {
  Activity,
  ArrowRight,
  BadgePercent,
  BarChart,
  Bell,
  Building2,
  ChevronDown,
  ChevronsUpDown,
  ClipboardList,
  Copy,
  File,
  Gauge,
  Globe,
  Home,
  LayoutTemplate,
  Link,
  LucideProps,
  Menu,
  Moon,
  PanelLeftClose,
  PanelLeftOpen,
  Plus,
  PoundSterling,
  Settings,
  SlidersHorizontal,
  SunMedium,
  User2,
  Users,
  Workflow,
  X,
} from "lucide-react";

const iconComponents = {
  activity: Activity,
  analytics: BarChart,
  arrowRight: ArrowRight,
  badgePercent: BadgePercent,
  building: Building2,
  chevronDown: ChevronDown,
  chevronUpDown: ChevronsUpDown,
  collaboration: Users,
  copy: Copy,
  file: File,
  globe: Globe,
  home: Home,
  link: Link,
  menu: Menu,
  menuClose: X,
  moon: Moon,
  notification: Bell,
  panelLeftClose: PanelLeftClose,
  panelLeftOpen: PanelLeftOpen,
  performance: Gauge,
  plus: Plus,
  poundSterling: PoundSterling,
  rules: ClipboardList,
  settings: Settings,
  slider: SlidersHorizontal,
  sun: SunMedium,
  template: LayoutTemplate,
  user2: User2,
  workflow: Workflow,
  github: (props: LucideProps) => (
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 98 96" {...props}>
      <path d="M48.854 0C21.839 0 0 22 0 49.217c0 21.756 13.993 40.172 33.405 46.69 2.427.49 3.316-1.059 3.316-2.362 0-1.141-.08-5.052-.08-9.127-13.59 2.934-16.42-5.867-16.42-5.867-2.184-5.704-5.42-7.17-5.42-7.17-4.448-3.015.324-3.015.324-3.015 4.934.326 7.523 5.052 7.523 5.052 4.367 7.496 11.404 5.378 14.235 4.074.404-3.178 1.699-5.378 3.074-6.6-10.839-1.141-22.243-5.378-22.243-24.283 0-5.378 1.94-9.778 5.014-13.2-.485-1.222-2.184-6.275.486-13.038 0 0 4.125-1.304 13.426 5.052a46.97 46.97 0 0 1 12.214-1.63c4.125 0 8.33.571 12.213 1.63 9.302-6.356 13.427-5.052 13.427-5.052 2.67 6.763.97 11.816.485 13.038 3.155 3.422 5.015 7.822 5.015 13.2 0 18.905-11.404 23.06-22.324 24.283 1.78 1.548 3.316 4.481 3.316 9.126 0 6.6-.08 11.897-.08 13.526 0 1.304.89 2.853 3.316 2.364 19.412-6.52 33.405-24.935 33.405-46.691C97.707 22 75.788 0 48.854 0z" />
    </svg>
  ),
  linkedin: (props: LucideProps) => (
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 50 50" {...props}>
      <path d="M41,4H9C6.24,4,4,6.24,4,9v32c0,2.76,2.24,5,5,5h32c2.76,0,5-2.24,5-5V9C46,6.24,43.76,4,41,4z M17,20v19h-6V20H17z M11,14.47c0-1.4,1.2-2.47,3-2.47s2.93,1.07,3,2.47c0,1.4-1.12,2.53-3,2.53C12.2,17,11,15.87,11,14.47z M39,39h-6c0,0,0-9.26,0-10 c0-2-1-4-3.5-4.04h-0.08C27,24.96,26,27.02,26,29c0,0.91,0,10,0,10h-6V20h6v2.56c0,0,1.93-2.56,5.81-2.56 c3.97,0,7.19,2.73,7.19,8.26V39z" />
    </svg>
  ),
  doubleChevron: (props: LucideProps) => (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 24 24"
      {...props}
      fill="none"
      stroke="currentColor"
      stroke-width="2"
      stroke-linecap="round"
      stroke-linejoin="round"
      className="text-foreground lucide lucide-chevrons-up-down"
    >
      <path d="m7 15 5 5 5-5" />
      <path d="m7 9 5-5 5 5" />
    </svg>
  ),
  logo: (props: LucideProps) => (
    <svg
      id="Layer_1"
      data-name="Layer 1"
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 100 100"
      {...props}
    >
      <defs>
        <style>
          {`.cls-1{fill:hsl(var(--foreground));}.cls-2{fill:none}.cls-3{stroke:hsl(var(--background));}.cls-4{stroke-width:8px;}.cls-5{stroke-linecap:round;}.cls-6{stroke-linejoin:round;}`}
        </style>
      </defs>
      <circle className="cls-1" cx="50" cy="50" r="49.9" />
      <path
        className="cls-2 cls-3 cls-4 cls-5 cls-6"
        d="M61.77,69.61,81.38,50,61.77,30.39"
      />
      <path
        className="cls-2 cls-3 cls-4 cls-5 cls-6"
        d="M38.23,69.61,18.62,50,38.23,30.39"
      />
    </svg>
  ),
  OneDrive: (props: LucideProps) => (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      x="0px"
      y="0px"
      width="100"
      height="100"
      viewBox="0 0 48 48"
      {...props}
    >
      <linearGradient
        id="NqAl5bicO~9wmXV3Tvu1Ra_PnENrLMMW4eV_gr1"
        x1="24.5"
        x2="24.5"
        y1="7.032"
        y2="22.852"
        gradientUnits="userSpaceOnUse"
      >
        <stop offset="0" stop-color="#0571bf"></stop>
        <stop offset="1" stop-color="#0268ba"></stop>
      </linearGradient>
      <path
        fill="url(#NqAl5bicO~9wmXV3Tvu1Ra_PnENrLMMW4eV_gr1)"
        d="M24.5,7C16.492,7,10,13.492,10,21.5S16.492,36,24.5,36S39,29.508,39,21.5S32.508,7,24.5,7	L24.5,7z"
      ></path>
      <linearGradient
        id="NqAl5bicO~9wmXV3Tvu1Rb_PnENrLMMW4eV_gr2"
        x1="14.228"
        x2="14.228"
        y1="14.219"
        y2="32.761"
        gradientUnits="userSpaceOnUse"
      >
        <stop offset="0" stop-color="#1684da"></stop>
        <stop offset="1" stop-color="#107cd4"></stop>
      </linearGradient>
      <path
        fill="url(#NqAl5bicO~9wmXV3Tvu1Rb_PnENrLMMW4eV_gr2)"
        d="M16.155,14.972c-1.32-0.505-2.753-0.781-4.25-0.781C5.33,14.191,0,19.521,0,26.096	c0,2.476,0.757,4.774,2.05,6.678c0.061-0.026,16.445-6.889,26.406-10.888C22.952,18.568,17.903,15.641,16.155,14.972z"
      ></path>
      <linearGradient
        id="NqAl5bicO~9wmXV3Tvu1Rc_PnENrLMMW4eV_gr3"
        x1="38.228"
        x2="38.228"
        y1="18.746"
        y2="34.097"
        gradientUnits="userSpaceOnUse"
      >
        <stop offset="0" stop-color="#138cdd"></stop>
        <stop offset="1" stop-color="#0c7dd4"></stop>
      </linearGradient>
      <path
        fill="url(#NqAl5bicO~9wmXV3Tvu1Rc_PnENrLMMW4eV_gr3)"
        d="M48,28.373c0-5.317-4.31-9.627-9.627-9.627c-0.997,0-1.958,0.152-2.863,0.433	c-0.996,0.31-3.652,1.342-7.054,2.708c8.377,5.05,17.79,10.996,18.252,11.288C47.525,31.76,48,30.123,48,28.373z"
      ></path>
      <linearGradient
        id="NqAl5bicO~9wmXV3Tvu1Rd_PnENrLMMW4eV_gr4"
        x1="17.13"
        x2="31.145"
        y1="24.083"
        y2="41.333"
        gradientUnits="userSpaceOnUse"
      >
        <stop offset="0" stop-color="#27a7ea"></stop>
        <stop offset="1" stop-color="#1c94e3"></stop>
      </linearGradient>
      <path
        fill="url(#NqAl5bicO~9wmXV3Tvu1Rd_PnENrLMMW4eV_gr4)"
        d="M46.709,33.175c-0.463-0.292-9.875-6.238-18.252-11.288	C18.495,25.885,2.111,32.748,2.05,32.774C2.467,33.388,5.627,38,11.904,38c5.03,0,16.176,0,26.354,0	C43.669,38,46.148,34.146,46.709,33.175z"
      ></path>
    </svg>
  ),
  GDrive: (props: LucideProps) => (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      x="0px"
      y="0px"
      width="100"
      height="100"
      viewBox="0 0 48 48"
      {...props}
    >
      <path
        fill="#1e88e5"
        d="M38.59,39c-0.535,0.93-0.298,1.68-1.195,2.197C36.498,41.715,35.465,42,34.39,42H13.61 c-1.074,0-2.106-0.285-3.004-0.802C9.708,40.681,9.945,39.93,9.41,39l7.67-9h13.84L38.59,39z"
      ></path>
      <path
        fill="#fbc02d"
        d="M27.463,6.999c1.073-0.002,2.104-0.716,3.001-0.198c0.897,0.519,1.66,1.27,2.197,2.201l10.39,17.996 c0.537,0.93,0.807,1.967,0.808,3.002c0.001,1.037-1.267,2.073-1.806,3.001l-11.127-3.005l-6.924-11.993L27.463,6.999z"
      ></path>
      <path
        fill="#e53935"
        d="M43.86,30c0,1.04-0.27,2.07-0.81,3l-3.67,6.35c-0.53,0.78-1.21,1.4-1.99,1.85L30.92,30H43.86z"
      ></path>
      <path
        fill="#4caf50"
        d="M5.947,33.001c-0.538-0.928-1.806-1.964-1.806-3c0.001-1.036,0.27-2.073,0.808-3.004l10.39-17.996 c0.537-0.93,1.3-1.682,2.196-2.2c0.897-0.519,1.929,0.195,3.002,0.197l3.459,11.009l-6.922,11.989L5.947,33.001z"
      ></path>
      <path
        fill="#1565c0"
        d="M17.08,30l-6.47,11.2c-0.78-0.45-1.46-1.07-1.99-1.85L4.95,33c-0.54-0.93-0.81-1.96-0.81-3H17.08z"
      ></path>
      <path
        fill="#2e7d32"
        d="M30.46,6.8L24,18L17.53,6.8c0.78-0.45,1.66-0.73,2.6-0.79L27.46,6C28.54,6,29.57,6.28,30.46,6.8z"
      ></path>
    </svg>
  ),
};

export const Icon = (props) => {
  const IconComponent = iconComponents[props.name];

  if (IconComponent) {
    return (
      <div className="flex justify-center">
        <IconComponent {...props} />
      </div>
    );
  }
  return null;
};
