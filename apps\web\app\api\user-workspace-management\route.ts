import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import db from "@/lib/shared-db";
import { authOptions } from "@/lib/next-auth/src/auth-options";
import { withPermission } from "@/lib/permission-middleware";

// Get all user-workspace assignments with optional filtering
export const GET = withPermission(
  async (request: Request) => {
    try {
      const url = new URL(request.url);
      const tenantId = request.headers.get("x-tenant-id");
      const userId = url.searchParams.get("userId");
      const workspaceId = url.searchParams.get("workspaceId");
      const role = url.searchParams.get("role");
      const groupId = url.searchParams.get("groupId");

      if (!tenantId) {
        return NextResponse.json(
          { error: "Tenant ID is required" },
          { status: 400 }
        );
      }

      // Build the query based on filters
      const query: any = {
        where: {
          role: {
            not: {
              in: ["OWNER", "ADMIN"],
            },
          },
          workspace: {
            tenantId,
          },
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              image: true,
            },
          },
          workspace: true,
          customRole: true,
          membership: {
            include: {
              customRole: true,
            },
          },
        },
      };

      // Add filters if they exist
      if (userId) {
        query.where.userId = userId;
      }

      if (workspaceId) {
        query.where.workspaceId = workspaceId;
      }

      if (role) {
        if (role?.includes("CUSTOM")) {
          const customRoleId = role?.split(":")[1];
          query.where.customRoleId = customRoleId;
        } else {
          query.where.role = role;
        }
      }

      // If groupId is provided, filter by users in that group
      if (groupId) {
        // Get all users in the group
        const groupMembers = await db.groupMember.findMany({
          where: {
            groupId,
          },
          select: {
            userId: true,
          },
        });

        const userIds = groupMembers.map(
          (member: { userId: string }) => member.userId
        );

        // Add to existing query
        if (query.where.userId) {
          // If userId is already specified, make sure it's in the group
          if (!userIds.includes(query.where.userId)) {
            // Return empty result if the specified user is not in the group
            return NextResponse.json({
              data: [],
              message: "No matching user-workspace assignments found",
            });
          }
        } else {
          // Filter by all users in the group
          query.where.userId = {
            in: userIds,
          };
        }
      }
      console.log({ query: JSON.stringify(query, null, 2) });

      // Get all workspace members based on the query
      const workspaceMembers = await db.workspaceMember.findMany(query);

      // Fetch group information for each user
      const workspaceMembersWithGroups = await Promise.all(
        workspaceMembers.map(async (member: any) => {
          // Get all groups the user belongs to
          const groupMembers = await db.groupMember.findMany({
            where: {
              userId: member.userId,
            },
            include: {
              group: true,
            },
          });

          // Extract the groups from the group members
          const userGroups = groupMembers.map((gm: any) => gm.group);

          return {
            ...member,
            userGroups,
          };
        })
      );

      return NextResponse.json({
        data: workspaceMembersWithGroups,
        message: "User-workspace assignments retrieved successfully",
      });
    } catch (error) {
      console.error("Error fetching user-workspace assignments:", error);
      return NextResponse.json(
        { error: "Failed to fetch user-workspace assignments" },
        { status: 500 }
      );
    }
  },
  "READ",
  "MEMBER"
);

// Add a user to a workspace with a specific role
export const POST = withPermission(
  async (request: Request) => {
    try {
      const session: any = await getServerSession(authOptions);

      if (!session?.user?.email) {
        return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
      }

      const { userId, workspaceSlug, role, customRoleId } =
        await request.json();
      const tenantId = request.headers.get("x-tenant-id");

      if (!userId || !workspaceSlug || !role || !tenantId) {
        return NextResponse.json(
          {
            error: "User ID, workspace slug, role, and tenant ID are required",
          },
          { status: 400 }
        );
      }

      // Get the workspace and verify it exists
      const workspace = await db.workspace.findFirst({
        where: { slug: workspaceSlug, tenantId },
      });

      if (!workspace) {
        return NextResponse.json(
          { error: "Workspace not found" },
          { status: 404 }
        );
      }

      // Verify the user exists and is a member of the tenant
      const user = await db.user.findUnique({
        where: { id: userId },
        include: {
          membership: {
            where: { tenantId },
          },
        },
      });

      if (!user || user.membership.length === 0) {
        return NextResponse.json(
          { error: "User not found or not a member of this tenant" },
          { status: 404 }
        );
      }

      // Check if user is already a member of the workspace
      const existingMember = await db.workspaceMember.findFirst({
        where: {
          userId,
          workspaceId: workspace.id,
        },
      });

      if (existingMember) {
        return NextResponse.json(
          { error: "User is already a member of this workspace" },
          { status: 400 }
        );
      }

      // Create workspace member
      const workspaceMember = await db.workspaceMember.create({
        data: {
          userId,
          workspaceId: workspace.id,
          membershipId: user.membership[0].id,
          role,
          customRoleId: customRoleId || undefined,
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              image: true,
            },
          },
          workspace: true,
          customRole: true,
        },
      });

      return NextResponse.json({
        message: "User added to workspace successfully",
        data: workspaceMember,
      });
    } catch (error) {
      console.error("Error adding user to workspace:", error);
      return NextResponse.json(
        { error: "Failed to add user to workspace" },
        { status: 500 }
      );
    }
  },
  "CREATE",
  "MEMBER"
);

// Remove a user from a workspace
export const DELETE = withPermission(
  async (request: Request) => {
    try {
      const session: any = await getServerSession(authOptions);

      if (!session?.user?.email) {
        return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
      }

      const { userId, workspaceSlug } = await request.json();
      const tenantId = request.headers.get("x-tenant-id");

      if (!userId || !workspaceSlug || !tenantId) {
        return NextResponse.json(
          { error: "User ID, workspace slug, and tenant ID are required" },
          { status: 400 }
        );
      }

      // Get the workspace and verify it exists
      const workspace = await db.workspace.findFirst({
        where: { slug: workspaceSlug, tenantId },
        include: {
          workspaceMember: true,
        },
      });

      if (!workspace) {
        return NextResponse.json(
          { error: "Workspace not found" },
          { status: 404 }
        );
      }

      // Find the member to be removed
      const memberToRemove = workspace.workspaceMember.find(
        (member: any) => member.userId === userId
      );

      if (!memberToRemove) {
        return NextResponse.json(
          { error: "User is not a member of this workspace" },
          { status: 404 }
        );
      }

      // Prevent removing the last owner
      if (memberToRemove.role === "OWNER") {
        const ownerCount = workspace.workspaceMember.filter(
          (member: any) => member.role === "OWNER"
        ).length;

        if (ownerCount <= 1) {
          return NextResponse.json(
            { error: "Cannot remove the last owner of the workspace" },
            { status: 400 }
          );
        }
      }

      // Delete the workspace member
      await db.workspaceMember.delete({
        where: {
          id: memberToRemove.id,
        },
      });

      return NextResponse.json({
        message: "User removed from workspace successfully",
      });
    } catch (error) {
      console.error("Error removing user from workspace:", error);
      return NextResponse.json(
        { error: "Failed to remove user from workspace" },
        { status: 500 }
      );
    }
  },
  "DELETE",
  "MEMBER"
);
