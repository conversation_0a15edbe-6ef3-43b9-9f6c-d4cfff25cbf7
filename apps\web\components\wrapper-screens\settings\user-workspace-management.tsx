"use client";

import { useState, useEffect } from "react";
import { UserWorkspaceFilters } from "./user-workspace-filters";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { useLanguage } from "@/lib/language-context";
import {
  getUserWorkspaceAssignments,
  addUserToWorkspace,
  removeUserFromWorkspace,
} from "@/services";
import toast from "react-hot-toast";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  UserPlus,
  UserMinus,
  Loader2,
  Info,
  HelpCircle,
  Shield,
  Building,
  Users,
} from "lucide-react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Badge } from "@/components/ui/badge";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { useRouter } from "next/navigation";

interface User {
  id: string;
  name: string;
  email: string;
  image?: string;
}

interface Workspace {
  id: string;
  name: string;
  slug: string;
}

interface Role {
  id: string;
  name: string;
}

interface Group {
  id: string;
  name: string;
}

interface Membership {
  id: string;
  role: string;
  customRoleId?: string;
  customRole?: Role;
}

interface WorkspaceMember {
  id: string;
  role: string;
  customRoleId?: string;
  customRole?: Role;
  user: User;
  workspace: Workspace;
  membership?: Membership;
  userGroups?: Group[]; // Added for group information
}

interface UserWorkspaceManagementProps {
  initialUsers: User[];
  initialWorkspaces: Workspace[];
  initialRoles: Role[];
  initialGroups: Group[];
  tenantId: string;
  userId: string;
}

export function UserWorkspaceManagement({
  initialUsers,
  initialWorkspaces,
  initialRoles,
  initialGroups,
  tenantId,
  userId,
}: UserWorkspaceManagementProps) {
  const { t } = useLanguage();
  const router = useRouter();
  const users = initialUsers;
  const workspaces = initialWorkspaces;
  const roles = initialRoles;
  const groups = initialGroups;
  const [workspaceMembers, setWorkspaceMembers] = useState<WorkspaceMember[]>(
    []
  );
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [filters, setFilters] = useState<any>({});

  // Add user dialog state
  const [addDialogOpen, setAddDialogOpen] = useState<boolean>(false);
  const [selectedUser, setSelectedUser] = useState<string>("");
  const [selectedWorkspace, setSelectedWorkspace] = useState<string>("");
  const [selectedRole, setSelectedRole] = useState<string>("MEMBER");
  const [selectedCustomRole, setSelectedCustomRole] = useState<string>("");
  const [isAddingUser, setIsAddingUser] = useState<boolean>(false);

  // Remove user dialog state
  const [removeDialogOpen, setRemoveDialogOpen] = useState<boolean>(false);
  const [memberToRemove, setMemberToRemove] = useState<WorkspaceMember | null>(
    null
  );
  const [isRemovingUser, setIsRemovingUser] = useState<boolean>(false);

  // Fetch workspace members on initial load and when filters change
  useEffect(() => {
    fetchWorkspaceMembers();
  }, [filters]);

  const fetchWorkspaceMembers = async () => {
    setIsLoading(true);
    try {
      const response = await getUserWorkspaceAssignments(
        tenantId,
        userId,
        filters
      );
      if (response?.data) {
        setWorkspaceMembers(response.data);
      }
    } catch (error) {
      console.error("Error fetching workspace members:", error);
      toast.error(t("common.errorFetchingData"));
    } finally {
      setIsLoading(false);
    }
  };

  const handleFilterChange = (newFilters: any) => {
    setFilters(newFilters);
  };

  const handleAddUser = async () => {
    if (!selectedUser || !selectedWorkspace || !selectedRole) {
      toast.error(t("common.allFieldsRequired"));
      return;
    }

    setIsAddingUser(true);

    try {
      const workspace = workspaces.find((w) => w.id === selectedWorkspace);
      if (!workspace) {
        toast.error(t("workspace.workspaceNotFound"));
        return;
      }

      const data: any = {
        userId: selectedUser,
        workspaceSlug: workspace.slug,
        role: selectedRole,
      };

      // If custom role is selected, add the custom role ID
      if (selectedRole === "CUSTOM" && selectedCustomRole) {
        data.customRoleId = selectedCustomRole;
      }

      const response = await addUserToWorkspace(data, tenantId, userId);

      if (response.error) {
        toast.error(response.error);
      } else {
        toast.success(t("workspace.userAddedToWorkspace"));
        setAddDialogOpen(false);
        resetAddForm();
        fetchWorkspaceMembers();
        router.refresh();
      }
    } catch (error) {
      console.error("Error adding user to workspace:", error);
      toast.error(t("workspace.errorAddingUser"));
    } finally {
      setIsAddingUser(false);
    }
  };

  const handleRemoveUser = async () => {
    if (!memberToRemove) return;

    setIsRemovingUser(true);

    try {
      const response = await removeUserFromWorkspace(
        {
          userId: memberToRemove.user.id,
          workspaceSlug: memberToRemove.workspace.slug,
        },
        tenantId,
        userId
      );

      if (response.error) {
        toast.error(response.error);
      } else {
        toast.success(t("workspace.userRemovedFromWorkspace"));
        setRemoveDialogOpen(false);
        setMemberToRemove(null);
        fetchWorkspaceMembers();
        router.refresh();
      }
    } catch (error) {
      console.error("Error removing user from workspace:", error);
      toast.error(t("workspace.errorRemovingUser"));
    } finally {
      setIsRemovingUser(false);
    }
  };

  const resetAddForm = () => {
    setSelectedUser("");
    setSelectedWorkspace("");
    setSelectedRole("MEMBER");
    setSelectedCustomRole(""); // This is fine as it's only used when a custom role is selected
  };

  const getRoleVariant = (role: string) => {
    switch (role) {
      case "OWNER":
        return "destructive";
      case "ADMIN":
        return "default";
      case "MEMBER":
        return "secondary";
      case "CUSTOM":
        return "outline";
      default:
        return "secondary";
    }
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case "OWNER":
        return <Shield className="h-3 w-3" />;
      case "ADMIN":
        return <Building className="h-3 w-3" />;
      case "MEMBER":
        return <Users className="h-3 w-3" />;
      default:
        return <Shield className="h-3 w-3" />;
    }
  };

  const getWorkspaceRoleName = (member: WorkspaceMember) => {
    if (member.role === "CUSTOM" && member.customRole) {
      return member.customRole.name;
    }
    return member.role.charAt(0) + member.role.slice(1).toLowerCase();
  };

  const getOrganizationRoleName = (member: WorkspaceMember) => {
    if (!member.membership) {
      return "—";
    }
    if (member.membership.role === "CUSTOM" && member.membership.customRole) {
      return member.membership.customRole.name;
    }
    return (
      member.membership.role.charAt(0) +
      member.membership.role.slice(1).toLowerCase()
    );
  };

  const renderRoleBadge = (
    role: string,
    roleName: string,
    isCustom: boolean = false
  ) => {
    return (
      <Badge variant={getRoleVariant(role)} className="flex items-center gap-1">
        {!isCustom && getRoleIcon(role)}
        {roleName}
      </Badge>
    );
  };

  return (
    <TooltipProvider>
      <div className="space-y-6">
        {/* Header Section with Help */}
        <Card>
          <CardHeader>
            <div className="flex flex-col gap-3 xl:flex-row xl:items-center xl:justify-between">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-primary/10 rounded-lg">
                  <Users className="h-5 w-5 text-primary" />
                </div>
                <div>
                  <CardTitle className="text-xl">
                    {t("workspace.userWorkspaceManagement")}
                  </CardTitle>
                  <CardDescription className="mt-1">
                    {t("workspace.userWorkspaceManagementDescription")}
                  </CardDescription>
                </div>
              </div>
              <Dialog open={addDialogOpen} onOpenChange={setAddDialogOpen}>
                <DialogTrigger asChild>
                  <Button className="flex items-center gap-2">
                    <UserPlus className="h-4 w-4" />
                    {t("workspace.addUserToWorkspace")}
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>
                      {t("workspace.addUserToWorkspace")}
                    </DialogTitle>
                    <DialogDescription>
                      {t("workspace.addUserToWorkspaceDescription")}
                    </DialogDescription>
                  </DialogHeader>
                  <div className="space-y-4 py-4">
                    <div className="space-y-2">
                      <Label htmlFor="user">{t("common.user")}</Label>
                      <Select
                        value={selectedUser}
                        onValueChange={setSelectedUser}
                      >
                        <SelectTrigger id="user">
                          <SelectValue placeholder={t("common.selectUser")} />
                        </SelectTrigger>
                        <SelectContent>
                          {users.map((user) => (
                            <SelectItem key={user.id} value={user.id}>
                              {user.name || user.email}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="workspace">
                        {t("workspace.workspace")}
                      </Label>
                      <Select
                        value={selectedWorkspace}
                        onValueChange={setSelectedWorkspace}
                      >
                        <SelectTrigger id="workspace">
                          <SelectValue
                            placeholder={t("workspace.selectWorkspace")}
                          />
                        </SelectTrigger>
                        <SelectContent>
                          {workspaces.map((workspace) => (
                            <SelectItem key={workspace.id} value={workspace.id}>
                              {workspace.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="role">{t("roles.role")}</Label>
                      <Select
                        value={selectedRole}
                        onValueChange={setSelectedRole}
                      >
                        <SelectTrigger id="role">
                          <SelectValue placeholder={t("roles.selectRole")} />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="OWNER">Owner</SelectItem>
                          <SelectItem value="ADMIN">Admin</SelectItem>
                          <SelectItem value="MEMBER">Member</SelectItem>
                          <SelectItem value="CUSTOM">Custom</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    {selectedRole === "CUSTOM" && (
                      <div className="space-y-2">
                        <Label htmlFor="customRole">
                          {t("roles.customRole")}
                        </Label>
                        <Select
                          value={selectedCustomRole}
                          onValueChange={setSelectedCustomRole}
                        >
                          <SelectTrigger id="customRole">
                            <SelectValue
                              placeholder={t("roles.selectCustomRole")}
                            />
                          </SelectTrigger>
                          <SelectContent>
                            {roles.map((role) => (
                              <SelectItem key={role.id} value={role.id}>
                                {role.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    )}
                  </div>
                  <DialogFooter>
                    <Button onClick={handleAddUser} disabled={isAddingUser}>
                      {isAddingUser ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          {t("common.adding")}
                        </>
                      ) : (
                        t("common.add")
                      )}
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            </div>
          </CardHeader>
        </Card>

        {/* Filters Section */}
        <UserWorkspaceFilters
          users={users}
          workspaces={workspaces}
          roles={roles}
          groups={groups}
          onFilterChange={handleFilterChange}
        />

        {/* Main Table Section */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg flex items-center gap-2">
              <Users className="h-5 w-5" />
              {t("workspace.userAssignments")}
            </CardTitle>
            <CardDescription>
              {t("workspace.userAssignmentsDescription")}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="border rounded-lg overflow-hidden">
              <Table>
                <TableHeader>
                  <TableRow className="bg-muted/50">
                    <TableHead>{t("common.user")}</TableHead>
                    <TableHead>{t("workspace.workspace")}</TableHead>
                    <TableHead>
                      <div className="flex items-center gap-2">
                        <Building className="h-4 w-4" />
                        {t("roles.organizationRole")}
                        <Tooltip>
                          <TooltipTrigger>
                            <HelpCircle className="h-3 w-3 text-muted-foreground" />
                          </TooltipTrigger>
                          <TooltipContent>
                            <p className="max-w-xs">
                              {t("roles.organizationRoleTooltip")}
                            </p>
                          </TooltipContent>
                        </Tooltip>
                      </div>
                    </TableHead>
                    <TableHead>
                      <div className="flex items-center gap-2">
                        <Shield className="h-4 w-4" />
                        {t("roles.workspaceRole")}
                        <Tooltip>
                          <TooltipTrigger>
                            <HelpCircle className="h-3 w-3 text-muted-foreground" />
                          </TooltipTrigger>
                          <TooltipContent>
                            <p className="max-w-xs">
                              {t("roles.workspaceRoleTooltip")}
                            </p>
                          </TooltipContent>
                        </Tooltip>
                      </div>
                    </TableHead>
                    <TableHead>{t("groups.group")}</TableHead>
                    <TableHead className="text-right">
                      {t("common.actions")}
                    </TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {isLoading ? (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-8">
                        <div className="flex justify-center">
                          <Loader2 className="h-8 w-8 animate-spin text-primary" />
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : workspaceMembers.length === 0 ? (
                    <TableRow>
                      <TableCell
                        colSpan={6}
                        className="text-center py-8 text-muted-foreground"
                      >
                        {t("workspace.noWorkspaceMembersFound")}
                      </TableCell>
                    </TableRow>
                  ) : (
                    workspaceMembers.map((member) => (
                      <TableRow key={member.id}>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Avatar className="h-8 w-8">
                              <AvatarImage
                                src={member.user.image || ""}
                                alt={member.user.name || ""}
                              />
                              <AvatarFallback>
                                {(member.user.name || member.user.email || "")
                                  .charAt(0)
                                  .toUpperCase()}
                              </AvatarFallback>
                            </Avatar>
                            <div>
                              <div className="font-medium">
                                {member.user.name || "—"}
                              </div>
                              <div className="text-sm text-muted-foreground">
                                {member.user.email}
                              </div>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Building className="h-4 w-4 text-muted-foreground" />
                            <span className="font-medium">
                              {member.workspace.name}
                            </span>
                          </div>
                        </TableCell>
                        <TableCell>
                          {member.membership ? (
                            renderRoleBadge(
                              member.membership.role,
                              getOrganizationRoleName(member),
                              member.membership.role === "CUSTOM"
                            )
                          ) : (
                            <Badge variant="secondary">—</Badge>
                          )}
                        </TableCell>
                        <TableCell>
                          {renderRoleBadge(
                            member.role,
                            getWorkspaceRoleName(member),
                            member.role === "CUSTOM"
                          )}
                        </TableCell>
                        <TableCell>
                          {member.userGroups && member.userGroups.length > 0
                            ? member.userGroups
                                .map((group) => group.name)
                                .join(", ")
                            : t("groups.noGroup")}
                        </TableCell>
                        <TableCell className="text-right">
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => {
                              setMemberToRemove(member);
                              setRemoveDialogOpen(true);
                            }}
                          >
                            <UserMinus className="h-4 w-4" />
                            <span className="sr-only">
                              {t("workspace.removeUser")}
                            </span>
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>

        <AlertDialog open={removeDialogOpen} onOpenChange={setRemoveDialogOpen}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>
                {t("workspace.removeUserFromWorkspace")}
              </AlertDialogTitle>
              <AlertDialogDescription>
                {t("workspace.removeUserFromWorkspaceDescription")}
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>{t("common.cancel")}</AlertDialogCancel>
              <AlertDialogAction
                onClick={handleRemoveUser}
                disabled={isRemovingUser}
                className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
              >
                {isRemovingUser ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    {t("common.removing")}
                  </>
                ) : (
                  t("common.remove")
                )}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>
    </TooltipProvider>
  );
}
