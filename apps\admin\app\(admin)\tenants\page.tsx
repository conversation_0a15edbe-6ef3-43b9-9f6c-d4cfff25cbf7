import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/next-auth";
import { redirect } from "next/navigation";
import db from "@/lib/shared-db";
import { EnhancedTenantsClient } from "@/components/tenants/enhanced-tenants-client";
import { Suspense } from "react";

// Enhanced types for comprehensive tenant management
interface Tenant {
  id: string;
  name: string;
  slug: string;
  createdAt: Date;
  plan: string;
  status: "active" | "inactive" | "suspended";
  users: number;
  storage: string;
  hasActiveSubscription: boolean;
  lastActivity: Date | null;
  tokenUsagePercent: number;
  infraCost: number;
  margin: number;
  healthScore: number;
  planType: "STARTER" | "TEAM" | "PRO" | "ENTERPRISE" | "SOLO";
  // ✅ isActiveTenant removed - using unified status field
}

interface TenantStats {
  totalTenants: number;
  activeTenants: number;
  trialTenants: number;
  inactiveTenants: number;
  suspendedTenants: number;
  healthyTenants: number;
  atRiskTenants: number;
}

interface Plan {
  id: string;
  name: string;
  type: string;
  description?: string; // ✅ Changed from string | null to optional string
  price?: number; // ✅ Made optional to match client component
  includedUsers: number;
  vectorStoreGB: number;
  webSearchLimit: number;
  additionalUserFee?: number; // ✅ Made optional to match client component
}

interface TenantsPageData {
  tenants: Tenant[];
  stats: TenantStats;
  availablePlans: Plan[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  searchQuery: string;
  initialFilter?: string;
}

// Helper functions for enhanced tenant calculations
const USD_TO_CHF_RATE = 0.91; // Swiss Franc exchange rate

const calculateTenantStorage = async (tenantId: string) => {
  const workspaces = await db.workspace.findMany({
    where: { tenantId },
    select: { id: true },
  });

  const workspaceIds = workspaces.map((w: any) => w.id);

  if (workspaceIds.length === 0) return 0;

  const files = await db.file.findMany({
    where: {
      workspaceId: { in: workspaceIds },
    },
    select: { size: true },
  });

  let totalKB = 0;

  for (const file of files) {
    if (!file.size || typeof file.size !== "string") continue;

    const match = file.size.match(/^([\d.]+)\s*(KB|MB|GB)?$/i);
    if (!match) continue;

    const value = parseFloat(match[1]);
    const unit = match[2]?.toUpperCase() || "KB";

    if (unit === "KB") totalKB += value;
    else if (unit === "MB") totalKB += value * 1024;
    else if (unit === "GB") totalKB += value * 1024 * 1024;
  }

  return totalKB;
};

// Calculate health score based on multiple factors
function calculateHealthScore(tenant: any): number {
  let score = 100;

  // Days since last activity (30% weight)
  if (tenant.lastActivity) {
    const daysSinceActivity = Math.floor((Date.now() - new Date(tenant.lastActivity).getTime()) / (1000 * 60 * 60 * 24));
    if (daysSinceActivity > 30) score -= 30;
    else if (daysSinceActivity > 14) score -= 15;
    else if (daysSinceActivity > 7) score -= 5;
  } else {
    score -= 30; // No activity recorded
  }

  // Token usage percentage (25% weight)
  const tokenUsage = tenant.tokenUsagePercent || 0;
  if (tokenUsage < 10) score -= 25; // Very low usage
  else if (tokenUsage < 30) score -= 10; // Low usage

  // Feature adoption (20% weight)
  if (!tenant.hasActiveSubscription) score -= 20;

  // Support tickets and churn risk (25% weight) - simplified for now
  // This would be calculated based on actual support ticket data

  return Math.max(0, Math.min(100, score));
}

// Calculate infrastructure costs in CHF including storage costs
async function calculateInfraCost(tenantId: string): Promise<number> {
  const now = new Date();
  const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);

  // Calculate token costs
  const tokenUsage = await db.tokenUsage.findMany({
    where: {
      tenantId,
      timestamp: {
        gte: startOfMonth,
        lte: now
      }
    },
    select: {
      cost: true
    }
  });

  const tokenCostUSD = tokenUsage.reduce((total, usage) => total + (Number(usage.cost) || 0), 0);

  // Calculate storage costs using VectorStoreUsage and StorageTier data
  const vectorStoreUsage = await db.vectorStoreUsage.findMany({
    where: {
      tenantId: tenantId,
      timestamp: {
        gte: startOfMonth,
        lte: now
      }
    },
    select: {
      usageGB: true,
      timestamp: true
    },
    orderBy: {
      timestamp: 'desc'
    }
  });

  // Get the latest storage usage for the month
  const latestStorageUsage = vectorStoreUsage.length > 0 ? vectorStoreUsage[0].usageGB : 0;

  // Get storage tier pricing (assuming CHF pricing)
  const storageTiers = await db.storageTier.findMany({
    where: {
      isActive: true
    },
    select: {
      sizeGB: true,
      price: true
    },
    orderBy: {
      sizeGB: 'asc'
    }
  });

  // Calculate storage cost based on usage and tier pricing
  let storageCostCHF = 0;
  if (latestStorageUsage > 0 && storageTiers.length > 0) {
    // Find the appropriate tier for the usage
    const appropriateTier = storageTiers.find(tier => latestStorageUsage <= tier.sizeGB) || storageTiers[storageTiers.length - 1];

    if (appropriateTier) {
      // Calculate proportional cost based on usage
      const usageRatio = latestStorageUsage / appropriateTier.sizeGB;
      storageCostCHF = appropriateTier.price * usageRatio;
    }
  }

  // Convert token costs to CHF and add storage costs
  const tokenCostCHF = tokenCostUSD * USD_TO_CHF_RATE;

  return tokenCostCHF + storageCostCHF;
}

// Calculate token usage percentage
async function calculateTokenUsagePercent(tenantId: string, planType: string): Promise<number> {
  const now = new Date();
  const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);

  const tokenUsage = await db.tokenUsage.findMany({
    where: {
      tenantId,
      timestamp: {
        gte: startOfMonth,
        lte: now
      }
    },
    select: {
      inputTokens: true,
      outputTokens: true
    }
  });

  const totalTokens = tokenUsage.reduce((total, usage) =>
    total + (usage.inputTokens || 0) + (usage.outputTokens || 0), 0);

  // Define monthly token limits by plan type (simplified)
  const tokenLimits: Record<string, number> = {
    STARTER: 100000,
    TEAM: 500000,
    PRO: 2000000,
    ENTERPRISE: 10000000,
    SOLO: 50000
  };

  const limit = tokenLimits[planType] || 100000;
  return Math.min(100, (totalTokens / limit) * 100);
}

// Get last activity date
async function getLastActivity(tenantId: string): Promise<Date | null> {
  // Check API requests
  const lastApiRequest = await db.aPIRequest.findFirst({
    where: { tenantId },
    orderBy: { timestamp: 'desc' },
    select: { timestamp: true }
  });

  // Check token usage
  const lastTokenUsage = await db.tokenUsage.findFirst({
    where: { tenantId },
    orderBy: { timestamp: 'desc' },
    select: { timestamp: true }
  });

  const dates = [
    lastApiRequest?.timestamp,
    lastTokenUsage?.timestamp
  ].filter(Boolean) as Date[];

  return dates.length > 0 ? new Date(Math.max(...dates.map(d => d.getTime()))) : null;
}

// Enhanced server-side data fetching functions
async function fetchTenants(
  searchQuery: string = ""
): Promise<{ tenants: Tenant[]; total: number }> {
  // Build where clause for search
  let whereClause: any = {};

  if (searchQuery) {
    whereClause.OR = [
      { name: { contains: searchQuery, mode: "insensitive" as const } },
      { slug: { contains: searchQuery, mode: "insensitive" as const } },
    ];
  }

  // Get ALL tenants with comprehensive data (no server-side pagination)
  const tenants = await db.tenant.findMany({
    where: whereClause,
    select: {
      id: true,
      name: true,
      slug: true,
      createdAt: true,
      status: true, // ✅ Use unified status field from database
      Subscription: {
        where: {
          isActive: true,
        },
        include: {
          plan: {
            select: {
              name: true,
              type: true,
              price: true,
            },
          },
        },
      },
      Membership: {
        select: {
          id: true,
        },
      },
    },
    orderBy: {
      createdAt: "desc",
    },
  });

  // Format the tenants with enhanced calculated fields
  const formattedTenants: Tenant[] = await Promise.all(
    tenants.map(async (tenant) => {
      const hasActiveSubscription = tenant.Subscription.length > 0;
      const userCount = tenant.Membership.length;
      const planData = tenant.Subscription[0]?.plan;
      const planType = planData?.type || "STARTER";

      // ✅ Use database status field directly instead of hardcoded calculation
      const status = tenant.status || "active";

      // Calculate storage
      const totalKB = await calculateTenantStorage(tenant.id);
      const storageFormatted =
        totalKB >= 1024 * 1024
          ? `${(totalKB / (1024 * 1024)).toFixed(2)} GB`
          : totalKB >= 1024
            ? `${(totalKB / 1024).toFixed(2)} MB`
            : `${totalKB.toFixed(2)} KB`;

      // Calculate enhanced metrics
      const lastActivity = await getLastActivity(tenant.id);
      const tokenUsagePercent = await calculateTokenUsagePercent(tenant.id, planType);
      const infraCost = await calculateInfraCost(tenant.id);

      // Calculate margin (Revenue - Cost)
      const monthlyRevenueUSD = Number(planData?.price) || 0;
      const monthlyRevenueCHF = monthlyRevenueUSD * USD_TO_CHF_RATE;
      const margin = monthlyRevenueCHF - infraCost;

      // Calculate health score
      const healthScore = calculateHealthScore({
        lastActivity,
        tokenUsagePercent,
        hasActiveSubscription,
      });

      return {
        id: tenant.id,
        name: tenant.name,
        slug: tenant.slug,
        createdAt: tenant.createdAt,
        plan: planData?.name || "No Plan",
        planType,
        status,
        users: userCount,
        storage: storageFormatted,
        hasActiveSubscription,
        lastActivity,
        tokenUsagePercent,
        infraCost,
        margin,
        healthScore,
        // ✅ isActiveTenant field removed - now using unified status field
      };
    })
  );
  // Return all tenants - filtering will be done on the frontend
  return { tenants: formattedTenants, total: formattedTenants.length };
}

async function fetchAvailablePlans(): Promise<Plan[]> {
  // Get all active plans for the Change Plan functionality
  const plans = await db.plan.findMany({
    where: {
      isActive: true
    },
    select: {
      id: true,
      name: true,
      type: true,
      description: true,
      price: true,
      includedUsers: true,
      vectorStoreGB: true,
      webSearchLimit: true,
      additionalUserFee: true
    },
    orderBy: [
      { type: "asc" },
      { price: "asc" }
    ]
  });

  // ✅ Transform to match the expected Plan interface with proper type conversion
  return plans.map((plan: any) => ({
    id: plan.id,
    name: plan.name,
    type: plan.type,
    description: plan.description || undefined, // Convert null to undefined
    price: plan.price || undefined, // Handle optional price
    includedUsers: plan.includedUsers,
    vectorStoreGB: plan.vectorStoreGB,
    webSearchLimit: plan.webSearchLimit,
    additionalUserFee: plan.additionalUserFee || undefined // Handle optional fee
  })) as Plan[];
}

async function fetchTenantStats(): Promise<TenantStats> {
  // Get total tenant count
  const totalTenants = await db.tenant.count();

  // Get tenants with comprehensive data for stats calculation
  const tenantsWithSubscriptions = await db.tenant.findMany({
    select: {
      id: true,
      Subscription: {
        where: {
          isActive: true,
        },
        select: {
          id: true,
          plan: {
            select: {
              type: true,
            },
          },
        },
      },
      Membership: {
        select: {
          id: true,
        },
      },
    },
  });

  // Calculate enhanced stats
  let activeTenants = 0;
  let trialTenants = 0;
  let inactiveTenants = 0;
  let suspendedTenants = 0;
  let healthyTenants = 0;
  let atRiskTenants = 0;

  // For health score calculation, we'll need to process each tenant
  const healthScorePromises = tenantsWithSubscriptions.map(async (tenant: any) => {
    const hasActiveSubscription = tenant.Subscription.length > 0;
    const userCount = tenant.Membership.length;
    const planType = tenant.Subscription[0]?.plan?.type || "STARTER";

    // Calculate basic status
    if (hasActiveSubscription) {
      if (userCount > 0) {
        activeTenants++;
      } else {
        trialTenants++;
      }
    } else {
      inactiveTenants++;
    }

    // Calculate health score for this tenant
    const lastActivity = await getLastActivity(tenant.id);
    const tokenUsagePercent = await calculateTokenUsagePercent(tenant.id, planType);

    const healthScore = calculateHealthScore({
      lastActivity,
      tokenUsagePercent,
      hasActiveSubscription,
    });

    if (healthScore >= 80) {
      healthyTenants++;
    } else if (healthScore < 60) {
      atRiskTenants++;
    }

    return healthScore;
  });

  // Wait for all health score calculations
  await Promise.all(healthScorePromises);

  return {
    totalTenants,
    activeTenants,
    trialTenants,
    inactiveTenants,
    suspendedTenants, // For now, we don't have suspended status in the data
    healthyTenants,
    atRiskTenants,
  };
}

// Main server component
export default async function TenantsPage({
  searchParams,
}: {
  searchParams: { search?: string; page?: string; limit?: string; filter?: string };
}) {
  const session = await getServerSession(authOptions);
  if (!session) {
    redirect("/auth/signin");
  }

  // Parse search params with defaults
  const searchQuery = searchParams.search || "";
  const initialFilter = searchParams.filter || "";

  // Fetch all data in parallel
  const [{ tenants, total }, stats, availablePlans] = await Promise.all([
    fetchTenants(searchQuery),
    fetchTenantStats(),
    fetchAvailablePlans(),
  ]);

  const pageData: TenantsPageData = {
    tenants,
    stats,
    availablePlans,
    pagination: {
      page: 1,
      limit: tenants.length,
      total,
      totalPages: 1,
    },
    searchQuery,
    initialFilter,
  };

  return (
    <Suspense fallback={<div className="p-6">Loading tenants...</div>}>
      <EnhancedTenantsClient data={pageData} />
    </Suspense>
  );
}
