"use client";

import { useState } from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Calculator, DollarSign, Users, Database, Zap } from "lucide-react";

interface EstimationInputs {
  users: number;
  tokensPerMonth: number;
  storageGB: number;
  apiCallsPerMonth: number;
  plan: string;
}

interface CostBreakdown {
  tokenCosts: number;
  storageCosts: number;
  apiCosts: number;
  basePlanCost: number;
  totalCostUSD: number;
  totalCostCHF: number;
}

export function CostEstimatorClient() {
  const [inputs, setInputs] = useState<EstimationInputs>({
    users: 10,
    tokensPerMonth: 100000,
    storageGB: 5,
    apiCallsPerMonth: 1000,
    plan: "professional",
  });

  const [estimation, setEstimation] = useState<CostBreakdown | null>(null);

  const planPricing = {
    starter: { basePrice: 29, name: "Starter" },
    professional: { basePrice: 99, name: "Professional" },
    enterprise: { basePrice: 299, name: "Enterprise" },
  };

  const calculateCosts = () => {
    // Cost calculation logic
    const tokenCostPer1K = 0.002; // $0.002 per 1K tokens
    const storageCostPerGB = 0.10; // $0.10 per GB per month
    const apiCostPer1K = 0.01; // $0.01 per 1K API calls

    const tokenCosts = (inputs.tokensPerMonth / 1000) * tokenCostPer1K;
    const storageCosts = inputs.storageGB * storageCostPerGB;
    const apiCosts = (inputs.apiCallsPerMonth / 1000) * apiCostPer1K;
    const basePlanCost = planPricing[inputs.plan as keyof typeof planPricing]?.basePrice || 0;

    const totalCostUSD = tokenCosts + storageCosts + apiCosts + basePlanCost;
    const totalCostCHF = totalCostUSD * 0.91; // USD to CHF conversion

    setEstimation({
      tokenCosts,
      storageCosts,
      apiCosts,
      basePlanCost,
      totalCostUSD,
      totalCostCHF,
    });
  };

  const handleInputChange = (field: keyof EstimationInputs, value: string | number) => {
    setInputs(prev => ({
      ...prev,
      [field]: value
    }));
  };

  return (
    <div className="grid gap-6 md:grid-cols-2">
      {/* Input Panel */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calculator className="h-5 w-5" />
            Configuration
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="users">Number of Users</Label>
            <Input
              id="users"
              type="number"
              value={inputs.users}
              onChange={(e) => handleInputChange("users", parseInt(e.target.value) || 0)}
              min="1"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="tokens">Tokens per Month</Label>
            <Input
              id="tokens"
              type="number"
              value={inputs.tokensPerMonth}
              onChange={(e) => handleInputChange("tokensPerMonth", parseInt(e.target.value) || 0)}
              min="0"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="storage">Storage (GB)</Label>
            <Input
              id="storage"
              type="number"
              value={inputs.storageGB}
              onChange={(e) => handleInputChange("storageGB", parseInt(e.target.value) || 0)}
              min="0"
              step="0.1"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="api-calls">API Calls per Month</Label>
            <Input
              id="api-calls"
              type="number"
              value={inputs.apiCallsPerMonth}
              onChange={(e) => handleInputChange("apiCallsPerMonth", parseInt(e.target.value) || 0)}
              min="0"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="plan">Subscription Plan</Label>
            <Select
              value={inputs.plan}
              onValueChange={(value) => handleInputChange("plan", value)}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="starter">Starter - $29/month</SelectItem>
                <SelectItem value="professional">Professional - $99/month</SelectItem>
                <SelectItem value="enterprise">Enterprise - $299/month</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <Button onClick={calculateCosts} className="w-full">
            Calculate Costs
          </Button>
        </CardContent>
      </Card>

      {/* Results Panel */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <DollarSign className="h-5 w-5" />
            Cost Estimation
          </CardTitle>
        </CardHeader>
        <CardContent>
          {estimation ? (
            <div className="space-y-4">
              <div className="grid gap-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Badge variant="outline">Base Plan</Badge>
                    <span className="text-sm text-muted-foreground">
                      {planPricing[inputs.plan as keyof typeof planPricing]?.name}
                    </span>
                  </div>
                  <span className="font-medium">CHF {estimation.basePlanCost.toFixed(2)}</span>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Zap className="h-4 w-4 text-blue-500" />
                    <span className="text-sm">Token Usage</span>
                  </div>
                  <span className="font-medium">CHF {(estimation.tokenCosts * 0.91).toFixed(2)}</span>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Database className="h-4 w-4 text-green-500" />
                    <span className="text-sm">Storage</span>
                  </div>
                  <span className="font-medium">CHF {(estimation.storageCosts * 0.91).toFixed(2)}</span>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Users className="h-4 w-4 text-purple-500" />
                    <span className="text-sm">API Calls</span>
                  </div>
                  <span className="font-medium">CHF {(estimation.apiCosts * 0.91).toFixed(2)}</span>
                </div>
              </div>

              <div className="border-t pt-4">
                <div className="flex items-center justify-between text-lg font-bold">
                  <span>Total Monthly Cost</span>
                  <span className="text-primary">CHF {estimation.totalCostCHF.toFixed(2)}</span>
                </div>
                <p className="text-sm text-muted-foreground mt-1">
                  (${estimation.totalCostUSD.toFixed(2)} USD)
                </p>
              </div>

              <div className="bg-muted/50 p-3 rounded-lg">
                <h4 className="font-medium mb-2">Cost per User</h4>
                <p className="text-2xl font-bold text-primary">
                  CHF {(estimation.totalCostCHF / inputs.users).toFixed(2)}
                </p>
                <p className="text-sm text-muted-foreground">per user per month</p>
              </div>
            </div>
          ) : (
            <div className="text-center py-8 text-muted-foreground">
              <Calculator className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>Configure your settings and click "Calculate Costs" to see the estimation.</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
