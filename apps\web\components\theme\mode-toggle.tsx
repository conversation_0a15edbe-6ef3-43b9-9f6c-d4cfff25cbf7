"use client";

import * as React from "react";
import { MoonIcon, SunIcon } from "@radix-ui/react-icons";
import { useTheme } from "next-themes";
import { useLanguage } from "@/lib/language-context";
import { useSession } from "next-auth/react";
import { updateThemeConfig } from "@/services/theme-config";
import { getCookie } from "@/utils/cookies";
import toast from "react-hot-toast";

import { Button } from "@/components/ui/button";

export function ModeToggle() {
  const { setTheme, resolvedTheme } = useTheme();
  const { t } = useLanguage();
  const { data: session } = useSession();
  const [isUpdating, setIsUpdating] = React.useState(false);

  const handleSubmit = async () => {
    const newTheme = resolvedTheme === "dark" ? "light" : "dark";
    const isPartnerConsole = process.env.NEXT_PUBLIC_IS_PARTNER_CONSOLE === "1";

    // Get current organization ID from cookies or session
    const currentOrgId = getCookie("currentOrganizationId") ||
      (session as any)?.currentOrganization?.id ||
      (session as any)?.memberships?.[0]?.tenant?.id;

    // Conditional behavior based on NEXT_PUBLIC_IS_PARTNER_CONSOLE
    if (isPartnerConsole && currentOrgId) {
      // Partner console: Save to database and sync across tenant members
      setIsUpdating(true);
      try {
        const result = await updateThemeConfig(currentOrgId, {
          themePreset: newTheme,
        });

        if (result.success) {
          // The theme will be applied via real-time updates
          console.log("Theme updated successfully via database");
        } else {
          // Fallback to local theme change
          setTheme(newTheme);
          toast.error("Failed to sync theme with team. Applied locally only.");
        }
      } catch (error) {
        // Fallback to local theme change
        setTheme(newTheme);
        toast.error("Failed to sync theme with team. Applied locally only.");
      } finally {
        setIsUpdating(false);
      }
    } else {
      // Non-partner console: Only affect local session, no database saving
      setTheme(newTheme);
    }
  };

  return (
    <>
      <Button
        onClick={handleSubmit}
        variant="outline"
        size="icon"
        disabled={isUpdating}
      >
        <SunIcon className="h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0" />
        <MoonIcon className="absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
        <span className="sr-only">{t("theme.toggleTheme")}</span>
      </Button>
    </>
  );
}
