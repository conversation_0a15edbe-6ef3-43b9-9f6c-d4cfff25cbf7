import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/next-auth/src/auth-options";
import { BlobServiceClient } from "@azure/storage-blob";
import db from "@/lib/shared-db";

const containerName = process.env.AZURE_STORAGE_CONTAINER_NAME || "default";
const connectionString = process.env.AZURE_STORAGE_CONNECTION_STRING || "";

export async function POST(
  request: NextRequest,
  { params }: { params: { tenantId: string } },
) {
  try {
    // Check if partner console is enabled
    const isPartnerConsole = process.env.NEXT_PUBLIC_IS_PARTNER_CONSOLE === "1";
    if (!isPartnerConsole) {
      return NextResponse.json(
        {
          error: "Logo upload is only available in Partner Console mode",
        },
        { status: 403 },
      );
    }

    const session: any = await getServerSession(authOptions);
    if (!session?.userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { tenantId } = params;

    // Check if user has admin access to this tenant
    const membership = await db.membership.findFirst({
      where: {
        userId: session.userId,
        tenantId: tenantId,
        role: {
          in: ["ADMIN", "OWNER"],
        },
      },
      include: {
        tenant: true,
      },
    });

    if (!membership) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const formData = await request.formData();
    const file = formData.get("file") as File;
    const type = formData.get("type") as string;

    if (!file || !type) {
      return NextResponse.json(
        { error: "Missing file or type parameter" },
        { status: 400 },
      );
    }

    // Validate file type
    if (!file.type.startsWith("image/")) {
      return NextResponse.json(
        { error: "Only image files are allowed" },
        { status: 400 },
      );
    }

    // Validate file size (5MB limit)
    const maxSize = 5 * 1024 * 1024; // 5MB
    if (file.size > maxSize) {
      return NextResponse.json(
        { error: "File size must be less than 5MB" },
        { status: 400 },
      );
    }

    // Validate type parameter
    if (!["logo", "favicon", "fullAppLogo"].includes(type)) {
      return NextResponse.json(
        { error: "Invalid type parameter" },
        { status: 400 },
      );
    }

    const blobServiceClient = BlobServiceClient.fromConnectionString(connectionString);
    const containerClient = blobServiceClient.getContainerClient(containerName);

    // Create container if it doesn't exist
    await containerClient.createIfNotExists({
      access: "container",
    });

    // Generate blob path for theme assets
    const fileExtension = file.name.split('.').pop();
    const blobName = `${membership.tenant.slug}/theme/${type}-${Date.now()}.${fileExtension}`;
    const blockBlobClient = containerClient.getBlockBlobClient(blobName);

    // Convert File to ArrayBuffer
    const arrayBuffer = await file.arrayBuffer();

    // Upload file
    await blockBlobClient.uploadData(arrayBuffer, {
      blobHTTPHeaders: { blobContentType: file.type },
    });

    const uploadedUrl = blockBlobClient.url;

    // Update theme configuration with the new URL
    const fieldName = type === "logo" ? "logoUrl" : type === "fullAppLogo" ? "fullAppLogoUrl" : "faviconUrl";
    
    await db.themeConfiguration.upsert({
      where: { tenantId: tenantId },
      create: {
        tenantId: tenantId,
        [fieldName]: uploadedUrl,
        isActive: true,
        version: 1,
      },
      update: {
        [fieldName]: uploadedUrl,
        updatedAt: new Date(),
        version: { increment: 1 },
      },
    });

    return NextResponse.json({
      success: true,
      url: uploadedUrl,
      message: `${type === "logo" ? "Logo" : type === "logoDark" ? "Dark logo" : type === "fullAppLogo" ? "Full app logo" : "Favicon"} uploaded successfully`,
    });
  } catch (error) {
    console.error("Error uploading logo:", error);
    return NextResponse.json(
      { error: "Failed to upload logo" },
      { status: 500 },
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { tenantId: string } },
) {
  try {
    // Check if partner console is enabled
    const isPartnerConsole = process.env.NEXT_PUBLIC_IS_PARTNER_CONSOLE === "1";
    if (!isPartnerConsole) {
      return NextResponse.json(
        {
          error: "Logo deletion is only available in Partner Console mode",
        },
        { status: 403 },
      );
    }

    const session: any = await getServerSession(authOptions);
    if (!session?.userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { tenantId } = params;
    const { searchParams } = new URL(request.url);
    const type = searchParams.get("type");

    // Check if user has admin access to this tenant
    const membership = await db.membership.findFirst({
      where: {
        userId: session.userId,
        tenantId: tenantId,
        role: {
          in: ["ADMIN", "OWNER"],
        },
      },
    });

    if (!membership) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    if (!type || !["logo", "favicon", "fullAppLogo"].includes(type)) {
      return NextResponse.json(
        { error: "Invalid or missing type parameter" },
        { status: 400 },
      );
    }

    // Get current theme configuration to find the URL to delete
    const themeConfig = await db.themeConfiguration.findUnique({
      where: { tenantId: tenantId },
    });

    if (!themeConfig) {
      return NextResponse.json(
        { error: "Theme configuration not found" },
        { status: 404 },
      );
    }

    const fieldName = type === "logo" ? "logoUrl" : type === "fullAppLogo" ? "fullAppLogoUrl" : "faviconUrl";
    const currentUrl = themeConfig[fieldName as keyof typeof themeConfig] as string;

    if (!currentUrl) {
      return NextResponse.json(
        { error: `No ${type} found to delete` },
        { status: 404 },
      );
    }

    // Delete from Azure Storage if it's an Azure blob URL
    if (currentUrl.includes("blob.core.windows.net")) {
      try {
        const blobServiceClient = BlobServiceClient.fromConnectionString(connectionString);
        const containerClient = blobServiceClient.getContainerClient(containerName);
        
        // Extract blob name from URL
        const urlParts = currentUrl.split("/");
        const blobName = urlParts.slice(urlParts.indexOf(containerName) + 1).join("/");
        
        const blockBlobClient = containerClient.getBlockBlobClient(blobName);
        await blockBlobClient.deleteIfExists();
      } catch (storageError) {
        console.error("Error deleting from storage:", storageError);
        // Continue with database update even if storage deletion fails
      }
    }

    // Update theme configuration to remove the URL
    await db.themeConfiguration.update({
      where: { tenantId: tenantId },
      data: {
        [fieldName]: null,
        updatedAt: new Date(),
        version: { increment: 1 },
      },
    });

    return NextResponse.json({
      success: true,
      message: `${type === "logo" ? "Logo" : type === "fullAppLogo" ? "Full app logo" : "Favicon"} deleted successfully`,
    });
  } catch (error) {
    console.error("Error deleting logo:", error);
    return NextResponse.json(
      { error: "Failed to delete logo" },
      { status: 500 },
    );
  }
}
