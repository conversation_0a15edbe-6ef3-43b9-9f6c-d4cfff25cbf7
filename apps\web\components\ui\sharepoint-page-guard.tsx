"use client";

import React, { useState } from "react";
import { <PERSON>, CardContent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Shield,
  Lock,
  ExternalLink,
  AlertTriangle,
  RefreshCw,
  Clock,
  CheckCircle,
} from "lucide-react";
import { useLanguage } from "@/lib/language-context";
import { useRouter } from "next/navigation";

interface SharePointPageAccess {
  hasAccess: boolean;
  permissionLevel?: string;
  errorMessage?: string;
  requiresIntegration?: boolean;
  cached?: boolean;
  cacheExpiry?: Date;
}

interface SharePointPageGuardProps {
  children: React.ReactNode;
  sharePointAccess: SharePointPageAccess;
  pageName?: string;
  pageId?: string;
  sharePointPath?: string;
  onRefreshAccess?: () => void;
  showCacheInfo?: boolean;
}

/**
 * Component that guards access to SharePoint-synced pages
 * Shows appropriate UI based on user's SharePoint permissions
 */
export function SharePointPageGuard({
  children,
  sharePointAccess,
  pageName,
  pageId,
  sharePointPath,
  onRefreshAccess,
  showCacheInfo = false,
}: SharePointPageGuardProps) {
  const { t } = useLanguage();
  const router = useRouter();
  const [isRefreshing, setIsRefreshing] = useState(false);

  // If user has access, show the content
  if (sharePointAccess.hasAccess) {
    return (
      <div>
        {showCacheInfo && sharePointAccess.cached && (
          <Alert className="mb-4 border-blue-200 bg-blue-50">
            <Clock className="h-4 w-4 text-blue-600" />
            <AlertDescription className="text-blue-800">
              <div className="flex items-center justify-between">
                <span>
                  {t("sharepoint.cachedAccess") ||
                    "Using cached SharePoint access"}
                  {sharePointAccess.cacheExpiry && (
                    <span className="text-sm ml-1">
                      (expires{" "}
                      {new Date(sharePointAccess.cacheExpiry).toLocaleString()})
                    </span>
                  )}
                </span>
                {onRefreshAccess && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={async () => {
                      setIsRefreshing(true);
                      await onRefreshAccess();
                      setIsRefreshing(false);
                    }}
                    disabled={isRefreshing}
                    className="ml-2"
                  >
                    <RefreshCw
                      className={`h-3 w-3 mr-1 ${
                        isRefreshing ? "animate-spin" : ""
                      }`}
                    />
                    {t("sharepoint.refresh") || "Refresh"}
                  </Button>
                )}
              </div>
            </AlertDescription>
          </Alert>
        )}
        {children}
      </div>
    );
  }

  // If user doesn't have access, show restriction UI
  const isIntegrationMissing = sharePointAccess.requiresIntegration;
  const isAccessDenied = !isIntegrationMissing;

  const handleConnectSharePoint = () => {
    router.push("/settings/integrations?connect=sharepoint");
  };

  const handleRefreshAccess = async () => {
    if (onRefreshAccess) {
      setIsRefreshing(true);
      await onRefreshAccess();
      setIsRefreshing(false);
    } else if (pageId) {
      // Refresh the page with force refresh parameter
      const url = new URL(window.location.href);
      url.searchParams.set("refreshSharePointAccess", "true");

      window.location.replace(url.toString());
      window.location.reload();
    }
  };

  return (
    <div className="min-h-[400px] flex items-center justify-center p-8">
      <Card className="max-w-2xl w-full">
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            {isIntegrationMissing ? (
              <ExternalLink className="h-12 w-12 text-blue-500" />
            ) : (
              <Lock className="h-12 w-12 text-amber-500" />
            )}
          </div>
          <CardTitle className="text-xl">
            {isIntegrationMissing
              ? t("sharepoint.integrationRequired") ||
                "SharePoint Connection Required"
              : t("common.accessDenied") || "Access Denied"}
          </CardTitle>
        </CardHeader>

        <CardContent className="space-y-4">
          <div className="text-center">
            <p className="text-muted-foreground mb-4">
              {isIntegrationMissing
                ? t("sharepoint.integrationRequiredMessage") ||
                  "This page contains SharePoint content. You need to connect your SharePoint account to access it."
                : t("sharepoint.accessDeniedMessage") ||
                  "You don't have permission to access this SharePoint-synced page."}
            </p>

            {pageName && (
              <div className="flex items-center justify-center space-x-2 mb-4">
                <Shield className="h-4 w-4 text-muted-foreground" />
                <span className="font-medium">{pageName}</span>
              </div>
            )}

            {sharePointPath && (
              <div className="text-center mb-4">
                <p className="text-sm text-muted-foreground">
                  <span className="font-medium">{t("sharepoint.path")}</span>{" "}
                  {sharePointPath}
                </p>
              </div>
            )}
          </div>

          {sharePointAccess.errorMessage && (
            <Alert className="border-amber-200 bg-amber-50">
              <AlertTriangle className="h-4 w-4 text-amber-600" />
              <AlertDescription className="text-amber-800">
                {sharePointAccess.errorMessage}
              </AlertDescription>
            </Alert>
          )}

          <div className="flex flex-col sm:flex-row gap-3 justify-center">
            {isIntegrationMissing && (
              <Button
                onClick={handleConnectSharePoint}
                className="bg-blue-600 hover:bg-blue-700"
              >
                <ExternalLink className="h-4 w-4 mr-2" />
                {t("sharepoint.connectAccount") || "Connect SharePoint"}
              </Button>
            )}

            <Button
              variant="outline"
              onClick={handleRefreshAccess}
              disabled={isRefreshing}
            >
              <RefreshCw
                className={`h-4 w-4 mr-2 ${isRefreshing ? "animate-spin" : ""}`}
              />
              {isRefreshing
                ? t("sharepoint.checking") || "Checking..."
                : t("sharepoint.checkAccess") || "Check Access"}
            </Button>
          </div>

          {isAccessDenied && (
            <Alert className="border-blue-200 bg-blue-50">
              <AlertTriangle className="h-4 w-4 text-blue-600" />
              <AlertDescription className="text-blue-800">
                <div>
                  <p className="font-medium mb-1">
                    {t("sharepoint.needAccess") ||
                      "Need access to this content?"}
                  </p>
                  <p className="text-sm">
                    {t("sharepoint.contactOwner") ||
                      "Contact the SharePoint site owner or your administrator to request access."}
                  </p>
                </div>
              </AlertDescription>
            </Alert>
          )}

          {sharePointAccess.cached && (
            <div className="text-center">
              <p className="text-xs text-muted-foreground">
                {t("sharepoint.lastChecked") || "Last checked"}:{" "}
                {sharePointAccess.cacheExpiry
                  ? new Date(sharePointAccess.cacheExpiry).toLocaleString()
                  : "Recently"}
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

/**
 * Status badge showing SharePoint access status
 */
export function SharePointAccessStatus({
  sharePointAccess,
  size = "sm",
}: {
  sharePointAccess: SharePointPageAccess;
  size?: "sm" | "xs";
}) {
  const { t } = useLanguage();
  const iconSize = size === "xs" ? "h-3 w-3" : "h-4 w-4";
  const textSize = size === "xs" ? "text-xs" : "text-sm";

  if (!sharePointAccess.hasAccess) {
    return (
      <div
        className={`inline-flex items-center space-x-1 px-2 py-1 rounded-md bg-red-50 border border-red-200 ${textSize}`}
      >
        <Lock className={`${iconSize} text-red-500`} />
        <span className="text-red-700 font-medium">
          {sharePointAccess.requiresIntegration
            ? t("sharepoint.connectNeeded") || "Connect Required"
            : t("common.accessDenied") || "Access Denied"}
        </span>
      </div>
    );
  }

  if (sharePointAccess.cached) {
    return (
      <div
        className={`inline-flex items-center space-x-1 px-2 py-1 rounded-md bg-blue-50 border border-blue-200 ${textSize}`}
      >
        <Clock className={`${iconSize} text-blue-500`} />
        <span className="text-blue-700 font-medium">
          {t("sharepoint.cached") || "Cached"}
        </span>
      </div>
    );
  }

  return (
    <div
      className={`inline-flex items-center space-x-1 px-2 py-1 rounded-md bg-green-50 border border-green-200 ${textSize}`}
    >
      <CheckCircle className={`${iconSize} text-green-500`} />
      <span className="text-green-700 font-medium">
        {t("sharepoint.verified") || "Verified"}
      </span>
    </div>
  );
}

/**
 * Hook for managing SharePoint page access
 */
export function useSharePointPageAccess(pageId?: string) {
  const [isRefreshing, setIsRefreshing] = useState(false);
  const router = useRouter();

  const refreshAccess = async () => {
    if (!pageId) return;

    setIsRefreshing(true);
    try {
      // Add refresh parameter and reload
      const url = new URL(window.location.href);
      url.searchParams.set("refreshSharePointAccess", "true");
      router.push(url.toString());
    } finally {
      setIsRefreshing(false);
    }
  };

  const connectSharePoint = () => {
    router.push("/settings/integrations?connect=sharepoint");
  };

  return {
    isRefreshing,
    refreshAccess,
    connectSharePoint,
  };
}
