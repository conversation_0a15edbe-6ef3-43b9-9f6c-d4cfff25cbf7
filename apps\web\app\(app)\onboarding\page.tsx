import { CompanyProfileForm } from "@/components/model/onboarding-model";
import { authOptions } from "@/lib/next-auth";
import { getServerSession } from "next-auth";
import { redirect } from "next/navigation";

export default async function Page() {
  const session: any = await getServerSession(authOptions);

  if (!session) {
    return redirect("/sign-in");
  } else if (session?.memberships?.length > 0) {
    return redirect("/dashboard");
  }
  return <CompanyProfileForm />;
}
