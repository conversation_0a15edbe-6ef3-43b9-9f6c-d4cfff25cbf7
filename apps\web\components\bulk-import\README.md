# Bulk Member Import

This feature allows administrators to import multiple team members at once using a CSV file upload.

## Components

### `BulkMemberImportDialog`
Main dialog component that orchestrates the entire bulk import process.

**Props:**
- `tenantId: string` - The organization's tenant ID
- `onImportComplete?: () => void` - Callback when import is finished
- `trigger?: React.ReactNode` - Custom trigger element (optional)

**Features:**
- Multi-step wizard interface (Upload → Preview → Import)
- Progress tracking with real-time updates
- Error handling and validation
- Batch processing to avoid overwhelming the server

### `CSVUploadZone`
File upload component with drag-and-drop support and custom roles integration.

**Props:**
- `onFileSelect: (file: File) => void` - Callback when file is selected
- `tenantId: string` - Organization's tenant ID for fetching custom roles
- `isLoading?: boolean` - Loading state
- `error?: string` - Error message to display
- `className?: string` - Additional CSS classes

**Features:**
- Drag and drop file upload
- CSV template download with custom role examples
- Format validation
- Visual feedback for upload states
- **Custom roles reference** - Shows available custom roles with IDs
- **Smart template generation** - Includes actual custom role IDs in template

### `MemberPreviewTable`
Displays parsed member data with validation results.

**Props:**
- `parseResult: CSVParseResult` - Parsed CSV data with errors/warnings
- `className?: string` - Additional CSS classes

**Features:**
- Data preview table with role badges
- Error and warning summaries
- Validation feedback
- Statistics display

### `ImportProgressComponent`
Shows real-time import progress and results.

**Props:**
- `progress: ImportProgress` - Current import progress state
- `className?: string` - Additional CSS classes

**Features:**
- Real-time progress bar
- Success/failure statistics
- Error details with specific failure reasons
- Completion status

## CSV Format

### Required Columns
- `email` - User's email address (must be valid email format)
- `role` - User role: `MEMBER`, `ADMIN`, or `CUSTOM`

### Optional Columns
- `name` - User's display name (defaults to email prefix if not provided)

### Using Custom Roles
Simply use "CUSTOM" in the role column to assign users to custom roles. The system will automatically handle custom role assignment based on your organization's settings.

### Example CSV
```csv
email,name,role
<EMAIL>,John Doe,MEMBER
<EMAIL>,Jane Admin,ADMIN
<EMAIL>,Custom User,CUSTOM
```

### Available Custom Roles Display
The bulk import interface shows available custom roles for reference:
- Lists all custom roles in your organization
- Shows role names and descriptions
- Helps you understand what "CUSTOM" role assignments will do

## API Endpoint

### `POST /api/admin/bulk-import/members`

Imports a single member as part of the bulk import process.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "name": "User Name",
  "role": "MEMBER",
  "tenantId": "tenant-id"
}
```

**Response:**
```json
{
  "message": "Member imported successfully",
  "data": {
    "userId": "user-id",
    "email": "<EMAIL>",
    "name": "User Name",
    "role": "MEMBER",
    "invitationId": "invitation-id"
  }
}
```

**Error Responses:**
- `400` - Validation errors (invalid email, role, etc.)
- `403` - Permission denied or subscription limits reached
- `500` - Server error

## Security & Permissions

- Requires `CREATE` permission on `MEMBER` resource
- Validates user subscription limits
- Checks for active subscription
- Prevents duplicate member imports
- Uses existing RBAC system for authorization

## Usage

```tsx
import { BulkMemberImportDialog } from '@/components/bulk-import/bulk-member-import-dialog';

function MemberManagement({ tenantId }) {
  return (
    <BulkMemberImportDialog
      tenantId={tenantId}
      onImportComplete={() => {
        // Refresh member list
        window.location.reload();
      }}
    />
  );
}
```

## Features

### Validation
- Email format validation
- Role validation (MEMBER, ADMIN, CUSTOM)
- Duplicate email detection
- Subscription limit checking

### Error Handling
- Detailed error messages for each validation failure
- Batch processing with individual error tracking
- Graceful handling of partial failures
- User-friendly error display

### User Experience
- Step-by-step wizard interface
- Real-time progress updates
- Template download for correct format
- Drag and drop file upload
- Visual feedback for all states

### Performance
- Batch processing (5 members per batch)
- Rate limiting between batches
- Efficient CSV parsing
- Memory-conscious file handling

## Testing

Run tests with:
```bash
npm test csv-parser.test.ts
```

The test suite covers:
- CSV parsing with various formats
- Validation logic
- Error handling
- Template generation
