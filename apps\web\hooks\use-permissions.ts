"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { getCookie } from "@/utils/cookies";

// Extend the Session type to include our custom properties
interface ExtendedSession {
  user?: {
    name?: string | null;
    email?: string | null;
    image?: string | null;
    role?: string;
    id?: string;
  };
  userId?: string;
}

interface Permission {
  action: string;
  resource: string;
}

interface UsePermissionsProps {
  workspaceId?: string;
  workspaceSlug?: string;
}

/**
 * Hook to check if the current user has a specific permission
 * @param props - Optional properties including workspaceId or workspaceSlug for workspace-specific permissions
 * @returns An object with functions to check permissions
 */
export function usePermissions(props?: UsePermissionsProps) {
  const { workspaceId, workspaceSlug } = props || {};
  const { data: session } = useSession() as { data: ExtendedSession | null };
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchPermissions = async () => {
      if (!session?.user) {
        setLoading(false);
        return;
      }

      try {
        const tenantId = getCookie("currentOrganizationId");
        if (!tenantId) {
          setError("No organization selected");
          setLoading(false);
          return;
        }

        // Include userId in the request for server-side compatibility
        const userId = session.userId || session.user?.id;

        // Build the URL with optional workspaceId or workspaceSlug parameter
        let url = `/api/permissions/user?tenantId=${tenantId}&userId=${userId}`;
        if (workspaceSlug) {
          url += `&workspaceSlug=${encodeURIComponent(workspaceSlug)}`;
        } else if (workspaceId) {
          url += `&workspaceId=${workspaceId}`;
        }

        const response = await fetch(url);
        if (!response.ok) {
          throw new Error("Failed to fetch permissions");
        }

        const data = await response.json();
        setPermissions(data.permissions || []);
      } catch (err) {
        console.error("Error fetching permissions:", err);
        setError(err.message || "Failed to fetch permissions");
      } finally {
        setLoading(false);
      }
    };

    fetchPermissions();
  }, [session, workspaceId, workspaceSlug]);

  /**
   * Check if the user has a specific permission
   * @param action - The action to check (CREATE, READ, UPDATE, DELETE)
   * @param resource - The resource to check (WORKSPACE, PAGE, FOLDER, FILE, MEMBER)
   * @returns boolean - Whether the user has the permission
   */
  const hasPermission = (action: string, resource: string): boolean => {
    if (loading || error) return false;

    // For OWNER role, always return true (owners have all permissions)
    const userRole = session?.user?.role;
    if (userRole === "OWNER") {
      return true;
    }

    // For all other roles (including ADMIN and MEMBER), rely on server-side permissions
    // This ensures that built-in role configurations are properly respected
    return permissions.some(
      (p) => p.action === action && p.resource === resource
    );
  };

  /**
   * Check if the user can create a resource
   * @param resource - The resource to check (WORKSPACE, PAGE, FOLDER, FILE, MEMBER)
   * @returns boolean - Whether the user can create the resource
   */
  const canCreate = (resource: string): boolean => {
    return hasPermission("CREATE", resource);
  };

  /**
   * Check if the user can read a resource
   * @param resource - The resource to check (WORKSPACE, PAGE, FOLDER, FILE, MEMBER)
   * @returns boolean - Whether the user can read the resource
   */
  const canRead = (resource: string): boolean => {
    return hasPermission("READ", resource);
  };

  /**
   * Check if the user can update a resource
   * @param resource - The resource to check (WORKSPACE, PAGE, FOLDER, FILE, MEMBER)
   * @returns boolean - Whether the user can update the resource
   */
  const canUpdate = (resource: string): boolean => {
    return hasPermission("UPDATE", resource);
  };

  /**
   * Check if the user can delete a resource
   * @param resource - The resource to check (WORKSPACE, PAGE, FOLDER, FILE, MEMBER)
   * @returns boolean - Whether the user can delete the resource
   */
  const canDelete = (resource: string): boolean => {
    return hasPermission("DELETE", resource);
  };

  return {
    loading,
    error,
    permissions,
    hasPermission,
    canCreate,
    canRead,
    canUpdate,
    canDelete,
  };
}
