"use client";

import { useSession } from "next-auth/react";
import { useEffect, useState } from "react";
import { QuickAskProvider } from "./quick-ask-provider";

export function QuickAskWrapper({ children }: { children: React.ReactNode }) {
  const { data: session } = useSession();
  const [userId, setUserId] = useState<string>("");
  const [tenantId, setTenantId] = useState<string>("");
  const [userName, setUserName] = useState<string>("");

  useEffect(() => {
    if (session) {
      // Get user ID from session
      const userIdValue = (session as any)?.userId || "";
      setUserId(userIdValue);

      // Get tenant ID from session
      const tenantIdValue =
        (session as any)?.memberships?.[0]?.tenant?.id || "";
      setTenantId(tenantIdValue);

      // Get user name from session
      const userNameValue = (session as any)?.user?.name || "";
      setUserName(userNameValue);
    }
  }, [session]);

  // Always render the provider to maintain consistent hook structure
  // Pass empty strings if data is not available yet
  return (
    <QuickAskProvider
      userId={userId}
      tenantId={tenantId}
      userName={userName}
      isReady={!!(userId && tenantId)}
    >
      {children}
    </QuickAskProvider>
  );
}
