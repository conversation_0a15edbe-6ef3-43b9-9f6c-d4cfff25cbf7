import { NextRequest, NextResponse } from "next/server";
import db from "@/lib/shared-db";
import crypto from "crypto";

interface WebhookEvent {
  type: "message.sent" | "session.started" | "session.ended" | "error.occurred";
  chatbotId: string;
  sessionId: string;
  timestamp: string;
  data: any;
}

interface WebhookConfig {
  url: string;
  secret?: string;
  events: string[];
  active: boolean;
}

// POST endpoint to register/update webhook
export async function POST(
  req: NextRequest,
  { params }: { params: { chatbotId: string } }
) {
  try {
    const { chatbotId } = params;
    const {
      url,
      secret,
      events,
      active = true,
    }: WebhookConfig = await req.json();

    // Get API key from Authorization header
    const authHeader = req.headers.get("authorization");
    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return NextResponse.json(
        { error: "Missing or invalid API key" },
        { status: 401 }
      );
    }

    const apiKey = authHeader.substring(7);

    // Validate chatbot and API key
    const chatbot = await db.chatbot.findUnique({
      where: { id: chatbotId },
    });

    if (!chatbot || chatbot.apiKey !== apiKey) {
      return NextResponse.json(
        { error: "Invalid chatbot or API key" },
        { status: 401 }
      );
    }

    // Validate webhook URL
    if (!url || !isValidUrl(url)) {
      return NextResponse.json(
        { error: "Invalid webhook URL" },
        { status: 400 }
      );
    }

    // Validate events
    const validEvents = [
      "message.sent",
      "session.started",
      "session.ended",
      "error.occurred",
    ];
    if (
      !events ||
      !Array.isArray(events) ||
      !events.every((event) => validEvents.includes(event))
    ) {
      return NextResponse.json(
        {
          error: "Invalid events. Must be array of: " + validEvents.join(", "),
        },
        { status: 400 }
      );
    }

    // Update chatbot with webhook configuration
    const updatedChatbot = await db.chatbot.update({
      where: { id: chatbotId },
      data: {
        customization: {
          ...chatbot.customization,
          webhook: {
            url,
            secret,
            events,
            active,
          },
        },
      },
    });

    return NextResponse.json({
      message: "Webhook configured successfully",
      webhook: {
        url,
        events,
        active,
      },
    });
  } catch (error) {
    console.error("[WEBHOOK_POST]", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// GET endpoint to retrieve webhook configuration
export async function GET(
  req: NextRequest,
  { params }: { params: { chatbotId: string } }
) {
  try {
    const { chatbotId } = params;

    // Get API key from Authorization header
    const authHeader = req.headers.get("authorization");
    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return NextResponse.json(
        { error: "Missing or invalid API key" },
        { status: 401 }
      );
    }

    const apiKey = authHeader.substring(7);

    // Validate chatbot and API key
    const chatbot = await db.chatbot.findUnique({
      where: { id: chatbotId },
    });

    if (!chatbot || chatbot.apiKey !== apiKey) {
      return NextResponse.json(
        { error: "Invalid chatbot or API key" },
        { status: 401 }
      );
    }

    const webhook = chatbot.customization?.webhook || null;

    return NextResponse.json({
      webhook: webhook
        ? {
            url: webhook.url,
            events: webhook.events,
            active: webhook.active,
            // Don't return the secret for security
          }
        : null,
    });
  } catch (error) {
    console.error("[WEBHOOK_GET]", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// DELETE endpoint to remove webhook
export async function DELETE(
  req: NextRequest,
  { params }: { params: { chatbotId: string } }
) {
  try {
    const { chatbotId } = params;

    // Get API key from Authorization header
    const authHeader = req.headers.get("authorization");
    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return NextResponse.json(
        { error: "Missing or invalid API key" },
        { status: 401 }
      );
    }

    const apiKey = authHeader.substring(7);

    // Validate chatbot and API key
    const chatbot = await db.chatbot.findUnique({
      where: { id: chatbotId },
    });

    if (!chatbot || chatbot.apiKey !== apiKey) {
      return NextResponse.json(
        { error: "Invalid chatbot or API key" },
        { status: 401 }
      );
    }

    // Remove webhook configuration
    await db.chatbot.update({
      where: { id: chatbotId },
      data: {
        customization: {
          ...chatbot.customization,
          webhook: null,
        },
      },
    });

    return NextResponse.json({
      message: "Webhook removed successfully",
    });
  } catch (error) {
    console.error("[WEBHOOK_DELETE]", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// Utility function to validate URL
function isValidUrl(string: string): boolean {
  try {
    const url = new URL(string);
    return url.protocol === "http:" || url.protocol === "https:";
  } catch (_) {
    return false;
  }
}

// Utility function to send webhook (to be used by other parts of the system)
async function sendWebhook(
  chatbotId: string,
  event: WebhookEvent
): Promise<boolean> {
  try {
    const chatbot = await db.chatbot.findUnique({
      where: { id: chatbotId },
    });

    if (!chatbot || !chatbot.customization?.webhook) {
      return false;
    }

    const webhook = chatbot.customization.webhook;

    // Check if webhook is active and event is subscribed
    if (!webhook.active || !webhook.events.includes(event.type)) {
      return false;
    }

    // Prepare webhook payload
    const payload = {
      ...event,
      chatbotId,
    };

    // Create signature if secret is provided
    let headers: Record<string, string> = {
      "Content-Type": "application/json",
      "User-Agent": "SwissKnowledgeHub-Webhook/1.0",
    };

    if (webhook.secret) {
      const signature = crypto
        .createHmac("sha256", webhook.secret)
        .update(JSON.stringify(payload))
        .digest("hex");
      headers["X-Webhook-Signature"] = `sha256=${signature}`;
    }

    // Send webhook
    const response = await fetch(webhook.url, {
      method: "POST",
      headers,
      body: JSON.stringify(payload),
      signal: AbortSignal.timeout(10000), // 10 second timeout
    });

    return response.ok;
  } catch (error) {
    console.error("Error sending webhook:", error);
    return false;
  }
}

// Handle CORS
export async function OPTIONS(req: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "GET, POST, DELETE, OPTIONS",
      "Access-Control-Allow-Headers": "Content-Type, Authorization",
      "Access-Control-Max-Age": "86400",
    },
  });
}
