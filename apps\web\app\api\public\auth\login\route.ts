import { NextRequest, NextResponse } from "next/server";
import db from "@/lib/shared-db";
import { compare } from "bcrypt";
import crypto from "crypto";
import { checkUserBlockedGlobal, logBlockedAuthAttempt } from "@/lib/services/user-blocking";

// CORS headers for cross-origin requests
const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
  "Access-Control-Allow-Headers": "Content-Type, Authorization, X-User-Token",
};

interface LoginRequest {
  email: string;
  password: string;
  chatbotId?: string; // Optional: to check if user has access to specific chatbot
}

interface LoginResponse {
  success: boolean;
  userToken?: string;
  user?: {
    id: string;
    name: string;
    email: string;
  };
  error?: string;
  chatbotAccess?: boolean;
}

/**
 * Handle preflight OPTIONS requests for CORS
 */
export async function OPTIONS() {
  return new NextResponse(null, { status: 200, headers: corsHeaders });
}

/**
 * Public login endpoint for chatbot SDK users
 * This allows users to authenticate for private chatbot access
 */
export async function POST(request: NextRequest) {
  try {
    const body: LoginRequest = await request.json();
    const { email, password, chatbotId } = body;

    // Validate required fields
    if (!email || !password) {
      return NextResponse.json(
        { success: false, error: "Email and password are required" },
        { status: 400, headers: corsHeaders }
      );
    }

    const emailHash = crypto
      .createHash("sha256")
      .update(email?.toLowerCase())
      .digest("hex");
    // Find user by email
    const user = await db.user.findFirst({
      where: {
        emailHash,
      },
      select: {
        id: true,
        name: true,
        email: true,
        password: true,
      },
    });

    console.log({ user });

    if (!user) {
      return NextResponse.json(
        { success: false, error: "Invalid email or password" },
        { status: 401, headers: corsHeaders }
      );
    }

    console.log({ password, userPassword: user.password });

    // Verify password
    const isValidPassword = await compare(password, user.password);
    console.log({ isValidPassword });
    if (!isValidPassword) {
      return NextResponse.json(
        { success: false, error: "Invalid email or password" },
        { status: 401, headers: corsHeaders }
      );
    }

    // Check if user is blocked globally (in any tenant)
    const blockCheck = await checkUserBlockedGlobal(
      user.id,
      user.email,
      undefined // No IP address available in this context
    );

    if (blockCheck.blocked) {
      // Log the blocked authentication attempt
      logBlockedAuthAttempt(
        user.id,
        user.email,
        undefined,
        blockCheck,
        "public_api_login"
      );

      return NextResponse.json(
        {
          success: false,
          error: blockCheck.reason || "Your account has been blocked. Please contact support.",
          blocked: true,
          blockInfo: {
            type: blockCheck.blockType,
            expiresAt: blockCheck.expiresAt,
          }
        },
        { status: 403, headers: corsHeaders }
      );
    }



    // Generate user token (simple base64 encoding for demo)
    // In production, use proper JWT with expiration
    const userToken = Buffer.from(
      JSON.stringify({
        userId: user.id,
        email: user.email,
        timestamp: Date.now(),
      })
    ).toString("base64");

    const response: LoginResponse = {
      success: true,
      userToken,
      user: {
        id: user.id,
        name: user.name,
        email: user.email,
      },
    };

    // If chatbotId is provided, check access
    if (chatbotId) {
      try {
        const chatbot = await db.chatbot.findFirst({
          where: {
            id: chatbotId,
            isActive: true,
          },
          select: {
            id: true,
            access: true,
            companyIds: true,
          },
        });

        if (!chatbot) {
          response.chatbotAccess = false;
        } else if (chatbot.access === "public") {
          response.chatbotAccess = true;
        } else if (chatbot.access === "private") {
          // Check company membership
          const userCompanyMemberships = await db.companyMember.findMany({
            where: {
              userId: user.id,
              companyId: { in: chatbot.companyIds || [] },
              status: "ACTIVE",
            },
          });
          response.chatbotAccess = userCompanyMemberships.length > 0;
        } else {
          response.chatbotAccess = false;
        }
      } catch (error) {
        console.error("Error checking chatbot access:", error);
        response.chatbotAccess = false;
      }
    }

    return NextResponse.json(response, { headers: corsHeaders });
  } catch (error) {
    console.error("Login error:", error);
    return NextResponse.json(
      { success: false, error: "Internal server error" },
      { status: 500, headers: corsHeaders }
    );
  }
}

/**
 * Check if user token is valid (for SDK validation)
 */
export async function GET(request: NextRequest) {
  try {
    const userToken = request.headers.get("x-user-token");

    if (!userToken) {
      return NextResponse.json(
        { valid: false, error: "No user token provided" },
        { status: 401, headers: corsHeaders }
      );
    }

    try {
      // Decode and verify token
      const decoded = Buffer.from(userToken, "base64").toString("utf-8");
      const userInfo = JSON.parse(decoded);

      const emailHash = crypto
        .createHash("sha256")
        .update(userInfo.email)
        .digest("hex");
      // Verify user still exists
      const user = await db.user.findFirst({
        where: {
          id: userInfo.userId,
          emailHash,
        },
        select: {
          id: true,
          name: true,
          email: true,
        },
      });

      if (!user) {
        return NextResponse.json(
          { valid: false, error: "User not found" },
          { status: 401, headers: corsHeaders }
        );
      }

      return NextResponse.json(
        {
          valid: true,
          user: {
            id: user.id,
            name: user.name,
            email: user.email,
          },
        },
        { headers: corsHeaders }
      );
    } catch (error) {
      return NextResponse.json(
        { valid: false, error: "Invalid token format" },
        { status: 401, headers: corsHeaders }
      );
    }
  } catch (error) {
    console.error("Token validation error:", error);
    return NextResponse.json(
      { valid: false, error: "Internal server error" },
      { status: 500, headers: corsHeaders }
    );
  }
}
