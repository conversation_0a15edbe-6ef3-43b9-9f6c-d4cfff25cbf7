"use client";

import React, { useEffect, useRef, useState } from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTit<PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Copy, Download, ZoomIn, ZoomOut, RotateCcw } from "lucide-react";
import { useTranslatedToast } from "@/hooks/use-translated-toast";
import { MermaidDiagram } from "@/lib/utils/chart-parser";

interface MermaidRendererProps {
  diagram: MermaidDiagram;
  className?: string;
  initialHeight?: number;
  resizable?: boolean;
}

/**
 * Sanitizes diagram content to handle special characters and parsing issues
 */
const sanitizeDiagramContent = (content: string): string => {
  return (
    content
      // Replace problematic characters in node labels
      .replace(/\[([^\]]*)\([^\)]*\)([^\]]*)\]/g, "[$1_$2]") // Remove parentheses in square brackets
      .replace(/\[([^\]]*)\s+\(([^\)]*)\)([^\]]*)\]/g, "[$1_$2$3]") // Handle spaces before parentheses
      // Escape special characters that might cause parsing issues
      .replace(/\[([^\]]*)\s*-\s*([^\]]*)\]/g, "[$1_$2]") // Replace dashes in labels
      // Clean up any double spaces
      .replace(/\s+/g, " ")
      .trim()
  );
};

/**
 * Renders Mermaid diagrams with interactive controls
 */
export const MermaidRenderer: React.FC<MermaidRendererProps> = ({
  diagram,
  className,
  initialHeight = 400,
  resizable = true,
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const resizeRef = useRef<HTMLDivElement>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [mermaidId] = useState(
    () => `mermaid-${Math.random().toString(36).substr(2, 9)}`
  );
  const [zoom, setZoom] = useState(1);
  const [height, setHeight] = useState(initialHeight);
  const [isResizing, setIsResizing] = useState(false);
  const toast = useTranslatedToast();

  // Handle mouse events for resizing
  const handleMouseDown = (e: React.MouseEvent) => {
    if (!resizable) return;
    setIsResizing(true);
    e.preventDefault();
  };

  const handleMouseMove = React.useCallback(
    (e: MouseEvent) => {
      if (!isResizing || !resizable) return;
      const rect = containerRef.current?.getBoundingClientRect();
      if (rect) {
        const newHeight = Math.max(200, e.clientY - rect.top);
        setHeight(newHeight);
      }
    },
    [isResizing, resizable]
  );

  const handleMouseUp = React.useCallback(() => {
    setIsResizing(false);
  }, []);

  // Add global mouse event listeners for resizing
  React.useEffect(() => {
    if (isResizing) {
      document.addEventListener("mousemove", handleMouseMove);
      document.addEventListener("mouseup", handleMouseUp);
      return () => {
        document.removeEventListener("mousemove", handleMouseMove);
        document.removeEventListener("mouseup", handleMouseUp);
      };
    }
  }, [isResizing, handleMouseMove, handleMouseUp]);

  useEffect(() => {
    let mounted = true;

    const renderDiagram = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Dynamically import mermaid to avoid SSR issues
        const mermaid = (await import("mermaid")).default;

        // Configure mermaid with enhanced error handling
        mermaid.initialize({
          startOnLoad: false,
          theme: diagram.config?.theme || "default",
          securityLevel: "loose",
          fontFamily: "inherit",
          fontSize: 14,
          logLevel: "error",
          flowchart: {
            useMaxWidth: true,
            htmlLabels: true,
            curve: "basis",
            padding: 20,
          },
          sequence: {
            useMaxWidth: true,
            wrap: true,
            padding: 20,
          },
          gantt: {
            useMaxWidth: true,
            padding: 20,
          },
          journey: {
            useMaxWidth: true,
            padding: 20,
          },
          pie: {
            useMaxWidth: true,
            padding: 20,
          },
          graph: {
            useMaxWidth: true,
            padding: 20,
          },
        });

        if (!mounted) return;

        // Sanitize diagram content to handle special characters
        const sanitizedContent = sanitizeDiagramContent(diagram.content);

        // Validate the diagram syntax with better error handling
        try {
          const isValid = await mermaid.parse(sanitizedContent);
          if (!isValid) {
            throw new Error("Invalid Mermaid diagram syntax");
          }
        } catch (parseError) {
          console.warn(
            "Mermaid parse validation failed, attempting render anyway:",
            parseError
          );
        }

        // Render the diagram with fallback handling
        const { svg } = await mermaid.render(mermaidId, sanitizedContent);

        if (!mounted) return;

        if (containerRef.current) {
          containerRef.current.innerHTML = svg;

          // Apply zoom transform
          const svgElement = containerRef.current.querySelector("svg");
          if (svgElement) {
            svgElement.style.transform = `scale(${zoom})`;
            svgElement.style.transformOrigin = "top left";
            svgElement.style.transition = "transform 0.2s ease";
          }
        }

        setIsLoading(false);
      } catch (err) {
        console.error("Mermaid rendering error:", err);
        if (mounted) {
          setError(
            err instanceof Error ? err.message : "Failed to render diagram"
          );
          setIsLoading(false);
        }
      }
    };

    renderDiagram();

    return () => {
      mounted = false;
    };
  }, [diagram.content, diagram.config?.theme, mermaidId, zoom]);

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(diagram.content);
      toast.success("Diagram code copied to clipboard");
    } catch (err) {
      toast.error("Failed to copy diagram code");
    }
  };

  const handleDownload = () => {
    try {
      const svgElement = containerRef.current?.querySelector("svg");
      if (!svgElement) {
        toast.error("No diagram to download");
        return;
      }

      // Create a blob with the SVG content
      const svgData = new XMLSerializer().serializeToString(svgElement);
      const svgBlob = new Blob([svgData], {
        type: "image/svg+xml;charset=utf-8",
      });

      // Create download link
      const url = URL.createObjectURL(svgBlob);
      const link = document.createElement("a");
      link.href = url;
      link.download = `mermaid-diagram-${Date.now()}.svg`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      toast.success("Diagram downloaded");
    } catch (err) {
      toast.error("Failed to download diagram");
    }
  };

  const handleZoomIn = () => {
    setZoom((prev) => Math.min(prev + 0.2, 3));
  };

  const handleZoomOut = () => {
    setZoom((prev) => Math.max(prev - 0.2, 0.5));
  };

  const handleResetZoom = () => {
    setZoom(1);
  };

  if (error) {
    return (
      <Card className={className}>
        <CardContent className="p-4">
          <div className="text-center text-destructive">
            <p className="font-medium">Failed to render diagram</p>
            <p className="text-sm mt-1">{error}</p>
            <details className="mt-2 text-left">
              <summary className="cursor-pointer text-sm text-muted-foreground">
                Show diagram code
              </summary>
              <pre className="mt-2 p-2 bg-muted rounded text-xs overflow-auto">
                {diagram.content}
              </pre>
            </details>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      {diagram.config?.title && (
        <CardHeader className="pb-2">
          <CardTitle className="text-base">{diagram.config.title}</CardTitle>
        </CardHeader>
      )}
      <CardContent className="p-4">
        <div className="relative group">
          {/* Controls */}
          <div className="absolute top-2 right-2 flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200 z-10">
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8 bg-background/80 hover:bg-background"
              onClick={handleZoomOut}
              disabled={zoom <= 0.5}
              title="Zoom out"
            >
              <ZoomOut className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8 bg-background/80 hover:bg-background"
              onClick={handleResetZoom}
              disabled={zoom === 1}
              title="Reset zoom"
            >
              <RotateCcw className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8 bg-background/80 hover:bg-background"
              onClick={handleZoomIn}
              disabled={zoom >= 3}
              title="Zoom in"
            >
              <ZoomIn className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8 bg-background/80 hover:bg-background"
              onClick={handleDownload}
              title="Download as SVG"
            >
              <Download className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8 bg-background/80 hover:bg-background"
              onClick={handleCopy}
              title="Copy diagram code"
            >
              <Copy className="h-4 w-4" />
            </Button>
          </div>

          {/* Diagram container */}
          <div
            className="overflow-auto border rounded-lg bg-background relative"
            style={{ height: `${height}px` }}
          >
            {isLoading ? (
              <div className="flex items-center justify-center h-full">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              </div>
            ) : (
              <div
                ref={containerRef}
                className="p-4 h-full flex items-center justify-center"
                style={{
                  transformOrigin: "top left",
                  overflow: zoom > 1 ? "auto" : "visible",
                }}
              />
            )}

            {/* Resize handle */}
            {resizable && (
              <div
                className="absolute bottom-0 left-0 right-0 h-2 cursor-ns-resize bg-transparent hover:bg-border/50 transition-colors"
                onMouseDown={handleMouseDown}
                title="Drag to resize"
              />
            )}
          </div>

          {/* Zoom indicator */}
          {zoom !== 1 && (
            <div className="absolute bottom-2 left-2 bg-background/80 px-2 py-1 rounded text-xs text-muted-foreground">
              {Math.round(zoom * 100)}%
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};
