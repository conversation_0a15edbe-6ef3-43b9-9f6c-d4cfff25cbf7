import { NextRequest, NextResponse } from "next/server";
import db from "@/lib/shared-db";
import crypto from "crypto";

interface RestrictionCheckRequest {
  chatbotId: string;
  userEmail?: string;
  ipAddress?: string;
  userAgent?: string;
  domain?: string;
  country?: string;
  userId?: string;
}

interface RestrictionCheckResponse {
  allowed: boolean;
  reason?: string;
  restrictions?: any;
  rateLimitInfo?: {
    remaining: number;
    resetTime: number;
  };
}

// Helper function to hash IP address for privacy
function hashIP(ip: string): string {
  return crypto.createHash('sha256').update(ip + (process.env.IP_SALT || 'default-salt')).digest('hex');
}

// Helper function to check if an item matches any pattern in a list
function matchesPattern(item: string, patterns: string[]): boolean {
  return patterns.some(pattern => {
    // Support wildcards
    const regex = new RegExp(pattern.replace(/\*/g, '.*'), 'i');
    return regex.test(item);
  });
}

export async function POST(request: NextRequest) {
  try {
    const body: RestrictionCheckRequest = await request.json();
    const { chatbotId, userEmail, ipAddress, userAgent, domain, country, userId } = body;

    if (!chatbotId) {
      return NextResponse.json(
        { error: "Chatbot ID is required" },
        { status: 400 }
      );
    }

    // Get chatbot with restrictions
    const chatbot = await db.chatbot.findUnique({
      where: { id: chatbotId },
      select: {
        id: true,
        name: true,
        customization: true,
        allowedDomains: true,
        access: true,
        isActive: true,
      },
    });

    if (!chatbot) {
      return NextResponse.json(
        { error: "Chatbot not found" },
        { status: 404 }
      );
    }

    if (!chatbot.isActive) {
      return NextResponse.json({
        allowed: false,
        reason: "Chatbot is currently inactive",
      } as RestrictionCheckResponse);
    }

    const customization = chatbot.customization as any;
    const userRestrictions = customization?.userRestrictions || {};

    // Check domain restrictions (from allowedDomains field)
    if (domain && chatbot.allowedDomains.length > 0) {
      if (!matchesPattern(domain, chatbot.allowedDomains)) {
        return NextResponse.json({
          allowed: false,
          reason: `Domain '${domain}' is not allowed for this chatbot`,
        } as RestrictionCheckResponse);
      }
    }

    // Check blocked items
    if (ipAddress && userRestrictions.blockedIPs?.length > 0) {
      const hashedIP = hashIP(ipAddress);
      if (matchesPattern(ipAddress, userRestrictions.blockedIPs) || 
          matchesPattern(hashedIP, userRestrictions.blockedIPs)) {
        return NextResponse.json({
          allowed: false,
          reason: "Your IP address has been blocked",
        } as RestrictionCheckResponse);
      }
    }

    if (userEmail && userRestrictions.blockedEmails?.length > 0) {
      if (matchesPattern(userEmail, userRestrictions.blockedEmails)) {
        return NextResponse.json({
          allowed: false,
          reason: "Your email address has been blocked",
        } as RestrictionCheckResponse);
      }
    }

    if (userAgent && userRestrictions.blockedUserAgents?.length > 0) {
      if (matchesPattern(userAgent, userRestrictions.blockedUserAgents)) {
        return NextResponse.json({
          allowed: false,
          reason: "Your browser/client has been blocked",
        } as RestrictionCheckResponse);
      }
    }

    if (domain && userRestrictions.blockedDomains?.length > 0) {
      if (matchesPattern(domain, userRestrictions.blockedDomains)) {
        return NextResponse.json({
          allowed: false,
          reason: `Domain '${domain}' has been blocked`,
        } as RestrictionCheckResponse);
      }
    }

    // Check allowed items (if specified, only these are allowed)
    if (userRestrictions.allowedIPs?.length > 0 && ipAddress) {
      const hashedIP = hashIP(ipAddress);
      if (!matchesPattern(ipAddress, userRestrictions.allowedIPs) && 
          !matchesPattern(hashedIP, userRestrictions.allowedIPs)) {
        return NextResponse.json({
          allowed: false,
          reason: "Your IP address is not in the allowed list",
        } as RestrictionCheckResponse);
      }
    }

    if (userRestrictions.allowedEmails?.length > 0 && userEmail) {
      if (!matchesPattern(userEmail, userRestrictions.allowedEmails)) {
        return NextResponse.json({
          allowed: false,
          reason: "Your email address is not in the allowed list",
        } as RestrictionCheckResponse);
      }
    }

    if (userRestrictions.allowedDomains?.length > 0 && domain) {
      if (!matchesPattern(domain, userRestrictions.allowedDomains)) {
        return NextResponse.json({
          allowed: false,
          reason: `Domain '${domain}' is not in the allowed list`,
        } as RestrictionCheckResponse);
      }
    }

    // Check geographic restrictions
    if (country && userRestrictions.geoRestrictions) {
      const { allowedCountries, blockedCountries } = userRestrictions.geoRestrictions;
      
      if (blockedCountries?.length > 0 && blockedCountries.includes(country)) {
        return NextResponse.json({
          allowed: false,
          reason: `Access from ${country} is not allowed`,
        } as RestrictionCheckResponse);
      }

      if (allowedCountries?.length > 0 && !allowedCountries.includes(country)) {
        return NextResponse.json({
          allowed: false,
          reason: `Access is only allowed from specific countries`,
        } as RestrictionCheckResponse);
      }
    }

    // Check authentication requirement
    if (userRestrictions.requireAuth && !userId && !userEmail) {
      return NextResponse.json({
        allowed: false,
        reason: "Authentication is required to use this chatbot",
      } as RestrictionCheckResponse);
    }

    // Check rate limits
    let rateLimitInfo;
    if (userRestrictions.rateLimits) {
      const rateLimits = userRestrictions.rateLimits;
      const now = new Date();
      const userIdentifier = userId || userEmail || hashIP(ipAddress || 'unknown');

      // Check recent messages for this user
      const oneMinuteAgo = new Date(now.getTime() - 60 * 1000);
      const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
      const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);

      const [minuteCount, hourCount, dayCount] = await Promise.all([
        db.chatbotMessage.count({
          where: {
            chatbotId,
            createdAt: { gte: oneMinuteAgo },
            session: {
              OR: [
                { userId: userId },
                { user: { email: userEmail } },
                { ipAddress: hashIP(ipAddress || '') },
              ].filter(Boolean),
            },
          },
        }),
        db.chatbotMessage.count({
          where: {
            chatbotId,
            createdAt: { gte: oneHourAgo },
            session: {
              OR: [
                { userId: userId },
                { user: { email: userEmail } },
                { ipAddress: hashIP(ipAddress || '') },
              ].filter(Boolean),
            },
          },
        }),
        db.chatbotMessage.count({
          where: {
            chatbotId,
            createdAt: { gte: oneDayAgo },
            session: {
              OR: [
                { userId: userId },
                { user: { email: userEmail } },
                { ipAddress: hashIP(ipAddress || '') },
              ].filter(Boolean),
            },
          },
        }),
      ]);

      if (minuteCount >= rateLimits.messagesPerMinute) {
        return NextResponse.json({
          allowed: false,
          reason: "Rate limit exceeded: too many messages per minute",
          rateLimitInfo: {
            remaining: 0,
            resetTime: oneMinuteAgo.getTime() + 60 * 1000,
          },
        } as RestrictionCheckResponse);
      }

      if (hourCount >= rateLimits.messagesPerHour) {
        return NextResponse.json({
          allowed: false,
          reason: "Rate limit exceeded: too many messages per hour",
          rateLimitInfo: {
            remaining: 0,
            resetTime: oneHourAgo.getTime() + 60 * 60 * 1000,
          },
        } as RestrictionCheckResponse);
      }

      if (dayCount >= rateLimits.messagesPerDay) {
        return NextResponse.json({
          allowed: false,
          reason: "Rate limit exceeded: too many messages per day",
          rateLimitInfo: {
            remaining: 0,
            resetTime: oneDayAgo.getTime() + 24 * 60 * 60 * 1000,
          },
        } as RestrictionCheckResponse);
      }

      rateLimitInfo = {
        remaining: Math.min(
          rateLimits.messagesPerMinute - minuteCount,
          rateLimits.messagesPerHour - hourCount,
          rateLimits.messagesPerDay - dayCount
        ),
        resetTime: oneMinuteAgo.getTime() + 60 * 1000,
      };
    }

    // All checks passed
    return NextResponse.json({
      allowed: true,
      restrictions: userRestrictions,
      rateLimitInfo,
    } as RestrictionCheckResponse);

  } catch (error) {
    console.error("Error checking user restrictions:", error);
    return NextResponse.json(
      { error: "Failed to check user restrictions" },
      { status: 500 }
    );
  }
}
