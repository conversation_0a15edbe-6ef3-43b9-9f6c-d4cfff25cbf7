import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/next-auth";
import db from "@/lib/shared-db";

export async function GET(request: Request) {
  const session = await getServerSession(authOptions);

  if (!session) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const { searchParams } = new URL(request.url);

    // Get tenant filter
    const tenantId = searchParams.get("tenantId");

    // Get date range from query parameters
    let startDate: Date;
    let endDate = new Date();

    // Check if custom date range is provided
    const fromDate = searchParams.get("from");
    const toDate = searchParams.get("to");

    if (fromDate && toDate) {
      // Use custom date range
      startDate = new Date(fromDate);
      endDate = new Date(toDate);
    } else {
      // Use period-based date range
      const period = searchParams.get("period") || "7days";

      // Parse the period
      const match = period.match(/^(\d+)(days|months)$/);
      if (!match) {
        return NextResponse.json(
          {
            error:
              "Invalid period format. Use format like '7days' or '6months'",
          },
          { status: 400 }
        );
      }

      const amount = parseInt(match[1], 10);
      const unit = match[2];

      // Calculate the start date
      startDate = new Date();
      if (unit === "days") {
        startDate.setDate(startDate.getDate() - amount);
      } else if (unit === "months") {
        startDate.setMonth(startDate.getMonth() - amount);
      }
    }

    // Base where clause for API requests
    const apiRequestsWhere: any = {
      timestamp: {
        gte: startDate,
        lte: endDate,
      },
    };

    // Add tenant filter if provided
    if (tenantId) {
      apiRequestsWhere.tenantId = tenantId;
    }

    // Get recent API requests with error handling
    let recentRequests = [];
    try {
      // First check if the APIRequest model exists and has records
      const apiRequestCount = await db.aPIRequest.count();

      if (apiRequestCount > 0) {
        // Only try to fetch if there are records
        try {
          recentRequests = await db.aPIRequest.findMany({
            where: apiRequestsWhere,
            select: {
              id: true,
              endpoint: true,
              method: true,
              statusCode: true,
              timestamp: true,
              duration: true,
              success: true,
              errorMessage: true,
              tenantId: true,
              userId: true,
              tenant: {
                select: {
                  name: true,
                },
              },
              user: {
                select: {
                  name: true,
                  email: true,
                },
              },
            },
            orderBy: {
              timestamp: "desc",
            },
            take: 10,
          });
        } catch (innerError) {
          console.error("Error fetching API request details:", innerError);
          recentRequests = [];
        }
      } else {
        console.log("No API request records found");
        recentRequests = [];
      }
    } catch (error) {
      console.error("Error checking API requests:", error);
      // Provide fallback data
      recentRequests = [];
    }

    // Format the recent requests
    const formattedRequests = recentRequests.map((request: any) => ({
      id: request.id,
      timestamp: request.timestamp,
      endpoint: request.endpoint,
      method: request.method,
      statusCode: request.statusCode,
      success: request.success,
      latencyMs: request.latencyMs,
      tenantId: request.tenantId,
      tenantName: request.tenant?.name || "Unknown",
      userId: request.userId,
      userName: request.user?.name || "Unknown",
      userEmail: request.user?.email || "Unknown",
    }));

    // Base where clause for user logins
    const userLoginsWhere: any = {
      lastLogin: {
        gte: startDate,
        lte: endDate,
      },
    };

    // Add tenant filter if provided
    if (tenantId) {
      userLoginsWhere.Membership = {
        some: {
          tenantId,
        },
      };
    }

    // Get recent logins (using sessions as a proxy)
    let recentLogins = [];
    try {
      const recentSessions = await db.session.findMany({
        orderBy: {
          expires: "desc",
        },
        take: 10,
        include: {
          user: {
            include: {
              Membership: {
                include: {
                  tenant: true,
                },
              },
            },
          },
        },
      });

      // Extract user data from sessions and format it
      recentLogins = recentSessions.map((session) => {
        const user = session.user;
        return {
          ...user,
          lastLogin: session.expires, // Use session expiry as a proxy for last login
          Membership: user.membership, // Map to expected field name for backward compatibility
        };
      });
    } catch (error) {
      console.error("Error fetching recent logins:", error);
      // Provide fallback data
      recentLogins = [];
    }

    // Format the recent logins
    const formattedLogins = recentLogins.map((user: any) => ({
      id: user.id,
      name: user.name,
      email: user.email,
      lastLogin: user.lastLogin,
      tenantId: user.Membership[0]?.tenantId,
      tenantName: user.Membership[0]?.tenant?.name || "Unknown",
    }));

    // Base where clause for tenants
    const tenantsWhere: any = {
      createdAt: {
        gte: startDate,
        lte: endDate,
      },
    };

    // Add tenant filter if provided
    if (tenantId) {
      tenantsWhere.id = tenantId;
    }

    // Get recent tenants with error handling
    let recentTenants = [];
    try {
      recentTenants = await db.tenant.findMany({
        where: tenantsWhere,
        orderBy: {
          createdAt: "desc",
        },
        take: 5,
        include: {
          Subscription: {
            include: {
              plan: true,
            },
          },
        },
      });
    } catch (error) {
      console.error("Error fetching recent tenants:", error);
      // Provide fallback data
      recentTenants = [];
    }

    // Format the recent tenants
    const formattedTenants = recentTenants.map((tenant: any) => ({
      id: tenant.id,
      name: tenant.name,
      createdAt: tenant.createdAt,
      plan: tenant.Subscription[0]?.plan?.name || "No Plan",
      status: tenant.Subscription[0]?.isActive ? "active" : "inactive",
    }));

    return NextResponse.json({
      recentRequests: formattedRequests,
      recentLogins: formattedLogins,
      recentTenants: formattedTenants,
    });
  } catch (error) {
    console.error("Error fetching dashboard activity:", error);
    return NextResponse.json(
      { error: "Failed to fetch dashboard activity" },
      { status: 500 }
    );
  }
}
