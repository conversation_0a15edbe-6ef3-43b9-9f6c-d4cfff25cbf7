import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/next-auth";
import { redirect } from "next/navigation";
import { UsersTableClient } from "./users-table-client";
import db from "@/lib/shared-db";
import { Suspense } from "react";

// Types for our data structures
interface User {
  id: string;
  name: string;
  email: string;
  tenantName: string;
  tenantId: string;
  role: string;
  lastActive: string;
  createdAt: string;
}

interface UsersByTenant {
  name: string;
  users: number;
}

interface UsersByRole {
  name: string;
  users: number;
}

interface UsersData {
  users: User[];
  usersByTenant: UsersByTenant[];
  usersByRole: UsersByRole[];
  allTenants: [];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  searchQuery: string;
}

// Server-side data fetching function
async function fetchUsersData(
  searchQuery: string = "",
  page: number = 1,
  limit: number = 5,
  tenantId: string = ""
): Promise<UsersData> {
  try {
    const users = await db.user.findMany({
      include: {
        membership: {
          include: {
            tenant: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    const allTenants = await db.tenant.findMany();

    let transformedUsers = users.flatMap((user: any) =>
      user.membership.map((membership: any) => ({
        id: user.id,
        name: user.name || "Unknown",
        email: user.email || "Unknown",
        tenantName: membership.tenant.name,
        tenantId: membership.tenant.id,
        role: membership.role,
        lastActive: user.updatedAt.toISOString(),
        createdAt: user.createdAt.toISOString(),
      }))
    );

    // Apply search filter
    if (searchQuery && searchQuery.trim()) {
      const searchTerm = searchQuery.toLowerCase().trim();
      transformedUsers = transformedUsers.filter((user: any) => {
        const nameMatch = user.name.toLowerCase().includes(searchTerm);
        const emailMatch = user.email.toLowerCase().includes(searchTerm);
        const tenantMatch = user.tenantName.toLowerCase().includes(searchTerm);
        const roleMatch = user.role.toLowerCase().includes(searchTerm);
        return nameMatch || emailMatch || tenantMatch || roleMatch;
      });
    }

    // Filter by tenantId if provided
    if (tenantId) {
      transformedUsers = transformedUsers.filter((user: any) => user.tenantId === tenantId);
    }

    const total = transformedUsers.length;
    const startIndex = (page - 1) * limit;
    const paginatedUsers = transformedUsers.slice(startIndex, startIndex + limit);

    // Chart data
    const usersByTenant = await db.membership.groupBy({
      by: ["tenantId"],
      _count: {
        userId: true,
      },
    });

    const usersByRole = await db.membership.groupBy({
      by: ["role"],
      _count: {
        userId: true,
      },
    });

    const tenantMap = new Map(
      allTenants.map((t) => [t.id, t.name])
    );

    const tenantStats = usersByTenant.map((item: any) => ({
      name: tenantMap.get(item.tenantId) || "Unknown",
      users: item._count.userId,
    }));

    const roleStats = usersByRole.map((item: any) => ({
      name: item.role,
      users: item._count.userId,
    }));

    const totalPages = Math.ceil(total / limit);

    return {
      users: paginatedUsers,
      usersByTenant: tenantStats,
      usersByRole: roleStats,
      allTenants,
      pagination: {
        page,
        limit,
        total,
        totalPages,
      },
      searchQuery,
    };
  } catch (error) {
    console.error("Error fetching users data:", error);
    return {
      users: [],
      usersByTenant: [],
      usersByRole: [],
      allTenants: [],
      pagination: {
        page: 1,
        limit: 5,
        total: 0,
        totalPages: 0,
      },
      searchQuery: "",
    };
  }
}

// Server page
export default async function UsersPage({
  searchParams,
}: {
  searchParams: { search?: string; page?: string; limit?: string; tenant?: string };
}) {
  const session = await getServerSession(authOptions);
  if (!session) {
    redirect("/auth/signin");
  }

  const searchQuery = searchParams.search || "";
  const page = parseInt(searchParams.page || "1", 10);
  const limit = parseInt(searchParams.limit || "5", 10);
  const tenantId = searchParams.tenant || "";

  const validPage = Math.max(1, page);
  const validLimit = Math.min(Math.max(1, limit), 100);

  const usersData = await fetchUsersData(searchQuery, validPage, validLimit, tenantId);

  return (
    <Suspense fallback={<div className="p-6">Loading users...</div>}>
      <UsersTableClient data={usersData} />
    </Suspense>
  );
}
