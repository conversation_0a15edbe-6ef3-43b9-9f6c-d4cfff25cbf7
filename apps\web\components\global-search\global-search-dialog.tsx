"use client";

import React, { useState, useEffect, useCallback } from "react";
import {
  Search,
  X,
  Filter,
  Clock,
  FileText,
  MessageCircle,
  Folder,
  Building2,
} from "lucide-react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, Ta<PERSON>List, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Skeleton } from "@/components/ui/skeleton";
import { useDebounce } from "@/hooks/use-debounce";
import { useLanguage } from "@/lib/language-context";
import {
  performGlobalSearch,
  SearchResult,
  GlobalSearchResponse,
  getSearchTypeDisplayName,
  formatSearchResultDate,
  highlightSearchTerm,
  truncateText,
} from "@/services/src/global-search";

interface GlobalSearchDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  tenantId: string;
}

const SearchResultItem: React.FC<{
  result: SearchResult;
  searchTerm: string;
  onSelect: (result: SearchResult) => void;
}> = ({ result, searchTerm, onSelect }) => {
  const getIcon = () => {
    switch (result.type) {
      case "file":
        return <FileText className="h-4 w-4" />;
      case "page":
        return <FileText className="h-4 w-4" />;
      case "folder":
        return <Folder className="h-4 w-4" />;
      case "workspace":
        return <Building2 className="h-4 w-4" />;
      case "chat":
        return <MessageCircle className="h-4 w-4" />;
      case "message":
        return <MessageCircle className="h-4 w-4" />;
      default:
        return <FileText className="h-4 w-4" />;
    }
  };

  const handleClick = () => {
    onSelect(result);
  };

  return (
    <div
      className="p-4 sm:p-3 hover:bg-muted/50 cursor-pointer border-b border-border last:border-b-0 transition-colors"
      onClick={handleClick}
    >
      <div className="flex items-start gap-3">
        <div className="flex-shrink-0 mt-1 text-muted-foreground">
          {getIcon()}
        </div>
        <div className="flex-1 min-w-0">
          <div className="flex items-start sm:items-center gap-2 mb-2 sm:mb-1 flex-col sm:flex-row">
            <h4
              className="font-medium text-sm truncate w-full sm:flex-1"
              dangerouslySetInnerHTML={{
                __html: highlightSearchTerm(result.title, searchTerm),
              }}
            />
            <Badge
              variant="secondary"
              className="text-xs self-start sm:self-auto"
            >
              {getSearchTypeDisplayName(result.type)}
            </Badge>
          </div>

          {result.snippet && (
            <p
              className="text-sm text-muted-foreground mb-3 sm:mb-2 line-clamp-2 sm:line-clamp-1"
              dangerouslySetInnerHTML={{
                __html: highlightSearchTerm(
                  truncateText(result.snippet, 120),
                  searchTerm
                ),
              }}
            />
          )}

          <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4 text-xs text-muted-foreground">
            {result.workspace_name && (
              <span className="flex items-center gap-1">
                <Building2 className="h-3 w-3" />
                <span className="truncate">{result.workspace_name}</span>
              </span>
            )}
            {result.updated_at && (
              <span className="flex items-center gap-1">
                <Clock className="h-3 w-3" />
                {formatSearchResultDate(result.updated_at)}
              </span>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export const GlobalSearchDialog: React.FC<GlobalSearchDialogProps> = ({
  open,
  onOpenChange,
  tenantId,
}) => {
  const { t } = useLanguage();
  const [searchTerm, setSearchTerm] = useState("");
  const [searchResults, setSearchResults] =
    useState<GlobalSearchResponse | null>(null);
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState("all");

  const debouncedSearchTerm = useDebounce(searchTerm, 300);

  const performSearch = useCallback(
    async (query: string) => {
      if (!query.trim()) {
        setSearchResults(null);
        return;
      }

      setLoading(true);
      try {
        const response = await performGlobalSearch({
          query: query.trim(),
          tenantId,
          limit: 50,
        });
        setSearchResults(response);
      } catch (error) {
        console.error("Search error:", error);
        setSearchResults({
          results: [],
          total_count: 0,
          categories: {},
          has_more: false,
          search_time_ms: 0,
        });
      } finally {
        setLoading(false);
      }
    },
    [tenantId]
  );

  useEffect(() => {
    if (debouncedSearchTerm) {
      performSearch(debouncedSearchTerm);
    } else {
      setSearchResults(null);
    }
  }, [debouncedSearchTerm, performSearch]);

  const handleResultSelect = (result: SearchResult) => {
    // Handle navigation based on result type
    switch (result.type) {
      case "file":
        if (result.workspace_id) {
          window.open(`/workspace/${result?.slug}/file/${result.id}`);
        }
        break;
      case "page":
        if (result.workspace_id) {
          window.open(`/workspace/${result?.slug}/page/${result.id}`);
        }
        break;
      case "folder":
        if (result.workspace_id) {
          window.open(`/workspace/${result?.slug}/folder/${result.id}`);
        }
        break;
      case "workspace":
        window.open(`/workspace/${result.slug}/pages`);
        break;
      case "chat":
        window.open(`/ask-ai/${result.id}`);
        break;
      case "message":
        if (result.metadata?.chat_id) {
          window.open(`/ask-ai/${result.metadata.chat_id}`);
        }
        break;
    }
    onOpenChange(false);
  };

  const getFilteredResults = () => {
    if (!searchResults) return [];

    if (activeTab === "all") {
      return searchResults.results;
    }

    return searchResults.results.filter((result) => result.type === activeTab);
  };

  const filteredResults = getFilteredResults();

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-3xl max-h-[90vh] sm:max-h-[80vh] p-0 mx-2 sm:mx-auto w-[calc(100vw-1rem)] sm:w-full">
        <DialogHeader className="p-4 sm:p-6 pb-3 sm:pb-0">
          <DialogTitle className="flex items-center gap-2 text-lg sm:text-xl">
            <Search className="h-5 w-5" />
            <span className="hidden sm:inline">{t("globalSearch.title")}</span>
            <span className="sm:hidden">{t("globalSearch.titleShort")}</span>
          </DialogTitle>
        </DialogHeader>

        <div className="px-4 sm:px-6">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder={t("globalSearch.placeholder")}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-9 pr-9 h-11 sm:h-10 text-base sm:text-sm"
              autoFocus
            />
            {searchTerm && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setSearchTerm("")}
                className="absolute right-1 top-1/2 transform -translate-y-1/2 h-8 w-8 sm:h-7 sm:w-7 p-0"
              >
                <X className="h-4 w-4 sm:h-3 sm:w-3" />
              </Button>
            )}
          </div>
        </div>

        {searchResults && (
          <div className="px-4 sm:px-6">
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid w-full grid-cols-2 sm:grid-cols-4 lg:grid-cols-7 h-auto p-1">
                <TabsTrigger
                  value="all"
                  className="text-xs sm:text-sm py-2 px-2 sm:px-3"
                >
                  <span className="hidden sm:inline">
                    {t("common.all")} ({searchResults.total_count})
                  </span>
                  <span className="sm:hidden">{t("common.all")}</span>
                </TabsTrigger>
                {Object.entries(searchResults.categories).map(
                  ([type, count]) => (
                    <TabsTrigger
                      key={type}
                      value={type}
                      className="text-xs sm:text-sm py-2 px-2 sm:px-3"
                    >
                      <span className="hidden sm:inline">
                        {getSearchTypeDisplayName(type)} ({count})
                      </span>
                      <span className="sm:hidden">
                        {getSearchTypeDisplayName(type)}
                      </span>
                    </TabsTrigger>
                  )
                )}
              </TabsList>
            </Tabs>
          </div>
        )}

        <ScrollArea className="flex-1 max-h-[50vh] sm:max-h-96">
          {loading ? (
            <div className="p-4 sm:p-6 space-y-4">
              {Array.from({ length: 5 }).map((_, i) => (
                <div key={i} className="space-y-2">
                  <Skeleton className="h-4 w-3/4" />
                  <Skeleton className="h-3 w-1/2" />
                </div>
              ))}
            </div>
          ) : filteredResults.length > 0 ? (
            <div>
              {filteredResults.map((result) => (
                <SearchResultItem
                  key={`${result.type}-${result.id}`}
                  result={result}
                  searchTerm={searchTerm}
                  onSelect={handleResultSelect}
                />
              ))}
            </div>
          ) : searchTerm && !loading ? (
            <div className="p-4 sm:p-6 text-center text-muted-foreground">
              <Search className="h-10 w-10 sm:h-12 sm:w-12 mx-auto mb-4 opacity-50" />
              <p className="text-sm sm:text-base">
                {t("globalSearch.noResults", { searchTerm })}
              </p>
            </div>
          ) : (
            <div className="p-4 sm:p-6 text-center text-muted-foreground">
              <Search className="h-10 w-10 sm:h-12 sm:w-12 mx-auto mb-4 opacity-50" />
              <p className="text-sm sm:text-base">{t("globalSearch.startTyping")}</p>
            </div>
          )}
        </ScrollArea>

        {searchResults && searchResults.search_time_ms > 0 && (
          <div className="px-4 sm:px-6 py-3 border-t border-border text-xs text-muted-foreground">
            <span className="hidden sm:inline">
              Found {searchResults.total_count} results in{" "}
              {searchResults.search_time_ms}ms
            </span>
            <span className="sm:hidden">
              {searchResults.total_count} results
            </span>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
};
