import { NextResponse } from "next/server";
import db from "@/lib/shared-db";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/next-auth";

export async function POST(req: Request) {
  try {
    const session: any = await getServerSession(authOptions);
    if (!session) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const { title, tenantId } = await req.json();

    const chat = await db.chat.create({
      data: {
        title,
        userId: session?.userId,
        tenantId,
      },
    });

    return NextResponse.json({ chat }, { status: 201 });
  } catch (error) {
    console.error("[CHAT_POST]", error);
    return NextResponse.json({ error: "Internal Error" }, { status: 500 });
  }
}

export async function GET(req: Request) {
  const { searchParams } = new URL(req.url);
  const id = searchParams.get("id");
  const userId = searchParams.get("userId");
  const tenantId = searchParams.get("tenantId");
  try {
    if (id) {
      const chat = await db.chat.findUnique({
        where: {
          id,
          userId,
          tenantId,
        },
        include: {
          messages: {
            orderBy: {
              createdAt: "asc",
            },
          },
        },
      });
      const ids =
        chat?.messages?.map(
          (message: any) =>
            message?.sources?.map(
              (source: any) => source?.metadata?.workspace_id
            )
        ) || [];

      const workspaceIds = [...new Set(ids?.flat(2)?.filter(Boolean))];
      const workspaces = await db.workspace.findMany({
        where: {
          id: {
            in: workspaceIds,
          },
        },
      });
      const chats = {
        ...chat,
        messages: chat?.messages?.map((message: any) => ({
          ...message,
          sources: message?.sources?.map((source: any) => ({
            ...source,
            metadata: {
              ...source?.metadata,
              workspace: workspaces.find(
                (workspace: any) =>
                  workspace.id === source?.metadata?.workspace_id
              ),
            },
          })),
        })),
      };

      if (!chats) {
        return NextResponse.json({ error: "Chat not found" }, { status: 404 });
      }
      return NextResponse.json({ chat: chats }, { status: 200 });
    }

    const chats = await db.chat.findMany({
      where: {
        userId,
        tenantId,
      },
      include: {
        messages: {
          orderBy: {
            createdAt: "desc",
          },
          take: 1,
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    if (!chats) {
      return NextResponse.json({ error: "Chat not found" }, { status: 404 });
    }
    const chat = chats.filter((chat: any) => chat?.groupId == null);
    return NextResponse.json({ chat }, { status: 200 });
  } catch (error) {
    console.error("[CHAT_GET]", error);
    return NextResponse.json({ error: "Internal Error" }, { status: 500 });
  }
}

export async function DELETE(
  req: Request,
  { params }: { params: { chatId: string } }
) {
  try {
    const session: any = await getServerSession(authOptions);
    if (!session) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    // Use a transaction to ensure all operations succeed or fail together
    await db.$transaction(async (tx) => {
      // Delete comments on messages in this chat
      const deletedComments = await tx.comment.deleteMany({
        where: {
          message: {
            chatId: params.chatId,
          },
        },
      });

      // Delete notifications related to this chat
      const deletedNotifications = await tx.notification.deleteMany({
        where: {
          chatId: params.chatId,
        },
      });

      // Delete thread shares
      const deletedThreadShares = await tx.threadShare.deleteMany({
        where: {
          chatId: params.chatId,
        },
      });

      // First, delete regenerated messages (messages that have originalMessageId)
      // These need to be deleted first to avoid foreign key constraint violations
      const deletedRegeneratedMessages = await tx.message.deleteMany({
        where: {
          chatId: params.chatId,
          originalMessageId: {
            not: null,
          },
        },
      });

      // Then delete the original messages (messages without originalMessageId)
      const deletedOriginalMessages = await tx.message.deleteMany({
        where: {
          chatId: params.chatId,
          originalMessageId: null,
        },
      });

      // Delete the chat
      await tx.chat.delete({
        where: {
          id: params.chatId,
          userId: session?.userId,
        },
      });
    });

    return NextResponse.json(
      { message: "Chat deleted successfully" },
      { status: 200 }
    );
  } catch (error) {
    console.error("[CHAT_DELETE]", error);

    // Provide more specific error messages for common issues
    if (error.code === 'P2014') {
      return NextResponse.json(
        { error: "Cannot delete chat due to database constraints. Please try again." },
        { status: 500 }
      );
    }

    if (error.code === 'P2025') {
      return NextResponse.json(
        { error: "Chat not found or you don't have permission to delete it." },
        { status: 404 }
      );
    }

    return NextResponse.json({ error: "Internal Error" }, { status: 500 });
  }
}

export async function PUT(
  req: Request,
  { params }: { params: { chatId: string } }
) {
  try {
    const session: any = await getServerSession(authOptions);
    if (!session) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const { title, groupId } = await req.json();
    const payload: any = {};
    if (title) {
      payload.title = title;
    }
    if (groupId !== undefined) {
      payload.groupId = groupId;
    }
    const chat = await db.chat.update({
      where: {
        id: params.chatId,
        userId: session?.userId,
      },
      data: {
        ...payload,
      },
    });

    return NextResponse.json(chat);
  } catch (error) {
    console.error("[CHAT_PATCH]", error);
    return NextResponse.json({ error: "Internal Error" }, { status: 500 });
  }
}
