import { redirect } from "next/navigation";
import { SidebarInset, SidebarProvider } from "@/components/layouts/sidebar";
import { MemberAppSidebar } from "@/components/layouts/member-sidebar";
import { SiteHeader } from "@/components/layouts/site-header";

import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/next-auth";
import { cookies } from "next/headers";
import { QuickAskWrapper } from "@/components/quick-ask";
import { GlobalSearchProvider } from "@/components/global-search";
import { ChangelogProvider } from "@/components/changelog/changelog-provider";
import db from "@/lib/shared-db";
import { ThemeConfigProvider } from "@/lib/theme-config-context";
import { DynamicFaviconManager } from "@/components/theme/dynamic-favicon-manager";
import { RealTimeThemeNotification } from "@/components/theme/real-time-theme-notification";

export default async function HomePage({ children }: any) {
  const session: any = await getServerSession(authOptions);

  if (!session) {
    return redirect("/sign-in");
  } else if (!session?.memberships || session?.memberships?.length === 0) {
    return redirect("/onboarding");
  }
  const tenantId =
    cookies().get("currentOrganizationId")?.value ??
    session?.memberships[0]?.tenant?.id;
  const chatHistory = [
    {
      name: "What is Reactjs",
      url: "#",
    },
    {
      name: "What is Nextjs",
      url: "#",
    },
    {
      name: "Status: InProgress. Time: 60.8694658(s)",
      url: "#",
    },
    {
      name: "Accounting search information",
      url: "#",
    },
    {
      name: "What is Reactjs",
      url: "#",
    },
    {
      name: "What is Reactjs",
      url: "#",
    },
    {
      name: "What is Nextjs",
      url: "#",
    },
    {
      name: "Status: InProgress. Time: 60.8694658(s)",
      url: "#",
    },
    {
      name: "Accounting search information",
      url: "#",
    },
    {
      name: "What is Reactjs",
      url: "#",
    },
    {
      name: "What is Reactjs",
      url: "#",
    },
    {
      name: "What is Nextjs",
      url: "#",
    },
    {
      name: "Status: InProgress. Time: 60.8694658(s)",
      url: "#",
    },
    {
      name: "Accounting search information",
      url: "#",
    },
    {
      name: "What is Reactjs",
      url: "#",
    },
  ];

  // Fetch theme configuration directly from database for server-side rendering only if partner console is enabled
  const isPartnerConsole = process.env.NEXT_PUBLIC_IS_PARTNER_CONSOLE === "1";
  const themeConfigDetails = isPartnerConsole
    ? await db.themeConfiguration.findUnique({
        where: { tenantId: tenantId },
      })
    : null;

  return (
    <ThemeConfigProvider tenantId={tenantId} initialConfig={themeConfigDetails || undefined}>
      <RealTimeThemeNotification>
        <DynamicFaviconManager themeConfig={themeConfigDetails} />
        <GlobalSearchProvider tenantId={tenantId}>
          <ChangelogProvider tenantId={tenantId}>
            <QuickAskWrapper>
          <div className="[--header-height:calc(theme(spacing.14))] h-screen flex flex-col">
            <SidebarProvider className="h-full flex flex-col">
              <SiteHeader />
              <div className="flex flex-1 overflow-hidden">
                <MemberAppSidebar chatHistory={chatHistory} session={session} />
                <SidebarInset className="overflow-y-auto flex-1"> {children}</SidebarInset>
              </div>
            </SidebarProvider>
          </div>
          </QuickAskWrapper>
        </ChangelogProvider>
      </GlobalSearchProvider>
      </RealTimeThemeNotification>
    </ThemeConfigProvider>
  );
}
