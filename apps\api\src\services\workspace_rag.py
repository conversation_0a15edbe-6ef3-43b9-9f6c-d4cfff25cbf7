import os
from typing import Dict, List, Optional, Any
import logging
import json
from bson import ObjectId
from src.agents.rag_agent import AdvancedRAGAgent
from src.services.usage_tracker import UsageTracker
from pymongo import MongoClient
from fastapi.responses import StreamingResponse
import datetime

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
)
logger = logging.getLogger(__name__)


# Import the shared connection pool for better performance
try:
    from src.agents.tools.vector_search_tool import mongo_pool
    logger.info("Successfully imported shared MongoDB connection pool")
except ImportError:
    logger.warning("Could not import shared connection pool, will use direct connections")
    mongo_pool = None

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)
import json


class WorkspaceRAGManager:
    """A manager class that handles workspace access control for RAG agents.

    Ensures users can only access documents from workspaces they have permission to access.
    """

    def __init__(self, db_client=None):
        """Initialize the workspace RAG manager.

        Args:
            db_client: Database client for checking workspace access (Prisma client or similar)
        """

        self.db_client = db_client
        self.workspace_agents = {}  # Cache of RAG agents by workspace_id
        self.agents = {}  # Cache of multi-workspace agents
        self.usage_tracker = UsageTracker(db_client)  # Initialize usage tracker

    async def get_user_workspaces(self, user_id: str,tenant_id:str) -> List[str]:
        """Get all workspaces that a user has access to.

        Args:
            user_id: The ID of the user

        Returns:
            List of workspace IDs
        """
        if self.db_client is None:
            logger.error("Database client not initialized")
            return []
        try:
            workspace_cursor = self.db_client.Workspace.find(
                {"tenantId": ObjectId(tenant_id)},
                {"_id": 1}
            )
            workspace_ids = await workspace_cursor.to_list(length=None)
            # Query workspaces where user is a member
            cursor = self.db_client.WorkspaceMember.find(
                {
                    "userId": ObjectId(user_id),
                    "workspaceId": {"$in": [workspace['_id'] for workspace in workspace_ids]}
                },
                {"_id": 1, "workspaceId": 1}
            )

            workspaces = await cursor.to_list(length=None)


            # Convert ObjectId to string before returning
            return [str(workspace["workspaceId"]) for workspace in workspaces]
        except Exception as e:
            logger.error(f"Error fetching user workspaces: {e}")
            return []

    async def check_workspace_access(self, user_id: str, workspace_id: str,tenant_id:str) -> bool:
        """Check if a user has access to a specific workspace.

        Args:
            user_id: The ID of the user
            workspace_id: The ID of the workspace

        Returns:
            Boolean indicating if the user has access
        """
        if self.db_client is None:
            logger.error("Database client not initialized")
            return False

        try:
            member = await self.db_client.Membership.find_one(
                {
                    "userId": ObjectId(user_id),
                    "tenantId": ObjectId(tenant_id)
                }
            )
            if member.get("role") in ["ADMIN", "OWNER"]:
                return True
            # Check if user is a member of the workspace
            workspace_member = await self.db_client.WorkspaceMember.find_one(
                {
                    "userId": ObjectId(user_id),
                    "workspaceId": ObjectId(workspace_id)
                }
            )
            return workspace_member is not None
        except Exception as e:
            logger.error(f"Error checking workspace access: {e}")
            return False

    async def get_workspace_agent(self, workspace_id: str, tenant_id: str) -> Optional[AdvancedRAGAgent]:
        """Get or create a RAG agent for a specific workspace.

        Args:
            workspace_id: The ID of the workspace
            config: Configuration for the RAG agent if it needs to be created

        Returns:
            AdvancedRAGAgent for the workspace or None if error
        """
        # Return cached agent if available
        if workspace_id in self.workspace_agents:
            return self.workspace_agents[workspace_id]

        try:
            llmModel=(await self.db_client.LLMSettings.find_one({"tenantId":ObjectId(tenant_id)}))or {}
            vectorDBSettings=(await self.db_client.VectorDBSettings.find_one({"tenantId":ObjectId(tenant_id)}))or {}
            embeddingSettings=(await self.db_client.EmbeddingSettings.find_one({"tenantId":ObjectId(tenant_id)})) or {}

            # Get vector store configuration for unified collection
            vector_store_config = {
                "connection_string":vectorDBSettings.get("localUri", os.getenv("VECTOR_DATABASE_URL")) ,
                "db_name": vectorDBSettings.get("environment", os.getenv("VECTOR_DATABASE_NAME")),
                "text_key": "content",
                "embedding_key": "embedding",
                "index_name": "sharded_diskann_vector_index",
                # Use unified vectors collection instead of workspace-specific
                "namespace": "ada3Vector"
            }

            # Create vector search index if it doesn't exist
            if vector_store_config.get("connection_string") and vector_store_config.get("db_name") and vectorDBSettings.get("provider","MONGODB")=="MONGODB":
                try:
                    from pymongo import MongoClient

                    # Use connection pool if available, otherwise create direct connection
                    if mongo_pool:
                        client = mongo_pool.get_client(vector_store_config["connection_string"])
                        logger.debug("Using pooled MongoDB connection for index creation")
                    else:
                        client = MongoClient(vector_store_config["connection_string"])
                        logger.debug("Using direct MongoDB connection for index creation")

                    db_name = vector_store_config["db_name"]
                    collection_name = vector_store_config["namespace"]

                    # Create database if it doesn't exist (in MongoDB this is implicit)
                    db = client[db_name]

                    # Check if collection exists, create if not
                    if collection_name not in db.list_collection_names():
                        logger.info(f"Creating collection {collection_name}")
                        db.create_collection(collection_name)

                    # Get index name and field names for unified collection
                    index_name = vector_store_config.get("index_name", "sharded_diskann_vector_index")
                    embedding_key = vector_store_config.get("embedding_key", "embedding")

                    # Create sharded DiskANN vector search index for unified collection
                    try:
                        # Check if index exists by listing indexes
                        existing_indexes = list(db[collection_name].list_indexes())
                        index_exists = any(idx.get('name') == index_name for idx in existing_indexes)

                        if not index_exists:
                            # Get document count for optimal index configuration
                            doc_count = db[collection_name].count_documents({})
                            max_degree = min(128, max(32, 1536 // 24))  # Based on dimensions
                            l_build = min(300, max(50, int(doc_count ** 0.3)))  # Based on doc count

                            logger.info(f"Creating sharded DiskANN vector index {index_name} for unified collection {collection_name} with {doc_count} documents")

                            # Create sharded DiskANN index using Cosmos DB syntax
                            result = db.command({
                                "createIndexes": collection_name,
                                "indexes": [{
                                    "name": index_name,
                                    "key": {embedding_key: "cosmosSearch"},
                                    "cosmosSearchOptions": {
                                        "kind": "vector-diskann",
                                        "similarity": "COS",
                                        "dimensions": 1536,
                                        "maxDegree": max_degree,
                                        "lBuild": l_build,
                                        "vectorIndexShardKey": ["/tenantId"]  # Shard by tenantId
                                    }
                                },       
                                # TenantId index
                                {
                                    "name": "tenantId_filter_index",
                                    "key": {"tenantId": 1}
                                },
                                # WorkspaceId index
                                {
                                    "name": "workspaceId_filter_index",
                                    "key": {"workspaceId": 1}
                                },
                                # PageId index
                                {
                                    "name": "pageId_filter_index",
                                    "key": {"pageId": 1}
                                },
                                # Compound index: tenantId + workspaceId
                                {
                                    "name": "tenant_workspace_filter_index",
                                    "key": {"tenantId": 1, "workspaceId": 1}
                                },
                                # Compound index: tenantId + workspaceId + pageId
                                {
                                    "name": "tenant_workspace_page_filter_index",
                                    "key": {"tenantId": 1, "workspaceId": 1, "pageId": 1}
                                }
                                
                                ]
                            })
                            logger.info(f"Successfully created sharded DiskANN vector index with maxDegree={max_degree}, lBuild={l_build}: {result}")
                        else:
                            logger.debug(f"Sharded vector index {index_name} already exists for unified collection {collection_name}")
                    except Exception as e:
                        if "IndexKeySpecsConflict" in str(e) or "existing index" in str(e):
                            logger.info(f"Sharded vector index {index_name} already exists")
                        else:
                            logger.warning(f"Error creating sharded DiskANN vector index: {e}")
                except Exception as e:
                    logger.warning(f"Error connecting to database: {e}")
            # Determine embedding model type based on configuration
            embedding_model_config = {
                "deployment_name": os.getenv("AZURE_OPENAI_EMBEDDING_DEPLOYMENT") or os.getenv("AZURE_OPENAI_EMBEDDING_MODEL", "text-embedding-3-small"),
                "azure_endpoint": os.getenv("AZURE_OPENAI_EMBEDDING_API_ENDPOINT") or os.getenv("AZURE_OPENAI_EMBEDDING_API_ENDPOINT"),
                "api_version": os.getenv("AZURE_OPENAI_EMBEDDING_API_VERSION", "2023-12-01-preview"),
                "model_name": str(embeddingSettings.get("embedderModel", os.getenv("AZURE_OPENAI_EMBEDDING_MODEL", "text-embedding-3-small"))),
                "api_key": str(embeddingSettings.get("apiKey", os.getenv("AZURE_OPENAI_EMBEDDING_API_KEY")))
            }

            # Use Azure Vision embeddings if AZURE_OPENAI_EMBEDDING_MODEL is configured
            if os.getenv("AZURE_OPENAI_EMBEDDING_MODEL") and os.getenv("AZURE_OPENAI_EMBEDDING_API_ENDPOINT"):
                embedding_model_config["type"] = "azure_vision"
            else:
                embedding_model_config["type"] = "azure"

            # Create new agent with workspace-specific configuration
            agent = AdvancedRAGAgent(
                vector_store_type=str(vectorDBSettings.get("provider", "mongodb")).lower(),
                embedding_model=embedding_model_config,
                llm_model= {
                    "type":str(llmModel.get("provider","deepseek")).lower(),
                    "deployment_name":str(llmModel.get("azureOpenAIDeployment",os.getenv("AZURE_OPENAI_DEPLOYMENT"))),
                    "azure_endpoint":str(llmModel.get("azureOpenAIEndpoint",os.getenv("AZURE_OPENAI_API_ENDPOINT"))),
                    "api_version": str(llmModel.get("azureOpenAIApiVersion",os.getenv("AZURE_OPENAI_API_VERSION"))),
                    "model_name": str(llmModel.get("model_name",os.getenv("AZURE_OPENAI_MODEL"))),
                    "api_key": str(llmModel.get("apiKey",os.getenv("DEEPSEEK_API_KEY")))
                },
                api_keys={
                    "OPENAI_API_KEY":str(embeddingSettings.get("apiKey",os.getenv("OPENAI_API_KEY"))),
                    "AZURE_OPENAI_EMBEDDING_API_KEY":str(embeddingSettings.get("apiKey",os.getenv("AZURE_OPENAI_EMBEDDING_API_KEY"))),
                    "DEEPSEEK_API_KEY":str(llmModel.get("apiKey",os.getenv("DEEPSEEK_API_KEY"))),
                    "AZURE_OPENAI_API_KEY":str(llmModel.get("apiKey",os.getenv("AZURE_OPENAI_API_KEY"))),
                },
                vector_store_config=vector_store_config
            )

            # Cache the agent
            self.workspace_agents[workspace_id] = agent
            return agent
        except Exception as e:
            logger.error(f"Error creating RAG agent for workspace {workspace_id}: {e}")
            return None


    async def index_document_with_access_check(self, user_id: str, workspace_id: str,
                                             document_path: str, document_type: str = "auto",
                                             metadata: Dict[str, Any] = None,tenant_id:str=None,file_id:str=None,slug:str=None,page_id:str=None) -> Dict[str, Any]:
        """Index a document in a workspace with access control check.

        Args:
            user_id: The ID of the user uploading the document
            workspace_id: The ID of the workspace
            document_path: Path to the document file
            document_type: Type of document (pdf, text, etc.)
            metadata: Additional metadata to add to the document
            config: Configuration for the RAG agent if needed

        Returns:
            Result of indexing operation
        """
        import datetime
        start_time = datetime.datetime.now()

        logger.info(f"Starting document indexing for file_id={file_id} in workspace {workspace_id}")

        # Detect audio/video files for appropriate processing
        is_audio_video_file = False
        if document_path:
            audio_video_extensions = ['.mp3', '.wav', '.m4a', '.ogg', '.mp4', '.avi', '.mov', '.webm', '.flac', '.aac']
            is_audio_video_file = any(ext in document_path.lower() for ext in audio_video_extensions)

            if is_audio_video_file:
                logger.info(f"Detected audio/video file for processing")
                if not document_path.startswith(('http://', 'https://')):
                    logger.warning(f"Audio/video file with local path may cause processing issues")

        # Check access first
        has_access = await self.check_workspace_access(user_id, workspace_id,tenant_id)
        if not has_access:
            logger.warning(f"Access denied for user {user_id} in workspace {workspace_id}")
            return {
                "error": "You do not have access to this workspace",
                "status": 403
            }

        # Get or create agent for this workspace
        agent = await self.get_workspace_agent(workspace_id,tenant_id)
        if not agent:
            logger.error(f"Failed to get RAG agent for workspace {workspace_id}")
            return {
                "error": "Failed to initialize document indexing for this workspace",
                "status": 500
            }

        try:
            # Load and index the document
            documents = await agent.load_documents(document_path, document_type,page_id)
            logger.info(f"Document loading completed: {len(documents) if documents else 0} documents loaded")

            # Log processing status for audio/video files
            if documents and is_audio_video_file:
                for doc in documents:
                    if 'transcription_service' in doc.metadata:
                        logger.info(f"Audio/video file processed via {doc.metadata['transcription_service']}")
                    if 'processing_status' in doc.metadata and doc.metadata['processing_status'] != 'success':
                        logger.warning(f"Audio/video processing status: {doc.metadata['processing_status']}")

            # Add unified collection metadata to all documents
            # Store all fields at root level AND inside metadata for maximum compatibility
            import datetime
            current_time = datetime.datetime.now(datetime.timezone.utc).isoformat()

            base_metadata = {
                # Root level fields for unified collection
                "workspace_id": workspace_id,
                "workspaceId": workspace_id,  # For unified collection compatibility
                "tenantId": tenant_id,  # Fix: Use proper tenant_id, not workspace_id
                "fileId": file_id,
                "pageId": page_id,  # Keep pageId at root level
                "slug": slug,
                "created_at": current_time,  # Add created_at timestamp
            }
            if metadata:
                base_metadata.update(metadata)

            # Add pageId to each document chunk
            for i, doc in enumerate(documents):
                logger.info(f"Adding base metadata to document {doc}")
                doc.metadata.update(base_metadata)
                doc.metadata["metadata"] = {
                    "file_name": doc.metadata.get("file_name", ""),
                    "title": doc.metadata.get("title", ""),
                    "workspace_id": workspace_id,
                    "workspaceId": workspace_id,  # For unified collection compatibility
                    "tenantId": tenant_id,
                    "fileId": file_id,
                    "pageId": page_id,
                    "source": document_path,
                    "slug": slug,
                    "created_at": current_time,  # Also in nested metadata
                }

            logger.info(f"Starting document indexing...")
            agent.index_documents(documents)
            logger.info(f"Document indexing completed successfully")

            await self.db_client.File.update_one(
                {"_id": ObjectId(file_id)},
                {"$set": {"vectorizationStatus": "COMPLETED"}}
            )

            end_time = datetime.datetime.now()
            duration = (end_time - start_time).total_seconds()

            logger.info(f"Document indexing completed in {duration:.2f} seconds ({len(documents)} documents)")

            return {
                "status": 200,
                "message": f"Successfully indexed document in workspace {workspace_id}",
                "document_count": len(documents)
            }
        except Exception as e:
            end_time = datetime.datetime.now()
            duration = (end_time - start_time).total_seconds()

            logger.error(f"Document indexing failed after {duration:.2f} seconds: {str(e)}")

            if is_audio_video_file:
                logger.error(f"Audio/video file processing failed - check URL accessibility and service configuration")

            await self.db_client.File.update_one(
                {"_id": ObjectId(file_id)},
                {"$set": {"vectorizationStatus": "FAILED"}}
            )
            logger.error(f"Error indexing document in workspace {workspace_id}: {e}")
            return {
                "error": f"Error indexing document: {str(e)}",
                "status": 500
            }
    
    async def generate_title_from_question(self, question: str,tenant_id:str=None, workspace_id: str = None) -> Dict[str, Any]:
        """
        Generate a title for a given question based on the user's question history.

        Args:
            question: The question for which a title is to be generated

        Returns:
            A dictionary containing the generated title or an error message.
        """
        agent = await self.get_workspace_agent(workspace_id,tenant_id)
        if not agent:
            return {
                "error": "Failed to initialize title generation",
                "status": 500
            }
        try:
            prompt = f"Generate a concise and engaging 3 to 4-word title summarizing this question: '{question}'"
            title = agent.llm.invoke(prompt)

            return {
                "title": title.content,
                "status": 200
            }
        except Exception as e:
            logger.error(f"Error generating title: {e}")
            return {
                "error": f"Error generating title: {str(e)}",
                "status": 500
            }

    async def delete_file(self,file_id:str=None, workspace_id: str = None,tenant_id:str=None) -> Dict[str, Any]:
        """
        Delete a file from the workspace.
        Args:
            file_id: The ID of the file to be deleted
        Returns:
            A dictionary containing the result of the deletion operation.
        """
        try:

            agent = await self.get_workspace_agent(workspace_id,tenant_id)
            if not agent:
                return {
                    "error": "Failed to initialize title generation",
                    "status": 500
                }
            result = agent.delete_documents(file_id=file_id,collection_name=f"workspace_{workspace_id}")
            return {
                "status": 200,
                "message": f"Successfully deleted file {file_id}"
            }
        except Exception as e:
            logger.error(f"Error deleting file: {e}")
            return {
                "error": f"Error deleting file: {str(e)}",
                "status": 500
            }

    def clear_agent_cache(self, workspace_id: str = None) -> Dict[str, Any]:
        """
        Clear the agent cache for a specific workspace or all workspaces.

        Args:
            workspace_id: The ID of the workspace to clear cache for. If None, clears all caches.

        Returns:
            A dictionary containing the result of the cache clearing operation.
        """
        try:
            if workspace_id:
                # Clear cache for specific workspace
                if workspace_id in self.workspace_agents:
                    del self.workspace_agents[workspace_id]
                    logger.info(f"Cleared agent cache for workspace {workspace_id}")
                    return {
                        "status": 200,
                        "message": f"Successfully cleared agent cache for workspace {workspace_id}"
                    }
                else:
                    return {
                        "status": 200,
                        "message": f"No cached agent found for workspace {workspace_id}"
                    }
            else:
                # Clear all caches
                workspace_count = len(self.workspace_agents)
                multi_agent_count = len(self.agents)

                self.workspace_agents.clear()
                self.agents.clear()

                logger.info(f"Cleared all agent caches: {workspace_count} workspace agents, {multi_agent_count} multi-workspace agents")
                return {
                    "status": 200,
                    "message": f"Successfully cleared all agent caches ({workspace_count} workspace agents, {multi_agent_count} multi-workspace agents)"
                }
        except Exception as e:
            logger.error(f"Error clearing agent cache: {e}")
            return {
                "error": f"Error clearing agent cache: {str(e)}",
                "status": 500
            }