import os

from dotenv import load_dotenv
from pydantic_settings import BaseSettings, SettingsConfigDict

load_dotenv()

# If the environment is Gitpod, the root path will be the workspace cluster host
# If not using gitpod, you can delete this if statement, but keep the else clause
if os.getenv("USER") == "gitpod":
    ROOT_PATH = f"https://8000-cording12-nextfastturbo-qqfo0frc496.{os.getenv('GITPOD_WORKSPACE_CLUSTER_HOST')}"
else:
    # Otherwise, the root path will be the local host. ROOT_PATH is an env var configured in Vercel deployment.
    # The value for production is equal to the root path of the deployment URL in Vercel.
    ROOT_PATH = os.getenv("ROOT_PATH", "http://127.0.0.1:8000")


class Settings(BaseSettings):
    PROJECT_NAME: str = "Swiss Knowledge Hub API"
    PROJECT_DESCRIPTION: str = "A Swiss Knowledge Hub wep API app"
    DATABASE_URL: str = os.getenv("DATABASE_URL")
    PINECONE_API_KEY: str = os.getenv("PINECONE_API_KEY")
    OPENAI_API_KEY: str = os.getenv("OPENAI_API_KEY")
    OPENAI_EMBEDDER_MODEL: str = os.getenv("OPENAI_EMBEDDER_MODEL")
    AZURE_OPENAI_DEPLOYMENT: str = os.getenv("AZURE_OPENAI_DEPLOYMENT")
    AZURE_OPENAI_API_KEY: str = os.getenv("AZURE_OPENAI_API_KEY")
    AZURE_OPENAI_API_ENDPOINT: str = os.getenv("AZURE_OPENAI_API_ENDPOINT")
    AZURE_OPENAI_ENDPOINT: str = os.getenv("AZURE_OPENAI_ENDPOINT")
    AZURE_OPENAI_MODEL: str = os.getenv("AZURE_OPENAI_MODEL")
    AZURE_OPENAI_API_VERSION: str = os.getenv("AZURE_OPENAI_API_VERSION")
    LANCEDB_LOCAL_URI: str = os.getenv("LANCEDB_LOCAL_URI")
    CHROMADB_LOCAL_URI: str = os.getenv("CHROMADB_LOCAL_URI")
    VECTOR_DATABASE_URL: str = os.getenv("VECTOR_DATABASE_URL")
    GOOGLE_SEARCH_ENGINE_ID: str = os.getenv("GOOGLE_SEARCH_ENGINE_ID")
    GOOGLE_API_KEY: str = os.getenv("GOOGLE_API_KEY")
    VECTOR_DATABASE_NAME: str = os.getenv("VECTOR_DATABASE_NAME")
    AZURE_VISION_API_ENDPOINT: str = os.getenv("AZURE_VISION_API_ENDPOINT")
    AZURE_VISION_MODEL: str = os.getenv("AZURE_VISION_MODEL")
    DEEPSEEK_API_KEY: str = os.getenv("DEEPSEEK_API_KEY")
    AZURE_DEEPSEEK_R1_DEPLOYMENT: str = os.getenv("AZURE_DEEPSEEK_R1_DEPLOYMENT")
    AZURE_OPENAI_EMBEDDING_DEPLOYMENT: str = os.getenv("AZURE_OPENAI_EMBEDDING_DEPLOYMENT")
    AZURE_OPENAI_EMBEDDING_API_KEY: str = os.getenv("AZURE_OPENAI_EMBEDDING_API_KEY")
    AZURE_OPENAI_EMBEDDING_API_VERSION: str = os.getenv("AZURE_OPENAI_EMBEDDING_API_VERSION")
    AZURE_OPENAI_EMBEDDING_API_ENDPOINT: str = os.getenv("AZURE_OPENAI_EMBEDDING_API_ENDPOINT")
    AZURE_OPENAI_EMBEDDING_MODEL: str = os.getenv("AZURE_OPENAI_EMBEDDING_MODEL")
    NEXTAUTH_SECRET: str = os.getenv("NEXTAUTH_SECRET", "")
    NEXTAUTH_URL: str = os.getenv("NEXTAUTH_URL", "http://localhost:3000")
    ENV: str = os.getenv("ENV", "dev")
    # Encryption settings
    NEXT_ENCRYPTION_CLOAK_KEY: str = os.getenv("NEXT_ENCRYPTION_CLOAK_KEY", "")
    NEXT_ENCRYPTION_CLOAK_KEYCHAIN: str = os.getenv("NEXT_ENCRYPTION_CLOAK_KEYCHAIN", "")
    # Azure Video Indexer settings (for audio transcription)
    AZURE_VIDEO_INDEXER_ACCOUNT_ID: str = os.getenv("AZURE_VIDEO_INDEXER_ACCOUNT_ID", "")
    AZURE_VIDEO_INDEXER_ACCESS_TOKEN: str = os.getenv("AZURE_VIDEO_INDEXER_ACCESS_TOKEN", "")
    AZURE_VIDEO_INDEXER_LOCATION: str = os.getenv("AZURE_VIDEO_INDEXER_LOCATION", "switzerlandnorth")
    AZURE_VIDEO_INDEXER_SUBSCRIPTION_KEY: str = os.getenv("AZURE_VIDEO_INDEXER_SUBSCRIPTION_KEY", "")
    AZURE_SUBSCRIPTION_ID: str = os.getenv("AZURE_SUBSCRIPTION_ID", "")
    AZURE_RESOURCE_GROUP_NAME: str = os.getenv("AZURE_RESOURCE_GROUP_NAME", "")
    AZURE_VIDEO_INDEXER_ACCOUNT_NAME: str = os.getenv("AZURE_VIDEO_INDEXER_ACCOUNT_NAME", "")
    AZURE_TENANT_ID: str = os.getenv("AZURE_TENANT_ID", "")
    AZURE_CLIENT_ID: str = os.getenv("AZURE_CLIENT_ID", "")
    AZURE_CLIENT_SECRET: str = os.getenv("AZURE_CLIENT_SECRET", "")
    # Azure Blob Storage settings (for audio file uploads)
    AZURE_STORAGE_CONNECTION_STRING: str = os.getenv("AZURE_STORAGE_CONNECTION_STRING", "")
    AZURE_STORAGE_CONTAINER_NAME: str = os.getenv("AZURE_STORAGE_CONTAINER_NAME", "default")
    model_config = SettingsConfigDict(env_file=".env")
    API_VERSION: str = "/api/v1"
    ROOT: str = ROOT_PATH


settings = Settings()
