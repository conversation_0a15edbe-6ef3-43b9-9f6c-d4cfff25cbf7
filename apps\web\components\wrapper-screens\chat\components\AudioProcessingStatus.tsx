"use client";

import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Loader2, CheckCircle, XCircle, Clock } from 'lucide-react';
import { useAudioProcessingStatus } from '@/hooks/use-audio-processing-status';
import { useLanguage } from '@/lib/language-context';

interface AudioProcessingStatusProps {
  jobId: string | null;
  fileName: string;
  onComplete?: (audioContext: string) => void;
  onError?: (error: string) => void;
}

export const AudioProcessingStatus: React.FC<AudioProcessingStatusProps> = ({
  jobId,
  fileName,
  onComplete,
  onError,
}) => {
  const { t } = useLanguage();
  
  const { status, isLoading, error } = useAudioProcessingStatus({
    jobId,
    enabled: !!jobId,
    pollingInterval: 60000, // 60 seconds - reduced from 30s to reduce server load
    onComplete,
    onError,
  });

  if (!jobId) {
    return null;
  }

  const getStatusIcon = () => {
    if (isLoading) {
      return <Loader2 className="h-3 w-3 animate-spin" />;
    }

    switch (status?.status) {
      case 'pending':
        return <Clock className="h-3 w-3" />;
      case 'processing':
        return <Loader2 className="h-3 w-3 animate-spin" />;
      case 'completed':
        return <CheckCircle className="h-3 w-3" />;
      case 'failed':
        return <XCircle className="h-3 w-3" />;
      default:
        return <Clock className="h-3 w-3" />;
    }
  };

  const getStatusText = () => {
    if (error) {
      return t('audioProcessing.failed') || 'Processing failed';
    }

    switch (status?.status) {
      case 'pending':
        return t('audioProcessing.pending') || 'Queued for processing';
      case 'processing':
        return t('audioProcessing.processing') || 'Processing audio...';
      case 'completed':
        return t('audioProcessing.completed') || 'Processing complete';
      case 'failed':
        return t('audioProcessing.failed') || 'Processing failed';
      default:
        return t('audioProcessing.unknown') || 'Status unknown';
    }
  };

  const getStatusVariant = () => {
    if (error) return 'destructive';
    
    switch (status?.status) {
      case 'pending':
        return 'secondary';
      case 'processing':
        return 'default';
      case 'completed':
        return 'success';
      case 'failed':
        return 'destructive';
      default:
        return 'secondary';
    }
  };

  return (
    <div className="flex items-center gap-2 p-2 bg-muted/50 rounded-md">
      <div className="flex items-center gap-1">
        {getStatusIcon()}
        <span className="text-xs font-medium">{fileName}</span>
      </div>
      
      <Badge variant={getStatusVariant() as any} className="text-xs">
        {getStatusText()}
      </Badge>

      {status?.status === 'completed' && (
        <div className="text-xs text-muted-foreground">
          {status.results && (
            <span>
              {t('audioProcessing.filesProcessed', { count: status.results.files_processed || 1 })}
            </span>
          )}
        </div>
      )}

      {error && (
        <div className="text-xs text-destructive">
          {error}
        </div>
      )}
    </div>
  );
};

// Add processing status indicator for audio files in the file list
interface AudioFileStatusProps {
  file: {
    name: string;
    audioJobId?: string;
    audioStatus?: string;
  };
  onStatusUpdate?: (audioContext: string) => void;
  onError?: (error: string) => void;
}

export const AudioFileStatus: React.FC<AudioFileStatusProps> = ({
  file,
  onStatusUpdate,
  onError,
}) => {
  if (!file.audioJobId) {
    return null;
  }

  const handleComplete = (audioContext: string) => {
    console.log(`🎵 Audio processing completed for ${file.name}:`, audioContext);
    onStatusUpdate?.(audioContext);
  };

  const handleError = (error: string) => {
    console.error(`❌ Audio processing failed for ${file.name}:`, error);
    onError?.(error);
  };

  return (
    <AudioProcessingStatus
      jobId={file.audioJobId}
      fileName={file.name}
      onComplete={handleComplete}
      onError={handleError}
    />
  );
};
