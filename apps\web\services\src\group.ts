import { apiUrl, fetchJson } from "..";

// Get all groups
export const getGroups = async (userId, tenantId) => {
  try {
    const response = await fetchJson(
      `${apiUrl}/groups?tenantId=${tenantId}`,
      userId,
      tenantId
    );

    return response;
  } catch (error) {
    console.error("Error fetching groups:", error);
    return { error: "Failed to fetch groups" };
  }
};

// Get a single group by ID
export const getGroup = async (
  groupId: string,
  userId: string,
  tenantId: string
) => {
  try {
    const response = await fetchJson(
      `${apiUrl}/groups/${groupId}?tenantId=${tenantId}`,
      userId,
      tenantId
    );

    return response;
  } catch (error) {
    console.error("Error fetching group:", error);
    return { error: "Failed to fetch group" };
  }
};

// Create a new group
export const createGroup = async (
  data: {
    name: string;
    description?: string;
    tenantId: string;
  },
  userId
) => {
  try {
    const response = await fetch(`${apiUrl}/groups`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "x-tenant-id": data.tenantId,
        "x-user-id": userId,
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const errorData = await response.json();
      return { error: errorData.error || "Network response was not ok" };
    }
    return await response.json();
  } catch (error) {
    console.error("Error creating group:", error);
    return { error: error?.error || "Failed to create group" };
  }
};

// Update a group
export const updateGroup = async (
  data: {
    id: string;
    name: string;
    description?: string;
    tenantId: string;
  },
  userId
) => {
  try {
    const response = await fetch(`${apiUrl}/groups`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
        "x-tenant-id": data.tenantId,
        "x-user-id": userId,
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error("Network response was not ok");
    }
    return await response.json();
  } catch (error) {
    console.error("Error updating group:", error);
    return { error: "Failed to update group" };
  }
};

// Delete a group
export const deleteGroup = async (
  id: string,
  tenantId: string,
  userId: string
) => {
  try {
    const response = await fetch(
      `${apiUrl}/groups?id=${id}&tenantId=${tenantId}`,
      {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
          "x-tenant-id": tenantId,
          "x-user-id": userId,
        },
      }
    );

    if (!response.ok) {
      throw new Error("Network response was not ok");
    }
    return await response.json();
  } catch (error) {
    console.error("Error deleting group:", error);
    return { error: "Failed to delete group" };
  }
};

// Add a user to a group
export const addUserToGroup = async (
  data: {
    groupId: string;
    email?: string;
    userId?: string;
    tenantId: string;
  },
  userId
) => {
  try {
    const response = await fetch(`${apiUrl}/groups/members`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "x-tenant-id": data.tenantId,
        "x-user-id": userId,
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const errorData = await response.json();
      return { error: errorData.error || "Network response was not ok" };
    }
    return await response.json();
  } catch (error) {
    console.error("Error adding user to group:", error);
    return { error: "Failed to add user to group" };
  }
};

// Remove a user from a group
export const removeUserFromGroup = async (
  groupId: string,
  userIdToRemove: string,
  tenantId: string,
  userId: string
) => {
  try {
    const response = await fetch(
      `${apiUrl}/groups/members?groupId=${groupId}&userId=${userIdToRemove}&tenantId=${tenantId}`,
      {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
          "x-tenant-id": tenantId,
          "x-user-id": userId,
        },
      }
    );

    if (!response.ok) {
      throw new Error("Network response was not ok");
    }
    return await response.json();
  } catch (error) {
    console.error("Error removing user from group:", error);
    return { error: "Failed to remove user from group" };
  }
};

// Get all members of a group
export const getGroupMembers = async (
  groupId: string,
  tenantId: string,
  userId: string
) => {
  try {
    const response = await fetchJson(
      `${apiUrl}/groups/members?groupId=${groupId}&tenantId=${tenantId}`,
      userId,
      tenantId
    );

    if (!response.ok) {
      throw new Error("Network response was not ok");
    }
    return await response.json();
  } catch (error) {
    console.error("Error fetching group members:", error);
    return { error: "Failed to fetch group members" };
  }
};

// Assign a workspace to a group
export const assignWorkspaceToGroup = async (
  data: {
    groupId: string;
    workspaceId: string;
    tenantId: string;
  },
  userId
) => {
  try {
    const response = await fetch(`${apiUrl}/groups/workspaces`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "x-tenant-id": data.tenantId,
        "x-user-id": userId,
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error("Network response was not ok");
    }
    return await response.json();
  } catch (error) {
    console.error("Error assigning workspace to group:", error);
    return { error: "Failed to assign workspace to group" };
  }
};

// Remove a workspace from a group
export const removeWorkspaceFromGroup = async (
  groupId: string,
  workspaceId: string,
  tenantId: string,
  userId: string
) => {
  try {
    const response = await fetch(
      `${apiUrl}/groups/workspaces?groupId=${groupId}&workspaceId=${workspaceId}&tenantId=${tenantId}`,
      {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
          "x-tenant-id": tenantId,
          "x-user-id": userId,
        },
      }
    );

    if (!response.ok) {
      throw new Error("Network response was not ok");
    }
    return await response.json();
  } catch (error) {
    console.error("Error removing workspace from group:", error);
    return { error: "Failed to remove workspace from group" };
  }
};

// Get all workspaces assigned to a group
export const getGroupWorkspaces = async (
  groupId: string,
  tenantId: string,
  userId: string
) => {
  try {
    const response = await fetchJson(
      `${apiUrl}/groups/workspaces?groupId=${groupId}&tenantId=${tenantId}`,
      userId,
      tenantId
    );

    if (!response.ok) {
      throw new Error("Network response was not ok");
    }
    return await response.json();
  } catch (error) {
    console.error("Error fetching group workspaces:", error);
    return { error: "Failed to fetch group workspaces" };
  }
};

// Assign a custom role to a group
export const assignRoleToGroup = async (
  data: {
    groupId: string;
    customRoleId: string;
    tenantId: string;
  },
  userId
) => {
  try {
    const response = await fetch(`${apiUrl}/groups/roles`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "x-tenant-id": data.tenantId,
        "x-user-id": userId,
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error("Network response was not ok");
    }
    return await response.json();
  } catch (error) {
    console.error("Error assigning role to group:", error);
    return { error: "Failed to assign role to group" };
  }
};

// Remove a custom role from a group
export const removeRoleFromGroup = async (
  groupId: string,
  tenantId: string,
  userId: string
) => {
  try {
    const response = await fetch(`${apiUrl}/groups/roles`, {
      method: "DELETE",
      headers: {
        "Content-Type": "application/json",
        "x-tenant-id": tenantId,
        "x-user-id": userId,
      },
      body: JSON.stringify({ groupId, tenantId }),
    });

    if (!response.ok) {
      throw new Error("Network response was not ok");
    }
    return await response.json();
  } catch (error) {
    console.error("Error removing role from group:", error);
    return { error: "Failed to remove role from group" };
  }
};
