import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/next-auth";
import db from "@/lib/shared-db";

export async function GET(request: Request) {
  const session = await getServerSession(authOptions);

  if (!session) {
    return NextResponse.json(
      { error: "Unauthorized" },
      { status: 401 }
    );
  }

  try {
    const { searchParams } = new URL(request.url);
    const days = parseInt(searchParams.get("days") || "30", 10);
    
    // Calculate the start date
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    // Get API requests grouped by endpoint
    const apiUsage = await db.aPIRequest.groupBy({
      by: ['endpoint'],
      where: {
        timestamp: {
          gte: startDate
        }
      },
      _count: {
        _all: true
      }
    });

    // Sort by usage count (descending)
    apiUsage.sort((a, b) => b._count._all - a._count._all);

    // Format for chart display
    const featureUsage = apiUsage.map(item => ({
      name: item.endpoint.split('/').pop() || item.endpoint, // Get the last part of the endpoint path
      value: item._count._all
    }));

    // Get token usage by request type
    const tokenUsage = await db.tokenUsage.groupBy({
      by: ['requestType'],
      where: {
        timestamp: {
          gte: startDate
        }
      },
      _sum: {
        inputTokens: true,
        outputTokens: true,
        cost: true
      },
      _count: {
        _all: true
      }
    });

    // Format token usage for chart display
    const tokenUsageByType = tokenUsage.map(item => ({
      name: item.requestType,
      requests: item._count._all,
      tokens: (item._sum.inputTokens || 0) + (item._sum.outputTokens || 0),
      cost: item._sum.cost || 0
    }));

    // Get usage by tenant
    const usageByTenant = await db.aPIRequest.groupBy({
      by: ['tenantId'],
      where: {
        timestamp: {
          gte: startDate
        }
      },
      _count: {
        _all: true
      }
    });

    // Get tenant names
    const tenantIds = usageByTenant.map(item => item.tenantId);
    const tenants = await db.tenant.findMany({
      where: {
        id: {
          in: tenantIds
        }
      },
      select: {
        id: true,
        name: true
      }
    });

    // Map tenant names to the results
    const usageByTenantWithNames = usageByTenant.map(item => {
      const tenant = tenants.find(t => t.id === item.tenantId);
      return {
        tenantId: item.tenantId,
        tenantName: tenant?.name || 'Unknown',
        requestCount: item._count._all
      };
    });

    // Sort by usage count (descending)
    usageByTenantWithNames.sort((a, b) => b.requestCount - a.requestCount);

    return NextResponse.json({
      period: `${days} days`,
      featureUsage,
      tokenUsageByType,
      usageByTenant: usageByTenantWithNames
    });
  } catch (error) {
    console.error("Error fetching feature usage metrics:", error);
    return NextResponse.json(
      { error: "Failed to fetch feature usage metrics" },
      { status: 500 }
    );
  }
}
