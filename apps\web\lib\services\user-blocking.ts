import db from "@/lib/shared-db";

/**
 * Shared utility for checking if a user is blocked
 * Used across authentication and chatbot endpoints
 */

export interface BlockCheckResult {
  blocked: boolean;
  reason?: string;
  blockType?: string;
  expiresAt?: Date | null;
  tenantId?: string;
}

/**
 * Check if a user is blocked in any tenant they have access to
 * This is used for authentication blocking - if user is blocked in ANY tenant, they cannot sign in
 */
export async function checkUserBlockedGlobal(
  userId?: string,
  email?: string,
  ipAddress?: string
): Promise<BlockCheckResult> {
  try {
    if (!userId && !email && !ipAddress) {
      return { blocked: false };
    }

    // Build user identification conditions
    const userConditions: any[] = [];
    if (userId) {
      userConditions.push({ userId: userId });
    }
    if (email) {
      userConditions.push({ email: email });
    }
    if (ipAddress) {
      userConditions.push({ ipAddress: ipAddress });
    }

    // Use corrected query structure (matching chatbot endpoint)
    const whereClause: any = {
      isActive: true,
      AND: [
        // Check expiration
        {
          OR: [
            { expiresAt: null }, // Permanent blocks
            { expiresAt: { gt: new Date() } }, // Non-expired blocks
          ],
        },
        // User identification
        {
          OR: userConditions,
        },
      ],
    };

    console.log(`🔍 Checking global user block status:`, {
      userId,
      email,
      ipAddress,
      whereClause: JSON.stringify(whereClause, null, 2),
    });

    const blockedUser = await db.blockedUser.findFirst({
      where: whereClause,
      orderBy: { createdAt: "desc" },
    });

    console.log(`🔍 Global block check result:`, blockedUser ? 'BLOCKED' : 'NOT BLOCKED', blockedUser ? {
      id: blockedUser.id,
      blockType: blockedUser.blockType,
      reason: blockedUser.reason,
      tenantId: blockedUser.tenantId,
      expiresAt: blockedUser.expiresAt,
    } : null);

    if (blockedUser) {
      return {
        blocked: true,
        reason: blockedUser.reason,
        blockType: blockedUser.blockType,
        expiresAt: blockedUser.expiresAt,
        tenantId: blockedUser.tenantId,
      };
    }

    return { blocked: false };
  } catch (error) {
    console.error("Error checking global user block status:", error);
    // Fail open for authentication - allow login if check fails
    return { blocked: false };
  }
}

/**
 * Check if a user is blocked in a specific tenant
 * This is used for chatbot access and tenant-specific operations
 */
export async function checkUserBlockedInTenant(
  tenantId: string,
  userId?: string,
  email?: string,
  ipAddress?: string
): Promise<BlockCheckResult> {
  try {
    if (!userId && !email && !ipAddress) {
      return { blocked: false };
    }

    // Build user identification conditions
    const userConditions: any[] = [];
    if (userId) {
      userConditions.push({ userId: userId });
    }
    if (email) {
      userConditions.push({ email: email });
    }
    if (ipAddress) {
      userConditions.push({ ipAddress: ipAddress });
    }

    // Use corrected query structure (matching chatbot endpoint)
    const whereClause: any = {
      tenantId: tenantId,
      isActive: true,
      AND: [
        // Check expiration
        {
          OR: [
            { expiresAt: null }, // Permanent blocks
            { expiresAt: { gt: new Date() } }, // Non-expired blocks
          ],
        },
        // User identification
        {
          OR: userConditions,
        },
      ],
    };

    console.log(`🔍 Checking tenant user block status:`, {
      tenantId,
      userId,
      email,
      ipAddress,
      whereClause: JSON.stringify(whereClause, null, 2),
    });

    const blockedUser = await db.blockedUser.findFirst({
      where: whereClause,
      orderBy: { createdAt: "desc" },
    });

    console.log(`🔍 Tenant block check result:`, blockedUser ? 'BLOCKED' : 'NOT BLOCKED', blockedUser ? {
      id: blockedUser.id,
      blockType: blockedUser.blockType,
      reason: blockedUser.reason,
      expiresAt: blockedUser.expiresAt,
    } : null);

    if (blockedUser) {
      return {
        blocked: true,
        reason: blockedUser.reason,
        blockType: blockedUser.blockType,
        expiresAt: blockedUser.expiresAt,
        tenantId: blockedUser.tenantId,
      };
    }

    return { blocked: false };
  } catch (error) {
    console.error("Error checking tenant user block status:", error);
    // Fail open for chatbot access - allow access if check fails
    return { blocked: false };
  }
}

/**
 * Check if a user is blocked in multiple specific tenants
 * Returns the first blocking found, or false if no blocks exist
 */
export async function checkUserBlockedInTenants(
  tenantIds: string[],
  userId?: string,
  email?: string,
  ipAddress?: string
): Promise<BlockCheckResult> {
  try {
    if (!userId && !email && !ipAddress) {
      return { blocked: false };
    }

    if (tenantIds.length === 0) {
      return { blocked: false };
    }

    // Build user identification conditions
    const userConditions: any[] = [];
    if (userId) {
      userConditions.push({ userId: userId });
    }
    if (email) {
      userConditions.push({ email: email });
    }
    if (ipAddress) {
      userConditions.push({ ipAddress: ipAddress });
    }

    // Use corrected query structure
    const whereClause: any = {
      tenantId: { in: tenantIds },
      isActive: true,
      AND: [
        // Check expiration
        {
          OR: [
            { expiresAt: null }, // Permanent blocks
            { expiresAt: { gt: new Date() } }, // Non-expired blocks
          ],
        },
        // User identification
        {
          OR: userConditions,
        },
      ],
    };

    const blockedUser = await db.blockedUser.findFirst({
      where: whereClause,
      orderBy: { createdAt: "desc" },
    });

    if (blockedUser) {
      return {
        blocked: true,
        reason: blockedUser.reason,
        blockType: blockedUser.blockType,
        expiresAt: blockedUser.expiresAt,
        tenantId: blockedUser.tenantId,
      };
    }

    return { blocked: false };
  } catch (error) {
    console.error("Error checking multi-tenant user block status:", error);
    // Fail open - allow access if check fails
    return { blocked: false };
  }
}

/**
 * Log a blocked authentication attempt for audit purposes
 */
export function logBlockedAuthAttempt(
  userId?: string,
  email?: string,
  ipAddress?: string,
  blockInfo?: BlockCheckResult,
  context?: string
) {
  console.log(`🚫 Blocked authentication attempt:`, {
    userId,
    email,
    ipAddress,
    blockType: blockInfo?.blockType,
    blockReason: blockInfo?.reason,
    tenantId: blockInfo?.tenantId,
    expiresAt: blockInfo?.expiresAt,
    context: context || "authentication",
    timestamp: new Date().toISOString(),
  });
}
