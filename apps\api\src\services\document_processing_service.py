"""
Document Processing Service for handling asynchronous document vectorization jobs.

This service manages the lifecycle of document processing jobs, including:
- Creating and tracking document processing jobs
- Background processing with vector indexing
- Job status updates and result delivery
"""

import asyncio
import logging
import uuid
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any
import time

from bson import ObjectId

logger = logging.getLogger(__name__)


class DocumentProcessingService:
    """Service for managing asynchronous document processing jobs."""

    def __init__(self):
        self.db_client = None  # Will be set by endpoints
        self._processing_tasks: Dict[str, asyncio.Task] = {}
        self._last_job_time = 0
        self._min_job_interval = 0.5  # Minimum 500ms between job creations
        self._processing_semaphore = asyncio.Semaphore(2)  # Only 2 concurrent processing tasks
    
    async def create_document_processing_job(
        self,
        document_path: str,
        document_type: str,
        user_id: str,
        tenant_id: str,
        workspace_id: str,
        file_id: str,
        workspace_slug: str,
        metadata: Optional[Dict[str, Any]] = None,
        page_id: Optional[str] = None
    ) -> str:
        """
        Create a new document processing job and start background processing.
        
        Args:
            document_path: Path or URL to the document
            document_type: Type of document (pdf, text, etc.)
            user_id: ID of the user requesting the processing
            tenant_id: ID of the tenant
            workspace_id: ID of the workspace
            file_id: ID of the file record
            workspace_slug: Slug of the workspace
            metadata: Additional metadata for the document
            page_id: Optional page ID for the document
            
        Returns:
            Job ID for tracking the processing status
        """
        try:
            # Rate limiting: ensure minimum interval between job creations
            current_time = time.time()
            time_since_last_job = current_time - self._last_job_time

            if time_since_last_job < self._min_job_interval:
                sleep_time = self._min_job_interval - time_since_last_job
                logger.info(f"Rate limiting: waiting {sleep_time:.2f}s before creating job")
                await asyncio.sleep(sleep_time)

            self._last_job_time = time.time()

            # Check for existing pending/processing jobs for this workspace to prevent overload
            existing_jobs = await self.db_client.DocumentProcessingJob.count_documents({
                "workspaceId": ObjectId(workspace_id),
                "status": {"$in": ["pending", "processing"]}
            })

            if existing_jobs >= 10:  # Limit to 10 concurrent jobs per workspace
                logger.warning(f"Too many concurrent jobs ({existing_jobs}) for workspace {workspace_id}")
                raise Exception(f"Too many concurrent processing jobs for this workspace. Please wait for some to complete.")

            # Generate unique job ID
            job_id = str(uuid.uuid4())

            logger.info(f"Creating document processing job {job_id} for user {user_id} (workspace has {existing_jobs} active jobs)")
            
            # Create job record in database
            job_data = {
                "jobId": job_id,
                "status": "pending",
                "documentPath": document_path,
                "documentType": document_type,
                "metadata": metadata or {},
                "userId": ObjectId(user_id),
                "tenantId": ObjectId(tenant_id),
                "workspaceId": ObjectId(workspace_id),
                "fileId": file_id,
                "pageId": page_id,
                "workspaceSlug": workspace_slug,
                "createdAt": datetime.now(timezone.utc),
                "updatedAt": datetime.now(timezone.utc)
            }

            await self.db_client.DocumentProcessingJob.insert_one(job_data)
            
            # Start background processing
            task = asyncio.create_task(self._process_document_job(job_id))
            self._processing_tasks[job_id] = task
            
            logger.info(f"Document processing job {job_id} created and background processing started")
            return job_id
            
        except Exception as e:
            logger.error(f"Failed to create document processing job: {e}")
            raise
    
    async def get_job_status(self, job_id: str) -> Optional[Dict[str, Any]]:
        """
        Get the current status of a document processing job.

        Args:
            job_id: The job ID to check
            
        Returns:
            Job status information or None if not found
        """
        try:
            job = await self.db_client.DocumentProcessingJob.find_one({"jobId": job_id})
            if not job:
                return None
                
            return {
                "jobId": job_id,
                "status": job["status"],
                "documentPath": job.get("documentPath"),
                "documentType": job.get("documentType"),
                "errorMessage": job.get("errorMessage"),
                "documentCount": job.get("documentCount"),
                "createdAt": job["createdAt"],
                "updatedAt": job["updatedAt"],
                "completedAt": job.get("completedAt")
            }
            
        except Exception as e:
            logger.error(f"Error getting job status for {job_id}: {e}")
            return None

    async def _process_document_job(self, job_id: str):
        """
        Background processing of document job with concurrency control.

        Args:
            job_id: The job ID to process
        """
        # Use semaphore to limit concurrent processing to 2 documents
        async with self._processing_semaphore:
            try:
                logger.info(f"Starting background document processing for job {job_id} (concurrency: {2 - self._processing_semaphore._value}/2)")

                # Get job details
                job = await self.db_client.DocumentProcessingJob.find_one({"jobId": job_id})
                if not job:
                    logger.error(f"Document processing job {job_id} not found")
                    return
            
                # Update status to processing
                await self.db_client.DocumentProcessingJob.update_one(
                    {"jobId": job_id},
                    {
                        "$set": {
                            "status": "processing",
                            "updatedAt": datetime.now(timezone.utc)
                        }
                    }
                )
            
                # Extract job parameters
                document_path = job["documentPath"]
                document_type = job["documentType"]
                workspace_id = job["workspaceId"]
                user_id = job["userId"]
                tenant_id = job["tenantId"]
                file_id = job["fileId"]
                workspace_slug = job["workspaceSlug"]
                metadata = job.get("metadata", {})
                page_id = job.get("pageId")

                # Import and create workspace RAG manager
                from src.services.workspace_rag import WorkspaceRAGManager
                workspace_rag_manager = WorkspaceRAGManager()
                workspace_rag_manager.db_client = self.db_client

                # Process the document with access control
                result = await workspace_rag_manager.index_document_with_access_check(
                    user_id=str(user_id),
                    workspace_id=str(workspace_id),
                    document_path=document_path,
                    document_type=document_type,
                    metadata=metadata,
                    tenant_id=str(tenant_id),
                    file_id=file_id,
                    slug=workspace_slug,
                    page_id=page_id
                )

                # Check if processing was successful
                if result.get("status") == 200:
                    await self._complete_job(job_id, result.get("document_count", 0))
                    logger.info(f"Document processing job {job_id} completed successfully")
                else:
                    error_message = result.get("message", "Unknown error during document processing")
                    await self._fail_job(job_id, error_message)
                    logger.error(f"Document processing job {job_id} failed: {error_message}")

            except Exception as e:
                logger.error(f"Error in background document processing for job {job_id}: {e}")
                await self._fail_job(job_id, f"Processing failed: {str(e)}")
            finally:
                # Clean up task reference
                if job_id in self._processing_tasks:
                    del self._processing_tasks[job_id]

    async def _complete_job(self, job_id: str, document_count: int):
        """Mark job as completed with results."""
        try:
            await self.db_client.DocumentProcessingJob.update_one(
                {"jobId": job_id},
                {
                    "$set": {
                        "status": "completed",
                        "documentCount": document_count,
                        "completedAt": datetime.now(timezone.utc),
                        "updatedAt": datetime.now(timezone.utc)
                    }
                }
            )
            logger.info(f"Document processing job {job_id} marked as completed")
        except Exception as e:
            logger.error(f"Failed to complete job {job_id}: {e}")

    async def _fail_job(self, job_id: str, error_message: str):
        """Mark job as failed with error message."""
        try:
            await self.db_client.DocumentProcessingJob.update_one(
                {"jobId": job_id},
                {
                    "$set": {
                        "status": "failed",
                        "errorMessage": error_message,
                        "completedAt": datetime.now(timezone.utc),
                        "updatedAt": datetime.now(timezone.utc)
                    }
                }
            )
            logger.error(f"Document processing job {job_id} marked as failed: {error_message}")
        except Exception as e:
            logger.error(f"Failed to mark job {job_id} as failed: {e}")


# Global instance
document_processing_service = DocumentProcessingService()
