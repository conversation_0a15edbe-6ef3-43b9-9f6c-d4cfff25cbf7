import { apiUrl, fetchJson } from "..";

// Get all custom roles for a tenant
export const getCustomRoles = async (tenantId: string, userId: string) => {
  try {
    const response = await fetchJson(
      `${apiUrl}/roles?tenantId=${tenantId}`,
      userId,
      tenantId
    );
    return response;
  } catch (error) {
    console.error("Error fetching custom roles:", error);
    return { error: "Failed to fetch custom roles" };
  }
};

// Get a specific custom role by ID
export const getCustomRole = async (roleId: string, tenantId: string) => {
  try {
    const response = await fetchJson(
      `${apiUrl}/roles/${roleId}`,
      null,
      tenantId
    );
    return response;
  } catch (error) {
    console.error("Error fetching custom role:", error);
    return { error: "Failed to fetch custom role" };
  }
};

// Create a new custom role
export const createCustomRole = async (
  data: {
    name: string;
    description?: string;
    permissionIds: string[];
    workspaceIds?: string[];
  },
  tenantId: string
) => {
  try {
    const response = await fetch(`${apiUrl}/roles`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "x-tenant-id": tenantId,
      },
      body: JSON.stringify({
        ...data,
        tenantId,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Network response was not ok");
    }
    return await response.json();
  } catch (error) {
    console.error("Error creating custom role:", error);
    return { error: error.message || "Error creating custom role" };
  }
};

// Update a custom role
export const updateCustomRole = async (
  roleId: string,
  data: {
    name?: string;
    description?: string;
    permissionIds?: string[];
    workspaceIds?: string[];
  },
  tenantId: string
) => {
  try {
    const response = await fetch(`${apiUrl}/roles/${roleId}`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
        "x-tenant-id": tenantId,
      },
      body: JSON.stringify({
        ...data,
        tenantId,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Network response was not ok");
    }
    return await response.json();
  } catch (error) {
    console.error("Error updating custom role:", error);
    return { error: error.message || "Error updating custom role" };
  }
};

// Delete a custom role
export const deleteCustomRole = async (roleId: string, tenantId: string) => {
  try {
    const response = await fetch(`${apiUrl}/roles/${roleId}`, {
      method: "DELETE",
      headers: {
        "Content-Type": "application/json",
        "x-tenant-id": tenantId,
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Network response was not ok");
    }
    return await response.json();
  } catch (error) {
    console.error("Error deleting custom role:", error);
    return { error: error.message || "Error deleting custom role" };
  }
};
