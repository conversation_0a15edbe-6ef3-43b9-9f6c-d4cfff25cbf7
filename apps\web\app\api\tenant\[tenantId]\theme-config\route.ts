import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/next-auth";
import db from "@/lib/shared-db";
import { ThemeConfig } from "@/types/theme-config";
import { broadcastThemeUpdate } from "@/lib/websocket/notification-broadcaster";

export async function GET(
  _request: NextRequest,
  { params }: { params: { tenantId: string } },
) {
  try {
    // Check if partner console is enabled
    const isPartnerConsole = process.env.NEXT_PUBLIC_IS_PARTNER_CONSOLE === "1";
    if (!isPartnerConsole) {
      return NextResponse.json(
        {
          error:
            "Theme configuration is only available in Partner Console mode",
        },
        { status: 403 },
      );
    }

    const session: any = await getServerSession(authOptions);
    if (!session?.userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { tenantId } = params;

    // Check if user has access to this tenant
    const membership = await db.membership.findFirst({
      where: {
        userId: session.userId,
        tenantId: tenantId,
      },
      include: {
        tenant: true,
      },
    });

    if (!membership) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const themeConfiguration = await db.themeConfiguration.findUnique({
      where: { tenantId: tenantId },
      include: {
        tenant: true,
      },
    });

    if (!themeConfiguration) {
      return NextResponse.json(
        { error: "Theme configuration not found" },
        { status: 404 },
      );
    }

    const themeConfig: ThemeConfig = {
      brandName: themeConfiguration.brandName,
      logoUrl: themeConfiguration.logoUrl,
      faviconUrl: themeConfiguration.faviconUrl,
      fullAppLogoUrl: themeConfiguration.fullAppLogoUrl,



      // Light theme color fields
      lightPrimaryColor: themeConfiguration.lightPrimaryColor,
      lightSecondaryColor: themeConfiguration.lightSecondaryColor,
      lightAccentColor: themeConfiguration.lightAccentColor,
      lightNavigationBackgroundColor: themeConfiguration.lightNavigationBackgroundColor,
      lightContentBackgroundColor: themeConfiguration.lightContentBackgroundColor,
      lightForegroundColor: themeConfiguration.lightForegroundColor,

      // Dark theme color fields
      darkPrimaryColor: themeConfiguration.darkPrimaryColor,
      darkSecondaryColor: themeConfiguration.darkSecondaryColor,
      darkAccentColor: themeConfiguration.darkAccentColor,
      darkNavigationBackgroundColor: themeConfiguration.darkNavigationBackgroundColor,
      darkContentBackgroundColor: themeConfiguration.darkContentBackgroundColor,
      darkForegroundColor: themeConfiguration.darkForegroundColor,

      themePreset: themeConfiguration.themePreset as any,
    };

    return NextResponse.json({ themeConfig });
  } catch (error) {
    console.error("Error fetching theme config:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { tenantId: string } },
) {
  try {
    // Check if partner console is enabled
    const isPartnerConsole = process.env.NEXT_PUBLIC_IS_PARTNER_CONSOLE === "1";
    if (!isPartnerConsole) {
      return NextResponse.json(
        {
          error:
            "Theme configuration is only available in Partner Console mode",
        },
        { status: 403 },
      );
    }

    const session: any = await getServerSession(authOptions);
    if (!session?.userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { tenantId } = params;

    // Check if user has admin access to this tenant
    const membership = await db.membership.findFirst({
      where: {
        userId: session.userId,
        tenantId: tenantId,
        role: {
          in: ["ADMIN", "OWNER"],
        },
      },
    });

    if (!membership) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const body = await request.json();

    // Filter out deprecated fields to prevent database errors
    const { logoUrlDark, backgroundColor, ...filteredBody } = body;
    const themeConfig: Partial<ThemeConfig> = filteredBody;

    // Validate light theme color fields
    const lightColorFields = [
      { field: 'lightPrimaryColor', name: 'light primary color' },
      { field: 'lightSecondaryColor', name: 'light secondary color' },
      { field: 'lightAccentColor', name: 'light accent color' },
      { field: 'lightNavigationBackgroundColor', name: 'light navigation background color' },
      { field: 'lightContentBackgroundColor', name: 'light content background color' },
      { field: 'lightForegroundColor', name: 'light foreground color' }
    ];

    for (const { field, name } of lightColorFields) {
      const colorValue = (themeConfig as any)[field];
      if (colorValue && !isValidHexColor(colorValue)) {
        return NextResponse.json(
          { error: `Invalid ${name} format` },
          { status: 400 }
        );
      }
    }

    // Validate dark theme color fields
    const darkColorFields = [
      { field: 'darkPrimaryColor', name: 'dark primary color' },
      { field: 'darkSecondaryColor', name: 'dark secondary color' },
      { field: 'darkAccentColor', name: 'dark accent color' },
      { field: 'darkNavigationBackgroundColor', name: 'dark navigation background color' },
      { field: 'darkContentBackgroundColor', name: 'dark content background color' },
      { field: 'darkForegroundColor', name: 'dark foreground color' }
    ];

    for (const { field, name } of darkColorFields) {
      const colorValue = (themeConfig as any)[field];
      if (colorValue && !isValidHexColor(colorValue)) {
        return NextResponse.json(
          { error: `Invalid ${name} format` },
          { status: 400 }
        );
      }
    }



    // Update or create theme configuration
    const updatedThemeConfig = await db.themeConfiguration.upsert({
      where: { tenantId: tenantId },
      create: {
        tenantId: tenantId,
        brandName: themeConfig.brandName,
        logoUrl: themeConfig.logoUrl,
        faviconUrl: themeConfig.faviconUrl,
        fullAppLogoUrl: themeConfig.fullAppLogoUrl,

        // Light theme color fields
        lightPrimaryColor: themeConfig.lightPrimaryColor,
        lightSecondaryColor: themeConfig.lightSecondaryColor,
        lightAccentColor: themeConfig.lightAccentColor,
        lightNavigationBackgroundColor: themeConfig.lightNavigationBackgroundColor,
        lightContentBackgroundColor: themeConfig.lightContentBackgroundColor,
        lightForegroundColor: themeConfig.lightForegroundColor,
        // Dark theme color fields
        darkPrimaryColor: themeConfig.darkPrimaryColor,
        darkSecondaryColor: themeConfig.darkSecondaryColor,
        darkAccentColor: themeConfig.darkAccentColor,
        darkNavigationBackgroundColor: themeConfig.darkNavigationBackgroundColor,
        darkContentBackgroundColor: themeConfig.darkContentBackgroundColor,
        darkForegroundColor: themeConfig.darkForegroundColor,
        themePreset: themeConfig.themePreset,
        isActive: true,
        version: 1,
      },
      update: {
        brandName: themeConfig.brandName,
        logoUrl: themeConfig.logoUrl,
        faviconUrl: themeConfig.faviconUrl,
        fullAppLogoUrl: themeConfig.fullAppLogoUrl,

        // Light theme color fields
        lightPrimaryColor: themeConfig.lightPrimaryColor,
        lightSecondaryColor: themeConfig.lightSecondaryColor,
        lightAccentColor: themeConfig.lightAccentColor,
        lightNavigationBackgroundColor: themeConfig.lightNavigationBackgroundColor,
        lightContentBackgroundColor: themeConfig.lightContentBackgroundColor,
        lightForegroundColor: themeConfig.lightForegroundColor,
        // Dark theme color fields
        darkPrimaryColor: themeConfig.darkPrimaryColor,
        darkSecondaryColor: themeConfig.darkSecondaryColor,
        darkAccentColor: themeConfig.darkAccentColor,
        darkNavigationBackgroundColor: themeConfig.darkNavigationBackgroundColor,
        darkContentBackgroundColor: themeConfig.darkContentBackgroundColor,
        darkForegroundColor: themeConfig.darkForegroundColor,
        themePreset: themeConfig.themePreset,
        updatedAt: new Date(),
        version: { increment: 1 },
      },
    });

    const responseThemeConfig: ThemeConfig = {
      brandName: updatedThemeConfig.brandName,
      logoUrl: updatedThemeConfig.logoUrl,
      faviconUrl: updatedThemeConfig.faviconUrl,
      fullAppLogoUrl: updatedThemeConfig.fullAppLogoUrl,

      // Light theme color fields
      lightPrimaryColor: updatedThemeConfig.lightPrimaryColor,
      lightSecondaryColor: updatedThemeConfig.lightSecondaryColor,
      lightAccentColor: updatedThemeConfig.lightAccentColor,
      lightNavigationBackgroundColor: updatedThemeConfig.lightNavigationBackgroundColor,
      lightContentBackgroundColor: updatedThemeConfig.lightContentBackgroundColor,
      lightForegroundColor: updatedThemeConfig.lightForegroundColor,
      // Dark theme color fields
      darkPrimaryColor: updatedThemeConfig.darkPrimaryColor,
      darkSecondaryColor: updatedThemeConfig.darkSecondaryColor,
      darkAccentColor: updatedThemeConfig.darkAccentColor,
      darkNavigationBackgroundColor: updatedThemeConfig.darkNavigationBackgroundColor,
      darkContentBackgroundColor: updatedThemeConfig.darkContentBackgroundColor,
      darkForegroundColor: updatedThemeConfig.darkForegroundColor,
      themePreset: updatedThemeConfig.themePreset as any,
    };

    // Broadcast theme update to all tenant members via WebSocket
    try {
      await broadcastThemeUpdate({
        type: "THEME_UPDATED",
        tenantId: tenantId,
        themeConfig: responseThemeConfig,
        updatedBy: session.userId,
        timestamp: new Date().toISOString(),
      });
    } catch (broadcastError) {
      console.error("Failed to broadcast theme update:", broadcastError);
      // Don't fail the request if broadcast fails
    }

    return NextResponse.json({
      success: true,
      message: "Theme configuration updated successfully",
      themeConfig: responseThemeConfig,
    });
  } catch (error) {
    console.error("Error updating theme config:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}

export async function DELETE(
  _request: NextRequest,
  { params }: { params: { tenantId: string } },
) {
  try {
    // Check if partner console is enabled
    const isPartnerConsole = process.env.NEXT_PUBLIC_IS_PARTNER_CONSOLE === "1";
    if (!isPartnerConsole) {
      return NextResponse.json(
        {
          error:
            "Theme configuration is only available in Partner Console mode",
        },
        { status: 403 },
      );
    }

    const session: any = await getServerSession(authOptions);
    if (!session?.userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { tenantId } = params;

    // Check if user has admin access to this tenant
    const membership = await db.membership.findFirst({
      where: {
        userId: session.userId,
        tenantId: tenantId,
        role: {
          in: ["ADMIN", "OWNER"],
        },
      },
    });

    if (!membership) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    // Reset theme configuration to default values instead of deleting
    const resetThemeConfig = await db.themeConfiguration.upsert({
      where: { tenantId: tenantId },
      create: {
        tenantId: tenantId,
        brandName: "Swiss Knowledge Hub", // Use hardcoded default brand name
        logoUrl: null,
        faviconUrl: null,
        fullAppLogoUrl: null,
        primaryColor: null,
        secondaryColor: null,
        accentColor: null,
        navigationBackgroundColor: null,
        contentBackgroundColor: null,
        foregroundColor: null,
        themePreset: "light",
        isActive: true,
        version: 1,
      },
      update: {
        brandName: "Swiss Knowledge Hub", // Use hardcoded default brand name
        logoUrl: null,
        faviconUrl: null,
        fullAppLogoUrl: null,
        primaryColor: null,
        secondaryColor: null,
        accentColor: null,
        navigationBackgroundColor: null,
        contentBackgroundColor: null,
        foregroundColor: null,
        themePreset: "light",
        updatedAt: new Date(),
        version: { increment: 1 },
      },
    });

    // Broadcast theme update to all tenant members
    try {
      await broadcastThemeUpdate({
        type: "THEME_UPDATED",
        tenantId: tenantId,
        themeConfig: resetThemeConfig,
        updatedBy: session.userId,
        timestamp: new Date().toISOString(),
      });
    } catch (broadcastError) {
      console.error("Failed to broadcast theme reset:", broadcastError);
      // Don't fail the request if broadcast fails
    }

    return NextResponse.json({
      success: true,
      message: "Theme configuration reset to default",
      themeConfig: resetThemeConfig,
    });
  } catch (error) {
    console.error("Error resetting theme config:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}

function isValidHexColor(color: string): boolean {
  const hexColorRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;
  return hexColorRegex.test(color);
}
