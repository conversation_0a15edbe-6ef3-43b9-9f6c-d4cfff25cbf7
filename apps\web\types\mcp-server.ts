export interface MCPServer {
  id: string;
  name: string;
  description?: string;
  server_type: MCPServerType;
  command?: string; // Required for stdio servers
  args?: string[];
  env?: Record<string, string>;
  url?: string; // Required for http servers
  headers?: Record<string, string>; // For http servers (e.g., Authorization)
  status: MCPServerStatus;
  last_error?: string;
  timeout: number;
  auto_restart: boolean;
  is_public: boolean;
  user_id: string;
  tenant_id: string;
  created_at: string;
  updated_at: string;
}

export type MCPServerStatus = "ACTIVE" | "INACTIVE" | "ERROR" | "TESTING";
export type MCPServerType = "STDIO" | "HTTP";

export interface MCPServerCreate {
  name: string;
  description?: string;
  server_type: MCPServerType;
  command?: string; // Required for stdio servers
  args?: string[];
  env?: Record<string, string>;
  url?: string; // Required for http servers
  headers?: Record<string, string>; // For http servers
  timeout?: number;
  auto_restart?: boolean;
  is_public?: boolean;
}

export interface MCPServerUpdate {
  name?: string;
  description?: string;
  server_type?: MCPServerType;
  command?: string;
  args?: string[];
  env?: Record<string, string>;
  url?: string;
  headers?: Record<string, string>;
  timeout?: number;
  auto_restart?: boolean;
  is_public?: boolean;
  status?: MCPServerStatus;
}

export interface MCPServerTool {
  name: string;
  description?: string;
  input_schema?: {
    type: string;
    properties?: Record<string, any>;
    required?: string[];
  };
}

export interface MCPServerToolsResponse {
  server_id: string;
  server_name: string;
  tools_count: number;
  tools: MCPServerTool[];
}

export interface MCPServerTestResponse {
  message: string;
  status: MCPServerStatus;
  tools_count: number;
  tools: MCPServerTool[];
}

// MCPServerActionResponse removed - manual start/stop functionality removed
