{"name": "swiss-knowledge-hub", "private": true, "scripts": {"predev": "pnpm --filter web run prisma:generate", "db-push": "pnpm --filter web run prisma:db-push", "db-format": "pnpm --filter web run prisma:db-format", "generate": "pnpm --filter web run prisma:generate", "build": "NODE_OPTIONS='--max-old-space-size=8192' turbo build", "dev": "turbo dev", "lint": "turbo lint", "format": "prettier --write \"**/*.{ts,tsx,md}\""}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@types/bcrypt": "^5.0.2", "prettier": "^3.1.1", "turbo": "^1.12.4"}, "packageManager": "pnpm@8.9.0", "engines": {"node": ">=18.17.0"}}