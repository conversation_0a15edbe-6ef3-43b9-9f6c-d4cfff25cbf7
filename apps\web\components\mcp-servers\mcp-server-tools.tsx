"use client";

import { useState, useEffect } from "react";
import { MCPServerTool, MCPServerToolsResponse } from "@/types/mcp-server";
import { useMCPServers } from "@/hooks/use-mcp-servers";
import { useLanguage } from "@/lib/language-context";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import * as Collapsible from "@radix-ui/react-collapsible";
import {
  ChevronDown,
  ChevronRight,
  RefreshCw,
  AlertCircle,
  CheckCircle,
  Code,
  FileText,
  Hammer,
} from "lucide-react";

interface MCPServerToolsProps {
  serverId: string;
  serverName: string;
  open: boolean;
  setOpen: (open: any) => void;
}

export function MCPServerTools({
  serverId,
  serverName,
  open,
  setOpen,
}: MCPServerToolsProps) {
  const { t } = useLanguage();
  const { getServerTools } = useMCPServers();
  const [toolsData, setToolsData] = useState<MCPServerTool[] | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [expandedTools, setExpandedTools] = useState<Set<string>>(new Set());

  const fetchTools = async () => {
    if (!serverId) return;

    setLoading(true);
    setError(null);

    try {
      const data = await getServerTools(serverId);
      setToolsData(data);
    } catch (err: any) {
      setError(err.message || "Failed to fetch tools");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (open && serverId) {
      fetchTools();
    }
  }, [open, serverId]);

  const toggleToolExpanded = (toolName: string) => {
    const newExpanded = new Set(expandedTools);
    if (newExpanded.has(toolName)) {
      newExpanded.delete(toolName);
    } else {
      newExpanded.add(toolName);
    }
    setExpandedTools(newExpanded);
  };

  const renderToolSchema = (tool: MCPServerTool) => {
    console.log(tool);
    if (!tool.input_schema) {
      return (
        <div className="text-muted-foreground">
{t("mcpServers.noInputSchema")}
        </div>
      );
    }

    const { properties, required = [] } = tool.input_schema;

    if (!properties || Object.keys(properties).length === 0) {
      return (
        <div className="text-sm text-gray-400 dark:text-gray-500 italic">
{t("mcpServers.noParametersRequired")}
        </div>
      );
    }

    return (
      <div className="space-y-3">
        <h5 className="text-sm font-medium text-gray-700 dark:text-gray-200">Parameters:</h5>
        <div className="space-y-2">
          {Object.entries(properties).map(
            ([paramName, paramInfo]: [string, any]) => (
              <div key={paramName} className="border rounded-md p-3 bg-gray-50 dark:bg-gray-800 dark:border-gray-600">
                <div className="flex items-center gap-2 mb-1">
                  <code className="text-sm font-mono bg-white dark:bg-gray-900 dark:border-gray-600 dark:text-gray-100 px-2 py-1 rounded border">
                    {paramName}
                  </code>
                  <Badge
                    variant={
                      required.includes(paramName) ? "default" : "secondary"
                    }
                  >
                    {required.includes(paramName) ? "Required" : "Optional"}
                  </Badge>
                  {paramInfo.type && (
                    <Badge variant="outline">{paramInfo.type}</Badge>
                  )}
                </div>
                {paramInfo.description && (
                  <p className="text-sm text-gray-400 dark:text-gray-400 mt-1">
                    {paramInfo.description}
                  </p>
                )}
                {paramInfo.enum && (
                  <div className="mt-2">
                    <span className="text-xs text-gray-400 dark:text-gray-500">
                      Allowed values:{" "}
                    </span>
                    <code className="text-xs dark:text-gray-200">{paramInfo.enum.join(", ")}</code>
                  </div>
                )}
              </div>
            )
          )}
        </div>
      </div>
    );
  };

  return (
    <>
      <Dialog open={open} onOpenChange={() => setOpen({ open: false })}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-hidden flex flex-col">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Hammer className="h-5 w-5" />
{serverName} - {t("mcpServers.availableTools")}
            </DialogTitle>
          </DialogHeader>

          <div className="flex-1 overflow-auto">
            {loading && (
              <div className="flex items-center justify-center py-12">
                <RefreshCw className="h-6 w-6 animate-spin text-blue-500" />
                <span className="text-muted-foreground">{t("mcpServers.loadingTools")}</span>
              </div>
            )}

            {error && (
              <Card className="border-red-200 bg-red-50">
                <CardContent className="p-6">
                  <div className="flex items-center gap-2 text-red-700">
                    <AlertCircle className="h-5 w-5" />
                    <span className="font-medium">Error loading tools</span>
                  </div>
                  <p className="text-red-600 mt-2">{error}</p>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={fetchTools}
                    className="mt-3"
                  >
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Retry
                  </Button>
                </CardContent>
              </Card>
            )}

            {toolsData && !loading && !error && (
              <div className="space-y-4">
                {/* Summary */}
                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center gap-4">
                      <div className="flex items-center gap-2">
                        <CheckCircle className="h-5 w-5 text-green-500" />
                        <span className="font-medium">{t("mcpServers.serverConnected")}</span>
                      </div>
                      <Badge variant="outline">
{t("mcpServers.toolsAvailable", { count: toolsData?.length || 0 })}
                      </Badge>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={fetchTools}
                        className="ml-auto"
                      >
                        <RefreshCw className="h-4 w-4 mr-2" />
{t("mcpServers.refresh")}
                      </Button>
                    </div>
                  </CardContent>
                </Card>

                {/* Tools List */}
                {toolsData?.length === 0 ? (
                  <Card>
                    <CardContent className="p-8 text-center">
                      <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                      <h3 className="text-lg font-medium text-gray-900 mb-2">
                        No Tools Available
                      </h3>
                      <p className="text-gray-400 dark:text-gray-500">
                        This MCP server doesn't expose any tools.
                      </p>
                    </CardContent>
                  </Card>
                ) : (
                  <div className="space-y-3">
                    {toolsData?.map((tool, index) => (
                      <Card key={`${tool.name}-${index}`}>
                        <Collapsible.Root
                          open={expandedTools.has(tool.name)}
                          onOpenChange={() => toggleToolExpanded(tool.name)}
                        >
                          <Collapsible.Trigger asChild>
                            <CardHeader className="cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 rounded-md border p-4 transition-colors">
                              <div className="flex items-center justify-between">
                                <div className="flex items-center gap-3">
                                  <div className="flex items-center gap-2">
                                    {expandedTools.has(tool.name) ? (
                                      <ChevronDown className="h-4 w-4" />
                                    ) : (
                                      <ChevronRight className="h-4 w-4" />
                                    )}
                                    <Code className="h-4 w-4 text-blue-500" />
                                  </div>
                                  <div>
                                    <CardTitle className="text-base">
                                      {tool.name}
                                    </CardTitle>
                                    {tool.description && (
                                      <p className="text-sm text-gray-400 dark:text-gray-400 mt-1">
                                        {tool.description}
                                      </p>
                                    )}
                                  </div>
                                </div>
                                <Badge variant="outline">{t("mcpServers.tool")}</Badge>
                              </div>
                            </CardHeader>
                          </Collapsible.Trigger>

                          <Collapsible.Content>
                            <CardContent className="pt-0">
                              <Separator className="mb-4" />
                              {renderToolSchema(tool)}
                            </CardContent>
                          </Collapsible.Content>
                        </Collapsible.Root>
                      </Card>
                    ))}
                  </div>
                )}
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}
