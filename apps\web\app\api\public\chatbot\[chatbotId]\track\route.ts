import { NextRequest, NextResponse } from "next/server";
import db from "@/lib/shared-db";
import { collectUserTrackingData } from "@/lib/services/user-tracking";
import {
  applyCORSHeaders,
  handleCORSPreflight,
  createChatbotCORSConfig,
} from "@/lib/security/cors";

interface TrackingRequest {
  eventType: 'page_view' | 'session_start' | 'session_end' | 'user_action' | 'error';
  sessionToken?: string;
  eventData?: {
    action?: string;
    value?: string | number;
    category?: string;
    label?: string;
    error?: {
      message: string;
      stack?: string;
      code?: string;
    };
  };
  userTrackingData?: any;
}

interface TrackingResponse {
  success: boolean;
  message?: string;
  sessionToken?: string;
}

// Handle CORS preflight
export async function OPTIONS(request: NextRequest) {
  return handleCORSPreflight(request);
}

export async function POST(
  request: NextRequest,
  { params }: { params: { chatbotId: string } }
) {
  try {
    const { chatbotId } = params;
    const body: TrackingRequest = await request.json();
    const { eventType, sessionToken, eventData, userTrackingData } = body;

    // Validate required fields
    if (!eventType || !chatbotId) {
      const response = NextResponse.json(
        { error: "Missing required fields: eventType, chatbotId" },
        { status: 400 }
      );
      return applyCORSHeaders(response, request, createChatbotCORSConfig({}));
    }

    // Get API key from Authorization header
    const authHeader = request.headers.get("authorization");
    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      const response = NextResponse.json(
        { error: "Missing or invalid API key" },
        { status: 401 }
      );
      return applyCORSHeaders(response, request, createChatbotCORSConfig({}));
    }

    const apiKey = authHeader.substring(7);

    // Find chatbot by API key
    const chatbot = await db.chatbot.findFirst({
      where: {
        apiKey: apiKey,
        id: chatbotId,
        isActive: true,
      },
    });

    if (!chatbot) {
      const response = NextResponse.json(
        { error: "Invalid API key or chatbot not found" },
        { status: 404 }
      );
      return applyCORSHeaders(response, request, createChatbotCORSConfig({}));
    }

    // Collect comprehensive user tracking data
    const trackingData = await collectUserTrackingData(request, userTrackingData);

    let response: TrackingResponse;

    switch (eventType) {
      case 'session_start':
        // Create or update session with tracking data
        const newSessionToken = sessionToken || crypto.randomUUID();
        
        await db.chatbotSession.upsert({
          where: { sessionToken: newSessionToken },
          create: {
            sessionToken: newSessionToken,
            chatbotId: chatbot.id,
            tenantId: chatbot.tenantId, // Add tenantId for multi-tenant isolation

            // Basic session metadata
            domain: trackingData.domain,
            userAgent: trackingData.userAgent,
            ipAddress: trackingData.ipAddress,
            referrer: trackingData.referrer,
            
            // Geographic information
            country: trackingData.country,
            city: trackingData.city,
            region: trackingData.region,
            timezone: trackingData.timezone,
            
            // Device and browser information
            deviceType: trackingData.deviceType,
            operatingSystem: trackingData.operatingSystem,
            browserName: trackingData.browserName,
            browserVersion: trackingData.browserVersion,
            screenWidth: trackingData.screenWidth,
            screenHeight: trackingData.screenHeight,
            viewportWidth: trackingData.viewportWidth,
            viewportHeight: trackingData.viewportHeight,
            
            // Network information
            connectionType: trackingData.connectionType,
            isp: trackingData.isp,
            organization: trackingData.organization,
            
            // Page and language information
            pageUrl: trackingData.pageUrl,
            pageTitle: trackingData.pageTitle,
            language: trackingData.language,
            
            // UTM parameters
            utmSource: trackingData.utmSource,
            utmMedium: trackingData.utmMedium,
            utmCampaign: trackingData.utmCampaign,
            utmTerm: trackingData.utmTerm,
            utmContent: trackingData.utmContent,
            
            // User behavior tracking
            sessionDepth: trackingData.sessionDepth || 1,
            isReturning: trackingData.isReturning || false,
            previousVisits: trackingData.previousVisits || 0,
            
            // Session tracking
            messagesCount: 0,
            startedAt: new Date(),
            lastActivityAt: new Date(),
            
            // Additional metadata
            metadata: {
              ...trackingData.metadata,
              sessionStartEvent: eventData,
            },
          },
          update: {
            lastActivityAt: new Date(),
            metadata: {
              sessionStartEvent: eventData,
            },
          },
        });

        response = {
          success: true,
          message: "Session tracking started",
          sessionToken: newSessionToken,
        };
        break;

      case 'session_end':
        if (!sessionToken) {
          const response = NextResponse.json(
            { error: "Session token required for session_end event" },
            { status: 400 }
          );
          return applyCORSHeaders(response, request, createChatbotCORSConfig(chatbot));
        }

        const sessionToEnd = await db.chatbotSession.findFirst({
          where: {
            sessionToken,
            tenantId: chatbot.tenantId, // Ensure tenant isolation
          },
        });

        if (sessionToEnd && sessionToEnd.chatbotId === chatbot.id) {
          const duration = sessionToEnd.startedAt 
            ? Math.floor((new Date().getTime() - sessionToEnd.startedAt.getTime()) / 1000)
            : 0;

          await db.chatbotSession.update({
            where: { sessionToken },
            data: {
              endedAt: new Date(),
              duration: duration,
              metadata: {
                ...(sessionToEnd.metadata as any),
                sessionEndEvent: eventData,
              },
            },
          });
        }

        response = {
          success: true,
          message: "Session tracking ended",
        };
        break;

      case 'page_view':
        // Update session with new page view data
        if (sessionToken) {
          await db.chatbotSession.updateMany({
            where: {
              sessionToken,
              chatbotId: chatbot.id,
            },
            data: {
              pageUrl: trackingData.pageUrl,
              pageTitle: trackingData.pageTitle,
              sessionDepth: trackingData.sessionDepth || 1,
              lastActivityAt: new Date(),
              metadata: {
                pageViewEvent: eventData,
                lastPageView: new Date().toISOString(),
              },
            },
          });
        }

        response = {
          success: true,
          message: "Page view tracked",
        };
        break;

      case 'user_action':
        // Track user actions (clicks, form submissions, etc.)
        if (sessionToken) {
          const session = await db.chatbotSession.findUnique({
            where: { sessionToken },
          });

          if (session && session.chatbotId === chatbot.id) {
            const currentMetadata = (session.metadata as any) || {};
            const userActions = currentMetadata.userActions || [];
            
            userActions.push({
              ...eventData,
              timestamp: new Date().toISOString(),
            });

            await db.chatbotSession.update({
              where: { sessionToken },
              data: {
                lastActivityAt: new Date(),
                metadata: {
                  ...currentMetadata,
                  userActions,
                },
              },
            });
          }
        }

        response = {
          success: true,
          message: "User action tracked",
        };
        break;

      case 'error':
        // Track errors
        if (sessionToken) {
          await db.chatbotSession.updateMany({
            where: {
              sessionToken,
              chatbotId: chatbot.id,
            },
            data: {
              errorCount: {
                increment: 1,
              },
              lastActivityAt: new Date(),
              metadata: {
                lastError: {
                  ...eventData?.error,
                  timestamp: new Date().toISOString(),
                },
              },
            },
          });
        }

        response = {
          success: true,
          message: "Error tracked",
        };
        break;

      default:
        const errorResponse = NextResponse.json(
          { error: "Invalid event type" },
          { status: 400 }
        );
        return applyCORSHeaders(errorResponse, request, createChatbotCORSConfig(chatbot));
    }

    const nextResponse = NextResponse.json(response);
    return applyCORSHeaders(nextResponse, request, createChatbotCORSConfig(chatbot));

  } catch (error) {
    console.error("Error tracking user event:", error);
    const response = NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
    return applyCORSHeaders(response, request, createChatbotCORSConfig({}));
  }
}
