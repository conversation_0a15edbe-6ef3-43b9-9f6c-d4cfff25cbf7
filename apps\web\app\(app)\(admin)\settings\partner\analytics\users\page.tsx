"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTit<PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Users,
  MessageSquare,
  Clock,
  Globe,
  Shield,
  AlertTriangle,
  Search,
  Download,
  Eye,
  Plus,
  X,
} from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { getCookie } from "@/utils/cookies";
import { use<PERSON><PERSON><PERSON> } from "next/navigation";
import { useLanguage } from "@/lib/language-context";
import toast from "react-hot-toast";

interface UserAnalytics {
  userKey: string;
  userId?: string;
  name?: string;
  email?: string;
  totalSessions: number;
  totalMessages: number;
  domains: string[];
  userAgents: string[];
  ipAddresses: string[];
  chatbots: string[];
  firstSeen: string;
  lastSeen: string;
  averageDuration: number;
  averageSatisfaction?: number;
  errorRate: number;
  sessions: any[];
}

interface AnalyticsOverview {
  totalUsers: number;
  totalSessions: number;
  totalMessages: number;
  averageSessionsPerUser: number;
  averageMessagesPerUser: number;
  topDomains: Array<{ item: string; count: number }>;
  topUserAgents: Array<{ item: string; count: number }>;
  topCountries: Array<{ item: string; count: number }>;
}

export default function UserAnalyticsPage() {
  const { t } = useLanguage();
  const router = useRouter();
  const [users, setUsers] = useState<UserAnalytics[]>([]);
  const [overview, setOverview] = useState<AnalyticsOverview | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [timeRange, setTimeRange] = useState("all");
  const [selectedChatbot, setSelectedChatbot] = useState<string>("all");
  const [page, setPage] = useState(1);
  const [selectedUser, setSelectedUser] = useState<UserAnalytics | null>(null);
  const [exportLoading, setExportLoading] = useState(false);
  const [showRestrictions, setShowRestrictions] = useState(false);
  const [restrictions, setRestrictions] = useState({
    blockedIPs: [] as string[],
    blockedEmails: [] as string[],
    blockedDomains: [] as string[],
    blockedUserAgents: [] as string[],
    allowedIPs: [] as string[],
    allowedEmails: [] as string[],
    allowedDomains: [] as string[],
    allowedUserAgents: [] as string[],
    requireAuth: false,
    rateLimits: {
      messagesPerMinute: 10,
      messagesPerHour: 100,
      messagesPerDay: 500,
    },
    geoRestrictions: {
      allowedCountries: [] as string[],
      blockedCountries: [] as string[],
    },
  });
  const [newRestriction, setNewRestriction] = useState("");
  const [restrictionType, setRestrictionType] = useState("blockedIPs");

  const tenantId = getCookie("currentOrganizationId");

  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      setError(null);
      try {
        const params = new URLSearchParams({
          tenantId: tenantId || "",
          timeRange,
          page: page.toString(),
          limit: "50",
          ...(selectedChatbot !== "all" && { chatbotId: selectedChatbot }),
          ...(searchTerm && { search: searchTerm }),
        });

        const response = await fetch(`/api/analytics/users?${params}`);
        if (!response.ok) {
          throw new Error(`Failed to fetch user analytics: ${response.status}`);
        }

        const data = await response.json();
        setUsers(data.users || []);
        setOverview(data.overview || null);
      } catch (error) {
        console.error("Error loading user analytics:", error);
        setError(
          error instanceof Error
            ? error.message
            : t("analytics.failedToLoadUsers")
        );
        setUsers([]);
        setOverview(null);
      } finally {
        setLoading(false);
      }
    };

    if (tenantId) {
      loadData();
    }
  }, [timeRange, selectedChatbot, searchTerm, page, tenantId]);

  const formatDuration = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds.toFixed(0)}s`;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const getRiskLevel = (user: UserAnalytics) => {
    if (user.errorRate > 20) return "high";
    if (user.errorRate > 10 || user.ipAddresses.length > 3) return "medium";
    return "low";
  };

  const getRiskBadge = (level: string) => {
    switch (level) {
      case "high":
        return <Badge variant="destructive">{t("analytics.highRisk")}</Badge>;
      case "medium":
        return <Badge variant="secondary">{t("analytics.mediumRisk")}</Badge>;
      default:
        return <Badge variant="outline">{t("analytics.lowRisk")}</Badge>;
    }
  };

  const exportUserData = async () => {
    if (!tenantId) return;

    setExportLoading(true);
    try {
      // Use server-side export API for better performance
      const params = new URLSearchParams({
        tenantId: tenantId,
        timeRange,
        search: searchTerm,
      });

      if (selectedChatbot !== "all") {
        params.append("chatbot", selectedChatbot);
      }

      const response = await fetch(
        `/api/analytics/users/export?${params.toString()}`
      );

      if (!response.ok) {
        throw new Error(`Export failed: ${response.status}`);
      }

      // Get the CSV content
      const csvContent = await response.text();

      // Create and download file
      const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
      const link = document.createElement("a");
      const url = URL.createObjectURL(blob);

      link.setAttribute("href", url);
      link.setAttribute(
        "download",
        `user-analytics-${new Date().toISOString().split("T")[0]}.csv`
      );
      link.style.visibility = "hidden";

      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // Clean up
      URL.revokeObjectURL(url);

      toast.success("User data exported successfully");
    } catch (error) {
      console.error("Error exporting user data:", error);
      toast.error("Failed to export user data");
    } finally {
      setExportLoading(false);
    }
  };

  const addRestriction = () => {
    if (!newRestriction.trim()) return;

    setRestrictions((prev) => ({
      ...prev,
      [restrictionType]: [
        ...(prev[restrictionType as keyof typeof prev] as string[]),
        newRestriction.trim(),
      ],
    }));
    setNewRestriction("");
  };

  const removeRestriction = (type: string, value: string) => {
    setRestrictions((prev) => ({
      ...prev,
      [type]: (prev[type as keyof typeof prev] as string[]).filter(
        (item) => item !== value
      ),
    }));
  };

  const blockUser = async (
    user: UserAnalytics,
    type: "ip" | "email" | "domain"
  ) => {
    const values = {
      ip: user.ipAddresses,
      email: user.email ? [user.email] : [],
      domain: user.domains,
    };

    const restrictionField = {
      ip: "blockedIPs",
      email: "blockedEmails",
      domain: "blockedDomains",
    };

    const valuesToBlock = values[type];
    if (valuesToBlock.length === 0) return;

    setRestrictions((prev) => ({
      ...prev,
      [restrictionField[type]]: [
        ...(prev[restrictionField[type] as keyof typeof prev] as string[]),
        ...valuesToBlock,
      ],
    }));
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-4"></div>
          <span>{t("analytics.loadingUserAnalytics")}</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold">User Analytics & Monitoring</h1>
            <p className="text-muted-foreground">
              Monitor user behavior and manage access restrictions
            </p>
          </div>
        </div>
        <Card>
          <CardContent className="pt-6">
            <div className="text-center py-12">
              <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">
                Failed to Load Analytics
              </h3>
              <p className="text-muted-foreground mb-4">{error}</p>
              <Button onClick={() => window.location.reload()}>
                <Search className="h-4 w-4 mr-2" />
                Retry
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">
            {t("analytics.userAnalyticsMonitoring")}
          </h1>
          <p className="text-muted-foreground">
            {t("analytics.monitorUserBehavior")}
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={exportUserData}
            disabled={exportLoading || users.length === 0}
          >
            <Download className="h-4 w-4 mr-2" />
            {exportLoading
              ? t("analytics.exporting")
              : t("analytics.exportData")}
          </Button>
        </div>
      </div>

      {/* Overview Cards */}
      {overview && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {t("analytics.totalUsers")}
              </CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{overview.totalUsers}</div>
              <p className="text-xs text-muted-foreground">
                {overview.averageSessionsPerUser.toFixed(1)}{" "}
                {t("analytics.avgSessionsPerUser")}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {t("analytics.totalSessions")}
              </CardTitle>
              <MessageSquare className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{overview.totalSessions}</div>
              <p className="text-xs text-muted-foreground">
                {overview.totalMessages} {t("analytics.totalMessages")}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {t("analytics.topDomain")}
              </CardTitle>
              <Globe className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {overview.topDomains[0]?.item || t("analytics.notAvailable")}
              </div>
              <p className="text-xs text-muted-foreground">
                {overview.topDomains[0]?.count || 0} {t("analytics.sessions")}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {t("analytics.avgMessagesPerUser")}
              </CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {overview.averageMessagesPerUser.toFixed(1)}
              </div>
              <p className="text-xs text-muted-foreground">
                {t("analytics.perUserIn")}{" "}
                {timeRange === "all"
                  ? t("analytics.allTime")
                  : t("analytics.lastTimeRange", { timeRange })}
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Filters */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder={t("analytics.searchPlaceholder")}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-8"
                />
              </div>
            </div>
            <Select value={timeRange} onValueChange={setTimeRange}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder={t("analytics.timeRange")} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="7d">{t("analytics.sevenDays")}</SelectItem>
                <SelectItem value="30d">{t("analytics.thirtyDays")}</SelectItem>
                <SelectItem value="90d">{t("analytics.ninetyDays")}</SelectItem>
                <SelectItem value="all">{t("analytics.allTime")}</SelectItem>
              </SelectContent>
            </Select>
            <Select value={selectedChatbot} onValueChange={setSelectedChatbot}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder={t("analytics.allChatbots")} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">
                  {t("analytics.allChatbots")}
                </SelectItem>
                {/* Add chatbot options here */}
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Users Table */}
      <Card>
        <CardHeader>
          <CardTitle>{t("analytics.userActivity")}</CardTitle>
        </CardHeader>
        <CardContent>
          {users.length === 0 ? (
            <div className="text-center py-12">
              <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">
                {t("analytics.noUsersFound")}
              </h3>
              <p className="text-muted-foreground">
                {t("analytics.noUsersFoundForFilters")}
              </p>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>{t("analytics.user")}</TableHead>
                  <TableHead>{t("analytics.sessions")}</TableHead>
                  <TableHead>{t("analytics.messages")}</TableHead>
                  <TableHead>{t("analytics.domains")}</TableHead>
                  <TableHead>{t("analytics.riskLevel")}</TableHead>
                  <TableHead>{t("analytics.lastSeen")}</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {users.map((user) => (
                  <TableRow
                    key={user.userKey}
                    className="hover:bg-muted/50 cursor-pointer overflow-scroll"
                    onClick={() =>
                      router.push(
                        `/settings/partner/analytics/users/${encodeURIComponent(
                          user.userKey
                        )}`
                      )
                    }
                  >
                    <TableCell>
                      <div>
                        <div className="font-medium">
                          {user.name || user.email || t("analytics.anonymous")}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {user.email && user.name
                            ? user?.email
                            : `${user?.userKey}`}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>{user.totalSessions}</TableCell>
                    <TableCell>{user.totalMessages}</TableCell>
                    <TableCell>
                      <div className="flex flex-wrap gap-1">
                        {user.domains.slice(0, 2).map((domain) => (
                          <Badge
                            key={domain}
                            variant="outline"
                            className="text-xs"
                          >
                            {domain}
                          </Badge>
                        ))}
                        {user.domains.length > 2 && (
                          <Badge variant="outline" className="text-xs">
                            +{user.domains.length - 2}
                          </Badge>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>{getRiskBadge(getRiskLevel(user))}</TableCell>
                    <TableCell>{formatDate(user.lastSeen)}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
