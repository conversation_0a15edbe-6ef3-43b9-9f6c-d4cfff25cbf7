import { useState, useEffect, useCallback } from 'react';
import { workspaceChatService } from '@/services/workspace-chat';

interface VideoProcessingStatus {
  jobId: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  progress?: number;
  message?: string;
  results?: any;
  error?: string;
}

interface UseVideoProcessingStatusProps {
  jobId: string | null;
  enabled?: boolean;
  pollingInterval?: number;
  onComplete?: (results: any) => void;
  onError?: (error: string) => void;
}

export const useVideoProcessingStatus = ({
  jobId,
  enabled = true,
  pollingInterval = 5000, // 5 seconds
  onComplete,
  onError,
}: UseVideoProcessingStatusProps) => {
  const [status, setStatus] = useState<VideoProcessingStatus | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchStatus = useCallback(async () => {
    if (!jobId || !enabled) return;

    try {
      setIsLoading(true);
      setError(null);
      
      const response = await workspaceChatService.getVideoProcessingStatus(jobId);
      
      if (response.error) {
        setError(response.error);
        onError?.(response.error);
        return;
      }

      setStatus(response);

      // Handle completion
      if (response.status === 'completed') {
        onComplete?.(response.results);
      } else if (response.status === 'failed') {
        const errorMsg = response.error || 'Video processing failed';
        setError(errorMsg);
        onError?.(errorMsg);
      }
    } catch (err) {
      const errorMsg = err instanceof Error ? err.message : 'Failed to fetch video processing status';
      setError(errorMsg);
      onError?.(errorMsg);
    } finally {
      setIsLoading(false);
    }
  }, [jobId, enabled, onComplete, onError]);

  useEffect(() => {
    if (!jobId || !enabled) return;

    // Initial fetch
    fetchStatus();

    // Set up polling only if status is not completed or failed
    const shouldPoll = status?.status === 'pending' || status?.status === 'processing' || !status;
    
    if (shouldPoll) {
      const interval = setInterval(fetchStatus, pollingInterval);
      return () => clearInterval(interval);
    }
  }, [fetchStatus, pollingInterval, status?.status]);

  const isProcessing = status?.status === 'pending' || status?.status === 'processing';
  const isCompleted = status?.status === 'completed';
  const isFailed = status?.status === 'failed';

  return {
    status,
    isLoading,
    error,
    isProcessing,
    isCompleted,
    isFailed,
    refetch: fetchStatus,
  };
};

// Helper function to get status display text
export const getStatusDisplayText = (status: VideoProcessingStatus | null): string => {
  if (!status) return 'Initializing...';
  
  switch (status.status) {
    case 'pending':
      return 'Queued for processing...';
    case 'processing':
      return status.message || 'Processing video...';
    case 'completed':
      return 'Processing completed';
    case 'failed':
      return status.error || 'Processing failed';
    default:
      return 'Unknown status';
  }
};

// Helper function to get progress percentage
export const getProgressPercentage = (status: VideoProcessingStatus | null): number => {
  if (!status) return 0;
  
  switch (status.status) {
    case 'pending':
      return 10;
    case 'processing':
      return status.progress || 50;
    case 'completed':
      return 100;
    case 'failed':
      return 0;
    default:
      return 0;
  }
};
