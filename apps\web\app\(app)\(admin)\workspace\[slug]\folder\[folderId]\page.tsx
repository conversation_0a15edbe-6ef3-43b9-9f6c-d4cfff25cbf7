import FolderPage from "@/components/wrapper-screens/workspace/folders-wrapper";
import { getFolder, getVectorStoreUsageSummary } from "@/services";
import { cookies } from "next/headers";
import { redirect } from "next/navigation";
import { hasPermission } from "@/lib/permissions";

export default async function Page({ params, searchParams }) {
  const { folderId, slug } = params;
  const pageId = searchParams.page || "";

  const tenantId = cookies().get("currentOrganizationId")?.value ?? "";
  const userId = cookies().get("userId")?.value ?? "";

  if (!tenantId || !userId) {
    return redirect("/sign-in");
  }

  try {
    // If user doesn't have permission, redirect to workspace page

    const [folderData, usageSummary] = await Promise.all([
      getFolder({
        workspaceSlug: slug,
        tenantId,
        folderId,
        userId,
      }),
      getVectorStoreUsageSummary(tenantId, userId),
    ]);

    if (!folderData || !folderData.folder) {
      return redirect(`/my-hub`);
    }
    return (
      <FolderPage
        tenantId={tenantId}
        permission={folderData?.permission}
        currentFolder={folderData?.folder ?? []}
        folderId={folderId}
        workspaceSlug={slug}
        pageId={pageId}
        usageSummary={usageSummary}
      />
    );
  } catch (error) {
    console.error("Error loading folder:", error);
    return redirect(`/workspace/${slug}${pageId ? `?page=${pageId}` : ""}`);
  }
}
