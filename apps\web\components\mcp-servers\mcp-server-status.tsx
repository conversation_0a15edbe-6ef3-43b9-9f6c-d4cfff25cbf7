"use client";

import { MCPServerStatus } from "@/types/mcp-server";
import { Badge } from "@/components/ui/badge";
import {
  CheckCircle,
  XCircle,
  AlertCircle,
  Clock,
  Loader2,
} from "lucide-react";
import { useLanguage } from "@/lib/language-context";

interface MCPServerStatusProps {
  status: MCPServerStatus;
  lastError?: string;
  showIcon?: boolean;
  size?: "sm" | "md" | "lg";
}

export function MCPServerStatusBadge({
  status,
  lastError,
  showIcon = true,
  size = "md",
}: MCPServerStatusProps) {
  const { t } = useLanguage();

  const getStatusConfig = (status: MCPServerStatus) => {
    switch (status) {
      case "ACTIVE":
        return {
          variant: "default" as const,
          color: "bg-blue-500",
          icon: CheckCircle,
          label: t("mcpServers.statusReady"),
          description: t("mcpServers.statusReadyDescription"),
        };
      case "INACTIVE":
        return {
          variant: "secondary" as const,
          color: "bg-gray-500",
          icon: XCircle,
          label: t("mcpServers.statusConfigured"),
          description: t("mcpServers.statusConfiguredDescription"),
        };
      case "ERROR":
        return {
          variant: "destructive" as const,
          color: "bg-red-500",
          icon: AlertCircle,
          label: t("mcpServers.statusError"),
          description: lastError || t("mcpServers.statusErrorDescription"),
        };
      case "TESTING":
        return {
          variant: "outline" as const,
          color: "bg-blue-500",
          icon: Loader2,
          label: t("mcpServers.statusTesting"),
          description: t("mcpServers.statusTestingDescription"),
        };
      default:
        return {
          variant: "secondary" as const,
          color: "bg-gray-500",
          icon: Clock,
          label: t("mcpServers.statusUnknown"),
          description: t("mcpServers.statusUnknownDescription"),
        };
    }
  };

  const config = getStatusConfig(status);
  const Icon = config.icon;

  const sizeClasses = {
    sm: "text-xs px-2 py-1",
    md: "text-sm px-2.5 py-1.5",
    lg: "text-base px-3 py-2",
  };

  const iconSizes = {
    sm: "h-3 w-3",
    md: "h-4 w-4",
    lg: "h-5 w-5",
  };

  return (
    <div className="flex items-center gap-2">
      <Badge
        variant={config.variant}
        className={`${sizeClasses[size]} flex items-center gap-1.5`}
        title={config.description}
      >
        {showIcon && (
          <Icon
            className={`${iconSizes[size]} ${
              status === "TESTING" ? "animate-spin" : ""
            }`}
          />
        )}
        {config.label}
      </Badge>

      {/* Status indicator dot */}
      <div
        className={`w-2 h-2 rounded-full ${config.color} ${
          status === "TESTING" ? "animate-pulse" : ""
        }`}
        title={config.description}
      />
    </div>
  );
}

export function MCPServerStatusIndicator({
  status,
  lastError,
  size = "md",
}: MCPServerStatusProps) {
  const config = getStatusConfig(status);
  const Icon = config.icon;

  const iconSizes = {
    sm: "h-4 w-4",
    md: "h-5 w-5",
    lg: "h-6 w-6",
  };

  function getStatusConfig(status: MCPServerStatus) {
    switch (status) {
      case "ACTIVE":
        return {
          color: "text-green-500",
          icon: CheckCircle,
          description: "Server is running and ready",
        };
      case "INACTIVE":
        return {
          color: "text-gray-500",
          icon: XCircle,
          description: "Server is stopped",
        };
      case "ERROR":
        return {
          color: "text-red-500",
          icon: AlertCircle,
          description: lastError || "Server encountered an error",
        };
      case "TESTING":
        return {
          color: "text-blue-500",
          icon: Loader2,
          description: "Server connectivity is being tested",
        };
      default:
        return {
          color: "text-gray-500",
          icon: Clock,
          description: "Server status is unknown",
        };
    }
  }

  return (
    <div className="flex items-center" title={config.description}>
      <Icon
        className={`${iconSizes[size]} ${config.color} ${
          status === "TESTING" ? "animate-spin" : ""
        }`}
      />
    </div>
  );
}
