import { NextRequest, NextResponse } from "next/server";
import db from "@/lib/shared-db";

/**
 * Public API endpoint for fetching theme configuration
 * This endpoint allows public access to theme configuration for white-labeling
 * Used by public pages like changelog, docs, and shared threads
 */
export async function GET(
  _request: NextRequest,
  { params }: { params: { tenantId: string } },
) {
  try {
    // Check if partner console is enabled
    const isPartnerConsole = process.env.NEXT_PUBLIC_IS_PARTNER_CONSOLE === "1";
    if (!isPartnerConsole) {
      return NextResponse.json(
        {
          error: "Theme configuration is only available in Partner Console mode",
        },
        { status: 403 },
      );
    }

    const { tenantId } = params;

    // Validate tenant ID format (basic validation)
    if (!tenantId || tenantId.length < 3) {
      return NextResponse.json(
        { error: "Invalid tenant ID" },
        { status: 400 },
      );
    }

    // Fetch theme configuration for the tenant
    const themeConfiguration = await db.themeConfiguration.findUnique({
      where: { tenantId: tenantId },
      select: {
        id: true,
        tenantId: true,
        brandName: true,
        logoUrl: true,
        faviconUrl: true,
        fullAppLogoUrl: true,

        // Light theme color fields
        lightPrimaryColor: true,
        lightSecondaryColor: true,
        lightAccentColor: true,
        lightNavigationBackgroundColor: true,
        lightContentBackgroundColor: true,
        lightForegroundColor: true,
        // Dark theme color fields
        darkPrimaryColor: true,
        darkSecondaryColor: true,
        darkAccentColor: true,
        darkNavigationBackgroundColor: true,
        darkContentBackgroundColor: true,
        darkForegroundColor: true,
        themePreset: true,
        isActive: true,
        version: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    if (!themeConfiguration) {
      return NextResponse.json(
        { error: "Theme configuration not found" },
        { status: 404 },
      );
    }

    // Check if theme configuration is active
    if (!themeConfiguration.isActive) {
      return NextResponse.json(
        { error: "Theme configuration is not active" },
        { status: 404 },
      );
    }

    // Return theme configuration (without sensitive data)
    return NextResponse.json({
      success: true,
      themeConfig: {
        id: themeConfiguration.id,
        tenantId: themeConfiguration.tenantId,
        brandName: themeConfiguration.brandName,
        logoUrl: themeConfiguration.logoUrl,
        faviconUrl: themeConfiguration.faviconUrl,
        fullAppLogoUrl: themeConfiguration.fullAppLogoUrl,
        primaryColor: themeConfiguration.primaryColor,
        secondaryColor: themeConfiguration.secondaryColor,
        accentColor: themeConfiguration.accentColor,
        navigationBackgroundColor: themeConfiguration.navigationBackgroundColor,
        contentBackgroundColor: themeConfiguration.contentBackgroundColor,
        foregroundColor: themeConfiguration.foregroundColor,
        themePreset: themeConfiguration.themePreset,
        version: themeConfiguration.version,
        updatedAt: themeConfiguration.updatedAt,
      },
    });
  } catch (error) {
    console.error("Error fetching public theme config:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}
