import { NextRequest, NextResponse } from "next/server";
import db from "@/lib/shared-db";

interface ChatbotStatus {
  id: string;
  name: string;
  isActive: boolean;
  status: "online" | "offline" | "maintenance";
  lastUsedAt: string | null;
  uptime: {
    percentage: number;
    totalSessions: number;
    activeSessions: number;
  };
  performance: {
    averageResponseTime: number;
    successRate: number;
  };
  limits: {
    rateLimitStatus: {
      perMinute: { used: number; limit: number };
      perHour: { used: number; limit: number };
      perDay: { used: number; limit: number };
    };
  };
  version: string;
  lastHealthCheck: string;
}

export async function GET(
  req: NextRequest,
  { params }: { params: { chatbotId: string } }
) {
  try {
    const { chatbotId } = params;
    const { searchParams } = new URL(req.url);

    // Get API key from Authorization header or query parameter
    const authHeader = req.headers.get("authorization");
    const apiKeyFromHeader = authHeader?.startsWith("Bearer ") ? authHeader.substring(7) : null;
    const apiKeyFromQuery = searchParams.get("apiKey");
    const apiKey = apiKeyFromHeader || apiKeyFromQuery;

    if (!apiKey) {
      return NextResponse.json(
        { error: "Missing API key" },
        { status: 401 }
      );
    }

    // Get chatbot
    const chatbot = await db.chatbot.findUnique({
      where: { id: chatbotId },
    });

    if (!chatbot) {
      return NextResponse.json(
        { error: "Chatbot not found" },
        { status: 404 }
      );
    }

    // Validate API key
    if (chatbot.apiKey !== apiKey) {
      return NextResponse.json(
        { error: "Invalid API key" },
        { status: 401 }
      );
    }

    // Calculate time ranges for rate limiting
    const now = new Date();
    const oneMinuteAgo = new Date(now.getTime() - 60 * 1000);
    const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
    const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);

    // Get current rate limit usage
    const minuteUsage = await db.chatbotSession.count({
      where: {
        chatbotId,
        lastActivityAt: { gte: oneMinuteAgo },
      },
    });

    const hourUsage = await db.chatbotSession.count({
      where: {
        chatbotId,
        lastActivityAt: { gte: oneHourAgo },
      },
    });

    const dayUsage = await db.chatbotSession.count({
      where: {
        chatbotId,
        lastActivityAt: { gte: oneDayAgo },
      },
    });

    // Get total sessions for uptime calculation
    const totalSessions = await db.chatbotSession.count({
      where: { chatbotId },
    });

    // Get active sessions (sessions with activity in last 30 minutes)
    const thirtyMinutesAgo = new Date(now.getTime() - 30 * 60 * 1000);
    const activeSessions = await db.chatbotSession.count({
      where: {
        chatbotId,
        lastActivityAt: { gte: thirtyMinutesAgo },
      },
    });

    // Get average response time
    const avgResponseTime = await db.chatbotSession.aggregate({
      where: {
        chatbotId,
        startedAt: { gte: oneDayAgo },
      },
      _avg: {
        averageResponseTime: true,
      },
    });

    // Calculate success rate (sessions with at least one message exchange)
    const successfulSessions = await db.chatbotSession.count({
      where: {
        chatbotId,
        messagesCount: { gt: 0 },
        startedAt: { gte: oneDayAgo },
      },
    });

    const totalDaySessions = await db.chatbotSession.count({
      where: {
        chatbotId,
        startedAt: { gte: oneDayAgo },
      },
    });

    const successRate = totalDaySessions > 0 ? (successfulSessions / totalDaySessions) * 100 : 100;

    // Determine status
    let status: "online" | "offline" | "maintenance" = "online";
    if (!chatbot.isActive) {
      status = "offline";
    } else if (chatbot.lastUsedAt && new Date(chatbot.lastUsedAt) < oneHourAgo) {
      status = "maintenance"; // No activity in last hour might indicate maintenance
    }

    // Calculate uptime percentage (simplified - based on active status)
    const uptimePercentage = chatbot.isActive ? 99.9 : 0; // Simplified calculation

    const statusResponse: ChatbotStatus = {
      id: chatbot.id,
      name: chatbot.name,
      isActive: chatbot.isActive,
      status,
      lastUsedAt: chatbot.lastUsedAt,
      uptime: {
        percentage: uptimePercentage,
        totalSessions,
        activeSessions,
      },
      performance: {
        averageResponseTime: avgResponseTime._avg.averageResponseTime || 0,
        successRate: Math.round(successRate * 100) / 100,
      },
      limits: {
        rateLimitStatus: {
          perMinute: {
            used: minuteUsage,
            limit: chatbot.rateLimitPerMinute || 60,
          },
          perHour: {
            used: hourUsage,
            limit: chatbot.rateLimitPerHour || 1000,
          },
          perDay: {
            used: dayUsage,
            limit: chatbot.rateLimitPerDay || 10000,
          },
        },
      },
      version: "1.0.0", // You can make this dynamic
      lastHealthCheck: now.toISOString(),
    };

    return NextResponse.json(statusResponse);

  } catch (error) {
    console.error("[STATUS_GET]", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// POST endpoint for health check ping
export async function POST(
  req: NextRequest,
  { params }: { params: { chatbotId: string } }
) {
  try {
    const { chatbotId } = params;

    // Get API key from Authorization header
    const authHeader = req.headers.get("authorization");
    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return NextResponse.json(
        { error: "Missing or invalid API key" },
        { status: 401 }
      );
    }

    const apiKey = authHeader.substring(7);

    // Validate chatbot and API key
    const chatbot = await db.chatbot.findUnique({
      where: { id: chatbotId },
    });

    if (!chatbot || chatbot.apiKey !== apiKey) {
      return NextResponse.json(
        { error: "Invalid chatbot or API key" },
        { status: 401 }
      );
    }

    // Update last health check time (you might want to add this field to the schema)
    await db.chatbot.update({
      where: { id: chatbotId },
      data: {
        lastUsedAt: new Date(),
      },
    });

    return NextResponse.json({
      message: "Health check successful",
      timestamp: new Date().toISOString(),
      status: chatbot.isActive ? "healthy" : "inactive",
    });

  } catch (error) {
    console.error("[STATUS_POST]", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// Handle CORS
export async function OPTIONS(req: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
      "Access-Control-Allow-Headers": "Content-Type, Authorization",
      "Access-Control-Max-Age": "86400",
    },
  });
}
