"use client";

import * as React from "react";
import { useEffect, useState } from "react";
import { ThemeProvider as NextThemesProvider, useTheme } from "next-themes";
import { type ThemeProviderProps } from "next-themes/dist/types";
import { ThemeConfig } from "@/types/theme-config";
import { generateThemeVariables } from "@/services/theme-config";

interface WhiteLabelThemeProviderProps extends ThemeProviderProps {
  tenantId?: string;
  themeConfig?: ThemeConfig;
}

export function WhiteLabelThemeProvider({
  children,
  tenantId,
  themeConfig,
  ...props
}: WhiteLabelThemeProviderProps) {
  const [mounted, setMounted] = useState(false);
  const [appliedTheme, setAppliedTheme] = useState<ThemeConfig | null>(null);

  const isPartnerConsole = process.env.NEXT_PUBLIC_IS_PARTNER_CONSOLE === "1";

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (themeConfig && mounted && isPartnerConsole) {
      applyThemeConfig(themeConfig);
      setAppliedTheme(themeConfig);
    }
  }, [themeConfig, mounted, isPartnerConsole]);

  const applyThemeConfig = (config: ThemeConfig) => {
    // Only apply theme config if partner console is enabled
    if (!isPartnerConsole) {
      return;
    }

    const root = document.documentElement;

    // Apply brand name to document title if configured
    if (config.brandName) {
      const titleElement = document.querySelector("title");
      if (titleElement) {
        const currentTitle = titleElement.textContent;
        if (currentTitle && currentTitle.includes("Swiss Knowledge Hub")) {
          titleElement.textContent = currentTitle.replace(
            "Swiss Knowledge Hub",
            config.brandName,
          );
        }
      }
    }

    // Apply favicon if configured - delegate to DynamicFaviconManager
    if (config.faviconUrl || config.brandName) {
      // Dispatch a custom event for the DynamicFaviconManager to handle
      const brandingUpdateEvent = new CustomEvent('brandingUpdate', {
        detail: { themeConfig: config }
      });
      window.dispatchEvent(brandingUpdateEvent);
      console.log('White-label theme provider: Branding update dispatched', config);
    }

    // Generate and apply CSS variables
    const themeVariables = generateThemeVariables(config);

    // Apply color variables
    Object.entries(themeVariables).forEach(([property, value]) => {
      root.style.setProperty(property, value);
    });

    // Apply theme preset (light/dark mode)
    if (config.themePreset) {
      root.setAttribute("data-theme-preset", config.themePreset);

      // Set the theme mode directly
      if (config.themePreset === "light") {
        root.classList.remove("dark");
        root.classList.add("light");
      } else if (config.themePreset === "dark") {
        root.classList.remove("light");
        root.classList.add("dark");
      }
    }
  };

  // Custom theme names for white-label
  const customThemes = React.useMemo(() => {
    const baseThemes = [
      "light",
      "dark",
      "primary",
      "primaryDark",
      "redDark",
      "redLight",
    ];

    if (appliedTheme && isPartnerConsole) {
      return [...baseThemes, "custom-light", "custom-dark"];
    }

    return baseThemes;
  }, [appliedTheme, isPartnerConsole]);

  if (!mounted) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <NextThemesProvider
      attribute="class"
      defaultTheme="system"
      enableSystem
      disableTransitionOnChange
      themes={customThemes}
      {...props}
    >
      <ThemeConfigApplier
        themeConfig={isPartnerConsole ? appliedTheme : null}
      />
      {children}
    </NextThemesProvider>
  );
}

// Component to apply theme configuration including theme mode
function ThemeConfigApplier({
  themeConfig,
}: {
  themeConfig: ThemeConfig | null;
}) {
  const isPartnerConsole = process.env.NEXT_PUBLIC_IS_PARTNER_CONSOLE === "1";
  const { setTheme } = useTheme();

  const applyThemeConfiguration = (config: ThemeConfig) => {
    // Apply theme mode (light/dark) if specified
    if (config.themePreset) {
      // Force theme application by setting it directly on the HTML element
      const root = document.documentElement;

      // Remove existing theme classes
      root.classList.remove("light", "dark");

      // Add the new theme class
      root.classList.add(config.themePreset);

      // Also use the next-themes setTheme method
      setTheme(config.themePreset);

      // Store the theme preference
      localStorage.setItem("theme", config.themePreset);
    }

    // Clean up any existing theme styles before applying new ones
    const existingStyles = document.querySelectorAll('style[id^="white-label-theme-styles"]');
    existingStyles.forEach(style => style.remove());

    const styleId = "white-label-theme-styles";
    const styleElement = document.createElement("style");
    styleElement.id = styleId;
    document.head.appendChild(styleElement);

    // Generate theme-specific CSS
    const css = generateThemeSpecificCSS(config);
    styleElement.textContent = css;
  };

  useEffect(() => {
    if (!themeConfig || !isPartnerConsole) return;

    applyThemeConfiguration(themeConfig);

    return () => {
      const styleId = "white-label-theme-styles";
      const el = document.getElementById(styleId);
      if (el) {
        el.remove();
      }
    };
  }, [themeConfig, isPartnerConsole, setTheme]);

  // Listen for real-time theme updates
  useEffect(() => {
    if (!isPartnerConsole) return;

    const handleRealtimeThemeUpdate = (event: CustomEvent) => {
      const themeData = event.detail;
      console.log("Theme config applier received real-time update:", themeData);

      if (themeData?.themeConfig) {
        applyThemeConfiguration(themeData.themeConfig);
      }
    };

    window.addEventListener("realtimeThemeUpdate", handleRealtimeThemeUpdate as EventListener);

    return () => {
      window.removeEventListener("realtimeThemeUpdate", handleRealtimeThemeUpdate as EventListener);
    };
  }, [isPartnerConsole]);

  return null;
}

// Favicon updates are now handled by DynamicFaviconManager

function generateThemeSpecificCSS(config: ThemeConfig): string {
  let css = "";

  // Add theme-specific customizations based on colors
  if (config.primaryColor || config.secondaryColor || config.accentColor) {
    css += `
      /* Custom brand colors applied */
      .brand-primary {
        color: ${config.primaryColor || "hsl(var(--primary))"};
      }

      .brand-secondary {
        color: ${config.secondaryColor || "hsl(var(--secondary))"};
      }

      .brand-accent {
        color: ${config.accentColor || "hsl(var(--accent))"};
      }
    `;
  }

  // Custom scrollbar styles with brand colors
  css += `
    ::-webkit-scrollbar {
      width: 8px;
    }

    ::-webkit-scrollbar-track {
      background: hsl(var(--muted));
    }

    ::-webkit-scrollbar-thumb {
      background: ${config.accentColor || "hsl(var(--accent))"};
      border-radius: 4px;
    }

    ::-webkit-scrollbar-thumb:hover {
      background: ${config.primaryColor || "hsl(var(--primary))"};
    }
  `;

  return css;
}

// Hook to access theme configuration
export function useWhiteLabelTheme() {
  const [themeConfig, setThemeConfig] = useState<ThemeConfig | null>(null);
  const isPartnerConsole = process.env.NEXT_PUBLIC_IS_PARTNER_CONSOLE === "1";

  const updateThemeConfig = (config: ThemeConfig) => {
    if (isPartnerConsole) {
      setThemeConfig(config);
    }
  };

  const resetThemeConfig = () => {
    if (isPartnerConsole) {
      setThemeConfig(null);

      // Reset document modifications
      const root = document.documentElement;
      root.removeAttribute("data-theme-preset");
      root.classList.remove("light", "dark");

      // Remove custom styles
      const themeStyleElement = document.getElementById(
        "white-label-theme-styles",
      );
      if (themeStyleElement) {
        themeStyleElement.remove();
      }
    }
  };

  return {
    themeConfig: isPartnerConsole ? themeConfig : null,
    updateThemeConfig,
    resetThemeConfig,
    isPartnerConsole,
  };
}
