"use client";

import toast from "react-hot-toast";
import { useLanguage } from "@/lib/language-context";

export const useTranslatedToast = () => {
  const { t } = useLanguage();

  // Common API error message mappings
  const translateApiError = (errorMessage: string): string => {
    // Common error patterns that should be translated
    const errorMappings: Record<string, string> = {
      EMAIL_NOT_VERIFIED: "auth.emailNotVerified",
      PASSWORD_RESET_REQUIRED: "auth.passwordResetRequired",
      INVALID_CREDENTIALS: "auth.invalidCredentials",
      USER_NOT_FOUND: "auth.userNotFound",
      EMAIL_ALREADY_EXISTS: "auth.emailAlreadyExists",
      WEAK_PASSWORD: "auth.weakPassword",
      TOKEN_EXPIRED: "auth.tokenExpired",
      INVALID_TOKEN: "auth.invalidToken",
      UNAUTHORIZED: "common.unauthorized",
      FORBIDDEN: "common.forbidden",
      NOT_FOUND: "common.notFound",
      INTERNAL_SERVER_ERROR: "common.internalServerError",
      BAD_REQUEST: "common.badRequest",
      NETWORK_ERROR: "common.networkError",
      TIMEOUT: "common.timeout",
      SERVICE_UNAVAILABLE: "common.serviceUnavailable",
    };

    // Check for exact matches first
    if (errorMappings[errorMessage]) {
      return t(errorMappings[errorMessage]);
    }

    // Check for partial matches (case insensitive)
    const lowerMessage = errorMessage.toLowerCase();
    for (const [key, translationKey] of Object.entries(errorMappings)) {
      if (lowerMessage.includes(key.toLowerCase().replace(/_/g, " "))) {
        return t(translationKey);
      }
    }

    // If no translation found, return the original message
    return errorMessage;
  };

  return {
    success: (key: string, params?: Record<string, any>) => {
      const message = t(key, params);
      return toast.success(message);
    },
    error: (key: string, params?: Record<string, any>) => {
      const message = t(key, params);
      return toast.error(message);
    },
    loading: (key: string, params?: Record<string, any>) => {
      const message = t(key, params);
      return toast.loading(message);
    },
    info: (key: string, params?: Record<string, any>) => {
      const message = t(key, params);
      return toast(message);
    },
    // For API errors that might need translation
    apiError: (errorMessage: string) => {
      const translatedMessage = translateApiError(errorMessage);
      return toast.error(translatedMessage);
    },
    // For cases where we need to show raw messages (like API errors)
    rawSuccess: (message: string) => toast.success(message),
    rawError: (message: string) => toast.error(message),
    rawLoading: (message: string) => toast.loading(message),
    rawInfo: (message: string) => toast(message),
    // Utility methods
    dismiss: toast.dismiss,
    remove: toast.remove,
  };
};
