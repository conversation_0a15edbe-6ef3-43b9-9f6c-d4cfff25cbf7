import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/next-auth";
import db from "@/lib/shared-db";
import crypto from "crypto";

// POST /api/companies/invite/[slug]/join - Join company via invite link
export async function POST(
  _request: Request,
  { params }: { params: { slug: string } }
) {
  try {
    const session: any = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { slug } = params;

    // Find company by slug
    const company = await db.company.findFirst({
      where: { 
        slug: {
          equals: slug,
          mode: "insensitive",
        },
        status: "active",
      },
      include: {
        tenant: true,
        members: true,
      },
    });

    if (!company) {
      return NextResponse.json(
        { error: "Company not found or invitation is no longer valid" },
        { status: 404 }
      );
    }

    // Check if company is at capacity
    if (company.members.length >= company.maxSignups) {
      return NextResponse.json(
        { error: "Company has reached its maximum capacity" },
        { status: 400 }
      );
    }

    // Get user
    const emailHash = crypto
      .createHash("sha256")
      .update(session.user.email)
      .digest("hex");

    const user = await db.user.findUnique({
      where: { emailHash },
      include: {
        membership: true,
        CompanyMember: true,
      },
    });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Check if user is already a company member
    const existingCompanyMembership = await db.companyMember.findFirst({
      where: {
        userId: user.id,
        companyId: company.id,
      },
    });

    if (existingCompanyMembership) {
      return NextResponse.json(
        { error: "You are already a member of this company" },
        { status: 400 }
      );
    }

    // Check if user is already a member of the organization (tenant)
    const existingTenantMembership = user.membership.find(
      (m: any) => m.tenantId === company.tenantId
    );

    // Create memberships in a transaction
    const result = await db.$transaction(async (tx: any) => {
      // If user is not a member of the organization, add them
      if (!existingTenantMembership) {
        await tx.membership.create({
          data: {
            userId: user.id,
            tenantId: company.tenantId,
            role: "MEMBER", // Default role for organization
          },
        });
      }

      // Add user to company
      const companyMembership = await tx.companyMember.create({
        data: {
          userId: user.id,
          companyId: company.id,
          role: "MEMBER", // Default role for company
          status: "ACTIVE",
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      });

      return companyMembership;
    });

    // Format response
    const formattedMember = {
      id: result.id,
      name: result.user.name,
      email: result.user.email,
      role: result.role.toLowerCase(),
      joinedAt: result.joinedAt.toISOString().split("T")[0],
      status: result.status.toLowerCase(),
    };

    return NextResponse.json({
      message: "Successfully joined the company",
      member: formattedMember,
      company: {
        id: company.id,
        name: company.name,
        slug: company.slug,
      },
      addedToOrganization: !existingTenantMembership,
    }, { status: 201 });
  } catch (error) {
    console.error("Error joining company via invite:", error);
    return NextResponse.json(
      { error: "Failed to join company" },
      { status: 500 }
    );
  }
}
