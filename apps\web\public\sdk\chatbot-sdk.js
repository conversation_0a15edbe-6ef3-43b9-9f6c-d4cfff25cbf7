/**
 * Swiss Knowledge Hub Chatbot SDK
 * Version: 1.0.0
 * 
 * A lightweight JavaScript SDK for embedding Swiss Knowledge Hub chatbots
 * into external websites.
 */

(function (window, document) {
  'use strict';

  // SDK Configuration
  const SDK_VERSION = '1.0.0';

  // Auto-detect API base from script source URL
  function getScriptApiBase() {
    const scripts = document.getElementsByTagName('script');
    for (let script of scripts) {
      if (script.src && script.src.includes('chatbot-sdk.js')) {
        const url = new URL(script.src);
        return `${url.protocol}//${url.host}`;
      }
    }
    // Fallback to current domain if script source not found
    return window.location.origin;
  }

  const DEFAULT_API_BASE = getScriptApiBase();
  console.log('SDK detected API base:', DEFAULT_API_BASE);

  // Simple markdown parser for rendering assistant messages
  const MarkdownParser = {
    parse(text) {
      if (!text) return '';

      // Split into lines for better processing
      const lines = text.split('\n');
      const processedLines = [];
      let inCodeBlock = false;
      let inList = false;
      let listItems = [];
      let listType = null;

      for (let i = 0; i < lines.length; i++) {
        let line = lines[i];

        // Handle code blocks
        if (line.trim().startsWith('```')) {
          if (inCodeBlock) {
            processedLines.push('</code></pre>');
            inCodeBlock = false;
          } else {
            processedLines.push('<pre><code>');
            inCodeBlock = true;
          }
          continue;
        }

        if (inCodeBlock) {
          processedLines.push(this.escapeHtml(line));
          continue;
        }

        // Handle lists
        const bulletMatch = line.match(/^(\s*)[\*\-\+] (.+)$/);
        const numberMatch = line.match(/^(\s*)\d+\. (.+)$/);

        if (bulletMatch || numberMatch) {
          if (!inList) {
            inList = true;
            listType = bulletMatch ? 'ul' : 'ol';
            listItems = [];
          }
          const content = bulletMatch ? bulletMatch[2] : numberMatch[2];
          listItems.push(`<li>${this.parseInline(content)}</li>`);
          continue;
        } else if (inList) {
          // End of list
          processedLines.push(`<${listType}>${listItems.join('')}</${listType}>`);
          inList = false;
          listItems = [];
          listType = null;
        }

        // Process regular lines
        if (line.trim() === '') {
          processedLines.push('');
        } else {
          processedLines.push(this.parseLine(line));
        }
      }

      // Close any open list
      if (inList) {
        processedLines.push(`<${listType}>${listItems.join('')}</${listType}>`);
      }

      // Join lines and clean up
      return processedLines
        .join('\n')
        .replace(/\n\n+/g, '</p><p>')
        .replace(/^(.+)$/gm, (match) => {
          if (match.startsWith('<h') || match.startsWith('<pre') ||
            match.startsWith('<ul') || match.startsWith('<ol') ||
            match.trim() === '') {
            return match;
          }
          return `<p>${match}</p>`;
        })
        .replace(/<p><\/p>/g, '')
        .replace(/<p>(<h[1-6]>.*?<\/h[1-6]>)<\/p>/g, '$1')
        .replace(/<p>(<pre>.*?<\/pre>)<\/p>/g, '$1')
        .replace(/<p>(<[uo]l>.*?<\/[uo]l>)<\/p>/g, '$1');
    },

    parseLine(line) {
      // Headers
      if (line.startsWith('### ')) return `<h3>${this.parseInline(line.slice(4))}</h3>`;
      if (line.startsWith('## ')) return `<h2>${this.parseInline(line.slice(3))}</h2>`;
      if (line.startsWith('# ')) return `<h1>${this.parseInline(line.slice(2))}</h1>`;

      return this.parseInline(line);
    },

    parseInline(text) {
      return this.escapeHtml(text)
        // Bold and italic
        .replace(/\*\*\*(.*?)\*\*\*/g, '<strong><em>$1</em></strong>')
        .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
        .replace(/\*(.*?)\*/g, '<em>$1</em>')

        // Inline code
        .replace(/`(.*?)`/g, '<code>$1</code>')

        // Links
        .replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" target="_blank" rel="noopener noreferrer">$1</a>');
    },

    escapeHtml(text) {
      return text
        .replace(/&/g, '&amp;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;')
        .replace(/"/g, '&quot;')
        .replace(/'/g, '&#39;');
    }
  };

  // Default configuration
  const DEFAULT_CONFIG = {
    position: 'bottom-right',
    size: 'medium',
    mode: 'widget', // 'widget' or 'inline'
    greeting: 'Hi! How can I help you today?',
    placeholder: 'Type your message...',
    showBranding: true,
    autoOpen: false,
    debug: false,
    enableStreaming: true, // Enable streaming by default
    searchModes: {
      internal: true,
      web: true,
      mcp: false
    },
    defaultSearchMode: 'internal'
  };

  // CSS Styles
  const CSS_STYLES = `
    .skh-chatbot-widget {
      position: fixed;
      z-index: 10000;
      font-family: var(--skh-font-family, 'Inter', sans-serif);
      font-size: 14px;
      line-height: 1.4;
    }
    
    .skh-chatbot-widget.bottom-right {
      bottom: 20px;
      right: 20px;
    }
    
    .skh-chatbot-widget.bottom-left {
      bottom: 20px;
      left: 20px;
    }
    
    .skh-chatbot-widget.top-right {
      top: 20px;
      right: 20px;
    }
    
    .skh-chatbot-widget.top-left {
      top: 20px;
      left: 20px;
    }
    
    .skh-chat-button {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      background: var(--skh-primary-color, #007bff);
      border: none;
      cursor: pointer;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;
    }
    
    .skh-chat-button:hover {
      transform: scale(1.1);
      box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
    }
    
    .skh-chat-button svg {
      width: 24px;
      height: 24px;
      fill: white !important;
    }
    
    .skh-chat-window {
      width: 420px;
      height: 600px;
      background: white;
      border-radius: var(--skh-border-radius, 8px);
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
      display: none;
      flex-direction: column;
      overflow: hidden;
      margin-bottom: 80px;
    }
    
    .skh-chat-window.open {
      display: flex;
    }

    /* Transform chat button when window is open */
    .skh-chatbot-widget.chat-open .skh-chat-button {
      position: absolute;
      bottom: 10px;
      right: 10px;
      width: 40px;
      height: 40px;
      background: #dc3545;
      transform: scale(1);
      transition: all 0.3s ease;
    }

    .skh-chatbot-widget.chat-open .skh-chat-button:hover {
      background: #c82333;
      transform: scale(1.05);
    }

    .skh-chatbot-widget.chat-open .skh-chat-button svg {
      width: 16px;
      height: 16px;
      fill: white !important;
      stroke: white !important;
    }

    .skh-chatbot-widget.chat-open .skh-chat-button span {
      color: white !important;
      font-size: 18px !important;
      font-weight: bold !important;
      line-height: 1 !important;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    /* Alternative: Hide chat button completely when open */
    .skh-chatbot-widget.chat-open.hide-button .skh-chat-button {
      display: none;
    }
    
    .skh-chat-header {
      background: var(--skh-primary-color, #007bff);
      color: white;
      padding: 16px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      position: relative;
    }

    .skh-header-top {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
    }

    .skh-mode-buttons {
      display: flex;
      flex-direction: column;
      gap: 8px;
      align-items: flex-start;
      padding: 12px 16px;
      background: rgba(0, 0, 0, 0.02);
      border-top: 1px solid rgba(0, 0, 0, 0.1);
    }

    .skh-mode-buttons-label {
      font-size: 11px;
      color: #6c757d;
      font-weight: 500;
      margin-bottom: 4px;
    }

    .skh-mode-buttons-container {
      display: flex;
      gap: 8px;
      align-items: center;
      justify-content: center;
    }

    .skh-mode-button {
      background: #f8f9fa;
      border: 1px solid #dee2e6;
      color: #495057;
      padding: 6px 12px;
      border-radius: 16px;
      font-size: 12px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s ease;
      display: flex;
      align-items: center;
      gap: 4px;
    }

    .skh-mode-button:hover {
      background: #e9ecef;
      border-color: #adb5bd;
    }

    .skh-mode-button.active {
      background: var(--skh-primary-color, #007bff);
      color: white;
      border-color: var(--skh-primary-color, #007bff);
      position: relative;
    }



    .skh-mode-button.disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }

    .skh-mode-icon {
      width: 14px;
      height: 14px;
      display: inline-block;
    }

    .skh-user-menu {
      position: relative;
      display: inline-block;
    }

    .skh-user-button {
      background: rgba(255, 255, 255, 0.2);
      border: none;
      color: white;
      padding: 6px 10px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 12px;
      display: flex;
      align-items: center;
      gap: 4px;
      transition: background 0.2s ease;
    }

    .skh-user-button:hover {
      background: rgba(255, 255, 255, 0.3);
    }

    .skh-user-dropdown {
      position: absolute;
      top: 100%;
      right: 0;
      background: white;
      border: 1px solid #ddd;
      border-radius: 4px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      min-width: 150px;
      z-index: 1000;
      display: none;
      margin-top: 4px;
    }

    .skh-user-dropdown.show {
      display: block;
    }

    .skh-user-dropdown button {
      width: 100%;
      padding: 8px 12px;
      border: none;
      background: none;
      text-align: left;
      cursor: pointer;
      font-size: 12px;
      color: #333;
      transition: background 0.2s ease;
    }

    .skh-user-dropdown button:hover {
      background: #f8f9fa;
    }

    .skh-chat-input-container.disabled {
      opacity: 0.5;
      pointer-events: none;
    }

    .skh-chat-input-container.disabled .skh-chat-input {
      background-color: #f5f5f5;
      cursor: not-allowed;
    }

    .skh-chat-input-container.disabled .skh-send-button {
      background-color: #ccc;
      cursor: not-allowed;
    }

    .skh-new-chat-button {
      background: rgba(255, 255, 255, 0.2);
      border: none;
      color: white;
      padding: 6px;
      border-radius: 4px;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: background 0.2s ease;
    }

    .skh-new-chat-button:hover {
      background: rgba(255, 255, 255, 0.3);
    }

    .skh-sessions-accordion {
      border-bottom: 1px solid #e1e5e9;
      background: #f8f9fa;
    }

    .skh-sessions-header {
      padding: 12px 16px;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 14px;
      font-weight: 500;
      color: #495057;
      transition: background 0.2s ease;
    }

    .skh-sessions-header:hover {
      background: #e9ecef;
    }

    .skh-sessions-header .skh-accordion-icon {
      transition: transform 0.2s ease;
    }

    .skh-sessions-header.expanded .skh-accordion-icon {
      transform: rotate(180deg);
    }

    .skh-sessions-content {
      max-height: 0;
      overflow: hidden;
      transition: max-height 0.3s ease;
    }

    .skh-sessions-content.expanded {
      max-height: 300px;
      overflow-y: auto;
    }

    .skh-sessions-list {
      padding: 8px 0;
    }

    .skh-session-item {
      padding: 8px 16px;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 13px;
      transition: background 0.2s ease;
      border-left: 3px solid transparent;
    }

    .skh-session-item:hover {
      background: #e9ecef;
    }

    .skh-session-item.active {
      background: #e3f2fd;
      border-left-color: var(--skh-primary-color, #007bff);
    }

    .skh-session-info {
      flex: 1;
    }

    .skh-session-title {
      font-weight: 500;
      color: #212529;
      margin-bottom: 2px;
    }

    .skh-session-time {
      color: #6c757d;
      font-size: 11px;
    }

    .skh-session-actions {
      display: flex;
      gap: 4px;
    }

    .skh-new-session-btn {
      margin: 8px 16px;
      padding: 8px 12px;
      background: var(--skh-primary-color, #007bff);
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 12px;
      font-weight: 500;
      transition: background 0.2s ease;
    }

    .skh-new-session-btn:hover {
      background: var(--skh-primary-color-dark, #0056b3);
    }
    
    .skh-chat-title {
      font-weight: 600;
      font-size: 16px;
    }
    
    .skh-close-button {
      background: none;
      border: none;
      color: white;
      cursor: pointer;
      padding: 4px;
      border-radius: 4px;
    }
    
    .skh-close-button:hover {
      background: rgba(255, 255, 255, 0.1);
    }
    
    .skh-chat-messages {
      flex: 1;
      overflow-y: auto;
      padding: 16px;
      display: flex;
      flex-direction: column;
      gap: 12px;
    }
    
    .skh-message {
      max-width: 80%;
      padding: 12px 16px;
      border-radius: var(--skh-border-radius, 8px);
      word-wrap: break-word;
    }
    
    .skh-message.user {
      background: var(--skh-primary-color, #007bff);
      color: white;
      align-self: flex-end;
      margin-left: auto;
    }
    
    .skh-message.bot {
      background: #f8f9fa;
      color: #333;
      align-self: flex-start;
    }

    /* Markdown styles for bot messages */
    .skh-message.bot h1,
    .skh-message.bot h2,
    .skh-message.bot h3,
    .skh-message.bot h4,
    .skh-message.bot h5,
    .skh-message.bot h6 {
      margin: 0.5em 0 0.3em 0;
      font-weight: 600;
      line-height: 1.3;
    }

    .skh-message.bot h1 { font-size: 1.5em; }
    .skh-message.bot h2 { font-size: 1.3em; }
    .skh-message.bot h3 { font-size: 1.1em; }

    .skh-message.bot p {
      margin: 0.5em 0;
      line-height: 1.5;
    }

    .skh-message.bot ul,
    .skh-message.bot ol {
      margin: 0.5em 0;
      padding-left: 1.5em;
    }

    .skh-message.bot li {
      margin: 0.2em 0;
    }

    .skh-message.bot code {
      background: #e9ecef;
      padding: 0.2em 0.4em;
      border-radius: 3px;
      font-family: 'Monaco', 'Menlo', 'Consolas', monospace;
      font-size: 0.9em;
    }

    .skh-message.bot pre {
      background: #f8f9fa;
      border: 1px solid #e9ecef;
      border-radius: 4px;
      padding: 0.8em;
      margin: 0.5em 0;
      overflow-x: auto;
    }

    .skh-message.bot pre code {
      background: none;
      padding: 0;
      border-radius: 0;
    }

    .skh-message.bot strong {
      font-weight: 600;
    }

    .skh-message.bot em {
      font-style: italic;
    }

    .skh-message.bot a {
      color: var(--skh-primary-color, #007bff);
      text-decoration: none;
    }

    .skh-message.bot a:hover {
      text-decoration: underline;
    }
    
    .skh-message.typing {
      background: #f8f9fa;
      color: #666;
      align-self: flex-start;
      font-style: italic;
    }
    
    .skh-chat-input-container {
      padding: 16px;
      border-top: 1px solid #e9ecef;
      display: flex;
      gap: 8px;
    }
    
    .skh-chat-input {
      flex: 1;
      border: 1px solid #ddd;
      border-radius: var(--skh-border-radius, 8px);
      padding: 12px;
      font-size: 14px;
      outline: none;
      resize: none;
      min-height: 20px;
      max-height: 100px;
    }
    
    .skh-chat-input:focus {
      border-color: var(--skh-primary-color, #007bff);
    }
    
    .skh-send-button {
      background: var(--skh-primary-color, #007bff);
      color: white;
      border: none;
      border-radius: var(--skh-border-radius, 8px);
      padding: 12px 16px;
      cursor: pointer;
      font-size: 14px;
      transition: background-color 0.2s;
    }
    
    .skh-send-button:hover:not(:disabled) {
      background: var(--skh-primary-color-dark, #0056b3);
    }
    
    .skh-send-button:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
    
    .skh-branding {
      padding: 8px 16px;
      text-align: center;
      font-size: 12px;
      color: #666;
      border-top: 1px solid #e9ecef;
    }
    
    .skh-branding a {
      color: var(--skh-primary-color, #007bff);
      text-decoration: none;
    }
    
    .skh-inline-container {
      width: 100%;
      height: 600px;
      border: 1px solid #e9ecef;
      border-radius: var(--skh-border-radius, 8px);
      display: flex;
      flex-direction: column;
      overflow: hidden;
    }
    
    @media (max-width: 480px) {
      .skh-chat-window {
        width: calc(100vw - 20px);
        height: calc(100vh - 80px);
        margin-bottom: 20px;
      }
      
      .skh-chatbot-widget.bottom-right,
      .skh-chatbot-widget.bottom-left {
        bottom: 10px;
        right: 10px;
        left: 10px;
      }
    }

    /* Streaming styles */
    .skh-streaming .skh-streaming-cursor {
      animation: skh-blink 1s infinite;
      color: var(--skh-primary-color, #007bff);
      font-weight: bold;
      margin-left: 2px;
    }

    @keyframes skh-blink {
      0%, 50% { opacity: 1; }
      51%, 100% { opacity: 0; }
    }

    .skh-streaming {
      position: relative;
    }

    /* Citation styles */
    .skh-citation {
      color: var(--skh-primary-color, #007bff);
      text-decoration: none;
      font-weight: bold;
      cursor: pointer;
      margin: 0 1px;
    }

    .skh-citation:hover {
      text-decoration: underline;
    }

    /* Source modal styles */
    .skh-source-modal {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.5);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 10001;
    }

    .skh-source-modal-content {
      background: white;
      padding: 20px;
      border-radius: var(--skh-border-radius, 8px);
      max-width: 500px;
      max-height: 80vh;
      overflow-y: auto;
      margin: 20px;
    }

    .skh-source-modal-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15px;
      padding-bottom: 10px;
      border-bottom: 1px solid #e9ecef;
    }

    .skh-source-modal-close {
      background: none;
      border: none;
      font-size: 24px;
      cursor: pointer;
      color: #666;
    }

    .skh-source-modal-close:hover {
      color: #333;
    }
  `;

  // Main SDK Class
  class SwissKnowledgeHubSDK {
    constructor() {
      this.instances = new Map();
      this.isStylesInjected = false;
    }

    init(config) {
      if (!config.chatbotId || !config.apiKey) {
        throw new Error('chatbotId and apiKey are required');
      }

      // Create a unique instance ID based on chatbot ID and container
      let containerId = 'default';
      if (config.container) {
        if (config.container instanceof HTMLElement) {
          // Use element's ID or create one
          containerId = config.container.id || 'element_' + Math.random().toString(36).substr(2, 9);
          if (!config.container.id) {
            config.container.id = containerId;
          }
        } else if (typeof config.container === 'string') {
          containerId = config.container.replace(/[^a-zA-Z0-9]/g, '_');
        }
      }

      const instanceId = `${config.chatbotId}_${config.mode || 'widget'}_${containerId}`;

      // Check if instance already exists for this container
      if (this.instances.has(instanceId)) {
        console.warn(`Chatbot instance already exists for container: ${containerId}. Destroying previous instance.`);
        this.destroy(instanceId);
      }

      const instance = new ChatbotInstance(instanceId, config);
      this.instances.set(instanceId, instance);

      if (!this.isStylesInjected) {
        this.injectStyles();
        this.isStylesInjected = true;
      }

      instance.render();

      return instance;
    }

    injectStyles() {
      const styleElement = document.createElement('style');
      styleElement.textContent = CSS_STYLES;
      document.head.appendChild(styleElement);
    }

    destroy(instanceId) {
      const instance = this.instances.get(instanceId);
      if (instance) {
        instance.destroy();
        this.instances.delete(instanceId);
      }
    }

    destroyAll() {
      this.instances.forEach(instance => instance.destroy());
      this.instances.clear();
    }
  }

  // Individual Chatbot Instance
  class ChatbotInstance {
    constructor(instanceId, config) {
      this.instanceId = instanceId;
      this.config = { ...DEFAULT_CONFIG, ...config };
      this.apiBase = config.apiBase || DEFAULT_API_BASE;
      this.sessionToken = null;
      this.isOpen = false;
      this.isLoading = false;
      this.messages = [];
      this.userToken = null;
      this.isAuthenticated = false;
      this.requiresAuth = false;
      this.cachedSessions = null;
      this.sessionsAccordion = null;
      this.status = "public";
      this.userTrackingData = null;
      this.currentSearchModes = this.getDefaultSearchModes();

      // Load saved tokens from cookies
      this.loadTokensFromCookies();

      // Initialize user tracking
      this.initializeUserTracking();

      // DOM elements
      this.container = null;
      this.chatWindow = null;
      this.messagesContainer = null;
      this.inputElement = null;
      this.sendButton = null;

      // Chart renderer
      this.chartRenderer = null;
      this.initializeChartRenderer();

    }

    // Get default search modes based on configuration
    getDefaultSearchModes() {
      const searchModes = this.config.searchModes || { internal: true, web: false, mcp: false, hybrid: false };
      const defaultMode = this.config.defaultSearchMode || 'internal';

      // If default mode is specified and enabled, use it
      if (searchModes[defaultMode]) {
        return [defaultMode];
      }

      // Otherwise, use the first enabled mode
      const enabledModes = Object.keys(searchModes).filter(mode => searchModes[mode]);
      return enabledModes.length > 0 ? [enabledModes[0]] : ['internal'];
    }

    log(...args) {
      if (this.config.debug) {
        console.log('[SwissKnowledgeHub SDK]', ...args);
      }
    }

    // Initialize chart renderer if enabled
    async initializeChartRenderer() {
      if (this.config.enableCharts !== false) {
        try {
          // Load chart renderer script if not already loaded
          if (!window.ChartRenderer) {
            await this.loadScript(`${this.apiBase}/sdk/chart-renderer.js`);
          }
          this.chartRenderer = new window.ChartRenderer();
          this.log('Chart renderer initialized');
        } catch (error) {
          this.log('Failed to initialize chart renderer:', error);
        }
      }
    }

    // Load external script
    loadScript(src) {
      return new Promise((resolve, reject) => {
        // Check if script is already loaded
        const existingScript = document.querySelector(`script[src="${src}"]`);
        if (existingScript) {
          resolve();
          return;
        }

        const script = document.createElement('script');
        script.src = src;
        script.onload = resolve;
        script.onerror = reject;
        document.head.appendChild(script);
      });
    }

    // Initialize comprehensive user tracking
    async initializeUserTracking() {
      try {
        this.userTrackingData = await this.collectUserTrackingData();
        this.log('User tracking data collected:', this.userTrackingData);
      } catch (error) {
        console.warn('Failed to initialize user tracking:', error);
        this.userTrackingData = {};
      }
    }

    // Collect comprehensive user tracking data
    async collectUserTrackingData() {
      const trackingData = {};

      try {
        // Basic browser and device information
        const userAgent = navigator.userAgent;
        trackingData.userAgent = userAgent;
        trackingData.language = navigator.language || navigator.userLanguage;

        // Device type detection
        let deviceType = 'desktop';
        if (/Mobile|Android|iPhone|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent)) {
          deviceType = 'mobile';
        } else if (/Tablet|iPad|PlayBook|Silk/i.test(userAgent)) {
          deviceType = 'tablet';
        }
        trackingData.deviceType = deviceType;

        // Operating system detection
        let operatingSystem = 'unknown';
        if (/Windows NT/i.test(userAgent)) {
          operatingSystem = 'Windows';
        } else if (/Mac OS X/i.test(userAgent)) {
          operatingSystem = 'macOS';
        } else if (/Linux/i.test(userAgent)) {
          operatingSystem = 'Linux';
        } else if (/Android/i.test(userAgent)) {
          operatingSystem = 'Android';
        } else if (/iPhone|iPad|iPod/i.test(userAgent)) {
          operatingSystem = 'iOS';
        }
        trackingData.operatingSystem = operatingSystem;

        // Browser detection
        let browserName = 'unknown';
        let browserVersion = 'unknown';

        if (/Chrome/i.test(userAgent) && !/Edge|Edg/i.test(userAgent)) {
          browserName = 'Chrome';
          const match = userAgent.match(/Chrome\/([0-9.]+)/);
          browserVersion = match ? match[1] : 'unknown';
        } else if (/Firefox/i.test(userAgent)) {
          browserName = 'Firefox';
          const match = userAgent.match(/Firefox\/([0-9.]+)/);
          browserVersion = match ? match[1] : 'unknown';
        } else if (/Safari/i.test(userAgent) && !/Chrome/i.test(userAgent)) {
          browserName = 'Safari';
          const match = userAgent.match(/Version\/([0-9.]+)/);
          browserVersion = match ? match[1] : 'unknown';
        } else if (/Edge|Edg/i.test(userAgent)) {
          browserName = 'Edge';
          const match = userAgent.match(/Edge?\/([0-9.]+)/);
          browserVersion = match ? match[1] : 'unknown';
        }
        trackingData.browserName = browserName;
        trackingData.browserVersion = browserVersion;

        // Screen and viewport information
        trackingData.screenWidth = screen.width;
        trackingData.screenHeight = screen.height;
        trackingData.viewportWidth = window.innerWidth || document.documentElement.clientWidth;
        trackingData.viewportHeight = window.innerHeight || document.documentElement.clientHeight;

        // Page information
        trackingData.pageUrl = window.location.href;
        trackingData.pageTitle = document.title;
        trackingData.domain = window.location.hostname;
        trackingData.referrer = document.referrer || undefined;

        // UTM parameters
        const urlParams = new URLSearchParams(window.location.search);
        const utmSource = urlParams.get('utm_source');
        const utmMedium = urlParams.get('utm_medium');
        const utmCampaign = urlParams.get('utm_campaign');
        const utmTerm = urlParams.get('utm_term');
        const utmContent = urlParams.get('utm_content');

        if (utmSource) trackingData.utmSource = utmSource;
        if (utmMedium) trackingData.utmMedium = utmMedium;
        if (utmCampaign) trackingData.utmCampaign = utmCampaign;
        if (utmTerm) trackingData.utmTerm = utmTerm;
        if (utmContent) trackingData.utmContent = utmContent;

        // Connection information (if available)
        if ('connection' in navigator) {
          const connection = navigator.connection || navigator.mozConnection || navigator.webkitConnection;
          if (connection) {
            trackingData.connectionType = connection.effectiveType || connection.type;
          }
        }

        // Timezone information
        try {
          trackingData.timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
        } catch (error) {
          // Timezone detection failed
        }

        // User behavior tracking
        try {
          const visitCount = localStorage.getItem(`skh_visit_count_${this.config.chatbotId}`);
          const isReturning = visitCount !== null;
          const previousVisits = visitCount ? parseInt(visitCount, 10) : 0;

          // Update visit count
          localStorage.setItem(`skh_visit_count_${this.config.chatbotId}`, (previousVisits + 1).toString());

          trackingData.isReturning = isReturning;
          trackingData.previousVisits = previousVisits;

          // Session depth
          const sessionDepth = sessionStorage.getItem(`skh_session_depth_${this.config.chatbotId}`) || '1';
          sessionStorage.setItem(`skh_session_depth_${this.config.chatbotId}`, (parseInt(sessionDepth, 10) + 1).toString());
          trackingData.sessionDepth = parseInt(sessionDepth, 10);
        } catch (error) {
          // Local storage not available
          trackingData.isReturning = false;
          trackingData.previousVisits = 0;
          trackingData.sessionDepth = 1;
        }

      } catch (error) {
        console.warn('Error collecting user tracking data:', error);
      }

      return trackingData;
    }

    async render() {
      try {
        // Load chatbot configuration
        await this.loadConfig();

        // Create DOM structure
        this.createDOM();

        // Store reference to chatbot instance on container for sign-out functionality
        if (this.container) {
          this.container.chatbotInstance = this;
        }

        // Set up event listeners
        this.setupEventListeners();

        // Check if authentication is required
        if (this.requiresAuth && !this.isAuthenticated) {
          this.showAuthenticationForm();
        } else {
          // Ensure input container is visible for authenticated users or public chatbots
          this.showInputContainer();

          // Update header if user is authenticated
          if (this.isAuthenticated && this.headerElement) {
            this.updateHeader(this.headerElement);
            this.setupHeaderEventListeners();
          }

          // Add sessions accordion for private chatbots when authenticated
          if (this.isAuthenticated && !this.sessionsAccordion) {
            this.sessionsAccordion = this.createSessionsAccordion();
            const target = this.config.mode === 'widget' ? this.chatWindow : this.container;
            target.insertBefore(this.sessionsAccordion, this.messagesContainer);
          }

          // Load chat history if session token exists
          if (this.sessionToken) {
            await this.loadChatHistory();
          } else {
            // Show greeting message if no chat history
            this.showGreeting();
          }
        }

      } catch (error) {
        console.error('Failed to render chatbot:', error);
      }
    }

    async loadConfig() {
      const headers = {
        'Authorization': `Bearer ${this.config.apiKey}`,
        'Content-Type': 'application/json'
      };

      // Add user token for private chatbot access
      if (this.userToken) {
        headers['X-User-Token'] = this.userToken;
      }
      try {
        const response = await fetch(`${this.apiBase}/api/public/chatbot/${this.config.chatbotId}/config`, {
          headers
        });

        if (!response.ok) {
          const errorData = await response.json();
          if (errorData.error === "Authentication required for private chatbot access") {
            this.requiresAuth = true;
            return;
          }
          throw new Error(`Failed to load config: ${response.status}`);
        }

        const configData = await response.json();

        // Check if this is a private chatbot that requires authentication
        if (configData.error === "Authentication required for private chatbot access") {
          this.requiresAuth = true;
          this.status = "private";
          this.log('Private chatbot detected - authentication required');

          // Still apply customization for UI rendering (sign-in form styling)
          if (configData.customization) {
            const originalConfig = { ...this.config };

            // Handle theme merging for private chatbots too
            let mergedTheme = {};
            if (configData.customization?.theme) {
              mergedTheme = { ...configData.customization.theme };
            }
            if (originalConfig.theme) {
              mergedTheme = { ...mergedTheme, ...originalConfig.theme };
            }
            console.log({ configData });

            // Convert searchModes array to object format
            let searchModesConfig = { internal: true, web: false, mcp: false };
            if (configData?.features?.searchModes && Array.isArray(configData.features.searchModes)) {
              searchModesConfig = {};
              configData.features.searchModes.forEach(mode => {
                searchModesConfig[mode] = true;
              });
            }

            this.config = {
              ...DEFAULT_CONFIG,
              ...configData.customization,
              ...originalConfig, // Local config takes precedence
              theme: mergedTheme, // Use merged theme
              searchModes: originalConfig.searchModes || searchModesConfig, // Use local config if provided, otherwise server config
              serverConfig: configData
            };





            // Apply theme variables for sign-in form
            this.applyTheme();
          }
          return;
        }

        // this.status = configData?.;

        // Store original local config values that were explicitly set
        const originalConfig = { ...this.config };

        // Handle theme merging: use server theme if no local theme provided
        let mergedTheme = {};
        if (configData.customization?.theme) {
          mergedTheme = { ...configData.customization.theme };
        }
        if (originalConfig.theme) {
          mergedTheme = { ...mergedTheme, ...originalConfig.theme };
        }

        // Convert searchModes array to object format
        let searchModesConfig = { internal: true, web: false, mcp: false };
        if (configData?.features?.searchModes && Array.isArray(configData.features.searchModes)) {
          searchModesConfig = {};
          configData.features.searchModes.forEach(mode => {
            searchModesConfig[mode] = true;
          });
        }

        // Merge server config with local config, but preserve explicitly set local values
        this.config = {
          ...DEFAULT_CONFIG,
          ...configData.customization,
          ...originalConfig, // Local config takes precedence
          theme: mergedTheme, // Use merged theme
          searchModes: originalConfig.searchModes || searchModesConfig, // Use local config if provided, otherwise server config
          serverConfig: configData
        };


        this.log('Config merge:', {
          original: originalConfig,
          server: configData.customization,
          final: this.config
        });

        // Apply theme variables
        this.applyTheme();

        this.log('Configuration loaded', this.config);
      } catch (error) {
        console.error('Failed to load chatbot configuration:', error);
        // Continue with default config
      }
    }

    applyTheme() {
      const theme = this.config.theme || {};
      const root = document.documentElement;

      if (!theme || Object.keys(theme).length === 0) {
        console.log('No theme to apply');
        return;
      }

      if (theme.primaryColor) {
        root.style.setProperty('--skh-primary-color', theme.primaryColor);
        root.style.setProperty('--skh-primary-color-dark', this.darkenColor(theme.primaryColor, 20));
      }
      if (theme.secondaryColor) {
        root.style.setProperty('--skh-secondary-color', theme.secondaryColor);
      }
      if (theme.fontFamily) {
        root.style.setProperty('--skh-font-family', theme.fontFamily);
      }
      if (theme.borderRadius) {
        root.style.setProperty('--skh-border-radius', theme.borderRadius);
      }
    }

    darkenColor(color, percent) {
      const num = parseInt(color.replace("#", ""), 16);
      const amt = Math.round(2.55 * percent);
      const R = (num >> 16) - amt;
      const G = (num >> 8 & 0x00FF) - amt;
      const B = (num & 0x0000FF) - amt;
      return "#" + (0x1000000 + (R < 255 ? R < 1 ? 0 : R : 255) * 0x10000 +
        (G < 255 ? G < 1 ? 0 : G : 255) * 0x100 +
        (B < 255 ? B < 1 ? 0 : B : 255)).toString(16).slice(1);
    }

    createDOM() {
      if (this.config.mode === 'inline') {
        this.createInlineChat();
      } else {
        this.createWidgetChat();
      }
    }

    createWidgetChat() {
      // Create main container
      this.container = document.createElement('div');
      this.container.className = `skh-chatbot-widget ${this.config.position}`;

      // Create chat button
      const chatButton = document.createElement('button');
      chatButton.className = 'skh-chat-button';
      chatButton.innerHTML = `
        <svg viewBox="0 0 24 24" fill="white">
          <path d="M20 2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h4l4 4 4-4h4c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2z"/>
        </svg>
      `;

      // Create chat window
      this.chatWindow = document.createElement('div');
      this.chatWindow.className = 'skh-chat-window';

      this.createChatContent();

      this.container.appendChild(chatButton);
      this.container.appendChild(this.chatWindow);
      document.body.appendChild(this.container);

      // Chat button click handler
      chatButton.addEventListener('click', () => this.toggleChat());
    }

    createInlineChat() {
      let targetElement;

      if (this.config.container) {
        // Check if container is already a DOM element
        if (this.config.container instanceof HTMLElement) {
          targetElement = this.config.container;
        } else if (typeof this.config.container === 'string') {
          // It's a CSS selector string
          targetElement = document.querySelector(this.config.container);
        } else {
          throw new Error(`Invalid container type. Expected HTMLElement or CSS selector string.`);
        }
      } else {
        targetElement = document.body;
      }

      if (!targetElement) {
        throw new Error(`Container element not found: ${this.config.container}`);
      }

      this.container = document.createElement('div');
      this.container.className = 'skh-inline-container';
      this.container.style.height = this.config.mode === 'inline' ? '100%' : "600px";

      this.createChatContent();

      targetElement.appendChild(this.container);
      this.isOpen = true;
    }

    createChatContent() {
      const header = document.createElement('div');
      header.className = 'skh-chat-header';
      this.updateHeader(header);
      this.headerElement = header;

      this.messagesContainer = document.createElement('div');
      this.messagesContainer.className = 'skh-chat-messages';

      const inputContainer = document.createElement('div');
      inputContainer.className = 'skh-chat-input-container';

      // Hide input container initially for private chatbots
      if (this.requiresAuth && !this.isAuthenticated) {
        inputContainer.style.display = 'none';
      }

      this.inputElement = document.createElement('textarea');
      this.inputElement.className = 'skh-chat-input';
      this.inputElement.placeholder = this.config.placeholder;
      this.inputElement.rows = 1;

      this.sendButton = document.createElement('button');
      this.sendButton.className = 'skh-send-button';
      this.sendButton.textContent = 'Send';

      inputContainer.appendChild(this.inputElement);
      inputContainer.appendChild(this.sendButton);

      // Create mode buttons container - always create it even if initially empty
      const modeButtonsContainer = document.createElement('div');
      modeButtonsContainer.className = 'skh-mode-buttons-wrapper';
      this.modeButtonsContainer = modeButtonsContainer;

      const target = this.config.mode === 'inline' ? this.container : this.chatWindow;
      target.appendChild(header);
      target.appendChild(this.messagesContainer);
      target.appendChild(inputContainer);

      // Always add mode buttons container to DOM (even if initially empty)
      target.appendChild(modeButtonsContainer);

      // Populate mode buttons content
      this.refreshModeButtons();

      // Hide mode buttons initially if authentication is required and user is not authenticated
      if (this.requiresAuth && !this.isAuthenticated) {
        modeButtonsContainer.style.display = 'none';
      }

      // Set up header event listeners
      this.setupHeaderEventListeners();

      // Set up mode button event listeners
      this.setupModeButtonEventListeners();

      if (this.config.showBranding) {
        const branding = document.createElement('div');
        branding.className = 'skh-branding';
        branding.innerHTML = `Powered by <a href="#" target="_blank">Swiss Knowledge Hub</a>`;
        target.appendChild(branding);
      }
    }

    setupEventListeners() {
      // Close button event listener is now set up in updateHeader() with proper timing
      // This ensures the DOM element exists before attaching the event listener

      // Send button
      this.sendButton.addEventListener('click', () => this.sendMessage());

      // Enter key in input
      this.inputElement.addEventListener('keydown', (e) => {
        if (e.key === 'Enter' && !e.shiftKey) {
          e.preventDefault();
          this.sendMessage();
        }
      });

      // Auto-resize textarea
      this.inputElement.addEventListener('input', () => {
        this.inputElement.style.height = 'auto';
        this.inputElement.style.height = Math.min(this.inputElement.scrollHeight, 100) + 'px';
      });
    }

    async loadChatHistory() {
      if (!this.sessionToken) return;

      try {
        const headers = {
          'Authorization': `Bearer ${this.config.apiKey}`,
          'Content-Type': 'application/json'
        };

        // Add user token for private chatbot access
        if (this.userToken) {
          headers['X-User-Token'] = this.userToken;
        }

        const response = await fetch(
          `${this.apiBase}/api/public/chatbot/${this.config.chatbotId}/chat?sessionToken=${this.sessionToken}`,
          {
            method: 'GET',
            headers
          }
        );

        if (response.ok) {
          const data = await response.json();

          if (data.messages && data.messages.length > 0) {
            // Clear any existing messages
            this.messagesContainer.innerHTML = '';

            // Add each message from history
            data.messages.forEach(message => {
              // Convert role to the format expected by addMessage
              const messageType = message.role === 'user' ? 'user' : 'bot';
              this.addMessage(messageType, message.content, message?.metadata?.sources);
            });

            this.log(`Loaded ${data.messages.length} messages from chat history`);
          } else {
            // No chat history, show greeting
            this.showGreeting();
          }
        } else {
          // If history loading fails, show greeting
          this.log('Failed to load chat history, showing greeting');
          this.showGreeting();
        }
      } catch (error) {
        console.error('Error loading chat history:', error);
        // If history loading fails, show greeting
        this.showGreeting();
      }
    }

    async loadUserSessions() {
      if (!this.userToken) return;

      try {
        const headers = {
          'Authorization': `Bearer ${this.config.apiKey}`,
          'Content-Type': 'application/json',
          'X-User-Token': this.userToken
        };

        const response = await fetch(
          `${this.apiBase}/api/public/chatbot/${this.config.chatbotId}/sessions`,
          {
            method: 'GET',
            headers
          }
        );

        if (response.ok) {
          const data = await response.json();
          this.cachedSessions = data.sessions || [];
          this.renderUserSessions(this.cachedSessions);
        } else {
          this.cachedSessions = [];
          this.renderUserSessions([]);
        }
      } catch (error) {
        console.error('Error loading user sessions:', error);
        this.renderUserSessions([]);
      }
    }

    renderUserSessions(sessions) {
      const sessionsList = document.getElementById(`skh-sessions-list-${this.instanceId}`);
      if (!sessionsList) {
        // If sessions list doesn't exist yet, cache the sessions for later
        this.cachedSessions = sessions;
        return;
      }

      if (sessions.length === 0) {
        sessionsList.innerHTML = `
          <div style="padding: 16px; text-align: center; color: #6c757d; font-size: 12px;">
            No previous conversations
          </div>
        `;
        return;
      }

      sessionsList.innerHTML = sessions.map(session => {
        const isActive = session.sessionToken === this.sessionToken;
        const lastMessage = session.messages?.[session.messages.length - 1];
        const preview = lastMessage ?
          (lastMessage.content.length > 50 ? lastMessage.content.substring(0, 50) + '...' : lastMessage.content) :
          'New conversation';

        const timeAgo = this.formatTimeAgo(new Date(session.updatedAt));

        return `
          <div class="skh-session-item ${isActive ? 'active' : ''}" data-session-token="${session.sessionToken}">
            <div class="skh-session-info">
              <div class="skh-session-title">${preview}</div>
              <div class="skh-session-time">${timeAgo}</div>
            </div>
          </div>
        `;
      }).join('');

      // Add click listeners to session items
      sessionsList.querySelectorAll('.skh-session-item').forEach(item => {
        item.addEventListener('click', () => {
          const sessionToken = item.dataset.sessionToken;
          this.loadChatSession(sessionToken);
        });
      });
    }

    formatTimeAgo(date) {
      const now = new Date();
      const diffMs = now - date;
      const diffMins = Math.floor(diffMs / 60000);
      const diffHours = Math.floor(diffMs / 3600000);
      const diffDays = Math.floor(diffMs / 86400000);

      if (diffMins < 1) return 'Just now';
      if (diffMins < 60) return `${diffMins}m ago`;
      if (diffHours < 24) return `${diffHours}h ago`;
      if (diffDays < 7) return `${diffDays}d ago`;
      return date.toLocaleDateString();
    }

    async loadChatSession(sessionToken) {
      if (sessionToken === this.sessionToken) return; // Already loaded

      this.sessionToken = sessionToken;
      this.saveTokensToCookies();

      // Load the chat history for this session
      await this.loadChatHistory();

      // Update the active session in the UI
      this.renderUserSessions(this.cachedSessions || []);

      this.log(`Switched to session: ${sessionToken}`);
    }

    startNewChatSession() {

      // Clear current session token only (keep user authentication)
      this.sessionToken = null;
      this.deleteCookie(`skh_session_token_${this.config.chatbotId}`);

      // Clear messages
      this.messagesContainer.innerHTML = '';

      // Show greeting for new session
      this.showGreeting();

      // Refresh sessions list
      if (this.userToken) {
        this.loadUserSessions();
      }

      this.log('Started new chat session');
    }

    createSessionsAccordion() {
      const accordion = document.createElement('div');
      accordion.className = 'skh-sessions-accordion';
      accordion.innerHTML = `
        <div class="skh-sessions-header" id="skh-sessions-header-${this.instanceId}">
          <span>Previous Conversations</span>
          <svg class="skh-accordion-icon" width="12" height="12" viewBox="0 0 24 24" fill="currentColor">
            <path d="M7 10l5 5 5-5z"/>
          </svg>
        </div>
        <div class="skh-sessions-content" id="skh-sessions-content-${this.instanceId}">
          <div class="skh-sessions-list" id="skh-sessions-list-${this.instanceId}">
            <div style="padding: 16px; text-align: center; color: #6c757d; font-size: 12px;">
              Loading conversations...
            </div>
          </div>
        </div>
      `;

      // Set up event listeners
      this.setupSessionsAccordionListeners(accordion);

      // Load user sessions or render cached sessions
      if (this.cachedSessions) {
        this.renderUserSessions(this.cachedSessions);
      } else {
        this.loadUserSessions();
      }

      return accordion;
    }

    setupSessionsAccordionListeners(accordion) {
      const header = accordion.querySelector(`#skh-sessions-header-${this.instanceId}`);
      const content = accordion.querySelector(`#skh-sessions-content-${this.instanceId}`);
      const newSessionBtn = accordion.querySelector(`#skh-new-session-btn-${this.instanceId}`);

      // Toggle accordion
      if (header) {
        header.addEventListener('click', () => {
          const isExpanded = header.classList.contains('expanded');
          if (isExpanded) {
            header.classList.remove('expanded');
            content.classList.remove('expanded');
          } else {
            header.classList.add('expanded');
            content.classList.add('expanded');
          }
        });
      }

      // New session button (only if it exists)
      if (newSessionBtn) {
        newSessionBtn.addEventListener('click', (e) => {
          e.stopPropagation();
          this.startNewChatSession();
          // Close accordion after creating new session
          header.classList.remove('expanded');
          content.classList.remove('expanded');
        });
      }
    }

    showGreeting() {
      if (this.config.greeting) {
        this.addMessage('bot', this.config.greeting);
      }
    }

    toggleChat() {
      this.isOpen = !this.isOpen;
      const chatButton = this.container.querySelector('.skh-chat-button');

      if (this.isOpen) {
        this.chatWindow.classList.add('open');
        this.container.classList.add('chat-open');

        // Change button to close icon
        if (chatButton) {
          chatButton.innerHTML = `
            <span style="color: white; font-size: 18px; font-weight: bold; line-height: 1;">×</span>
          `;
        }

        this.inputElement.focus();
      } else {
        this.chatWindow.classList.remove('open');
        this.container.classList.remove('chat-open');

        // Change button back to chat icon
        if (chatButton) {
          chatButton.innerHTML = `
            <svg viewBox="0 0 24 24" fill="white">
              <path d="M20 2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h4l4 4 4-4h4c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2z"/>
            </svg>
          `;
        }
      }
    }

    async addMessage(type, content, sources = []) {
      const messageElement = document.createElement('div');
      messageElement.className = `skh-message ${type}`;

      if (type === 'bot') {
        console.log('Adding bot message with sources:', sources);

        // Try to render charts first if chart renderer is available
        let hasCharts = false;
        if (this.chartRenderer && this.config.enableCharts !== false) {
          try {
            hasCharts = await this.chartRenderer.renderContentWithCharts(content, messageElement);
          } catch (error) {
            this.log('Error rendering charts:', error);
          }
        }

        // If no charts were rendered, use regular markdown rendering
        if (!hasCharts) {
          const markdownContent = MarkdownParser.parse(content);

          if (sources && sources.length > 0) {
            // Add sources/citations
            const sourcesHtml = sources.map((source, index) =>
              `<sup><a href="#" class="skh-citation" data-source="${index}">${index + 1}</a></sup>`
            ).join('');
            messageElement.innerHTML = markdownContent + ' ' + sourcesHtml;

            // Add click handlers for citations
            messageElement.querySelectorAll('.skh-citation').forEach((citation, index) => {
              citation.addEventListener('click', (e) => {
                e.preventDefault();
                this.showSourceModal(sources[index]);
              });
            });
          } else {
            messageElement.innerHTML = markdownContent;
          }
        } else {
          // Charts were rendered, add citations if present
          if (sources && sources.length > 0) {
            const sourcesContainer = document.createElement('div');
            sourcesContainer.style.cssText = 'margin-top: 8px; padding-top: 8px; border-top: 1px solid #e2e8f0;';

            const sourcesHtml = sources.map((source, index) =>
              `<sup><a href="#" class="skh-citation" data-source="${index}" style="margin-right: 4px;">${index + 1}</a></sup>`
            ).join('');
            sourcesContainer.innerHTML = `<small style="color: #6c757d;">Sources: ${sourcesHtml}</small>`;

            // Add click handlers for citations
            sourcesContainer.querySelectorAll('.skh-citation').forEach((citation, index) => {
              citation.addEventListener('click', (e) => {
                e.preventDefault();
                this.showSourceModal(sources[index]);
              });
            });

            messageElement.appendChild(sourcesContainer);
          }
        }
      } else {
        // User messages remain as plain text
        messageElement.textContent = content;
      }

      this.messagesContainer.appendChild(messageElement);
      this.messagesContainer.scrollTop = this.messagesContainer.scrollHeight;

      this.messages.push({ type, content, sources, timestamp: Date.now() });
    }

    showTyping() {
      const typingElement = document.createElement('div');
      typingElement.className = 'skh-message typing';
      typingElement.textContent = 'Generating response...';
      typingElement.id = 'skh-typing-indicator';

      this.messagesContainer.appendChild(typingElement);
      this.messagesContainer.scrollTop = this.messagesContainer.scrollHeight;
    }

    hideTyping() {
      const typingElement = document.getElementById('skh-typing-indicator');
      if (typingElement) {
        typingElement.remove();
      }
    }

    async sendMessage() {
      const message = this.inputElement.value.trim();
      if (!message || this.isLoading) return;

      // Check if user can send messages (authentication required for private chatbots)
      if (!this.canSendMessage()) {
        this.log('Message blocked: Authentication required');
        return;
      }

      this.isLoading = true;
      this.sendButton.disabled = true;
      this.inputElement.value = '';
      this.inputElement.style.height = 'auto';

      // Add user message
      this.addMessage('user', message);

      // Check if streaming is supported and enabled
      const useStreaming = this.config.enableStreaming !== false && this.isStreamingSupported();

      if (useStreaming) {
        await this.sendStreamingMessage(message);
      } else {
        await this.sendRegularMessage(message);
      }
    }

    isStreamingSupported() {
      return typeof EventSource !== 'undefined' || typeof fetch !== 'undefined';
    }

    async sendStreamingMessage(message) {
      // Show typing indicator
      this.showTyping();

      let errorText;
      try {
        const headers = {
          'Authorization': `Bearer ${this.config.apiKey}`,
          'Content-Type': 'application/json'
        };

        // Add user token for private chatbot access
        if (this.userToken) {
          headers['X-User-Token'] = this.userToken;
        }

        const response = await fetch(`${this.apiBase}/api/public/chatbot/${this.config.chatbotId}/chat`, {
          method: 'POST',
          headers,
          body: JSON.stringify({
            message: message,
            sessionToken: this.sessionToken,
            stream: true, // Enable streaming
            searchModes: this.currentSearchModes,
            userTrackingData: this.userTrackingData,
            metadata: {
              domain: window.location.hostname,
              userAgent: navigator.userAgent,
              referrer: document.referrer,
              ...this.userTrackingData
            }
          })
        });

        if (!response.ok) {
          errorText = await response.json();
          console.error('API Error Response:', errorText);
          const errorMessage = errorText?.message || errorText?.error || 'An error occurred while sending your message';
          alert(errorMessage);
          throw new Error(`Chat request failed: ${response.status} - ${errorText}`);
        }

        // Check if response is streaming (SSE)
        const contentType = response.headers.get('content-type');

        if (contentType && contentType.includes('text/event-stream')) {
          await this.handleStreamingResponse(response);
        } else {
          // Fallback to regular response
          const data = await response.json();
          this.handleRegularResponse(data);
        }

        this.log('Streaming message sent successfully');
      } catch (error) {
        this.hideTyping();
        console.error('Failed to send streaming message:', error);
        this.addMessage('bot', errorText?.message ?? 'Sorry, I encountered an error. Please try again.');
      } finally {
        this.isLoading = false;
        this.sendButton.disabled = false;
        this.inputElement.focus();
      }
    }

    async sendRegularMessage(message) {
      // Show typing indicator
      this.showTyping();

      let errorText;
      try {
        const headers = {
          'Authorization': `Bearer ${this.config.apiKey}`,
          'Content-Type': 'application/json'
        };

        // Add user token for private chatbot access
        if (this.userToken) {
          headers['X-User-Token'] = this.userToken;
        }

        const response = await fetch(`${this.apiBase}/api/public/chatbot/${this.config.chatbotId}/chat`, {
          method: 'POST',
          headers,
          body: JSON.stringify({
            message: message,
            sessionToken: this.sessionToken,
            searchModes: this.currentSearchModes,
            userTrackingData: this.userTrackingData,
            metadata: {
              domain: window.location.hostname,
              userAgent: navigator.userAgent,
              referrer: document.referrer,
              ...this.userTrackingData
            }
          })
        });

        this.hideTyping();

        if (!response.ok) {
          errorText = await response.json();
          console.error('API Error Response:', errorText);
          const errorMessage = errorText?.message || errorText?.error || 'An error occurred while sending your message';
          alert(errorMessage);
          throw new Error(`Chat request failed: ${response.status}`);
        }

        const data = await response.json();
        this.handleRegularResponse(data);

        this.log('Regular message sent successfully', data);
      } catch (error) {
        this.hideTyping();
        console.error('Failed to send message:', error);
        this.addMessage('bot', errorText?.message ?? 'Sorry, I encountered an error. Please try again.');
      } finally {
        this.isLoading = false;
        this.sendButton.disabled = false;
        this.inputElement.focus();
      }
    }

    async handleStreamingResponse(response) {
      this.hideTyping();

      // Create a message element for streaming content
      const messageElement = this.createStreamingMessage();
      let fullResponse = '';
      let sources = [];

      try {
        const reader = response.body.getReader();
        const decoder = new TextDecoder();

        while (true) {
          const { done, value } = await reader.read();
          if (done) break;

          const chunk = decoder.decode(value);
          const lines = chunk.split('\n');

          for (const line of lines) {
            if (line.startsWith('data: ')) {
              try {
                const data = JSON.parse(line.slice(6));

                switch (data.type) {
                  case 'content':
                    fullResponse += data.content;
                    this.updateStreamingMessage(messageElement, fullResponse);
                    if (data.sessionToken) {
                      this.sessionToken = data.sessionToken;
                      this.saveTokensToCookies();
                    }
                    break;

                  case 'sources':
                    sources = data.sources || [];
                    console.log('Received sources:', sources);
                    if (data.sessionToken) {
                      this.sessionToken = data.sessionToken;
                      this.saveTokensToCookies();
                    }
                    break;

                  case 'done':
                    if (data.sessionToken) {
                      this.sessionToken = data.sessionToken;
                      this.saveTokensToCookies();
                    }
                    this.finalizeStreamingMessage(messageElement, fullResponse, sources);
                    return;

                  case 'error':
                    throw new Error(data.error || 'Streaming error occurred');
                }
              } catch (parseError) {
                console.warn('Failed to parse streaming data:', line, parseError);
              }
            }
          }
        }
      } catch (error) {
        console.error('Streaming error:', error);
        this.finalizeStreamingMessage(messageElement, fullResponse || 'Sorry, I encountered an error while streaming the response.', sources);
      }
    }



    handleRegularResponse(data) {
      this.hideTyping();

      // Update session token and save to cookies
      if (data.sessionToken) {
        this.sessionToken = data.sessionToken;
        this.saveTokensToCookies();
      }

      // Add bot response
      console.log('Regular response sources:', data.sources);
      this.addMessage('bot', data.response, data.sources);
    }

    createStreamingMessage() {
      const messageElement = document.createElement('div');
      messageElement.className = 'skh-message bot skh-streaming';

      const messageContent = document.createElement('div');
      messageContent.className = 'skh-message-content';
      messageContent.textContent = '';

      // Add typing cursor for streaming
      const cursor = document.createElement('span');
      cursor.className = 'skh-streaming-cursor';
      cursor.textContent = '▋';
      messageContent.appendChild(cursor);

      messageElement.appendChild(messageContent);
      this.messagesContainer.appendChild(messageElement);
      this.scrollToBottom();

      return messageElement;
    }

    updateStreamingMessage(messageElement, content) {
      const messageContent = messageElement.querySelector('.skh-message-content');
      const cursor = messageContent.querySelector('.skh-streaming-cursor');

      // Update content while preserving cursor
      messageContent.textContent = content;
      messageContent.appendChild(cursor);

      this.scrollToBottom();
    }

    finalizeStreamingMessage(messageElement, content, sources = []) {
      const messageContent = messageElement.querySelector('.skh-message-content');

      // Remove streaming class and cursor
      messageElement.classList.remove('skh-streaming');
      messageContent.textContent = '';

      // Render content as markdown
      const markdownContent = MarkdownParser.parse(content);

      console.log('Finalizing streaming message with sources:', sources);

      // Add content with sources if provided
      if (sources && sources.length > 0) {
        const sourcesHtml = sources.map((source, index) =>
          `<sup><a href="#" class="skh-citation" data-source="${index}">${index + 1}</a></sup>`
        ).join('');
        messageContent.innerHTML = markdownContent + ' ' + sourcesHtml;

        // Add click handlers for citations
        messageContent.querySelectorAll('.skh-citation').forEach((citation, index) => {
          citation.addEventListener('click', (e) => {
            e.preventDefault();
            this.showSourceModal(sources[index]);
          });
        });

        // Store sources for modal display
        messageElement.setAttribute('data-sources', JSON.stringify(sources));
      } else {
        messageContent.innerHTML = markdownContent;
      }

      this.scrollToBottom();
    }

    scrollToBottom() {
      if (this.messagesContainer) {
        this.messagesContainer.scrollTop = this.messagesContainer.scrollHeight;
      }
    }

    showSourceModal(source) {
      console.log('Showing source modal for:', source);
      // Simple modal for showing source information
      const modal = document.createElement('div');
      modal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        z-index: 10001;
        display: flex;
        align-items: center;
        justify-content: center;
      `;

      const content = document.createElement('div');
      content.style.cssText = `
        background: white;
        padding: 20px;
        border-radius: 8px;
        max-width: 500px;
        max-height: 400px;
        overflow-y: auto;
        margin: 20px;
      `;

      // Handle different source types (web vs document)
      const isWebSource = source?.metadata.link;
      const title = source.title || source.metadata?.title || 'Unknown Source';
      const sourceContent = source.content || source.snippet || 'No content available';
      const url = source?.metadata?.link;

      content.innerHTML = `
        <h3>Source</h3>
        <p><strong>${isWebSource ? 'Website' : 'Document'}:</strong> ${title}</p>
        ${url ? `<p><strong>URL:</strong> <a href="${url}" target="_blank" style="color: var(--skh-primary-color, #007bff);">${url}</a></p>` : ''}
        <p><strong>Content:</strong> ${sourceContent}</p>
        <button onclick="this.closest('[style*=fixed]').remove()" style="
          background: var(--skh-primary-color, #007bff);
          color: white;
          border: none;
          padding: 8px 16px;
          border-radius: 4px;
          cursor: pointer;
          margin-top: 10px;
        ">Close</button>
      `;

      modal.appendChild(content);
      document.body.appendChild(modal);

      // Close on background click
      modal.addEventListener('click', (e) => {
        if (e.target === modal) {
          modal.remove();
        }
      });
    }

    showAuthenticationForm() {
      if (!this.messagesContainer) return;

      // Hide input container when showing auth form
      this.hideInputContainer();

      const authForm = document.createElement('div');
      authForm.className = 'skh-auth-form';
      authForm.innerHTML = `
        <div style="padding: 30px 25px; text-align: center; height: 100%; display: flex; flex-direction: column; justify-content: center;">
          <!-- Lock Icon -->
          <div style="margin-bottom: 20px;">
            <svg width="48" height="48" viewBox="0 0 24 24" fill="none" style="margin: 0 auto; color: ${this.config.theme?.primaryColor || '#007bff'};">
              <path d="M18 8h-1V6c0-2.76-2.24-5-5-5S7 3.24 7 6v2H6c-1.1 0-2 .9-2 2v10c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V10c0-1.1-.9-2-2-2zM9 6c0-1.66 1.34-3 3-3s3 1.34 3 3v2H9V6zm9 14H6V10h12v10zm-6-3c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2z" fill="currentColor"/>
            </svg>
          </div>

          <h3 style="margin: 0 0 8px 0; color: #333; font-size: 20px; font-weight: 600;">Sign In Required</h3>
          <p style="margin: 0 0 25px 0; color: #666; font-size: 14px; line-height: 1.4;">
            This is a private chatbot. Please sign in with your account to continue.
          </p>

          <form id="skh-login-form-${this.instanceId}" style="display: flex; flex-direction: column; gap: 15px; max-width: 280px; margin: 0 auto; width: 100%;">
            <div style="position: relative;">
              <input
                type="email"
                id="skh-email-${this.instanceId}"
                placeholder="Email address"
                required
                style="
                  width: 100%;
                  padding: 12px 16px;
                  border: 2px solid #e1e5e9;
                  border-radius: 8px;
                  font-size: 14px;
                  transition: border-color 0.2s ease;
                  box-sizing: border-box;
                  background: #fff;
                "
                onfocus="this.style.borderColor='${this.config.theme?.primaryColor || '#007bff'}'"
                onblur="this.style.borderColor='#e1e5e9'"
              />
            </div>

            <div style="position: relative;">
              <input
                type="password"
                id="skh-password-${this.instanceId}"
                placeholder="Password"
                required
                style="
                  width: 100%;
                  padding: 12px 16px;
                  border: 2px solid #e1e5e9;
                  border-radius: 8px;
                  font-size: 14px;
                  transition: border-color 0.2s ease;
                  box-sizing: border-box;
                  background: #fff;
                "
                onfocus="this.style.borderColor='${this.config.theme?.primaryColor || '#007bff'}'"
                onblur="this.style.borderColor='#e1e5e9'"
              />
            </div>

            <button
              type="submit"
              id="skh-signin-btn-${this.instanceId}"
              style="
                width: 100%;
                padding: 12px 16px;
                background: ${this.config.theme?.primaryColor || '#007bff'};
                color: white;
                border: none;
                border-radius: 8px;
                cursor: pointer;
                font-size: 14px;
                font-weight: 600;
                transition: all 0.2s ease;
                box-sizing: border-box;
              "
              onmouseover="this.style.background='${this.adjustColor(this.config.theme?.primaryColor || '#007bff', -10)}'"
              onmouseout="this.style.background='${this.config.theme?.primaryColor || '#007bff'}'"
            >
              <span id="skh-signin-text-${this.instanceId}">Sign In</span>
              <span id="skh-signin-spinner-${this.instanceId}" style="display: none;">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" style="animation: spin 1s linear infinite; margin-left: 8px;">
                  <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4" stroke-dasharray="31.416" stroke-dashoffset="31.416" opacity="0.3"/>
                  <path d="M12 2a10 10 0 0 1 10 10" stroke="currentColor" stroke-width="4" stroke-linecap="round"/>
                </svg>
              </span>
            </button>
          </form>

          <div id="skh-auth-error-${this.instanceId}" style="
            color: #dc3545;
            font-size: 13px;
            margin-top: 15px;
            display: none;
            padding: 10px;
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 6px;
            text-align: left;
          "></div>

          <p style="margin: 20px 0 0 0; color: #999; font-size: 12px;">
            Need access? Contact your administrator.
          </p>
        </div>

        <style>
          @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
          }
        </style>
      `;

      this.messagesContainer.innerHTML = '';
      this.messagesContainer.appendChild(authForm);

      // Set up form submission
      const form = document.getElementById(`skh-login-form-${this.instanceId}`);
      form.addEventListener('submit', (e) => this.handleLogin(e));
    }

    async handleLogin(event) {
      event.preventDefault();

      const email = document.getElementById(`skh-email-${this.instanceId}`).value;
      const password = document.getElementById(`skh-password-${this.instanceId}`).value;
      const errorDiv = document.getElementById(`skh-auth-error-${this.instanceId}`);
      const submitBtn = document.getElementById(`skh-signin-btn-${this.instanceId}`);
      const submitText = document.getElementById(`skh-signin-text-${this.instanceId}`);
      const submitSpinner = document.getElementById(`skh-signin-spinner-${this.instanceId}`);

      // Show loading state
      submitBtn.disabled = true;
      submitText.style.display = 'none';
      submitSpinner.style.display = 'inline-block';
      errorDiv.style.display = 'none';

      try {
        const response = await fetch(`${this.apiBase}/api/public/auth/login`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            email,
            password,
            chatbotId: this.config.chatbotId
          })
        });

        const result = await response.json();

        if (result.success) {
          this.userToken = result.userToken;

          if (result.chatbotAccess === false) {
            errorDiv.innerHTML = `
              <strong>Access Denied</strong><br>
              You do not have access to this private chatbot. Please contact your administrator to be added to an authorized company.
            `;
            errorDiv.style.display = 'block';
            return;
          }
          this.isAuthenticated = true;

          // Save user token to cookies
          this.saveTokensToCookies();

          // Show success state briefly
          submitText.textContent = 'Success!';
          submitText.style.display = 'inline-block';
          submitSpinner.style.display = 'none';
          submitBtn.style.background = '#28a745';

          // Clear auth form and show greeting after brief delay
          setTimeout(async () => {
            // Update header to show user menu
            if (this.headerElement) {
              this.updateHeader(this.headerElement);
              this.setupHeaderEventListeners();
            }

            // Show input container (enable chat)
            this.showInputContainer();

            // Add sessions accordion for private chatbots
            if (this.requiresAuth && !this.sessionsAccordion) {
              this.sessionsAccordion = this.createSessionsAccordion();
              const target = this.config.mode === 'widget' ? this.chatWindow : this.container;
              target.insertBefore(this.sessionsAccordion, this.messagesContainer);
            }

            // Load chat history if session token exists, otherwise show greeting
            if (this.sessionToken) {
              await this.loadChatHistory();
            } else {
              this.messagesContainer.innerHTML = '';
              this.showGreeting();
            }
            this.log('User authenticated successfully');
          }, 800);
        } else {
          errorDiv.innerHTML = `<strong>Sign In Failed</strong><br>${result.error || 'Please check your credentials and try again.'}`;
          errorDiv.style.display = 'block';
        }
      } catch (error) {
        console.error('Login error:', error);
        errorDiv.innerHTML = `<strong>Connection Error</strong><br>Unable to connect to the server. Please check your internet connection and try again.`;
        errorDiv.style.display = 'block';
      } finally {
        // Reset button state if there was an error
        if (!this.isAuthenticated) {
          submitBtn.disabled = false;
          submitText.style.display = 'inline-block';
          submitSpinner.style.display = 'none';
          submitText.textContent = 'Sign In';
        }
      }
    }

    // Cookie management methods
    setCookie(name, value, days = 7) {
      const expires = new Date();
      expires.setTime(expires.getTime() + (days * 24 * 60 * 60 * 1000));
      document.cookie = `${name}=${value};expires=${expires.toUTCString()};path=/;SameSite=Lax`;
    }

    getCookie(name) {
      const nameEQ = name + "=";
      const ca = document.cookie.split(';');
      for (let i = 0; i < ca.length; i++) {
        let c = ca[i];
        while (c.charAt(0) === ' ') c = c.substring(1, c.length);
        if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length);
      }
      return null;
    }

    deleteCookie(name) {
      document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 UTC;path=/;`;
    }

    // Load tokens from cookies
    loadTokensFromCookies() {
      const savedUserToken = this.getCookie(`skh_user_token_${this.config.chatbotId}`);
      const savedSessionToken = this.getCookie(`skh_session_token_${this.config.chatbotId}`);

      // Only set authenticated if we have a valid user token AND this is a private chatbot
      if (savedUserToken && this.requiresAuth) {
        this.userToken = savedUserToken;
        this.isAuthenticated = true;
        this.log('Loaded user token from cookies');
      } else if (savedUserToken && !this.requiresAuth) {
        // For public chatbots, clear any stale tokens
        this.clearTokensFromCookies();
      }

      if (savedSessionToken && this.isAuthenticated) {
        this.sessionToken = savedSessionToken;
        this.log('Loaded session token from cookies');
      } else if (savedSessionToken && !this.isAuthenticated) {
        // Clear stale session token if not authenticated
        this.deleteCookie(`skh_session_token_${this.config.chatbotId}`);
      }
    }

    // Save tokens to cookies
    saveTokensToCookies() {
      if (this.userToken) {
        this.setCookie(`skh_user_token_${this.config.chatbotId}`, this.userToken, 7); // 7 days
        this.log('Saved user token to cookies');
      }

      if (this.sessionToken) {
        this.setCookie(`skh_session_token_${this.config.chatbotId}`, this.sessionToken, 30); // 30 days
        this.log('Saved session token to cookies');
      }
    }

    // Clear tokens from cookies
    clearTokensFromCookies() {
      this.deleteCookie(`skh_user_token_${this.config.chatbotId}`);
      this.deleteCookie(`skh_session_token_${this.config.chatbotId}`);

      // Also clear any cached sessions
      this.cachedSessions = null;
    }

    // Helper method to adjust color brightness
    adjustColor(color, amount) {
      const usePound = color[0] === '#';
      const col = usePound ? color.slice(1) : color;
      const num = parseInt(col, 16);
      let r = (num >> 16) + amount;
      let g = (num >> 8 & 0x00FF) + amount;
      let b = (num & 0x0000FF) + amount;
      r = r > 255 ? 255 : r < 0 ? 0 : r;
      g = g > 255 ? 255 : g < 0 ? 0 : g;
      b = b > 255 ? 255 : b < 0 ? 0 : b;
      return (usePound ? '#' : '') + (r << 16 | g << 8 | b).toString(16).padStart(6, '0');
    }

    // Update header content based on authentication state
    updateHeader(header) {
      const chatbotName = this.config.serverConfig?.name || 'Chat Assistant';

      // New Chat button for all chatbots
      const newChatButtonHtml = this.isAuthenticated || this.status === "public" ? `
        <button class="skh-new-chat-button" id="skh-new-chat-btn-${this.instanceId}" title="Start New Chat">
          <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
            <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>
          </svg>
        </button>
      ` : '';

      const userMenuHtml = this.isAuthenticated ? `
        <div class="skh-user-menu">
          <button class="skh-user-button" onclick="this.closest('.skh-chatbot-widget').chatbotInstance.signOut()"">
         
            <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
              <path d="M17 7l-1.41 1.41L18.17 11H8v2h10.17l-2.58 2.59L17 17l5-5zM4 5h8V3H4c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h8v-2H4V5z"/>
            </svg>
            
          </button>
          
        </div>
      ` : '';

      header.innerHTML = `
        <div class="skh-header-top">
          <div class="skh-chat-title">${chatbotName}</div>
          <div style="display: flex; align-items: center; gap: 8px;">
            ${newChatButtonHtml}
            ${userMenuHtml}
            ${this.config.mode === 'widget' ? `
              <button class="skh-close-button">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                </svg>
              </button>
            ` : ''}
          </div>
        </div>
      `;

      // Verify the new chat button was created and set up event listeners with proper timing
      setTimeout(() => {
        const newChatBtn = document.getElementById(`skh-new-chat-btn-${this.instanceId}`);

        if (newChatBtn) {
          // Set up the event listener here with proper timing
          newChatBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            this.startNewChatSession();
          });
        }

        // Set up close button event listener with proper timing
        if (this.config.mode === 'widget') {
          const closeButton = this.chatWindow.querySelector('.skh-close-button');
          if (closeButton) {
            this.log('Setting up close button event listener (delayed)');
            closeButton.addEventListener('click', (e) => {
              e.stopPropagation();
              this.toggleChat();
            });
          } else {
            this.log('Close button not found in DOM');
          }
        }
      }, 50); // Increased delay to ensure DOM is ready
    }

    // Create mode selection buttons
    createModeButtons() {
      const searchModes = this.config.searchModes || { internal: true, web: false, mcp: false };
      const enabledModes = Object.keys(searchModes).filter(mode => searchModes[mode]);


      // Don't show mode buttons if only one mode is enabled
      if (enabledModes.length <= 1) {
        this.log('Only one mode enabled, hiding mode buttons. Enabled modes:', enabledModes);
        return '';
      }

      const modeIcons = {
        internal: `<svg class="skh-mode-icon" viewBox="0 0 24 24" fill="currentColor">
          <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
        </svg>`,
        web: `<svg class="skh-mode-icon" viewBox="0 0 24 24" fill="currentColor">
          <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.94-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v1.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z"/>
        </svg>`,
        mcp: `<svg class="skh-mode-icon" viewBox="0 0 24 24" fill="currentColor">
          <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-1-13h2v6h-2zm0 8h2v2h-2z"/>
        </svg>`
      };

      const modeLabels = {
        internal: 'Internal',
        web: 'Web',
        mcp: 'MCP'
      };

      // Filter enabled modes to only include those with valid icons and labels
      const validModes = enabledModes.filter(mode => modeIcons[mode] && modeLabels[mode]);

      this.log('Mode button creation:', {
        enabledModes,
        validModes,
        availableIcons: Object.keys(modeIcons),
        availableLabels: Object.keys(modeLabels)
      });

      const buttons = validModes.map(mode => {
        const isActive = this.currentSearchModes.includes(mode);
        const activeClass = isActive ? ' active' : '';

        return `
          <button class="skh-mode-button${activeClass}" data-mode="${mode}">
            ${modeIcons[mode]}
            ${modeLabels[mode]}
          </button>
        `;
      }).join('');

      return `
        <div class="skh-mode-buttons">
          <div class="skh-mode-buttons-container">
            ${buttons}
          </div>
        </div>
      `;
    }

    // Toggle search mode (supports multiple selections)
    setSearchMode(mode) {
      const searchModes = this.config.searchModes || { internal: true, web: false, mcp: false };

      // Check if the mode is enabled
      if (!searchModes[mode]) {
        console.warn(`Search mode '${mode}' is not enabled for this chatbot`);
        return;
      }

      // Toggle the mode in the current selection
      const currentIndex = this.currentSearchModes.indexOf(mode);
      if (currentIndex > -1) {
        // Mode is currently selected, remove it (but keep at least one mode)
        if (this.currentSearchModes.length > 1) {
          this.currentSearchModes.splice(currentIndex, 1);
        }
      } else {
        // Mode is not selected, add it
        this.currentSearchModes.push(mode);
      }

      // Update mode buttons visual state
      const modeButtons = this.modeButtonsContainer?.querySelectorAll('.skh-mode-button');
      if (modeButtons) {
        modeButtons.forEach(button => {
          const buttonMode = button.getAttribute('data-mode');
          if (this.currentSearchModes.includes(buttonMode)) {
            button.classList.add('active');
          } else {
            button.classList.remove('active');
          }
        });
      }

      // Log mode change for debugging
      this.log(`Search modes changed to: ${this.currentSearchModes.join(', ')}`);

      // You can add additional logic here to handle mode-specific behavior
      // For example, clearing current conversation, showing mode-specific greeting, etc.
    }

    // Set up header event listeners
    setupHeaderEventListeners() {


      // Prevent duplicate event listeners
      if (this.headerListenersSetup) {
        return;
      }

      // User menu event listeners (only for authenticated users)
      if (this.isAuthenticated) {
        const userBtn = document.getElementById(`skh-user-btn-${this.instanceId}`);
        const userDropdown = document.getElementById(`skh-user-dropdown-${this.instanceId}`);

        if (userBtn && userDropdown) {
          userBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            userDropdown.classList.toggle('show');
          });

          // Close dropdown when clicking outside
          document.addEventListener('click', () => {
            userDropdown.classList.remove('show');
          });
        }
      }

      this.headerListenersSetup = true;
    }

    // Hide input container (disable chat)
    hideInputContainer() {
      const inputContainer = this.container?.querySelector('.skh-chat-input-container');
      if (inputContainer) {
        inputContainer.style.display = 'none';
      }

      // Also hide mode buttons during authentication
      if (this.modeButtonsContainer) {
        this.modeButtonsContainer.style.display = 'none';
      }
    }

    // Set up mode button event listeners
    setupModeButtonEventListeners() {
      // Mode button event listeners
      const modeButtons = this.modeButtonsContainer?.querySelectorAll('.skh-mode-button');
      if (modeButtons) {
        modeButtons.forEach(button => {
          button.addEventListener('click', (e) => {
            e.stopPropagation();
            const mode = button.getAttribute('data-mode');
            if (mode) {
              this.setSearchMode(mode);
            }
          });
        });
      }
    }

    // Refresh mode buttons when configuration changes
    refreshModeButtons() {
      if (this.modeButtonsContainer) {
        const modeButtonsHTML = this.createModeButtons();
        this.modeButtonsContainer.innerHTML = modeButtonsHTML;

        // Set up event listeners if buttons were created
        if (modeButtonsHTML.trim()) {
          this.setupModeButtonEventListeners();
          this.log('Mode buttons refreshed with content:', modeButtonsHTML.length, 'characters');
        } else {
          this.log('Mode buttons refreshed but no content (single mode or no modes enabled)');
        }
      }
    }

    // Show input container (enable chat)
    showInputContainer() {
      const inputContainer = this.container?.querySelector('.skh-chat-input-container');
      if (inputContainer) {
        inputContainer.style.display = 'flex';
      }

      // Show mode buttons when chat is enabled
      if (this.modeButtonsContainer) {
        // Refresh mode buttons content in case configuration changed
        this.refreshModeButtons();

        // Show mode buttons for public chatbots or authenticated private chatbots
        if (!this.requiresAuth || this.isAuthenticated) {
          this.modeButtonsContainer.style.display = 'block';
        } else {
          this.modeButtonsContainer.style.display = 'none';
        }
      } else {
        this.log('Mode buttons container not found');
      }
    }

    // Check if user can send messages
    canSendMessage() {
      // For private chatbots, user must be authenticated
      if (this.requiresAuth && !this.isAuthenticated) {
        return false;
      }
      return true;
    }

    // Sign out functionality
    signOut() {
      this.userToken = null;
      this.sessionToken = null; // Also clear session token
      this.isAuthenticated = false;

      // Clear tokens from cookies
      this.clearTokensFromCookies();

      // Remove sessions accordion if it exists
      if (this.sessionsAccordion) {
        this.sessionsAccordion.remove();
        this.sessionsAccordion = null;
      }

      // Update header to remove user menu
      if (this.headerElement) {
        this.updateHeader(this.headerElement);
        this.setupHeaderEventListeners();
      }

      // Hide input container and show authentication form if required
      this.hideInputContainer();
      this.messagesContainer.innerHTML = '';

      // Only show authentication form if this is a private chatbot
      if (this.requiresAuth) {
        this.showAuthenticationForm();
      } else {
        // For public chatbots, just show the input container
        this.showInputContainer();
      }

      this.log('User signed out successfully');
    }

    destroy() {
      try {
        // Clear any ongoing requests or timers
        if (this.typingTimer) {
          clearTimeout(this.typingTimer);
        }

        // Remove event listeners
        if (this.container) {
          // Clone and replace to remove all event listeners
          const newContainer = this.container.cloneNode(false);
          if (this.container.parentNode) {
            this.container.parentNode.replaceChild(newContainer, this.container);
          }
        }

        // Clean up DOM elements
        if (this.chatWindow && this.chatWindow.parentNode) {
          this.chatWindow.parentNode.removeChild(this.chatWindow);
        }

        if (this.container && this.container.parentNode) {
          this.container.parentNode.removeChild(this.container);
        }

        // Clear references
        this.container = null;
        this.chatWindow = null;
        this.messagesContainer = null;
        this.inputElement = null;
        this.sendButton = null;
        this.messages = [];
        this.sessionToken = null;
        this.userToken = null;

        console.log(`Chatbot instance ${this.instanceId} destroyed successfully`);
      } catch (error) {
        console.error('Error during chatbot destruction:', error);
      }
    }
  }



  // Export SDK to global scope
  window.SwissKnowledgeHub = new SwissKnowledgeHubSDK();

})(window, document);
