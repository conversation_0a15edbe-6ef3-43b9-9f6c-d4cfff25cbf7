import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import db from "@/lib/shared-db";
import { authOptions } from "@/lib/next-auth/src/auth-options";
import crypto from "crypto";
export async function POST(request: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { organizationId } = await request.json();

    if (!organizationId) {
      return NextResponse.json(
        { error: "Organization ID is required" },
        { status: 400 }
      );
    }

    const emailHash = crypto
      .createHash("sha256")
      .update(session.user.email)
      .digest("hex");
    // Get the user
    const user = await db.user.findUnique({
      where: {
        emailHash,
      },
      include: {
        membership: {
          where: {
            tenantId: organizationId,
          },
          include: {
            tenant: true,
          },
        },
      },
    });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Check if the user is a member of the organization
    if (user.membership.length === 0) {
      return NextResponse.json(
        { error: "You are not a member of this organization" },
        { status: 403 }
      );
    }

    // Return the organization data
    const organization = user.membership[0].tenant;

    // Create a response and set a cookie to persist the selected organization
    const response = NextResponse.json(
      {
        message: "Current organization set successfully",
        data: organization,
      },
      { status: 200 }
    );

    return response;
  } catch (error) {
    console.error("Error setting current organization:", error);
    return NextResponse.json(
      { error: "Failed to set current organization" },
      { status: 500 }
    );
  }
}
