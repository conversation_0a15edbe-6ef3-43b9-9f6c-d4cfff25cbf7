"use client";

import { useState } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  AlertTriangle,
  Download,
  Settings,
  Plus,
  TrendingUp,
  Activity,
  Calculator,
  Shield
} from "lucide-react";
import { useRouter } from "next/navigation";
import { AddTenantModal } from "./add-tenant-modal";

interface ExecutiveDashboardData {
  kpiMetrics: {
    activeTenants: number;
    activeUsers: number;
    mrr: number;
    infrastructureCost: number;
    grossMargin: number;
    alertsCount: number;
  };
  alerts: Array<{
    id: string;
    type: "usage_spike" | "quota_risk" | "system_error" | "cost_alert";
    severity: "red" | "orange" | "yellow" | "green";
    title: string;
    description: string;
    acknowledged: boolean;
  }>;
}

interface QuickActionsSidebarProps {
  dashboardData: ExecutiveDashboardData;
}

export function QuickActionsSidebar({ dashboardData }: QuickActionsSidebarProps) {
  const router = useRouter();
  const [isAddTenantModalOpen, setIsAddTenantModalOpen] = useState(false);

  const criticalAlerts = dashboardData.alerts.filter(
    alert => alert.severity === "red" && !alert.acknowledged
  ).length;

  const warningAlerts = dashboardData.alerts.filter(
    alert => alert.severity === "orange" && !alert.acknowledged
  ).length;

  // CSV Export functionality
  const handleDownloadCSV = () => {
    const csvData = [
      ['Metric', 'Value'],
      ['Active Tenants', dashboardData.kpiMetrics.activeTenants.toString()],
      ['Active Users', dashboardData.kpiMetrics.activeUsers.toString()],
      ['Monthly Recurring Revenue (CHF)', dashboardData.kpiMetrics.mrr.toFixed(2)],
      ['Infrastructure Cost (CHF)', dashboardData.kpiMetrics.infrastructureCost.toFixed(2)],
      ['Gross Margin (%)', dashboardData.kpiMetrics.grossMargin.toFixed(2)],
      ['Critical Alerts', criticalAlerts.toString()],
      ['Warning Alerts', warningAlerts.toString()],
      ['Export Date', new Date().toISOString()]
    ];

    const csvContent = csvData.map(row => row.join(',')).join('\n');
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');

    if (link.download !== undefined) {
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `dashboard-snapshot-${new Date().toISOString().split('T')[0]}.csv`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  const quickActions = [
    {
      title: "Add Tenant",
      description: "Create new tenant account",
      icon: Plus,
      action: () => setIsAddTenantModalOpen(true),
      color: "blue",
      type: "button" as const
    },
    {
      title: "View Tenants in Risk",
      description: "Show at-risk tenant accounts",
      icon: Shield,
      action: () => router.push("/tenants?filter=risk"),
      color: "orange",
      type: "link" as const
    },
    {
      title: "Open Estimator Tool",
      description: "Cost estimation calculator",
      icon: Calculator,
      action: () => router.push("/tools/cost-estimator"),
      color: "purple",
      type: "link" as const
    },
    {
      title: "Download CSV Snapshot",
      description: "Export current metrics",
      icon: Download,
      action: handleDownloadCSV,
      color: "green",
      type: "button" as const
    }
  ];

  const exportActions = [
    {
      title: "Export Dashboard Data",
      description: "Download current dashboard data",
      action: () => {
        // Implement export functionality
        console.log("Exporting dashboard data...");
      }
    },
    {
      title: "Generate Report",
      description: "Create executive summary report",
      action: () => {
        // Implement report generation
        console.log("Generating report...");
      }
    }
  ];

  return (
    <div className="space-y-6">
      {/* Quick Stats */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Quick Stats
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">Active Tenants</span>
            <Badge variant="secondary">{dashboardData.kpiMetrics.activeTenants}</Badge>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">Active Users</span>
            <Badge variant="secondary">{dashboardData.kpiMetrics.activeUsers}</Badge>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">Monthly Revenue</span>
            <Badge variant="secondary">CHF {dashboardData.kpiMetrics.mrr.toLocaleString()}</Badge>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">Gross Margin</span>
            <Badge variant="secondary">{dashboardData.kpiMetrics.grossMargin.toFixed(1)}%</Badge>
          </div>
        </CardContent>
      </Card>

      {/* Alerts Summary */}
      {(criticalAlerts > 0 || warningAlerts > 0) && false && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-orange-500" />
              Active Alerts
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            {criticalAlerts > 0 && (
              <div className="flex items-center justify-between p-2 bg-red-50 dark:bg-red-950 rounded">
                <span className="text-sm font-medium text-red-700 dark:text-red-300">Critical</span>
                <Badge variant="destructive">{criticalAlerts}</Badge>
              </div>
            )}
            {warningAlerts > 0 && (
              <div className="flex items-center justify-between p-2 bg-orange-50 dark:bg-orange-950 rounded">
                <span className="text-sm font-medium text-orange-700 dark:text-orange-300">Warning</span>
                <Badge variant="secondary">{warningAlerts}</Badge>
              </div>
            )}
            <Button
              variant="outline"
              size="sm"
              className="w-full"
              onClick={() => router.push("/alerts")}
            >
              View All Alerts
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          {quickActions.map((action, index) => (
            <Button
              key={index}
              variant="outline"
              className="w-full justify-start h-auto p-3 text-left"
              onClick={action.action}
            >
              <div className="flex items-start gap-3 w-full">
                <action.icon className="h-5 w-5 mt-0.5 flex-shrink-0" />
                <div className="flex flex-col w-full min-w-0"> {/* enforce wrap here */}
                  <div className="font-medium text-sm">{action.title}</div>
                  <div className="text-xs text-muted-foreground break-words whitespace-normal">
                    {action.description}
                  </div>
                </div>
              </div>
            </Button>
          ))}
        </CardContent>
      </Card>


      {/* Export Actions */}
      {
        false && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Download className="h-5 w-5" />
                Export & Reports
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {exportActions.map((action, index) => (
                <Button
                  key={index}
                  variant="outline"
                  size="sm"
                  className="w-full justify-start"
                  onClick={action.action}
                >
                  <div className="text-left">
                    <div className="font-medium text-sm">{action.title}</div>
                    <div className="text-xs text-muted-foreground">{action.description}</div>
                  </div>
                </Button>
              ))}
            </CardContent>
          </Card>
        )
      }

      {/* System Settings */}
      {
        false && (<Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              System
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <Button
              variant="outline"
              size="sm"
              className="w-full justify-start"
              onClick={() => router.push("/settings")}
            >
              <Settings className="h-4 w-4 mr-2" />
              System Settings
            </Button>
            <Button
              variant="outline"
              size="sm"
              className="w-full justify-start"
              onClick={() => router.push("/storage")}
            >
              <Activity className="h-4 w-4 mr-2" />
              Storage Management
            </Button>
          </CardContent>
        </Card>)
      }

      {/* Add Tenant Modal */}
      <AddTenantModal
        open={isAddTenantModalOpen}
        onOpenChange={setIsAddTenantModalOpen}
      />
    </div>
  );
}
