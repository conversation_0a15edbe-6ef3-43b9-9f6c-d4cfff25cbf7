"use client";

import React from 'react';
import { DragOverlay } from '@dnd-kit/core';
import { History } from 'lucide-react';
import { useChatDrag } from './chat-drag-context';
import { cn } from '@/lib/utils';

export function ChatDragOverlay() {
  const { dragState } = useChatDrag();

  if (!dragState.isDragging || !dragState.draggedItem) {
    return null;
  }

  return (
    <DragOverlay>
      <div className={cn(
        "flex items-center gap-2 rounded-md py-1.5 px-2 text-sm",
        "bg-background border shadow-lg",
        "max-w-[200px]"
      )}>
        <History className="size-3.5 shrink-0 text-muted-foreground" />
        <span className="truncate text-foreground">
          {dragState.draggedItem.chatTitle}
        </span>
      </div>
    </DragOverlay>
  );
}
