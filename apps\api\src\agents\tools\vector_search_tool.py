"""
High-Performance Vector Search Tool for CopilotKit Agent

This tool provides optimized vector search with semantic similarity,
hybrid search capabilities, and intelligent result aggregation.
"""

import asyncio
import logging
from typing import List, Dict, Any, Optional, Type, Tuple
from langchain_core.tools import BaseTool
from langchain_core.callbacks import CallbackManagerFor<PERSON>oolRun
from pydantic import BaseModel, Field
from bson import ObjectId
from pymongo import MongoClient
from langchain_community.vectorstores.azure_cosmos_db import (
    AzureCosmosDBVectorSearch,
    CosmosDBSimilarityType,
    CosmosDBVectorSearchType,
)
from langchain_openai import AzureOpenAIEmbeddings
import os
import time

logger = logging.getLogger(__name__)

class MongoConnectionPool:
    """
    Singleton connection pool for MongoDB clients to improve performance
    by reusing connections across vector searches.
    """
    _instance = None
    _clients = {}

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def get_client(self, connection_string: str) -> MongoClient:
        """Get or create a MongoDB client with optimized connection settings."""
        if connection_string not in self._clients:
            logger.info(f"Creating new MongoDB client with connection pooling")
            self._clients[connection_string] = MongoClient(
                connection_string,
                maxPoolSize=50,  # Increase pool size for better concurrency
                minPoolSize=5,   # Keep minimum connections alive
                maxIdleTimeMS=30000,  # 30 seconds idle timeout
                serverSelectionTimeoutMS=5000,  # 5 seconds server selection
                connectTimeoutMS=10000,  # 10 seconds connection timeout
                socketTimeoutMS=20000,   # 20 seconds socket timeout
                retryWrites=True,
                retryReads=True
            )
            logger.info(f"MongoDB client created with optimized connection pool settings")
        return self._clients[connection_string]

    def close_all(self):
        """Close all MongoDB clients in the pool."""
        for client in self._clients.values():
            client.close()
        self._clients.clear()

# Global connection pool instance
mongo_pool = MongoConnectionPool()

class SafeEmbeddingWrapper:
    """
    A wrapper around embedding models that ensures proper input validation
    to prevent tokenization issues that cause Azure OpenAI API errors.
    """

    def __init__(self, base_embedding_model):
        self.base_model = base_embedding_model

    def embed_query(self, text):
        """Safely embed a query with input validation."""
        if not isinstance(text, str):
            logger.error(f"Expected string for query, got {type(text)}: {text}")
            if isinstance(text, list):
                logger.error("Query appears to be tokenized - this will cause embedding API errors")
                # Check if this looks like token IDs (list of integers)
                if all(isinstance(x, int) for x in text):
                    logger.error(f"Detected token IDs: {text}")
                    raise ValueError(f"Query appears to be tokenized (list of token IDs: {text}) instead of a string. This indicates a bug in the query processing pipeline.")
                raise ValueError("Query appears to be tokenized (list of tokens) instead of a string")
            text = str(text)

        if not text.strip():
            logger.warning("Empty query provided to embedding model")
            # Return a zero vector for empty queries
            return [0.0] * getattr(self.base_model, 'dimension', 1536)

        logger.debug(f"Calling base model embed_query with text: {text[:100]}...")
        return self.base_model.embed_query(text)

    def embed_documents(self, texts):
        """Safely embed documents with input validation."""
        if not isinstance(texts, list):
            logger.error(f"Expected list of texts, got {type(texts)}: {texts}")
            raise ValueError(f"Expected list of texts, got {type(texts)}")

        validated_texts = []
        for i, text in enumerate(texts):
            if not isinstance(text, str):
                logger.error(f"Text at index {i} is not a string: {type(text)} - {text}")
                if isinstance(text, list):
                    logger.error("Text appears to be tokenized - this will cause embedding API errors")
                    raise ValueError(f"Text at index {i} appears to be tokenized (list of tokens) instead of a string")
                text = str(text)
            validated_texts.append(text)

        return self.base_model.embed_documents(validated_texts)

    def __getattr__(self, name):
        """Delegate other attributes to the base model."""
        return getattr(self.base_model, name)

class VectorSearchInput(BaseModel):
    """Input schema for vector search tool."""
    query: str = Field(description="The search query for semantic similarity search")
    workspace_ids: List[str] = Field(description="List of workspace IDs to search in")
    search_type: str = Field(default="similarity", description="Type of search: only 'similarity' is supported")
    top_k: int = Field(default=6, description="Number of top results to return")
    similarity_threshold: float = Field(default=0.5, description="Minimum similarity threshold")
    is_follow_up: bool = Field(default=False, description="Whether this is a follow-up question that doesn't need new vector search")

class VectorSearchTool(BaseTool):
    """
    High-performance vector search tool that performs semantic similarity search
    across multiple workspaces with advanced ranking and filtering.
    """
    
    name: str = "vector_search_tool"
    description: str = """
    Perform semantic vector search to find documents similar to the query.
    Use this tool for finding documents based on semantic meaning rather than exact keywords.
    This tool is particularly useful for conceptual queries, finding related topics,
    or when keyword search might miss relevant content due to different terminology.
    """
    args_schema: Type[BaseModel] = VectorSearchInput

    # Pydantic v2 requires explicit field definitions
    db_client: Any = Field(default=None, exclude=True)
    embedding_model: Any = Field(default=None, exclude=True)
    vector_stores: Dict[str, Any] = Field(default_factory=dict, exclude=True)

    def __init__(self, db_client=None, **kwargs):
        super().__init__(**kwargs)
        self.db_client = db_client
        self.embedding_model = None
        self.vector_stores = {}  # Cache for vector stores
        self._initialize_embedding_model()
    
    def _initialize_embedding_model(self):
        """Initialize the embedding model for vector search."""
        try:
            # Use Azure Vision embeddings instead of LangChain AzureOpenAIEmbeddings
            # to avoid the tokenization bug in langchain-openai 0.3.13
            from ..azure_vision_embeddings import AzureVisionEmbeddings

            base_model = AzureVisionEmbeddings(
                azure_endpoint=os.getenv("AZURE_OPENAI_EMBEDDING_API_ENDPOINT"),
                api_key=os.getenv("AZURE_OPENAI_EMBEDDING_API_KEY"),
                deployment_name=os.getenv("AZURE_OPENAI_EMBEDDING_DEPLOYMENT"),
                api_version=os.getenv("AZURE_OPENAI_EMBEDDING_API_VERSION", "2023-12-01-preview"),
                model=os.getenv("AZURE_OPENAI_EMBEDDING_MODEL", "text-embedding-3-small")
            )

            # Wrap it with our validation wrapper
            self.embedding_model = SafeEmbeddingWrapper(base_model)
            logger.info("Azure Cosmos DB vector search embedding model initialized successfully with safety wrapper")
            return self.embedding_model
        except Exception as e:
            logger.error(f"Failed to initialize embedding model: {e}")
            raise

    async def _enhance_document_metadata(self, original_metadata: Dict[str, Any]) -> Dict[str, Any]:
        """Enhance document metadata by fetching complete file information from database."""
        enhanced_metadata = original_metadata.copy()

        # Try to fetch complete file information from database if fileId is available
        file_id = enhanced_metadata.get("fileId")
        if file_id and self.db_client is not None:
            try:
                from bson import ObjectId
                # Fetch file information from database
                file_info = await self.db_client.File.find_one({"_id": ObjectId(file_id)})
                if file_info:
                    enhanced_metadata.update({
                        "file_name": file_info.get("name", "Unknown"),
                        "file_type": file_info.get("type", "Unknown"),
                        "file_extension": file_info.get("extension", ""),
                        "file_size": file_info.get("size", ""),
                        "file_url": file_info.get("url", ""),
                        "created_at": file_info.get("createdAt", ""),
                        "updated_at": file_info.get("updatedAt", "")
                    })

                    # Also fetch workspace information if available
                    workspace_id = file_info.get("workspaceId")
                    if workspace_id:
                        workspace_info = await self.db_client.Workspace.find_one({"_id": ObjectId(workspace_id)})
                        if workspace_info:
                            enhanced_metadata["workspace"] = {
                                "id": str(workspace_id),
                                "name": workspace_info.get("name", ""),
                                "slug": workspace_info.get("slug", "")
                            }

                    logger.debug(f"Enhanced metadata with file info: {enhanced_metadata.get('file_name', 'Unknown')}")
                else:
                    logger.warning(f"File not found in database: {file_id}")
            except Exception as e:
                logger.warning(f"Failed to fetch file metadata for {file_id}: {e}")

        # Fallback to existing metadata fields if database lookup failed
        if "file_name" not in enhanced_metadata:
            # Use existing metadata fields that should be present in vector embeddings
            source_name = enhanced_metadata.get("source", "")
            if source_name:
                enhanced_metadata.update({
                    "file_name": source_name.split("/")[-1] if "/" in source_name else source_name,
                    "source": source_name
                })
            else:
                enhanced_metadata["file_name"] = "Unknown Document"

        # Ensure workspace information is available
        if "workspace" not in enhanced_metadata:
            enhanced_metadata["workspace"] = {
                "id": enhanced_metadata.get("workspace_id", ""),
                "slug": enhanced_metadata.get("slug", ""),
                "name": enhanced_metadata.get("workspaceName", "")
            }

        return enhanced_metadata

    def _normalize_query_for_search(self, query: str) -> str:
        """
        Normalize query for better semantic search while preserving important context.

        This method applies minimal normalization to improve semantic similarity
        matching while preserving case for proper nouns and technical terms.
        """
        if not query or not isinstance(query, str):
            return query

        # Preserve the original query structure for better semantic matching
        # Only apply minimal normalization to avoid losing important context
        normalized = query.strip()

        # Remove extra whitespace but preserve case for proper nouns and technical terms
        normalized = ' '.join(normalized.split())

        # Only convert to lowercase if the query is all uppercase (likely shouting)
        if normalized.isupper():
            normalized = normalized.lower()

        logger.debug(f"Query normalization: '{query}' -> '{normalized}'")
        return normalized

    def _get_adaptive_threshold(self, query: str, base_threshold: float) -> float:
        """
        Calculate adaptive threshold based on query characteristics for better relevance.

        Shorter queries and specific terms need lower thresholds for better recall.
        """
        query_length = len(query.split())

        # Shorter queries need lower thresholds for better recall
        if query_length <= 2:
            adaptive_threshold = base_threshold * 0.7
        elif query_length <= 5:
            adaptive_threshold = base_threshold * 0.85
        else:
            adaptive_threshold = base_threshold

        # Check for specific patterns that might need special handling
        query_lower = query.lower()

        # Person names, technical terms, or specific identifiers
        if any(indicator in query_lower for indicator in ['who is', 'what is', 'define', 'explain']):
            adaptive_threshold *= 0.8  # Be more lenient for definition queries

        logger.debug(f"Adaptive threshold for query '{query[:50]}...': {base_threshold} -> {adaptive_threshold}")
        return adaptive_threshold

    async def _get_optimal_index_config(self, collection) -> Dict[str, Any]:
        """
        Calculate optimal DiskANN index configuration based on collection size.
        """
        try:
            # Safely get document count for optimal index configuration
            if collection is None:
                logger.warning("Collection is None, using default DiskANN configuration")
                raise ValueError("Collection is None")

            # Get document count with proper error handling
            doc_count = await asyncio.to_thread(collection.count_documents, {})

            if doc_count <= 0:
                logger.warning("Collection appears to be empty, using default configuration")
                doc_count = 1000  # Use reasonable default for calculations

            # Calculate optimal DiskANN parameters
            # maxDegree: typically between 32-128, based on dimensions and data size
            max_degree = min(128, max(32, 1536 // 24))  # Based on embedding dimensions

            # lBuild: typically 50-300, based on collection size
            l_build = min(300, max(50, int(doc_count ** 0.3)))

            logger.info(f"Collection has {doc_count} documents, using maxDegree={max_degree}, lBuild={l_build} for DiskANN")

            # Return DiskANN-specific configuration
            return {
                "maxDegree": max_degree,
                "lBuild": l_build,
                "dimensions": 1536
            }

        except Exception as e:
            logger.warning(f"Failed to get collection stats for DiskANN optimization: {e}")
            # Return safe default configuration for DiskANN
            return {
                "maxDegree": 64,  # Safe default
                "lBuild": 100,   # Safe default
                "dimensions": 1536
            }

    async def _get_unified_vector_store(self) -> Optional[AzureCosmosDBVectorSearch]:
        """Get or create an optimized vector store using the unified vectors collection."""
        # Use a single vector store instance for the unified collection
        if hasattr(self, '_unified_vector_store') and self._unified_vector_store is not None:
            return self._unified_vector_store

        try:
            # Use default configuration from environment variables
            logger.info(f"Creating Azure Cosmos DB vector store for unified vectors collection")
            vector_config = {
                "localUri": os.getenv("VECTOR_DATABASE_URL"),
                "environment": os.getenv("VECTOR_DATABASE_NAME"),
                "provider": "MONGODB"
            }

            # Validate required environment variables
            if not vector_config["localUri"] or not vector_config["environment"]:
                logger.error(f"Missing required environment variables: VECTOR_DATABASE_URL or VECTOR_DATABASE_NAME")
                return None

            # Use connection pool for optimized MongoDB connection
            connection_string = vector_config.get("localUri", os.getenv("VECTOR_DATABASE_URL"))
            db_name = vector_config.get("environment", os.getenv("VECTOR_DATABASE_NAME"))

            logger.debug(f"Getting pooled MongoDB client for unified vectors collection")
            client = mongo_pool.get_client(connection_string)

            # Safely get database and collection references
            try:
                db = client[db_name]
                collection = db["ada3Vector"]  # Use unified vectors collection

                # Verify collection exists and is accessible
                logger.debug(f"Verifying collection access for 'vectors'")

                # Test collection access with a simple operation
                try:
                    # This should not trigger the boolean error
                    collection_name = collection.name
                    logger.debug(f"Successfully accessed collection: {collection_name}")
                except Exception as access_error:
                    logger.warning(f"Collection access test failed: {access_error}")

                # Get optimal index configuration based on collection size
                index_config = await self._get_optimal_index_config(collection)

            except Exception as db_error:
                logger.error(f"Failed to access database or collection: {db_error}")
                # Use default configuration if we can't access the collection
                index_config = {
                    "maxDegree": 64,
                    "lBuild": 100,
                    "dimensions": 1536
                }
                # Still try to get the collection reference for vector store creation
                db = client[db_name]
                collection = db["ada3Vector"]

            # Ensure sharded DiskANN vector index exists with optimal configuration
            try:
                await self._ensure_sharded_vector_index(collection, "sharded_diskann_vector_index", index_config)
            except Exception as index_error:
                logger.warning(f"Failed to ensure sharded vector index: {index_error}")
                # Continue anyway - index might already exist

            # Create Azure Cosmos DB vector store for unified collection
            logger.debug(f"Creating Azure Cosmos DB vector store for unified vectors collection")
            try:
                logger.debug(f"Vector store parameters: collection={type(collection)}, embedding={type(self.embedding_model)}")
                vector_store = AzureCosmosDBVectorSearch(
                    collection=collection,
                    embedding=self.embedding_model,
                    index_name="sharded_diskann_vector_index",
                    text_key="content",
                    embedding_key="embedding"
                )
                logger.debug(f"Successfully created vector store: {type(vector_store)}")
            except Exception as vs_error:
                logger.error(f"Failed to create AzureCosmosDBVectorSearch: {vs_error}")
                logger.error(f"Collection type: {type(collection)}, Collection name: {getattr(collection, 'name', 'unknown')}")
                return None

            # Cache the unified vector store
            self._unified_vector_store = vector_store
            logger.info(f"Optimized unified vector store created and cached")
            return vector_store

        except Exception as e:
            logger.error(f"Failed to create unified vector store: {e}")
            return None

    async def _get_vector_store(self, workspace_id: str) -> Optional[AzureCosmosDBVectorSearch]:
        """Get the unified vector store (maintained for backward compatibility)."""
        return await self._get_unified_vector_store()

    def _extract_tenant_from_workspaces(self, workspace_ids: List[str]) -> Optional[str]:
        """Extract tenant information from workspace IDs for optimized filtering."""
        # This is a placeholder - in a real implementation, you might:
        # 1. Query the database to get tenant info for workspaces
        # 2. Use a mapping service
        # 3. Extract from workspace ID patterns
        # For now, we'll let the database handle workspace filtering
        return None

    async def _ensure_vector_index(self, collection, index_name: str, index_config: Dict[str, Any]):
        """
        Ensure vector index exists with optimal configuration for Azure Cosmos DB.
        """
        try:
            # Check if index exists first
            indexes = await asyncio.to_thread(collection.list_indexes)
            existing_indexes = list(indexes)

            # Check if an index with the same name already exists
            index_exists = any(idx.get("name") == index_name for idx in existing_indexes)

            if index_exists:
                logger.info(f"Vector index {index_name} already exists, skipping creation")
                return

            logger.info(f"Creating Azure Cosmos DB vector index {index_name}")

            # Create the index directly on the collection with proper field mapping
            await asyncio.to_thread(
                collection.create_index,
                [("embedding", "cosmosSearch")],  # Use 'embedding' field to match existing data
                name=index_name,
                cosmosSearchOptions={
                    "kind": "vector-ivf",
                    "numLists": index_config["num_lists"],
                    "similarity": "COS",
                    "dimensions": index_config["dimensions"]
                }
            )
            logger.info(f"Azure Cosmos DB vector index {index_name} created with optimal configuration")

        except Exception as e:
            error_msg = str(e)
            if "IndexKeySpecsConflict" in error_msg or "existing index" in error_msg:
                logger.info(f"Vector index {index_name} already exists with different configuration, continuing...")
            else:
                logger.warning(f"Failed to ensure Azure Cosmos DB vector index {index_name}: {e}")
            # Continue without failing - index might already exist or be created by other means

    async def _ensure_sharded_vector_index(self, collection, index_name: str, index_config: Dict[str, Any]):
        """
        Ensure sharded DiskANN vector index exists with tenantId sharding for unified collection.
        """
        try:
            # Check if index exists first
            indexes = await asyncio.to_thread(collection.list_indexes)
            existing_indexes = list(indexes)

            # Check if an index with the same name already exists
            index_exists = any(idx.get("name") == index_name for idx in existing_indexes)

            if index_exists:
                logger.info(f"Sharded vector index {index_name} already exists, skipping creation")
                return

            logger.info(f"Creating sharded DiskANN vector index {index_name} for unified collection")

            # Create sharded DiskANN index configuration with higher lSearch limit
            sharded_config = {
                "kind": "vector-diskann",
                "similarity": "COS",
                "dimensions": 1536,
                "maxDegree": index_config.get("maxDegree", 64),
                "lBuild": index_config.get("lBuild", 100),
                "lSearch": 100,  # Set higher lSearch limit to allow more results
                "vectorIndexShardKey": ["/tenantId"]  # Shard by tenantId
            }

            # Create the index directly on the collection
            await asyncio.to_thread(
                collection.create_index,
                [("embedding", "cosmosSearch")],
                name=index_name,
                cosmosSearchOptions=sharded_config
            )
            await asyncio.to_thread(
                collection.create_index,
                [("tenantId", 1)],
                name="tenantId_filter_index"
            )

            # WorkspaceId index
            await asyncio.to_thread(
                collection.create_index,
                [("workspaceId", 1)],
                name="workspaceId_filter_index"
            )

            # PageId index
            await asyncio.to_thread(
                collection.create_index,
                [("pageId", 1)],
                name="pageId_filter_index"
            )

            # Compound indexes
            await asyncio.to_thread(
                collection.create_index,
                [("tenantId", 1), ("workspaceId", 1)],
                name="tenant_workspace_filter_index"
            )

            await asyncio.to_thread(
                collection.create_index,
                [("tenantId", 1), ("workspaceId", 1), ("pageId", 1)],
                name="tenant_workspace_page_filter_index"
            )
            logger.info(f"Sharded DiskANN vector index {index_name} created successfully")

        except Exception as e:
            error_msg = str(e)
            if "IndexKeySpecsConflict" in error_msg or "existing index" in error_msg:
                logger.info(f"Sharded vector index {index_name} already exists with different configuration, continuing...")
            else:
                logger.warning(f"Failed to ensure sharded vector index {index_name}: {e}")
            # Continue without failing - index might already exist or be created by other means

    async def _similarity_search_unified(self, workspace_ids: List[str], query: str, top_k: int,
                                        similarity_threshold: float, normalized_query: str) -> List[Tuple[Dict[str, Any], float]]:
        """Perform optimized similarity search across workspaces using unified collection."""
        embed_start = time.time()

        try:
            logger.debug(f"Getting unified vector store for {len(workspace_ids)} workspaces")
            vector_store = await self._get_unified_vector_store()
            if vector_store is None:
                logger.error("Failed to get unified vector store")
                return []

            logger.debug(f"Performing unified similarity search for {len(workspace_ids)} workspaces with query: '{query[:100]}...'")

            # Ensure query is definitely a string before calling vector store
            if not isinstance(normalized_query, str):
                logger.error(f"Query is not a string before vector store call: {type(normalized_query)} - {normalized_query}")
                normalized_query = str(normalized_query)

            # Use adaptive threshold for better relevance
            adaptive_threshold = self._get_adaptive_threshold(query, similarity_threshold)

            # Retrieve more documents initially for better filtering, but respect DiskANN limits
            # With lSearch=100 configured, we can request up to 100 results
            # Be conservative and use a reasonable multiplier
            initial_k = min(top_k * 3, 80)  # Cap at 80 to stay well within lSearch=100 limit

            # Create optimized filter for multi-workspace search in unified collection
            # Try to use tenant-based filtering if possible for better sharded index performance
            tenant_id = self._extract_tenant_from_workspaces(workspace_ids)

            if tenant_id:
                # Use tenant + workspace filter for optimal sharded index performance
                workspace_filter = {
                    "$and": [
                        {"tenantId": tenant_id},
                        {"workspaceId": {"$in": workspace_ids}}
                    ]
                }
                logger.debug(f"Using tenant-optimized filter for tenant {tenant_id}")
            else:
                # Fall back to workspace-only filter
                workspace_filter = {
                    "workspaceId": {"$in": workspace_ids}
                }
                logger.debug(f"Using workspace-only filter for {len(workspace_ids)} workspaces")

            # Perform similarity search with error handling for DiskANN limits
            try:
                logger.info(f"Calling similarity_search_with_score with k={initial_k}, filter={workspace_filter} normalized_query={normalized_query}")
                docs_with_scores = await asyncio.to_thread(
                    vector_store.similarity_search_with_score,
                    normalized_query,
                    k=initial_k,
                    pre_filter=workspace_filter,
                    kind=CosmosDBVectorSearchType.VECTOR_DISKANN,
                )
                logger.debug(f"Successfully got {len(docs_with_scores)} results from similarity search")
            except Exception as search_error:
                error_msg = str(search_error)
                if "lSearch" in error_msg or "must be less than or equal to" in error_msg:
                    # Retry with a smaller k value
                    fallback_k = min(top_k, 20)  # Very conservative fallback
                    logger.warning(f"DiskANN k limit exceeded, retrying with k={fallback_k}: {error_msg}")
                    docs_with_scores = await asyncio.to_thread(
                        vector_store.similarity_search_with_score,
                        normalized_query,
                        k=fallback_k,
                        pre_filter=workspace_filter,
                        kind=CosmosDBVectorSearchType.VECTOR_DISKANN,
                    )
                else:
                    raise search_error

            # Enhanced similarity calculation and filtering
            results = []
            filtered_count = 0
            effective_threshold = min(adaptive_threshold, 0.05)  # Define threshold outside loop

            for doc, score in docs_with_scores:
                # Enhanced similarity score calculation
                # MongoDB Atlas returns cosine distance (0 = identical, 2 = opposite)
                similarity = self._calculate_similarity_score(score, query, doc.page_content)

                # Apply adaptive threshold for relevance filtering
                if similarity >= effective_threshold:
                    # Enhance metadata using existing vector embedding metadata
                    # enhanced_metadata = await self._enhance_document_metadata(doc.metadata)

                    # Get workspace_id from document metadata (unified collection structure)
                    doc_workspace_id = doc.metadata.get("workspaceId", "unknown")

                    # Debug logging for metadata
                    if logger.isEnabledFor(logging.DEBUG):
                        logger.debug(f"Original metadata keys: {list(doc.metadata.keys())}")
                        # logger.debug(f"Enhanced metadata keys: {list(enhanced_metadata.keys())}")
                        logger.debug(f"Document workspace: {doc_workspace_id}")

                    result = {
                        "content": doc.page_content,
                        "metadata": {
                            **doc.metadata,
                            "workspace_id": doc_workspace_id,
                            "similarity_score": similarity,
                            "search_score": float(score),
                            "raw_distance": float(score),
                            "adaptive_threshold": adaptive_threshold
                        }
                    }
                    results.append((result, similarity))
                else:
                    filtered_count += 1

            embed_time = time.time() - embed_start
            logger.info(f"Unified similarity search completed in {embed_time:.3f}s for {len(workspace_ids)} workspaces")
            logger.info(f"Found {len(results)} similar documents across workspaces (filtered out {filtered_count} below adaptive threshold {effective_threshold:.3f})")
            return results

        except Exception as e:
            error_msg = str(e)
            if "Database objects do not implement truth value testing" in error_msg:
                logger.error(f"MongoDB database object error in unified similarity search: {e}")
                logger.error("This suggests an issue with async database operations or object handling")
            elif "lSearch" in error_msg:
                logger.error(f"DiskANN lSearch limit error: {e}")
            else:
                logger.error(f"Error in unified similarity search: {e}")
            return []

    async def _similarity_search(self, workspace_id: str, query: str, top_k: int,
                                similarity_threshold: float, normalized_query: str) -> List[Tuple[Dict[str, Any], float]]:
        """Legacy method - redirects to unified search for single workspace."""
        return await self._similarity_search_unified([workspace_id], query, top_k, similarity_threshold, normalized_query)

    def _calculate_similarity_score(self, raw_score: float, query: str, content: str) -> float:
        """
        Calculate enhanced similarity score with content-aware boosting.
        """
        # Base similarity calculation for cosine distance
        if raw_score <= 2.0:
            similarity = max(0.0, 1.0 - (float(raw_score) / 2.0))
        else:
            similarity = max(0.0, 1.0 / (1.0 + float(raw_score)))

        # Content-aware boosting
        content_lower = content.lower()
        query_lower = query.lower()
        query_terms = [term for term in query_lower.split() if len(term) > 2]

        # Boost for exact term matches
        exact_matches = sum(1 for term in query_terms if term in content_lower)
        if exact_matches > 0:
            boost_factor = 1.0 + (exact_matches * 0.1)  # 10% boost per exact match
            similarity = min(1.0, similarity * boost_factor)

        # Special boost for person name queries
        if any(term in content_lower for term in query_terms):
            similarity = min(1.0, similarity * 1.2)  # 20% boost for term matches

        return similarity

    def _calculate_content_relevance_boost(self, query: str, content: str) -> float:
        """
        Calculate content relevance boost based on term matching and context.
        """
        query_lower = query.lower()
        content_lower = content.lower()
        query_terms = [term for term in query_lower.split() if len(term) > 2]

        if not query_terms:
            return 1.0

        # Calculate term match ratio
        matches = sum(1 for term in query_terms if term in content_lower)
        match_ratio = matches / len(query_terms)

        # Base boost: 1.0 to 1.5 based on match ratio
        boost = 1.0 + (match_ratio * 0.5)

        return boost

    async def _parallel_vector_search(self, workspace_ids: List[str], query: str,
                                    search_type: str, top_k: int,
                                    similarity_threshold: float) -> List[Tuple[Dict[str, Any], float]]:
        """Perform optimized vector search using unified collection (no longer parallel per workspace)."""
        # Pre-normalize query once for the search
        normalized_query = self._normalize_query_for_search(query)

        # Use unified search instead of parallel workspace searches
        logger.info(f"Performing unified vector search across {len(workspace_ids)} workspaces")

        # Single search across all workspaces using unified collection
        results_with_scores = await self._similarity_search_unified(
            workspace_ids, query, top_k, similarity_threshold, normalized_query
        )

        logger.info(f"Completed unified vector search across {len(workspace_ids)} workspaces, found {len(results_with_scores)} total results")
        return results_with_scores
    
    def _aggregate_and_rank_results(self, results: List[Tuple[Dict[str, Any], float]], 
                                  top_k: int) -> List[Dict[str, Any]]:
        """Aggregate results from multiple workspaces and rank them."""
        if not results:
            return []
        
        # Sort by score (descending)
        sorted_results = sorted(results, key=lambda x: x[1], reverse=True)
        
        # Take top_k and format for output
        final_results = []
        for result, score in sorted_results[:top_k]:
            result["metadata"]["final_score"] = score
            final_results.append(result)
        
        return final_results
    
    async def _arun(
        self,
        query: str,
        workspace_ids: List[str],
        search_type: str = "similarity",
        top_k: int = 5,
        similarity_threshold: float = 0.5,
        is_follow_up: bool = False,
        run_manager: Optional[CallbackManagerForToolRun] = None,
    ) -> str:
        """Async implementation of the vector search tool."""
        # Initialize embedding model to ensure it's ready for use
        start_time = time.time()
        self.embedding_model.embed_query(query)  # Ensure embedding model is initialized
        logger.info(f"Embedding model initialized in {time.time() - start_time:.3f}s")

        start_time = time.time()
        try:
            # Validate query input early to catch tokenization issues
            if not isinstance(query, str):
                logger.error(f"Vector search received non-string query: {type(query)} - {query}")
                if isinstance(query, list):
                    logger.error("Query appears to be tokenized - this will cause embedding API errors")
                    return {
                        "content": "Error: Query was tokenized instead of being a string. This indicates a bug in the query processing pipeline.",
                        "documents": []
                    }
                query = str(query)

            if not query.strip():
                logger.warning("Empty query provided to vector search")
                return {
                    "content": "No query provided for vector search.",
                    "documents": []
                }

            logger.info(f"Starting {search_type} vector search across {len(workspace_ids)} workspaces for query: '{query[:100]}...'")
            logger.debug(f"Full query: {query}")
            
            # Use adaptive threshold for initial search
            adaptive_threshold = self._get_adaptive_threshold(query, similarity_threshold)
            logger.debug(f"Using adaptive threshold {adaptive_threshold:.3f} for initial search")

            # Perform parallel vector search with adaptive threshold
            results_with_scores = await self._parallel_vector_search(
                workspace_ids, query, search_type, top_k, adaptive_threshold
            )

            # If no results found with adaptive threshold, try with progressively lower thresholds
            if not results_with_scores:
                fallback_thresholds = [
                    max(0.05, adaptive_threshold * 0.5),  # Half of adaptive threshold
                    0.05,  # Very low threshold
                    0.01   # Extremely low threshold for last resort
                ]

                for fallback_threshold in fallback_thresholds:
                    if fallback_threshold != adaptive_threshold:  # Don't repeat the same threshold
                        logger.info(f"No results found with adaptive threshold {adaptive_threshold:.3f}, trying fallback threshold {fallback_threshold:.3f}")
                        results_with_scores = await self._parallel_vector_search(
                            workspace_ids, query, search_type, top_k, fallback_threshold
                        )
                        if results_with_scores:
                            logger.info(f"Found {len(results_with_scores)} results with fallback threshold {fallback_threshold:.3f}")
                            break

            if not results_with_scores:
                logger.info("No vector search results found even with fallback threshold")
                return {
                    "content": "No semantically similar documents found.",
                    "documents": []
                }
            
            # Aggregate and rank results
            # final_results = self._aggregate_and_rank_results(results_with_scores, top_k)
            
            # Format response
            response_parts = [f"Found {len(results_with_scores)} semantically similar documents:"]

            # Convert results to proper format for response
            formatted_results = []
            for i, item in enumerate(results_with_scores, 1):
                # Handle both tuple format (result, score) and dict format
                if isinstance(item, tuple) and len(item) == 2:
                    doc, score = item
                    metadata = doc.get("metadata", {})

                    # Get document information with enhanced metadata
                    file_name = metadata.get("file_name", metadata.get("source", "Unknown Document"))
                    file_type = metadata.get("file_type", "")
                    workspace_name = metadata.get("workspace", {}).get("name", "")
                    content_preview = doc.get("content", "")[:150]

                    # Add final_score to metadata for consistency
                    metadata["final_score"] = score
                    formatted_results.append(doc)
                else:
                    # Assume it's already a dict
                    doc = item
                    metadata = doc.get("metadata", {})
                    score = metadata.get("final_score", 0)

                    # Get document information with enhanced metadata
                    file_name = metadata.get("file_name", metadata.get("source", "Unknown Document"))
                    file_type = metadata.get("file_type", "")
                    workspace_name = metadata.get("workspace", {}).get("name", "")
                    content_preview = doc.get("content", "")[:150]
                    formatted_results.append(doc)

                # Create more informative response with document metadata
                doc_info = f"📄 {file_name}"
                if file_type:
                    doc_info += f" ({file_type})"
                if workspace_name:
                    doc_info += f" | Workspace: {workspace_name}"

                response_parts.append(
                    f"\n[{i}] {doc_info} (Score: {score:.3f})\n"
                    f"Content: {content_preview}..."
                )
            
            elapsed_time = time.time() - start_time
            logger.info(f"Vector search completed in {elapsed_time:.2f} seconds")

            return {"content": "\n".join(response_parts), "documents": formatted_results}
            
        except Exception as e:
            logger.error(f"Error in vector search: {e}")
            return {
                "content": f"Error performing vector search: {str(e)}",
                "documents": []
            }
    
    def _run(
        self,
        query: str,
        workspace_ids: List[str],
        search_type: str = "similarity",
        top_k: int = 6,
        similarity_threshold: float = 0.5,
        is_follow_up: bool = False,
        run_manager: Optional[CallbackManagerForToolRun] = None,
    ) -> str:
        """Sync wrapper for async implementation."""
        return asyncio.run(self._arun(query, workspace_ids, search_type, top_k, similarity_threshold, is_follow_up, run_manager))
