import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/next-auth";
import db from "@/lib/shared-db";

// Helper function to create a consistent user identifier
function createUserKey(session: any): string {
  // For authenticated users, use userId or email
  if (session.user?.id) {
    return `user_${session.user.id}`;
  }
  if (session.user?.email) {
    return `email_${session.user.email}`;
  }

  // For anonymous users, use IP address
  if (session.ipAddress) {
    return `ip_${session.ipAddress}`;
  }

  // Fallback to session ID
  return `session_${session.id}`;
}

// Helper function to create a display name for the user
function createUserDisplayName(session: any, userKey: string): string {
  if (session.user?.name) {
    return session.user.name;
  }
  if (session.user?.email) {
    return session.user.email;
  }

  // For anonymous users, create a friendly name
  if (userKey.startsWith("ip_")) {
    const location =
      session.country && session.city
        ? `${session.city}, ${session.country}`
        : session.country || "Unknown Location";
    const device = session.deviceType || "Unknown Device";
    return `Anonymous User (${location}, ${device})`;
  }

  return "Anonymous User";
}

export async function GET(request: NextRequest) {
  try {
    const session: any = await getServerSession(authOptions);
    if (!session?.userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const tenantId = searchParams.get("tenantId");
    const timeRange = searchParams.get("timeRange") || "7d";
    const chatbotId = searchParams.get("chatbotId");
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "50");
    const search = searchParams.get("search") || "";

    if (!tenantId) {
      return NextResponse.json(
        { error: "Tenant ID is required" },
        { status: 400 }
      );
    }

    // Time range mapping
    const timeRangeMap: Record<string, number | null> = {
      "7d": 7 * 24 * 60 * 60 * 1000,
      "30d": 30 * 24 * 60 * 60 * 1000,
      "90d": 90 * 24 * 60 * 60 * 1000,
      "all": null, // No time filtering for "all time"
    };

    // Build filter for sessions
    const sessionFilter: any = {
      chatbot: {
        tenantId: tenantId,
      },
    };

    // Add time filtering only if not "all time"
    if (timeRange !== "all" && timeRangeMap[timeRange] !== null) {
      const now = new Date();
      const timeRangeMs = timeRangeMap[timeRange] || timeRangeMap["7d"];
      if (timeRangeMs) {
        const startDate = new Date(now.getTime() - timeRangeMs);
        sessionFilter.startedAt = {
          gte: startDate,
        };
      }
    }

    if (chatbotId) {
      sessionFilter.chatbotId = chatbotId;
    }

    // Get user sessions with aggregated data
    const sessions = await db.chatbotSession.findMany({
      where: sessionFilter,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            emailHash: true,
          },
        },
        chatbot: {
          select: {
            id: true,
            name: true,
          },
        },
        messages: {
          select: {
            id: true,
            role: true,
            createdAt: true,
          },
        },
      },
      orderBy: {
        startedAt: "desc",
      },
      skip: (page - 1) * limit,
      take: limit,
    });

    // Get total count for pagination
    const totalSessions = await db.chatbotSession.count({
      where: sessionFilter,
    });

    // Process sessions to create user analytics
    // Group by userId for authenticated users, by IP address for anonymous users
    const userMap = new Map<string, any>();

    console.log(`Processing ${sessions.length} sessions for user analytics...`);

    sessions.forEach((session) => {
      // Create a unique identifier for the user using the helper function
      const userKey = createUserKey(session);
      const displayName = createUserDisplayName(session, userKey);

      console.log(
        `Session ${session.id}: userKey=${userKey}, displayName=${displayName}`
      );

      if (!userMap.has(userKey)) {
        userMap.set(userKey, {
          userKey,
          userId: session.user?.id,
          name: displayName,
          email: session.user?.email,
          emailHash: session.user?.emailHash,
          sessions: [],
          totalSessions: 0,
          totalMessages: 0,
          domains: new Set<string>(),
          userAgents: new Set<string>(),
          ipAddresses: new Set<string>(),
          chatbots: new Set<string>(),
          firstSeen: session.startedAt,
          lastSeen: session.lastActivityAt,
          totalDuration: 0,
          averageResponseTime: 0,
          satisfactionRatings: [],
          errorCount: 0,
        });
      }

      const userData = userMap.get(userKey);
      userData.sessions.push({
        id: session.id,
        sessionToken: session.sessionToken,
        chatbotId: session.chatbotId,
        chatbotName: session.chatbot.name,
        domain: session.domain,
        userAgent: session.userAgent,
        ipAddress: session.ipAddress,
        country: session.country,
        startedAt: session.startedAt,
        lastActivityAt: session.lastActivityAt,
        duration: session.duration,
        messagesCount: session.messagesCount,
        averageResponseTime: session.averageResponseTime,
        satisfactionRating: session.satisfactionRating,
        errorCount: session.errorCount,
        messages: session.messages,
      });

      userData.totalSessions += 1;
      userData.totalMessages += session.messagesCount;
      if (session.domain) userData.domains.add(session.domain);
      if (session.userAgent) userData.userAgents.add(session.userAgent);
      if (session.ipAddress) userData.ipAddresses.add(session.ipAddress);
      userData.chatbots.add(session.chatbot.name);

      if (session.startedAt < userData.firstSeen) {
        userData.firstSeen = session.startedAt;
      }
      if (session.lastActivityAt > userData.lastSeen) {
        userData.lastSeen = session.lastActivityAt;
      }

      userData.totalDuration += session.duration || 0;
      if (session.averageResponseTime) {
        userData.averageResponseTime =
          (userData.averageResponseTime + session.averageResponseTime) / 2;
      }
      if (session.satisfactionRating) {
        userData.satisfactionRatings.push(session.satisfactionRating);
      }
      userData.errorCount += session.errorCount;
    });

    // Convert map to array and process final calculations
    const users = Array.from(userMap.values()).map((user) => ({
      ...user,
      domains: Array.from(user.domains),
      userAgents: Array.from(user.userAgents),
      ipAddresses: Array.from(user.ipAddresses),
      chatbots: Array.from(user.chatbots),
      averageDuration:
        user.totalSessions > 0 ? user.totalDuration / user.totalSessions : 0,
      averageSatisfaction:
        user.satisfactionRatings.length > 0
          ? user.satisfactionRatings.reduce(
              (sum: number, rating: number) => sum + rating,
              0
            ) / user.satisfactionRatings.length
          : null,
      errorRate:
        user.totalSessions > 0
          ? (user.errorCount / user.totalSessions) * 100
          : 0,
    }));

    // Apply search filter if provided
    const filteredUsers = search
      ? users.filter(
          (user) =>
            user.email?.toLowerCase().includes(search.toLowerCase()) ||
            user.name?.toLowerCase().includes(search.toLowerCase()) ||
            user.domains.some((domain: string) =>
              domain.toLowerCase().includes(search.toLowerCase())
            ) ||
            user.ipAddresses.some((ip: string) => ip.includes(search))
        )
      : users;

    // Calculate overview statistics
    const overview = {
      totalUsers: filteredUsers.length,
      totalSessions: filteredUsers.reduce(
        (sum, user) => sum + user.totalSessions,
        0
      ),
      totalMessages: filteredUsers.reduce(
        (sum, user) => sum + user.totalMessages,
        0
      ),
      averageSessionsPerUser:
        filteredUsers.length > 0
          ? filteredUsers.reduce((sum, user) => sum + user.totalSessions, 0) /
            filteredUsers.length
          : 0,
      averageMessagesPerUser:
        filteredUsers.length > 0
          ? filteredUsers.reduce((sum, user) => sum + user.totalMessages, 0) /
            filteredUsers.length
          : 0,
      // Fix: Use session data directly for top domains to get accurate counts
      topDomains: getTopItems(sessions.map((s) => s.domain).filter(Boolean)),
      topUserAgents: getTopItems(
        sessions.map((s) => s.userAgent).filter(Boolean)
      ),
      topCountries: getTopItems(sessions.map((s) => s.country).filter(Boolean)),
    };

    return NextResponse.json({
      users: filteredUsers,
      overview,
      pagination: {
        page,
        limit,
        total: totalSessions,
        totalPages: Math.ceil(totalSessions / limit),
      },
    });
  } catch (error) {
    console.error("Error fetching user analytics:", error);
    return NextResponse.json(
      { error: "Failed to fetch user analytics" },
      { status: 500 }
    );
  }
}

// Helper function to get top items with counts
function getTopItems(items: string[], limit: number = 10) {
  const counts = items.reduce((acc: Record<string, number>, item) => {
    acc[item] = (acc[item] || 0) + 1;
    return acc;
  }, {});

  return Object.entries(counts)
    .sort(([, a], [, b]) => b - a)
    .slice(0, limit)
    .map(([item, count]) => ({ item, count }));
}
