---
title: "Administratorhandbuch"
description: "Umfassender Leitfaden für Administratoren von Swiss Knowledge Hub"
---

# Administratorhandbuch

Dieser Leitfaden bietet Administratoren von Swiss Knowledge Hub detaillierte Informationen über Benutzerverwaltung, Abrechnung, Einstellungen und Systemkonfiguration.

## Administrator-Dashboard

Als Administrator haben Sie Zugriff auf zusätzliche Funktionen und Einstellungen:

1. **Admin-Dashboard**: Zugriff über einen Klick auf „Dashboard“ in der Hauptnavigation  
2. **Admin-Steuerung**: befindet sich im Bereich „Einstellungen“  
3. **Systemmetriken**: Anzeige von Nutzungsstatistiken und Leistungskennzahlen  

## Benutzerverwaltung

### Benutzerrollen und Berechtigungen

Swiss Knowledge Hub verfügt über mehrere Benutzerrollen mit unterschiedlichen Berechtigungen:

| Rolle | Bereich | Fähigkeiten |
|------|-------|-------------|
| **Systemadministrator** | Gesamtes System | Vollständiger Zugriff auf alle Funktionen und Einstellungen |
| **Arbeitsbereichsadministrator** | Spezifischer Arbeitsbereich | Verwaltung von Arbeitsbereichseinstellungen und Mitgliedern |
| **Editor** | Spezifischer Arbeitsbereich | Hochladen und Verwalten von Dokumenten, Erstellen von Chats |
| **Betrachter** | Spezifischer Arbeitsbereich | Anzeigen von Dokumenten und Teilnahme an Chats |

### Benutzer verwalten

So verwalten Sie Benutzer in Ihrer Organisation:

1. Navigieren Sie zu „Einstellungen“ > „Benutzerverwaltung“  
2. Zeigen Sie alle Benutzer Ihrer Organisation an  
3. Filtern Sie Benutzer nach Status, Rolle oder Arbeitsbereich  
4. Führen Sie folgende Aktionen aus:  
   - Benutzerdetails bearbeiten  
   - Benutzerrollen ändern  
   - Benutzerkonten deaktivieren/aktivieren  
   - Benutzer löschen (mit Vorsicht verwenden)  

### Benutzer‑Onboarding

Optimieren Sie den Onboarding‑Prozess:

1. Erstellen Sie E‑Mail‑Vorlagen für neue Benutzer  
2. Legen Sie Standardrollen und ‑berechtigungen fest  
3. Konfigurieren Sie automatische Arbeitsbereichszuweisungen  
4. Aktivieren oder deaktivieren Sie E‑Mail‑Verifizierungsanforderungen  

## Abonnement und Abrechnung

### Abonnementverwaltung

Verwalten Sie das Abonnement Ihrer Organisation:

1. Gehen Sie zu „Einstellungen“ > „Abrechnung“  
2. Sehen Sie sich die aktuellen Abonnementdetails an  
3. Aktualisieren (Upgrade oder Downgrade) Sie Ihren Plan  
4. Aktualisieren Sie Zahlungsinformationen  
5. Sehen Sie sich den Abrechnungsverlauf an und laden Sie Rechnungen herunter  

### Benutzerzuweisung

Verwalten Sie Plätze für Ihr Abonnement:

1. Sehen Sie sich die Gesamtzahl verfügbarer Plätze an  
2. Sehen Sie sich die aktuell zugewiesenen Plätze an  
3. Fügen Sie bei Bedarf zusätzliche Plätze hinzu  
4. Entfernen Sie ungenutzte Plätze, um Kosten zu optimieren  

### Nutzungsüberwachung

Verfolgen Sie die Ressourcennutzung in Ihrer Organisation:

1. Dokumentenspeichernutzung  
2. Vektordatenbanknutzung  
3. API‑Aufrufvolumen  
4. Benutzeraktivitätsmetriken  

## Systemkonfiguration

### KI‑Anbietereinstellungen

Konfigurieren Sie KI‑Integrationseinstellungen:

1. Navigieren Sie zu „Einstellungen“ > „KI‑Konfiguration“  
2. Wählen Sie Ihren bevorzugten KI‑Anbieter:  
   - OpenAI  
   - Azure OpenAI  
3. Geben Sie API‑Schlüssel und Konfigurationsdetails ein  
4. Legen Sie Standardmodelle für verschiedene Funktionen fest  
5. Konfigurieren Sie Ratenlimits und Nutzungsalarme  

### Vektordatenbankkonfiguration

Richten Sie Vektordatenbankeinstellungen ein und verwalten Sie sie:

1. Gehen Sie zu „Einstellungen“ > „Vektordatenbank“  
2. Wählen Sie Ihren Vektorspeicheranbieter:  
   - ChromaDB  
   - LanceDB  
   - Pinecone  
3. Konfigurieren Sie Verbindungsdetails  
4. Legen Sie Indexierungsparameter fest  
5. Überwachen Sie Leistungsmetriken  

### Speicherkonfiguration

Verwalten Sie Dokumentenspeichereinstellungen:

1. Navigieren Sie zu „Einstellungen“ > „Speicher“  
2. Konfigurieren Sie die Blob‑Speicherverbindung  
3. Legen Sie Parameter für die Dokumentenverarbeitung fest  
4. Definieren Sie Aufbewahrungsrichtlinien  
5. Konfigurieren Sie Backup‑Einstellungen  

## Sicherheitseinstellungen

### Authentifizierungseinstellungen

Konfigurieren Sie Authentifizierungsmethoden:

1. Passwortrichtlinien (Komplexität, Ablauf)  
2. Anforderungen für Multi‑Faktor‑Authentifizierung  
3. Single‑Sign‑On‑(SSO)‑Integration  
4. Sitzungs‑Timeout‑Einstellungen  

### Zugriffskontrolle

Verwalten Sie den Systemzugriff:

1. IP‑Beschränkungen  
2. Einschränkungen für Anmeldeversuche  
3. Automatisierte Kontosperrung  
4. Sitzungsverwaltung  

### Audit‑Protokollierung

Überwachen Sie Systemaktivitäten:

1. Benutzeranmelde‑ und ‑abmeldeereignisse  
2. Dokumentenzugriff und ‑änderungen  
3. Einstellungsänderungen  
4. Administrative Aktionen  
5. Exportieren Sie Audit‑Protokolle für Compliance‑Zwecke  

## Systemwartung

### Leistungsüberwachung

Halten Sie Ihr System reibungslos am Laufen:

1. Überwachen Sie Systemgesundheitsmetriken  
2. Verfolgen Sie Antwortzeiten  
3. Identifizieren Sie Engpässe  
4. Richten Sie Alarme für Leistungsprobleme ein  

### Backup und Wiederherstellung

Schützen Sie Ihre Daten:

1. Konfigurieren Sie automatisierte Backups  
2. Legen Sie Backup‑Häufigkeit und ‑Aufbewahrung fest  
3. Testen Sie Wiederherstellungsverfahren  
4. Dokumentieren Sie Disaster‑Recovery‑Pläne  

## Best Practices für Administratoren

1. **Regelmäßige Audits**: Überprüfen Sie vierteljährlich Benutzerkonten und Berechtigungen  
2. **Nutzungsüberwachung**: Verfolgen Sie die Ressourcennutzung, um Kosten zu optimieren  
3. **Sicherheitsüberprüfungen**: Aktualisieren Sie regelmäßig Sicherheitseinstellungen und ‑richtlinien  
4. **Dokumentation**: Pflegen Sie interne Dokumentation zur Systemkonfiguration  
5. **Benutzerschulung**: Stellen Sie Schulungsressourcen für Arbeitsbereichsadministratoren bereit  
6. **Feedback‑Sammlung**: Sammeln Sie Benutzerfeedback, um die Systemkonfiguration zu verbessern  
