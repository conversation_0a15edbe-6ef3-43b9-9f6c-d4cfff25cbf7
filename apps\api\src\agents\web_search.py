import aiohttp
import json
import logging
import os
from typing import List, Dict, Any

logger = logging.getLogger(__name__)

class WebSearchClient:
    """Client for performing web searches through the Python API."""

    def __init__(self):
        # Use the internal API URL for server-to-server communication
        # This avoids making an HTTP request to the Next.js frontend
        self.base_url = os.getenv("INTERNAL_API_URL", "http://localhost:8000")
        self.search_endpoint = f"{self.base_url}/api/v1/web-search/search"

    async def search(self, query: str, tenant_id: str, user_id: str) -> List[Dict[str, Any]]:
        """
        Perform a web search using the Python API.

        Args:
            query: The search query
            tenant_id: The tenant ID
            user_id: The user ID

        Returns:
            List of search results
        """
        try:
            # For internal server-side calls, we can directly import and call the function
            # This is more efficient than making an HTTP request
            from src.api.api_v1.endpoints.web_search import perform_web_search

            # Call the function directly
            result = await perform_web_search(query, tenant_id, user_id)
            logger.info(f"Web search results: {result}")

            # Check if the limit is exceeded
            # if result.get("limitExceeded"):
            #     return {
            #         "error": result.get("error", "Web search limit exceeded"),
            #         "limitExceeded": True,
            #         "limitInfo": result
            #     }

            return result.get("results", [])

        except ImportError:
            # Fall back to HTTP request if direct import fails
            logger.warning("Direct import of perform_web_search failed, falling back to HTTP request")
            try:
                url = f"{self.search_endpoint}?query={query}&tenant_id={tenant_id}&user_id={user_id}"

                async with aiohttp.ClientSession() as session:
                    async with session.get(url) as response:
                        if response.status != 200:
                            error_text = await response.text()
                            logger.error(f"Web search failed with status {response.status}: {error_text}")

                            # Check if the error is due to limit exceeded
                            try:
                                error_data = json.loads(error_text)
                                if error_data.get("limitExceeded"):
                                    return {
                                        "error": error_data.get("error", "Web search limit exceeded"),
                                        "limitExceeded": True,
                                        "limitInfo": error_data.get("limitInfo", {})
                                    }
                            except:
                                pass

                            return []

                        data = await response.json()
                        return data.get("results", [])
            except Exception as e:
                logger.error(f"Error performing web search via HTTP: {e}")
                return []
        except Exception as e:
            logger.error(f"Error performing web search: {e}")
            return []

def format_web_sources(web_results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    Format web search results as sources for the RAG agent.

    Args:
        web_results: List of web search results

    Returns:
        List of formatted sources
    """
    sources = []

    for result in web_results:
        sources.append({
            "content": result.get("snippet", ""),
            "metadata": {
                "title": result.get("title", ""),
                "link": result.get("link", ""),
                "displayLink": result.get("displayLink", ""),
                "source": "web",
                "type": "web_search"
            }
        })

    return sources

def enhance_prompt_with_web_results(prompt: str, web_results: List[Dict[str, Any]]) -> str:
    """
    Enhance the prompt with web search results.

    Args:
        prompt: The original prompt
        web_results: List of web search results

    Returns:
        Enhanced prompt with web search results
    """
    if not web_results:
        return prompt

    web_context = "\n\nWeb Search Results:\n"

    for i, result in enumerate(web_results):
        title = result.get("title", "Untitled")
        snippet = result.get("snippet", "No content available")
        link = result.get("link", "")

        web_context += f"[{i+1}] {title}\n"
        web_context += f"Content: {snippet}\n"
        web_context += f"URL: {link}\n\n"

    # Add instructions for using web results
    web_context += "\nIMPORTANT: For questions about recent events, news, or general knowledge, prioritize information from these web search results. "
    web_context += "When using information from web results, cite your sources using [W1], [W2], etc. corresponding to the numbering above. "
    web_context += "If the user is asking about current events or recent information, you MUST use these web search results to provide an accurate answer.\n"

    # Insert web context before the final instruction
    if "Answer:" in prompt:
        parts = prompt.split("Answer:", 1)
        return parts[0] + web_context + "\nAnswer:" + parts[1]
    else:
        return prompt + "\n" + web_context
