import { deleteIntegration } from "@/services/src/integration";
import { getCookie } from "@/utils/cookies";
import { useMutation } from "@tanstack/react-query";
import { toast } from "react-hot-toast";

const useDisconnectSocialConnect = () => {
  const tenantId = getCookie("currentOrganizationId") ?? "";
  const userId = getCookie("userId") ?? "";
  return useMutation(
    ["disconnect-social-connect"],
    async (data: any) => {
      toast.loading("Disconnecting your account");
      const response = await deleteIntegration(data?.id, tenantId, userId);
      return response;
    },
    {
      onSuccess: async (data) => {
        toast.remove();
        toast.success("Disconnected successfully");
        window.location.reload();
      },
      onError: (data: any) => {
        toast.remove();
        toast.error("Error Disconnecting Account!! Kindly try reconnecting");
      },
    }
  );
};

export { useDisconnectSocialConnect };
