import { apiUrl } from "..";

export interface ChatbotConfig {
  id?: string;
  name: string;
  description: string;
  type: "web-snippet" | "inline-embedding" | "dedicated-page";
  access: "public" | "private";
  allowedUsers: string[];
  isActive?: boolean;
  userId?: string;
  tenantId?: string;
  companyIds?: string[];
  createdAt?: string;
  updatedAt?: string;

  // SDK-specific fields
  apiKey?: string;
  allowedDomains?: string[];
  customization?: {
    theme?: {
      primaryColor?: string;
      secondaryColor?: string;
      fontFamily?: string;
      borderRadius?: string;
    };
    position?: "bottom-right" | "bottom-left" | "top-right" | "top-left";
    size?: "small" | "medium" | "large";
    greeting?: string;
    placeholder?: string;
    showBranding?: boolean;
  };
  usageCount?: number;
  monthlyUsage?: number;
  lastUsedAt?: string;

  // LLM Configuration
  llmScope?: string[];
  searchModes?: string[];
  maxTokens?: number;
  temperature?: number;

  // Rate limiting
  rateLimitPerMinute?: number;
  rateLimitPerHour?: number;
  rateLimitPerDay?: number;
}

export interface ChatbotResponse {
  chatbot: ChatbotConfig;
}

export interface ChatbotsResponse {
  chatbots: ChatbotConfig[];
}

export const chatbotService = {
  /**
   * Get all chatbots for a tenant
   */
  async getChatbots(
    tenantId: string
  ): Promise<ChatbotsResponse | { error: string }> {
    try {
      const response = await fetch(`${apiUrl}/chatbots?tenantId=${tenantId}`, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
        credentials: "include",
      });

      if (!response.ok) {
        const errorData = await response.json();
        return { error: errorData.error || "Failed to fetch chatbots" };
      }

      return await response.json();
    } catch (error) {
      console.error("Error fetching chatbots:", error);
      return { error: "Failed to fetch chatbots" };
    }
  },

  /**
   * Get a single chatbot by ID
   */
  async getChatbot(
    chatbotId: string,
    tenantId: string
  ): Promise<ChatbotResponse | { error: string }> {
    try {
      const response = await fetch(
        `${apiUrl}/chatbots/${chatbotId}?tenantId=${tenantId}`,
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
          },
          credentials: "include",
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        return { error: errorData.error || "Failed to fetch chatbot" };
      }

      return await response.json();
    } catch (error) {
      console.error("Error fetching chatbot:", error);
      return { error: "Failed to fetch chatbot" };
    }
  },

  /**
   * Create a new chatbot
   */
  async createChatbot(
    config: Omit<ChatbotConfig, "id" | "userId" | "createdAt" | "updatedAt"> & {
      tenantId: string;
    }
  ): Promise<ChatbotResponse | { error: string }> {
    try {
      const response = await fetch(`${apiUrl}/chatbots`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        credentials: "include",
        body: JSON.stringify(config),
      });

      if (!response.ok) {
        const errorData = await response.json();
        return { error: errorData.error || "Failed to create chatbot" };
      }

      return await response.json();
    } catch (error) {
      console.error("Error creating chatbot:", error);
      return { error: "Failed to create chatbot" };
    }
  },

  /**
   * Update an existing chatbot
   */
  async updateChatbot(
    chatbotId: string,
    updates: Partial<ChatbotConfig> & { tenantId: string }
  ): Promise<ChatbotResponse | { error: string }> {
    try {
      const response = await fetch(`${apiUrl}/chatbots/${chatbotId}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        credentials: "include",
        body: JSON.stringify(updates),
      });

      if (!response.ok) {
        const errorData = await response.json();
        return { error: errorData.error || "Failed to update chatbot" };
      }

      return await response.json();
    } catch (error) {
      console.error("Error updating chatbot:", error);
      return { error: "Failed to update chatbot" };
    }
  },

  /**
   * Delete a chatbot
   */
  async deleteChatbot(
    chatbotId: string,
    tenantId: string
  ): Promise<{ message: string } | { error: string }> {
    try {
      const response = await fetch(
        `${apiUrl}/chatbots/${chatbotId}?tenantId=${tenantId}`,
        {
          method: "DELETE",
          headers: {
            "Content-Type": "application/json",
          },
          credentials: "include",
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        return { error: errorData.error || "Failed to delete chatbot" };
      }

      return await response.json();
    } catch (error) {
      console.error("Error deleting chatbot:", error);
      return { error: "Failed to delete chatbot" };
    }
  },

  /**
   * Generate a new API key for a chatbot
   */
  async generateApiKey(
    chatbotId: string,
    tenantId: string
  ): Promise<{ apiKey: string } | { error: string }> {
    try {
      const response = await fetch(`${apiUrl}/chatbots/${chatbotId}/api-key`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        credentials: "include",
        body: JSON.stringify({ tenantId }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        return { error: errorData.error || "Failed to generate API key" };
      }

      return await response.json();
    } catch (error) {
      console.error("Error generating API key:", error);
      return { error: "Failed to generate API key" };
    }
  },

  /**
   * Regenerate API key for a chatbot
   */
  async regenerateApiKey(
    chatbotId: string,
    tenantId: string
  ): Promise<{ apiKey: string } | { error: string }> {
    try {
      const response = await fetch(`${apiUrl}/chatbots/${chatbotId}/api-key`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        credentials: "include",
        body: JSON.stringify({ tenantId }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        return { error: errorData.error || "Failed to regenerate API key" };
      }

      return await response.json();
    } catch (error) {
      console.error("Error regenerating API key:", error);
      return { error: "Failed to regenerate API key" };
    }
  },

  /**
   * Get embed code for a chatbot
   */
  async getEmbedCode(
    chatbotId: string,
    tenantId: string
  ): Promise<{ embedCode: string; scriptUrl: string } | { error: string }> {
    try {
      const response = await fetch(
        `${apiUrl}/chatbots/${chatbotId}/embed-code?tenantId=${tenantId}`,
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
          },
          credentials: "include",
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        return { error: errorData.error || "Failed to get embed code" };
      }

      return await response.json();
    } catch (error) {
      console.error("Error getting embed code:", error);
      return { error: "Failed to get embed code" };
    }
  },

  /**
   * Get chatbot analytics
   */
  async getAnalytics(
    chatbotId: string,
    tenantId: string,
    timeRange: "day" | "week" | "month" = "week"
  ): Promise<{ analytics: any } | { error: string }> {
    try {
      const response = await fetch(
        `${apiUrl}/chatbots/${chatbotId}/analytics?tenantId=${tenantId}&timeRange=${timeRange}`,
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
          },
          credentials: "include",
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        return { error: errorData.error || "Failed to get analytics" };
      }

      return await response.json();
    } catch (error) {
      console.error("Error getting analytics:", error);
      return { error: "Failed to get analytics" };
    }
  },
};
