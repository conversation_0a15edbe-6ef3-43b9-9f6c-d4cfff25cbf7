"""
CopilotKit RAG Service for Swiss Knowledge Hub

This service integrates the CopilotKit agent with the existing workspace
management system, providing high-performance RAG capabilities with
intelligent tool selection and parallel execution.
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional, AsyncGenerator, Union
from bson import ObjectId
from src.agents.minimal_copilotkit_agent import MinimalCopilotKitRAGAgent
from src.services.usage_tracker import UsageTracker
import datetime
import json

logger = logging.getLogger(__name__)

class CopilotKitRAGService:
    """
    Service layer for the CopilotKit RAG agent that handles workspace access control,
    user permissions, and integration with the existing Swiss Knowledge Hub infrastructure.
    """

    def __init__(self, db_client=None):
        self.db_client = db_client
        self.agents = {}  # Cache of agents by tenant
        self.usage_tracker = UsageTracker(db_client) if db_client else None

    def clear_agent_cache(self, tenant_id: str = None) -> Dict[str, Any]:
        """
        Clear the agent cache for a specific tenant or all tenants.

        Args:
            tenant_id: The ID of the tenant to clear cache for. If None, clears all caches.

        Returns:
            A dictionary containing the result of the cache clearing operation.
        """
        try:
            if tenant_id:
                # Clear cache for specific tenant
                if tenant_id in self.agents:
                    del self.agents[tenant_id]
                    logger.info(f"Cleared CopilotKit agent cache for tenant {tenant_id}")
                    return {
                        "status": 200,
                        "message": f"Successfully cleared CopilotKit agent cache for tenant {tenant_id}"
                    }
                else:
                    return {
                        "status": 200,
                        "message": f"No cached CopilotKit agent found for tenant {tenant_id}"
                    }
            else:
                # Clear all caches
                agent_count = len(self.agents)
                self.agents.clear()

                logger.info(f"Cleared all CopilotKit agent caches: {agent_count} agents")
                return {
                    "status": 200,
                    "message": f"Successfully cleared all CopilotKit agent caches ({agent_count} agents)"
                }
        except Exception as e:
            logger.error(f"Error clearing CopilotKit agent cache: {e}")
            return {
                "error": f"Error clearing CopilotKit agent cache: {str(e)}",
                "status": 500
            }

    async def get_user_workspaces(self, user_id: str, tenant_id: str) -> List[str]:
        """Get all workspaces that a user has access to."""
        try:
            tenantMemberships = await self.db_client.Membership.find({
                "userId": ObjectId(user_id),
                "tenantId": ObjectId(tenant_id)
            }).to_list(length=None)

            if not tenantMemberships:
                return []
            if tenantMemberships[0].get("role") in ["ADMIN", "OWNER"]:
                # Admin and Owner can access all workspaces in the tenant
                workspaces = await self.db_client.Workspace.find({
                    "tenantId": ObjectId(tenant_id)
                }).to_list(length=None)
                return [str(workspace["_id"]) for workspace in workspaces]

            # First, get all workspace memberships for the user
            memberships = await self.db_client.WorkspaceMember.find({
                "userId": ObjectId(user_id),
                "membershipId":ObjectId(tenantMemberships[0].get("_id"))
            }).to_list(length=None)

            workspace_ids = []

            # For each membership, check if the workspace belongs to the tenant
            for membership in memberships:
                workspace_id = membership.get("workspaceId")
                if workspace_id:
                    workspace_ids.append(str(workspace_id))
            logger.info(f"User {user_id} has access to {len(workspace_ids)} workspaces in tenant {tenant_id}")
            return workspace_ids

        except Exception as e:
            logger.error(f"Error getting user workspaces: {e}")
            return []

    async def get_or_create_agent(self, tenant_id: str, language: str = "en") -> MinimalCopilotKitRAGAgent:
        """Get or create a minimal CopilotKit agent for a specific tenant and language."""
        # Create cache key that includes both tenant_id and language
        cache_key = f"{tenant_id}_{language}"

        # TEMPORARY: Clear cache to force new agent creation with Swiss Knowledge Hub fixes
        if cache_key in self.agents:
            logger.info(f"CACHE: Clearing cached agent for {cache_key} to apply Swiss Knowledge Hub fixes")
            del self.agents[cache_key]

        # if cache_key in self.agents:
        #     return self.agents[cache_key]

        try:
            # Create new agent with language support
            agent = MinimalCopilotKitRAGAgent(db_client=self.db_client, language=language)

            # Cache the agent with the combined key
            self.agents[cache_key] = agent
            logger.info(f"Created new CopilotKit agent for tenant {tenant_id} with language {language}")

            return agent

        except Exception as e:
            logger.error(f"Failed to create CopilotKit agent for tenant {tenant_id}: {e}")
            raise

    async def check_llm_scope_permissions(self, tenant_id: str, search_mode: str) -> tuple[bool, Optional[str]]:
        """Check if the tenant has permission for the requested search mode."""
        try:
            # Get tenant's LLM scope settings
            llm_scope = await self.db_client.Tenant.find_one({
                "_id": ObjectId(tenant_id)
            })

            if not llm_scope:
                # Default to internal only if no scope is set
                if search_mode in ["web", "hybrid"]:
                    return False, "External search not permitted. Please contact your administrator."
                return True, None

            # Get the actual scope array from the tenant document
            scope_array = llm_scope.get("llmScope", ["INTERNAL_ONLY"])

            # Handle both old single string format and new array format
            if isinstance(scope_array, str):
                scope_array = [scope_array]

            # Check permissions based on scope array
            if search_mode == "web" or search_mode == "hybrid":
                # Check if web search is allowed
                web_allowed = any(scope in ["EXTERNAL_ONLY", "HYBRID", "FULL_ACCESS", "web", "hybrid"] for scope in scope_array)
                if not web_allowed:
                    return False, "External search not permitted for this organization."

            if search_mode == "internal":
                # Check if internal search is allowed (blocked only for EXTERNAL_ONLY)
                if scope_array == ["EXTERNAL_ONLY"]:
                    return False, "Internal document search not permitted for this organization."

            # Check if MCP_ONLY scope is being used correctly
            if scope_array == ["MCP_ONLY"] or "MCP_ONLY" in scope_array:
                if search_mode in ["internal", "web", "hybrid"]:
                    return False, "Only MCP tools are permitted for this organization. Please use MCP servers instead."

            return True, None

        except Exception as e:
            logger.error(f"Error checking LLM scope permissions: {e}")
            return False, "Error checking permissions"

    async def query_workspace(
        self,
        user_id: str,
        question: str,
        tenant_id: str,
        user_name: str,
        search_mode: str = "hybrid",
        include_web_results: bool = False,
        stream: bool = False,
        previous_message: Optional[str] = None,
        chat_id: Optional[str] = None,  # NEW: For persistent memory and image context
        deep_answer: bool = False,  # NEW: Use DeepSeek R1 for expanded answers
        language: str = "en",  # NEW: Language code for localized responses (en, de)
        **kwargs
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """
        Query the CopilotKit agent with workspace access control.

        Args:
            user_id: User ID
            question: User's question
            tenant_id: Tenant ID
            user_name: User name
            search_mode: Search mode ("internal", "web", "hybrid")
            include_web_results: Whether to include web search results
            stream: Whether to stream the response
            previous_message: Previous message for context
            deep_answer: Whether to use DeepSeek R1 for expanded research-grade answers
            **kwargs: Additional parameters

        Returns:
            Response dictionary or async generator if streaming
        """
        if stream:
            # For streaming, delegate to the streaming method
            # Use streaming for stream requests
            async for chunk in self._query_workspace_streaming(
                user_id, question, tenant_id, user_name, search_mode,
                include_web_results, previous_message, chat_id, deep_answer, language, **kwargs
            ):
                yield chunk
        else:
            # For non-streaming, yield the single result
            # Use non-streaming for non-stream requests
            result = await self._query_workspace_non_streaming(
                user_id, question, tenant_id, user_name, search_mode,
                include_web_results, previous_message, chat_id, deep_answer, language, **kwargs
            )
            yield result

    async def _query_workspace_non_streaming(
        self,
        user_id: str,
        question: str,
        tenant_id: str,
        user_name: str,
        search_mode: str = "hybrid",
        include_web_results: bool = False,
        previous_message: Optional[str] = None,
        chat_id: Optional[str] = None,
        deep_answer: bool = False,
        language: str = "en",
        **kwargs
    ) -> Dict[str, Any]:
        """Handle non-streaming workspace query."""
        start_time = datetime.datetime.now(datetime.timezone.utc)

        try:
            # Check LLM scope permissions
            has_permission, error_message = await self.check_llm_scope_permissions(tenant_id, search_mode)
            if not has_permission:
                return {
                    "error": error_message,
                    "answer": "",
                    "title": question[:50] + "..." if len(question) > 50 else question,
                    "sources": [],
                    "status": 403
                }

            # Get user's accessible workspaces
            workspace_ids = await self.get_user_workspaces(user_id, tenant_id)
            if not workspace_ids and search_mode in ["internal", "hybrid"]:
                return {
                    "error": "No accessible workspaces found",
                    "answer": "",
                    "title": question[:50] + "..." if len(question) > 50 else question,
                    "sources": [],
                    "status": 403
                }

            # Get or create agent for this tenant
            agent = await self.get_or_create_agent(tenant_id, language)

            # Execute query
            return await self._handle_non_streaming_query(
                agent, question, user_id, tenant_id, user_name,
                workspace_ids, search_mode, include_web_results,
                start_time, previous_message, chat_id, deep_answer, **kwargs
            )

        except Exception as e:
            logger.error(f"Error in CopilotKit workspace query: {e}")
            return {
                "error": f"Query failed: {str(e)}",
                "answer": "",
                "title": question[:50] + "..." if len(question) > 50 else question,
                "sources": [],
                "status": 500
            }

    async def _query_workspace_streaming(
        self,
        user_id: str,
        question: str,
        tenant_id: str,
        user_name: str,
        search_mode: str = "hybrid",
        include_web_results: bool = False,
        previous_message: Optional[str] = None,
        chat_id: Optional[str] = None,
        deep_answer: bool = False,
        language: str = "en",
        **kwargs
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """Handle streaming workspace query."""
        start_time = datetime.datetime.now(datetime.timezone.utc)

        try:
            # Check LLM scope permissions
            has_permission, error_message = await self.check_llm_scope_permissions(tenant_id, search_mode)
            if not has_permission:
                yield {
                    "error": error_message,
                    "answer": "",
                    "title": question[:50] + "..." if len(question) > 50 else question,
                    "sources": [],
                    "status": 403,
                    "done": True
                }
                return

            # Get user's accessible workspaces
            workspace_ids = await self.get_user_workspaces(user_id, tenant_id)
            if not workspace_ids and search_mode in ["internal", "hybrid"]:
                yield {
                    "error": "No accessible workspaces found",
                    "answer": "",
                    "title": question[:50] + "..." if len(question) > 50 else question,
                    "sources": [],
                    "status": 403,
                    "done": True
                }
                return

            # Get or create agent for this tenant
            agent = await self.get_or_create_agent(tenant_id, language)

            # Execute streaming query
            async for chunk in self._handle_streaming_query(
                agent, question, user_id, tenant_id, user_name,
                workspace_ids, search_mode, include_web_results,
                start_time, previous_message, chat_id, deep_answer, **kwargs
            ):
                yield chunk

        except Exception as e:
            logger.error(f"Error in CopilotKit workspace query: {e}")
            yield {
                "error": f"Query failed: {str(e)}",
                "answer": "",
                "title": question[:50] + "..." if len(question) > 50 else question,
                "sources": [],
                "status": 500,
                "done": True
            }

    async def _handle_non_streaming_query(
        self,
        agent: MinimalCopilotKitRAGAgent,
        question: str,
        user_id: str,
        tenant_id: str,
        user_name: str,
        workspace_ids: List[str],
        search_mode: str,
        include_web_results: bool,
        start_time: datetime.datetime,
        previous_message: Optional[str] = None,
        chat_id: Optional[str] = None,
        deep_answer: bool = False,

        **kwargs
    ) -> Dict[str, Any]:
        """Handle non-streaming query execution."""
        try:
            # Execute agent query
            result = await agent.query(
                question=question,
                user_id=user_id,
                tenant_id=tenant_id,
                user_name=user_name,
                workspace_ids=workspace_ids,
                search_mode=search_mode,
                include_web_results=include_web_results,
                stream=False,
                previous_message=previous_message,
                chat_id=chat_id,  # Pass chat_id for persistent memory
                deep_answer=deep_answer,  # Pass deep_answer flag
                **kwargs
            )

            # Track usage if tracker is available
            if self.usage_tracker:
                try:
                    await self.usage_tracker.track_usage(
                        user_id=user_id,
                        tenant_id=tenant_id,
                        operation_type="copilotkit_query",
                        tokens_used=len(question.split()) + len(result.get("answer", "").split()),
                        model_name="copilotkit-agent",
                        start_time=start_time,
                        end_time=datetime.datetime.now(datetime.timezone.utc),
                        metadata={
                            "search_mode": search_mode,
                            "tools_used": result.get("tools_used", []),
                            "workspace_count": len(workspace_ids)
                        }
                    )
                except Exception as e:
                    logger.warning(f"Failed to track usage: {e}")

            return result

        except Exception as e:
            logger.error(f"Error in non-streaming query: {e}")
            return {
                "error": f"Query execution failed: {str(e)}",
                "answer": "",
                "title": question[:50] + "..." if len(question) > 50 else question,
                "sources": [],
                "status": 500
            }

    async def _handle_streaming_query(
        self,
        agent: MinimalCopilotKitRAGAgent,
        question: str,
        user_id: str,
        tenant_id: str,
        user_name: str,
        workspace_ids: List[str],
        search_mode: str,
        include_web_results: bool,
        start_time: datetime.datetime,
        previous_message: Optional[str] = None,
        chat_id: Optional[str] = None,
        deep_answer: bool = False,

        **kwargs
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """Handle streaming query execution."""
        try:
            # Execute agent query with streaming
            async for chunk in agent.query(
                question=question,
                user_id=user_id,
                tenant_id=tenant_id,
                user_name=user_name,
                workspace_ids=workspace_ids,
                search_mode=search_mode,
                include_web_results=include_web_results,
                stream=True,
                previous_message=previous_message,
                chat_id=chat_id,  # Pass chat_id for persistent memory
                deep_answer=deep_answer,  # Pass deep_answer flag
                **kwargs
            ):
                # Track usage on final chunk asynchronously (non-blocking)
                if chunk.get("done") and self.usage_tracker:
                    # Fire and forget usage tracking to avoid blocking the response
                    asyncio.create_task(self._track_usage_async(
                        user_id=user_id,
                        tenant_id=tenant_id,
                        question=question,
                        search_mode=search_mode,
                        workspace_ids=workspace_ids,
                        chunk=chunk,
                        start_time=start_time
                    ))

                yield chunk

        except Exception as e:
            logger.error(f"Error in streaming query: {e}")
            yield {
                "error": f"Streaming query failed: {str(e)}",
                "answer": "",
                "title": question[:50] + "..." if len(question) > 50 else question,
                "sources": [],
                "done": True
            }

    async def _track_usage_async(self, user_id: str, tenant_id: str, question: str,
                               search_mode: str, workspace_ids: List[str], chunk: Dict[str, Any],
                               start_time: datetime.datetime):
        """Asynchronously track usage without blocking the response stream."""
        try:
            await self.usage_tracker.track_usage(
                user_id=user_id,
                tenant_id=tenant_id,
                operation_type="copilotkit_query_stream",
                tokens_used=len(question.split()) + len(chunk.get("answer", "").split()),
                model_name="copilotkit-agent",
                start_time=start_time,
                end_time=datetime.datetime.now(datetime.timezone.utc),
                metadata={
                    "search_mode": search_mode,
                    "tools_used": chunk.get("tools_used", []),
                    "workspace_count": len(workspace_ids),
                    "elapsed_time": chunk.get("elapsed_time", 0),
                    "sources_count": len(chunk.get("sources", []))
                }
            )
        except Exception as e:
            logger.warning(f"Failed to track streaming usage asynchronously: {e}")
            # Don't propagate errors from usage tracking

# Global instance
copilotkit_rag_service = CopilotKitRAGService()
