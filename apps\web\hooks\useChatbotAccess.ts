import { useState, useEffect } from 'react';

interface AccessResult {
  hasAccess: boolean;
  reason: string;
  companies?: Array<{
    id: string;
    name: string;
  }>;
}

interface ChatbotAccessDetails {
  chatbot: {
    id: string;
    name: string;
    access: string;
    isActive: boolean;
  };
  companies: Array<{
    id: string;
    name: string;
    slug: string;
    members: Array<{
      userId: string;
      status: string;
      user: {
        id: string;
        name: string;
        email: string;
      };
    }>;
  }>;
  userAccess: AccessResult;
  totalAuthorizedUsers: number;
}

export const useChatbotAccess = (chatbotId: string | null) => {
  const [accessResult, setAccessResult] = useState<AccessResult | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!chatbotId) {
      setAccessResult(null);
      return;
    }

    const checkAccess = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await fetch(`/api/chatbots/${chatbotId}/access`);
        
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const result = await response.json();
        setAccessResult(result);
      } catch (err) {
        console.error('Error checking chatbot access:', err);
        setError(err instanceof Error ? err.message : 'Failed to check access');
        setAccessResult({ hasAccess: false, reason: 'error' });
      } finally {
        setLoading(false);
      }
    };

    checkAccess();
  }, [chatbotId]);

  return { accessResult, loading, error };
};

export const useChatbotAccessDetails = (chatbotId: string | null) => {
  const [details, setDetails] = useState<ChatbotAccessDetails | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!chatbotId) {
      setDetails(null);
      return;
    }

    const getDetails = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await fetch(`/api/chatbots/${chatbotId}/access`, {
          method: 'POST',
        });
        
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const result = await response.json();
        setDetails(result);
      } catch (err) {
        console.error('Error getting chatbot access details:', err);
        setError(err instanceof Error ? err.message : 'Failed to get access details');
      } finally {
        setLoading(false);
      }
    };

    getDetails();
  }, [chatbotId]);

  return { details, loading, error };
};

// Helper function to get user-friendly access messages
export const getAccessMessage = (accessResult: AccessResult | null): string => {
  if (!accessResult) return 'Checking access...';

  switch (accessResult.reason) {
    case 'public_chatbot':
      return 'This chatbot is publicly accessible.';
    case 'company_member':
      const companyNames = accessResult.companies?.map(c => c.name).join(', ') || '';
      return `You have access through your membership in: ${companyNames}`;
    case 'not_company_member':
      return 'You do not have access to this private chatbot. Contact your administrator to be added to an authorized company.';
    case 'no_companies_configured':
      return 'This private chatbot has no companies configured for access.';
    case 'error':
      return 'Unable to verify access. Please try again.';
    default:
      return 'Access denied for unknown reason.';
  }
};

// Helper function to check if user can access chatbot (for conditional rendering)
export const canAccessChatbot = (accessResult: AccessResult | null): boolean => {
  return accessResult?.hasAccess === true;
};
