import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/next-auth";
import db from "@/lib/shared-db";

export async function GET(
  request: NextRequest,
  { params }: { params: { chatbotId: string } }
) {
  try {
    const session: any = await getServerSession(authOptions);
    if (!session?.userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const tenantId = searchParams.get("tenantId");
    const timeRange = searchParams.get("timeRange") || "7d";
    const { chatbotId } = params;

    if (!tenantId) {
      return NextResponse.json(
        { error: "Tenant ID is required" },
        { status: 400 }
      );
    }

    // Verify chatbot belongs to user's tenant
    const chatbot = await db.chatbot.findFirst({
      where: {
        id: chatbotId,
        tenantId: tenantId,
        userId: session.userId,
      },
    });

    if (!chatbot) {
      return NextResponse.json(
        { error: "Chatbot not found" },
        { status: 404 }
      );
    }

    // Calculate date range
    const now = new Date();
    let startDate: Date;
    
    switch (timeRange) {
      case "1d":
        startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
        break;
      case "7d":
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case "30d":
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      case "90d":
        startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
        break;
      default:
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    }

    // Get sessions for this chatbot
    const sessions = await db.chatbotSession.findMany({
      where: {
        chatbotId: chatbotId,
        startedAt: {
          gte: startDate,
        },
      },
      include: {
        messages: {
          select: {
            id: true,
            createdAt: true,
          },
        },
      },
    });

    // Calculate analytics
    const totalSessions = sessions.length;

    // Count messages directly from ChatbotMessage table for accuracy
    const totalMessages = await db.chatbotMessage.count({
      where: {
        chatbotId: chatbotId,
        createdAt: {
          gte: startDate,
        },
      },
    });
    const totalDuration = sessions.reduce((sum: number, session: any) => sum + (session.duration || 0), 0);
    const totalErrors = sessions.reduce((sum: number, session: any) => sum + session.errorCount, 0);

    // Calculate average response time
    const responseTimes = sessions
      .map((session: any) => session.averageResponseTime)
      .filter((time: any) => time !== null && time !== undefined);
    const averageResponseTime = responseTimes.length > 0
      ? responseTimes.reduce((sum: number, time: number) => sum + time, 0) / responseTimes.length
      : 0;

    // Calculate error rate
    const errorRate = totalSessions > 0 ? (totalErrors / totalSessions) * 100 : 0;

    // Calculate active users (unique users in the time period)
    const uniqueUserIds = new Set();
    const uniqueIPs = new Set();
    
    sessions.forEach((session: any) => {
      if (session.userId) {
        uniqueUserIds.add(session.userId);
      } else if (session.ipAddress) {
        uniqueIPs.add(session.ipAddress);
      }
    });
    
    const activeUsers = uniqueUserIds.size + uniqueIPs.size;

    // Get last activity
    const lastActivity = sessions.length > 0
      ? sessions.reduce((latest: any, session: any) =>
          session.lastActivityAt > latest ? session.lastActivityAt : latest,
          sessions[0].lastActivityAt
        )
      : null;

    // Calculate satisfaction metrics
    const satisfactionRatings = sessions
      .map((session: any) => session.satisfactionRating)
      .filter((rating: any) => rating !== null && rating !== undefined);

    const averageSatisfaction = satisfactionRatings.length > 0
      ? satisfactionRatings.reduce((sum: number, rating: number) => sum + rating, 0) / satisfactionRatings.length
      : null;

    // Daily breakdown for charts
    const dailyStats: Array<{
      date: string;
      sessions: number;
      messages: number;
      errors: number;
    }> = [];
    for (let i = 6; i >= 0; i--) {
      const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000);
      const dayStart = new Date(date.getFullYear(), date.getMonth(), date.getDate());
      const dayEnd = new Date(dayStart.getTime() + 24 * 60 * 60 * 1000);
      
      const daySessions = sessions.filter((session: any) =>
        session.startedAt >= dayStart && session.startedAt < dayEnd
      );

      dailyStats.push({
        date: dayStart.toISOString().split('T')[0],
        sessions: daySessions.length,
        messages: daySessions.reduce((sum: number, session: any) => sum + session.messagesCount, 0),
        errors: daySessions.reduce((sum: number, session: any) => sum + session.errorCount, 0),
      });
    }

    // Geographic distribution
    const countries: Record<string, number> = {};
    sessions.forEach((session: any) => {
      if (session.country) {
        countries[session.country] = (countries[session.country] || 0) + 1;
      }
    });

    // Device distribution
    const devices: Record<string, number> = {};
    sessions.forEach((session: any) => {
      if (session.deviceType) {
        devices[session.deviceType] = (devices[session.deviceType] || 0) + 1;
      }
    });

    const analytics = {
      totalSessions,
      totalMessages,
      averageResponseTime,
      errorRate,
      lastActivity: lastActivity ? lastActivity.toISOString() : 'Never',
      activeUsers,
      averageSatisfaction,
      totalDuration,
      averageSessionDuration: totalSessions > 0 ? totalDuration / totalSessions : 0,
      
      // Breakdown data
      dailyStats,
      countries: Object.entries(countries)
        .map(([country, count]) => ({ country, count: count as number }))
        .sort((a, b) => b.count - a.count)
        .slice(0, 10),
      devices: Object.entries(devices)
        .map(([device, count]) => ({ device, count: count as number }))
        .sort((a, b) => b.count - a.count),
      
      // Recent activity
      recentSessions: sessions
        .sort((a: any, b: any) => b.startedAt.getTime() - a.startedAt.getTime())
        .slice(0, 10)
        .map((session: any) => ({
          id: session.id,
          startedAt: session.startedAt,
          messagesCount: session.messagesCount,
          duration: session.duration,
          country: session.country,
          deviceType: session.deviceType,
          satisfactionRating: session.satisfactionRating,
          errorCount: session.errorCount,
        })),
    };

    return NextResponse.json({ analytics });

  } catch (error) {
    console.error("Error fetching chatbot analytics:", error);
    return NextResponse.json(
      { error: "Failed to fetch analytics" },
      { status: 500 }
    );
  }
}
