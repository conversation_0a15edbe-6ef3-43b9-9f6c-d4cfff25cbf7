"use client";

import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  CheckCircle, 
  XCircle, 
  Loader2, 
  Users, 
  AlertTriangle,
  Clock
} from "lucide-react";
import { cn } from "@/lib/utils";

export interface ImportProgress {
  total: number;
  processed: number;
  successful: number;
  failed: number;
  isComplete: boolean;
  currentItem?: string;
  errors: Array<{
    email: string;
    error: string;
  }>;
}

interface ImportProgressProps {
  progress: ImportProgress;
  className?: string;
}

export function ImportProgressComponent({ 
  progress, 
  className 
}: ImportProgressProps) {
  const { 
    total, 
    processed, 
    successful, 
    failed, 
    isComplete, 
    currentItem, 
    errors 
  } = progress;

  const progressPercentage = total > 0 ? (processed / total) * 100 : 0;
  const hasErrors = failed > 0;

  return (
    <div className={cn("space-y-6", className)}>
      {/* Progress Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            {isComplete ? (
              <CheckCircle className="h-5 w-5 text-green-600" />
            ) : (
              <Loader2 className="h-5 w-5 animate-spin text-primary" />
            )}
            Import Progress
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Progress Bar */}
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>
                {processed} of {total} members processed
              </span>
              <span>{Math.round(progressPercentage)}%</span>
            </div>
            <Progress value={progressPercentage} className="h-2" />
          </div>

          {/* Current Item */}
          {!isComplete && currentItem && (
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <Clock className="h-4 w-4" />
              <span>Processing: {currentItem}</span>
            </div>
          )}

          {/* Stats Grid */}
          <div className="grid grid-cols-3 gap-4 pt-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{successful}</div>
              <div className="text-xs text-muted-foreground">Successful</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">{failed}</div>
              <div className="text-xs text-muted-foreground">Failed</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-muted-foreground">
                {total - processed}
              </div>
              <div className="text-xs text-muted-foreground">Remaining</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Completion Status */}
      {isComplete && (
        <Alert variant={hasErrors ? "destructive" : "default"}>
          {hasErrors ? (
            <AlertTriangle className="h-4 w-4" />
          ) : (
            <CheckCircle className="h-4 w-4" />
          )}
          <AlertDescription>
            {hasErrors ? (
              <div>
                <p className="font-medium">
                  Import completed with {failed} error{failed !== 1 ? 's' : ''}
                </p>
                <p className="text-sm mt-1">
                  {successful} member{successful !== 1 ? 's' : ''} were successfully imported.
                </p>
              </div>
            ) : (
              <div>
                <p className="font-medium">Import completed successfully!</p>
                <p className="text-sm mt-1">
                  All {successful} member{successful !== 1 ? 's' : ''} have been imported.
                </p>
              </div>
            )}
          </AlertDescription>
        </Alert>
      )}

      {/* Error Details */}
      {errors.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-red-600">
              <XCircle className="h-5 w-5" />
              Import Errors ({errors.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {errors.slice(0, 10).map((error, index) => (
                <div 
                  key={index}
                  className="flex items-start gap-3 p-3 bg-red-50 dark:bg-red-950/20 rounded-lg"
                >
                  <XCircle className="h-4 w-4 text-red-600 mt-0.5 flex-shrink-0" />
                  <div className="flex-1 min-w-0">
                    <p className="font-medium text-sm">{error.email}</p>
                    <p className="text-xs text-muted-foreground mt-1">
                      {error.error}
                    </p>
                  </div>
                </div>
              ))}
              
              {errors.length > 10 && (
                <div className="text-center text-sm text-muted-foreground pt-2 border-t">
                  ... and {errors.length - 10} more errors
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Success Summary */}
      {isComplete && successful > 0 && (
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="rounded-full bg-green-100 p-2">
                <Users className="h-4 w-4 text-green-600" />
              </div>
              <div>
                <p className="font-medium text-sm">
                  {successful} member{successful !== 1 ? 's' : ''} successfully imported
                </p>
                <p className="text-xs text-muted-foreground">
                  {successful === 1 ? 'The member has' : 'All members have'} been added to your organization and will receive invitation emails.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
