import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/next-auth";
import db from "@/lib/shared-db";
import { redirect } from "next/navigation";

// Types for dashboard data
export interface TenantMetrics {
  totalTenants: number;
  activeTenants: number;
  newTenants: number;
  growthRate: number;
}

export interface UserMetrics {
  totalUsers: number;
  activeUsers: number;
  activeUserPercentage: number;
  newUsers: number;
  growthRate: number;
}

export interface RevenueMetrics {
  mrr: number;
  arr: number;
  activeSubscriptions: number;
  growthRate: number;
}

export interface StorageMetrics {
  totalStorageGB: number;
  growthRate: number;
}

export interface UsageDataPoint {
  name: string;
  value: number;
  inputTokens?: number;
  outputTokens?: number;
  totalTokens?: number;
  cost?: number;
}

export interface RecentTenant {
  id: string;
  name: string;
  plan: string;
  createdAt: string;
  hasActiveSubscription: boolean;
  status: "active" | "inactive" | "trial";
}

export interface DashboardData {
  tenantMetrics: TenantMetrics;
  userMetrics: UserMetrics;
  revenueMetrics: RevenueMetrics;
  storageMetrics: StorageMetrics;
  tokenUsageData: UsageDataPoint[];
  storageUsageData: UsageDataPoint[];
  recentTenants: RecentTenant[];
}

// Helper function to parse period
function parsePeriod(period: string): { startDate: Date; endDate: Date } {
  const endDate = new Date();
  if (period === "lifetime") {
    const startDate = new Date(0); // January 1, 1970
    return { startDate, endDate };
  }
  const startDate = new Date();

  const match = period.match(/^(\d+)(days|months)$/);
  if (!match) {
    throw new Error(
      "Invalid period format. Use format like '30days' or '6months'"
    );
  }

  const amount = parseInt(match[1], 10);
  const unit = match[2];

  if (unit === "days") {
    startDate.setDate(startDate.getDate() - amount);
  } else if (unit === "months") {
    startDate.setMonth(startDate.getMonth() - amount);
  }

  return { startDate, endDate };
}

// Server action to get dashboard overview data
async function getDashboardOverview(
  period: string,
  tenantId?: string
): Promise<{
  tenantMetrics: TenantMetrics;
  userMetrics: UserMetrics;
  revenueMetrics: RevenueMetrics;
  storageMetrics: StorageMetrics;
}> {
  const { startDate, endDate } = parsePeriod(period);
  // Calculate the duration for previous period
  const periodDuration = endDate.getTime() - startDate.getTime();
  const previousPeriodStart = new Date(startDate.getTime() - periodDuration);

  // Base where clause for tenant queries
  const tenantBaseWhere: any = {};
  if (tenantId) {
    tenantBaseWhere.id = tenantId;
  }

  // Get tenant metrics
  const [totalTenants, activeTenants, newTenants, previousPeriodTenants] =
    await Promise.all([
      db.tenant.count({ where: tenantBaseWhere }),
      db.tenant.count({
        where: {
          ...tenantBaseWhere,
          Subscription: { some: { isActive: true } },
        },
      }),
      db.tenant.count({
        where: {
          ...tenantBaseWhere,
          createdAt: { gte: startDate, lte: endDate },
        },
      }),
      db.tenant.count({
        where: {
          ...tenantBaseWhere,
          createdAt: { gte: previousPeriodStart, lt: startDate },
        },
      }),
    ]);

  const tenantGrowthRate =
    previousPeriodTenants > 0
      ? Math.round(
          ((newTenants - previousPeriodTenants) / previousPeriodTenants) * 100
        )
      : newTenants > 0
        ? 100
        : 0;

  // Base where clause for user queries
  const userBaseWhere: any = {};
  if (tenantId) {
    userBaseWhere.Membership = { some: { tenantId } };
  }

  // Get user metrics
  const [totalUsers, newUsers, previousPeriodUsers] = await Promise.all([
    db.user.count({ where: userBaseWhere }),
    db.user.count({
      where: {
        ...userBaseWhere,
        createdAt: { gte: startDate, lte: endDate },
      },
    }),
    db.user.count({
      where: {
        ...userBaseWhere,
        createdAt: { gte: previousPeriodStart, lt: startDate },
      },
    }),
  ]);

  // Get active users (users with sessions in the period)
  const activeSessions = await db.session.findMany({
    where: {
      expires: { gte: startDate },
      user: userBaseWhere,
    },
    select: { userId: true },
  });

  const uniqueUserIds = new Set(
    activeSessions.map((session) => session.userId)
  );
  const activeUsers = uniqueUserIds.size;
  const activeUserPercentage =
    totalUsers > 0 ? Math.round((activeUsers / totalUsers) * 100) : 0;

  const userGrowthRate =
    previousPeriodUsers > 0
      ? Math.round(
          ((newUsers - previousPeriodUsers) / previousPeriodUsers) * 100
        )
      : newUsers > 0
        ? 100
        : 0;

  // Get revenue metrics
  const subscriptionWhere: any = { isActive: true };
  if (tenantId) {
    subscriptionWhere.tenantId = tenantId;
  }

  const subscriptions = await db.subscription.findMany({
    where: subscriptionWhere,
    include: { plan: true },
  });

  let mrr = 0;
  subscriptions.forEach((subscription) => {
    const basePrice =
      subscription.billingInterval === "month"
        ? subscription.plan.price || 0
        : (subscription.plan.price || 0) / 12;

    const additionalUsersCost =
      subscription.additionalUsers *
      (subscription.billingInterval === "month"
        ? subscription.plan.additionalUserFee || 0
        : (subscription.plan.additionalUserFee || 0) / 12);

    const additionalStorageCost = subscription.additionalStorageGB * 10;
    mrr += basePrice + additionalUsersCost + additionalStorageCost;
  });

  const arr = mrr * 12;

  // Get storage metrics
  const storageWhere: any = {
    timestamp: { gte: startDate, lte: endDate },
  };
  if (tenantId) {
    storageWhere.tenantId = tenantId;
  }

  const [storageUsage, previousStorageUsage] = await Promise.all([
    db.vectorStoreUsage.aggregate({
      where: storageWhere,
      _sum: { usageGB: true },
    }),
    db.vectorStoreUsage.aggregate({
      where: {
        ...storageWhere,
        timestamp: { gte: previousPeriodStart, lt: startDate },
      },
      _sum: { usageGB: true },
    }),
  ]);

  const totalStorageGB = storageUsage._sum.usageGB || 0;
  const previousStorageGB = previousStorageUsage._sum.usageGB || 0;

  const storageGrowthRate =
    previousStorageGB > 0
      ? Math.round(
          ((totalStorageGB - previousStorageGB) / previousStorageGB) * 100
        )
      : totalStorageGB > 0
        ? 100
        : 0;

  return {
    tenantMetrics: {
      totalTenants,
      activeTenants,
      newTenants,
      growthRate: tenantGrowthRate,
    },
    userMetrics: {
      totalUsers,
      activeUsers,
      activeUserPercentage,
      newUsers,
      growthRate: userGrowthRate,
    },
    revenueMetrics: {
      mrr,
      arr,
      activeSubscriptions: subscriptions.length,
      growthRate: 5, // Placeholder
    },
    storageMetrics: {
      totalStorageGB,
      growthRate: storageGrowthRate,
    },
  };
}

// Server action to get dashboard usage data
async function getDashboardUsage(
  period: string,
  tenantId?: string
): Promise<{
  tokenUsageData: UsageDataPoint[];
  storageUsageData: UsageDataPoint[];
}> {
  const { startDate, endDate } = parsePeriod(period);



  // Determine grouping unit based on period
  const match = period.match(/^(\d+)(days|months)$/);
  const unit = match ? match[2] : "days";

  // Base where clause for token usage queries
  const tokenUsageWhere: any = {
    timestamp: { gte: startDate, lte: endDate },
  };
  if (tenantId) {
    tokenUsageWhere.tenantId = tenantId;
  }

  // Get token usage data with error handling
  let tokenUsage = [];
  try {
    const tokenUsageCount = await db.tokenUsage.count();
    if (tokenUsageCount > 0) {
      tokenUsage = await db.tokenUsage.findMany({
        where: tokenUsageWhere,
        orderBy: { timestamp: "asc" },
        select: {
          id: true,
          userId: true,
          inputTokens: true,
          outputTokens: true,
          timestamp: true,
          requestType: true,
          modelUsed: true,
          cost: true,
          tenantId: true,
        },
      });
    }
  } catch (error) {
    console.error("Error fetching token usage:", error);
    tokenUsage = [];
  }

  // Group token usage by period
  const tokenUsageByPeriod: Record<
    string,
    {
      inputTokens: number;
      outputTokens: number;
      totalTokens: number;
      cost: number;
    }
  > = {};
  const monthNames = [
    "Jan",
    "Feb",
    "Mar",
    "Apr",
    "May",
    "Jun",
    "Jul",
    "Aug",
    "Sep",
    "Oct",
    "Nov",
    "Dec",
  ];

  tokenUsage.forEach((usage: any) => {
    const date = new Date(usage.timestamp);
    let key: string;

    if (unit === "days") {
      key = date.toISOString().split("T")[0]; // YYYY-MM-DD
    } else {
      key = `${monthNames[date.getMonth()]} ${date.getFullYear()}`;
    }

    if (!tokenUsageByPeriod[key]) {
      tokenUsageByPeriod[key] = {
        inputTokens: 0,
        outputTokens: 0,
        totalTokens: 0,
        cost: 0,
      };
    }

    tokenUsageByPeriod[key].inputTokens += usage.inputTokens;
    tokenUsageByPeriod[key].outputTokens += usage.outputTokens;
    tokenUsageByPeriod[key].totalTokens +=
      usage.inputTokens + usage.outputTokens;
    tokenUsageByPeriod[key].cost += usage.cost;
  });

  // Convert to array format for charts
  const tokenUsageData = Object.entries(tokenUsageByPeriod).map(
    ([name, data]) => ({
      name,
      inputTokens: (data as any).inputTokens,
      outputTokens: (data as any).outputTokens,
      totalTokens: (data as any).totalTokens,
      cost: (data as any).cost,
      value: (data as any).totalTokens,
    })
  );

  // Base where clause for storage usage queries
  const storageUsageWhere: any = {
    timestamp: { gte: startDate, lte: endDate },
  };
  if (tenantId) {
    storageUsageWhere.tenantId = tenantId;
  }

  // Get storage usage data
  let storageUsage = [];
  try {
    storageUsage = await db.vectorStoreUsage.findMany({
      where: storageUsageWhere,
      orderBy: { timestamp: "asc" },
    });
  } catch (error) {
    console.error("Error fetching storage usage:", error);
    storageUsage = [];
  }

  // Group storage usage by period
  const storageUsageByPeriod: Record<string, number> = {};

  storageUsage.forEach((usage: any) => {
    const date = new Date(usage.timestamp);
    let key: string;

    if (unit === "days") {
      key = date.toISOString().split("T")[0]; // YYYY-MM-DD
    } else {
      key = `${monthNames[date.getMonth()]} ${date.getFullYear()}`;
    }

    if (!storageUsageByPeriod[key]) {
      storageUsageByPeriod[key] = 0;
    }

    storageUsageByPeriod[key] += usage.usageGB;
  });

  // Convert to array format for charts
  const storageUsageData: UsageDataPoint[] = Object.entries(
    storageUsageByPeriod
  ).map(([name, value]) => ({
    name,
    value: value as number,
  }));

  return {
    tokenUsageData,
    storageUsageData,
  };
}

// Server action to get dashboard activity data
async function getDashboardActivity(
  period: string,
  tenantId?: string
): Promise<{
  recentTenants: RecentTenant[];
}> {
  const { startDate, endDate } = parsePeriod(period);

  // Base where clause for tenants
  const tenantsWhere: any = {
    createdAt: { gte: startDate, lte: endDate },
  };
  if (tenantId) {
    tenantsWhere.id = tenantId;
  }

  // Get recent tenants
  let recentTenants = [];
  try {
    recentTenants = await db.tenant.findMany({
      where: tenantsWhere,
      orderBy: { createdAt: "desc" },
      take: 5,
      include: {
        Subscription: {
          include: { plan: true },
        },
      },
    });
  } catch (error) {
    console.error("Error fetching recent tenants:", error);
    recentTenants = [];
  }

  // Format the recent tenants
  const formattedTenants: RecentTenant[] = recentTenants.map((tenant: any) => ({
    id: tenant.id,
    name: tenant.name,
    createdAt: tenant.createdAt.toISOString(),
    plan: tenant.Subscription[0]?.plan?.name || "No Plan",
    hasActiveSubscription: tenant.Subscription[0]?.isActive || false,
    status: tenant.Subscription[0]?.isActive ? "active" : "inactive",
  }));

  return {
    recentTenants: formattedTenants,
  };
}

// Main server action to get all dashboard data
export async function getDashboardData(
  period: string = "30days",
  tenantId?: string
): Promise<DashboardData> {
  const session = await getServerSession(authOptions);

  if (!session) {
    redirect("/auth/signin");
  }

  try {
    // Fetch all data in parallel
    const [overviewData, usageData, activityData] = await Promise.all([
      getDashboardOverview(period, tenantId),
      getDashboardUsage(period, tenantId),
      getDashboardActivity(period, tenantId),
    ]);

    return {
      ...overviewData,
      ...usageData,
      ...activityData,
    };
  } catch (error) {
    console.error("Error fetching dashboard data:", error);

    // Return fallback data
    return {
      tenantMetrics: {
        totalTenants: 0,
        activeTenants: 0,
        newTenants: 0,
        growthRate: 0,
      },
      userMetrics: {
        totalUsers: 0,
        activeUsers: 0,
        activeUserPercentage: 0,
        newUsers: 0,
        growthRate: 0,
      },
      revenueMetrics: {
        mrr: 0,
        arr: 0,
        activeSubscriptions: 0,
        growthRate: 0,
      },
      storageMetrics: {
        totalStorageGB: 0,
        growthRate: 0,
      },
      tokenUsageData: [],
      storageUsageData: [],
      recentTenants: [],
    };
  }
}
