"use client";

import React from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  CheckCircle,
  AlertTriangle,
  XCircle,
  Users,
  AlertCircle,
} from "lucide-react";
import { ParsedMemberData, CSVParseResult } from "@/lib/utils/csv-parser";
import { cn } from "@/lib/utils";

interface MemberPreviewTableProps {
  parseResult: CSVParseResult;
  className?: string;
}

export function MemberPreviewTable({
  parseResult,
  className,
}: MemberPreviewTableProps) {
  const { data, errors, warnings } = parseResult;
  const hasErrors = errors.length > 0;
  const hasWarnings = warnings.length > 0;

  const getRoleBadgeVariant = (role: string) => {
    switch (role) {
      case "ADMIN":
        return "destructive";
      case "CUSTOM":
        return "secondary";
      default:
        return "default";
    }
  };

  return (
    <div className={cn("space-y-6", className)}>
      {/* Summary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="rounded-full bg-primary/10 p-2">
                <Users className="h-4 w-4 text-primary" />
              </div>
              <div>
                <p className="text-2xl font-bold">{data.length}</p>
                <p className="text-xs text-muted-foreground">Valid Members</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="rounded-full bg-yellow-100 p-2">
                <AlertTriangle className="h-4 w-4 text-yellow-600" />
              </div>
              <div>
                <p className="text-2xl font-bold">{warnings.length}</p>
                <p className="text-xs text-muted-foreground">Warnings</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="rounded-full bg-red-100 p-2">
                <XCircle className="h-4 w-4 text-red-600" />
              </div>
              <div>
                <p className="text-2xl font-bold">{errors.length}</p>
                <p className="text-xs text-muted-foreground">Errors</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Errors Alert */}
      {hasErrors && (
        <Alert variant="destructive">
          <XCircle className="h-4 w-4" />
          <AlertDescription>
            <div className="space-y-2">
              <p className="font-medium">
                Found {errors.length} error{errors.length !== 1 ? "s" : ""} that
                must be fixed:
              </p>
              <ul className="list-disc list-inside space-y-1 text-sm">
                {errors.slice(0, 5).map((error, index) => (
                  <li key={index}>
                    Row {error.row}: {error.message} ({error.field})
                  </li>
                ))}
                {errors.length > 5 && (
                  <li className="text-muted-foreground">
                    ... and {errors.length - 5} more errors
                  </li>
                )}
              </ul>
            </div>
          </AlertDescription>
        </Alert>
      )}

      {/* Warnings Alert */}
      {hasWarnings && !hasErrors && (
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            <div className="space-y-2">
              <p className="font-medium">
                Found {warnings.length} warning
                {warnings.length !== 1 ? "s" : ""}:
              </p>
              <ul className="list-disc list-inside space-y-1 text-sm">
                {warnings.slice(0, 3).map((warning, index) => (
                  <li key={index}>
                    Row {warning.row}: {warning.message} ({warning.field})
                  </li>
                ))}
                {warnings.length > 3 && (
                  <li className="text-muted-foreground">
                    ... and {warnings.length - 3} more warnings
                  </li>
                )}
              </ul>
            </div>
          </AlertDescription>
        </Alert>
      )}

      {/* Success Message */}
      {!hasErrors && !hasWarnings && data.length > 0 && (
        <Alert>
          <CheckCircle className="h-4 w-4" />
          <AlertDescription>
            All {data.length} member{data.length !== 1 ? "s" : ""} are valid and
            ready to import!
          </AlertDescription>
        </Alert>
      )}

      {/* Preview Table */}
      {data.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Member Preview ({data.length} members)
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Email</TableHead>
                    <TableHead>Name</TableHead>
                    <TableHead>Role</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {data.slice(0, 10).map((member, index) => (
                    <TableRow key={index}>
                      <TableCell className="font-medium">
                        {member.email}
                      </TableCell>
                      <TableCell>
                        {member.name || (
                          <span className="text-muted-foreground italic">
                            Will use email
                          </span>
                        )}
                      </TableCell>
                      <TableCell>
                        <Badge variant={getRoleBadgeVariant(member.role)}>
                          {member.role}
                        </Badge>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              {data.length > 10 && (
                <div className="p-4 text-center text-sm text-muted-foreground border-t">
                  Showing first 10 of {data.length} members
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* No Data Message */}
      {data.length === 0 && !hasErrors && (
        <Card>
          <CardContent className="p-8 text-center">
            <AlertCircle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="font-semibold text-lg mb-2">No Valid Data Found</h3>
            <p className="text-muted-foreground">
              Please check your CSV file format and try again.
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
