"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { 
  Calendar,
  Filter,
  RefreshCw,
  Download,
  X
} from "lucide-react";
import { cn } from "@/lib/utils";

export interface DashboardFilters {
  timeRange: "7d" | "30d" | "90d";
  planTier?: string;
  region?: string;
}

interface DashboardFiltersProps {
  filters: DashboardFilters;
  onFiltersChange: (filters: DashboardFilters) => void;
  loading?: boolean;
  planTierOptions: { value: string; label: string }[];
}

const timeRangeOptions = [
  { value: "7d", label: "Last 7 days" },
  { value: "30d", label: "Last 30 days" },
  { value: "90d", label: "Last 90 days" },
];

const regionOptions = [
  { value: "all", label: "All Regions" },
  { value: "us-east", label: "US East" },
  { value: "us-west", label: "US West" },
  { value: "eu-central", label: "EU Central" },
  { value: "asia-pacific", label: "Asia Pacific" },
];

export function DashboardFilters({ 
  filters, 
  onFiltersChange, 
  loading = false ,
  planTierOptions
}: DashboardFiltersProps) {
  const handleTimeRangeChange = (timeRange: "7d" | "30d" | "90d") => {
    onFiltersChange({ ...filters, timeRange });
  };

  const handlePlanTierChange = (planTier: string) => {
    onFiltersChange({ ...filters, planTier: planTier === "all" ? undefined : planTier });
  };

  const handleRegionChange = (region: string) => {
    onFiltersChange({ ...filters, region: region === "all" ? undefined : region });
  };

  const clearFilters = () => {
    onFiltersChange({ timeRange: "30d" });
  };

  const hasActiveFilters = (filters.planTier && filters.planTier !== "all") || (filters.region && filters.region !== "all");

  const exportData = async () => {
    try {
      const params = new URLSearchParams({
        timeRange: filters.timeRange,
        ...(filters.planTier && { planTier: filters.planTier }),
        ...(filters.region && { region: filters.region }),
      });

      const response = await fetch(`/api/dashboard/export?${params}`);
      
      if (!response.ok) {
        throw new Error('Export failed');
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.style.display = 'none';
      a.href = url;
      a.download = `executive-dashboard-${new Date().toISOString().split('T')[0]}.csv`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error('Export failed:', error);
      // You could add a toast notification here
    }
  };

  return (
    <Card>
      <CardContent className="p-4">
        <div className="flex flex-col space-y-4 lg:flex-row lg:items-center lg:justify-between lg:space-y-0">
          {/* Filter Controls */}
          <div className="flex flex-col space-y-3 sm:flex-row sm:items-center sm:space-y-0 sm:space-x-3">
            {/* Time Range Buttons */}
            <div className="flex items-center space-x-1">
              <Calendar className="h-4 w-4 text-muted-foreground mr-2" />
              <div className="flex rounded-md border">
                {timeRangeOptions.map((option) => (
                  <Button
                    key={option.value}
                    variant={filters.timeRange === option.value ? "default" : "ghost"}
                    size="sm"
                    className={cn(
                      "rounded-none border-0 first:rounded-l-md last:rounded-r-md",
                      filters.timeRange === option.value && "bg-primary text-primary-foreground"
                    )}
                    onClick={() => handleTimeRangeChange(option.value as "7d" | "30d" | "90d")}
                    disabled={loading}
                  >
                    {option.label}
                  </Button>
                ))}
              </div>
            </div>

            {/* Plan Tier Filter */}
            <div className="flex items-center space-x-2">
              <Filter className="h-4 w-4 text-muted-foreground" />
              <Select
                value={filters.planTier || "all"}
                onValueChange={handlePlanTierChange}
                disabled={loading}
              >
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="Plan Tier" />
                </SelectTrigger>
                <SelectContent>
                  {planTierOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Region Filter */}
            {/* <Select
              value={filters.region || "all"}
              onValueChange={handleRegionChange}
              disabled={loading}
            >
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Region" />
              </SelectTrigger>
              <SelectContent>
                {regionOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select> */}

            {/* Clear Filters */}
            {hasActiveFilters && (
              <Button
                variant="outline"
                size="sm"
                onClick={clearFilters}
                disabled={loading}
                className="flex items-center space-x-1"
              >
                <X className="h-3 w-3" />
                <span>Clear</span>
              </Button>
            )}
          </div>

          {/* Action Buttons */}
          <div className="flex items-center space-x-2">
            {/* Active Filters Display */}
            {hasActiveFilters && (
              <div className="flex items-center space-x-1">
                {filters.planTier && filters.planTier !== "all" && (
                  <Badge variant="secondary" className="text-xs">
                    {planTierOptions.find(opt => opt.value === filters.planTier)?.label}
                  </Badge>
                )}
                {filters.region && filters.region !== "all" && (
                  <Badge variant="secondary" className="text-xs">
                    {regionOptions.find(opt => opt.value === filters.region)?.label}
                  </Badge>
                )}
              </div>
            )}

            {/* Export Button */}
            {/* <Button
              variant="outline"
              size="sm"
              onClick={exportData}
              disabled={loading}
              className="flex items-center space-x-1"
            >
              <Download className="h-3 w-3" />
              <span className="hidden sm:inline">Export CSV</span>
            </Button> */}

            {/* Refresh Indicator */}
            {loading && (
              <div className="flex items-center space-x-1 text-muted-foreground">
                <RefreshCw className="h-3 w-3 animate-spin" />
                <span className="text-xs hidden sm:inline">Updating...</span>
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
