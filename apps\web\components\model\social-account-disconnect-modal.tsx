import { useSocialAccountStore } from "@/lib/shared-store";
import { Button } from "../ui/button";
import { Loader2 } from "lucide-react";
import Modal from "./modal";
import { useDisconnectSocialConnect } from "@/hooks/use-disconnect";
import { useLanguage } from "@/lib/language-context";

export const DisconnectAccountModal = () => {
  const { mutate: handleDisconnect, isLoading } = useDisconnectSocialConnect();
  const data = useSocialAccountStore((state) => (state as any).disconnectData);
  const updateData = useSocialAccountStore(
    (state) => (state as any).disconnect
  );
  const { t } = useLanguage();

  if (!data?.showModal) {
    return null;
  }

  return (
    <Modal showModal={data?.showModal} setShowModal={() => updateData(false)}>
      <div className="mx-5 overflow-scroll rounded-lg bg-background p-4 shadow-lg sm:w-3/4 sm:overflow-hidden md:w-1/3">
        <p className="px-2 py-2">
          {t("socialAccount.disconnectConfirm", { platform: data?.platform })}
        </p>
        <p className="px-2 pb-4 text-sm opacity-70">
          {t("socialAccount.disconnectWarning", { platform: data?.platform })}
        </p>
        <div className="flex flex-1 items-center justify-end gap-2">
          <Button variant="outline" onClick={() => updateData(false)}>
            {t("common.cancel")}
          </Button>
          <Button
            disabled={isLoading}
            variant="default"
            onClick={() =>
              handleDisconnect({
                id: data?.id,
                platform: data?.platform?.toUpperCase(),
              })
            }
          >
            {isLoading ? (
              <Loader2 className={`mr-2 h-5 w-5 animate-spin`} />
            ) : null}
            {t("common.yes")}
          </Button>
        </div>
      </div>
    </Modal>
  );
};
