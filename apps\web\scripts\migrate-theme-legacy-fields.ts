#!/usr/bin/env tsx

/**
 * Database Migration Script: Remove Legacy Theme Color Fields
 * 
 * This script removes legacy color fields from existing ThemeConfiguration records
 * and ensures the database is clean after the schema migration.
 * 
 * Legacy fields being removed:
 * - primaryColor
 * - secondaryColor  
 * - accentColor
 * - navigationBackgroundColor
 * - contentBackgroundColor
 * - foregroundColor
 * 
 * Usage:
 * npx tsx scripts/migrate-theme-legacy-fields.ts
 */

import db from "../lib/shared-db";

interface MigrationStats {
  totalRecords: number;
  recordsWithLegacyFields: number;
  recordsUpdated: number;
  errors: string[];
}

async function migrateLegacyThemeFields(): Promise<MigrationStats> {
  const stats: MigrationStats = {
    totalRecords: 0,
    recordsWithLegacyFields: 0,
    recordsUpdated: 0,
    errors: []
  };

  try {
    console.log("🚀 Starting legacy theme fields migration...");
    
    // Get all theme configuration records
    const allThemeConfigs = await db.themeConfiguration.findMany({
      select: {
        id: true,
        tenantId: true,
        brandName: true,
        // Legacy fields (these will be removed)
        primaryColor: true,
        secondaryColor: true,
        accentColor: true,
        navigationBackgroundColor: true,
        contentBackgroundColor: true,
        foregroundColor: true,
        // New light/dark fields
        lightPrimaryColor: true,
        lightSecondaryColor: true,
        lightAccentColor: true,
        lightNavigationBackgroundColor: true,
        lightContentBackgroundColor: true,
        lightForegroundColor: true,
        darkPrimaryColor: true,
        darkSecondaryColor: true,
        darkAccentColor: true,
        darkNavigationBackgroundColor: true,
        darkContentBackgroundColor: true,
        darkForegroundColor: true,
        themePreset: true,
        version: true
      }
    });

    stats.totalRecords = allThemeConfigs.length;
    console.log(`📊 Found ${stats.totalRecords} theme configuration records`);

    if (stats.totalRecords === 0) {
      console.log("✅ No theme configuration records found. Migration complete.");
      return stats;
    }

    // Process each record
    for (const config of allThemeConfigs) {
      try {
        // Check if record has any legacy fields with values
        const hasLegacyFields = !!(
          config.primaryColor ||
          config.secondaryColor ||
          config.accentColor ||
          config.navigationBackgroundColor ||
          config.contentBackgroundColor ||
          config.foregroundColor
        );

        if (hasLegacyFields) {
          stats.recordsWithLegacyFields++;
          
          console.log(`🔄 Processing record for tenant: ${config.tenantId} (${config.brandName || 'Unnamed'})`);
          console.log(`   Legacy fields found:`, {
            primaryColor: config.primaryColor || 'none',
            secondaryColor: config.secondaryColor || 'none',
            accentColor: config.accentColor || 'none',
            navigationBackgroundColor: config.navigationBackgroundColor || 'none',
            contentBackgroundColor: config.contentBackgroundColor || 'none',
            foregroundColor: config.foregroundColor || 'none'
          });

          // Migrate legacy fields to light/dark fields if new fields are empty
          const updateData: any = {};
          let shouldUpdate = false;

          // Migrate legacy colors to light theme colors if light colors are empty
          if (config.primaryColor && !config.lightPrimaryColor) {
            updateData.lightPrimaryColor = config.primaryColor;
            shouldUpdate = true;
          }
          if (config.secondaryColor && !config.lightSecondaryColor) {
            updateData.lightSecondaryColor = config.secondaryColor;
            shouldUpdate = true;
          }
          if (config.accentColor && !config.lightAccentColor) {
            updateData.lightAccentColor = config.accentColor;
            shouldUpdate = true;
          }
          if (config.navigationBackgroundColor && !config.lightNavigationBackgroundColor) {
            updateData.lightNavigationBackgroundColor = config.navigationBackgroundColor;
            shouldUpdate = true;
          }
          if (config.contentBackgroundColor && !config.lightContentBackgroundColor) {
            updateData.lightContentBackgroundColor = config.contentBackgroundColor;
            shouldUpdate = true;
          }
          if (config.foregroundColor && !config.lightForegroundColor) {
            updateData.lightForegroundColor = config.foregroundColor;
            shouldUpdate = true;
          }

          // Clear legacy fields (set to null)
          updateData.primaryColor = null;
          updateData.secondaryColor = null;
          updateData.accentColor = null;
          updateData.navigationBackgroundColor = null;
          updateData.contentBackgroundColor = null;
          updateData.foregroundColor = null;

          // Increment version for cache invalidation
          updateData.version = { increment: 1 };
          updateData.updatedAt = new Date();

          if (shouldUpdate) {
            console.log(`   ✨ Migrating legacy colors to light theme fields`);
          }
          console.log(`   🧹 Clearing legacy fields`);

          // Update the record
          await db.themeConfiguration.update({
            where: { id: config.id },
            data: updateData
          });

          stats.recordsUpdated++;
          console.log(`   ✅ Updated record for tenant: ${config.tenantId}`);
        } else {
          console.log(`✨ Record for tenant ${config.tenantId} (${config.brandName || 'Unnamed'}) already clean`);
        }
      } catch (error) {
        const errorMsg = `Failed to update record for tenant ${config.tenantId}: ${error}`;
        console.error(`❌ ${errorMsg}`);
        stats.errors.push(errorMsg);
      }
    }

    console.log("\n📈 Migration Summary:");
    console.log(`   Total records: ${stats.totalRecords}`);
    console.log(`   Records with legacy fields: ${stats.recordsWithLegacyFields}`);
    console.log(`   Records updated: ${stats.recordsUpdated}`);
    console.log(`   Errors: ${stats.errors.length}`);

    if (stats.errors.length > 0) {
      console.log("\n❌ Errors encountered:");
      stats.errors.forEach(error => console.log(`   - ${error}`));
    }

    if (stats.recordsUpdated > 0) {
      console.log("\n🎉 Migration completed successfully!");
      console.log("   Legacy theme color fields have been removed from the database.");
      console.log("   Any existing legacy colors have been migrated to light theme fields.");
    } else {
      console.log("\n✅ No migration needed - all records are already clean!");
    }

  } catch (error) {
    console.error("💥 Migration failed:", error);
    stats.errors.push(`Migration failed: ${error}`);
  }

  return stats;
}

// Run the migration
async function main() {
  try {
    const stats = await migrateLegacyThemeFields();
    
    // Exit with error code if there were errors
    if (stats.errors.length > 0) {
      process.exit(1);
    }
    
    process.exit(0);
  } catch (error) {
    console.error("💥 Script execution failed:", error);
    process.exit(1);
  } finally {
    await db.$disconnect();
  }
}

// Only run if this script is executed directly
if (require.main === module) {
  main();
}

export { migrateLegacyThemeFields };
