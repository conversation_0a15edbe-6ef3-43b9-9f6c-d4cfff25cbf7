import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/next-auth";
import { redirect } from "next/navigation";
import db from "@/lib/shared-db";
import { SubscriptionDetailClient } from "./subscription-detail-client";
import { formatDate } from "@/lib/utils";

// Types for our data structures
interface Subscription {
  id: string;
  tenantName: string;
  tenantId: string;
  plan: string;
  planId: string;
  startDate: string;
  endDate: string | null;
  status: string;
  billingInterval: string;
  amount: number;
  additionalUsers: number;
  additionalStorage: number;
  storageTierItems: Array<{
    id: string;
    size: number;
    quantity: number;
    price: number;
    stripePriceId?: string;
  }>;
}

interface TokenUsageData {
  name: string;
  value: number;
  inputTokens: number;
  outputTokens: number;
  totalTokens: number;
  cost: number;
}

interface StorageUsageData {
  name: string;
  value: number;
}

interface SubscriptionDetailData {
  subscription: Subscription;
  tokenUsageData: TokenUsageData[];
  storageUsageData: StorageUsageData[];
  period: string;
}

// Function to fetch subscription details
async function fetchSubscriptionDetail(
  id: string,
  period: string = "30days"
): Promise<SubscriptionDetailData> {
  try {
    // Fetch the subscription
    const subscription = await db.subscription.findUnique({
      where: { id },
    });

    if (!subscription) {
      throw new Error("Subscription not found");
    }

    // Fetch related tenant and plan
    const [tenant, plan] = await Promise.all([
      db.tenant.findUnique({
        where: { id: subscription.tenantId },
        select: { id: true, name: true },
      }),
      db.plan.findUnique({
        where: { id: subscription.planId },
        select: { id: true, name: true, price: true },
      }),
    ]);

    // Determine status based on subscription properties
    let status = "inactive";
    if (subscription.isActive) {
      if (subscription.isOnTrial) {
        status = "trial";
      } else {
        status = "active";
      }
    }

    // Parse period for usage data
    const { startDate, endDate } = parsePeriod(period);

    // Fetch token usage data
    const tokenUsageWhere: any = {
      timestamp: { gte: startDate, lte: endDate },
      tenantId: subscription.tenantId,
    };

    const tokenUsage = await db.tokenUsage.findMany({
      where: tokenUsageWhere,
      orderBy: { timestamp: "asc" },
      select: {
        id: true,
        userId: true,
        inputTokens: true,
        outputTokens: true,
        timestamp: true,
        requestType: true,
        modelUsed: true,
        cost: true,
        tenantId: true,
      },
    });

    // Group token usage by period
    const tokenUsageByPeriod: Record<
      string,
      {
        inputTokens: number;
        outputTokens: number;
        totalTokens: number;
        cost: number;
      }
    > = {};
    const monthNames = [
      "Jan",
      "Feb",
      "Mar",
      "Apr",
      "May",
      "Jun",
      "Jul",
      "Aug",
      "Sep",
      "Oct",
      "Nov",
      "Dec",
    ];

    // Determine grouping unit based on period
    const match = period.match(/^(\d+)(days|months)$/);
    const unit = match ? match[2] : "days";

    tokenUsage.forEach((usage: any) => {
      const date = new Date(usage.timestamp);
      let key: string;

      if (unit === "days") {
        key = date.toISOString().split("T")[0]; // YYYY-MM-DD
      } else {
        key = `${monthNames[date.getMonth()]} ${date.getFullYear()}`;
      }

      if (!tokenUsageByPeriod[key]) {
        tokenUsageByPeriod[key] = {
          inputTokens: 0,
          outputTokens: 0,
          totalTokens: 0,
          cost: 0,
        };
      }

      tokenUsageByPeriod[key].inputTokens += usage.inputTokens;
      tokenUsageByPeriod[key].outputTokens += usage.outputTokens;
      tokenUsageByPeriod[key].totalTokens +=
        usage.inputTokens + usage.outputTokens;
      tokenUsageByPeriod[key].cost += usage.cost;
    });

    // Convert to array format for charts
    const tokenUsageData = Object.entries(tokenUsageByPeriod).map(
      ([name, data]) => ({
        name,
        inputTokens: (data as any).inputTokens,
        outputTokens: (data as any).outputTokens,
        totalTokens: (data as any).totalTokens,
        cost: (data as any).cost,
        value: (data as any).totalTokens,
      })
    );

    // Fetch storage usage data
    const storageUsageWhere: any = {
      timestamp: { gte: startDate, lte: endDate },
      tenantId: subscription.tenantId,
    };

    const storageUsage = await db.vectorStoreUsage.findMany({
      where: storageUsageWhere,
      orderBy: { timestamp: "asc" },
    });

    // Group storage usage by period
    const storageUsageByPeriod: Record<string, number> = {};

    storageUsage.forEach((usage: any) => {
      const date = new Date(usage.timestamp);
      let key: string;

      if (unit === "days") {
        key = date.toISOString().split("T")[0]; // YYYY-MM-DD
      } else {
        key = `${monthNames[date.getMonth()]} ${date.getFullYear()}`;
      }

      if (!storageUsageByPeriod[key]) {
        storageUsageByPeriod[key] = 0;
      }

      storageUsageByPeriod[key] += usage.usageGB;
    });

    // Convert to array format for charts
    const storageUsageData = Object.entries(storageUsageByPeriod).map(
      ([name, value]) => ({
        name,
        value: value as number,
      })
    );

    // Transform the subscription data
    const transformedSubscription: Subscription = {
      id: subscription.id,
      tenantName: tenant?.name || "Unknown Tenant",
      tenantId: subscription.tenantId,
      plan: plan?.name || "Unknown Plan",
      planId: subscription.planId,
      startDate: subscription.startDate.toISOString(),
      endDate: subscription.endDate ? subscription.endDate.toISOString() : null,
      status,
      billingInterval: subscription.billingInterval || "month",
      amount: plan?.price || 0,
      additionalUsers: subscription.additionalUsers,
      additionalStorage: subscription.additionalStorageGB,
      storageTierItems: subscription.storageTierItems || [],
    };

    return {
      subscription: transformedSubscription,
      tokenUsageData,
      storageUsageData,
      period,
    };
  } catch (error) {
    console.error("Error fetching subscription detail:", error);
    // Return fallback data
    return {
      subscription: {
        id: id,
        tenantName: "Error loading tenant",
        tenantId: "",
        plan: "Error loading plan",
        planId: "",
        startDate: new Date().toISOString(),
        endDate: null,
        status: "inactive",
        billingInterval: "month",
        amount: 0,
        additionalUsers: 0,
        additionalStorage: 0,
        storageTierItems: [],
      },
      tokenUsageData: [],
      storageUsageData: [],
      period,
    };
  }
}

// Helper function to parse period string into start and end dates
function parsePeriod(period: string): { startDate: Date; endDate: Date } {
  const endDate = new Date();
  let startDate = new Date();

  const match = period.match(/^(\d+)(days|months)$/);
  if (!match) {
    // Default to 30 days if format is invalid
    startDate.setDate(startDate.getDate() - 30);
    return { startDate, endDate };
  }

  const value = parseInt(match[1], 10);
  const unit = match[2];

  if (unit === "days") {
    startDate.setDate(startDate.getDate() - value);
  } else if (unit === "months") {
    startDate.setMonth(startDate.getMonth() - value);
  }

  return { startDate, endDate };
}

// Main server component
export default async function SubscriptionDetailPage({
  params,
  searchParams,
}: {
  params: { id: string };
  searchParams?: { [key: string]: string | string[] | undefined };
}) {
  const session = await getServerSession(authOptions);
  if (!session) {
    redirect("/auth/signin");
  }

  const { id } = params;
  const period = typeof searchParams?.period === "string" ? searchParams.period : "30days";

  // Fetch subscription detail data
  const subscriptionDetailData = await fetchSubscriptionDetail(id, period);

  return <SubscriptionDetailClient data={subscriptionDetailData} />;
}