/**
 * API Performance Monitoring System
 * 
 * Comprehensive monitoring for API endpoints including response times,
 * throughput, error rates, and resource usage
 */

import { NextRequest, NextResponse } from "next/server";

export interface PerformanceMetrics {
  endpoint: string;
  method: string;
  statusCode: number;
  responseTime: number;
  requestSize: number;
  responseSize: number;
  timestamp: Date;
  userAgent?: string;
  clientIP?: string;
  tenantId?: string;
  chatbotId?: string;
  errorMessage?: string;
  memoryUsage?: number;
  cpuUsage?: number;
}

export interface AggregatedMetrics {
  endpoint: string;
  totalRequests: number;
  successfulRequests: number;
  errorRequests: number;
  averageResponseTime: number;
  p95ResponseTime: number;
  p99ResponseTime: number;
  minResponseTime: number;
  maxResponseTime: number;
  throughput: number; // requests per second
  errorRate: number; // percentage
  lastUpdated: Date;
}

class APIPerformanceMonitor {
  private metrics: PerformanceMetrics[] = [];
  private aggregatedMetrics: Map<string, AggregatedMetrics> = new Map();
  private readonly maxMetricsHistory = 10000; // Keep last 10k metrics
  private readonly aggregationInterval = 60000; // 1 minute

  constructor() {
    // Start aggregation interval
    setInterval(() => {
      this.aggregateMetrics();
    }, this.aggregationInterval);

    // Clean up old metrics
    setInterval(() => {
      this.cleanupOldMetrics();
    }, 300000); // 5 minutes
  }

  /**
   * Record a new performance metric
   */
  recordMetric(metric: PerformanceMetrics): void {
    this.metrics.push(metric);
    
    // Maintain max history size
    if (this.metrics.length > this.maxMetricsHistory) {
      this.metrics = this.metrics.slice(-this.maxMetricsHistory);
    }

    // Log critical performance issues
    this.checkPerformanceThresholds(metric);
  }

  /**
   * Get aggregated metrics for an endpoint
   */
  getAggregatedMetrics(endpoint: string): AggregatedMetrics | null {
    return this.aggregatedMetrics.get(endpoint) || null;
  }

  /**
   * Get all aggregated metrics
   */
  getAllAggregatedMetrics(): AggregatedMetrics[] {
    return Array.from(this.aggregatedMetrics.values());
  }

  /**
   * Get raw metrics for a time period
   */
  getMetrics(
    startTime: Date,
    endTime: Date,
    endpoint?: string
  ): PerformanceMetrics[] {
    return this.metrics.filter(metric => {
      const inTimeRange = metric.timestamp >= startTime && metric.timestamp <= endTime;
      const matchesEndpoint = !endpoint || metric.endpoint === endpoint;
      return inTimeRange && matchesEndpoint;
    });
  }

  /**
   * Get performance summary for dashboard
   */
  getPerformanceSummary(): {
    totalRequests: number;
    averageResponseTime: number;
    errorRate: number;
    throughput: number;
    topEndpoints: Array<{ endpoint: string; requests: number; avgTime: number }>;
    recentErrors: PerformanceMetrics[];
  } {
    const now = new Date();
    const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
    const recentMetrics = this.getMetrics(oneHourAgo, now);

    const totalRequests = recentMetrics.length;
    const errorRequests = recentMetrics.filter(m => m.statusCode >= 400).length;
    const averageResponseTime = recentMetrics.reduce((sum, m) => sum + m.responseTime, 0) / totalRequests || 0;
    const errorRate = totalRequests > 0 ? (errorRequests / totalRequests) * 100 : 0;
    const throughput = totalRequests / 3600; // requests per second over the hour

    // Group by endpoint
    const endpointStats = new Map<string, { requests: number; totalTime: number }>();
    recentMetrics.forEach(metric => {
      const key = `${metric.method} ${metric.endpoint}`;
      const existing = endpointStats.get(key) || { requests: 0, totalTime: 0 };
      existing.requests++;
      existing.totalTime += metric.responseTime;
      endpointStats.set(key, existing);
    });

    const topEndpoints = Array.from(endpointStats.entries())
      .map(([endpoint, stats]) => ({
        endpoint,
        requests: stats.requests,
        avgTime: stats.totalTime / stats.requests
      }))
      .sort((a, b) => b.requests - a.requests)
      .slice(0, 10);

    const recentErrors = recentMetrics
      .filter(m => m.statusCode >= 400)
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
      .slice(0, 20);

    return {
      totalRequests,
      averageResponseTime,
      errorRate,
      throughput,
      topEndpoints,
      recentErrors
    };
  }

  /**
   * Aggregate metrics for efficient querying
   */
  private aggregateMetrics(): void {
    const now = new Date();
    const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
    const recentMetrics = this.getMetrics(oneHourAgo, now);

    // Group by endpoint
    const endpointGroups = new Map<string, PerformanceMetrics[]>();
    recentMetrics.forEach(metric => {
      const key = `${metric.method} ${metric.endpoint}`;
      if (!endpointGroups.has(key)) {
        endpointGroups.set(key, []);
      }
      endpointGroups.get(key)!.push(metric);
    });

    // Calculate aggregated metrics for each endpoint
    endpointGroups.forEach((metrics, endpoint) => {
      const responseTimes = metrics.map(m => m.responseTime).sort((a, b) => a - b);
      const successfulRequests = metrics.filter(m => m.statusCode < 400).length;
      const errorRequests = metrics.length - successfulRequests;

      const aggregated: AggregatedMetrics = {
        endpoint,
        totalRequests: metrics.length,
        successfulRequests,
        errorRequests,
        averageResponseTime: responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length,
        p95ResponseTime: responseTimes[Math.floor(responseTimes.length * 0.95)] || 0,
        p99ResponseTime: responseTimes[Math.floor(responseTimes.length * 0.99)] || 0,
        minResponseTime: responseTimes[0] || 0,
        maxResponseTime: responseTimes[responseTimes.length - 1] || 0,
        throughput: metrics.length / 3600, // per second over the hour
        errorRate: metrics.length > 0 ? (errorRequests / metrics.length) * 100 : 0,
        lastUpdated: now
      };

      this.aggregatedMetrics.set(endpoint, aggregated);
    });
  }

  /**
   * Check performance thresholds and log warnings
   */
  private checkPerformanceThresholds(metric: PerformanceMetrics): void {
    const thresholds = {
      responseTime: {
        warning: 2000, // 2 seconds
        critical: 5000 // 5 seconds
      },
      errorRate: {
        warning: 5, // 5%
        critical: 10 // 10%
      }
    };

    // Check response time
    if (metric.responseTime > thresholds.responseTime.critical) {
      console.error(`CRITICAL: Slow response time for ${metric.endpoint}: ${metric.responseTime}ms`);
    } else if (metric.responseTime > thresholds.responseTime.warning) {
      console.warn(`WARNING: Slow response time for ${metric.endpoint}: ${metric.responseTime}ms`);
    }

    // Check for errors
    if (metric.statusCode >= 500) {
      console.error(`ERROR: Server error on ${metric.endpoint}: ${metric.statusCode} - ${metric.errorMessage}`);
    } else if (metric.statusCode >= 400) {
      console.warn(`WARNING: Client error on ${metric.endpoint}: ${metric.statusCode} - ${metric.errorMessage}`);
    }
  }

  /**
   * Clean up old metrics to prevent memory leaks
   */
  private cleanupOldMetrics(): void {
    const cutoffTime = new Date(Date.now() - 24 * 60 * 60 * 1000); // 24 hours ago
    this.metrics = this.metrics.filter(metric => metric.timestamp > cutoffTime);

    // Clean up old aggregated metrics
    this.aggregatedMetrics.forEach((value, key) => {
      if (value.lastUpdated < cutoffTime) {
        this.aggregatedMetrics.delete(key);
      }
    });
  }

  /**
   * Export metrics for external monitoring systems
   */
  exportMetrics(format: 'json' | 'prometheus' = 'json'): string {
    if (format === 'prometheus') {
      return this.exportPrometheusMetrics();
    }

    return JSON.stringify({
      timestamp: new Date().toISOString(),
      summary: this.getPerformanceSummary(),
      aggregatedMetrics: this.getAllAggregatedMetrics(),
      recentMetrics: this.metrics.slice(-100) // Last 100 metrics
    }, null, 2);
  }

  /**
   * Export metrics in Prometheus format
   */
  private exportPrometheusMetrics(): string {
    const summary = this.getPerformanceSummary();
    const aggregated = this.getAllAggregatedMetrics();

    let output = '';

    // Overall metrics
    output += `# HELP api_requests_total Total number of API requests\n`;
    output += `# TYPE api_requests_total counter\n`;
    output += `api_requests_total ${summary.totalRequests}\n\n`;

    output += `# HELP api_response_time_seconds Average API response time\n`;
    output += `# TYPE api_response_time_seconds gauge\n`;
    output += `api_response_time_seconds ${summary.averageResponseTime / 1000}\n\n`;

    output += `# HELP api_error_rate_percent API error rate percentage\n`;
    output += `# TYPE api_error_rate_percent gauge\n`;
    output += `api_error_rate_percent ${summary.errorRate}\n\n`;

    // Per-endpoint metrics
    output += `# HELP api_endpoint_requests_total Total requests per endpoint\n`;
    output += `# TYPE api_endpoint_requests_total counter\n`;
    aggregated.forEach(metric => {
      output += `api_endpoint_requests_total{endpoint="${metric.endpoint}"} ${metric.totalRequests}\n`;
    });

    output += `\n# HELP api_endpoint_response_time_seconds Response time per endpoint\n`;
    output += `# TYPE api_endpoint_response_time_seconds gauge\n`;
    aggregated.forEach(metric => {
      output += `api_endpoint_response_time_seconds{endpoint="${metric.endpoint}",quantile="0.5"} ${metric.averageResponseTime / 1000}\n`;
      output += `api_endpoint_response_time_seconds{endpoint="${metric.endpoint}",quantile="0.95"} ${metric.p95ResponseTime / 1000}\n`;
      output += `api_endpoint_response_time_seconds{endpoint="${metric.endpoint}",quantile="0.99"} ${metric.p99ResponseTime / 1000}\n`;
    });

    return output;
  }
}

// Global instance
export const apiPerformanceMonitor = new APIPerformanceMonitor();

/**
 * Middleware to track API performance
 */
export function withPerformanceMonitoring(
  handler: (request: NextRequest, context?: any) => Promise<NextResponse>
) {
  return async (request: NextRequest, context?: any): Promise<NextResponse> => {
    const startTime = Date.now();
    const requestSize = parseInt(request.headers.get('content-length') || '0');
    
    let response: NextResponse;
    let error: Error | null = null;

    try {
      response = await handler(request, context);
    } catch (err) {
      error = err as Error;
      response = NextResponse.json(
        { error: 'Internal server error' },
        { status: 500 }
      );
    }

    const endTime = Date.now();
    const responseTime = endTime - startTime;
    const responseSize = response.headers.get('content-length') 
      ? parseInt(response.headers.get('content-length')!)
      : 0;

    // Extract endpoint info
    const url = new URL(request.url);
    const endpoint = url.pathname;
    const method = request.method;

    // Get client info
    const clientIP = request.headers.get('x-forwarded-for')?.split(',')[0] || 
                    request.headers.get('x-real-ip') || 
                    'unknown';
    const userAgent = request.headers.get('user-agent') || undefined;

    // Extract tenant/chatbot info from URL if available
    const pathParts = endpoint.split('/');
    const chatbotId = pathParts.includes('chatbot') 
      ? pathParts[pathParts.indexOf('chatbot') + 1] 
      : undefined;

    // Record the metric
    const metric: PerformanceMetrics = {
      endpoint,
      method,
      statusCode: response.status,
      responseTime,
      requestSize,
      responseSize,
      timestamp: new Date(startTime),
      userAgent,
      clientIP,
      chatbotId,
      errorMessage: error?.message
    };

    apiPerformanceMonitor.recordMetric(metric);

    // Add performance headers to response
    response.headers.set('X-Response-Time', `${responseTime}ms`);
    response.headers.set('X-Request-ID', `${startTime}-${Math.random().toString(36).substr(2, 9)}`);

    return response;
  };
}

/**
 * Get performance metrics for API endpoint
 */
export async function getPerformanceMetrics(
  endpoint?: string,
  timeRange: '1h' | '24h' | '7d' = '1h'
) {
  const now = new Date();
  const timeRanges = {
    '1h': 60 * 60 * 1000,
    '24h': 24 * 60 * 60 * 1000,
    '7d': 7 * 24 * 60 * 60 * 1000
  };
  
  const startTime = new Date(now.getTime() - timeRanges[timeRange]);
  
  if (endpoint) {
    return {
      aggregated: apiPerformanceMonitor.getAggregatedMetrics(endpoint),
      raw: apiPerformanceMonitor.getMetrics(startTime, now, endpoint)
    };
  }
  
  return {
    summary: apiPerformanceMonitor.getPerformanceSummary(),
    aggregated: apiPerformanceMonitor.getAllAggregatedMetrics(),
    raw: apiPerformanceMonitor.getMetrics(startTime, now)
  };
}
