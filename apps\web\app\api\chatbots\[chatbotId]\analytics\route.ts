import { NextRequest, NextResponse } from "next/server";
import db from "@/lib/shared-db";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/next-auth";

export async function GET(
  req: Request,
  { params }: { params: { chatbotId: string } }
) {
  try {
    const session: any = await getServerSession(authOptions);
    if (!session) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const url = new URL(req.url);
    const tenantId = url.searchParams.get("tenantId");
    const timeRange = url.searchParams.get("timeRange") || "week";

    if (!tenantId) {
      return NextResponse.json(
        { error: "Tenant ID is required" },
        { status: 400 }
      );
    }

    // Check if chatbot exists and user has access
    const chatbot = await db.chatbot.findUnique({
      where: {
        id: params.chatbotId,
      },
    });

    if (!chatbot) {
      return NextResponse.json({ error: "Chatbot not found" }, { status: 404 });
    }

    // Verify user has access to this chatbot
    if (chatbot.userId !== session.userId || chatbot.tenantId !== tenantId) {
      return NextResponse.json({ error: "Access denied" }, { status: 403 });
    }

    // Calculate date range
    const now = new Date();
    let startDate: Date;

    switch (timeRange) {
      case "day":
        startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
        break;
      case "week":
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case "month":
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      default:
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    }

    // Get session analytics
    const sessions = await db.chatbotSession.findMany({
      where: {
        chatbotId: params.chatbotId,
        startedAt: {
          gte: startDate,
        },
        // Filter out sessions without tenantId (legacy data)
        tenantId: {
          not: null,
        },
      },
      orderBy: {
        startedAt: "desc",
      },
    });

    // Get analytics records
    const analyticsRecords = await db.chatbotAnalytics.findMany({
      where: {
        chatbotId: params.chatbotId,
        date: {
          gte: startDate,
        },
      },
      orderBy: {
        date: "desc",
      },
    });

    // Calculate summary metrics
    const totalSessions = sessions.length;
    const totalMessages = sessions.reduce(
      (sum, session) => sum + session.messagesCount,
      0
    );
    const completedSessions = sessions.filter((s) => s.endedAt).length;
    const averageSessionDuration =
      sessions
        .filter((s) => s.duration && s.duration > 0)
        .reduce((sum, s) => sum + (s.duration || 0), 0) /
      Math.max(1, sessions.filter((s) => s.duration).length);

    // Calculate satisfaction metrics
    const ratedSessions = sessions.filter((s) => s.satisfactionRating);
    const averageSatisfaction =
      ratedSessions.length > 0
        ? ratedSessions.reduce(
            (sum, s) => sum + (s.satisfactionRating || 0),
            0
          ) / ratedSessions.length
        : null;

    // Calculate unique users (estimated by unique IP addresses)
    const uniqueIPs = new Set(sessions.map((s) => s.ipAddress).filter(Boolean));
    const estimatedUniqueUsers = uniqueIPs.size;

    // Calculate bounce rate (sessions with only 1 message)
    const bounceSessions = sessions.filter((s) => s.messagesCount <= 1).length;
    const bounceRate =
      totalSessions > 0 ? (bounceSessions / totalSessions) * 100 : 0;

    // Group sessions by domain
    const domainStats = sessions.reduce(
      (acc, session) => {
        const domain = session.domain || "unknown";
        if (!acc[domain]) {
          acc[domain] = { count: 0, messages: 0 };
        }
        acc[domain].count++;
        acc[domain].messages += session.messagesCount;
        return acc;
      },
      {} as Record<string, { count: number; messages: number }>
    );

    // Group sessions by date for time series
    const dailyStats = sessions.reduce(
      (acc, session) => {
        const date = session.startedAt.toISOString().split("T")[0];
        if (!acc[date]) {
          acc[date] = { sessions: 0, messages: 0, duration: 0 };
        }
        acc[date].sessions++;
        acc[date].messages += session.messagesCount;
        acc[date].duration += session.duration || 0;
        return acc;
      },
      {} as Record<
        string,
        { sessions: number; messages: number; duration: number }
      >
    );

    // Convert to arrays for charting
    const timeSeriesData = Object.entries(dailyStats)
      .sort(([a], [b]) => a.localeCompare(b))
      .map(([date, stats]: [string, any]) => ({
        date,
        sessions: stats.sessions,
        messages: stats.messages,
        averageDuration:
          stats.sessions > 0 ? stats.duration / stats.sessions : 0,
      }));

    // Top domains
    const topDomains = Object.entries(domainStats)
      .sort(([, a]: [string, any], [, b]: [string, any]) => (b as any).count - (a as any).count)
      .slice(0, 10)
      .map(([domain, stats]: [string, any]) => ({
        domain,
        sessions: stats.count,
        messages: stats.messages,
      }));

    // Recent feedback
    const recentFeedback = sessions
      .filter((s) => s.feedback || s.satisfactionRating)
      .slice(0, 10)
      .map((s) => ({
        date: s.startedAt,
        rating: s.satisfactionRating,
        comment: s.feedback,
        domain: s.domain,
      }));

    // Performance metrics
    const averageResponseTime =
      sessions
        .filter((s) => s.averageResponseTime)
        .reduce((sum, s) => sum + (s.averageResponseTime || 0), 0) /
      Math.max(1, sessions.filter((s) => s.averageResponseTime).length);

    const errorSessions = sessions.filter((s) => s.errorCount > 0).length;
    const errorRate =
      totalSessions > 0 ? (errorSessions / totalSessions) * 100 : 0;

    const analytics = {
      summary: {
        totalSessions,
        totalMessages,
        estimatedUniqueUsers,
        completedSessions,
        averageSessionDuration: Math.round(averageSessionDuration),
        averageSatisfaction: averageSatisfaction
          ? Math.round(averageSatisfaction * 10) / 10
          : null,
        bounceRate: Math.round(bounceRate * 10) / 10,
        averageResponseTime: Math.round(averageResponseTime),
        errorRate: Math.round(errorRate * 10) / 10,
      },
      timeRange: {
        start: startDate.toISOString(),
        end: now.toISOString(),
        period: timeRange,
      },
      timeSeries: timeSeriesData,
      domains: topDomains,
      feedback: recentFeedback,
      usage: {
        totalUsage: chatbot.usageCount,
        monthlyUsage: chatbot.monthlyUsage,
        lastUsed: chatbot.lastUsedAt,
      },
    };

    return NextResponse.json({ analytics });
  } catch (error) {
    console.error("[CHATBOT_ANALYTICS_GET]", error);
    return NextResponse.json({ error: "Internal Error" }, { status: 500 });
  }
}
