import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/next-auth/src/auth-options";
import { BlobServiceClient } from "@azure/storage-blob";
import db from "@/lib/shared-db";
import { withPermission } from "@/lib/permission-middleware";

const containerName = process.env.AZURE_STORAGE_CONTAINER_NAME || "default";
const connectionString = process.env.AZURE_STORAGE_CONNECTION_STRING || "";

// Helper function to truncate filename to ensure Azure Video Indexer compatibility
function truncateFilename(filename: string, maxLength: number = 50): string {
  if (filename.length <= maxLength) return filename;

  const extension = filename.substring(filename.lastIndexOf('.'));
  const nameWithoutExt = filename.substring(0, filename.lastIndexOf('.'));
  const maxNameLength = maxLength - extension.length;

  if (maxNameLength <= 0) return filename.substring(0, maxLength);

  return nameWithoutExt.substring(0, maxNameLength) + extension;
}

async function uploadFile(request: Request) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const formData = await request.formData();
    const file = formData.get("file") as File;
    const tenantId = formData.get("tenantId") as string;
    const workspaceSlug = formData.get("workspaceSlug") as string;

    if (!file || !tenantId || !workspaceSlug) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      );
    }
    const tenant = await db.tenant.findUnique({
      where: { id: tenantId },
    });

    const blobServiceClient =
      BlobServiceClient.fromConnectionString(connectionString);
    const containerClient = blobServiceClient.getContainerClient(containerName);

    // Create container if it doesn't exist
    await containerClient.createIfNotExists({
      access: "container",
    });

    // Truncate filename to ensure Azure Video Indexer compatibility
    // Account for timestamp prefix and path structure
    const truncatedFilename = truncateFilename(file.name, 50);

    // Generate blob path with tenant and workspace structure
    const blobName = `${tenant?.slug}/${workspaceSlug}/${Date.now()}-${truncatedFilename}`;
    const blockBlobClient = containerClient.getBlockBlobClient(blobName);

    // Convert File to ArrayBuffer
    const arrayBuffer = await file.arrayBuffer();

    // Upload file
    await blockBlobClient.uploadData(arrayBuffer, {
      blobHTTPHeaders: { blobContentType: file.type },
    });

    return NextResponse.json({
      url: blockBlobClient.url,
      message: "File uploaded successfully",
    });
  } catch (error) {
    console.error("Error uploading file:", error);
    return NextResponse.json(
      { error: "Failed to upload file" },
      { status: 500 }
    );
  }
}

// Export the POST handler with permission middleware
export const POST = withPermission(uploadFile, "CREATE", "FILE");
