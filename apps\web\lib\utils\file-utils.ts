/**
 * File utility functions for file upload and processing
 */

import { SupportedMimeTypes, FileSizeLimits, getFileCategory, SupportedExtensions } from "@/lib/constant/supported-extensions";

export interface FileValidationResult {
  valid: boolean;
  error?: string;
  category?: string;
  maxSize?: number;
}

export interface FileInfo {
  name: string;
  type: string;
  size: number;
  category: string;
  extension: string;
  isSupported: boolean;
  maxAllowedSize: number;
}

/**
 * Validates a file against supported types and size limits
 */
export const validateFileUpload = (file: File): FileValidationResult => {
  // Check if file is supported by either MIME type or extension
  if (!isFileSupported(file)) {
    const extension = getFileExtension(file.name);
    return {
      valid: false,
      error: `Unsupported file type: .${extension}. Please use supported formats: Images (JPG, PNG, WebP), Documents (PDF, TXT, CSV, Markdown, Excel), Audio files (MP3, WAV, M4A, OGG, FLAC), or Video files (MP4, AVI, MOV, WebM, MKV).`
    };
  }

  // Determine file category - use MIME type if available, otherwise use extension
  let category: keyof typeof FileSizeLimits;
  if (file.type && file.type.trim() !== '') {
    category = getFileCategory(file.type);
  } else {
    // Fallback to extension-based categorization when MIME type is empty
    category = getFileCategoryByExtension(file.name);
  }

  const maxSize = FileSizeLimits[category];
  const maxSizeMB = Math.round(maxSize / (1024 * 1024));
  const fileSizeMB = Math.round(file.size / (1024 * 1024));

  if (file.size > maxSize) {
    return {
      valid: false,
      error: `File "${file.name}" is ${fileSizeMB}MB, which exceeds the ${maxSizeMB}MB limit for ${category} files.`,
      category,
      maxSize
    };
  }

  return {
    valid: true,
    category,
    maxSize
  };
};

/**
 * Gets comprehensive file information
 */
export const getFileInfo = (file: File): FileInfo => {
  const extension = getFileExtension(file.name);

  // Determine category using the same logic as validation
  let category: keyof typeof FileSizeLimits;
  if (file.type && file.type.trim() !== '') {
    category = getFileCategory(file.type);
  } else {
    category = getFileCategoryByExtension(file.name);
  }

  const isSupported = isFileSupported(file);
  const maxAllowedSize = FileSizeLimits[category];

  return {
    name: file.name,
    type: file.type,
    size: file.size,
    category,
    extension,
    isSupported,
    maxAllowedSize
  };
};

/**
 * Extracts file extension from filename
 */
export const getFileExtension = (filename: string): string => {
  return filename.split('.').pop()?.toLowerCase() || '';
};

/**
 * Checks if a file is supported by either MIME type or file extension
 * This handles cases where browsers don't set MIME types for certain files (like .md files)
 */
export const isFileSupported = (file: File): boolean => {
  // First check if MIME type is supported
  if (file.type && SupportedMimeTypes.includes(file.type)) {
    return true;
  }

  // If MIME type is empty or not recognized, check file extension
  const extension = getFileExtension(file.name);
  return SupportedExtensions.includes(extension);
};

/**
 * Gets the file category, handling cases where MIME type might be empty
 */
export const getFileCategoryByExtension = (filename: string): keyof typeof FileSizeLimits => {
  const extension = getFileExtension(filename);

  // Image extensions
  if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(extension)) {
    return 'image';
  }

  // Audio extensions
  if (['mp3', 'wav', 'm4a', 'ogg', 'flac'].includes(extension)) {
    return 'audio';
  }

  // Video extensions
  if (['mp4', 'avi', 'mov', 'webm', 'mkv', 'flv', 'wmv', 'm4v', '3gp'].includes(extension)) {
    return 'video';
  }

  // Document extensions
  if (['pdf', 'txt', 'csv', 'md', 'mdx', 'markdown', 'docx', 'doc', 'xlsx', 'xls', 'pptx', 'ppt'].includes(extension)) {
    return 'document';
  }

  return 'default';
};

/**
 * Gets a default MIME type based on file extension when browser doesn't provide one
 */
export const getDefaultMimeType = (filename: string): string => {
  const extension = getFileExtension(filename);

  // Common MIME type mappings for extensions
  const mimeTypeMap: Record<string, string> = {
    // Images
    'jpg': 'image/jpeg',
    'jpeg': 'image/jpeg',
    'png': 'image/png',
    'gif': 'image/gif',
    'bmp': 'image/bmp',
    'webp': 'image/webp',

    // Documents
    'pdf': 'application/pdf',
    'txt': 'text/plain',
    'csv': 'text/csv',
    'md': 'text/markdown',
    'mdx': 'text/x-markdown',
    'markdown': 'text/markdown',
    'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'doc': 'application/vnd.ms-word',
    'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'xls': 'application/vnd.ms-excel',
    'pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    'ppt': 'application/vnd.ms-powerpoint',

    // Audio
    'mp3': 'audio/mpeg',
    'wav': 'audio/wav',
    'm4a': 'audio/mp4',
    'ogg': 'audio/ogg',
    'flac': 'audio/flac',

    // Video
    'mp4': 'video/mp4',
    'avi': 'video/x-msvideo',
    'mov': 'video/quicktime',
    'webm': 'video/webm',
    'mkv': 'video/x-matroska',
    'flv': 'video/x-flv',
    'wmv': 'video/x-ms-wmv',
    'm4v': 'video/mp4',
    '3gp': 'video/3gpp'
  };

  return mimeTypeMap[extension] || 'application/octet-stream';
};

/**
 * Formats file size in human-readable format
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

/**
 * Gets file type description for display
 */
export const getFileTypeDescription = (mimeType: string): string => {
  if (mimeType.startsWith('image/')) {
    return 'Image';
  } else if (mimeType === 'application/pdf') {
    return 'PDF Document';
  } else if (mimeType.includes('spreadsheet') || mimeType.includes('excel')) {
    return 'Excel Spreadsheet';
  } else if (mimeType.includes('csv')) {
    return 'CSV File';
  } else if (mimeType.includes('wordprocessingml') || mimeType.includes('ms-word')) {
    return 'Word Document';
  } else if (mimeType.includes('presentationml') || mimeType.includes('ms-powerpoint')) {
    return 'PowerPoint Presentation';
  } else if (mimeType.startsWith('text/')) {
    return 'Text File';
  } else if (mimeType.startsWith('audio/')) {
    return 'Audio File';
  } else if (mimeType.includes('document')) {
    return 'Document';
  }
  return 'File';
};

/**
 * Checks if file is an image
 */
export const isImageFile = (mimeType: string): boolean => {
  return mimeType.startsWith('image/');
};

/**
 * Checks if file is an audio file
 */
export const isAudioFile = (mimeType: string): boolean => {
  return mimeType.startsWith('audio/');
};

/**
 * Checks if file is a video file
 */
export const isVideoFile = (mimeType: string): boolean => {
  return mimeType.startsWith('video/');
};

/**
 * Checks if file is a document
 */
export const isDocumentFile = (mimeType: string): boolean => {
  return mimeType.includes('pdf') ||
         mimeType.includes('document') ||
         mimeType.includes('spreadsheet') ||
         mimeType.includes('text') ||
         mimeType.includes('csv') ||
         mimeType.includes('word') ||
         mimeType.includes('powerpoint') ||
         mimeType.includes('presentation');
};

/**
 * Gets appropriate file icon class based on file type
 */
export const getFileIconClass = (mimeType: string): string => {
  if (isImageFile(mimeType)) {
    return 'text-blue-500';
  } else if (mimeType === 'application/pdf') {
    return 'text-red-500';
  } else if (mimeType.includes('spreadsheet') || mimeType.includes('excel') || mimeType.includes('csv')) {
    return 'text-green-500';
  } else if (mimeType.includes('wordprocessingml') || mimeType.includes('ms-word')) {
    return 'text-blue-600';
  } else if (mimeType.includes('presentationml') || mimeType.includes('ms-powerpoint')) {
    return 'text-orange-500';
  } else if (mimeType.includes('markdown')) {
    return 'text-indigo-500';
  } else if (isAudioFile(mimeType)) {
    return 'text-purple-500';
  } else if (mimeType.startsWith('text/')) {
    return 'text-gray-500';
  }
  return 'text-gray-400';
};

/**
 * Validates multiple files at once
 */
export const validateMultipleFiles = (files: File[], maxFiles: number = 5): {
  validFiles: File[];
  invalidFiles: Array<{ file: File; error: string }>;
  totalSizeExceeded: boolean;
} => {
  const validFiles: File[] = [];
  const invalidFiles: Array<{ file: File; error: string }> = [];

  // Check file count
  if (files.length > maxFiles) {
    return {
      validFiles: [],
      invalidFiles: files.map(file => ({ 
        file, 
        error: `Too many files. Maximum ${maxFiles} files allowed.` 
      })),
      totalSizeExceeded: false
    };
  }

  // Validate each file
  files.forEach(file => {
    const validation = validateFileUpload(file);
    if (validation.valid) {
      validFiles.push(file);
    } else {
      invalidFiles.push({ file, error: validation.error || 'Unknown error' });
    }
  });

  // Check total size (optional - can be implemented based on requirements)
  const totalSize = validFiles.reduce((sum, file) => sum + file.size, 0);
  const maxTotalSize = 100 * 1024 * 1024; // 100MB total limit
  const totalSizeExceeded = totalSize > maxTotalSize;

  return {
    validFiles,
    invalidFiles,
    totalSizeExceeded
  };
};

/**
 * Creates a preview URL for supported file types
 */
export const createFilePreview = (file: File): string | null => {
  if (isImageFile(file.type)) {
    return URL.createObjectURL(file);
  }
  return null;
};

/**
 * Cleans up preview URLs to prevent memory leaks
 */
export const cleanupPreviewUrl = (url: string): void => {
  if (url && url.startsWith('blob:')) {
    URL.revokeObjectURL(url);
  }
};

/**
 * Formats duration in seconds to MM:SS or HH:MM:SS format
 */
export const formatDuration = (seconds: number): string => {
  if (isNaN(seconds) || seconds < 0) return '0:00';

  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);

  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  } else {
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  }
};

/**
 * Checks if file extension is audio
 */
export const isAudioExtension = (extension: string): boolean => {
  const audioExtensions = ['mp3', 'wav', 'm4a', 'ogg', 'flac', 'aac'];
  return audioExtensions.includes(extension.toLowerCase());
};

/**
 * Checks if file extension is video
 */
export const isVideoExtension = (extension: string): boolean => {
  const videoExtensions = ['mp4', 'avi', 'mov', 'webm', 'mkv', 'flv', 'wmv', 'm4v', '3gp'];
  return videoExtensions.includes(extension.toLowerCase());
};
