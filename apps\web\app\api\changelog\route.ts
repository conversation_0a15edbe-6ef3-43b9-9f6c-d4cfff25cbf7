import { NextRequest, NextResponse } from "next/server";
import { authOptions } from "@/lib/next-auth";
import db from "@/lib/shared-db";
import { getServerSession } from "next-auth/next";
import {
  getLanguageFromRequest,
  createServerTranslator,
} from "@/lib/server-translations";

export async function GET(request: NextRequest) {
  const language = getLanguageFromRequest(request);
  const t = createServerTranslator(language);

  try {
    const session: any = await getServerSession(authOptions);

    if (!session?.userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const userId = (session as any).user.id;
    const tenantId = searchParams.get("tenantId");
    const environment = searchParams.get("environment") || process.env.NODE_ENV;

    if (!tenantId) {
      return NextResponse.json(
        { error: "Tenant ID is required" },
        { status: 400 }
      );
    }

    // Get user's viewed changelog IDs
    const viewedChangelogs = await db.userChangelogView.findMany({
      where: { userId },
      select: { changelogId: true },
    });
    const viewedIds = viewedChangelogs.map((view) => view.changelogId);

    // Get current time for expiration check
    const currentTime = new Date();

    // Build filter conditions for unviewed changelogs
    const whereConditions: any = {
      isActive: true,
      publishedAt: { lte: currentTime },
      ...(viewedIds.length > 0 && { id: { notIn: viewedIds } }),
      OR: [{ expiresAt: { gt: currentTime } }, { expiresAt: null }],
    };

    // Filter by environment and tenant
    const environmentFilter = [
      { targetEnvironment: environment },
      { targetEnvironment: null },
    ];

    const tenantFilter = [
      { targetTenants: { has: tenantId } },
      { targetTenants: { equals: [] } },
    ];

    // Combine filters
    whereConditions.AND = [{ OR: environmentFilter }, { OR: tenantFilter }];

    const changelogs = await db.changelog.findMany({
      where: whereConditions,
      orderBy: [{ priority: "desc" }, { publishedAt: "desc" }],
    });
    return NextResponse.json({ data: changelogs });
  } catch (error) {
    console.error("Error fetching changelogs:", error);
    return NextResponse.json(
      { error: t("api.errors.failedToFetchChangelogs") },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session: any = await getServerSession(authOptions);
    if (!session?.userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if user is admin (you may need to adjust this based on your role system)
    const user = await db.user.findUnique({
      where: { id: session.userId },
      include: { membership: true },
    });

    const isAdmin = user?.membership?.some(
      (m) => m.role === "ADMIN" || m.role === "OWNER"
    );
    if (!isAdmin) {
      return NextResponse.json(
        { error: "Admin access required" },
        { status: 403 }
      );
    }

    const body = await request.json();
    const {
      title,
      content,
      version,
      type = "RELEASE",
      priority = "MEDIUM",
      targetTenants = [],
      targetEnvironment,
      publishedAt,
      expiresAt,
      githubCommitSha,
      deploymentId,
    } = body;

    if (!title || !content) {
      return NextResponse.json(
        { error: "Title and content are required" },
        { status: 400 }
      );
    }

    const changelog = await db.changelog.create({
      data: {
        title,
        content,
        version,
        type,
        priority,
        targetTenants,
        targetEnvironment,
        publishedAt: publishedAt ? new Date(publishedAt) : new Date(),
        expiresAt: expiresAt ? new Date(expiresAt) : null,
        githubCommitSha,
        deploymentId,
        authorId: session.userId,
      },
    });

    return NextResponse.json({ data: changelog });
  } catch (error) {
    console.error("Error creating changelog:", error);
    return NextResponse.json(
      { error: "Failed to create changelog" },
      { status: 500 }
    );
  }
}
