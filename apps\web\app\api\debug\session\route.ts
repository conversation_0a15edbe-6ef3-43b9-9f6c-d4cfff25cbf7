import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/next-auth";
import db from "@/lib/shared-db";
import crypto from "crypto";

export async function GET() {
  try {
    const session: any = await getServerSession(authOptions);
    
    // Get all users and their memberships for debugging
    const users = await db.user.findMany({
      include: {
        membership: {
          include: {
            tenant: true,
          },
        },
      },
      take: 5, // Limit to first 5 users for debugging
    });

    // Get total counts
    const userCount = await db.user.count();
    const membershipCount = await db.membership.count();
    const tenantCount = await db.tenant.count();

    return NextResponse.json({
      session: session ? {
        userId: session.userId,
        email: session.user?.email,
        memberships: session.memberships,
        currentOrganization: session.currentOrganization,
      } : null,
      database: {
        userCount,
        membershipCount,
        tenantCount,
        users: users.map(user => ({
          id: user.id,
          email: user.email ? "***@***.***" : null, // Mask email for privacy
          emailVerified: user.emailVerified,
          memberships: user.membership.map(m => ({
            tenantId: m.tenantId,
            role: m.role,
            tenantName: m.tenant.name,
          })),
        })),
      },
    });
  } catch (error) {
    console.error("Debug session error:", error);
    return NextResponse.json(
      { 
        error: "Failed to debug session",
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}
