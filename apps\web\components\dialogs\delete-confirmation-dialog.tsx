"use client";

import { useState } from "react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useLanguage } from "@/lib/language-context";
import { AlertTriangle } from "lucide-react";

interface DeleteConfirmationDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title: string;
  description: string;
  warningText?: string;
  confirmText?: string;
  requireTypeConfirmation?: boolean;
  typeConfirmationText?: string;
  typeConfirmationPlaceholder?: string;
  isLoading?: boolean;
  loadingText?: string;
  destructiveActionText?: string;
}

export function DeleteConfirmationDialog({
  isOpen,
  onClose,
  onConfirm,
  title,
  description,
  warningText,
  confirmText,
  requireTypeConfirmation = false,
  typeConfirmationText = "",
  typeConfirmationPlaceholder = "",
  isLoading = false,
  loadingText,
  destructiveActionText,
}: DeleteConfirmationDialogProps) {
  const { t } = useLanguage();
  const [confirmationInput, setConfirmationInput] = useState("");

  const handleClose = () => {
    setConfirmationInput("");
    onClose();
  };

  const handleConfirm = () => {
    if (requireTypeConfirmation && confirmationInput !== typeConfirmationText) {
      return;
    }
    onConfirm();
  };

  const isConfirmDisabled = 
    isLoading || 
    (requireTypeConfirmation && confirmationInput !== typeConfirmationText);

  return (
    <AlertDialog open={isOpen} onOpenChange={handleClose}>
      <AlertDialogContent className="max-w-md">
        <AlertDialogHeader>
          <div className="flex items-center gap-3">
            <div className="flex h-10 w-10 items-center justify-center rounded-full bg-destructive/10">
              <AlertTriangle className="h-5 w-5 text-destructive" />
            </div>
            <div>
              <AlertDialogTitle className="text-left">{title}</AlertDialogTitle>
            </div>
          </div>
          <AlertDialogDescription className="text-left space-y-3">
            <p>{description}</p>
            {warningText && (
              <div className="rounded-md bg-destructive/10 p-3">
                <p className="text-sm font-medium text-destructive">
                  {warningText}
                </p>
              </div>
            )}
            {requireTypeConfirmation && (
              <div className="space-y-2">
                <Label htmlFor="confirmation-input" className="text-sm font-medium">
                  {confirmText}
                </Label>
                <Input
                  id="confirmation-input"
                  placeholder={typeConfirmationPlaceholder}
                  value={confirmationInput}
                  onChange={(e) => setConfirmationInput(e.target.value)}
                  disabled={isLoading}
                  className="font-mono"
                />
              </div>
            )}
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel disabled={isLoading} onClick={handleClose}>
            {t("common.cancel")}
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={handleConfirm}
            disabled={isConfirmDisabled}
            className="bg-destructive text-destructive-foreground hover:bg-destructive/90 focus:ring-destructive"
          >
            {isLoading 
              ? loadingText || t("common.loading")
              : destructiveActionText || t("common.delete")
            }
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
