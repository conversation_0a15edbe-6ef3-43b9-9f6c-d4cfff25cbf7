"use client";

import React, { useState } from "react";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  Server,
  Loader2,
  CheckCircle,
  XCircle,
  Zap,
  Clock,
  RefreshCw,
} from "lucide-react";
import { useLanguage } from "@/lib/language-context";

interface MCPStatusIndicatorProps {
  status:
    | "connecting"
    | "health_check"
    | "reconnecting"
    | "reconnection_failed"
    | "tool_call"
    | "processing"
    | "completed"
    | "error";
  message?: string;
  serverName?: string;
  serverId?: string;
  toolName?: string;
  errorType?: string;
  rawError?: string;
  className?: string;
}

export function MCPStatusIndicator({
  status,
  message,
  serverName,
  serverId,
  toolName,
  errorType,
  rawError,
  className,
}: MCPStatusIndicatorProps) {
  const { t } = useLanguage();
  const [showErrorGuide, setShowErrorGuide] = useState(false);
  const getStatusIcon = () => {
    switch (status) {
      case "connecting":
        return <Loader2 className="h-4 w-4 animate-spin text-blue-500" />;
      case "health_check":
        return <Server className="h-4 w-4 animate-pulse text-yellow-500" />;
      case "reconnecting":
        return <RefreshCw className="h-4 w-4 animate-spin text-orange-500" />;
      case "reconnection_failed":
        return <XCircle className="h-4 w-4 text-red-500" />;
      case "tool_call":
        return <Zap className="h-4 w-4 text-orange-500 animate-pulse" />;
      case "processing":
        return <Loader2 className="h-4 w-4 animate-spin text-green-500" />;
      case "completed":
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case "error":
        return <XCircle className="h-4 w-4 text-red-500" />;
      default:
        return <Server className="h-4 w-4 text-muted-foreground" />;
    }
  };

  const getStatusColor = () => {
    switch (status) {
      case "connecting":
        return "border-l-blue-500 bg-blue-50";
      case "health_check":
        return "border-l-yellow-500 bg-yellow-50";
      case "reconnecting":
        return "border-l-orange-500 bg-orange-50";
      case "reconnection_failed":
        return "border-l-red-500 bg-red-50";
      case "tool_call":
        return "border-l-orange-500 bg-orange-50";
      case "processing":
        return "border-l-green-500 bg-green-50";
      case "completed":
        return "border-l-green-500 bg-green-50";
      case "error":
        return "border-l-red-500 bg-red-50";
      default:
        return "border-l-gray-500 bg-gray-50";
    }
  };

  const getStatusText = () => {
    switch (status) {
      case "connecting":
        return t("mcpServers.statusIndicator.connecting");
      case "health_check":
        return t("mcpServers.statusIndicator.healthCheck");
      case "reconnecting":
        return t("mcpServers.statusIndicator.reconnecting");
      case "reconnection_failed":
        return t("mcpServers.statusIndicator.reconnectionFailed");
      case "tool_call":
        return toolName
          ? t("mcpServers.statusIndicator.toolCall", { toolName })
          : t("mcpServers.statusIndicator.toolCallGeneric");
      case "processing":
        return t("mcpServers.statusIndicator.processing");
      case "completed":
        return t("mcpServers.statusIndicator.completed");
      case "error":
        return t("mcpServers.statusIndicator.error");
      default:
        return t("mcpServers.statusIndicator.unknown");
    }
  };

  return (
    <Card className={`border-l-4 ${getStatusColor()} ${className}`}>
      <CardContent className="p-3">
        <div className="flex items-center gap-2">
          {getStatusIcon()}
          <div className="flex-1">
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium">
                {message || getStatusText()}
              </span>
              {status === "tool_call" && toolName && (
                <Badge variant="outline" className="text-xs">
                  <Zap className="h-3 w-3 mr-1" />
                  {toolName}
                </Badge>
              )}
            </div>
            {serverName && (
              <div className="flex items-center gap-1 mt-1">
                <Server className="h-3 w-3 text-muted-foreground" />
                <span className="text-xs text-muted-foreground">
                  {serverName}
                </span>
                {serverId && (
                  <span className="text-xs text-muted-foreground">
                    ({serverId.slice(0, 8)}...)
                  </span>
                )}
              </div>
            )}
          </div>
          {status === "processing" && (
            <div className="flex items-center gap-1">
              <Clock className="h-3 w-3 text-muted-foreground" />
              <span className="text-xs text-muted-foreground">
                {t("mcpServers.statusIndicator.processingLabel")}
              </span>
            </div>
          )}
        </div>

        {/* Error Actions */}
        {status === "error" && (
          <div className="mt-2 flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowErrorGuide(!showErrorGuide)}
              className="text-xs"
            >
              {showErrorGuide
                ? t("mcpServers.statusIndicator.hideErrorGuide")
                : t("mcpServers.statusIndicator.showErrorGuide")}
            </Button>
          </div>
        )}

        {/* Detailed Error Guide */}
      </CardContent>
    </Card>
  );
}
