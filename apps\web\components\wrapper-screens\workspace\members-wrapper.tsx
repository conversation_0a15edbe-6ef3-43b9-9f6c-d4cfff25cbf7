"use client";

import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import { InviteDialog } from "@/components/model/invite-dialog";
import { useState, useEffect } from "react";
import toast from "react-hot-toast";
import { inviteWorkspaceMember, removeWorkspace<PERSON>ember } from "@/services";
import { useRouter } from "next/navigation";
import { useLanguage } from "@/lib/language-context";
import { fetchWorkspaceBySlugWithCache } from "@/utils/workspace";
import { CreateGuard, DeleteGuard } from "@/components/permission-guard";
import { usePermissions } from "@/hooks/use-permissions";

export default function WorkspaceMembersPage({
  tenantId,
  members,
  workspaceSlug,
  currentUserId,
}) {
  const router = useRouter();
  const { t } = useLanguage();
  const [open, setOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [workspaceTitle, setWorkspaceTitle] = useState("");

  // Fetch the workspace name when component mounts
  useEffect(() => {
    const getWorkspaceName = async () => {
      try {
        const workspace = await fetchWorkspaceBySlugWithCache(workspaceSlug);
        if (workspace && workspace.name) {
          setWorkspaceTitle(workspace.name);
        } else {
          // Fallback to formatting the slug if workspace data is not available
          const formattedTitle = workspaceSlug
            .split("-")
            .slice(0, -1)
            .map((word: string) => word.charAt(0).toUpperCase() + word.slice(1))
            .join(" ");
          setWorkspaceTitle(formattedTitle);
        }
      } catch (error) {
        console.error("Error fetching workspace name:", error);
        // Fallback to formatting the slug
        const formattedTitle = workspaceSlug
          .split("-")
          .slice(0, -1)
          .map((word: string) => word.charAt(0).toUpperCase() + word.slice(1))
          .join(" ");
        setWorkspaceTitle(formattedTitle);
      }
    };

    getWorkspaceName();
  }, [workspaceSlug]);

  async function onSubmit(values: { email: string; role: string }) {
    setIsLoading(true);
    toast.loading(t("workspace.sendingInvitation"));

    try {
      const result = await inviteWorkspaceMember(
        {
          email: values.email,
          role: values.role,
          tenantId: tenantId,
          slug: workspaceSlug,
        },
        tenantId,
        currentUserId
      );

      toast.remove();

      if (result.error) {
        toast.error(result.error);
      } else {
        router.refresh();
        toast.success(t("workspace.invitationSentSuccess"));

        setOpen(false);
      }
    } catch (error) {
      console.error("Invite user error:", error);
      toast.error(t("workspace.failedToSendInvitation"));
    } finally {
      setIsLoading(false);
    }
  }

  const removeMember = async (memberId: string) => {
    setIsLoading(true);
    toast.loading(t("workspace.removingMember"));
    try {
      const result = await removeWorkspaceMember(
        {
          memberId: memberId,
          slug: workspaceSlug,
        },
        tenantId,
        currentUserId
      );
      toast.remove();
      if (result.error) {
        toast.error(result.error);
      } else {
        router.refresh();
        toast.success(t("workspace.memberRemovedSuccess"));
      }
    } catch (error) {
      console.error("Error removing member:", error);
      toast.error(t("workspace.failedToRemoveMember"));
    } finally {
      setIsLoading(false);
    }
  };

  const userRole = members?.find(
    (member: any) => member.user.id === currentUserId
  )?.role;
  // Check if user has permission to view members
  const { hasPermission } = usePermissions();
  const canViewMembers = hasPermission("READ", "MEMBER");

  // If user doesn't have permission to view members, show access denied message
  if (!canViewMembers) {
    return (
      <div className="h-full px-1 py-2 md:px-4">
        <div className="flex items-center mb-6">
          <Link href={`/workspace/${workspaceSlug}`}>
            <Button variant="outline" size="icon" className="mr-2">
              <ArrowLeft className="h-4 w-4" />
            </Button>
          </Link>
          <h1 className="text-2xl font-bold md:text-3xl tracking-tight">
            {workspaceTitle} {t("workspace.members")}
          </h1>
        </div>
        <div className="rounded-lg border bg-card p-6 text-center">
          <h2 className="text-xl font-semibold text-red-500 mb-2">
            {t("common.accessDenied")}
          </h2>
          <p className="text-muted-foreground">
            {t("common.noPermissionToView")}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full px-1 py-2 md:px-4">
      <div className="flex items-center mb-6">
        <Link href={`/workspace/${workspaceSlug}`}>
          <Button variant="outline" size="icon" className="mr-2">
            <ArrowLeft className="h-4 w-4" />
          </Button>
        </Link>
        <h1 className="text-2xl font-bold md:text-3xl tracking-tight">
          {workspaceTitle} {t("workspace.members")}
        </h1>
      </div>

      <div className="grid gap-6">
        <div className="rounded-lg border bg-card p-6">
          {/* <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-semibold">
              {t("workspace.workspaceMembers")}
            </h2>
            <CreateGuard hasPermission={permission?.member?.create} resource="MEMBER">
              <InviteDialog
                subTitle={t("workspace.inviteUserSubtitle")}
                title={t("workspace.inviteUser")}
                trigger={t("workspace.addMember")}
                open={open}
                isLoading={isLoading}
                onSubmit={onSubmit}
                setOpen={setOpen}
                userRole={userRole}
              />
            </CreateGuard>
          </div> */}

          <div className="rounded-md border">
            <div className="grid grid-cols-3 md:grid-cols-4 p-4 font-medium border-b">
              <div>{t("common.name")}</div>
              <div className="hidden md:block">{t("common.email")}</div>
              <div>{t("roles.role")}</div>
              <div className="text-right">{t("common.actions")}</div>
            </div>

            {members.map((member: any) => (
              <div
                key={member.id}
                className="grid grid-cols-3 md:grid-cols-4 p-4 border-b last:border-0 items-center"
              >
                <div>{member?.user?.name}</div>
                <div className="hidden md:block text-muted-foreground">
                  {member?.user?.email}
                </div>
                <div>{member.role}</div>
                <div className="text-right">
                  <DeleteGuard
                    hasPermission={member?.permission?.member?.delete}
                    resource="MEMBER"
                    fallback={
                      <Button
                        variant="outline"
                        size="sm"
                        disabled={true}
                        className="cursor-not-allowed opacity-50"
                      >
                        {t("common.remove")}
                      </Button>
                    }
                  >
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => removeMember(member?.id)}
                      disabled={isLoading}
                    >
                      {t("common.remove")}
                    </Button>
                  </DeleteGuard>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
