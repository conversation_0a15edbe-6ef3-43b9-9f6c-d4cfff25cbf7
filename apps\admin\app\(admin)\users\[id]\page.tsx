import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/next-auth";
import { redirect, notFound } from "next/navigation";
import { Button } from "@/components/ui/button";
import { formatDate } from "@/lib/utils";
import db from "@/lib/shared-db";
import {
  ArrowLeft,
  Building,
  Calendar,
  Mail,
  Shield,
  User,
  Users,
  HardDrive,
} from "lucide-react";
import Link from "next/link";
import { MetricCard } from "@/components/dashboard/metric-card";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { UsageChart } from "@/components/dashboard/usage-chart";
import { addDays } from "date-fns";
import React from "react";

// Types for user data structure
interface UserTenant {
  id: string;
  name: string;
  role: string;
}

interface UserActivity {
  type: string;
  timestamp: Date;
  details: string;
}

interface StorageUsageData {
  name: string;
  value: number;
}

interface UserData {
  id: string;
  name: string | null;
  email: string;
  emailVerified: Date | null;
  image: string | null;
  createdAt: Date;
  updatedAt: Date;
  tenants: UserTenant[];
  totalTenants: number;
  lastActive: Date;
  recentActivity: UserActivity[];
  storageUsage: string;
  storageUsageData: StorageUsageData[];
}

// Return type includes value and unit separately
type StorageUsage = {
  value: number;
  unit: "KB" | "MB" | "GB";
};

async function calculateUserStorage(userId: string): Promise<StorageUsage> {
  const memberships = await db.membership.findMany({
    where: { userId },
    select: { tenantId: true },
  });

  const tenantIds = memberships.map((m) => m.tenantId);
  if (tenantIds.length === 0) return { value: 0, unit: "KB" };

  const workspaces = await db.workspace.findMany({
    where: { tenantId: { in: tenantIds } },
    select: { id: true },
  });

  const workspaceIds = workspaces.map((w) => w.id);
  if (workspaceIds.length === 0) return { value: 0, unit: "KB" };

  const files = await db.file.findMany({
    where: {
      workspaceId: { in: workspaceIds },
    },
    select: { size: true },
  });

  let totalKB = 0;

  for (const file of files) {
    if (!file.size || typeof file.size !== "string") continue;

    const match = file.size.match(/^([\d.]+)\s*(KB|MB|GB)?$/i);
    if (!match) continue;

    const value = parseFloat(match[1]);
    const unit = match[2]?.toUpperCase() || "KB";

    if (unit === "KB") totalKB += value;
    else if (unit === "MB") totalKB += value * 1024;
    else if (unit === "GB") totalKB += value * 1024 * 1024;
  }

  // Determine appropriate unit to return
  if (totalKB >= 1024 * 1024) {
    return { value: +(totalKB / (1024 * 1024)).toFixed(2), unit: "GB" };
  } else if (totalKB >= 1024) {
    return { value: +(totalKB / 1024).toFixed(2), unit: "MB" };
  } else {
    return { value: +totalKB.toFixed(2), unit: "KB" };
  }
}


async function calculateUserStorageByMonth(userId: string, period: string = "12months"): Promise<StorageUsageData[]> {
  const memberships = await db.membership.findMany({
    where: { userId },
    select: { tenantId: true },
  });

  const tenantIds = memberships.map((m) => m.tenantId);
  if (tenantIds.length === 0) return [];

  const workspaces = await db.workspace.findMany({
    where: { tenantId: { in: tenantIds } },
    select: { id: true },
  });

  const workspaceIds = workspaces.map((w) => w.id);
  if (workspaceIds.length === 0) return [];

  // Set date range
  let startDate = new Date();
  switch (period) {
    case "7days":
      startDate = addDays(new Date(), -7);
      break;
    case "30days":
      startDate = addDays(new Date(), -30);
      break;
    case "90days":
      startDate = addDays(new Date(), -90);
      break;
    case "6months":
      startDate = addDays(new Date(), -180);
      break;
    case "12months":
      startDate = addDays(new Date(), -365);
      break;
    case "lifetime":
      startDate = new Date(0);
      break;
    default:
      startDate = addDays(new Date(), -30);
  }

  // Fetch files
  const files = await db.file.findMany({
    where: {
      workspaceId: { in: workspaceIds },
      createdAt: { gte: startDate },
    },
    select: { size: true, createdAt: true },
  });

  const storageByKey: Record<string, number> = {};
  const isWeekly = period === "7days";

  for (const file of files) {
    if (!file.size || typeof file.size !== "string") continue;

    const match = file.size.match(/^([\d.]+)\s*(KB|MB|GB)?$/i);
    if (!match) continue;

    const value = parseFloat(match[1]);
    const unit = match[2]?.toUpperCase() || "KB";

    let sizeInKB = 0;
    if (unit === "KB") sizeInKB = value;
    else if (unit === "MB") sizeInKB = value * 1024;
    else if (unit === "GB") sizeInKB = value * 1024 * 1024;

    // Group by date or month
    const key = isWeekly
      ? file.createdAt.toLocaleDateString("en-US", { day: "numeric", month: "short", year: "numeric" }) // e.g., "19 Jun 2025"
      : file.createdAt.toLocaleDateString("en-US", { month: "short", year: "numeric" }); // e.g., "Jun 2025"

    storageByKey[key] = (storageByKey[key] || 0) + sizeInKB;
  }

  // Convert to sorted array
  const sortedData = Object.entries(storageByKey)
    .map(([name, kb]) => ({
      name,
      value: parseFloat((kb / 1024).toFixed(2)), // in MB
    }))
    .sort((a, b) => new Date(a.name).getTime() - new Date(b.name).getTime());

  return sortedData;
}

// Server-side data fetching function
async function fetchUserData(id: string, period: string = "12months"): Promise<UserData | null> {
  // Validate ObjectId format
  if (!id || id.length !== 24 || !/^[0-9a-f]{24}$/i.test(id)) {
    return null;
  }

  try {
    const user = await db.user.findUnique({
      where: { id },
      include: {
        membership: {
          include: {
            tenant: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
        sessions: {
          orderBy: {
            createdAt: "desc",
          },
          take: 1,
        },
      },
    });

    if (!user) {
      return null;
    }

    // Transform tenant data
    const tenants = user.membership.map((membership) => ({
      id: membership.tenant.id,
      name: membership.tenant.name,
      role: membership.role,
    }));

    // Get last active time from sessions or default to updatedAt
    const lastActive = user.sessions[0]?.updatedAt || user.updatedAt;

    // Mock recent activity (in a real app, this would come from a proper activity log)
    const recentActivity: UserActivity[] = [
      {
        type: "Login",
        timestamp: lastActive,
        details: "User logged in",
      },
      {
        type: "Profile Update",
        timestamp: user.updatedAt,
        details: "User profile was updated",
      },
      {
        type: "Account Created",
        timestamp: user.createdAt,
        details: "User account was created",
      },
    ];

    // Calculate storage usage
    const { value, unit } = await calculateUserStorage(id);
    const storageUsageData = await calculateUserStorageByMonth(id, period);

    const storageFormatted =
      unit === "GB"
        ? `${value.toFixed(2)} GB`
        : unit === "MB"
          ? `${value.toFixed(2)} MB`
          : `${value.toFixed(2)} KB`;

    return {
      id: user.id,
      name: user.name,
      email: user.email,
      emailVerified: user.emailVerified,
      image: user.image,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
      tenants,
      totalTenants: tenants.length,
      lastActive,
      recentActivity,
      storageUsage: storageFormatted,
      storageUsageData,
    };
  } catch (error) {
    console.error("Error fetching user data:", error);
    return null;
  }
}

// Import client components from separate files
import { UserStorageChart } from "./user-storage-chart";


// Server component
export default async function UserDetailPage({
  params,
  searchParams,
}: {
  params: { id: string };
  searchParams?: { [key: string]: string | string[] | undefined };
}) {
  const session = await getServerSession(authOptions);
  if (!session) {
    redirect("/auth/signin");
  }

  const { id } = params;
  const period = typeof searchParams?.period === "string" ? searchParams.period : "12months";

  // Fetch user data server-side
  const userData = await fetchUserData(id, period);

  if (!userData) {
    notFound();
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <Link href="/users">
          <Button variant="outline" size="icon">
            <ArrowLeft className="h-4 w-4" />
          </Button>
        </Link>
        <div>
          <h1 className="text-3xl font-bold">{userData.name || "Unknown User"}</h1>
          <p className="text-muted-foreground">{userData.email}</p>
        </div>
      </div>

      {/* User metrics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5">
        <MetricCard
          title="Member Since"
          value={formatDate(userData.createdAt)}
          icon={Calendar}
          color="blue"
        />
        <MetricCard
          title="Last Active"
          value={formatDate(userData.lastActive)}
          icon={User}
          color="green"
        />
        <MetricCard
          title="Organizations"
          value={userData.totalTenants.toString()}
          icon={Building}
          color="indigo"
        />
        <MetricCard
          title="Email Status"
          value={userData.emailVerified ? "Verified" : "Unverified"}
          icon={Mail}
          color="amber"
        />
        <MetricCard
          title="Storage Used"
          value={userData.storageUsage}
          icon={HardDrive}
          color="green"
        />
      </div>

      {/* Storage Usage Chart */}
      <UserStorageChart
        storageUsageData={userData.storageUsageData}
        initialPeriod={period}
      />

      {/* User details */}
      <div className="grid gap-6 md:grid-cols-2">
        {/* Organizations/Tenants */}
        <Card>
          <CardHeader>
            <CardTitle>Organizations</CardTitle>
          </CardHeader>
          <CardContent>
            {userData.tenants.length > 0 ? (
              <div className="overflow-x-auto">
                <table className="w-full text-sm">
                  <thead>
                    <tr className="border-b">
                      <th className="px-4 py-3 text-left font-medium">Name</th>
                      <th className="px-4 py-3 text-left font-medium">Role</th>
                    </tr>
                  </thead>
                  <tbody>
                    {userData.tenants.map((tenant) => (
                      <tr key={tenant.id} className="border-b">
                        <td className="px-4 py-3">
                          <Link
                            href={`/tenants/${tenant.id}`}
                            className="text-blue-600 hover:underline"
                          >
                            {tenant.name}
                          </Link>
                        </td>
                        <td className="px-4 py-3">
                          <span className="inline-flex items-center rounded-full bg-blue-50 px-2 py-1 text-xs font-medium text-blue-700">
                            {tenant.role}
                          </span>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                <Building className="mx-auto h-12 w-12 mb-4 opacity-50" />
                <p>User is not a member of any organization.</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Recent Activity */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Activity</CardTitle>
          </CardHeader>
          <CardContent>
            {userData.recentActivity.length > 0 ? (
              <div className="space-y-4">
                {userData.recentActivity.map((activity, index) => (
                  <div key={index} className="flex items-start gap-4 border-b pb-4 last:border-0">
                    <div className="rounded-full bg-blue-100 p-2">
                      <Shield className="h-4 w-4 text-blue-600" />
                    </div>
                    <div>
                      <p className="font-medium">{activity.type}</p>
                      <p className="text-sm text-muted-foreground">{activity.details}</p>
                      <p className="text-xs text-muted-foreground mt-1">
                        {formatDate(activity.timestamp)}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                <Calendar className="mx-auto h-12 w-12 mb-4 opacity-50" />
                <p>No recent activity found.</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* User profile information */}
      <Card>
        <CardHeader>
          <CardTitle>Profile Information</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-6 md:grid-cols-2">
            <div className="space-y-1">
              <p className="text-sm font-medium text-muted-foreground">Full Name</p>
              <p>{userData.name || "Not provided"}</p>
            </div>
            <div className="space-y-1">
              <p className="text-sm font-medium text-muted-foreground">Email</p>
              <p>{userData.email}</p>
            </div>
            <div className="space-y-1">
              <p className="text-sm font-medium text-muted-foreground">Email Verification</p>
              <p>{userData.emailVerified ? `Verified on ${formatDate(userData.emailVerified)}` : "Not verified"}</p>
            </div>
            <div className="space-y-1">
              <p className="text-sm font-medium text-muted-foreground">Account Created</p>
              <p>{formatDate(userData.createdAt)}</p>
            </div>
            <div className="space-y-1">
              <p className="text-sm font-medium text-muted-foreground">Last Updated</p>
              <p>{formatDate(userData.updatedAt)}</p>
            </div>
            <div className="space-y-1">
              <p className="text-sm font-medium text-muted-foreground">Last Active</p>
              <p>{formatDate(userData.lastActive)}</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
