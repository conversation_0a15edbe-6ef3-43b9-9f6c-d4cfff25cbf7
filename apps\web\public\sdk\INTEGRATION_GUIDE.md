# Swiss Knowledge Hub Chatbot SDK - Integration Guide

This guide provides detailed instructions for integrating the Swiss Knowledge Hub Chatbot SDK into various environments and frameworks.

## Table of Contents

1. [Getting Started](#getting-started)
2. [Configuration](#configuration)
3. [Framework-Specific Integration](#framework-specific-integration)
4. [Advanced Customization](#advanced-customization)
5. [Security Best Practices](#security-best-practices)
6. [Performance Optimization](#performance-optimization)
7. [Testing](#testing)

## Getting Started

### Prerequisites

- A Swiss Knowledge Hub account
- A created chatbot with an API key
- A website or web application to integrate with

### Step 1: Obtain Your Credentials

1. Log into your Swiss Knowledge Hub admin panel
2. Navigate to Settings > Chatbots
3. Select your chatbot or create a new one
4. Go to the "API Keys" tab
5. Generate an API key if you haven't already
6. Note your Chatbot ID and API Key

### Step 2: Basic Integration

Add the SDK script to your HTML:

```html
<!DOCTYPE html>
<html>
<head>
    <title>My Website</title>
</head>
<body>
    <!-- Your website content -->
    
    <!-- Swiss Knowledge Hub Chatbot SDK -->
    <script src="https://your-domain.com/sdk/chatbot-sdk.min.js"></script>
    <script>
        SwissKnowledgeHub.init({
            chatbotId: 'your-chatbot-id',
            apiKey: 'your-api-key'
        });
    </script>
</body>
</html>
```

## Configuration

### Complete Configuration Options

```javascript
SwissKnowledgeHub.init({
    // Required Configuration
    chatbotId: 'your-chatbot-id',
    apiKey: 'your-api-key',
    
    // Display Options
    position: 'bottom-right', // 'bottom-right', 'bottom-left', 'top-right', 'top-left'
    size: 'medium', // 'small', 'medium', 'large'
    mode: 'widget', // 'widget' or 'inline'
    container: '#chatbot-container', // Required for inline mode
    
    // Behavior Options
    autoOpen: false, // Auto-open chat on page load
    greeting: 'Hello! How can I help you today?',
    placeholder: 'Type your message...',
    showBranding: true,
    
    // Theme Customization
    theme: {
        primaryColor: '#007bff',
        secondaryColor: '#6c757d',
        fontFamily: 'Inter, sans-serif',
        borderRadius: '8px'
    },
    
    // Development Options
    debug: false, // Enable debug logging
    apiBase: 'https://your-domain.com' // Override API base URL
});
```

### Size Configurations

```javascript
// Small chatbot (mobile-optimized)
size: 'small' // 300px width, 400px height

// Medium chatbot (default)
size: 'medium' // 350px width, 500px height

// Large chatbot (desktop-optimized)
size: 'large' // 400px width, 600px height
```

## Framework-Specific Integration

### React Integration

#### Functional Component with Hooks

```jsx
import React, { useEffect, useRef } from 'react';

const ChatbotWidget = ({ chatbotId, apiKey, ...config }) => {
    const instanceRef = useRef(null);

    useEffect(() => {
        const loadChatbot = async () => {
            // Load SDK script
            if (!window.SwissKnowledgeHub) {
                const script = document.createElement('script');
                script.src = 'https://your-domain.com/sdk/chatbot-sdk.min.js';
                script.async = true;
                
                return new Promise((resolve, reject) => {
                    script.onload = resolve;
                    script.onerror = reject;
                    document.head.appendChild(script);
                });
            }
        };

        loadChatbot().then(() => {
            // Initialize chatbot
            instanceRef.current = window.SwissKnowledgeHub.init({
                chatbotId,
                apiKey,
                ...config
            });
        }).catch(error => {
            console.error('Failed to load chatbot:', error);
        });

        // Cleanup
        return () => {
            if (instanceRef.current) {
                window.SwissKnowledgeHub.destroy(instanceRef.current.instanceId);
            }
        };
    }, [chatbotId, apiKey]);

    return null;
};

export default ChatbotWidget;
```

#### Usage in React App

```jsx
import ChatbotWidget from './components/ChatbotWidget';

function App() {
    return (
        <div className="App">
            <h1>My React App</h1>
            
            <ChatbotWidget
                chatbotId="your-chatbot-id"
                apiKey="your-api-key"
                position="bottom-right"
                theme={{
                    primaryColor: '#007bff'
                }}
            />
        </div>
    );
}
```

### Vue.js Integration

#### Vue 3 Composition API

```vue
<template>
    <div></div>
</template>

<script setup>
import { onMounted, onUnmounted, ref } from 'vue';

const props = defineProps({
    chatbotId: String,
    apiKey: String,
    position: {
        type: String,
        default: 'bottom-right'
    }
});

const chatbotInstance = ref(null);

const loadChatbot = async () => {
    if (!window.SwissKnowledgeHub) {
        const script = document.createElement('script');
        script.src = 'https://your-domain.com/sdk/chatbot-sdk.min.js';
        
        return new Promise((resolve, reject) => {
            script.onload = resolve;
            script.onerror = reject;
            document.head.appendChild(script);
        });
    }
};

onMounted(async () => {
    try {
        await loadChatbot();
        chatbotInstance.value = window.SwissKnowledgeHub.init({
            chatbotId: props.chatbotId,
            apiKey: props.apiKey,
            position: props.position
        });
    } catch (error) {
        console.error('Failed to load chatbot:', error);
    }
});

onUnmounted(() => {
    if (chatbotInstance.value) {
        window.SwissKnowledgeHub.destroy(chatbotInstance.value.instanceId);
    }
});
</script>
```

### Angular Integration

#### Angular Service

```typescript
// chatbot.service.ts
import { Injectable } from '@angular/core';

interface ChatbotConfig {
    chatbotId: string;
    apiKey: string;
    position?: string;
    theme?: any;
}

@Injectable({
    providedIn: 'root'
})
export class ChatbotService {
    private scriptLoaded = false;
    private instances = new Map();

    async loadSDK(): Promise<void> {
        if (this.scriptLoaded) return;

        return new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = 'https://your-domain.com/sdk/chatbot-sdk.min.js';
            script.onload = () => {
                this.scriptLoaded = true;
                resolve();
            };
            script.onerror = reject;
            document.head.appendChild(script);
        });
    }

    async initChatbot(config: ChatbotConfig): Promise<string> {
        await this.loadSDK();
        
        const instance = (window as any).SwissKnowledgeHub.init(config);
        this.instances.set(instance.instanceId, instance);
        
        return instance.instanceId;
    }

    destroyChatbot(instanceId: string): void {
        if (this.instances.has(instanceId)) {
            (window as any).SwissKnowledgeHub.destroy(instanceId);
            this.instances.delete(instanceId);
        }
    }

    destroyAll(): void {
        (window as any).SwissKnowledgeHub?.destroyAll();
        this.instances.clear();
    }
}
```

#### Angular Component

```typescript
// chatbot.component.ts
import { Component, Input, OnInit, OnDestroy } from '@angular/core';
import { ChatbotService } from './chatbot.service';

@Component({
    selector: 'app-chatbot',
    template: ''
})
export class ChatbotComponent implements OnInit, OnDestroy {
    @Input() chatbotId!: string;
    @Input() apiKey!: string;
    @Input() position = 'bottom-right';

    private instanceId?: string;

    constructor(private chatbotService: ChatbotService) {}

    async ngOnInit() {
        try {
            this.instanceId = await this.chatbotService.initChatbot({
                chatbotId: this.chatbotId,
                apiKey: this.apiKey,
                position: this.position
            });
        } catch (error) {
            console.error('Failed to initialize chatbot:', error);
        }
    }

    ngOnDestroy() {
        if (this.instanceId) {
            this.chatbotService.destroyChatbot(this.instanceId);
        }
    }
}
```

## Advanced Customization

### Custom CSS Styling

Override default styles with CSS custom properties:

```css
:root {
    --skh-primary-color: #007bff;
    --skh-secondary-color: #6c757d;
    --skh-font-family: 'Inter', sans-serif;
    --skh-border-radius: 8px;
}

/* Custom chatbot button */
.skh-chat-button {
    background: linear-gradient(45deg, #007bff, #0056b3) !important;
    box-shadow: 0 4px 20px rgba(0, 123, 255, 0.3) !important;
}

/* Custom chat window */
.skh-chat-window {
    border: 2px solid #007bff !important;
}

/* Custom message styling */
.skh-message.user {
    background: linear-gradient(45deg, #007bff, #0056b3) !important;
}
```

### Event Handling

```javascript
// Initialize with event handlers
const chatbot = SwissKnowledgeHub.init({
    chatbotId: 'your-chatbot-id',
    apiKey: 'your-api-key',
    onOpen: () => console.log('Chat opened'),
    onClose: () => console.log('Chat closed'),
    onMessage: (message) => console.log('Message sent:', message),
    onResponse: (response) => console.log('Response received:', response),
    onError: (error) => console.error('Chat error:', error)
});
```

## Security Best Practices

### 1. API Key Management

```javascript
// ❌ Don't expose API keys in client-side code
const apiKey = 'skh_1234567890abcdef'; // Visible to users

// ✅ Use environment variables or server-side rendering
const apiKey = process.env.CHATBOT_API_KEY;
```

### 2. Domain Restrictions

Configure allowed domains in your chatbot settings:

```javascript
// In your chatbot configuration
allowedDomains: [
    'yourdomain.com',
    '*.yourdomain.com',
    'staging.yourdomain.com'
]
```

### 3. Content Security Policy

Add CSP headers to allow the chatbot SDK:

```html
<meta http-equiv="Content-Security-Policy" 
      content="script-src 'self' https://your-domain.com; 
               connect-src 'self' https://your-domain.com;">
```

## Performance Optimization

### 1. Lazy Loading

```javascript
// Load chatbot only when needed
function loadChatbotOnDemand() {
    const script = document.createElement('script');
    script.src = 'https://your-domain.com/sdk/chatbot-sdk.min.js';
    script.onload = () => {
        SwissKnowledgeHub.init({
            chatbotId: 'your-chatbot-id',
            apiKey: 'your-api-key'
        });
    };
    document.head.appendChild(script);
}

// Load on user interaction
document.getElementById('help-button').addEventListener('click', loadChatbotOnDemand);
```

### 2. Preloading

```html
<!-- Preload the SDK script -->
<link rel="preload" href="https://your-domain.com/sdk/chatbot-sdk.min.js" as="script">
```

## Testing

### Unit Testing

```javascript
// Jest test example
describe('Chatbot Integration', () => {
    beforeEach(() => {
        // Mock the global SwissKnowledgeHub object
        global.SwissKnowledgeHub = {
            init: jest.fn(),
            destroy: jest.fn(),
            destroyAll: jest.fn()
        };
    });

    test('should initialize chatbot with correct config', () => {
        const config = {
            chatbotId: 'test-id',
            apiKey: 'test-key'
        };

        SwissKnowledgeHub.init(config);
        
        expect(SwissKnowledgeHub.init).toHaveBeenCalledWith(config);
    });
});
```

### End-to-End Testing

```javascript
// Cypress test example
describe('Chatbot Widget', () => {
    it('should load and display chatbot', () => {
        cy.visit('/');
        cy.get('.skh-chat-button').should('be.visible');
        cy.get('.skh-chat-button').click();
        cy.get('.skh-chat-window').should('be.visible');
        cy.get('.skh-chat-input').type('Hello');
        cy.get('.skh-send-button').click();
        cy.get('.skh-message.user').should('contain', 'Hello');
    });
});
```

## Troubleshooting

### Common Issues and Solutions

1. **Script loading errors**
   - Check network connectivity
   - Verify script URL is correct
   - Check for CORS issues

2. **Authentication failures**
   - Verify API key is correct
   - Check domain restrictions
   - Ensure chatbot is active

3. **Styling conflicts**
   - Use CSS specificity
   - Check for conflicting styles
   - Use custom CSS properties

For more help, contact <NAME_EMAIL>
