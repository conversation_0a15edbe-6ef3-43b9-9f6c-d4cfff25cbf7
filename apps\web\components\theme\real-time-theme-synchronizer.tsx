"use client";

import { useEffect } from "react";
import { useTheme } from "next-themes";
import { ThemeConfig } from "@/types/theme-config";
import { generateThemeVariables } from "@/services/theme-config";

interface RealTimeThemeSynchronizerProps {
  children: React.ReactNode;
}

export function RealTimeThemeSynchronizer({ children }: RealTimeThemeSynchronizerProps) {
  const { setTheme } = useTheme();
  const isPartnerConsole = process.env.NEXT_PUBLIC_IS_PARTNER_CONSOLE === "1";

  useEffect(() => {
    // Only activate real-time synchronization if partner console is enabled
    if (!isPartnerConsole) {
      return;
    }

    let themeUpdateTimeout: NodeJS.Timeout;

    const handleRealtimeThemeUpdate = (event: CustomEvent) => {
      const themeData = event.detail;
      console.log("Real-time theme update received:", themeData);

      // Clear any pending theme updates to prevent conflicts
      if (themeUpdateTimeout) {
        clearTimeout(themeUpdateTimeout);
      }

      // Debounce theme updates to prevent rapid successive applications
      themeUpdateTimeout = setTimeout(() => {
        if (themeData?.themeConfig) {
          const themeConfig: ThemeConfig = themeData.themeConfig;

          // Apply theme mode (light/dark) if specified
          if (themeConfig.themePreset) {
            console.log("Applying theme preset:", themeConfig.themePreset);

            // Update localStorage
            localStorage.setItem("theme", themeConfig.themePreset);

            // Apply the theme using next-themes
            setTheme(themeConfig.themePreset);

            // Force apply the theme class to the HTML element
            const root = document.documentElement;
            root.classList.remove("light", "dark");
            root.classList.add(themeConfig.themePreset);
          }

          // Apply custom colors if specified
          applyCustomColors(themeConfig);

          // Apply branding if specified
          applyBranding(themeConfig);
        }
      }, 100); // 100ms debounce
    };

    // Listen for real-time theme updates
    window.addEventListener("realtimeThemeUpdate", handleRealtimeThemeUpdate as EventListener);

    return () => {
      window.removeEventListener("realtimeThemeUpdate", handleRealtimeThemeUpdate as EventListener);
      // Clear any pending theme updates on cleanup
      if (themeUpdateTimeout) {
        clearTimeout(themeUpdateTimeout);
      }
    };
  }, [setTheme, isPartnerConsole]);

  return <>{children}</>;
}

function applyCustomColors(themeConfig: ThemeConfig) {
  const root = document.documentElement;

  // Clean up any existing custom theme styles first
  const existingStyles = document.querySelectorAll('style[id^="realtime-theme-styles"]');
  existingStyles.forEach(style => style.remove());

  // Determine current theme mode from the HTML element or themeConfig
  const currentTheme = root.classList.contains('dark') ? 'dark' :
                      root.classList.contains('light') ? 'light' :
                      themeConfig.themePreset || 'light';

  // Generate theme variables using the current theme mode
  const themeVariables = generateThemeVariables(themeConfig, currentTheme as "light" | "dark");

  // Apply the CSS variables to the root element
  Object.entries(themeVariables).forEach(([property, value]) => {
    if (value) {
      root.style.setProperty(property, value);
    } else {
      // Remove the custom property to fall back to default
      root.style.removeProperty(property);
    }
  });

  console.log(`Applied ${currentTheme} theme colors via real-time synchronizer:`, themeVariables);
}

function applyBranding(themeConfig: ThemeConfig) {
  // Branding updates are now handled by DynamicFaviconManager
  // This function is kept for backward compatibility but delegates to the event system

  // Dispatch a custom event for the DynamicFaviconManager to handle
  const brandingUpdateEvent = new CustomEvent('brandingUpdate', {
    detail: { themeConfig }
  });
  window.dispatchEvent(brandingUpdateEvent);

  console.log('Real-time theme synchronizer: Branding update dispatched', themeConfig);
}
