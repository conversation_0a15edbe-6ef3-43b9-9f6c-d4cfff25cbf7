import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/next-auth";
import db from "@/lib/shared-db";

export async function GET(request: Request) {
  const session = await getServerSession(authOptions);

  if (!session) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const { searchParams } = new URL(request.url);
    const fromDate = searchParams.get("from")
      ? new Date(searchParams.get("from") as string)
      : new Date(new Date().setDate(new Date().getDate() - 30));
    const toDate = searchParams.get("to")
      ? new Date(searchParams.get("to") as string)
      : new Date();

    // Get total active tenants
    const totalActiveTenants = await db.tenant.count({
      where: {
        Subscription: {
          some: {
            isActive: true,
          },
        },
        createdAt: {
          lte: toDate,
        },
      },
    });

    // Get total tenants
    const totalTenants = await db.tenant.count({
      where: {
        createdAt: {
          lte: toDate,
        },
      },
    });

    // Get new tenants in the selected date range
    const newTenants = await db.tenant.count({
      where: {
        createdAt: {
          gte: fromDate,
          lte: toDate,
        },
      },
    });

    // Calculate growth rate
    const growthRate = totalTenants > 0 ? (newTenants / totalTenants) * 100 : 0;

    return NextResponse.json({
      totalActiveTenants,
      totalTenants,
      newTenants,
      growthRate: Math.round(growthRate * 100) / 100, // Round to 2 decimal places
    });
  } catch (error) {
    console.error("Error fetching tenant metrics:", error);
    return NextResponse.json(
      { error: "Failed to fetch tenant metrics" },
      { status: 500 }
    );
  }
}
