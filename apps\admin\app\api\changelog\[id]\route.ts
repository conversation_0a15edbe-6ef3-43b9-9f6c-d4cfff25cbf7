import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/next-auth";
import db from "@/lib/shared-db";

interface RouteParams {
  params: {
    id: string;
  };
}

export async function PUT(request: NextRequest, { params }: RouteParams) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    const { id } = params;

    // Validate ID format (MongoDB ObjectId)
    if (!id || id.length !== 24) {
      return NextResponse.json(
        { error: "Invalid changelog ID" },
        { status: 400 }
      );
    }

    // Parse request body
    const body = await request.json();
    const {
      title,
      content,
      version,
      type,
      priority,
      targetTenants,
      targetEnvironment,
      publishedAt,
      expiresAt,
      githubCommitSha,
      deploymentId,
      isActive,
    } = body;

    // Validate required fields
    if (!title || !content) {
      return NextResponse.json(
        { error: "Title and content are required" },
        { status: 400 }
      );
    }

    // Validate type
    const validTypes = ["RELEASE", "HOTFIX", "MAINTENANCE", "ANNOUNCEMENT"];
    if (type && !validTypes.includes(type)) {
      return NextResponse.json(
        { error: "Invalid type" },
        { status: 400 }
      );
    }

    // Validate priority
    const validPriorities = ["LOW", "MEDIUM", "HIGH", "CRITICAL"];
    if (priority && !validPriorities.includes(priority)) {
      return NextResponse.json(
        { error: "Invalid priority" },
        { status: 400 }
      );
    }

    // Get database connection
    

    // Check if changelog exists
    const existingChangelog = await db.changelog.findUnique({
      where: { id },
    });

    if (!existingChangelog) {
      return NextResponse.json(
        { error: "Changelog not found" },
        { status: 404 }
      );
    }

    // Update changelog entry
    const updatedChangelog = await db.changelog.update({
      where: { id },
      data: {
        title: title.trim(),
        content: content.trim(),
        version: version?.trim() || null,
        type: type || existingChangelog.type,
        priority: priority || existingChangelog.priority,
        targetTenants: Array.isArray(targetTenants) ? targetTenants : existingChangelog.targetTenants,
        targetEnvironment: targetEnvironment?.trim() || null,
        publishedAt: publishedAt ? new Date(publishedAt) : existingChangelog.publishedAt,
        expiresAt: expiresAt ? new Date(expiresAt) : null,
        githubCommitSha: githubCommitSha?.trim() || null,
        deploymentId: deploymentId?.trim() || null,
        isActive: typeof isActive === 'boolean' ? isActive : existingChangelog.isActive,
        updatedAt: new Date(),
      },
    });

    return NextResponse.json(
      {
        message: "Changelog updated successfully",
        changelog: {
          id: updatedChangelog.id,
          title: updatedChangelog.title,
          type: updatedChangelog.type,
          priority: updatedChangelog.priority,
          updatedAt: updatedChangelog.updatedAt,
        },
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error updating changelog:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    const { id } = params;

    // Validate ID format (MongoDB ObjectId)
    if (!id || id.length !== 24) {
      return NextResponse.json(
        { error: "Invalid changelog ID" },
        { status: 400 }
      );
    }

    // Get database connection
    

    // Check if changelog exists
    const existingChangelog = await db.changelog.findUnique({
      where: { id },
    });

    if (!existingChangelog) {
      return NextResponse.json(
        { error: "Changelog not found" },
        { status: 404 }
      );
    }

    // Delete changelog entry
    await db.changelog.delete({
      where: { id },
    });

    return NextResponse.json(
      {
        message: "Changelog deleted successfully",
        id,
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error deleting changelog:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    const { id } = params;

    // Validate ID format (MongoDB ObjectId)
    if (!id || id.length !== 24) {
      return NextResponse.json(
        { error: "Invalid changelog ID" },
        { status: 400 }
      );
    }

    // Get database connection
    

    // Fetch changelog entry
    const changelog = await db.changelog.findUnique({
      where: { id },
    });

    if (!changelog) {
      return NextResponse.json(
        { error: "Changelog not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({
      changelog,
    });
  } catch (error) {
    console.error("Error fetching changelog:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
