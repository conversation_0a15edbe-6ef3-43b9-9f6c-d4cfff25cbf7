import { apiUrl, fetchJson } from "..";

export interface SharedThread {
  id: string;
  chatId: string;
  shareToken: string;
  isPublic: boolean;
  expiresAt?: string;
  createdAt: string;
  updatedAt: string;
  unreadCount: number;
  hasNewActivity: boolean;
  lastActivityAt: string;
  totalComments: number;
  userMentions: number;
  chat: {
    id: string;
    title?: string;
    description?: string;
    createdAt: string;
    updatedAt: string;
    user: {
      id: string;
      name: string;
      email: string;
      image?: string;
    };
    messages: Array<{
      id: string;
      content: string;
      createdAt: string;
      user: {
        id: string;
        name: string;
        email: string;
      };
    }>;
    _count: {
      messages: number;
    };
  };
  createdBy: {
    id: string;
    name: string;
    email: string;
    image?: string;
  };
  tenant: {
    id: string;
    name: string;
    slug: string;
  };
}

export interface SharedThreadsResponse {
  privateThreads: SharedThread[];
  publicThreads: SharedThread[];
  totalCount: number;
  hasMore: boolean;
}

export interface UnreadCountResponse {
  privateUnreadCount: number;
  publicUnreadCount: number;
  totalUnreadCount: number;
  totalPrivateThreads: number;
  totalPublicThreads: number;
  totalThreads: number;
}

export const getSharedThreads = async ({
  search = "",
  type,
  limit = 50,
  offset = 0,
}: {
  search?: string;
  type?: "private" | "public";
  limit?: number;
  offset?: number;
} = {}): Promise<SharedThreadsResponse> => {
  try {
    const params = new URLSearchParams();
    if (search) params.append("search", search);
    if (type) params.append("type", type);
    params.append("limit", limit.toString());
    params.append("offset", offset.toString());

    const response = await fetchJson(
      `${apiUrl}/shared-threads?${params.toString()}`
    );
    return response;
  } catch (error) {
    console.error("Error in getSharedThreads service:", error);
    throw error;
  }
};

export const getSharedThreadsUnreadCount =
  async (): Promise<UnreadCountResponse> => {
    try {
      const response = await fetchJson(`${apiUrl}/shared-threads/unread-count`);
      return response;
    } catch (error) {
      console.error("Error in getSharedThreadsUnreadCount service:", error);
      throw error;
    }
  };

export const markSharedThreadAsRead = async (chatId: string): Promise<void> => {
  try {
    await fetch(`${apiUrl}/notifications`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        chatId,
      }),
    });
  } catch (error) {
    console.error("Error in markSharedThreadAsRead service:", error);
    throw error;
  }
};
