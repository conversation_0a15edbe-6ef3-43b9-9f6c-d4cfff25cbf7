/* eslint-disable no-unused-vars */
import React from "react";
import { Message, Source } from "../types";
import { useLanguage } from "@/lib/language-context";
import { MessageCarousel } from "./MessageCarousel";
import { MessageFeedback } from "./MessageFeedback";
import { MessageCitations } from "./MessageCitations";
import { ImagePreview } from "./ImagePreview";
import { MessageEdit } from "./MessageEdit";
import { UnifiedEditCarousel } from "./UnifiedEditCarousel";
import { MessageContent } from "./MessageContent";
import { DeepResearchProgress } from "./DeepResearchProgress";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Copy,
  MessageCircle,
  Edit,
  Brain,
  ChevronDown,
  ChevronUp,
  Zap,
} from "lucide-react";
import { toast } from "react-hot-toast";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import {
  findAssistantMessageWithEditTriggeredResponse,
  getUnifiedCarouselVersionCount,
  getCurrentConversationThreadIndex,
} from "../utils/edit-utils";

interface ChatMessageProps {
  message: Message;
  index: number;
  processedMessages: Message[];
  displayIndicesRef: React.MutableRefObject<Record<string, number>>;
  isLoading: boolean;
  status: "idle" | "streaming" | "submitted";
  updateDisplayIndex: (
    messageId: string,
    newIndex: number,
    isManuallySet: boolean,
  ) => void;
  isManuallySetIndex: (messageId: string) => boolean;
  handleFeedback: (messageIndex: number, feedback: "like" | "dislike") => void;
  handleRegenerate: (messageIndex?: number) => void;
  toggleCitationAccordion: (messageId: string) => void;
  setSelectedSource: (source: Source) => void;
  setIsCitationModalOpen: (isOpen: boolean) => void;
  onCommentClick?: () => void; // Optional comment handler
  isSharedView?: boolean; // Whether this is in a shared thread view
  // Edit functionality props
  startEdit?: (messageIndex: number, displayedContent?: string) => void;
  cancelEdit?: (messageIndex: number) => void;
  saveEdit?: (messageIndex: number) => void;
  updateEditContent?: (messageId: string, content: string) => void;
  canEdit?: (messageIndex: number, processedMessages: Message[]) => boolean;
  isMessageEditing?: (messageId: string) => boolean;
  getEditContent?: (messageId: string) => string;
  isEditLoading?: boolean;
}

// Utility function to strip markdown formatting from table cell content
const stripMarkdownFormatting = (text: string): string => {
  return (
    text
      // Remove bold formatting (**text** or __text__)
      .replace(/\*\*(.*?)\*\*/g, "$1")
      .replace(/__(.*?)__/g, "$1")
      // Remove italic formatting (*text* or _text_) - but be careful not to break other formatting
      .replace(/(?<!\*)\*([^*]+)\*(?!\*)/g, "$1")
      .replace(/(?<!_)_([^_]+)_(?!_)/g, "$1")
      // Remove inline code formatting (`text`)
      .replace(/`([^`]+)`/g, "$1")
      // Remove strikethrough formatting (~~text~~)
      .replace(/~~(.*?)~~/g, "$1")
  );
};

// Custom table parser for markdown tables
const parseMarkdownTable = (content: string) => {
  const lines = content.split("\n");
  const tables: Array<{
    startIndex: number;
    endIndex: number;
    headers: string[];
    rows: string[][];
    alignments: string[];
  }> = [];

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim();
    if (line.includes("|") && line.split("|").length > 2) {
      // Potential table start
      const nextLine = lines[i + 1]?.trim();
      if (nextLine && nextLine.includes("|") && nextLine.includes("-")) {
        // This is a table header with separator
        const headers = line
          .split("|")
          .map((h) => stripMarkdownFormatting(h.trim()))
          .filter((h) => h);
        const separatorCells = nextLine
          .split("|")
          .map((s) => s.trim())
          .filter((s) => s);
        const alignments = separatorCells.map((cell) => {
          if (cell.startsWith(":") && cell.endsWith(":")) return "center";
          if (cell.endsWith(":")) return "right";
          return "left";
        });

        const rows: string[][] = [];
        let j = i + 2;

        // Parse table rows
        while (j < lines.length && lines[j].trim().includes("|")) {
          const rowCells = lines[j]
            .split("|")
            .map((c) => stripMarkdownFormatting(c.trim()))
            .filter((c) => c);
          if (rowCells.length > 0) {
            rows.push(rowCells);
          }
          j++;
        }

        if (rows.length > 0) {
          tables.push({
            startIndex: i,
            endIndex: j - 1,
            headers,
            rows,
            alignments,
          });
        }

        i = j - 1; // Skip processed lines
      }
    }
  }

  return tables;
};

export const ChatMessage: React.FC<ChatMessageProps> = ({
  message,
  index,
  processedMessages,
  displayIndicesRef,
  isLoading,
  status,
  updateDisplayIndex,
  isManuallySetIndex,
  handleFeedback,
  handleRegenerate,
  toggleCitationAccordion,
  setSelectedSource,
  setIsCitationModalOpen,
  onCommentClick,
  isSharedView = false,
  // Edit functionality props
  startEdit,
  cancelEdit,
  saveEdit,
  updateEditContent,
  canEdit,
  isMessageEditing,
  getEditContent,
  isEditLoading = false,
}) => {
  const { t } = useLanguage();
  const [isThinkingExpanded, setIsThinkingExpanded] = React.useState(false);
  const isDeepAnswer = (message.metadata?.iterations?.length || 0) > 0;

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast.success(t("chat.copiedToClipboard") || "Copied to clipboard");
  };

  // Function to render content with tables and citations
  const renderContentWithTablesAndCitations = (content: string) => {
    const processedContent = content?.replace(/\\n/g, "\n") || "";
    const tables = parseMarkdownTable(processedContent);

    // Create citation click handler
    const handleCitationClick = (source: Source, _highlightedText?: string) => {
      setSelectedSource(source);
      setIsCitationModalOpen(true);
    };

    if (tables.length === 0) {
      // No tables found, use MessageContent for citation processing
      return (
        <MessageContent
          content={processedContent}
          sources={displayedMessage.sources || []}
          onCitationClick={handleCitationClick}
        />
      );
    }

    // Split content and render with tables and citations
    const lines = processedContent.split("\n");
    const elements: React.ReactNode[] = [];
    let lastIndex = 0;

    tables.forEach((table, tableIndex) => {
      // Add content before table with citation processing
      if (table.startIndex > lastIndex) {
        const beforeTableContent = lines
          .slice(lastIndex, table.startIndex)
          .join("\n");
        if (beforeTableContent.trim()) {
          elements.push(
            <MessageContent
              key={`before-${tableIndex}`}
              content={beforeTableContent}
              sources={displayedMessage.sources || []}
              onCitationClick={handleCitationClick}
            />,
          );
        }
      }

      // Add table
      elements.push(
        <div key={`table-${tableIndex}`} className="overflow-x-auto max-w-full">
          <Table className="min-w-full">
            <TableHeader>
              <TableRow>
                {table.headers.map((header, i) => (
                  <TableHead
                    key={i}
                    style={{ textAlign: table.alignments[i] as any }}
                    className="whitespace-nowrap"
                  >
                    {header}
                  </TableHead>
                ))}
              </TableRow>
            </TableHeader>
            <TableBody>
              {table.rows.map((row, rowIndex) => (
                <TableRow key={rowIndex}>
                  {row.map((cell, cellIndex) => (
                    <TableCell
                      key={cellIndex}
                      style={{ textAlign: table.alignments[cellIndex] as any }}
                      className="whitespace-nowrap"
                    >
                      {cell}
                    </TableCell>
                  ))}
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>,
      );

      lastIndex = table.endIndex + 1;
    });

    // Add remaining content after last table with citation processing
    if (lastIndex < lines.length) {
      const afterTableContent = lines.slice(lastIndex).join("\n");
      if (afterTableContent.trim()) {
        elements.push(
          <MessageContent
            key="after-last"
            content={afterTableContent}
            sources={displayedMessage.sources || []}
            onCitationClick={handleCitationClick}
          />,
        );
      }
    }

    return <>{elements}</>;
  };

  const hasRegeneratedVersions =
    message.regeneratedMessages && message.regeneratedMessages.length > 0;
  const hasEditedVersions =
    message.editedMessages && message.editedMessages.length > 0;

  let displayedMessage = message;
  let isRegeneratedVersion = false;
  let isEditedVersion = false;
  let versionLabel = "";

  let displayIndex = message.id
    ? displayIndicesRef.current[message.id] || 0
    : 0;

  // Check if the message is currently being edited
  const isCurrentlyEditing =
    message.id && isMessageEditing ? isMessageEditing(message.id) : false;

  // Check if there's a streaming message that should be prioritized (regenerated or edited)
  if (hasRegeneratedVersions && message.regeneratedMessages) {
    const streamingMessageIndex = message.regeneratedMessages.findIndex(
      (regMsg) => regMsg.metadata?.isStreaming,
    );

    // If there's a streaming regenerated message, always show it (unless user manually navigated away)
    if (streamingMessageIndex !== -1) {
      const streamingDisplayIndex =
        streamingMessageIndex + 1 + (message.editedMessages?.length || 0); // +1 because index 0 is the original message, plus edited messages

      // Check if the user has manually set a different display index for this message
      const isManuallySet = message.id ? isManuallySetIndex(message.id) : false;

      // Always show the streaming message unless the user has manually navigated to a different version
      // This ensures that when regeneration starts, the user immediately sees the new streaming response
      if (!isManuallySet) {
        displayIndex = streamingDisplayIndex;
      }
    }
  }

  // Check if there's a streaming edited message that should be prioritized
  if (hasEditedVersions && message.editedMessages) {
    const streamingEditedIndex = message.editedMessages.findIndex(
      (editMsg) => editMsg.metadata?.isStreaming,
    );

    // If there's a streaming edited message, always show it (unless user manually navigated away)
    if (streamingEditedIndex !== -1) {
      const streamingDisplayIndex = streamingEditedIndex + 1; // +1 because index 0 is the original message

      // Check if the user has manually set a different display index for this message
      const isManuallySet = message.id ? isManuallySetIndex(message.id) : false;

      // Always show the streaming edited message unless the user has manually navigated to a different version
      // This ensures that when edit-triggered AI response starts, the user immediately sees the new streaming response
      if (!isManuallySet) {
        displayIndex = streamingDisplayIndex;
      }
    }
  }

  // Determine which message to display based on display index
  // Index 0 = original, 1-N = edited versions, N+1-M = regenerated versions
  const editedCount = message.editedMessages?.length || 0;
  const regeneratedCount = message.regeneratedMessages?.length || 0;

  if (displayIndex > 0) {
    if (displayIndex <= editedCount && message.editedMessages) {
      // Show edited version
      displayedMessage = message.editedMessages[displayIndex - 1];
      isEditedVersion = true;
      versionLabel = `${t(
        "chat.editedMessage",
      )} ${displayIndex}/${editedCount}`;
    } else if (displayIndex > editedCount && message.regeneratedMessages) {
      // Show regenerated version
      const regeneratedIndex = displayIndex - editedCount - 1;
      if (regeneratedIndex < regeneratedCount) {
        displayedMessage = message.regeneratedMessages[regeneratedIndex];
        isRegeneratedVersion = true;

        // Get the generation level of the displayed message
        const generationLevel = displayedMessage.metadata?.generationLevel || 1;

        // Create a more descriptive label based on generation level
        if (generationLevel === 1) {
          versionLabel = `${t("chat.regeneratedResponse")} ${
            regeneratedIndex + 1
          }/${regeneratedCount}`;
        } else {
          versionLabel = `${t(
            "chat.regeneratedResponse",
          )} (Gen ${generationLevel}) ${
            regeneratedIndex + 1
          }/${regeneratedCount}`;
        }
      }
    }
  } else if (hasEditedVersions || hasRegeneratedVersions) {
    // Show the original message with version info
    const totalVersions = 1 + editedCount + regeneratedCount;
    versionLabel = `${t("chat.originalMessage")} (1/${totalVersions})`;
  }

  // Calculate total versions for the carousel indicator
  const totalVersions = 1 + editedCount + regeneratedCount;

  // Calculate total versions for regeneration carousel (only regenerated messages)
  const regenerationTotalVersions = 1 + regeneratedCount;

  // Current version index is simply our display index
  const currentVersionIndex = displayIndex;

  if (displayedMessage.role === "user") {
    return (
      <div
        className={`px-4 text-xs sm:text-sm self-end ${
          isCurrentlyEditing
            ? "w-full"
            : "flex flex-col justify-end w-full group"
        }`}
      >
        {/* Version label for user messages */}
        {(hasRegeneratedVersions || hasEditedVersions) && (
          <div
            className={`text-xs mb-1 font-medium flex items-center gap-1 justify-end ${
              isRegeneratedVersion
                ? "text-green-600"
                : isEditedVersion
                  ? "text-blue-600"
                  : "text-amber-600"
            }`}
          >
            <span>{versionLabel}</span>
          </div>
        )}
        {displayedMessage.images && displayedMessage.images.length > 0 && (
          <div className="my-1 flex justify-end">
            <ImagePreview images={displayedMessage.images} />
          </div>
        )}

        {/* Message content container */}
        <div
          className={`bg-secondary rounded-lg relative overflow-hidden ${
            !isCurrentlyEditing && "w-fit self-end max-w-[75vw] sm:max-w-[85%]"
          }`}
        >
          <div className="relative overflow-hidden">
            {/* Show edit interface if currently editing */}
            {isCurrentlyEditing &&
            message.id &&
            updateEditContent &&
            getEditContent &&
            saveEdit &&
            cancelEdit ? (
              <MessageEdit
                content={getEditContent(message.id) ?? displayedMessage.content}
                isLoading={isEditLoading}
                onSave={() => saveEdit(index)}
                onCancel={() => cancelEdit(index)}
                onContentChange={(content) =>
                  updateEditContent(message.id!, content)
                }
              />
            ) : (
              <div className="px-3 py-1 overflow-hidden">
                <div className="overflow-x-auto">
                  {renderContentWithTablesAndCitations(
                    displayedMessage?.content || "",
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
        <div className="flex items-center justify-end w-full">
          {status === "idle" &&
            !displayedMessage.metadata?.isStreaming &&
            !message.metadata?.isStreaming && (
              <div
                className={`flex items-center ${
                  isCurrentlyEditing
                    ? "opacity-0"
                    : "opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                }`}
              >
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-7 w-7 rounded-full opacity-70 hover:opacity-100 transition-opacity"
                  onClick={() => copyToClipboard(displayedMessage.content)}
                  title={t("chat.copyMessage") || "Copy message"}
                >
                  <Copy className="h-3.5 w-3.5" />
                </Button>

                {/* Edit button - only show for user messages that can be edited */}
                {message.role === "user" &&
                  !isDeepAnswer &&
                  canEdit &&
                  canEdit(index, processedMessages) &&
                  startEdit && (
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-7 w-7 rounded-full opacity-70 hover:opacity-100 transition-opacity"
                      onClick={() => startEdit(index, displayedMessage.content)}
                      title={t("chat.editMessage")}
                    >
                      <Edit className="h-3.5 w-3.5" />
                    </Button>
                  )}
                {/* Show unified carousel if this user message has corresponding AI responses */}
              </div>
            )}
          {!isSharedView &&
            hasEditedVersions &&
            totalVersions > 1 &&
            status === "idle" &&
            !displayedMessage.metadata?.isStreaming &&
            !message.metadata?.isStreaming && (
              <div
                className={`flex items-center gap-2 ${
                  isCurrentlyEditing
                    ? "opacity-0"
                    : "opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                }`}
              >
                <div className="mr-auto">
                  {/* Check if there's a corresponding assistant message for unified navigation */}
                  {(() => {
                    const correspondingAssistant =
                      findAssistantMessageWithEditTriggeredResponse(
                        processedMessages,
                        index,
                      );
                    if (correspondingAssistant) {
                      // Calculate unified carousel data
                      const totalThreads = getUnifiedCarouselVersionCount(
                        message,
                        correspondingAssistant,
                      );
                      const userDisplayIndex = message.id
                        ? displayIndicesRef.current[message.id] || 0
                        : 0;
                      const assistantDisplayIndex = correspondingAssistant.id
                        ? displayIndicesRef.current[
                            correspondingAssistant.id
                          ] || 0
                        : 0;
                      const currentThreadIndex =
                        getCurrentConversationThreadIndex(
                          message,
                          correspondingAssistant,
                          userDisplayIndex,
                          assistantDisplayIndex,
                        );

                      return (
                        <UnifiedEditCarousel
                          userMessage={message}
                          assistantMessage={correspondingAssistant}
                          currentThreadIndex={currentThreadIndex}
                          totalThreads={totalThreads}
                          updateDisplayIndex={updateDisplayIndex}
                        />
                      );
                    } else {
                      // No carousel for user messages without corresponding assistant message
                      // Edited user messages without AI responses don't need a carousel
                      return null;
                    }
                  })()}
                </div>
              </div>
            )}
        </div>
      </div>
    );
  }

  // For assistant messages, show the carousel in the same position
  return (
    <div
      data-role={message?.role}
      className={`w-auto px-4 text-xs sm:text-sm data-[role=assistant]:self-start max-w-[75vw] sm:max-w-[85%] overflow-hidden`}
    >
      {/* Version label */}
      {(hasRegeneratedVersions || hasEditedVersions) && (
        <div
          className={`text-xs mb-1 font-medium flex items-center gap-1 ${
            isRegeneratedVersion
              ? "text-green-600"
              : isEditedVersion
                ? "text-blue-600"
                : "text-amber-600"
          }`}
        >
          <span>{versionLabel}</span>
          {/* Streaming indicator - show when the displayed message is streaming */}
          {(displayedMessage.metadata?.isStreaming ||
            message.metadata?.isStreaming) && (
            <span className="ml-2 flex items-center text-blue-500 animate-pulse">
              <span className="mr-1">Streaming</span>
              <span className="inline-block h-2 w-2 rounded-full bg-blue-500 mr-0.5 animate-ping"></span>
              <span
                className="inline-block h-2 w-2 rounded-full bg-blue-500 mr-0.5 animate-ping"
                style={{ animationDelay: "0.2s" }}
              ></span>
              <span
                className="inline-block h-2 w-2 rounded-full bg-blue-500 animate-ping"
                style={{ animationDelay: "0.4s" }}
              ></span>
            </span>
          )}
        </div>
      )}

      {/* Message content and other components */}
      <div className="relative">
        {displayedMessage.images && displayedMessage.images.length > 0 && (
          <div className="my-3">
            <ImagePreview images={displayedMessage.images} />
          </div>
        )}

        {/* Deep Research Progress */}
        {displayedMessage.role === "assistant" &&
          displayedMessage.metadata?.researchProgress && (
            <DeepResearchProgress
              researchProgress={displayedMessage.metadata.researchProgress}
              researchSummary={displayedMessage.metadata.research_summary}
              iterations={displayedMessage.metadata.iterations}
              isComplete={false}
            />
          )}

        {/* Research Summary (when complete) */}
        {displayedMessage.role === "assistant" &&
          displayedMessage.metadata?.research_summary &&
          !displayedMessage.metadata?.researchProgress && (
            <DeepResearchProgress
              researchSummary={displayedMessage.metadata.research_summary}
              iterations={displayedMessage.metadata.iterations}
              isComplete={true}
            />
          )}

        {/* Thinking Process (for DeepSeek R1) */}
        {displayedMessage.role === "assistant" &&
          displayedMessage.metadata?.thinking && (
            <>
              {console.log(
                "ChatMessage - Displaying thinking process:",
                displayedMessage.metadata.thinking.substring(0, 100) + "..."
              )}
              <div className="mb-3">
                <Collapsible
                  open={isThinkingExpanded}
                  onOpenChange={setIsThinkingExpanded}
                >
                  <CollapsibleTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="mb-2 p-0 h-auto"
                    >
                      <Brain className="h-4 w-4 mr-2" />
                      <span className="text-sm text-muted-foreground">
                        Thinking Process
                      </span>
                      {isThinkingExpanded ? (
                        <ChevronUp className="h-4 w-4 ml-2" />
                      ) : (
                        <ChevronDown className="h-4 w-4 ml-2" />
                      )}
                    </Button>
                  </CollapsibleTrigger>
                  <CollapsibleContent className="space-y-2">
                    <div className="bg-muted/50 rounded-lg p-3 text-sm">
                      <pre className="whitespace-pre-wrap font-mono text-xs">
                        {displayedMessage.metadata.thinking}
                      </pre>
                    </div>
                  </CollapsibleContent>
                </Collapsible>
              </div>
            </>
          )}

        {/* Show edit interface if currently editing */}
        {isCurrentlyEditing &&
        message.id &&
        updateEditContent &&
        getEditContent &&
        saveEdit &&
        cancelEdit ? (
          <MessageEdit
            content={getEditContent(message.id) ?? ""}
            isLoading={isEditLoading}
            onSave={() => saveEdit(index)}
            onCancel={() => cancelEdit(index)}
            onContentChange={(content) =>
              updateEditContent(message.id!, content)
            }
          />
        ) : (
          <div className="overflow-x-auto">
            {renderContentWithTablesAndCitations(
              displayedMessage?.content || "",
            )}
          </div>
        )}

        {/* Document Citations */}
        {displayedMessage.role === "assistant" &&
          displayedMessage.sources &&
          displayedMessage.sources.length > 0 && (
            <MessageCitations
              displayedMessage={displayedMessage}
              index={index}
              toggleCitationAccordion={toggleCitationAccordion}
              setSelectedSource={setSelectedSource}
              setIsCitationModalOpen={setIsCitationModalOpen}
            />
          )}

        {/* Message controls container - only show when not streaming or submitted */}
        {status === "idle" &&
          !displayedMessage.metadata?.isStreaming &&
          !message.metadata?.isStreaming && (
            <div className="flex items-center gap-2 mt-2 justify-between">
              {displayedMessage.role === "assistant" &&
                !isCurrentlyEditing &&
                hasRegeneratedVersions &&
                regenerationTotalVersions > 1 && (
                  <div>
                    <MessageCarousel
                      message={message}
                      currentVersionIndex={
                        currentVersionIndex >= editedCount
                          ? currentVersionIndex - editedCount
                          : 0
                      }
                      totalVersions={regenerationTotalVersions}
                      updateDisplayIndex={(
                        messageId,
                        newIndex,
                        isManuallySet,
                      ) => {
                        // Adjust the index to account for edited messages when updating display
                        const adjustedIndex = newIndex + editedCount;
                        updateDisplayIndex(
                          messageId,
                          adjustedIndex,
                          isManuallySet,
                        );
                      }}
                    />
                  </div>
                )}
              {/* Right side - Copy, Comment, and Feedback buttons */}
              <div className="flex items-center gap-1">
                {/* Copy button */}
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-7 w-7 rounded-full"
                  onClick={() => copyToClipboard(displayedMessage.content)}
                  title={t("chat.copyMessage") || "Copy message"}
                >
                  <Copy className="h-3.5 w-3.5" />
                </Button>

                {/* Comment button */}
                {onCommentClick && (
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-7 w-7 rounded-full"
                    onClick={onCommentClick}
                    title="Add comment"
                  >
                    <MessageCircle className="h-3.5 w-3.5" />
                  </Button>
                )}

                {/* Feedback buttons - only show in regular chat, not shared view */}
                {!isSharedView && (
                  <MessageFeedback
                    displayedMessage={displayedMessage}
                    isDeepAnswer={isDeepAnswer}
                    index={index}
                    processedMessages={processedMessages}
                    handleFeedback={handleFeedback}
                    handleRegenerate={handleRegenerate}
                  />
                )}
              </div>
            </div>
          )}
      </div>
    </div>
  );
};
