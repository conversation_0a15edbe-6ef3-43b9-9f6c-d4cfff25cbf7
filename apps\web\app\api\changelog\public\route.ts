import { NextRequest, NextResponse } from "next/server";
import { authOptions } from "@/lib/next-auth";
import db from "@/lib/shared-db";
import { getServerSession } from "next-auth/next";

export async function GET(request: NextRequest) {
  try {
    const session: any = await getServerSession(authOptions);

    // Allow both authenticated and unauthenticated access for public changelog
    const { searchParams } = new URL(request.url);
    const environment = searchParams.get("environment") || process.env.NODE_ENV;
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");
    const skip = (page - 1) * limit;

    // Build where conditions for public changelogs
    const currentTime = new Date();
    const whereConditions: any = {
      isActive: true,
      publishedAt: { lte: currentTime },
    };

    // Environment filter
    const environmentFilter = [
      { targetEnvironment: environment },
      { targetEnvironment: null },
    ];

    // Expiration filter
    const expirationFilter = [
      { expiresAt: { gt: currentTime } },
      { expiresAt: null },
    ];

    // Combine filters
    whereConditions.AND = [{ OR: environmentFilter }, { OR: expirationFilter }];

    // Get total count for pagination
    const totalCount = await db.changelog.count({
      where: whereConditions,
    });

    // Fetch changelogs
    const changelogs = await db.changelog.findMany({
      where: whereConditions,
      orderBy: [{ priority: "desc" }, { publishedAt: "desc" }],
      skip,
      take: limit,
    });

    // If user is authenticated, also get their viewed status
    let viewedChangelogIds: string[] = [];
    if (session?.userId) {
      const userViews = await db.userChangelogView.findMany({
        where: { userId: (session as any).user.id },
        select: { changelogId: true },
      });
      viewedChangelogIds = userViews.map((view) => view.changelogId);
    }

    // Add viewed status to changelogs
    const changelogsWithViewStatus = changelogs.map((changelog) => ({
      ...changelog,
      isViewed: viewedChangelogIds.includes(changelog.id),
    }));

    return NextResponse.json({
      data: changelogsWithViewStatus,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
        hasNextPage: page < Math.ceil(totalCount / limit),
        hasPreviousPage: page > 1,
      },
    });
  } catch (error) {
    console.error("Error fetching public changelogs:", error);
    return NextResponse.json(
      { error: "Failed to fetch changelogs" },
      { status: 500 }
    );
  }
}
