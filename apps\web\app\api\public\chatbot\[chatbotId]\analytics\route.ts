import { NextRequest, NextResponse } from "next/server";
import db from "@/lib/shared-db";

interface AnalyticsResponse {
  chatbotId: string;
  totalSessions: number;
  totalMessages: number;
  averageResponseTime: number;
  averageSessionDuration: number;
  topDomains: Array<{ domain: string; count: number }>;
  dailyStats: Array<{
    date: string;
    sessions: number;
    messages: number;
  }>;
  monthlyUsage: number;
  usageCount: number;
  lastUsedAt: string | null;
}

export async function GET(
  req: NextRequest,
  { params }: { params: { chatbotId: string } }
) {
  try {
    const { chatbotId } = params;
    const { searchParams } = new URL(req.url);
    const days = parseInt(searchParams.get("days") || "30");
    const includeDetails = searchParams.get("details") === "true";

    // Get API key from Authorization header
    const authHeader = req.headers.get("authorization");
    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return NextResponse.json(
        { error: "Missing or invalid API key" },
        { status: 401 }
      );
    }

    const apiKey = authHeader.substring(7);

    // Validate chatbot and API key
    const chatbot = await db.chatbot.findUnique({
      where: { id: chatbotId },
    });

    if (!chatbot || chatbot.apiKey !== apiKey) {
      return NextResponse.json(
        { error: "Invalid chatbot or API key" },
        { status: 401 }
      );
    }

    // Calculate date range
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(endDate.getDate() - days);

    // Get basic analytics
    const totalSessions = await db.chatbotSession.count({
      where: {
        chatbotId,
        startedAt: {
          gte: startDate,
          lte: endDate,
        },
      },
    });

    const totalMessages = await db.chatbotMessage.count({
      where: {
        chatbotId,
        createdAt: {
          gte: startDate,
          lte: endDate,
        },
      },
    });

    // Get average response time
    const avgResponseTime = await db.chatbotSession.aggregate({
      where: {
        chatbotId,
        startedAt: {
          gte: startDate,
          lte: endDate,
        },
      },
      _avg: {
        averageResponseTime: true,
      },
    });

    // Get average session duration
    const sessions = await db.chatbotSession.findMany({
      where: {
        chatbotId,
        startedAt: {
          gte: startDate,
          lte: endDate,
        },
      },
      select: {
        startedAt: true,
        lastActivityAt: true,
      },
    });

    const averageSessionDuration = sessions.length > 0
      ? sessions.reduce((acc, session) => {
          const duration = session.lastActivityAt.getTime() - session.startedAt.getTime();
          return acc + duration;
        }, 0) / sessions.length / 1000 // Convert to seconds
      : 0;

    // Get top domains
    const domainStats = await db.chatbotSession.groupBy({
      by: ["domain"],
      where: {
        chatbotId,
        startedAt: {
          gte: startDate,
          lte: endDate,
        },
      },
      _count: {
        domain: true,
      },
      orderBy: {
        _count: {
          domain: "desc",
        },
      },
      take: 10,
    });

    const topDomains = domainStats.map(stat => ({
      domain: stat.domain,
      count: stat._count.domain,
    }));

    // Get daily stats if details requested
    let dailyStats: Array<{ date: string; sessions: number; messages: number }> = [];
    
    if (includeDetails) {
      const dailySessionStats = await db.chatbotSession.groupBy({
        by: ["startedAt"],
        where: {
          chatbotId,
          startedAt: {
            gte: startDate,
            lte: endDate,
          },
        },
        _count: {
          id: true,
        },
      });

      const dailyMessageStats = await db.chatbotMessage.groupBy({
        by: ["createdAt"],
        where: {
          chatbotId,
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
        },
        _count: {
          id: true,
        },
      });

      // Process daily stats (simplified - you might want to group by actual date)
      const dateMap = new Map<string, { sessions: number; messages: number }>();
      
      // Initialize all dates in range
      for (let d = new Date(startDate); d <= endDate; d.setDate(d.getDate() + 1)) {
        const dateStr = d.toISOString().split('T')[0];
        dateMap.set(dateStr, { sessions: 0, messages: 0 });
      }

      // Add session counts
      dailySessionStats.forEach(stat => {
        const dateStr = stat.startedAt.toISOString().split('T')[0];
        if (dateMap.has(dateStr)) {
          dateMap.get(dateStr)!.sessions += stat._count.id;
        }
      });

      // Add message counts
      dailyMessageStats.forEach(stat => {
        const dateStr = stat.createdAt.toISOString().split('T')[0];
        if (dateMap.has(dateStr)) {
          dateMap.get(dateStr)!.messages += stat._count.id;
        }
      });

      dailyStats = Array.from(dateMap.entries()).map(([date, stats]) => ({
        date,
        sessions: stats.sessions,
        messages: stats.messages,
      }));
    }

    const analyticsResponse: AnalyticsResponse = {
      chatbotId,
      totalSessions,
      totalMessages,
      averageResponseTime: avgResponseTime._avg.averageResponseTime || 0,
      averageSessionDuration,
      topDomains,
      dailyStats,
      monthlyUsage: chatbot.monthlyUsage || 0,
      usageCount: chatbot.usageCount || 0,
      lastUsedAt: chatbot.lastUsedAt || null,
    };

    return NextResponse.json(analyticsResponse);

  } catch (error) {
    console.error("[ANALYTICS_GET]", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// POST endpoint to track custom events
export async function POST(
  req: NextRequest,
  { params }: { params: { chatbotId: string } }
) {
  try {
    const { chatbotId } = params;
    const { event, sessionId, metadata } = await req.json();

    // Get API key from Authorization header
    const authHeader = req.headers.get("authorization");
    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return NextResponse.json(
        { error: "Missing or invalid API key" },
        { status: 401 }
      );
    }

    const apiKey = authHeader.substring(7);

    // Validate chatbot and API key
    const chatbot = await db.chatbot.findUnique({
      where: { id: chatbotId },
    });

    if (!chatbot || chatbot.apiKey !== apiKey) {
      return NextResponse.json(
        { error: "Invalid chatbot or API key" },
        { status: 401 }
      );
    }

    // Validate required fields
    if (!event || !sessionId) {
      return NextResponse.json(
        { error: "Missing required fields: event, sessionId" },
        { status: 400 }
      );
    }

    // Find the session (with tenant validation)
    const session = await db.chatbotSession.findFirst({
      where: {
        sessionToken: sessionId,
        tenantId: chatbot.tenantId, // Ensure tenant isolation
      },
    });

    if (!session) {
      return NextResponse.json(
        { error: "Session not found" },
        { status: 404 }
      );
    }

    // Create custom event record (you might want to create a separate table for this)
    await db.chatbotMessage.create({
      data: {
        content: `Custom event: ${event}`,
        role: "system",
        sessionId: session.id,
        chatbotId,
        tenantId: chatbot.tenantId,
        metadata: {
          type: "custom_event",
          event,
          ...metadata,
        },
      },
    });

    return NextResponse.json({
      message: "Event tracked successfully",
      event,
      sessionId,
    });

  } catch (error) {
    console.error("[ANALYTICS_POST]", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// Handle CORS
export async function OPTIONS(req: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
      "Access-Control-Allow-Headers": "Content-Type, Authorization",
      "Access-Control-Max-Age": "86400",
    },
  });
}
