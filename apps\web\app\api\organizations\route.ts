import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import crypto from "crypto";
import db from "@/lib/shared-db";
import { authOptions } from "@/lib/next-auth/src/auth-options";

// Get organizations for the current user
export async function GET(request: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    const emailHash = crypto
      .createHash("sha256")
      .update(session.user.email)
      .digest("hex");
    const user = await db.user.findUnique({
      where: {
        emailHash,
      },
      include: {
        membership: {
          include: {
            tenant: true,
          },
        },
      },
    });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Format the organizations
    const organizations = user.membership.map((membership) => ({
      id: membership.tenant.id,
      name: membership.tenant.name,
      slug: membership.tenant.slug,
      role: membership.role,
      image: membership.tenant.image,
      description: membership.tenant.description,
      url: membership.tenant.url,
    }));

    return NextResponse.json(
      {
        organizations,
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error fetching organizations:", error);
    return NextResponse.json(
      { error: "Failed to fetch organizations" },
      { status: 500 }
    );
  }
}

// Create a new organization
export async function POST(request: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { name, slug, description, url, image } = await request.json();

    // Validate required fields
    if (!name || !slug) {
      return NextResponse.json(
        { error: "Name and slug are required" },
        { status: 400 }
      );
    }
    const emailHash = crypto
      .createHash("sha256")
      .update(session.user.email)
      .digest("hex");
    // Get the user id
    const user = await db.user.findUnique({
      where: {
        emailHash,
      },
    });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Create organization and membership in a transaction
    const result = await db.$transaction(async (tx) => {
      // Create the organization
      const newOrg = await tx.tenant.create({
        data: {
          name,
          slug: `${slug}-${Date.now()}`,
          description,
          url,
          image,
          isOnboarded: true,
        },
      });

      // Create membership with OWNER role
      const membership = await tx.membership.create({
        data: {
          userId: user.id,
          tenantId: newOrg.id,
          role: "OWNER",
        },
        include: {
          tenant: true,
        },
      });

      return {
        ...newOrg,
        role: membership.role,
      };
    });

    return NextResponse.json(
      {
        message: "Organization created successfully",
        data: result,
      },
      { status: 201 }
    );
  } catch (error) {
    console.error("Error creating organization:", error);
    return NextResponse.json(
      { error: "Failed to create organization" },
      { status: 500 }
    );
  }
}
