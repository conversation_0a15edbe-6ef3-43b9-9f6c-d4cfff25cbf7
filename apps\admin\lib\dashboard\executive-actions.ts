import db from "@/lib/shared-db";

export interface DashboardFilters {
  timeRange: "7d" | "30d" | "90d";
  planTier?: string;
  region?: string;
}

export interface ExecutiveDashboardData {
  kpiMetrics: {
    activeTenants: number;
    activeUsers: number;
    mrr: number;
    infrastructureCost: number;
    grossMargin: number;
    alertsCount: number;
    trends: {
      activeTenants: number;
      activeUsers: number;
      mrr: number;
      infrastructureCost: number;
      grossMargin: number;
    };
  };
  usageTrends: {
    activeTenantsData: Array<{ name: string; value: number }>;
    tokenUsageData: Array<{ name: string; [key: string]: any }>;
    apiVolumeData: Array<{ name: string; success: number; errors: number }>;
    storageGrowthData: Array<{ name: string; value: number }>;
  };
  financialData: {
    mrrVsCostData: Array<{ name: string; mrr: number; cost: number }>;
    tenantProfitabilityData: Array<{ 
      name: string; 
      profit: number; 
      plan: string; 
      margin: number 
    }>;
  };
  alerts: Array<{
    id: string;
    type: "usage_spike" | "quota_risk" | "system_error" | "cost_alert";
    severity: "red" | "orange" | "yellow" | "green";
    title: string;
    description: string;
    tenantId?: string;
    tenantName?: string;
    timestamp: string;
    acknowledged: boolean;
  }>;
}

export async function getExecutiveDashboardData(filters: DashboardFilters): Promise<ExecutiveDashboardData> {
  try {
    const { timeRange, planTier, region } = filters;

    // Parse time range
    const days = timeRange === "7d" ? 7 : timeRange === "30d" ? 30 : 90;
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);
    const endDate = new Date();

    // Calculate previous period for trends
    const previousStartDate = new Date(startDate);
    previousStartDate.setDate(previousStartDate.getDate() - days);

    // Base filters
    const tenantFilter: any = {};
    if (planTier) {
      tenantFilter.Subscription = {
        some: {
          plan: {
            name: {
              contains: planTier,
              mode: 'insensitive'
            }
          }
        }
      };
    }

    // 1. KPI Metrics Calculation
    
    // Active Tenants (tenants with usage in the period)
    const activeTenantsQuery = {
      where: {
        ...tenantFilter,
        OR: [
          {
            TokenUsage: {
              some: {
                timestamp: { gte: startDate, lte: endDate }
              }
            }
          },
          {
            APIRequest: {
              some: {
                timestamp: { gte: startDate, lte: endDate }
              }
            }
          }
        ]
      }
    };

    const [activeTenants, previousActiveTenants] = await Promise.all([
      db.tenant.count(activeTenantsQuery),
      db.tenant.count({
        where: {
          ...tenantFilter,
          OR: [
            {
              TokenUsage: {
                some: {
                  timestamp: { gte: previousStartDate, lt: startDate }
                }
              }
            },
            {
              APIRequest: {
                some: {
                  timestamp: { gte: previousStartDate, lt: startDate }
                }
              }
            }
          ]
        }
      })
    ]);

    // Active Users
    const activeUsersQuery = {
      where: {
        OR: [
          {
            TokenUsage: {
              some: {
                timestamp: { gte: startDate, lte: endDate }
              }
            }
          },
          {
            APIRequest: {
              some: {
                timestamp: { gte: startDate, lte: endDate }
              }
            }
          }
        ]
      }
    };

    const [activeUsers, previousActiveUsers] = await Promise.all([
      db.user.count(activeUsersQuery),
      db.user.count({
        where: {
          OR: [
            {
              TokenUsage: {
                some: {
                  timestamp: { gte: previousStartDate, lt: startDate }
                }
              }
            },
            {
              APIRequest: {
                some: {
                  timestamp: { gte: previousStartDate, lt: startDate }
                }
              }
            }
          ]
        }
      })
    ]);

    // MRR Calculation
    const subscriptions = await db.subscription.findMany({
      where: {
        isActive: true,
        ...(planTier && {
          plan: {
            name: {
              contains: planTier,
              mode: 'insensitive'
            }
          }
        })
      },
      include: {
        plan: true
      }
    });

    const mrr = subscriptions.reduce((sum, sub) => {
      const monthlyAmount = sub.billingInterval === 'year' 
        ? (sub.plan?.price || 0) / 12 
        : (sub.plan?.price || 0);
      return sum + monthlyAmount;
    }, 0);

    // Infrastructure Cost Calculation
    const tokenUsageCosts = await db.tokenUsage.aggregate({
      where: {
        timestamp: { gte: startDate, lte: endDate },
        ...(planTier && {
          tenant: {
            Subscription: {
              some: {
                plan: {
                  name: {
                    contains: planTier,
                    mode: 'insensitive'
                  }
                }
              }
            }
          }
        })
      },
      _sum: {
        cost: true
      }
    });

    const infrastructureCost = tokenUsageCosts._sum.cost || 0;

    // Gross Margin
    const grossMargin = mrr > 0 ? ((mrr - infrastructureCost) / mrr) * 100 : 0;

    // Alerts Count (simplified - you can expand this based on your alert logic)
    const alertsCount = await db.tokenUsage.count({
      where: {
        timestamp: { gte: new Date(Date.now() - 24 * 60 * 60 * 1000) }, // Last 24 hours
        cost: { gt: 100 } // High cost threshold
      }
    });

    // Calculate trends
    const activeTenantsTrend = previousActiveTenants > 0 
      ? ((activeTenants - previousActiveTenants) / previousActiveTenants) * 100 
      : 0;
    
    const activeUsersTrend = previousActiveUsers > 0 
      ? ((activeUsers - previousActiveUsers) / previousActiveUsers) * 100 
      : 0;

    // 2. Usage Trends Data
    
    // Generate date labels for the period
    const dateLabels: string[] = [];
    for (let i = days - 1; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      dateLabels.push(date.toISOString().split('T')[0]);
    }

    // Active Tenants Data (daily)
    const activeTenantsData = await Promise.all(
      dateLabels.map(async (date) => {
        const dayStart = new Date(date);
        const dayEnd = new Date(date);
        dayEnd.setDate(dayEnd.getDate() + 1);
        
        const count = await db.tenant.count({
          where: {
            ...tenantFilter,
            OR: [
              {
                TokenUsage: {
                  some: {
                    timestamp: { gte: dayStart, lt: dayEnd }
                  }
                }
              },
              {
                APIRequest: {
                  some: {
                    timestamp: { gte: dayStart, lt: dayEnd }
                  }
                }
              }
            ]
          }
        });
        
        return {
          name: new Date(date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
          value: count
        };
      })
    );

    // Token Usage Data (aggregated by day)
    const tokenUsageData = await Promise.all(
      dateLabels.map(async (date) => {
        const dayStart = new Date(date);
        const dayEnd = new Date(date);
        dayEnd.setDate(dayEnd.getDate() + 1);
        
        const usage = await db.tokenUsage.aggregate({
          where: {
            timestamp: { gte: dayStart, lt: dayEnd },
            ...(planTier && {
              tenant: {
                Subscription: {
                  some: {
                    plan: {
                      name: {
                        contains: planTier,
                        mode: 'insensitive'
                      }
                    }
                  }
                }
              }
            })
          },
          _sum: {
            inputTokens: true,
            outputTokens: true
          }
        });
        
        return {
          name: new Date(date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
          value: (usage._sum.inputTokens || 0) + (usage._sum.outputTokens || 0)
        };
      })
    );

    // API Volume Data
    const apiVolumeData = await Promise.all(
      dateLabels.map(async (date) => {
        const dayStart = new Date(date);
        const dayEnd = new Date(date);
        dayEnd.setDate(dayEnd.getDate() + 1);
        
        const [successCount, errorCount] = await Promise.all([
          db.aPIRequest.count({
            where: {
              timestamp: { gte: dayStart, lt: dayEnd },
              success: true
            }
          }),
          db.aPIRequest.count({
            where: {
              timestamp: { gte: dayStart, lt: dayEnd },
              success: false
            }
          })
        ]);
        
        return {
          name: new Date(date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
          success: successCount,
          errors: errorCount
        };
      })
    );

    // Storage Growth Data (simplified - replace with actual storage data)
    const storageGrowthData = dateLabels.map((date, index) => ({
      name: new Date(date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
      value: Math.random() * 100 + index * 5 // Placeholder - replace with actual storage data
    }));

    // 3. Financial Data
    
    // MRR vs Cost Trend
    const mrrVsCostData = dateLabels.slice(-7).map((date, index) => ({
      name: new Date(date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
      mrr: mrr + (Math.random() - 0.5) * mrr * 0.1, // Slight variation
      cost: infrastructureCost / 7 + (Math.random() - 0.5) * infrastructureCost * 0.2
    }));

    // Tenant Profitability Data (top 10 tenants)
    const tenantProfitabilityData = await db.tenant.findMany({
      take: 10,
      include: {
        Subscription: {
          include: { plan: true }
        },
        TokenUsage: {
          where: {
            timestamp: { gte: startDate, lte: endDate }
          }
        }
      }
    }).then(tenants => 
      tenants.map(tenant => {
        const revenue = tenant.Subscription.reduce((sum, sub) => 
          sum + (sub.plan?.price || 0), 0);
        const cost = tenant.TokenUsage.reduce((sum, usage) => 
          sum + usage.cost, 0);
        const profit = revenue - cost;
        const margin = revenue > 0 ? (profit / revenue) * 100 : 0;
        
        return {
          name: tenant.name.length > 15 ? tenant.name.substring(0, 15) + '...' : tenant.name,
          profit,
          plan: tenant.Subscription[0]?.plan?.name || 'No Plan',
          margin
        };
      }).sort((a, b) => b.profit - a.profit)
    );

    // 4. Real Database-Driven Alerts
    const alerts: any[] = [];

    try {
      // HIGH USAGE RANKING ALERTS - Top tenants by usage category

      // HIGHEST TOKEN USAGE - Time period aware
      const topTokenUsage = await db.tokenUsage.groupBy({
        by: ['tenantId'],
        where: {
          timestamp: { gte: startDate, lte: endDate },
          ...(planTier && {
            tenant: {
              Subscription: {
                some: {
                  plan: {
                    name: {
                      contains: planTier,
                      mode: 'insensitive'
                    }
                  }
                }
              }
            }
          })
        },
        _sum: {
          inputTokens: true,
          outputTokens: true,
          cost: true
        },
        orderBy: {
          _sum: {
            cost: 'desc'
          }
        },
        take: 1
      });

      if (topTokenUsage.length > 0) {
        const topTenant = topTokenUsage[0];
        const tenant = await db.tenant.findUnique({
          where: { id: topTenant.tenantId },
          select: { name: true }
        });

        const inputTokens = Number(topTenant._sum.inputTokens) || 0;
        const outputTokens = Number(topTenant._sum.outputTokens) || 0;
        const totalTokens = inputTokens + outputTokens;
        const costCHF = (Number(topTenant._sum.cost) || 0) * 0.91;

        // Dynamic time period description
        const timePeriodText = days === 7 ? "7 days" : days === 30 ? "30 days" : "90 days";

        alerts.push({
          id: `highest-token-${topTenant.tenantId}`,
          type: "usage_spike" as const,
          severity: "orange" as const,
          title: `Highest Token Usage`,
          description: `${tenant?.name || 'Unknown'} has highest token usage in ${timePeriodText} (${totalTokens.toLocaleString()} tokens, CHF ${costCHF.toFixed(2)})`,
          tenantId: topTenant.tenantId,
          tenantName: tenant?.name || 'Unknown Tenant',
          timestamp: new Date().toISOString(),
          acknowledged: false
        });
      }

      // HIGHEST API REQUESTS - Time period aware
      const topApiRequests = await db.aPIRequest.groupBy({
        by: ['tenantId'],
        where: {
          timestamp: { gte: startDate, lte: endDate },
          ...(planTier && {
            tenant: {
              Subscription: {
                some: {
                  plan: {
                    name: {
                      contains: planTier,
                      mode: 'insensitive'
                    }
                  }
                }
              }
            }
          })
        },
        _count: {
          id: true
        },
        orderBy: {
          _count: {
            id: 'desc'
          }
        },
        take: 1
      });

      if (topApiRequests.length > 0) {
        const topTenant = topApiRequests[0];
        const tenant = await db.tenant.findUnique({
          where: { id: topTenant.tenantId },
          select: { name: true }
        });

        const requests = Number(topTenant._count.id) || 0;

        // Dynamic time period description
        const timePeriodText = days === 7 ? "7 days" : days === 30 ? "30 days" : "90 days";

        alerts.push({
          id: `highest-api-${topTenant.tenantId}`,
          type: "usage_spike" as const,
          severity: "orange" as const,
          title: `Highest API Traffic`,
          description: `${tenant?.name || 'Unknown'} has highest API traffic in ${timePeriodText} (${requests.toLocaleString()} requests)`,
          tenantId: topTenant.tenantId,
          tenantName: tenant?.name || 'Unknown Tenant',
          timestamp: new Date().toISOString(),
          acknowledged: false
        });
      }

    } catch (error) {
      console.error("❌ Error calculating alerts:", error);
    }

    return {
      kpiMetrics: {
        activeTenants,
        activeUsers,
        mrr,
        infrastructureCost,
        grossMargin,
        alertsCount,
        trends: {
          activeTenants: activeTenantsTrend,
          activeUsers: activeUsersTrend,
          mrr: 5.2, // Placeholder
          infrastructureCost: -2.1, // Placeholder
          grossMargin: 3.8 // Placeholder
        }
      },
      usageTrends: {
        activeTenantsData,
        tokenUsageData,
        apiVolumeData,
        storageGrowthData
      },
      financialData: {
        mrrVsCostData,
        tenantProfitabilityData
      },
      alerts
    };

  } catch (error) {
    console.error("Error fetching executive dashboard data:", error);
    
    // Return fallback data in case of error
    return {
      kpiMetrics: {
        activeTenants: 0,
        activeUsers: 0,
        mrr: 0,
        infrastructureCost: 0,
        grossMargin: 0,
        alertsCount: 0,
        trends: {
          activeTenants: 0,
          activeUsers: 0,
          mrr: 0,
          infrastructureCost: 0,
          grossMargin: 0,
        }
      },
      usageTrends: {
        activeTenantsData: [],
        tokenUsageData: [],
        apiVolumeData: [],
        storageGrowthData: []
      },
      financialData: {
        mrrVsCostData: [],
        tenantProfitabilityData: []
      },
      alerts: []
    };
  }
}
