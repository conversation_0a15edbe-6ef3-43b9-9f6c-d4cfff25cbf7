"use client";

import { useEffect } from "react";
import { generateThemeVariables } from "@/services/theme-config";
import { ThemeConfig } from "@/types/theme-config";

interface DocsThemeApplierProps {
  themeConfig?: ThemeConfig | null;
}

export function DocsThemeApplier({ themeConfig }: DocsThemeApplierProps) {
  const isPartnerConsole = process.env.NEXT_PUBLIC_IS_PARTNER_CONSOLE === "1";

  useEffect(() => {
    // Only apply custom colors if partner console is enabled
    if (!isPartnerConsole) {
      return;
    }
    
    // Only run on client side
    if (typeof window === "undefined") return;

    if (themeConfig) {
      console.log("DocsThemeApplier: Applying custom colors to docs:", themeConfig);

      // Generate and apply CSS variables for custom colors
      const themeVariables = generateThemeVariables(themeConfig);
      const root = document.documentElement;

      // Apply color variables to the root element
      Object.entries(themeVariables).forEach(([property, value]) => {
        root.style.setProperty(property, value);
      });

      // Apply theme preset (light/dark mode) if specified
      if (themeConfig.themePreset) {
        root.classList.remove("light", "dark");
        root.classList.add(themeConfig.themePreset);
      }

      console.log("DocsThemeApplier: Custom colors applied to docs successfully");
    }
  }, [themeConfig, isPartnerConsole]);

  // This component doesn't render anything visible
  return null;
}

// Hook to fetch theme config for docs pages
export function useDocsThemeConfig() {
  const isPartnerConsole = process.env.NEXT_PUBLIC_IS_PARTNER_CONSOLE === "1";

  useEffect(() => {
    if (!isPartnerConsole) return;

    // Fetch theme config from API
    const fetchThemeConfig = async () => {
      try {
        // Get tenant ID from cookies
        const cookies = document.cookie.split(';');
        const tenantIdCookie = cookies.find(cookie =>
          cookie.trim().startsWith('currentOrganizationId=')
        );

        if (!tenantIdCookie) {
          console.log("DocsThemeApplier: No tenant ID cookie found");
          return;
        }

        const tenantId = tenantIdCookie.split('=')[1];
        if (!tenantId) {
          console.log("DocsThemeApplier: Empty tenant ID");
          return;
        }

        console.log("DocsThemeApplier: Fetching theme config for tenant:", tenantId);

        const response = await fetch(`/api/tenant/${tenantId}/theme-config`);
        if (response.ok) {
          const themeConfig = await response.json();

          console.log("DocsThemeApplier: Received theme config:", themeConfig);

          // Apply the theme config
          const themeVariables = generateThemeVariables(themeConfig);
          const root = document.documentElement;

          Object.entries(themeVariables).forEach(([property, value]) => {
            root.style.setProperty(property, value);
          });

          if (themeConfig.themePreset) {
            root.classList.remove("light", "dark");
            root.classList.add(themeConfig.themePreset);
          }

          console.log("DocsThemeApplier: Theme applied successfully");
        } else {
          console.log("DocsThemeApplier: Failed to fetch theme config, status:", response.status);
        }
      } catch (error) {
        console.error("Failed to fetch theme config for docs:", error);
      }
    };

    // Add a small delay to ensure cookies are available
    setTimeout(fetchThemeConfig, 100);
  }, [isPartnerConsole]);
}
