"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { formatNumber } from "@/lib/utils";
import {
  <PERSON><PERSON><PERSON>,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  <PERSON><PERSON>hart,
  Line,
  PieChart,
  Pie,
  Cell,
} from "recharts";

// Types (matching the server component)
interface UserMetrics {
  activeUserCount: number;
  totalUsers: number;
  activeUserPercentage: number;
  newUsers: number;
  userGrowthRate: number;
}

interface ActiveUsersOverTime {
  month?: string;
  date?: string;
  activeUsers: number;
}

interface RoleDistribution {
  name: string;
  value: number;
}

interface FeatureUsage {
  name: string;
  value: number;
}

interface UserMetricsData {
  userMetrics: UserMetrics;
  activeUsersOverTime: ActiveUsersOverTime[];
  roleDistribution: RoleDistribution[];
  featureUsage: FeatureUsage[];
  period: string;
}

interface UserMetricsClientProps {
  data: UserMetricsData;
}

export function UserMetricsClient({ data }: UserMetricsClientProps) {
  const { userMetrics, activeUsersOverTime, roleDistribution, featureUsage } = data;

  // Colors for the pie chart
  const COLORS = ["#0088FE", "#00C49F", "#FFBB28", "#FF8042", "#8884d8"];

  // Fallback data for charts if API returns empty
  const fallbackFeatureData = [
    { name: "Chat", value: 0 },
    { name: "Search", value: 0 },
    { name: "Upload", value: 0 },
    { name: "Share", value: 0 },
    { name: "Export", value: 0 },
  ];

  return (
    <div className="space-y-6">
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Total Users</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatNumber(userMetrics.totalUsers)}
            </div>
            <p className="text-xs text-muted-foreground">
              {userMetrics.userGrowthRate > 0 ? "+" : ""}
              {userMetrics.userGrowthRate}% from last period
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Active Users</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatNumber(userMetrics.activeUserCount)}
            </div>
            <p className="text-xs text-muted-foreground">
              {userMetrics.activeUserPercentage}% of total users
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">
              New Users
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatNumber(userMetrics.newUsers)}
            </div>
            <p className="text-xs text-muted-foreground">
              {userMetrics.totalUsers > 0
                ? `${Math.round(
                    (userMetrics.newUsers / userMetrics.totalUsers) * 100
                  )}% growth rate`
                : "0% growth rate"}
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">
              Average Session Duration
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">12m 30s</div>
            <p className="text-xs text-muted-foreground">+5% from last period</p>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Active Users Over Time</CardTitle>
          </CardHeader>
          <CardContent className="h-[300px]">
            {activeUsersOverTime.length === 0 ? (
              <div className="flex h-full items-center justify-center">
                <p className="text-muted-foreground">No activity data available</p>
              </div>
            ) : (
              <ResponsiveContainer width="100%" height="100%">
                <LineChart
                  data={activeUsersOverTime}
                  margin={{
                    top: 20,
                    right: 30,
                    left: 20,
                    bottom: 5,
                  }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey={activeUsersOverTime[0]?.month ? "month" : "date"} />
                  <YAxis />
                  <Tooltip
                    formatter={(value) => [`${value} users`, "Active Users"]}
                  />
                  <Legend />
                  <Line
                    type="monotone"
                    dataKey="activeUsers"
                    stroke="#8884d8"
                    activeDot={{ r: 8 }}
                    name="Active Users"
                  />
                </LineChart>
              </ResponsiveContainer>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Users by Role</CardTitle>
          </CardHeader>
          <CardContent className="h-[300px]">
            {roleDistribution.length === 0 ? (
              <div className="flex h-full items-center justify-center">
                <p className="text-muted-foreground">No role data available</p>
              </div>
            ) : (
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={roleDistribution}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ name, percent }) =>
                      `${name}: ${(percent * 100).toFixed(0)}%`
                    }
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                  >
                    {roleDistribution.map((_, index) => (
                      <Cell
                        key={`cell-${index}`}
                        fill={COLORS[index % COLORS.length]}
                      />
                    ))}
                  </Pie>
                  <Tooltip formatter={(value) => [`${value} users`, "Count"]} />
                </PieChart>
              </ResponsiveContainer>
            )}
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Feature Usage</CardTitle>
        </CardHeader>
        <CardContent className="h-[300px]">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart
              data={
                featureUsage.length > 0 ? featureUsage : fallbackFeatureData
              }
              margin={{
                top: 20,
                right: 30,
                left: 20,
                bottom: 5,
              }}
            >
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip
                formatter={(value) => [`${value} uses`, "Usage Count"]}
              />
              <Legend />
              <Bar dataKey="value" fill="#8884d8" name="Usage Count" />
            </BarChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>
    </div>
  );
}
