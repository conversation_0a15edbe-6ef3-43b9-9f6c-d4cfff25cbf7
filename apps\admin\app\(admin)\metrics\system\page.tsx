import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/next-auth";
import { redirect } from "next/navigation";
import db from "@/lib/shared-db";
import { addDays } from "date-fns";
import { SystemMetricsClient } from "@/components/metrics/system-metrics-client";

// Types for our data structures
interface ErrorMetrics {
  totalRequests: number;
  failedRequests: number;
  errorRate: number;
  errorsByEndpoint: Array<{
    endpoint: string;
    errorCount: number;
  }>;
  errorsByTenant: Array<{
    tenantId: string;
    tenantName: string;
    errorCount: number;
  }>;
}

interface LatencyMetrics {
  averageLatency: number;
  p95Latency: number;
  p99Latency: number;
  latencyByEndpoint: Array<{
    endpoint: string;
    requestCount: number;
    averageLatency: number;
    p95Latency: number;
  }>;
  latencyOverTime: Array<{
    date: string;
    averageLatency: number;
  }>;
}

interface SystemResourceUsage {
  cpuUsage: number;
  memoryUsage: number;
  storageUsage: number;
  activeConnections: number;
}

interface SystemMetricsData {
  errorMetrics: ErrorMetrics;
  latencyMetrics: LatencyMetrics;
  systemResourceUsage: SystemResourceUsage;
  period: string;
}

// Helper function to convert date range to period parameter
function dateRangeToPeriod(from: Date, to: Date): string {
  const diffTime = Math.abs(to.getTime() - from.getTime());
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  
  if (diffDays <= 7) return "7days";
  if (diffDays <= 30) return "30days";
  if (diffDays <= 90) return "90days";
  if (diffDays <= 180) return "6months";
  if (diffDays <= 365) return "12months";
  return "12months";
}

// Helper function to get days from period
function periodToDays(period: string): number {
  switch (period) {
    case "7days": return 7;
    case "30days": return 30;
    case "90days": return 90;
    case "6months": return 180;
    case "12months": return 365;
    default: return 7;
  }
}

// Server-side data fetching functions
async function fetchErrorMetrics(fromDate: Date, toDate: Date, period: string): Promise<ErrorMetrics> {
  // Get total API requests in the period
  const totalRequests = await db.aPIRequest.count({
    where: {
      timestamp: {
        gte: fromDate,
        lte: toDate
      }
    }
  });

  // Get failed API requests in the period
  const failedRequests = await db.aPIRequest.count({
    where: {
      timestamp: {
        gte: fromDate,
        lte: toDate
      },
      success: false
    }
  });

  // Calculate error rate
  const errorRate = totalRequests > 0 
    ? Math.round((failedRequests / totalRequests) * 100 * 100) / 100
    : 0;

  // Get errors by endpoint
  const errorsByEndpointRaw = await db.aPIRequest.groupBy({
    by: ['endpoint'],
    where: {
      timestamp: {
        gte: fromDate,
        lte: toDate
      },
      success: false
    },
    _count: {
      _all: true
    }
  });

  // Sort by error count (descending) and take top 10
  errorsByEndpointRaw.sort((a, b) => b._count._all - a._count._all);
  const errorsByEndpoint = errorsByEndpointRaw.slice(0, 10).map(item => ({
    endpoint: item.endpoint,
    errorCount: item._count._all
  }));

  // Get errors by tenant
  const errorsByTenantRaw = await db.aPIRequest.groupBy({
    by: ['tenantId'],
    where: {
      timestamp: {
        gte: fromDate,
        lte: toDate
      },
      success: false
    },
    _count: {
      _all: true
    }
  });

  // Get tenant names
  const tenantIds = errorsByTenantRaw.map(item => item.tenantId);
  const tenants = await db.tenant.findMany({
    where: {
      id: {
        in: tenantIds
      }
    },
    select: {
      id: true,
      name: true
    }
  });

  // Map tenant names to the results
  const errorsByTenant = errorsByTenantRaw.map(item => {
    const tenant = tenants.find(t => t.id === item.tenantId);
    return {
      tenantId: item.tenantId,
      tenantName: tenant?.name || 'Unknown',
      errorCount: item._count._all
    };
  });

  // Sort by error count (descending)
  errorsByTenant.sort((a, b) => b.errorCount - a.errorCount);

  return {
    totalRequests,
    failedRequests,
    errorRate,
    errorsByEndpoint,
    errorsByTenant
  };
}

async function fetchLatencyMetrics(fromDate: Date, toDate: Date, period: string): Promise<LatencyMetrics> {
  // Get all successful API requests in the period
  const apiRequests = await db.aPIRequest.findMany({
    where: {
      timestamp: {
        gte: fromDate,
        lte: toDate
      },
      success: true // Only include successful requests for latency metrics
    },
    select: {
      endpoint: true,
      duration: true, // Use duration instead of latencyMs
      tenantId: true,
      timestamp: true
    }
  });

  // Calculate average latency
  const totalLatency = apiRequests.reduce((sum, req) => sum + (req.duration || 0), 0);
  const averageLatency = apiRequests.length > 0
    ? Math.round(totalLatency / apiRequests.length)
    : 0;

  // Calculate P95 and P99 latency
  const sortedLatencies = apiRequests
    .map(req => req.duration || 0)
    .sort((a, b) => a - b);
  
  const p95Index = Math.floor(sortedLatencies.length * 0.95);
  const p95Latency = sortedLatencies.length > 0 
    ? sortedLatencies[p95Index] || sortedLatencies[sortedLatencies.length - 1] 
    : 0;

  // Group by endpoint for latency analysis
  const endpointGroups: Record<string, { latencies: number[]; count: number }> = {};
  
  apiRequests.forEach(req => {
    const endpoint = req.endpoint || 'unknown';
    if (!endpointGroups[endpoint]) {
      endpointGroups[endpoint] = {
        latencies: [],
        count: 0
      };
    }
    
    endpointGroups[endpoint].latencies.push(req.duration || 0);
    endpointGroups[endpoint].count++;
  });
  
  // Calculate metrics for each endpoint
  const latencyByEndpoint = Object.entries(endpointGroups).map(([endpoint, data]) => {
    const latencies = data.latencies.sort((a, b) => a - b);
    const count = data.count;
    
    const endpointAvgLatency = Math.round(latencies.reduce((sum, val) => sum + val, 0) / count);
    
    const p95Index = Math.floor(latencies.length * 0.95);
    const endpointP95Latency = latencies[p95Index] || latencies[latencies.length - 1];
    
    return {
      endpoint,
      requestCount: count,
      averageLatency: endpointAvgLatency,
      p95Latency: endpointP95Latency
    };
  });
  
  // Sort by average latency (descending) and take top 10
  latencyByEndpoint.sort((a, b) => b.averageLatency - a.averageLatency);

  // Calculate latency over time (daily aggregation)
  const latencyOverTime: Array<{ date: string; averageLatency: number }> = [];
  const days = periodToDays(period);
  
  if (days <= 14) {
    // Daily aggregation for shorter periods
    const dailyGroups: Record<string, { latencies: number[]; count: number }> = {};
    
    apiRequests.forEach(req => {
      const day = req.timestamp.toISOString().split('T')[0];
      if (!dailyGroups[day]) {
        dailyGroups[day] = {
          latencies: [],
          count: 0
        };
      }
      
      dailyGroups[day].latencies.push(req.duration || 0);
      dailyGroups[day].count++;
    });
    
    // Calculate daily averages
    for (const [day, data] of Object.entries(dailyGroups)) {
      const latencies = data.latencies;
      const count = data.count;
      
      latencyOverTime.push({
        date: day,
        averageLatency: Math.round(latencies.reduce((sum, val) => sum + val, 0) / count)
      });
    }
    
    // Sort by date
    latencyOverTime.sort((a, b) => a.date.localeCompare(b.date));
  }

  return {
    averageLatency,
    p95Latency,
    p99Latency: p95Latency, // Using P95 as P99 for simplicity
    latencyByEndpoint: latencyByEndpoint.slice(0, 10),
    latencyOverTime
  };
}

async function fetchSystemResourceUsage(_fromDate: Date, _toDate: Date): Promise<SystemResourceUsage> {
  // Mock system resource usage data since we don't have actual system metrics
  // In a real implementation, this would fetch from system monitoring tools
  
  // Get active connections count from recent API requests
  const recentRequests = await db.aPIRequest.count({
    where: {
      timestamp: {
        gte: addDays(new Date(), -1) // Last 24 hours
      }
    }
  });

  return {
    cpuUsage: Math.floor(Math.random() * 30) + 20, // Mock 20-50% CPU usage
    memoryUsage: Math.floor(Math.random() * 40) + 30, // Mock 30-70% memory usage
    storageUsage: Math.floor(Math.random() * 20) + 40, // Mock 40-60% storage usage
    activeConnections: Math.floor(recentRequests / 100) // Approximate active connections
  };
}

// Main server component
export default async function SystemMetricsPage({
  searchParams,
}: {
  searchParams: { from?: string; to?: string };
}) {
  const session = await getServerSession(authOptions);
  if (!session) {
    redirect("/auth/signin");
  }

  // Parse date range from search params or use defaults
  const fromDate = searchParams.from ? new Date(searchParams.from) : addDays(new Date(), -7); // 7 days default
  const toDate = searchParams.to ? new Date(searchParams.to) : new Date();
  const period = dateRangeToPeriod(fromDate, toDate);

  // Fetch all data in parallel
  const [errorMetrics, latencyMetrics, systemResourceUsage] = await Promise.all([
    fetchErrorMetrics(fromDate, toDate, period),
    fetchLatencyMetrics(fromDate, toDate, period),
    fetchSystemResourceUsage(fromDate, toDate),
  ]);

  const metricsData: SystemMetricsData = {
    errorMetrics,
    latencyMetrics,
    systemResourceUsage,
    period,
  };

  return <SystemMetricsClient data={metricsData} />;
}
