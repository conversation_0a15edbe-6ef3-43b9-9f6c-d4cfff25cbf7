import { apiUrl, fetchJson } from "..";

export const createVectorDB = async (data) => {
  try {
    const response = await fetch(`${apiUrl}/vector-settings`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error("Network response was not ok");
    }
    return await response.json();
  } catch (error) {
    console.log({ "Create VectorDB api calling error": error });
    return { error: "Error Creating The VectorDB" };
  }
};

export const updateVectorDB = async (data) => {
  try {
    const response = await fetch(`${apiUrl}/vector-settings`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error("Network response was not ok");
    }
    return await response.json();
  } catch (error) {
    console.log({ "update vector-settings api calling error": error });
    return { error: "Error Update The vector-settings" };
  }
};

export const getVectorDB = async ({
  tenantId,
  workspaceSlug = "",
  id = "",
}) => {
  try {
    if (id) {
      const response = await fetchJson(
        `${apiUrl}/vector-settings?id=${id}&tenantId=${tenantId}`
      );
      return response;
    }
    if (workspaceSlug) {
      const response = await fetchJson(
        `${apiUrl}/vector-settings?slug=${workspaceSlug}&tenantId=${tenantId}`
      );
      return response;
    }
    const response = await fetchJson(
      `${apiUrl}/vector-settings?tenantId=${tenantId}`
    );

    return response;
  } catch (error) {
    console.error("Error in getVectorDB service:", error);
    throw error;
  }
};

export const deleteVectorDB = async (id) => {
  try {
    const url = `${apiUrl}/vector-settings?id=${id}`;
    const response = await fetch(url, {
      method: "DELETE",
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Network response was not ok");
    }
    return await response.json();
  } catch (error) {
    console.log({ "delete VectorDB api error": error });
    return { error: error.message || "Error deleting VectorDB" };
  }
};
