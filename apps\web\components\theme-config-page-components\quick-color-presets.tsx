"use client";

import React from "react";
import { Button } from "@/components/ui/button";
import { ThemeConfigFormValues } from "@/components/wrapper-screens/organization-settings/theme-config/theme-config-page";

interface ColorPreset {
  name: string;
  description: string;
  colors: {
    // Light theme colors
    lightPrimaryColor: string;
    lightSecondaryColor: string;
    lightAccentColor: string;
    lightNavigationBackgroundColor: string;
    lightContentBackgroundColor: string;
    lightForegroundColor: string;
    // Dark theme colors
    darkPrimaryColor: string;
    darkSecondaryColor: string;
    darkAccentColor: string;
    darkNavigationBackgroundColor: string;
    darkContentBackgroundColor: string;
    darkForegroundColor: string;
  };
}

interface QuickColorPresetsProps {
  form: any; // React Hook Form instance
  previewMode: boolean;
  applyPreviewColors: (colors: Partial<ThemeConfigFormValues>) => void;
  isAdmin: boolean;
}

const COLOR_PRESETS: ColorPreset[] = [
  {
    name: "Calm Sky",
    description: "Soft blue tones for a relaxed, professional look",
    colors: {
      lightPrimaryColor: "#60a5fa",
      lightSecondaryColor: "#cbd5e1",
      lightAccentColor: "#3b82f6",
      lightNavigationBackgroundColor: "#f1f5f9",
      lightContentBackgroundColor: "#ffffff",
      lightForegroundColor: "#1e293b",

      darkPrimaryColor: "#3b82f6",
      darkSecondaryColor: "#64748b",
      darkAccentColor: "#60a5fa",
      darkNavigationBackgroundColor: "#1e293b",
      darkContentBackgroundColor: "#0f172a",
      darkForegroundColor: "#e2e8f0"
    }
  },
  {
    name: "Muted Sage",
    description: "Subtle green tones inspired by nature, easy on the eyes",
    colors: {
      lightPrimaryColor: "#84cc16",
      lightSecondaryColor: "#d1d5db",
      lightAccentColor: "#65a30d",
      lightNavigationBackgroundColor: "#f7fee7",
      lightContentBackgroundColor: "#ffffff",
      lightForegroundColor: "#365314",

      darkPrimaryColor: "#a3e635",
      darkSecondaryColor: "#94a3b8",
      darkAccentColor: "#84cc16",
      darkNavigationBackgroundColor: "#365314",
      darkContentBackgroundColor: "#1a2e05",
      darkForegroundColor: "#ecfccb"
    }
  },
  {
    name: "Lavender Breeze",
    description: "Gentle purple and gray palette with modern contrast",
    colors: {
      lightPrimaryColor: "#a78bfa",
      lightSecondaryColor: "#e5e7eb",
      lightAccentColor: "#8b5cf6",
      lightNavigationBackgroundColor: "#fdf4ff",
      lightContentBackgroundColor: "#ffffff",
      lightForegroundColor: "#4c1d95",

      darkPrimaryColor: "#c4b5fd",
      darkSecondaryColor: "#d1d5db",
      darkAccentColor: "#a78bfa",
      darkNavigationBackgroundColor: "#3b0764",
      darkContentBackgroundColor: "#1e1b4b",
      darkForegroundColor: "#f3e8ff"
    }
  },
  {
    name: "Peach Sand",
    description: "Warm, muted oranges and neutrals for soft visual flow",
    colors: {
      lightPrimaryColor: "#fb923c",
      lightSecondaryColor: "#fcd34d",
      lightAccentColor: "#f59e0b",
      lightNavigationBackgroundColor: "#fffbeb",
      lightContentBackgroundColor: "#ffffff",
      lightForegroundColor: "#78350f",

      darkPrimaryColor: "#fdba74",
      darkSecondaryColor: "#fbbf24",
      darkAccentColor: "#f97316",
      darkNavigationBackgroundColor: "#78350f",
      darkContentBackgroundColor: "#431407",
      darkForegroundColor: "#fff7ed"
    }
  },
  {
    name: "Steel Mist",
    description: "Neutral gray-blue tones designed for minimal eye strain",
    colors: {
      lightPrimaryColor: "#64748b",
      lightSecondaryColor: "#e2e8f0",
      lightAccentColor: "#475569",
      lightNavigationBackgroundColor: "#f8fafc",
      lightContentBackgroundColor: "#ffffff",
      lightForegroundColor: "#1e293b",

      darkPrimaryColor: "#94a3b8",
      darkSecondaryColor: "#cbd5e1",
      darkAccentColor: "#64748b",
      darkNavigationBackgroundColor: "#1e293b",
      darkContentBackgroundColor: "#0f172a",
      darkForegroundColor: "#e2e8f0"
    }
  },
  {
    name: "Soft Clay",
    description: "Earthy and subdued, perfect for professional dashboards",
    colors: {
      lightPrimaryColor: "#fcd34d",
      lightSecondaryColor: "#fef3c7",
      lightAccentColor: "#f59e0b",
      lightNavigationBackgroundColor: "#fffbea",
      lightContentBackgroundColor: "#ffffff",
      lightForegroundColor: "#78350f",

      darkPrimaryColor: "#fde68a",
      darkSecondaryColor: "#fcd34d",
      darkAccentColor: "#fbbf24",
      darkNavigationBackgroundColor: "#78350f",
      darkContentBackgroundColor: "#431407",
      darkForegroundColor: "#fffbea"
    }
  },
  {
  name: "Calm Sage",
  description: "Gentle green tones with soft grays ideal for low-stress interfaces",
  colors: {
    lightPrimaryColor: "#6ee7b7",
    lightSecondaryColor: "#cbd5e1",
    lightAccentColor: "#5eead4",
    lightNavigationBackgroundColor: "#f9fafb",
    lightContentBackgroundColor: "#ffffff",
    lightForegroundColor: "#1e293b",
    darkPrimaryColor: "#4ade80",
    darkSecondaryColor: "#94a3b8",
    darkAccentColor: "#34d399",
    darkNavigationBackgroundColor: "#1e293b",
    darkContentBackgroundColor: "#111827",
    darkForegroundColor: "#f1f5f9"
  }
},
{
  name: "Soft Lavender",
  description: "Subtle purples and blues for a soothing UI experience",
  colors: {
    lightPrimaryColor: "#c4b5fd",
    lightSecondaryColor: "#e0e7ff",
    lightAccentColor: "#a78bfa",
    lightNavigationBackgroundColor: "#f8f5ff",
    lightContentBackgroundColor: "#ffffff",
    lightForegroundColor: "#312e81",
    darkPrimaryColor: "#a78bfa",
    darkSecondaryColor: "#818cf8",
    darkAccentColor: "#8b5cf6",
    darkNavigationBackgroundColor: "#312e81",
    darkContentBackgroundColor: "#1e1b4b",
    darkForegroundColor: "#e0e7ff"
  }
},
{
  name: "Slate Mist",
  description: "Minimalist grayscale theme with soft foggy accents",
  colors: {
    lightPrimaryColor: "#94a3b8",
    lightSecondaryColor: "#e5e7eb",
    lightAccentColor: "#cbd5e1",
    lightNavigationBackgroundColor: "#f8fafc",
    lightContentBackgroundColor: "#ffffff",
    lightForegroundColor: "#1f2937",
    darkPrimaryColor: "#cbd5e1",
    darkSecondaryColor: "#94a3b8",
    darkAccentColor: "#64748b",
    darkNavigationBackgroundColor: "#0f172a",
    darkContentBackgroundColor: "#1e293b",
    darkForegroundColor: "#f8fafc"
  }
}

  
];


export function QuickColorPresets({
  form,
  previewMode,
  applyPreviewColors,
  isAdmin
}: QuickColorPresetsProps) {
  const handlePresetClick = (preset: ColorPreset) => {
    // Update all color fields in the form
    Object.entries(preset.colors).forEach(([key, value]) => {
      form.setValue(key as keyof ThemeConfigFormValues, value);
    });

    // Apply preview colors if preview mode is enabled
    if (previewMode) {
      applyPreviewColors({ ...form.getValues(), ...preset.colors });
    }
  };

  return (
    <div className="space-y-4">
      <div>
        <h4 className="text-sm font-medium">Quick Color Presets</h4>
        <p className="text-xs text-muted-foreground">
          Complete color schemes optimized for both light and dark themes
        </p>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
        {COLOR_PRESETS.map((preset) => (
          <Button
            key={preset.name}
            type="button"
            variant="outline"
            onClick={() => handlePresetClick(preset)}
            disabled={!isAdmin}
            className="h-auto p-4 flex flex-col items-start gap-3 hover:bg-accent/50 transition-colors border-gray-200 dark:border-gray-700 w-full max-w-full overflow-hidden text-left"
            title={preset.description}
          >
            {/* Color Preview */}
            <div className="flex items-center gap-2 w-full flex-wrap overflow-hidden">
              <div className="flex gap-1">
                {/* Light theme color dots */}
                <div className="flex gap-0.5">
                  <div
                    className="w-3 h-3 rounded-full border border-gray-300 dark:border-gray-600"
                    style={{ backgroundColor: preset.colors.lightPrimaryColor }}
                    title="Light Primary"
                  />
                  <div
                    className="w-3 h-3 rounded-full border border-gray-300 dark:border-gray-600"
                    style={{ backgroundColor: preset.colors.lightAccentColor }}
                    title="Light Accent"
                  />
                </div>

                {/* Separator */}
                <div className="w-px h-3 bg-gray-300 dark:bg-gray-600 mx-1" />

                {/* Dark theme color dots */}
                <div className="flex gap-0.5">
                  <div
                    className="w-3 h-3 rounded-full border border-gray-300 dark:border-gray-600"
                    style={{ backgroundColor: preset.colors.darkPrimaryColor }}
                    title="Dark Primary"
                  />
                  <div
                    className="w-3 h-3 rounded-full border border-gray-300 dark:border-gray-600"
                    style={{ backgroundColor: preset.colors.darkAccentColor }}
                    title="Dark Accent"
                  />
                </div>
              </div>

              <span className="text-sm font-medium flex-1 text-left">
                {preset.name}
              </span>
            </div>

            {/* Description */}
            <p className="text-xs text-muted-foreground text-left leading-relaxed break-words whitespace-normal w-full">
              {preset.description}
            </p>
          </Button>
        ))}
      </div>
    </div>
  );
}
