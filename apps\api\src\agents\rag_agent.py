import os
from typing import Dict, List, Optional, Any, Union
import numpy as np
from pymongo import MongoClient
import json
from bson import ObjectId
import requests
from src.utils.intelligent_query_interpreter import query_interpreter
from PIL import Image
import base64
import io
from .web_search import WebSearchClient, format_web_sources, enhance_prompt_with_web_results

from langchain_community.embeddings import HuggingFaceEmbeddings
from langchain_community.embeddings import OpenAIEmbeddings
from langchain_core.embeddings import Embeddings
import datetime

# Custom JSON encoder to handle non-serializable objects
class CustomJSONEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, ObjectId):
            return str(obj)
        # Add more custom type handling as needed
        return super().default(obj)

# Import the markitdown converter
from src.utils.markitdown_converter import MarkitdownConverter


from langchain_community.vectorstores import Chroma

from langchain_community.vectorstores import ElasticsearchStore

from langchain_community.vectorstores import FAISS

from langchain_community.vectorstores import Milvus

from langchain_community.vectorstores import MongoDBAtlasVectorSearch

from langchain_community.vectorstores import OpenSearchVectorSearch

from langchain_community.vectorstores import Pinecone

from langchain_community.vectorstores import Qdrant

from langchain_community.vectorstores import Weaviate


from langchain_community.document_loaders import CSVLoader

from langchain_community.document_loaders import DirectoryLoader

from langchain_community.document_loaders import PyPDFLoader

from langchain_community.document_loaders import TextLoader

from langchain_community.document_loaders import UnstructuredMarkdownLoader
from langchain_text_splitters import RecursiveCharacterTextSplitter
from langchain.chains import RetrievalQA
from langchain_deepseek import ChatDeepSeek

from langchain.retrievers import EnsembleRetriever
from langchain_community.llms import AzureOpenAI
from langchain_core.messages import HumanMessage, SystemMessage
from langchain_community.llms import HuggingFacePipeline

from langchain_community.llms import LlamaCpp

from langchain_community.llms import OpenAI

from langchain_community.chat_models import ChatOpenAI

from langchain_community.chat_models import AzureChatOpenAI

from langchain_community.chat_models import ChatAnthropic
from langchain_core.prompts import PromptTemplate
from langchain_core.language_models import LLM
import logging


from langchain.schema import BaseRetriever
from langchain.schema.document import Document
import numpy as np
from typing import List
# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CustomEmbeddingModel(Embeddings):
    """A wrapper class for custom embedding models that don't have a LangChain integration."""

    def __init__(self, model_callable, dimension: int = 1536):
        """
        Initialize with a callable that generates embeddings.

        Args:
            model_callable: A function that takes a list of texts and returns a list of embeddings
            dimension: The dimensionality of the embeddings
        """
        self.model_callable = model_callable
        self.dimension = dimension

    def embed_documents(self, texts: List[str]) -> List[List[float]]:
        """Embed a list of documents."""
        return self.model_callable(texts)

    def embed_query(self, text: str) -> List[float]:
        """Embed a query."""
        return self.model_callable([text])[0]

class AdvancedRAGAgent:
    """
    An advanced RAG agent capable of connecting with multiple vector storage solutions,
    handling different document types, and providing optimized retrieval results with
    customizable LLM and embedding models.
    """

    def __init__(
        self,
        vector_store_type: str = "chroma",
        embedding_model: Union[str, Dict, Embeddings] = "openai",
        llm_model: Union[str, Dict, LLM] = "gpt-3.5-turbo",
        temperature: float = 0.0,
        chunk_size: int = 800,
        chunk_overlap: int = 150,
        top_k: int = 4,
        api_keys: Optional[Dict[str, str]] = None,
        vector_store_config: Optional[Dict[str, Any]] = None
    ):
        """
        Initialize the RAG agent with highly customizable components.

        Args:
            vector_store_type: Type of vector store to use (chroma, faiss, pinecone, etc.)
            embedding_model: Model to use for embeddings - can be:
                - string: "openai", "huggingface", "azure", etc.
                - dict: {"type": "huggingface", "model_name": "sentence-transformers/all-mpnet-base-v2", ...}
                - Embeddings: A pre-initialized LangChain embeddings object
            llm_model: Language model to use for generation - can be:
                - string: "gpt-3.5-turbo", "gpt-4", "claude-3-opus", "deepseek-chat", etc.
                - dict: {"type": "openai", "model_name": "gpt-4", "api_key": "...", ...}
                - LLM: A pre-initialized LangChain LLM object
            temperature: Temperature for generation (default when using string/dict config)
            chunk_size: Size of document chunks
            chunk_overlap: Overlap between chunks
            top_k: Number of documents to retrieve
            api_keys: Dictionary of API keys for different services
            vector_store_config: Additional configuration for the vector store
        """
        self.vector_store_type = vector_store_type.lower()
        self.embedding_model_config = embedding_model
        self.llm_model_config = llm_model
        self.temperature = temperature
        self.chunk_size = chunk_size
        self.chunk_overlap = chunk_overlap
        self.top_k = top_k
        self.api_keys = api_keys or {}
        self.vector_store_config = vector_store_config or {}

        # Set API keys if provided
        # Set API keys in environment variables if provided
        api_key_mapping = {
            "openai": "OPENAI_API_KEY",
            "anthropic": "ANTHROPIC_API_KEY",
            "azure": "AZURE_OPENAI_API_KEY",
            "deepseek": "DEEPSEEK_API_KEY"
        }

        for service, env_var in api_key_mapping.items():
            if self.api_keys.get(env_var):
                os.environ[env_var] = self.api_keys[env_var]

        # Initialize components
        self.embedding_model = self._initialize_embedding_model()
        self.llm = self._initialize_llm()
        self.vector_store = None
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=chunk_size,
            chunk_overlap=chunk_overlap,
            length_function=len,
            separators=["\n\n", "\n", ". ", "! ", "? ", " ", ""],
            keep_separator=True
        )
        self.qa_chain = None

        logger.info(f"Initialized RAG agent with {vector_store_type}")

    def _initialize_embedding_model(self):
        """Initialize the embedding model based on the selected configuration."""

        # If already a LangChain embeddings object, return it
        if isinstance(self.embedding_model_config, Embeddings):
            logger.info("Using pre-initialized embedding model")
            return self.embedding_model_config

        # Parse configuration if it's a dictionary
        if isinstance(self.embedding_model_config, dict):
            model_type = self.embedding_model_config.get("type", "openai").lower()
            model_name = self.embedding_model_config.get("model_name")
            api_key = self.embedding_model_config.get("api_key",
                                                     self.api_keys.get(model_type))
        else:
            # Assume it's a string
            model_type = self.embedding_model_config.lower()
            model_name = None
            api_key = self.api_keys.get(model_type)
        # Initialize based on the model type
        if model_type == "openai":
            logger.info("Initializing OpenAI embeddings")
            # Create the base model and wrap it with safety wrapper
            base_model = OpenAIEmbeddings(
                model=model_name or "embed-v-4-0",
                api_key=api_key
            )

            # Import the SafeEmbeddingWrapper from the tools
            from .tools.vector_search_tool import SafeEmbeddingWrapper
            return SafeEmbeddingWrapper(base_model)

        elif model_type == "azure":
            if not isinstance(self.embedding_model_config, dict):
                raise ValueError("Azure OpenAI embeddings require a dictionary configuration")

            # Required Azure-specific parameters
            azure_endpoint = self.embedding_model_config.get("azure_endpoint")
            if not azure_endpoint:
                raise ValueError("Azure OpenAI embeddings require azure_endpoint")

            deployment_name = self.embedding_model_config.get("deployment_name")
            if not deployment_name:
                raise ValueError("Azure OpenAI embeddings require deployment_name")

            api_version = self.embedding_model_config.get("api_version", "2024-02-15-preview")

            logger.info(f"Initializing Azure OpenAI embeddings with deployment {deployment_name}")

            # Use Azure Vision embeddings instead of LangChain AzureOpenAIEmbeddings
            # to avoid the tokenization bug in langchain-openai 0.3.13
            logger.info("Using Azure Vision embeddings to avoid LangChain tokenization bug")
            from .azure_vision_embeddings import AzureVisionEmbeddings

            base_model = AzureVisionEmbeddings(
                azure_endpoint=azure_endpoint,
                api_key=api_key,
                deployment_name=deployment_name,
                api_version=api_version,
                model=model_name or "embed-v-4-0"
            )

            # Import the SafeEmbeddingWrapper from the tools for additional safety
            from .tools.vector_search_tool import SafeEmbeddingWrapper
            return SafeEmbeddingWrapper(base_model)

        elif model_type == "azure_vision":
            if not isinstance(self.embedding_model_config, dict):
                raise ValueError("Azure Vision embeddings require a dictionary configuration")

            # Required Azure Vision-specific parameters
            azure_endpoint = self.embedding_model_config.get("azure_endpoint")
            if not azure_endpoint:
                raise ValueError("Azure Vision embeddings require azure_endpoint")

            deployment_name = self.embedding_model_config.get("deployment_name")
            if not deployment_name:
                raise ValueError("Azure Vision embeddings require deployment_name")

            api_version = self.embedding_model_config.get("api_version", "2023-12-01-preview")

            logger.info(f"Initializing Azure Vision embeddings with deployment {deployment_name}")

            # Import AzureVisionEmbeddings for azure_vision type
            from .azure_vision_embeddings import AzureVisionEmbeddings

            # Create the base model and wrap it with safety wrapper
            base_model = AzureVisionEmbeddings(
                azure_endpoint=azure_endpoint,
                api_key=api_key,
                deployment_name=deployment_name,
                api_version=api_version,
                model=model_name or "embed-v-4-0"
            )

            # Import the SafeEmbeddingWrapper from the tools
            from .tools.vector_search_tool import SafeEmbeddingWrapper
            return SafeEmbeddingWrapper(base_model)

        elif model_type == "huggingface" or model_type == "hf":
            model_name = model_name or "sentence-transformers/all-mpnet-base-v2"
            logger.info(f"Initializing HuggingFace embeddings with {model_name}")

            if isinstance(self.embedding_model_config, dict):
                # Get additional parameters from config
                hf_params = {k: v for k, v in self.embedding_model_config.items()
                            if k not in ["type", "model_name", "api_key"]}

                return HuggingFaceEmbeddings(
                    model_name=model_name,
                    **hf_params
                )
            else:
                return HuggingFaceEmbeddings(model_name=model_name)

        elif model_type == "custom":
            if not isinstance(self.embedding_model_config, dict) or "callable" not in self.embedding_model_config:
                raise ValueError("Custom embedding model requires a dictionary with a 'callable' function")

            callable_fn = self.embedding_model_config["callable"]
            dimension = self.embedding_model_config.get("dimension", 1536)

            logger.info("Initializing custom embedding model")
            return CustomEmbeddingModel(callable_fn, dimension)

        else:
            raise ValueError(f"Unsupported embedding model type: {model_type}")

    def _initialize_llm(self):
        """Initialize the language model based on the selected configuration."""

        # If already a LangChain LLM object, return it
        if isinstance(self.llm_model_config, LLM):
            logger.info("Using pre-initialized LLM")
            return self.llm_model_config

        # Parse configuration if it's a dictionary
        if isinstance(self.llm_model_config, dict):
            model_type = self.llm_model_config.get("type", "openai").lower()
            model_name = self.llm_model_config.get("model_name")

            api_key = self.llm_model_config.get("api_key",
                                              self.api_keys.get(model_type))
            temperature = self.llm_model_config.get("temperature", self.temperature)
        else:
            # Assume it's a string
            if "gpt" in self.llm_model_config.lower() or "text-davinci" in self.llm_model_config.lower():
                model_type = "openai"
            elif "claude" in self.llm_model_config.lower():
                model_type = "anthropic"
            elif "llama" in self.llm_model_config.lower():
                model_type = "llamacpp"
            elif "deepseek" in self.llm_model_config.lower():
                model_type = "DeepSeek-R1"
            else:
                model_type = "openai"  # Default to OpenAI

            model_name = self.llm_model_config
            api_key = self.api_keys.get(model_type)
            temperature = self.temperature

        # Initialize based on the model type
        if model_type == "openai":
            logger.info(f"Initializing OpenAI model: {model_name}")

            if model_name and ("gpt-3.5" in model_name or "gpt-4" in model_name):
                return ChatOpenAI(
                    model_name=model_name,
                    temperature=temperature,
                    api_key=api_key
                )
            else:
                return OpenAI(
                    model_name=model_name or "text-davinci-003",
                    temperature=temperature,
                    api_key=api_key
                )

        elif model_type == "azure":
            if not isinstance(self.llm_model_config, dict):
                raise ValueError("Azure OpenAI requires a dictionary configuration")

            # Required Azure-specific parameters
            azure_endpoint = self.llm_model_config.get("azure_endpoint")
            if not azure_endpoint:
                raise ValueError("Azure OpenAI requires azure_endpoint")

            deployment_name = self.llm_model_config.get("deployment_name")
            if not deployment_name:
                raise ValueError("Azure OpenAI requires deployment_name")

            api_version = self.llm_model_config.get("api_version", "2023-05-15")
            model_name = self.llm_model_config.get("model_name", "")

            logger.info(f"Initializing Azure OpenAI with deployment {deployment_name}")

            # Check if it's a chat model or completion model based on deployment name pattern
            if "gpt-3.5" in deployment_name.lower() or "gpt-4" in deployment_name.lower() or "chat" in deployment_name.lower() or "deepseek" in deployment_name.lower():
                return AzureChatOpenAI(
                    deployment_name=deployment_name,
                    azure_endpoint=azure_endpoint,
                    api_version=api_version,
                    api_key=api_key,
                    temperature=temperature,
                    model_name=model_name,
                )
            else:
                return AzureOpenAI(
                    deployment_name=deployment_name,
                    azure_endpoint=azure_endpoint,
                    api_version=api_version,
                    api_key=api_key,
                    temperature=temperature
                )

        elif model_type == "anthropic":
            logger.info(f"Initializing Anthropic Claude model: {model_name}")

            return ChatAnthropic(
                model_name=model_name or "claude-3-sonnet-20240229",
                temperature=temperature,
                api_key=api_key
            )

        elif model_type == "deepseek":
            logger.info(f"Initializing DeepSeek model: {model_name} {api_key}")

            azure_endpoint = self.llm_model_config.get("azure_endpoint")
            deployment_name = self.llm_model_config.get("deployment_name")
            api_version = self.llm_model_config.get("api_version", "2024-05-01-preview")

            # If azure_endpoint is provided, use Azure OpenAI for DeepSeek models
            if azure_endpoint and deployment_name:
                logger.info(f"Using Azure OpenAI for DeepSeek model with deployment {deployment_name}")
                return AzureChatOpenAI(
                    deployment_name=deployment_name,
                    azure_endpoint=azure_endpoint,
                    api_version=api_version,
                    api_key=api_key,
                    temperature=temperature,
                    model_name=model_name,
                )
            else:
                # Use native DeepSeek API
                return ChatDeepSeek(
                    api_base=azure_endpoint,
                    model_name=model_name or "DeepSeek-R1",
                    temperature=temperature,
                    api_key=api_key or os.getenv("DEEPSEEK_API_KEY")
                )

        elif model_type == "huggingface" or model_type == "hf":
            if not isinstance(self.llm_model_config, dict) or not model_name:
                raise ValueError("HuggingFace pipeline requires a model_name")

            logger.info(f"Initializing HuggingFace pipeline with {model_name}")

            # Extract additional parameters if any
            hf_params = {k: v for k, v in self.llm_model_config.items()
                       if k not in ["type", "model_name", "api_key", "temperature"]}

            try:
                from transformers import pipeline

                pipe = pipeline(
                    "text-generation",
                    model=model_name,
                    temperature=temperature,
                    **hf_params
                )

                return HuggingFacePipeline(pipeline=pipe)
            except ImportError:
                raise ImportError("HuggingFace transformers not installed. Install with: pip install transformers")

        elif model_type == "llamacpp":
            if not isinstance(self.llm_model_config, dict) or not self.llm_model_config.get("model_path"):
                raise ValueError("LlamaCpp requires a model_path in the configuration")

            model_path = self.llm_model_config.get("model_path")
            logger.info(f"Initializing LlamaCpp with model at {model_path}")

            # Extract additional parameters if any
            llama_params = {k: v for k, v in self.llm_model_config.items()
                         if k not in ["type", "model_name", "api_key", "temperature", "model_path"]}

            try:
                return LlamaCpp(
                    model_path=model_path,
                    temperature=temperature,
                    **llama_params
                )
            except ImportError:
                raise ImportError("LlamaCpp not installed. Install with: pip install llama-cpp-python")

        elif model_type == "custom":
            if not isinstance(self.llm_model_config, dict) or "callable" not in self.llm_model_config:
                raise ValueError("Custom LLM requires a dictionary with a 'callable' function")

            from langchain_core.language_models import BaseLLM

            class CustomLLM(BaseLLM):
                """Custom LLM wrapper for arbitrary callable models."""

                def __init__(self, model_callable, **kwargs):
                    super().__init__(**kwargs)
                    self.model_callable = model_callable

                def _call(self, prompt, stop=None, **kwargs):
                    return self.model_callable(prompt, stop=stop)

                @property
                def _llm_type(self):
                    return "custom"

            logger.info("Initializing custom LLM")
            return CustomLLM(self.llm_model_config["callable"])

        else:
            raise ValueError(f"Unsupported LLM type: {model_type}")

    def _initialize_vector_store(self, documents=None, collection_name=None):
        """Initialize the vector store based on the selected type."""
        # For MongoDB, we don't need documents or persist_directory to connect to existing store
        if documents is None and "persist_directory" not in self.vector_store_config:
            if self.vector_store_type == "mongodb":
                # For MongoDB, we can proceed without documents or persist_directory
                if "connection_string" in self.vector_store_config and "db_name" in self.vector_store_config and "namespace" in self.vector_store_config:
                    logger.info("Connecting to existing MongoDB store without documents")
                else:
                    logger.warning("MongoDB connection requires connection_string, db_name, and namespace")
                    return None
            else:
                logger.warning("No documents provided and no persist directory specified")
                return None

        store_params = {"embedding": self.embedding_model}
        store_params.update(self.vector_store_config)

        # Handle MongoDB Atlas Vector Search specific configuration
        if self.vector_store_type == "mongodb":
            if "namespace" not in store_params:
                raise ValueError("MongoDB requires collection_name in vector_store_config")
            if "connection_string" not in store_params:
                raise ValueError("MongoDB requires connection_string in vector_store_config")
            if "db_name" not in store_params:
                raise ValueError("MongoDB requires db_name in vector_store_config")
            client = MongoClient(store_params["connection_string"])
            db = client[store_params["db_name"]]
            collection = db[collection_name or store_params["namespace"]]
            # Ensure index name is set for vector search
            if "index_name" not in store_params:
                store_params["index_name"] = "sharded_diskann_vector_index"

            param={
                "index_name": store_params["index_name"],
                "text_key": store_params.get("text_key", "content"),
                "embedding_key": store_params.get("embedding_key", "embedding"),
                "collection" :collection,
                "embedding": self.embedding_model,
            }

            # Initialize Azure Cosmos DB vector store
            if documents:
                return MongoDBAtlasVectorSearch.from_documents(
                    documents=documents,
                    **param
                )
            else:
                return MongoDBAtlasVectorSearch(**param)

        if self.vector_store_type == "chroma":
            if documents:
                return Chroma.from_documents(documents=documents, **store_params)
            else:
                return Chroma(**store_params)

        elif self.vector_store_type == "faiss":
            if documents:
                return FAISS.from_documents(documents=documents, **store_params)
            else:
                raise ValueError("FAISS requires documents for initialization")

        elif self.vector_store_type == "pinecone":
            if "pinecone_api_key" not in store_params and "api_key" not in store_params:
                store_params["api_key"] = self.api_keys.get("pinecone")
            if "index_name" not in store_params:
                raise ValueError("Pinecone requires an index_name in vector_store_config")

            if documents:
                return Pinecone.from_documents(documents=documents, **store_params)
            else:
                return Pinecone(**store_params)

        elif self.vector_store_type == "weaviate":
            if documents:
                return Weaviate.from_documents(documents=documents, **store_params)
            else:
                return Weaviate(**store_params)

        elif self.vector_store_type == "milvus":
            if documents:
                return Milvus.from_documents(documents=documents, **store_params)
            else:
                return Milvus(**store_params)

        elif self.vector_store_type == "qdrant":
            if documents:
                return Qdrant.from_documents(documents=documents, **store_params)
            else:
                return Qdrant(**store_params)

        elif self.vector_store_type == "opensearch":
            if documents:
                return OpenSearchVectorSearch.from_documents(documents=documents, **store_params)
            else:
                return OpenSearchVectorSearch(**store_params)

        elif self.vector_store_type == "elasticsearch":
            if documents:
                return ElasticsearchStore.from_documents(documents=documents, **store_params)
            else:
                return ElasticsearchStore(**store_params)

        else:
            raise ValueError(f"Unsupported vector store type: {self.vector_store_type}")

    async def load_documents(self, source_path: str, source_type: str = "auto", page_id: str = None):
        """
        Load documents from a file or directory.

        Args:
            source_path: Path to the file or directory
            source_type: Type of documents to load (auto, pdf, text, csv, markdown, directory)
            page_id: Optional page ID to add to document metadata

        Returns:
            List of loaded and processed documents
        """

        # Detect source type if set to auto
        if source_type == "auto":
            if os.path.isdir(source_path):
                source_type = "directory"
            else:
                ext = os.path.splitext(source_path)[1].lower()

                # Prioritize specific file types first, then fall back to markitdown
                if ext in [".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp"]:
                    source_type = "image"
                elif ext in [".mp3", ".wav", ".m4a", ".ogg", ".flac", ".aac"]:
                    source_type = "audio"
                elif ext in [".mp4", ".avi", ".mov", ".wmv", ".flv", ".webm", ".mkv"]:
                    source_type = "video"
                elif ext == ".pdf":
                    source_type = "pdf"
                elif ext == ".txt":
                    source_type = "text"
                elif ext == ".csv":
                    source_type = "csv"
                elif ext in [".md", ".markdown", ".mdx"]:
                    # Use markitdown for better markdown processing
                    source_type = "markitdown"
                else:
                    # Check if we should use markitdown for other file types
                    markitdown_converter = MarkitdownConverter()
                    supported_extensions = markitdown_converter.get_supported_extensions()

                    if ext in supported_extensions:
                        source_type = "markitdown"
                    else:
                        source_type = "text"  # Default to text

        logger.info(f"Processing file with detected source_type: {source_type}")

        # Load documents based on type
        if source_type == "markitdown":
            # Use the markitdown converter for enhanced document processing
            logger.info(f"Using MarkItDown converter for: {source_path}")
            markitdown_converter = MarkitdownConverter()
            documents = markitdown_converter.convert_file(source_path)
            logger.info(f"Loaded document with Markitdown: {source_path}")
        elif source_type == "audio" or source_type == "video":
            # Use Azure Video Indexer for audio/video files instead of MarkItDown
            try:
                from src.services.azure_video_indexer import azure_video_indexer_service

                logger.info(f"Processing {source_type} file: {os.path.basename(source_path)}")

                if not hasattr(azure_video_indexer_service, 'configured') or not azure_video_indexer_service.configured:
                    logger.warning(f"Azure Video Indexer service not configured for {source_type} file processing")

                    # Create a placeholder document instead of using MarkItDown to avoid FFmpeg errors
                    placeholder_content = f"""
Audio/Video File Processing Unavailable

File: {os.path.basename(source_path)}
Type: {source_type.upper()}
Status: Azure Video Indexer not configured

To enable audio/video transcription, configure Azure Video Indexer with:
- AZURE_VIDEO_INDEXER_ACCOUNT_ID
- AZURE_VIDEO_INDEXER_LOCATION
- Authentication method (Azure Resource Manager or static token)

This file has been indexed as a placeholder document.
                    """.strip()

                    from langchain.schema.document import Document
                    import datetime

                    document_metadata = {
                        "source": source_path,
                        "file_name": os.path.basename(source_path),
                        "file_type": source_type,
                        "processing_status": "azure_video_indexer_not_configured",
                        "transcription_service": "none",
                        "created_at": datetime.datetime.now(datetime.timezone.utc).isoformat()
                    }
                    if page_id:
                        document_metadata["pageId"] = page_id

                    document = Document(
                        page_content=placeholder_content.lower(),
                        metadata=document_metadata
                    )
                    documents = [document]
                    logger.info(f"Created placeholder document for {source_type} file")
                else:
                    logger.info(f"Azure Video Indexer service configured, processing {source_type} file")

                    # Azure Video Indexer expects URLs, not local file paths
                    # For workspace indexing, the source_path should already be a URL from blob storage
                    if source_path.startswith('http'):
                        logger.info(f"Processing {source_type} file via Azure Video Indexer")

                        # Use Azure Video Indexer for transcription
                        try:
                            if source_type == "audio":
                                # Use asynchronous processing for RAG agent (background processing)
                                result = await azure_video_indexer_service.transcribe_audio(
                                    source_path,
                                    os.path.basename(source_path),
                                    language="en-US",
                                    wait_for_completion=False  # Async processing for RAG
                                )
                            else:  # video
                                result = await azure_video_indexer_service.analyze_video(source_path, os.path.basename(source_path))

                            if result.get("error"):
                                logger.error(f"Azure Video Indexer processing failed: {result['error']}")
                            else:
                                logger.info(f"Azure Video Indexer processing initiated successfully")
                        except Exception as service_error:
                            logger.error(f"Azure Video Indexer service call failed: {service_error}")
                            import traceback
                            logger.error(f"Service error traceback: {traceback.format_exc()}")
                            result = {"error": f"Service call failed: {str(service_error)}"}
                    else:
                        logger.warning(f"Invalid path type for Azure Video Indexer - requires HTTP URL, got local path: {source_path}")

                        # Create an error document for local file paths
                        error_content = f"""
Audio/Video File Processing Failed

File: {os.path.basename(source_path)}
Type: {source_type.upper()}
Error: Local file path not supported

Technical Details:
- Azure Video Indexer requires HTTP/HTTPS URLs
- Local file path provided: {source_path}
- Cannot process local audio/video files
To resolve this issue:
1. Upload the file to blob storage first
2. Use the blob storage URL for indexing
3. Ensure the URL is publicly accessible or has SAS token

This file has been indexed as an error document.
                        """.strip()

                        from langchain.schema.document import Document
                        import datetime

                        document_metadata = {
                            "source": source_path,
                            "file_name": os.path.basename(source_path),
                            "file_type": source_type,
                            "processing_status": "local_path_error",
                            "transcription_service": "none",
                            "error": "Local file path not supported by Azure Video Indexer",
                            "created_at": datetime.datetime.now(datetime.timezone.utc).isoformat()
                        }
                        if page_id:
                            document_metadata["pageId"] = page_id

                        document = Document(
                            page_content=error_content.lower(),
                            metadata=document_metadata
                        )
                        documents = [document]
                        logger.info(f"Created error document for local {source_type} file")
                        return documents

                    # Process Azure Video Indexer result
                    if result.get("error"):
                        logger.error(f"Azure Video Indexer processing failed: {result['error']}")
                        # Return empty document list instead of creating error documents
                        # This prevents misleading content from being indexed
                        documents = []
                        logger.info(f"Skipped indexing for {source_type} file due to Azure Video Indexer error")
                    elif result.get("status") == "processing":
                        # Azure Video Indexer processing initiated successfully
                        # Return empty document list - actual content will be indexed via webhook when processing completes
                        logger.info(f"Azure Video Indexer processing initiated for {source_type} file. Video ID: {result.get('video_id')}")
                        logger.info(f"Background processing will continue. File will be indexed when Azure Video Indexer completes.")
                        documents = []
                    elif result.get("transcript") or result.get("visual_insights"):
                        # Synchronous processing completed (fallback case)
                        logger.info(f"Azure Video Indexer processing completed synchronously")

                        # Create document from transcription
                        transcript = result.get("transcript", "")
                        metadata_info = result.get("metadata", {})
                        visual_insights = result.get("visual_insights", {})

                        logger.info(f"Transcript extracted: {len(transcript)} characters")

                        # Combine transcript and insights for video files
                        content = transcript
                        if visual_insights and source_type == "video":
                            content += f"\n\nVisual Insights:\n{str(visual_insights)}"

                        # Create LangChain document
                        from langchain.schema.document import Document
                        import datetime

                        document_metadata = {
                            "source": source_path,
                            "file_name": os.path.basename(source_path),
                            "file_type": source_type,
                            "duration": metadata_info.get("duration", 0),
                            "language": metadata_info.get("language", "unknown"),
                            "transcription_service": "azure_video_indexer",
                            "created_at": datetime.datetime.now(datetime.timezone.utc).isoformat()
                        }
                        if page_id:
                            document_metadata["pageId"] = page_id

                        document = Document(
                            page_content=content.lower(),
                            metadata=document_metadata
                        )
                        documents = [document]
                        logger.info(f"Successfully created document from {source_type} transcription")
                    else:
                        # Unexpected result format
                        logger.warning(f"Unexpected result format from Azure Video Indexer: {result}")
                        documents = []

            except ImportError as e:
                logger.error(f"Failed to import Azure Video Indexer service: {e}")
                logger.error(f"Skipping indexing for {source_type} file due to import failure")

                # Return empty document list instead of creating error documents
                # This prevents misleading content from being indexed
                documents = []
                logger.info(f"Skipped indexing for {source_type} file due to Azure Video Indexer import failure")

            except Exception as e:
                logger.error(f"Unexpected error during {source_type} file processing: {e}")
                import traceback
                logger.error(f"Traceback: {traceback.format_exc()}")

                # Check if this is a filename length error from Azure Video Indexer
                error_message = str(e).lower()
                if "video_name_too_long" in error_message or "name" in error_message and "80" in error_message:
                    logger.error(f"Azure Video Indexer filename length error - filename exceeds 80 characters")
                elif "upload failed" in error_message or "failed to upload" in error_message:
                    logger.error(f"Azure Video Indexer upload failed - check URL accessibility and service configuration")

                # Return empty document list instead of creating error documents
                # This prevents misleading content from being indexed
                documents = []
                logger.info(f"Skipped indexing for {source_type} file due to processing error: {type(e).__name__}")
        elif source_type == "image":
            try:
                # Create a prompt for image description
                # Load the image
                img = Image.open(source_path)

                # Convert image to base64 for API calls if needed
                buffered = io.BytesIO()
                img.save(buffered, format=img.format or "JPEG")
                img_str = base64.b64encode(buffered.getvalue()).decode()
                img_format = (img.format or "png").lower()

                # Configure the client based on the model type
                endpoint = f"{os.getenv('AZURE_VISION_API_ENDPOINT')}/openai/deployments/{os.getenv('AZURE_VISION_MODEL')}/chat/completions?api-version=2024-02-15-preview"
                headers = {
                    "Content-Type": "application/json",
                    "api-key": os.getenv("DEEPSEEK_API_KEY")
                }
                messages = [
                    {"role": "system", "content":[
                        {"type": "text", "text": "You are an AI assistant that provides detailed descriptions of images."}
                    ]},
                    {
                        "role": "user",
                        "content": [
                            {"type": "text", "text": "Provide a detailed description of this image. Include all visible elements, text, and context."},
                            {"type": "image_url", "image_url": {"url":f"data:image/{img_format};base64,{img_str}"}}
                        ]
                    }
                ]

                data = {
                    "messages": messages,
                    "model": os.getenv('AZURE_VISION_MODEL',"mistral-medium-2505"),
                    "temperature": 0.7,
                }

                response = requests.post(endpoint, headers=headers, json=data)

                description = response.json()["choices"][0]["message"]["content"]
                from langchain.schema.document import Document
                import datetime

                document_metadata = {
                    "source": source_path,
                    "file_type": "image",
                    "file_name": os.path.basename(source_path),
                    "description_type": "llm_generated",
                    "created_at": datetime.datetime.now(datetime.timezone.utc).isoformat()
                }
                if page_id:
                    document_metadata["pageId"] = page_id

                documents = [
                    Document(
                        page_content=description.lower(),
                        metadata=document_metadata
                    )
                ]
            except Exception as llm_error:
                logger.error(f"Error generating image description with LLM: {llm_error}")
                # Create a minimal document with error information
                from langchain.schema.document import Document
                import datetime

                document_metadata = {
                    "source": source_path,
                    "file_type": "image",
                    "file_name": os.path.basename(source_path),
                    "description_type": "fallback",
                    "created_at": datetime.datetime.now(datetime.timezone.utc).isoformat()
                }
                if page_id:
                    document_metadata["pageId"] = page_id

                documents = [
                    Document(
                        page_content=f"This is an image file named {os.path.basename(source_path)}. The system was unable to extract detailed information from this image.",
                        metadata=document_metadata
                    )
                ]

        elif source_type == "directory":
            loader = DirectoryLoader(source_path, show_progress=True)
            documents = loader.load()
        elif source_type == "pdf":
            loader = PyPDFLoader(source_path)
            documents = loader.load()
        elif source_type == "text":
            loader = TextLoader(source_path)
            documents = loader.load()
        elif source_type == "csv":
            loader = CSVLoader(source_path)
            documents = loader.load()
        elif source_type == "markdown":
            loader = UnstructuredMarkdownLoader(source_path)
            documents = loader.load()
        elif source_type == "excel":
            # Use MarkItDown for Excel files (.xlsx, .xls)
            logger.info(f"Using MarkItDown converter for Excel file: {source_path}")
            markitdown_converter = MarkitdownConverter()
            documents = markitdown_converter.convert_file(source_path)
            logger.info(f"Loaded Excel document with MarkItDown: {source_path}")
        elif source_type == "word":
            # Use MarkItDown for Word files (.docx, .doc)
            logger.info(f"Using MarkItDown converter for Word file: {source_path}")
            markitdown_converter = MarkitdownConverter()
            documents = markitdown_converter.convert_file(source_path)
            logger.info(f"Loaded Word document with MarkItDown: {source_path}")
        elif source_type == "powerpoint":
            # Use MarkItDown for PowerPoint files (.pptx, .ppt)
            logger.info(f"Using MarkItDown converter for PowerPoint file: {source_path}")
            markitdown_converter = MarkitdownConverter()
            documents = markitdown_converter.convert_file(source_path)
            logger.info(f"Loaded PowerPoint document with MarkItDown: {source_path}")
        else:
            raise ValueError(f"Unsupported document type: {source_type}")

        logger.info(f"Loaded {len(documents)} documents")

        # Add pageId and created_at to all documents if provided
        import datetime
        current_time = datetime.datetime.now(datetime.timezone.utc).isoformat()

        if page_id:
            for doc in documents:
                if not hasattr(doc, 'metadata'):
                    doc.metadata = {}
                doc.metadata["pageId"] = page_id
                doc.metadata["source"] = source_path
                doc.metadata["created_at"] = current_time
            logger.info(f"Added pageId '{page_id}' and created_at to {len(documents)} documents")
        else:
            # Add created_at even if no pageId
            for doc in documents:
                if not hasattr(doc, 'metadata'):
                    doc.metadata = {}
                doc.metadata["source"] = source_path
                doc.metadata["created_at"] = current_time
            logger.info(f"Added created_at to {len(documents)} documents")

        split_docs = self.text_splitter.split_documents(documents)
        logger.info(f"Split into {len(split_docs)} chunks")

        return split_docs

    def index_documents(self, documents):
        """
        Index the provided documents in the vector store.

        Args:
            documents: List of documents to index

        Returns:
            The initialized vector store
        """
        self.vector_store = self._initialize_vector_store(documents)

        # Initialize QA chain
        retriever = self.vector_store.as_retriever(search_kwargs={"k": self.top_k})

        # Create a custom prompt template for better retrieval
        template = """
            You are an AI assistant designed to answer user questions based on the provided context.

            Context:
            {context}

            Question:
            {question}

            Instructions:
            - Prioritize information from the provided context to answer the question.
            - If the context includes web search results, use this information to provide up-to-date answers about current events, news, or general knowledge questions.
            - When using web search results, cite your sources using the format [W1], [W2], etc. corresponding to the numbering in the web search results.
            - If the question is about recent events or current information and web search results are available, rely primarily on those results.
            - Always respond in the **same language** as the user's question, even if the context is in a different language. Translate the context internally as needed.
            - Format responses using Markdown for readability:
            - `Code blocks` for technical content
            - Use lists and headings for structure and clarity.

        """

        PROMPT = PromptTemplate(
            template=template,
            input_variables=["context", "question"]
        )

        self.qa_chain = RetrievalQA.from_chain_type(
            llm=self.llm,
            chain_type="stuff",
            retriever=retriever,
            chain_type_kwargs={"prompt": PROMPT}
        )

        logger.info("Indexing complete, QA chain initialized")
        return self.vector_store
    
    def delete_documents(self, file_id=None, document_ids=None, delete_all=False, collection_name=None,
                        page_id=None, workspace_id=None, tenant_id=None):
        """Delete documents from the vector store.

        Args:
            file_id: File ID to delete documents for (for MongoDB).
            document_ids: List of document IDs to delete. If None and delete_all is True, deletes all documents.
            delete_all: If True, deletes all documents from the store.
            collection_name: Optional name of collection to delete from (for MongoDB).
            page_id: Optional page ID to delete documents for (for unified collection).
            workspace_id: Optional workspace ID to delete documents for (for unified collection).
            tenant_id: Optional tenant ID to delete documents for (for unified collection).

        Returns:
            bool: True if deletion was successful, False otherwise.
        """
        try:
            # Initialize vector store if not already initialized
            if not self.vector_store:
                logger.info("Vector store not initialized, attempting to initialize...")
                self.vector_store = self._initialize_vector_store(collection_name=collection_name)
                if not self.vector_store:
                    logger.error("Failed to initialize vector store")
                    return False

            if self.vector_store_type == "mongodb":
                client = MongoClient(self.vector_store_config["connection_string"])
                db = client[self.vector_store_config["db_name"]]

                # Use unified vectors collection if available, otherwise fall back to collection_name
                target_collection_name = "vectors" if "vectors" in db.list_collection_names() else collection_name

                if target_collection_name:
                    collection = db[target_collection_name]

                    # Build deletion filter based on provided parameters
                    delete_filter = {}

                    if delete_all:
                        # Delete all documents
                        delete_filter = {}
                    else:
                        # Build specific filter based on provided parameters
                        if file_id:
                            delete_filter["fileId"] = file_id
                        if page_id:
                            delete_filter["pageId"] = page_id
                        if workspace_id:
                            delete_filter["workspaceId"] = workspace_id
                        if tenant_id:
                            delete_filter["tenantId"] = tenant_id

                    # Perform deletion
                    if delete_filter or delete_all:
                        result = collection.delete_many(delete_filter)
                        deleted_count = result.deleted_count
                        logger.info(f"Deleted {deleted_count} documents from {target_collection_name} collection with filter: {delete_filter}")
                        return True
                    else:
                        logger.warning("No deletion criteria specified")
                        return False
                else:
                    logger.error("No collection specified for document deletion")
                    return False

            elif self.vector_store_type == "chroma":
                if delete_all:
                    self.vector_store._collection.delete()
                elif file_id:
                    self.vector_store._collection.delete(ids=file_id)
                return True

            elif self.vector_store_type == "faiss":
                if delete_all:
                    self.vector_store.reset()
                elif document_ids:
                    self.vector_store.delete(ids=document_ids)
                return True

            elif self.vector_store_type == "pinecone":
                if delete_all:
                    self.vector_store.delete_all()
                elif document_ids:
                    self.vector_store.delete(ids=document_ids)
                return True

            elif self.vector_store_type == "weaviate":
                if delete_all:
                    self.vector_store.delete_collection()
                elif document_ids:
                    for doc_id in document_ids:
                        self.vector_store.delete(doc_id)
                return True

            elif self.vector_store_type == "milvus":
                if delete_all:
                    self.vector_store.delete_collection()
                elif document_ids:
                    self.vector_store.delete(ids=document_ids)
                return True

            elif self.vector_store_type == "qdrant":
                if delete_all:
                    self.vector_store.delete_collection()
                elif document_ids:
                    self.vector_store.delete(points_selector=document_ids)
                return True

            elif self.vector_store_type == "opensearch":
                if delete_all:
                    self.vector_store.delete_index()
                elif document_ids:
                    for doc_id in document_ids:
                        self.vector_store.delete_document(doc_id)
                return True

            elif self.vector_store_type == "elasticsearch":
                if delete_all:
                    self.vector_store.delete_index()
                elif document_ids:
                    for doc_id in document_ids:
                        self.vector_store.delete_document(doc_id)
                return True

            else:
                logger.error(f"Unsupported vector store type for deletion: {self.vector_store_type}")
                return False

        except Exception as e:
            logger.error(f"Error deleting documents: {e}")
            return False

    def delete_documents_by_page_id(self, page_id: str, workspace_id: str = None, tenant_id: str = None):
        """Delete all documents associated with a specific page ID.

        Args:
            page_id: The page ID to delete documents for
            workspace_id: Optional workspace ID to limit deletion scope
            tenant_id: Optional tenant ID to limit deletion scope

        Returns:
            bool: True if deletion was successful, False otherwise
        """
        logger.info(f"Deleting documents for page_id: {page_id}")
        return self.delete_documents(
            page_id=page_id,
            workspace_id=workspace_id,
            tenant_id=tenant_id
        )

    def delete_documents_by_file_id(self, file_id: str, workspace_id: str = None, tenant_id: str = None):
        """Delete all documents associated with a specific file ID.

        Args:
            file_id: The file ID to delete documents for
            workspace_id: Optional workspace ID to limit deletion scope
            tenant_id: Optional tenant ID to limit deletion scope

        Returns:
            bool: True if deletion was successful, False otherwise
        """
        logger.info(f"Deleting documents for file_id: {file_id}")
        return self.delete_documents(
            file_id=file_id,
            workspace_id=workspace_id,
            tenant_id=tenant_id
        )

    def delete_documents_by_workspace(self, workspace_id: str, tenant_id: str = None):
        """Delete all documents associated with a specific workspace.

        Args:
            workspace_id: The workspace ID to delete documents for
            tenant_id: Optional tenant ID to limit deletion scope

        Returns:
            bool: True if deletion was successful, False otherwise
        """
        logger.info(f"Deleting documents for workspace_id: {workspace_id}")
        return self.delete_documents(
            workspace_id=workspace_id,
            tenant_id=tenant_id
        )
