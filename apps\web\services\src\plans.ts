import axios from "axios";
import { apiUrl } from "..";

export interface Plan {
  id: string;
  name: string;
  type: "STARTER" | "BUSINESS" | "CUSTOM" | "ENTERPRISE" | "SOLO";
  description: string | null;
  stripePriceId?: string | null; // Monthly price ID for the base plan
  stripeYearlyPriceId?: string | null; // Yearly price ID for the base plan
  includedUsers: number;
  additionalUserFee: number; // For display purposes only
  stripeUserPriceId?: string | null; // Monthly price ID for additional users
  stripeUserYearlyPriceId?: string | null; // Yearly price ID for additional users
  vectorStoreGB: number;
  isActive: boolean;
  price?: number | null; // Base price of the plan (for display purposes only)
  createdAt: string;
  updatedAt: string;
}

export interface PlansResponse {
  plans: Plan[];
}

export interface PlanResponse {
  plan: Plan;
}

// Get all active plans
export const getPlans = async (): Promise<Plan[]> => {
  try {
    const response = await axios.get<PlansResponse>(`${apiUrl}/plans`);
    return response.data.plans;
  } catch (error) {
    console.error("Error fetching plans:", error);
    throw error;
  }
};

// Get a specific plan by ID
export const getPlan = async (planId: string): Promise<Plan> => {
  try {
    const response = await axios.get<PlanResponse>(`${apiUrl}/plans/${planId}`);
    return response.data.plan;
  } catch (error) {
    console.error(`Error fetching plan ${planId}:`, error);
    throw error;
  }
};

// Create a new plan (admin only)
export const createPlan = async (
  planData: Omit<Plan, "id" | "createdAt" | "updatedAt">
): Promise<Plan> => {
  try {
    const response = await axios.post<PlanResponse>(
      `${apiUrl}/plans`,
      planData
    );
    return response.data.plan;
  } catch (error) {
    console.error("Error creating plan:", error);
    throw error;
  }
};

// Update a plan (admin only)
export const updatePlan = async (
  planId: string,
  planData: Partial<Plan>
): Promise<Plan> => {
  try {
    const response = await axios.put<PlanResponse>(
      `${apiUrl}/plans/${planId}`,
      planData
    );
    return response.data.plan;
  } catch (error) {
    console.error(`Error updating plan ${planId}:`, error);
    throw error;
  }
};

// Delete a plan (admin only)
export const deletePlan = async (planId: string): Promise<void> => {
  try {
    await axios.delete(`${apiUrl}/plans/${planId}`);
  } catch (error) {
    console.error(`Error deleting plan ${planId}:`, error);
    throw error;
  }
};
