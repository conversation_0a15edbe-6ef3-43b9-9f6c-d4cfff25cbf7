resource "azurerm_mongo_cluster" "cosmos" {
  name                   = var.cosmosdb_name
  location               = var.cosmosdb_location
  resource_group_name    = var.resource_group_name
  administrator_username = var.cosmosdb_username
  administrator_password = var.cosmosdb_password
  shard_count            = "1"
  compute_tier           = var.cosmosdb_tire
  high_availability_mode = "Disabled"
  storage_size_in_gb     = var.cosmosdb_size
  version                = "7.0"

  lifecycle {
    ignore_changes = [
      preview_features, create_mode
    ]
  }
}


# resource "azurerm_mongo_cluster" "geo_replica" {
#   name                = "${var.cosmosdb_name}-geo-replica"
#   resource_group_name = var.resource_group_name
#   location            = var.static_app_location
#   source_server_id    = azurerm_mongo_cluster.cosmos.id
#   source_location     = azurerm_mongo_cluster.cosmos.location
#   create_mode         = "GeoReplica"

#   timeouts {
#     create = "60m"
#     update = "60m"
#   }
#   lifecycle {
#     ignore_changes = ["preview_features", "shard_count", "storage_size_in_gb", "compute_tier", "version"]
#   }
#}

# resource "azurerm_cosmosdb_mongo_database" "db" {
#   name                = var.database_name
#   resource_group_name = var.resource_group_name
#   account_name        = azurerm_mongo_cluster.cosmos.name

#   depends_on = [
#     azurerm_mongo_cluster.cosmos
#   ]
# }
