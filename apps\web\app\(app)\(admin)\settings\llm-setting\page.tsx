import LLMSettingsPage from "@/components/wrapper-screens/organization-settings/llm/llm-settings-page";
import { getLLMSettings } from "@/services";
import { cookies } from "next/headers";

export default async function Page() {
  const tenantId = cookies().get("currentOrganizationId")?.value ?? "";

  try {
    // Handle case where folder data is not found
    const llmSetting = await getLLMSettings({ tenantId });
    return <LLMSettingsPage settings={llmSetting?.settings} />;
  } catch (error) {
    console.error("Error loading folder:", error);
  }
}
