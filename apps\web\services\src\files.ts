import { apiUrl, fetchJson } from "..";

export const getFile = async (
  tenantId,
  userId,
  fileId = null,
  workspaceSlug = null
) => {
  if (fileId) {
    const response = await fetchJson(
      `${apiUrl}/files?id=${fileId}`,
      userId,
      tenantId,
      workspaceSlug
    );
    return response;
  }
  const response = await fetchJson(
    `${apiUrl}/files`,
    userId,
    tenantId,
    workspaceSlug
  );
  return response;
};

export const deleteFile = async (fileId, tenantId, userId, workspaceSlug) => {
  try {
    const url = `${apiUrl}/files?id=${fileId}`;
    const headers = {
      "x-user-id": userId,
      "x-tenant-id": tenantId,
    };

    // Add workspace slug to headers if available
    if (workspaceSlug) {
      headers["x-workspace-slug"] = workspaceSlug;
    }

    const response = await fetch(url, {
      method: "DELETE",
      headers,
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Network response was not ok");
    }
    return await response.json();
  } catch (error) {
    console.log({ "delete files api error": error });
    return { error: error.message || "Error deleting files" };
  }
};

export const updateFile = async (data, tenantId, userId, workspaceSlug) => {
  try {
    const headers = {
      "Content-Type": "application/json",
      "x-user-id": userId,
      "x-tenant-id": tenantId,
    };

    // Add workspace slug to headers if available
    if (workspaceSlug) {
      headers["x-workspace-slug"] = workspaceSlug;
    } else if (data.workspaceSlug) {
      headers["x-workspace-slug"] = data.workspaceSlug;
    }

    const response = await fetch(`${apiUrl}/files`, {
      method: "PUT",
      headers,
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error("Network response was not ok");
    }
    return await response.json();
  } catch (error) {
    console.log({ "Update file api calling error": error });
    return { error: "Error Updating The file" };
  }
};

export const createFile = async (data, tenantId, userId, workspaceSlug) => {
  try {
    const headers = {
      "Content-Type": "application/json",
      "x-user-id": userId,
      "x-tenant-id": tenantId,
    };

    // Add workspace slug to headers if available
    if (workspaceSlug) {
      headers["x-workspace-slug"] = workspaceSlug;
    } else if (data.workspaceSlug) {
      headers["x-workspace-slug"] = data.workspaceSlug;
    }

    const response = await fetch(`${apiUrl}/files`, {
      method: "POST",
      headers,
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error("Network response was not ok");
    }
    return await response.json();
  } catch (error) {
    console.log({ "Create files api calling error": error });
    return { error: "Error Creating The File" };
  }
};
