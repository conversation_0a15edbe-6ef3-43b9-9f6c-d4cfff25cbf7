"use client";

import React, { createContext, useContext, useState, ReactNode } from "react";
import { addDays } from "date-fns";

interface DateRange {
  from: Date;
  to: Date;
}

interface MetricsContextType {
  dateRange: DateRange;
  setDateRange: (range: DateRange) => void;
  isLoading: boolean;
  setIsLoading: (loading: boolean) => void;
}

const MetricsContext = createContext<MetricsContextType | undefined>(undefined);

export function MetricsProvider({ children }: { children: ReactNode }) {
  const [dateRange, setDateRange] = useState<DateRange>({
    from: addDays(new Date(), -30),
    to: new Date(),
  });
  const [isLoading, setIsLoading] = useState(false);

  return (
    <MetricsContext.Provider
      value={{
        dateRange,
        setDateRange,
        isLoading,
        setIsLoading,
      }}
    >
      {children}
    </MetricsContext.Provider>
  );
}

export function useMetrics() {
  const context = useContext(MetricsContext);
  if (context === undefined) {
    throw new Error("useMetrics must be used within a MetricsProvider");
  }
  return context;
}

// Helper function to convert date range to API parameters
export function dateRangeToParams(dateRange: DateRange): string {
  const { from, to } = dateRange;
  return `from=${from.toISOString()}&to=${to.toISOString()}`;
}

// Helper function to format date range for display
export function formatDateRange(dateRange: DateRange): string {
  const { from, to } = dateRange;
  return `${from.toLocaleDateString()} - ${to.toLocaleDateString()}`;
}

// Helper function to convert date range to period parameter for API calls
export function dateRangeToPeriod(dateRange: DateRange): string {
  const { from, to } = dateRange;
  const diffTime = Math.abs(to.getTime() - from.getTime());
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  // Determine appropriate period based on date range
  if (diffDays <= 7) {
    return "7days";
  } else if (diffDays <= 30) {
    return "30days";
  } else if (diffDays <= 90) {
    return "90days";
  } else if (diffDays <= 180) {
    return "6months";
  } else if (diffDays <= 365) {
    return "12months";
  } else {
    return "12months"; // Default to 12 months for very long ranges
  }
}
