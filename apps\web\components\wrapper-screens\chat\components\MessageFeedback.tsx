import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { ThumbsUp, ThumbsDown, RefreshCw } from "lucide-react";
import { Message } from "../types";
import { useLanguage } from "@/lib/language-context";

interface MessageFeedbackProps {
  displayedMessage: Message;
  isDeepAnswer: boolean;
  index: number;
  processedMessages: Message[];
  handleFeedback: (messageIndex: number, feedback: "like" | "dislike") => void;
  handleRegenerate: (messageIndex?: number) => void;
}

export const MessageFeedback: React.FC<MessageFeedbackProps> = ({
  displayedMessage,
  isDeepAnswer,
  index,
  processedMessages,
  handleFeedback,
  handleRegenerate,
}) => {
  const { t } = useLanguage();

  return (
    <div className="flex items-center gap-1">
      <Button
        variant="ghost"
        size="icon"
        className={`h-7 w-7 rounded-full ${
          displayedMessage.metadata?.feedback === "like"
            ? "bg-green-100 text-green-600"
            : ""
        }`}
        onClick={() => handleFeedback(index, "like")}
        title={t("chat.like")}
      >
        <ThumbsUp className="h-3.5 w-3.5" />
      </Button>
      <Button
        variant="ghost"
        size="icon"
        className={`h-7 w-7 rounded-full ${
          displayedMessage.metadata?.feedback === "dislike"
            ? "bg-red-100 text-red-600"
            : ""
        }`}
        onClick={() => handleFeedback(index, "dislike")}
        title={t("chat.dislike")}
      >
        <ThumbsDown className="h-3.5 w-3.5" />
      </Button>
      {!isDeepAnswer ? (
        <Button
          variant="ghost"
          size="icon"
          className="h-7 w-7 rounded-full"
          onClick={() => {
            // Allow regenerating any assistant message by passing the index
            handleRegenerate(index);
          }}
          title={t("chat.regenerate")}
        >
          <RefreshCw className="h-3.5 w-3.5" />
        </Button>
      ) : null}
    </div>
  );
};
