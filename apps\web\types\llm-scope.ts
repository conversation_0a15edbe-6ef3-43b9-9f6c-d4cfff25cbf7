export enum LLMScope {
  INTERNAL_ONLY = "INTERNAL_ONLY",
  EXTERNAL_ONLY = "EXTERNAL_ONLY",
  MCP_ONLY = "MCP_ONLY",
  HYBRID = "HYBRID",
  FULL_ACCESS = "FULL_ACCESS",
}

export interface LLMScopeSettings {
  id: string;
  llmScope: string[];
  updatedAt: string;
  updatedBy?: string;
}

export interface LLMScopeUpdateRequest {
  llmScope: string[];
}

export interface LLMScopeCapabilities {
  canAccessInternalDocs: boolean;
  canAccessWebSearch: boolean;
  canAccessMCPServers: boolean;
  canDeepSearch: boolean;
}

export const LLM_SCOPE_LABELS = {
  [LLMScope.INTERNAL_ONLY]: "Internal Only",
  [LLMScope.EXTERNAL_ONLY]: "External Only",
  [LLMScope.MCP_ONLY]: "MCP Tools Only",
  [LLMScope.HYBRID]: "Hybrid (Internal + Web)",
  [LLMScope.FULL_ACCESS]: "Full LLM Access",
} as const;

export const LLM_SCOPE_DESCRIPTIONS = {
  [LLMScope.INTERNAL_ONLY]:
    "Access only to internal documents and knowledge base. No external web search.",
  [LLMScope.EXTERNAL_ONLY]:
    "Access only to external web search. No internal documents.",
  [LLMScope.MCP_ONLY]:
    "Access only to MCP (Model Context Protocol) tools and external services. No internal documents or web search.",
  [LLMScope.HYBRID]:
    "Access to internal documents, external web search, and MCP servers. No advanced AI features.",
  [LLMScope.FULL_ACCESS]:
    "Full LLM capabilities including deep research, advanced AI features, and all data sources.",
} as const;

export function getLLMScopeCapabilities(
  scopeArray: string[]
): LLMScopeCapabilities {
  // If only INTERNAL_ONLY, return basic capabilities
  if (scopeArray.length === 1 && scopeArray[0] === "INTERNAL_ONLY") {
    return {
      canAccessInternalDocs: true,
      canAccessWebSearch: false,
      canAccessMCPServers: false,
      canDeepSearch: false,
    };
  }

  // For array format, check what capabilities are included
  const hasWeb =
    scopeArray.includes("web") || scopeArray.includes("EXTERNAL_ONLY");
  const hasMcp = scopeArray.includes("mcp") || scopeArray.includes("MCP_ONLY");

  const hasDeepSearch =
    scopeArray.includes("deep_search") || scopeArray.includes("FULL_ACCESS");

  return {
    canAccessInternalDocs: true, // Always true unless explicitly disabled
    canAccessWebSearch: hasWeb,
    canAccessMCPServers: hasMcp,
    canDeepSearch: hasDeepSearch,
  };
}
