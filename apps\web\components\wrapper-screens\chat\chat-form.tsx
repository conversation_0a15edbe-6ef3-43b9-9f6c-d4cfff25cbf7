"use client";

import React, { useState, useRef, useEffect } from "react";
import { useRouter } from "next/navigation";
import { MessageLoader } from "@/components/ui/message-loader";
import { CitationModal } from "./citation-modal";
import { useSidebar } from "@/components/layouts/sidebar";
import { useLanguage } from "@/lib/language-context";
import { useIsMobile } from "@/hooks/use-mobile";
import { addAuthHeaders } from "@/lib/api/auth-token";
import { updateChat } from "@/services/src/chat";
import { createMessage } from "@/services/src/message";
import { workspaceChatService } from "@/services/workspace-chat";
import {
  copilotKitChatService,
  CopilotKitChatQuery,
} from "@/services/copilotkit-chat";
import { ChatMessage } from "./components/ChatMessage";
import { ChatInputForm } from "./components/ChatInputForm";
import { ChatFloatingInfoPanel } from "./components/ChatHeaderVariants";
import { useCarouselState } from "./hooks/useCarouselState";
import { useMessageRegeneration } from "./hooks/useMessageRegeneration";
import { useMessageFeedback } from "./hooks/useMessageFeedback";
import { useMessageEdit } from "./hooks/useMessageEdit";
import { processMessages } from "./utils/message-utils";
import { ChatFormProps, Message, Source, ImageAttachment } from "./types";
import { getLLMScopeCapabilities } from "@/types/llm-scope";
import toast from "react-hot-toast";

export function ChatForm({
  userId,
  chats,
  tenantId,
  userName,
  settings,
}: ChatFormProps) {
  const router = useRouter();
  const { state } = useSidebar(); // Get sidebar state
  const { t, language } = useLanguage();
  const isMobile = useIsMobile(); // Check if device is mobile
  const [chatId, setChatId] = useState<string>(chats?.id ?? "");
  const [chatTitle, setChatTitle] = useState<string>(chats?.title || "");
  const [imageContext, setImageContext] = useState<string>(
    chats?.messages?.[0]?.metadata?.imageContext || ""
  );
  const [audioContext, setAudioContext] = useState<string>(
    chats?.messages?.[0]?.metadata?.audioContext || ""
  );
  const [videoContext, setVideoContext] = useState<string>(
    (chats?.messages?.[0]?.metadata as any)?.videoContext || ""
  );
  const [input, setInput] = useState("");
  const [headers, setHeaders] = useState({});
  const [messages, setMessages] = useState<Message[]>(chats?.messages ?? []);
  const [selectedSource, setSelectedSource] = useState<Source | null>(null);
  const [isCitationModalOpen, setIsCitationModalOpen] = useState(false);
  const [queryStatus, setQueryStatus] = useState("");

  // Web search state
  const [includeWebResults, setIncludeWebResults] = useState(false);
  const [webSearchLimitExceeded, setWebSearchLimitExceeded] = useState(false);

  // LLM Scope state
  const [llmScope, setLlmScope] = useState<string[]>(
    settings?.llmScope ?? ["INTERNAL_ONLY"]
  );
  const llmCapabilities = getLLMScopeCapabilities(llmScope);

  // Search mode state
  type SearchMode =
    | "internal"
    | "web"
    | "hybrid"
    | "deep_research"
    | "mcp"
    | "";
  const [selectedSearchMode, setSelectedSearchMode] = useState<SearchMode>(
    (chats?.messages?.[0]?.metadata?.searchMode as SearchMode) || ""
  );

  // Image upload state
  const [selectedImages, setSelectedImages] = useState<ImageAttachment[]>([]);

  // Reference to the message container and scroll container
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  // Auto-scroll state management
  const [isUserInteracting, setIsUserInteracting] = useState(false);
  const [shouldAutoScroll, setShouldAutoScroll] = useState(true);
  const userInteractionTimeoutRef = useRef<ReturnType<
    typeof setTimeout
  > | null>(null);

  // Save messages to local storage when they change
  useEffect(() => {
    if (chatId && messages.length > 0) {
      try {
        localStorage.setItem(`chat_${chatId}`, JSON.stringify(messages));
      } catch (error) {
        console.error("Error saving messages to local storage:", error);
      }
    }
  }, [messages, chatId]);

  // Load messages from local storage on initial load if not provided from server
  useEffect(() => {
    if (chatId && (!chats?.messages || chats.messages.length === 0)) {
      try {
        const savedMessages = localStorage.getItem(`chat_${chatId}`);
        if (savedMessages) {
          setMessages(JSON.parse(savedMessages));

          // Auto-scroll to bottom when messages are loaded from localStorage
          setTimeout(() => {
            messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
          }, 200);
        }
      } catch (error) {
        console.error("Error loading messages from local storage:", error);
      }
    }
  }, [chatId, chats?.messages]);

  // Process messages from database to extract images from metadata
  useEffect(() => {
    if (chats?.messages && chats.messages.length > 0) {
      const processedMessages = chats.messages.map((message: any) => ({
        ...message,
        images: message.metadata?.images || message.images || undefined,
      }));
      setMessages(processedMessages);

      // Auto-scroll to bottom when messages are loaded from server
      setTimeout(() => {
        messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
      }, 200);
    }
  }, [chats?.messages]);

  // Process the messages to group regenerated messages with their originals
  const processedMessages = processMessages(messages);

  // Use our custom hooks
  const { displayIndicesRef, updateDisplayIndex, isManuallySetIndex } =
    useCarouselState(processedMessages);

  const { handleRegenerate } = useMessageRegeneration(
    processedMessages,
    setMessages,
    chatId,
    userId,
    tenantId,
    userName,
    headers,
    includeWebResults,
    updateDisplayIndex,
    imageContext,
    selectedSearchMode
  );

  const { handleFeedback } = useMessageFeedback(
    processedMessages,
    setMessages,
    chatId,
    displayIndicesRef
  );
  const {
    startEdit,
    cancelEdit,
    saveEdit,
    updateEditContent,
    canEdit,
    isMessageEditing,
    getEditContent,
    isLoading: isEditLoading,
  } = useMessageEdit(
    messages,
    setMessages,
    chatId,
    userId,
    tenantId,
    userName,
    headers,
    includeWebResults,
    updateDisplayIndex,
    imageContext,
    selectedSearchMode
  );

  // Function to toggle citation accordion
  const toggleCitationAccordion = () => {
    // This is now handled within the MessageCitations component
    // No need to track expanded state at this level
  };

  // Load LLM scope settings
  useEffect(() => {
    try {
      if (settings) {
        setLlmScope(settings.llmScope);
      }
    } catch (error) {
      setLlmScope(["INTERNAL_ONLY"]);
    }
  }, [settings]);

  // Set up authentication headers
  useEffect(() => {
    getHeaders();
  }, []);

  const getHeaders = async () => {
    const headers = await addAuthHeaders({
      "Content-Type": "application/json",
    });
    setHeaders(headers);
  };

  // CopilotKit chat state
  const [isLoading, setIsLoading] = useState(false);
  const [status, setStatus] = useState<"idle" | "streaming" | "submitted">(
    "idle"
  );

  // Auto-scroll during streaming, but stop if user interacts
  useEffect(() => {
    if (
      status === "streaming" &&
      shouldAutoScroll &&
      !isUserInteracting &&
      isNearBottom()
    ) {
      // Use a small delay to ensure DOM has updated
      const timeoutId = setTimeout(() => {
        messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
      }, 50);

      return () => clearTimeout(timeoutId);
    }
  }, [messages, status, shouldAutoScroll, isUserInteracting]);

  // Reset auto-scroll when streaming starts
  useEffect(() => {
    if (status === "streaming") {
      setShouldAutoScroll(true);
      setIsUserInteracting(false);
    }
  }, [status]);

  // Auto-scroll to bottom when page loads/reloads and messages exist
  useEffect(() => {
    if (messages.length > 0) {
      // Use a timeout to ensure DOM is fully rendered
      const timeoutId = setTimeout(() => {
        messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
      }, 100);

      return () => clearTimeout(timeoutId);
    }
  }, [messages.length > 0]); // Only trigger when messages first load

  // Check if user is near bottom of scroll container
  const isNearBottom = () => {
    if (!scrollContainerRef.current) return true;

    const { scrollTop, scrollHeight, clientHeight } =
      scrollContainerRef.current;
    const threshold = 100; // pixels from bottom
    return scrollHeight - scrollTop - clientHeight < threshold;
  };

  // Handle scroll interaction - only stop auto-scroll if user scrolls up significantly
  const handleScrollInteraction = () => {
    if (status === "streaming" && !isNearBottom()) {
      setIsUserInteracting(true);
      setShouldAutoScroll(false);

      // Clear any existing timeout
      if (userInteractionTimeoutRef.current) {
        clearTimeout(userInteractionTimeoutRef.current);
      }
    }
  };

  // Handle other user interactions (clicks, touches, etc.)
  const handleUserInteraction = () => {
    if (status === "streaming") {
      setIsUserInteracting(true);
      setShouldAutoScroll(false);

      // Clear any existing timeout
      if (userInteractionTimeoutRef.current) {
        clearTimeout(userInteractionTimeoutRef.current);
      }
    }
  };

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (userInteractionTimeoutRef.current) {
        clearTimeout(userInteractionTimeoutRef.current);
      }
    };
  }, []);

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setInput(e.target.value);
  };

  // Handle form submission with CopilotKit
  const handleAISubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    if (!input.trim() || isLoading) return;
    // Store current input and images before clearing
    const currentInput = input.trim();
    const currentImages = [...selectedImages];

    try {
      // Get conversation context for better continuity
      const getConversationContext = () => {
        // Get the last few messages for context (max 2 exchanges)
        const recentMessages = messages.slice(-8); // Last 4 messages (2 user + 2 assistant)

        if (recentMessages.length === 0) return undefined;

        // Format as conversation history
        const conversationHistory = recentMessages
          .map(
            (msg) =>
              `${msg.role === "user" ? "User" : "Assistant"}: ${msg.content}`
          )
          .join("\n");

        return conversationHistory;
      };

      const previousMessage = getConversationContext();

      // Collect video context from all video files (including async completed ones)
      const collectVideoContext = () => {
        const videoContexts: string[] = [];

        // Add global video context if available
        if (videoContext) {
          videoContexts.push(videoContext);
        }

        // Collect video context from individual video files
        currentImages.forEach((img) => {
          if (typeof img !== "string" && img.type?.startsWith("video/")) {
            const videoCtx = (img as any).videoContext;
            if (videoCtx && !videoContexts.includes(videoCtx)) {
              videoContexts.push(videoCtx);
            }
          }
        });

        return videoContexts.join("\n\n");
      };

      const finalVideoContext = collectVideoContext();

      // Prepare CopilotKit query
      const query: CopilotKitChatQuery = {
        question: currentInput,
        stream: true,
        search_mode:
          selectedSearchMode === "deep_research"
            ? "hybrid"
            : selectedSearchMode === ""
              ? ""
              : (selectedSearchMode as "internal" | "web" | "hybrid"),
        include_web_results:
          selectedSearchMode === "hybrid" ||
          selectedSearchMode === "web" ||
          selectedSearchMode === "deep_research"
            ? true
            : false,
        images:
          currentImages.length > 0
            ? currentImages
                .map((img) =>
                  typeof img === "string" ? img : img.data || img.preview || ""
                )
                .filter(Boolean)
            : undefined,
        image_context: imageContext,
        audio_context: audioContext,
        video_context: finalVideoContext,
        previous_message: previousMessage,
        deep_answer: selectedSearchMode === "deep_research",
        language: language || "en", // Pass current language for localized responses
      };

      // Add user message to UI first
      const userMessage: Message = {
        id: "user_tempid",
        role: "user",
        content: currentInput,
        createdAt: new Date(),
        images: currentImages.length > 0 ? currentImages : undefined,
        metadata: {
          includeWebResults: includeWebResults === true,
          hasImages: currentImages.length > 0,
          imageContext,
          searchMode: selectedSearchMode,
        },
      };

      // Create assistant message placeholder
      const assistantMessage: Message = {
        id: `assistant_${Date.now()}`,
        role: "assistant",
        content: "",
        createdAt: new Date(),
        sources: [],
        metadata: {},
      };

      // Add both messages to UI immediately - user message first, then assistant
      setMessages((prev) => [...prev, userMessage, assistantMessage]);
      setStatus("streaming");

      // Clear input and images immediately after adding to UI
      setInput("");
      setSelectedImages([]);

      // Save user message to database asynchronously (non-blocking)
      if (chatId) {
        // Don't await this - let it run in background
        createMessage({
          chatId: chatId,
          userId,
          content: currentInput,
          role: "user",
          metadata: {
            includeWebResults: includeWebResults === true,
            hasImages: currentImages.length > 0,
            images: currentImages.length > 0 ? currentImages : undefined,
            imageContext,
            searchMode: selectedSearchMode,
          },
        })
          .then((userMessage) => {
            // Update the user message in the UI with its ID when it completes
            if (userMessage && !userMessage.error) {
              setMessages((prevMessages) => {
                const updatedMessages = [...prevMessages];
                // Find the user message with temp ID and update it with the real ID
                for (let i = updatedMessages.length - 1; i >= 0; i--) {
                  if (
                    updatedMessages[i].role === "user" &&
                    updatedMessages[i].id === "user_tempid"
                  ) {
                    updatedMessages[i] = {
                      ...updatedMessages[i],
                      id: userMessage.id,
                    };
                    break;
                  }
                }
                return updatedMessages;
              });
            }
          })
          .catch((error) => {
            console.error("Error saving user message:", error);
          });
      }

      // Stream the response using CopilotKit
      let fullContent = "";
      let currentSources: any[] = [];
      let currentToolsUsed: string[] = [];
      let responseTime = 0;
      let thinkingContent = "";
      let researchData: any = {};

      for await (const chunk of copilotKitChatService.streamChat(
        query,
        userId,
        tenantId,
        userName
      )) {
        if (chunk.error) {
          throw new Error(chunk.error);
        }

        if (chunk?.message) {
          setQueryStatus(chunk?.message);
        }

        // Handle different chunk types
        if (chunk.sources) {
          currentSources = chunk.sources;
        }

        if (chunk.tools_used) {
          currentToolsUsed = chunk.tools_used;
        }

        if (chunk.thinking) {
          thinkingContent = chunk.thinking;
        }

        // Handle deep research progress updates (for deep_research mode)
        if (chunk.research_data) {
          researchData = chunk.research_data;
          // Update message with research progress
          setMessages((prev) =>
            prev.map((msg) =>
              msg.id === assistantMessage.id
                ? {
                    ...msg,
                    content: chunk.message || msg.content,
                    metadata: {
                      ...msg.metadata,
                      researchProgress: {
                        phase: researchData.phase,
                        message: researchData.message,
                        progress: researchData.progress,
                        iteration: researchData.iteration,
                        confidence: researchData.confidence,
                        gaps_identified: researchData.gaps_identified,
                        research_summary: researchData.research_summary,
                        research_plan: researchData.research_plan,
                        iterations: researchData.iterations,
                      },
                    },
                  }
                : msg
            )
          );
        }

        if (chunk.answer_chunk || chunk.content) {
          fullContent += chunk.answer_chunk || chunk.content;
        } else if (chunk.answer) {
          fullContent = chunk.answer;
        }

        if (chunk.elapsed_time) {
          responseTime = chunk.elapsed_time;
        }

        // Update the assistant message with current content only if we have content
        if (fullContent || currentSources.length > 0) {
          setMessages((prev) => {
            const updated = [...prev];
            const lastMessage = updated[updated.length - 1];
            if (lastMessage && lastMessage.role === "assistant") {
              updated[updated.length - 1] = {
                ...lastMessage,
                content: fullContent,
                sources: currentSources,
                metadata: {
                  ...lastMessage.metadata,
                  tools_used: currentToolsUsed,
                  elapsed_time: responseTime,
                  thinking: thinkingContent,
                },
              };
            }
            return updated;
          });
        }

        if (chunk.done) {
          researchData = {
            ...researchData,
            research_summary: chunk.research_summary,
            confidence:
              chunk.research_summary?.final_confidence ??
              researchData.confidence,
            iterations: chunk.iterations ?? researchData.iterations,
          };

          // Final update to clear research progress and set research summary
          setMessages((prev) => {
            const updated = [...prev];
            const lastMessage = updated[updated.length - 1];
            if (lastMessage && lastMessage.role === "assistant") {
              updated[updated.length - 1] = {
                ...lastMessage,
                content: chunk.answer || fullContent,
                sources: currentSources,
                metadata: {
                  ...lastMessage.metadata,
                  tools_used: currentToolsUsed,
                  elapsed_time: responseTime,
                  thinking: thinkingContent,
                  research_summary: chunk.research_summary,
                  iterations: chunk.iterations,
                  researchProgress: undefined, // Clear progress when done
                },
              };
            }
            return updated;
          });

          // Clear status when done
          setStatus("idle");
          break;
        }
      }

      // Handle chat creation and message saving
      if (chats?.title === "New Chat") {
        try {
          // Save the complete assistant message
          const assistantMessageResponse = await createMessage({
            chatId,
            userId,
            content: fullContent,
            role: "assistant",
            metadata: {
              tools_used: currentToolsUsed,
              elapsed_time: responseTime,
              thinking: thinkingContent,
              imageContext,
              searchMode: selectedSearchMode,
              research_summary: researchData.research_summary,
              iterations: researchData.iterations,
            },
            sources: currentSources,
          });

          // Update the assistant message in the UI with its ID
          if (assistantMessageResponse && !assistantMessageResponse.error) {
            setMessages((prevMessages) => {
              const updatedMessages = [...prevMessages];
              // Update the last message (assistant) with the ID
              if (updatedMessages.length > 0) {
                updatedMessages[updatedMessages.length - 1] = {
                  ...updatedMessages[updatedMessages.length - 1],
                  id: assistantMessageResponse.id,
                };
              }
              return updatedMessages;
            });
          }
          const title = await workspaceChatService.generateTitle(
            currentInput,
            tenantId
          );
          setChatTitle(title?.title?.replaceAll('"', "") ?? "New Chat");
          // Create a new chat with a default title
          updateChat({
            id: chatId,
            userId,
            tenantId,
            title: title?.title?.replaceAll('"', "") ?? "New Chat",
          });
        } catch (error) {
          console.error("Error creating chat:", error);
        }
      } else {
        // Save the complete assistant message
        const assistantMessageResponse = await createMessage({
          chatId: chatId,
          userId,
          content: fullContent,
          role: "assistant",
          metadata: {
            tools_used: currentToolsUsed,
            elapsed_time: responseTime,
            thinking: thinkingContent,
            imageContext,

            research_summary: researchData.research_summary,
            iterations: researchData.iterations,
          },
          sources: currentSources,
        });

        // Update the assistant message in the UI with its ID
        if (assistantMessageResponse && !assistantMessageResponse.error) {
          setMessages((prevMessages) => {
            const updatedMessages = [...prevMessages];
            // Update the last message (assistant) with the ID
            if (updatedMessages.length > 0) {
              updatedMessages[updatedMessages.length - 1] = {
                ...updatedMessages[updatedMessages.length - 1],
                id: assistantMessageResponse.id,
              };
            }
            return updatedMessages;
          });
        }
      }
    } catch (error) {
      console.error("Error in CopilotKit chat:", error);
      // Show error message to user
      setMessages((prevMessages) => [
        ...prevMessages,
        {
          id: "assistant_tempid",
          role: "assistant",
          content: t("chat.errorProcessingRequest"),
        },
      ]);
    } finally {
      setIsLoading(false);
      setStatus("idle");
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    handleInputChange(e);
    setInput(e.target.value);
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      // Only submit if there's content or images
      if (input.trim() === "" && selectedImages.length === 0) return;

      // User message will be added in handleAISubmit, so just call it directly
      handleAISubmit(e as unknown as React.FormEvent<HTMLFormElement>);
    }
  };

  // Function to handle web search toggle
  const handleWebSearchToggle = async (include: boolean) => {
    if (include) {
      // Check if web search is allowed by LLM scope
      if (!llmCapabilities.canAccessWebSearch) {
        toast.error(
          "Web search is not enabled for your organization. Contact your administrator to change the LLM scope settings."
        );
        return;
      }

      try {
        // Check if the tenant has exceeded their daily web search limit
        // const result = await performWebSearch("test query", tenantId, userId);

        // if (result.limitExceeded) {
        setWebSearchLimitExceeded(true);
        // toast.error(result.limitInfo?.message || t("chat.tryAgainTomorrow"));
        return;
        // }

        // setWebSearchLimitExceeded(false);
        // setIncludeWebResults(true);
      } catch (error) {
        console.error("Error checking web search limit:", error);
        toast.error(t("chat.webSearchErrorDescription"));
        return;
      }
    } else {
      setIncludeWebResults(false);
    }
  };

  const header = (
    <header className="m-auto flex max-w-96 flex-col gap-3 sm:gap-5 text-center">
      <h1 className="text-xl sm:text-2xl font-semibold leading-none tracking-tight">
        {t("chat.askAi")}
      </h1>
    </header>
  );

  // Render the message list
  const renderMessages = () => {
    return processedMessages.map((message, index) => (
      <ChatMessage
        key={index}
        message={message}
        index={index}
        processedMessages={processedMessages}
        displayIndicesRef={displayIndicesRef}
        isLoading={isLoading}
        status={status}
        updateDisplayIndex={updateDisplayIndex}
        isManuallySetIndex={isManuallySetIndex}
        handleFeedback={handleFeedback}
        handleRegenerate={handleRegenerate}
        toggleCitationAccordion={toggleCitationAccordion}
        setSelectedSource={setSelectedSource}
        setIsCitationModalOpen={setIsCitationModalOpen}
        // Edit functionality props
        startEdit={startEdit}
        cancelEdit={cancelEdit}
        saveEdit={saveEdit}
        updateEditContent={updateEditContent}
        canEdit={canEdit}
        isMessageEditing={isMessageEditing}
        getEditContent={getEditContent}
        isEditLoading={isEditLoading}
      />
    ));
  };

  return (
    <main className="flex h-full flex-col w-full items-center border-none pt-0">
      <ChatFloatingInfoPanel
        chatId={chatId}
        chatTitle={chatTitle}
        hasMessages={processedMessages.length > 0}
        messageCount={processedMessages.length}
        lastActivity={
          processedMessages.length > 0
            ? (() => {
                const lastMessage =
                  processedMessages[processedMessages.length - 1];
                const createdAt = lastMessage?.createdAt;
                if (typeof createdAt === "string") {
                  return createdAt;
                } else if (createdAt instanceof Date) {
                  return createdAt.toISOString();
                }
                return undefined;
              })()
            : undefined
        }
      />

      <div
        className={`flex-1 mx-auto w-full max-w-[65rem] content-center px-3 sm:px-6 ${
          processedMessages.length
            ? "pt-4 sm:pt-8"
            : "pt-4 sm:pt-8 h-full justify-center"
        } pb-24`}
        onClick={handleUserInteraction}
      >
        {processedMessages.length ? (
          <div
            ref={scrollContainerRef}
            className="my-2 sm:my-4 p-2 sm:p-4 flex h-fit min-h-full flex-col gap-3 sm:gap-4 overflow-y-auto"
            onScroll={handleScrollInteraction}
            onWheel={handleUserInteraction}
            onTouchStart={handleUserInteraction}
            onTouchMove={handleUserInteraction}
            onMouseDown={handleUserInteraction}
            onKeyDown={handleUserInteraction}
          >
            {renderMessages()}
            {(status === "streaming" || status === "submitted") && (
              <div className="flex items-center gap-3">
                <MessageLoader />
                {queryStatus && (
                  <p className="text-sm text-gray-700 leading-relaxed animate-pulse select-text cursor-text">
                    {queryStatus}
                  </p>
                )}
              </div>
            )}
            <div ref={messagesEndRef} />
          </div>
        ) : (
          header
        )}
      </div>

      {/* Citation Modal */}
      <CitationModal
        isOpen={isCitationModalOpen}
        onClose={() => setIsCitationModalOpen(false)}
        source={selectedSource}
        highlightedText={
          selectedSource?.metadata?.relevantText ||
          selectedSource?.content?.substring(0, 150)
        } // Use relevant text if available, otherwise use a portion of the content
      />

      <div className="sticky bottom-0 z-10 flex justify-center items-end w-full mt-24 ">
        <div className="w-[90%] sm:w-[85%] md:w-[80%] lg:w-[75%] xl:w-[60rem] max-w-[60rem] mx-auto py-2">
          <ChatInputForm
            input={input}
            setInput={setInput}
            handleKeyDown={handleKeyDown}
            handleChange={handleChange}
            handleAISubmit={handleAISubmit}
            setMessages={setMessages}
            status={status}
            state={state}
            isMobile={isMobile}
            includeWebResults={includeWebResults}
            setIncludeWebResults={handleWebSearchToggle}
            webSearchLimitExceeded={webSearchLimitExceeded}
            selectedImages={selectedImages}
            setImageContext={setImageContext}
            setAudioContext={setAudioContext}
            setVideoContext={setVideoContext}
            setSelectedImages={setSelectedImages}
            llmCapabilities={llmCapabilities}
            selectedSearchMode={selectedSearchMode}
            setSelectedSearchMode={setSelectedSearchMode}
          />
        </div>
      </div>
    </main>
  );
}
