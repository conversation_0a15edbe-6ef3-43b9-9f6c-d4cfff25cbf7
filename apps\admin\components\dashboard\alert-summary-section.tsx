"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import {
  AlertTriangle,
  TrendingUp,
  AlertCircle,
  DollarSign,
  Eye,
  CheckCircle,
  Mail,
  Settings,
  ChevronDown,
  ChevronRight,
  Clock,
  Activity,
  Zap,
  BarChart3,
  ExternalLink
} from "lucide-react";
import { cn } from "@/lib/utils";
import { formatDistanceToNow } from "date-fns";
import { AlertDetailModal } from "./alert-detail-modal";
import { SystemErrorsModal } from "./system-errors-modal";

interface Alert {
  id: string;
  type: "usage_spike" | "quota_risk" | "system_error" | "cost_alert";
  severity: "red" | "orange" | "yellow" | "green";
  title: string;
  description: string;
  tenantId?: string;
  tenantName?: string;
  timestamp: string;
  acknowledged: boolean;
}

interface AlertSummarySectionProps {
  alerts: Alert[];
  loading?: boolean;
  timeRange?: "7d" | "30d" | "90d"; // ✅ Added timeRange prop for System Errors modal
  onAlertAction: (alertId: string, action: "view" | "acknowledge" | "notify" | "configure" | "view_all") => void;
}

const alertTypeConfig = {
  usage_spike: {
    icon: TrendingUp,
    label: "High Usage Spike",
    description: "Token usage spikes, API traffic bursts, above-average consumption",
  },
  quota_risk: {
    icon: AlertTriangle,
    label: "Over-Quota Risks",
    description: "≥80% of storage limits",
  },
  system_error: {
    icon: AlertCircle,
    label: "System Errors",
    description: "5xx errors, malformed requests",
  },
  cost_alert: {
    icon: DollarSign,
    label: "Cost Alerts",
    description: "Cost exceeding revenue",
  },
};

const severityConfig = {
  red: {
    color: "text-red-600",
    bg: "bg-red-50 dark:bg-red-950",
    border: "border-red-200 dark:border-red-800",
    badge: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",
    dot: "bg-red-500",
  },
  orange: {
    color: "text-orange-600",
    bg: "bg-orange-50 dark:bg-orange-950",
    border: "border-orange-200 dark:border-orange-800",
    badge: "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200",
    dot: "bg-orange-500",
  },
  yellow: {
    color: "text-yellow-600",
    bg: "bg-yellow-50 dark:bg-yellow-950",
    border: "border-yellow-200 dark:border-yellow-800",
    badge: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",
    dot: "bg-yellow-500",
  },
  green: {
    color: "text-green-600",
    bg: "bg-green-50 dark:bg-green-950",
    border: "border-green-200 dark:border-green-800",
    badge: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
    dot: "bg-green-500",
  },
};

function AlertItem({ alert, onAction, onView }: { alert: Alert; onAction: (action: string) => void; onView: (alert: Alert) => void }) {
  const severity = severityConfig[alert.severity];

  // Enhanced display for different alert types
  const getAlertDisplayInfo = (alert: Alert) => {
    if (alert.type === 'usage_spike') {
      // Extract percentage from description for enhanced display
      const percentageMatch = alert.description.match(/(\d+)%/);
      const percentage = percentageMatch ? percentageMatch[1] : null;

      if (alert.description.includes('increased usage by')) {
        return {
          title: `${alert.tenantName} usage up ${percentage}% in 24h`,
          actionLabel: "View Details",
          actionIcon: Activity,
          urgency: percentage && parseInt(percentage) >= 500 ? "CRITICAL" : "HIGH"
        };
      } else if (alert.description.includes('burst API traffic')) {
        return {
          title: `${alert.tenantName} burst API traffic detected`,
          actionLabel: "View Details",
          actionIcon: Zap,
          urgency: "HIGH"
        };
      } else if (alert.description.includes('doubled vs avg')) {
        return {
          title: `${alert.tenantName} token use doubled vs avg`,
          actionLabel: "View All",
          actionIcon: BarChart3,
          urgency: "MEDIUM"
        };
      }
    } else if (alert.type === 'quota_risk') {
      // Enhanced display for Over-Quota Risk alerts
      const percentageMatch = alert.description.match(/(\d+)%/);
      const percentage = percentageMatch ? percentageMatch[1] : null;

      return {
        title: alert.description, // Use the formatted description directly
        actionLabel: "View Details",
        actionIcon: AlertTriangle,
        urgency: percentage && parseInt(percentage) >= 95 ? "CRITICAL" : "HIGH"
      };
    } else if (alert.type === 'system_error') {
      // Enhanced display for System Error alerts
      if (alert.description.includes('5xx spike')) {
        return {
          title: alert.description,
          actionLabel: "Drill Down",
          actionIcon: AlertCircle,
          urgency: "HIGH"
        };
      } else if (alert.description.includes('malformed requests')) {
        return {
          title: alert.description,
          actionLabel: "View All",
          actionIcon: AlertCircle,
          urgency: "MEDIUM"
        };
      }

      return {
        title: alert.description,
        actionLabel: "View Logs",
        actionIcon: AlertCircle,
        urgency: "MEDIUM"
      };
    } else if (alert.type === 'cost_alert') {
      // Enhanced display for Cost Alert alerts
      return {
        title: alert.description,
        actionLabel: "Details",
        actionIcon: DollarSign,
        urgency: alert.description.includes('consecutive months') ? "CRITICAL" : "HIGH"
      };
    }

    return {
      title: alert.title,
      actionLabel: "View Details",
      actionIcon: Eye,
      urgency: alert.severity === 'red' ? "CRITICAL" : "NORMAL"
    };
  };

  const displayInfo = getAlertDisplayInfo(alert);
  const ActionIcon = displayInfo.actionIcon;

  return (
    <div className={cn(
      "flex items-center justify-between p-3 rounded-lg border transition-colors hover:shadow-sm",
      severity.bg,
      severity.border,
      alert.acknowledged && "opacity-60"
    )}>
      <div className="flex items-center space-x-3 flex-1">
        <div className={cn("w-2 h-2 rounded-full", severity.dot)} />
        <div className="flex-1 min-w-0">
          <div className="flex items-center space-x-2">
            <p className="text-sm font-medium truncate">{displayInfo.title}</p>
            {displayInfo.urgency === "CRITICAL" && (
              <Badge variant="destructive" className="text-xs">
                CRITICAL
              </Badge>
            )}
            {alert.acknowledged && (
              <Badge variant="secondary" className="text-xs">
                Acknowledged
              </Badge>
            )}
          </div>
          <p className="text-xs text-muted-foreground mt-1 truncate">
            {alert.description}
          </p>
          <div className="flex items-center space-x-2 mt-1">
            <Clock className="h-3 w-3 text-muted-foreground" />
            <span className="text-xs text-muted-foreground">
              {formatDistanceToNow(new Date(alert.timestamp), { addSuffix: true })}
            </span>
          </div>
        </div>
      </div>

      <div className="flex items-center space-x-1 ml-3">
        {/* Primary action button with enhanced styling for usage spikes */}
        <Button
          variant={alert.type === 'usage_spike' ? "default" : "ghost"}
          size="sm"
          className={cn(
            "text-xs h-7 px-2",
            alert.type === 'usage_spike' && "bg-primary hover:bg-primary/90"
          )}
          onClick={() => onView(alert)}
          title={displayInfo.actionLabel}
        >
          <ActionIcon className="h-3 w-3 mr-1" />
          {displayInfo.actionLabel}
        </Button>

        {/* Secondary action buttons */}
        {!alert.acknowledged && (
          <Button
            variant="ghost"
            size="icon"
            className="h-6 w-6"
            onClick={() => onAction("acknowledge")}
            title="Acknowledge"
          >
            <CheckCircle className="h-3 w-3" />
          </Button>
        )}
      </div>
    </div>
  );
}

function AlertCategory({
  type,
  alerts,
  onAlertAction,
  onAlertView,
  onSystemErrorsView // ✅ Added callback for System Errors modal
}: {
  type: keyof typeof alertTypeConfig;
  alerts: Alert[];
  onAlertAction: (alertId: string, action: string) => void;
  onAlertView: (alert: Alert) => void;
  onSystemErrorsView?: () => void; // ✅ Optional callback for System Errors
}) {
  const [isOpen, setIsOpen] = useState(false);
  const config = alertTypeConfig[type];
  const Icon = config.icon;

  const visibleAlerts = alerts.slice(0, 5);
  const hasMore = alerts.length > 5;

  // Always show alert categories, even when empty

  // Enhanced "View All" action for different alert types
  const getViewAllAction = () => {
    switch (type) {
      case 'usage_spike':
        return {
          label: "View All Usage Spikes",
          action: () => onAlertAction("view-all-usage", "view_all"),
          icon: BarChart3
        };
      case 'quota_risk':
        return {
          label: "View All Quotas",
          action: () => onAlertAction("view-all-quotas", "view_all"),
          icon: AlertTriangle
        };
      case 'system_error':
        return {
          label: "View Error Logs",
          action: () => onSystemErrorsView?.(), // ✅ Use callback for System Errors modal
          icon: AlertCircle
        };
      case 'cost_alert':
        return {
          label: "View Financial Report",
          action: () => onAlertAction("view-all-costs", "view_all"),
          icon: DollarSign
        };
      default:
        return {
          label: "View All",
          action: () => onAlertAction("view-all", "view_all"),
          icon: Eye
        };
    }
  };

  const viewAllConfig = getViewAllAction();
  const ViewAllIcon = viewAllConfig.icon;

  return (
    <Card>
      <Collapsible open={isOpen} onOpenChange={setIsOpen}>
        <CollapsibleTrigger asChild>
          <CardHeader className="cursor-pointer hover:bg-muted/50 transition-colors">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="p-2 rounded-full bg-primary/10">
                  <Icon className="h-4 w-4 text-primary" />
                </div>
                <div>
                  <CardTitle className="text-base font-medium flex items-center space-x-2">
                    <span>{config.label}</span>
                    <Badge
                      variant={alerts.some(a => a.severity === 'red') ? "destructive" : "secondary"}
                      className="text-xs"
                    >
                      {alerts.length}
                    </Badge>
                  </CardTitle>
                  <p className="text-xs text-muted-foreground">{config.description}</p>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                {/* <Button
                  variant="outline"
                  size="sm"
                  className="text-xs"
                  onClick={(e) => {
                    e.stopPropagation();
                    viewAllConfig.action();
                  }}
                >
                  <ViewAllIcon className="h-3 w-3 mr-1" />
                  {viewAllConfig.label}
                </Button> */}
                {isOpen ? (
                  <ChevronDown className="h-4 w-4" />
                ) : (
                  <ChevronRight className="h-4 w-4" />
                )}
              </div>
            </div>
          </CardHeader>
        </CollapsibleTrigger>

        <CollapsibleContent>
          <CardContent className="space-y-3">
            {alerts.length === 0 ? (
              // Show encouraging message for empty alert categories
              <div className="text-center py-4">
                <CheckCircle className="h-8 w-8 text-green-500 mx-auto mb-2" />
                <p className="text-sm font-medium text-green-700 dark:text-green-400">
                  All clear!
                </p>
                <p className="text-xs text-muted-foreground mt-1">
                  {type === 'usage_spike' && "No unusual usage patterns detected"}
                  {type === 'quota_risk' && "All tenants within storage limits"}
                  {type === 'system_error' && "No server errors or malformed requests"}
                  {type === 'cost_alert' && "All tenant costs within revenue"}
                </p>
              </div>
            ) : (
              <>
                {visibleAlerts.map((alert) => (
                  <AlertItem
                    key={alert.id}
                    alert={alert}
                    onAction={(action) => onAlertAction(alert.id, action)}
                    onView={onAlertView}
                  />
                ))}

                {hasMore && (
                  <div className="text-center pt-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-xs"
                      onClick={() => viewAllConfig.action()}
                    >
                      <ExternalLink className="h-3 w-3 mr-1" />
                      View {alerts.length - 5} more alerts
                    </Button>
                  </div>
                )}
              </>
            )}
          </CardContent>
        </CollapsibleContent>
      </Collapsible>
    </Card>
  );
}

export function AlertSummarySection({ alerts, loading, timeRange = "7d", onAlertAction }: AlertSummarySectionProps) {
  const [selectedAlert, setSelectedAlert] = useState<Alert | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isSystemErrorsModalOpen, setIsSystemErrorsModalOpen] = useState(false); // ✅ Added System Errors modal state

  const handleAlertView = (alert: Alert) => {
    // ✅ Open System Errors modal for system_error alerts
    if (alert.type === 'system_error') {
      setIsSystemErrorsModalOpen(true);
    } else {
      setSelectedAlert(alert);
      setIsModalOpen(true);
    }
  };

  const handleModalAction = (action: "acknowledge" | "notify" | "configure") => {
    if (selectedAlert) {
      onAlertAction(selectedAlert.id, action);
      if (action === "acknowledge") {
        setIsModalOpen(false);
      }
    }
  };

  if (loading) {
    return (
      <div>
        <h2 className="text-lg font-semibold mb-4">Alert Summary</h2>
        <div className="space-y-4">
          {Array.from({ length: 4 }).map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <div className="h-4 w-4 bg-gray-200 rounded"></div>
                    <div className="h-5 w-32 bg-gray-200 rounded"></div>
                    <div className="h-4 w-8 bg-gray-200 rounded"></div>
                  </div>
                  <div className="h-8 w-20 bg-gray-200 rounded"></div>
                </div>
              </CardHeader>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  // Group alerts by type
  const alertsByType = alerts.reduce((acc, alert) => {
    if (!acc[alert.type]) {
      acc[alert.type] = [];
    }
    acc[alert.type].push(alert);
    return acc;
  }, {} as Record<string, Alert[]>);

  // Sort alerts within each type by severity and timestamp
  Object.keys(alertsByType).forEach(type => {
    alertsByType[type].sort((a, b) => {
      const severityOrder = { red: 0, orange: 1, yellow: 2, green: 3 };
      const severityDiff = severityOrder[a.severity] - severityOrder[b.severity];
      if (severityDiff !== 0) return severityDiff;
      return new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime();
    });
  });

  const totalAlerts = alerts.length;
  const acknowledgedAlerts = alerts.filter(a => a.acknowledged).length;
  const criticalAlerts = alerts.filter(a => a.severity === 'red').length;

  return (
    <div>
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-lg font-semibold">Alert Summary</h2>
        <div className="flex items-center space-x-4 text-sm text-muted-foreground">
          <span>Total: {totalAlerts}</span>
          <span>Critical: {criticalAlerts}</span>
          <span>Acknowledged: {acknowledgedAlerts}</span>
        </div>
      </div>
      
      <div className="space-y-4">
        {(Object.keys(alertTypeConfig) as Array<keyof typeof alertTypeConfig>).map(type => (
          <AlertCategory
            key={type}
            type={type}
            alerts={alertsByType[type] || []}
            onAlertAction={onAlertAction}
            onAlertView={handleAlertView}
            onSystemErrorsView={type === 'system_error' ? () => setIsSystemErrorsModalOpen(true) : undefined} // ✅ Pass callback for system_error type
          />
        ))}
      </div>

      <AlertDetailModal
        alert={selectedAlert}
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onAction={handleModalAction}
      />

      {/* ✅ System Errors Modal */}
      <SystemErrorsModal
        isOpen={isSystemErrorsModalOpen}
        onClose={() => setIsSystemErrorsModalOpen(false)}
        timeRange={timeRange}
      />
    </div>
  );
}
