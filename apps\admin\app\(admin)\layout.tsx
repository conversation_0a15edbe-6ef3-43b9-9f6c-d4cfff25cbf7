"use client";

import { <PERSON><PERSON> } from "@/components/layout/header";
import { Sidebar } from "@/components/layout/sidebar";
import { SidebarProvider } from "@/components/layout/sidebar-context";

export default function AdminLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <SidebarProvider>
      <div className="min-h-screen bg-muted/40">
        {/* Sidebar will access the context internally */}
        <Sidebar />

        {/* Main content area - margin is handled by JavaScript in the Sidebar component */}
        <div
          className="flex min-h-screen flex-col transition-all duration-300 lg:ml-[240px]"
          id="main-content"
        >
          <Header />
          <main className="flex-1 overflow-auto p-4 md:p-6">{children}</main>
        </div>
      </div>
    </SidebarProvider>
  );
}
