"""
WebSocket Connection Manager for real-time notifications
"""
import json
import logging
from typing import Dict, List, Set
from fastapi import WebSocket, WebSocketDisconnect
import asyncio
from datetime import datetime

logger = logging.getLogger(__name__)

class ConnectionManager:
    """Manages WebSocket connections for real-time notifications"""
    
    def __init__(self):
        # Store active connections by user_id and tenant_id
        self.active_connections: Dict[str, Dict[str, List[WebSocket]]] = {}
        # Store user metadata for each connection
        self.connection_metadata: Dict[WebSocket, Dict[str, str]] = {}
        
    async def connect(self, websocket: WebSocket, user_id: str, tenant_id: str):
        """Accept a new WebSocket connection"""
        await websocket.accept()
        
        # Initialize tenant connections if not exists
        if tenant_id not in self.active_connections:
            self.active_connections[tenant_id] = {}
            
        # Initialize user connections if not exists
        if user_id not in self.active_connections[tenant_id]:
            self.active_connections[tenant_id][user_id] = []
            
        # Add connection
        self.active_connections[tenant_id][user_id].append(websocket)
        
        # Store metadata
        self.connection_metadata[websocket] = {
            "user_id": user_id,
            "tenant_id": tenant_id,
            "connected_at": datetime.utcnow().isoformat()
        }
        
        
        # Send connection confirmation
        await self.send_personal_message({
            "type": "connection_established",
            "message": "Connected to real-time notifications",
            "timestamp": datetime.utcnow().isoformat()
        }, websocket)
        
    def disconnect(self, websocket: WebSocket):
        """Remove a WebSocket connection"""
        if websocket in self.connection_metadata:
            metadata = self.connection_metadata[websocket]
            user_id = metadata["user_id"]
            tenant_id = metadata["tenant_id"]
            
            # Remove from active connections
            if (tenant_id in self.active_connections and 
                user_id in self.active_connections[tenant_id]):
                
                if websocket in self.active_connections[tenant_id][user_id]:
                    self.active_connections[tenant_id][user_id].remove(websocket)
                    
                # Clean up empty lists
                if not self.active_connections[tenant_id][user_id]:
                    del self.active_connections[tenant_id][user_id]
                    
                if not self.active_connections[tenant_id]:
                    del self.active_connections[tenant_id]
                    
            # Remove metadata
            del self.connection_metadata[websocket]
            
            
    async def send_personal_message(self, message: dict, websocket: WebSocket):
        """Send a message to a specific WebSocket connection"""
        try:
            await websocket.send_text(json.dumps(message))
        except Exception as e:
            logger.error(f"Error sending message to websocket: {e}")
            
    async def send_to_user(self, message: dict, user_id: str, tenant_id: str):
        """Send a message to all connections of a specific user in a tenant"""
        if (tenant_id in self.active_connections and 
            user_id in self.active_connections[tenant_id]):
            
            connections = self.active_connections[tenant_id][user_id].copy()
            disconnected_connections = []
            
            for connection in connections:
                try:
                    await connection.send_text(json.dumps(message))
                except WebSocketDisconnect:
                    disconnected_connections.append(connection)
                except Exception as e:
                    logger.error(f"Error sending message to user {user_id}: {e}")
                    disconnected_connections.append(connection)
                    
            # Clean up disconnected connections
            for connection in disconnected_connections:
                self.disconnect(connection)
                
    async def send_to_tenant(self, message: dict, tenant_id: str, exclude_user_id: str = None):
        """Send a message to all users in a tenant (except excluded user)"""
        if tenant_id not in self.active_connections:
            return
            
        for user_id, connections in self.active_connections[tenant_id].items():
            if exclude_user_id and user_id == exclude_user_id:
                continue
                
            connections_copy = connections.copy()
            disconnected_connections = []
            
            for connection in connections_copy:
                try:
                    await connection.send_text(json.dumps(message))
                except WebSocketDisconnect:
                    disconnected_connections.append(connection)
                except Exception as e:
                    logger.error(f"Error sending message to tenant {tenant_id}: {e}")
                    disconnected_connections.append(connection)
                    
            # Clean up disconnected connections
            for connection in disconnected_connections:
                self.disconnect(connection)
                
    async def broadcast_notification(self, notification_data: dict):
        """Broadcast a notification to the appropriate users"""
        notification_type = notification_data.get("type")
        tenant_id = notification_data.get("tenantId")
        target_user_id = notification_data.get("userId")
        
        if not tenant_id or not target_user_id:
            logger.warning("Missing tenant_id or user_id in notification data")
            return
            
        message = {
            "type": "notification",
            "data": notification_data,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        # Send to the target user
        await self.send_to_user(message, target_user_id, tenant_id)
        

    async def broadcast_thread_update(self, thread_data: dict):
        """Broadcast thread updates to all users in a tenant"""
        tenant_id = thread_data.get("tenantId")

        if not tenant_id:
            logger.warning("Missing tenant_id in thread data")
            return

        message = {
            "type": "thread_update",
            "data": thread_data,
            "timestamp": datetime.utcnow().isoformat()
        }

        # Send to all users in the tenant
        await self.send_to_tenant(message, tenant_id)

        logger.info(f"Broadcasted thread update to tenant {tenant_id}")

    async def broadcast_theme_update(self, theme_data: dict):
        """Broadcast theme updates to all users in a tenant (excluding the user who made the change)"""
        tenant_id = theme_data.get("tenantId")
        updated_by = theme_data.get("updatedBy")

        if not tenant_id:
            logger.warning("Missing tenant_id in theme data")
            return

        message = {
            "type": "theme_update",
            "data": theme_data,
            "timestamp": datetime.utcnow().isoformat()
        }

        # Send to all users in the tenant except the one who made the change
        await self.send_to_tenant(message, tenant_id, exclude_user_id=updated_by)

        logger.info(f"Broadcasted theme update to tenant {tenant_id} (excluding user {updated_by})")
        
    def get_connection_stats(self) -> dict:
        """Get statistics about active connections"""
        total_connections = 0
        tenant_stats = {}
        
        for tenant_id, users in self.active_connections.items():
            user_count = len(users)
            connection_count = sum(len(connections) for connections in users.values())
            total_connections += connection_count
            
            tenant_stats[tenant_id] = {
                "users": user_count,
                "connections": connection_count
            }
            
        return {
            "total_connections": total_connections,
            "total_tenants": len(self.active_connections),
            "tenant_stats": tenant_stats
        }

# Global connection manager instance
manager = ConnectionManager()
