"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Building,
  Plus,
  Users,
  Trash2,
  CheckCircle,
  AlertCircle,
  Loader2,
} from "lucide-react";
import toast from "react-hot-toast";
import { useLanguage } from "@/lib/language-context";
import {
  getCompanies,
  createCompany,
  deleteCompany,
  type Company,
  type CreateCompanyData,
} from "@/services";

// Remove duplicate interface and mock data - using types from services

const AddCompanyDialog = ({
  onAddCompany,
}: {
  onAddCompany: (company: Company) => void;
}) => {
  const { t } = useLanguage();
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    name: "",
    slug: "",
    description: "",
    maxSignups: 50,
  });

  const generateSlug = (name: string) => {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, "")
      .replace(/\s+/g, "-")
      .replace(/-+/g, "-")
      .trim();
  };

  const handleNameChange = (name: string) => {
    setFormData((prev) => ({
      ...prev,
      name,
      slug: generateSlug(name),
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (
      !formData.name.trim() ||
      !formData.slug.trim() ||
      !formData.description.trim()
    ) {
      toast.error(t("company.fillAllRequiredFields"));
      return;
    }

    if (formData.maxSignups < 1) {
      toast.error(t("company.maxSignupsMustBeAtLeast"));
      return;
    }

    setLoading(true);
    try {
      const result = await createCompany({
        name: formData.name,
        slug: formData.slug,
        description: formData.description,
        maxSignups: formData.maxSignups,
      });

      if ("error" in result) {
        toast.error(result.error);
        return;
      }

      onAddCompany(result.company);
      setFormData({ name: "", slug: "", description: "", maxSignups: 50 });
      setOpen(false);
      toast.success(t("company.companyCreatedSuccess"));
    } catch (error) {
      toast.error(t("company.failedToCreateCompany"));
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button>
          <Plus className="mr-2 h-4 w-4" />
          {t("company.addCompany")}
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>{t("company.addNewCompany")}</DialogTitle>
          <DialogDescription>{t("company.createNewCompany")}</DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="name">{t("company.companyName")} *</Label>
            <Input
              id="name"
              placeholder={t("company.enterCompanyName")}
              value={formData.name}
              onChange={(e) => handleNameChange(e.target.value)}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="slug">{t("company.slug")} *</Label>
            <Input
              id="slug"
              placeholder={t("company.slugPlaceholder")}
              value={formData.slug}
              onChange={(e) =>
                setFormData((prev) => ({ ...prev, slug: e.target.value }))
              }
            />
            <p className="text-xs text-muted-foreground">
              {t("company.inviteLinkWillBe")}
              https://app.swissknowledgehub.ch/invite/
              {formData.slug || t("company.yourSlug")}
            </p>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">{t("common.description")} *</Label>
            <Textarea
              id="description"
              placeholder={t("company.describeCompany")}
              value={formData.description}
              onChange={(e) =>
                setFormData((prev) => ({
                  ...prev,
                  description: e.target.value,
                }))
              }
              rows={3}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="maxSignups">{t("company.maxSignups")} *</Label>
            <Input
              id="maxSignups"
              type="number"
              min="1"
              placeholder="50"
              value={formData.maxSignups}
              onChange={(e) =>
                setFormData((prev) => ({
                  ...prev,
                  maxSignups: parseInt(e.target.value) || 0,
                }))
              }
            />
            <p className="text-xs text-muted-foreground">
              {t("company.maxSignupsDescription")}
            </p>
          </div>

          <div className="flex justify-end space-x-2 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => setOpen(false)}
              disabled={loading}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  {t("common.creating")}
                </>
              ) : (
                t("company.createCompany")
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default function PartnerCompanyPage() {
  const { t } = useLanguage();
  const router = useRouter();
  const [companies, setCompanies] = useState<Company[]>([]);
  const [loading, setLoading] = useState(true);

  // Load companies on component mount
  useEffect(() => {
    loadCompanies();
  }, []);

  const loadCompanies = async () => {
    setLoading(true);
    try {
      const result = await getCompanies();
      if ("error" in result) {
        toast.error(result.error);
        return;
      }
      setCompanies(result.companies);
    } catch (error) {
      toast.error("Failed to load companies");
    } finally {
      setLoading(false);
    }
  };

  const handleAddCompany = (newCompany: Company) => {
    setCompanies((prev) => [...prev, newCompany]);
  };

  const handleDeleteCompany = async (id: string) => {
    try {
      const result = await deleteCompany(id);
      if ("error" in result) {
        toast.error(result.error);
        return;
      }
      setCompanies((prev) => prev.filter((company) => company.id !== id));
      toast.success(t("company.companyDeletedSuccess"));
    } catch (error) {
      toast.error(t("company.failedToDeleteCompany"));
    }
  };

  const handleRowClick = (companyId: string) => {
    router.push(`/settings/partner/company/${companyId}`);
  };

  const getUsageStatus = (current: number, max: number) => {
    const percentage = (current / max) * 100;
    if (percentage >= 100)
      return {
        color: "hsl(var(--destructive))",
        icon: AlertCircle,
        bg: "hsl(var(--destructive) / 0.1)",
      };
    if (percentage >= 80)
      return {
        color: "hsl(var(--secondary))",
        icon: AlertCircle,
        bg: "hsl(var(--secondary) / 0.1)",
      };
    return {
      color: "hsl(var(--primary))",
      icon: CheckCircle,
      bg: "hsl(var(--primary) / 0.1)",
    };
  };

  const totalSignups = companies.reduce(
    (sum, company) => sum + company.currentSignups,
    0
  );
  const totalMaxSignups = companies.reduce(
    (sum, company) => sum + company.maxSignups,
    0
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">
            {t("company.management")}
          </h2>
          <p className="text-muted-foreground">
            {t("company.manageCompanies")}
          </p>
        </div>
        <AddCompanyDialog onAddCompany={handleAddCompany} />
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {t("company.totalCompanies")}
            </CardTitle>
            <Building className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{companies.length}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {t("company.totalSignups")}
            </CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalSignups}</div>
            <p className="text-xs text-muted-foreground">
              {t("company.ofMaximum", { max: totalMaxSignups })}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {t("company.averageUsage")}
            </CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {totalMaxSignups > 0
                ? Math.round((totalSignups / totalMaxSignups) * 100)
                : 0}
              %
            </div>
            <p className="text-xs text-muted-foreground">
              across all companies
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Companies Table */}
      <Card>
        <CardHeader>
          <CardTitle>
            {t("company.companies")} ({companies.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex flex-col items-center justify-center py-12">
              <Loader2 className="h-12 w-12 text-muted-foreground mb-4 animate-spin" />
              <h3 className="text-lg font-semibold mb-2">
                {t("company.loadingCompanies")}
              </h3>
              <p className="text-muted-foreground text-center">
                {t("company.pleaseWait")}
              </p>
            </div>
          ) : companies.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-12">
              <Building className="h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-semibold mb-2">
                {t("company.noCompaniesYet")}
              </h3>
              <p className="text-muted-foreground text-center mb-4">
                {t("company.getStartedByCreating")}
              </p>
              <AddCompanyDialog onAddCompany={handleAddCompany} />
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>{t("company.company")}</TableHead>
                  <TableHead>{t("company.slug")}</TableHead>
                  <TableHead>{t("company.usage")}</TableHead>
                  <TableHead>{t("company.created")}</TableHead>
                  <TableHead>{t("common.actions")}</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {companies.map((company) => {
                  const status = getUsageStatus(
                    company.currentSignups,
                    company.maxSignups
                  );
                  const StatusIcon = status.icon;

                  return (
                    <TableRow
                      key={company.id}
                      className="cursor-pointer hover:bg-muted/50"
                      onClick={() => handleRowClick(company.id)}
                    >
                      <TableCell>
                        <div>
                          <div className="font-medium flex items-center gap-2">
                            <Building className="h-4 w-4 text-muted-foreground" />
                            {company.name}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {company.description}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <code className="text-sm bg-muted px-2 py-1 rounded">
                          {company.slug}
                        </code>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <StatusIcon
                            className="h-4 w-4"
                            style={{ color: status.color }}
                          />
                          <span
                            className="text-sm font-medium"
                            style={{ color: status.color }}
                          >
                            {company.currentSignups} / {company.maxSignups}
                          </span>
                        </div>
                        <div
                          className="w-full rounded-full h-1.5 mt-1"
                          style={{ backgroundColor: "hsl(var(--muted))" }}
                        >
                          <div
                            className="h-1.5 rounded-full transition-all"
                            style={{
                              backgroundColor: status.color,
                              width: `${Math.min(
                                (company.currentSignups / company.maxSignups) *
                                  100,
                                100
                              )}%`,
                            }}
                          />
                        </div>
                      </TableCell>
                      <TableCell>{company.createdAt}</TableCell>
                      <TableCell>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDeleteCompany(company.id);
                          }}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
