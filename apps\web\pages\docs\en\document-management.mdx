---
title: "Document Management"
description: "Learn how to manage your documents in Swiss Knowledge Hub"
---

# Document Management

Swiss Knowledge Hub provides powerful document management capabilities that allow you to organize, search, and interact with your documents efficiently.

## Supported Document Types

Swiss Knowledge Hub supports a wide range of document formats:

### Document Formats

- PDF documents (.pdf)
- Microsoft Word documents (.docx, .doc)
- Microsoft PowerPoint presentations (.pptx, .ppt)
- Microsoft Excel spreadsheets (.xlsx, .xls)
- Text files (.txt)
- Markdown files (.md, .markdown)
- CSV files (.csv)
- JSON files (.json)
- XML files (.xml)
- HTML files (.html, .htm)

### Media Formats

- Images with OCR and metadata extraction (.jpg, .jpeg, .png, .gif, .bmp, .webp)
- Audio files with transcription (.mp3, .wav)

### Archive and Other Formats

- ZIP archives (contents are processed)
- EPub books (.epub)
- Email messages (.eml, .msg)

## Uploading Documents

### Manual Upload

1. Navigate to "My Hub" in the main navigation
2. Click the "Upload" button
3. Select one or more PDF files from your computer
4. Click "Upload" to begin the processing

### Bulk Upload

For uploading multiple documents at once:

1. Navigate to "My Hub"
2. Click "Bulk Upload"
3. Select multiple files or drag and drop them into the upload area
4. Click "Upload All" to process the documents

### Upload Status

After uploading, documents go through several processing stages:

1. **Uploading**: The file is being transferred to the server
2. **Processing**: Text extraction and chunking is in progress
3. **Embedding**: Vector embeddings are being generated
4. **Complete**: The document is ready for use

You can view the status of your uploads in the "Recent Uploads" section.

## Document Organization

### Folders and Collections

Organize your documents using folders and collections:

1. **Create Folder**: Click "New Folder" and give it a name
2. **Move Documents**: Drag and drop documents into folders
3. **Create Collection**: Group related documents across folders into collections

### Tagging

Add tags to your documents for easier categorization and searching:

1. Select a document and click "Edit"
2. Add tags in the tags field
3. Save your changes

## Document Actions

Each document has several available actions:

- **View**: Open the document in the built-in viewer
- **Chat**: Start an AI chat session with the document
- **Edit**: Modify document metadata and tags
- **Share**: Share the document with team members
- **Download**: Download the original document
- **Delete**: Remove the document from your workspace

## Search Capabilities

Swiss Knowledge Hub offers powerful search functionality:

### Basic Search

Use the search bar at the top of the document list to find documents by:

- Title
- Content
- Tags
- Date

### Advanced Search

Click "Advanced Search" to access more search options:

- Date ranges
- File types
- Specific folders
- Tag combinations
- Content-specific queries

## Document Sync

### Google Drive Integration

Connect your Google Drive to automatically sync documents:

1. Go to "Settings" > "Integrations"
2. Click "Connect" next to Google Drive
3. Authorize the connection
4. Select folders to sync

### OneDrive/Outlook Integration

Connect Microsoft OneDrive or Outlook:

1. Go to "Settings" > "Integrations"
2. Click "Connect" next to OneDrive/Outlook
3. Authorize the connection
4. Select folders to sync

### Sync Settings

Configure sync behavior:

- Automatic or manual sync
- Sync frequency
- File type filters
- Remove sync connection

## Document Limits

Document management limits depend on your subscription plan:

| Plan       | Storage Limit | Max File Size | Vector DB Usage   |
| ---------- | ------------- | ------------- | ----------------- |
| Free       | 100 MB        | 10 MB         | 10,000 vectors    |
| Pro        | 10 GB         | 50 MB         | 1,000,000 vectors |
| Enterprise | 100 GB+       | 100 MB+       | Unlimited         |

## Best Practices

For optimal document management:

1. **Organize systematically**: Create a logical folder structure
2. **Use descriptive names**: Name documents clearly
3. **Apply consistent tags**: Develop a tagging system
4. **Regular cleanup**: Remove unnecessary documents
5. **Check processing status**: Ensure documents are fully processed before chatting
