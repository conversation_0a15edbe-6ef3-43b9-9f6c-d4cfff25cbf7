import os
import time
import requests
import hashlib
from typing import List, Optional
from langchain_core.embeddings import Embeddings
import logging

logger = logging.getLogger(__name__)

# Try to import diskcache, handle gracefully if not available
try:
    import diskcache as dc
    DISKCACHE_AVAILABLE = True
except ImportError:
    logger.warning("diskcache library not available, embedding caching disabled")
    DISKCACHE_AVAILABLE = False
    dc = None

class AzureVisionEmbeddings(Embeddings):
    """
    Custom Azure Vision embeddings implementation using the text-embedding-3-small model.
    This implementation follows the Azure Vision API endpoint format.
    """

    def __init__(
        self,
        azure_endpoint: Optional[str] = None,
        api_key: Optional[str] = None,
        deployment_name: Optional[str] = None,
        api_version: str = "2023-12-01-preview",
        model: str = "text-embedding-3-small",
        enable_cache: bool = True,
        cache_ttl: int = 24 * 60 * 60  # 24 hours in seconds
    ):
        """
        Initialize Azure Vision embeddings.

        Args:
            azure_endpoint: Azure endpoint URL
            api_key: Azure API key
            deployment_name: Azure deployment name
            api_version: API version
            model: Model name (default: text-embedding-3-small)
            enable_cache: Whether to enable embedding caching (default: True)
            cache_ttl: Cache time-to-live in seconds (default: 24 hours)
        """
        self.azure_endpoint = azure_endpoint or os.getenv("AZURE_OPENAI_EMBEDDING_API_ENDPOINT")
        self.api_key = api_key or os.getenv("AZURE_OPENAI_EMBEDDING_API_KEY")
        self.deployment_name = deployment_name or os.getenv("AZURE_OPENAI_EMBEDDING_MODEL", "text-embedding-3-small")
        self.api_version = api_version
        self.model = model

        logger.info(f"Initializing Azure Vision embeddings with deployment {deployment_name} model {model}")

        # Enable text normalization for case-insensitive embeddings
        self.normalize_text = True

        # Cache configuration
        self.enable_cache = enable_cache and DISKCACHE_AVAILABLE
        self.cache_ttl = cache_ttl
        self._cache = None
        self._cache_directory = "./cache/azure_vision_embeddings"

        if not self.azure_endpoint:
            raise ValueError("Azure endpoint is required")
        if not self.api_key:
            raise ValueError("API key is required")
        if not self.deployment_name:
            raise ValueError("Deployment name is required")

        if self.enable_cache:
            logger.info("Embedding caching enabled with TTL of %d seconds", self.cache_ttl)
        else:
            logger.info("Embedding caching disabled")

    @property
    def cache(self):
        """Get diskcache Cache instance for embedding persistence."""
        if not self.enable_cache:
            return None

        if self._cache is None:
            try:
                if not DISKCACHE_AVAILABLE:
                    logger.warning("diskcache library not available, embedding caching disabled")
                    return None

                # Create cache directory if it doesn't exist
                os.makedirs(self._cache_directory, exist_ok=True)

                # Initialize diskcache with TTL support
                self._cache = dc.Cache(
                    directory=self._cache_directory,
                    size_limit=500 * 1024 * 1024,  # 500MB limit for embeddings
                    eviction_policy='least-recently-used'
                )
                logger.debug(f"Embedding cache initialized at {self._cache_directory}")

            except Exception as e:
                logger.warning(f"Failed to initialize diskcache for embedding persistence: {e}")
                self._cache = None
        return self._cache

    def _get_cache_key(self, text: str) -> str:
        """
        Generate a cache key for the given text.

        Args:
            text: The text to generate a cache key for

        Returns:
            A unique cache key string
        """
        # Include model configuration in cache key to avoid conflicts
        key_components = [
            text,
            self.model,
            self.deployment_name,
            str(self.normalize_text)
        ]
        key_string = "|".join(key_components)

        # Use SHA256 hash for consistent, collision-resistant keys
        cache_key = hashlib.sha256(key_string.encode('utf-8')).hexdigest()
        return f"embedding_{cache_key}"

    def _normalize_text(self, text: str) -> str:
        """
        Normalize text for case-insensitive embeddings.

        This method applies consistent text normalization to ensure that
        semantically similar text with different cases produces similar embeddings.
        """
        if not text or not isinstance(text, str):
            return text

        # Convert to lowercase for case-insensitive matching
        normalized = text.lower()

        # Remove extra whitespace while preserving structure
        normalized = ' '.join(normalized.split())

        return normalized

    def _get_endpoint_url(self) -> str:
        """Construct the Azure Vision API endpoint URL."""
        return f"{self.azure_endpoint}/openai/deployments/{self.deployment_name}/embeddings?api-version={self.api_version}"

    def _get_headers(self) -> dict:
        """Get request headers."""
        return {
            "Content-Type": "application/json",
            "api-key": self.api_key
        }

    def _embed_texts(self, texts: List[str]) -> List[List[float]]:
        """
        Embed a list of texts or image URLs using Azure Vision API.
        Handles batch processing with Azure OpenAI's limit of 96 texts per request.

        Args:
            texts: List of texts or image URLs to embed

        Returns:
            List of embeddings
        """
        # Validate input to prevent tokenization issues
        if not isinstance(texts, list):
            logger.error(f"Expected list of texts, got {type(texts)}: {texts}")
            raise ValueError(f"Expected list of texts, got {type(texts)}")

        # Create a copy to avoid modifying the original list
        validated_texts = []
        for i, text in enumerate(texts):
            if not isinstance(text, str):
                logger.error(f"Text at index {i} is not a string: {type(text)} - {text}")
                if isinstance(text, list):
                    logger.error("Text appears to be tokenized - this will cause embedding API errors")
                    # Check if this looks like token IDs (list of integers)
                    if all(isinstance(x, int) for x in text):
                        logger.error(f"Detected token IDs at index {i}: {text}")
                        raise ValueError(f"Text at index {i} appears to be tokenized (list of token IDs) instead of a string. This indicates a bug in the query processing pipeline.")
                    raise ValueError(f"Text at index {i} appears to be tokenized (list of tokens) instead of a string")
                text = str(text)

            # Apply text normalization if enabled
            if self.normalize_text:
                text = self._normalize_text(text)

            validated_texts.append(text)

        logger.info(f"Embedding {len(validated_texts)} documents using Azure Vision text-embedding-3-small")

        # Azure OpenAI has a limit of 96 texts per request
        batch_size = 96
        all_embeddings = []

        # Process texts in batches
        for i in range(0, len(validated_texts), batch_size):
            batch = validated_texts[i:i + batch_size]
            logger.debug(f"Processing batch {i//batch_size + 1}/{(len(validated_texts) + batch_size - 1)//batch_size} with {len(batch)} texts")

            batch_embeddings = self._embed_batch(batch)
            all_embeddings.extend(batch_embeddings)

        if len(all_embeddings) != len(texts):
            raise ValueError(f"Expected {len(texts)} embeddings, got {len(all_embeddings)}")

        logger.debug(f"Successfully embedded {len(all_embeddings)} texts")
        return all_embeddings

    def _embed_batch(self, texts: List[str]) -> List[List[float]]:
        """
        Embed a single batch of texts (max 96 texts).

        Args:
            texts: List of texts to embed (max 96)

        Returns:
            List of embeddings for the batch
        """
        if len(texts) > 96:
            raise ValueError(f"Batch size too large: {len(texts)} > 96")

        endpoint = self._get_endpoint_url()
        headers = self._get_headers()

        # Prepare the payload following the reference format
        data = {
            "model": self.model,
            "input": texts
        }

        try:
            logger.debug(f"Sending batch request to Azure Vision API: {endpoint}")
            logger.debug(f"Batch size: {len(texts)}")

            response = requests.post(endpoint, headers=headers, json=data)
            response.raise_for_status()

            result = response.json()
            logger.debug(f"Azure Vision API response received for batch")

            # Extract embeddings from response
            embeddings = []
            if "data" in result:
                for item in result["data"]:
                    if "embedding" in item:
                        embeddings.append(item["embedding"])

            if len(embeddings) != len(texts):
                raise ValueError(f"Expected {len(texts)} embeddings, got {len(embeddings)}")

            logger.debug(f"Successfully extracted {len(embeddings)} embeddings from batch")
            return embeddings

        except requests.exceptions.RequestException as e:
            logger.error(f"Error calling Azure Vision embeddings API for batch: {e}")
            if hasattr(e, 'response') and e.response is not None:
                logger.error(f"Response status: {e.response.status_code}")
                logger.error(f"Response text: {e.response.text}")
                # Log the request data that caused the error
                logger.error(f"Request data that caused error: {data}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error in Azure Vision embeddings batch: {e}")
            logger.error(f"Request data: {data}")
            raise

    def embed_image(self, image_url: str) -> List[float]:
        """
        Embed a single image URL.

        Args:
            image_url: Image URL (data URL or HTTP URL) to embed

        Returns:
            Single embedding vector
        """
        logger.info("Embedding image using Azure Vision text-embedding-3-small")
        embeddings = self._embed_texts([image_url])
        return embeddings[0]

    def embed_documents(self, texts: List[str]) -> List[List[float]]:
        """
        Embed a list of documents.

        Args:
            texts: List of document texts to embed

        Returns:
            List of embeddings
        """
        logger.info(f"Embedding {len(texts)} documents using Azure Vision text-embedding-3-small")
        return self._embed_texts(texts)

    def embed_query(self, text: str) -> List[float]:
        """
        Embed a single query text with caching support.

        Args:
            text: Query text to embed

        Returns:
            Single embedding vector
        """

        start_time = time.time()
        # Apply text normalization if enabled
        normalized_text = text
        if self.normalize_text:
            original_text = text
            normalized_text = self._normalize_text(text)
            logger.debug(f"Query normalization: '{original_text[:50]}...' -> '{normalized_text[:50]}...'")

        # Check cache first if caching is enabled
        cache = self.cache
        cache_key = None
        if cache is not None:
            cache_key = self._get_cache_key(normalized_text)
            try:
                cached_embedding = cache.get(cache_key)
                if cached_embedding is not None:
                    logger.debug(f"Cache hit for query: '{normalized_text[:50]}...'")
                    return cached_embedding
                else:
                    logger.debug(f"Cache miss for query: '{normalized_text[:50]}...'")
            except Exception as e:
                logger.warning(f"Error reading from cache: {e}")

        # Generate embedding if not cached
        logger.info(f"Embedding query using Azure Vision text-embedding-3-small: '{normalized_text[:100]}...'")
        embeddings = self._embed_texts([normalized_text])
        embedding = embeddings[0]

        # Store in cache if caching is enabled
        if cache is not None and cache_key is not None:
            try:
                cache.set(cache_key, embedding, expire=self.cache_ttl)
                logger.debug(f"Cached embedding for query: '{normalized_text[:50]}...'")
            except Exception as e:
                logger.warning(f"Error writing to cache: {e}")

        logger.info(f"Query embedding completed in {time.time() - start_time:.3f}s")
        return embedding

    async def aembed_documents(self, texts: List[str]) -> List[List[float]]:
        """
        Truly async version of embed_documents using asyncio.to_thread for better performance.
        """
        import asyncio
        logger.info(f"Async embedding {len(texts)} documents using Azure Vision text-embedding-3-small")
        return await asyncio.to_thread(self.embed_documents, texts)

    async def aembed_query(self, text: str) -> List[float]:
        """
        Truly async version of embed_query with caching support using asyncio.to_thread for better performance.
        """
        import asyncio
        import time

        embed_start = time.time()
        logger.info(f"Fast async embedding query: '{text[:50]}...'")

        # Use asyncio.to_thread for true parallelism while maintaining cache functionality
        result = await asyncio.to_thread(self.embed_query, text)

        embed_time = time.time() - embed_start
        logger.info(f"Query embedding completed in {embed_time:.3f}s")

        return result

    def clear_cache(self) -> bool:
        """
        Clear the embedding cache.

        Returns:
            True if cache was cleared successfully, False otherwise
        """
        cache = self.cache
        if cache is not None:
            try:
                cache.clear()
                logger.info("Embedding cache cleared successfully")
                return True
            except Exception as e:
                logger.error(f"Error clearing embedding cache: {e}")
                return False
        else:
            logger.warning("Cache not available, nothing to clear")
            return False

    def get_cache_stats(self) -> dict:
        """
        Get cache statistics.

        Returns:
            Dictionary with cache statistics or empty dict if cache not available
        """
        cache = self.cache
        if cache is not None:
            try:
                stats = {
                    "size": len(cache),
                    "volume": cache.volume(),
                    "directory": self._cache_directory,
                    "enabled": True
                }
                return stats
            except Exception as e:
                logger.error(f"Error getting cache stats: {e}")
                return {"enabled": False, "error": str(e)}
        else:
            return {"enabled": False, "reason": "Cache not available"}
