import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/next-auth";
import db from "@/lib/shared-db";

export async function GET(request: Request) {
  const session = await getServerSession(authOptions);

  if (!session) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const { searchParams } = new URL(request.url);

    // Get tenant filter
    const tenantId = searchParams.get("tenantId");

    // Get date range from query parameters
    let startDate: Date;
    let endDate = new Date();

    // Check if custom date range is provided
    const fromDate = searchParams.get("from");
    const toDate = searchParams.get("to");

    if (fromDate && toDate) {
      // Use custom date range
      startDate = new Date(fromDate);
      endDate = new Date(toDate);
    } else {
      // Use period-based date range
      const period = searchParams.get("period") || "30days";

      // Parse the period
      const match = period.match(/^(\d+)(days|months)$/);
      if (!match) {
        return NextResponse.json(
          {
            error:
              "Invalid period format. Use format like '30days' or '6months'",
          },
          { status: 400 }
        );
      }

      const amount = parseInt(match[1], 10);
      const unit = match[2];

      // Calculate the start date
      startDate = new Date();
      if (unit === "days") {
        startDate.setDate(startDate.getDate() - amount);
      } else if (unit === "months") {
        startDate.setMonth(startDate.getMonth() - amount);
      }
    }

    // Calculate the duration for previous period
    const periodDuration = endDate.getTime() - startDate.getTime();
    const previousPeriodStart = new Date(startDate.getTime() - periodDuration);

    // Base where clause for tenant queries
    const tenantBaseWhere: any = {};

    // Add tenant filter if provided
    if (tenantId) {
      tenantBaseWhere.id = tenantId;
    }

    // Get total tenants
    const totalTenants = await db.tenant.count({
      where: tenantBaseWhere,
    });

    // Get active tenants
    const activeTenants = await db.tenant.count({
      where: {
        ...tenantBaseWhere,
        Subscription: {
          some: {
            isActive: true,
          },
        },
      },
    });

    // Get new tenants in the period
    const newTenants = await db.tenant.count({
      where: {
        ...tenantBaseWhere,
        createdAt: {
          gte: startDate,
          lte: endDate,
        },
      },
    });

    // Get tenants created in the previous period
    const previousPeriodTenants = await db.tenant.count({
      where: {
        ...tenantBaseWhere,
        createdAt: {
          gte: previousPeriodStart,
          lt: startDate,
        },
      },
    });

    const tenantGrowthRate =
      previousPeriodTenants > 0
        ? Math.round(
            ((newTenants - previousPeriodTenants) / previousPeriodTenants) * 100
          )
        : newTenants > 0
          ? 100
          : 0;

    // Base where clause for user queries
    const userBaseWhere: any = {};

    // Add tenant filter if provided
    if (tenantId) {
      userBaseWhere.Membership = {
        some: {
          tenantId,
        },
      };
    }

    // Get total users
    const totalUsers = await db.user.count({
      where: userBaseWhere,
    });

    // Get active users (users who have sessions in the period)
    // First get all sessions
    const activeSessions = await db.session.findMany({
      where: {
        expires: {
          gte: startDate,
        },
        user: userBaseWhere,
      },
      select: {
        userId: true,
      },
    });

    // Then count unique userIds
    const uniqueUserIds = new Set(
      activeSessions.map((session) => session.userId)
    );
    const activeUsers = uniqueUserIds.size;

    // Calculate active user percentage
    const activeUserPercentage =
      totalUsers > 0 ? Math.round((activeUsers / totalUsers) * 100) : 0;

    // Get new users in the period
    const newUsers = await db.user.count({
      where: {
        ...userBaseWhere,
        createdAt: {
          gte: startDate,
          lte: endDate,
        },
      },
    });

    // Get users created in the previous period
    const previousPeriodUsers = await db.user.count({
      where: {
        ...userBaseWhere,
        createdAt: {
          gte: previousPeriodStart,
          lt: startDate,
        },
      },
    });

    const userGrowthRate =
      previousPeriodUsers > 0
        ? Math.round(
            ((newUsers - previousPeriodUsers) / previousPeriodUsers) * 100
          )
        : newUsers > 0
          ? 100
          : 0;

    // Get revenue metrics with tenant filter if provided
    const subscriptionWhere: any = {
      isActive: true,
    };

    if (tenantId) {
      subscriptionWhere.tenantId = tenantId;
    }

    const subscriptions = await db.subscription.findMany({
      where: subscriptionWhere,
      include: {
        plan: true,
      },
    });

    // Calculate MRR (Monthly Recurring Revenue)
    let mrr = 0;
    let arr = 0;

    subscriptions.forEach((subscription) => {
      // Base plan price
      const basePrice =
        subscription.billingInterval === "month"
          ? subscription.plan.price || 0
          : (subscription.plan.price || 0) / 12;

      // Additional users cost
      const additionalUsersCost =
        subscription.additionalUsers *
        (subscription.billingInterval === "month"
          ? subscription.plan.additionalUserFee || 0
          : (subscription.plan.additionalUserFee || 0) / 12);

      // Additional storage cost (simplified calculation)
      const additionalStorageCost = subscription.additionalStorageGB * 10; // Assuming $10 per GB

      // Add to MRR
      mrr += basePrice + additionalUsersCost + additionalStorageCost;
    });

    // Calculate ARR (Annual Recurring Revenue)
    arr = mrr * 12;

    // Get historical revenue data for growth calculation
    // This would ideally come from historical data in the database
    // For now, using a placeholder
    const revenueGrowthRate = 5; // 5% growth

    // Get storage metrics with tenant filter if provided
    const storageWhere: any = {};

    if (tenantId) {
      storageWhere.tenantId = tenantId;
    }

    // Add date range filter
    if (startDate && endDate) {
      storageWhere.timestamp = {
        gte: startDate,
        lte: endDate,
      };
    }

    const storageUsage = await db.vectorStoreUsage.aggregate({
      where: storageWhere,
      _sum: {
        usageGB: true,
      },
    });

    const totalStorageGB = storageUsage._sum.usageGB || 0;

    // Get historical storage data for growth calculation
    const previousPeriodStorageWhere = {
      ...storageWhere,
      timestamp: {
        gte: previousPeriodStart,
        lt: startDate,
      },
    };

    const previousStorageUsage = await db.vectorStoreUsage.aggregate({
      where: previousPeriodStorageWhere,
      _sum: {
        usageGB: true,
      },
    });

    const previousStorageGB = previousStorageUsage._sum.usageGB || 0;

    // Calculate storage growth rate
    const storageGrowthRate =
      previousStorageGB > 0
        ? Math.round(
            ((totalStorageGB - previousStorageGB) / previousStorageGB) * 100
          )
        : totalStorageGB > 0
          ? 100
          : 0;

    return NextResponse.json({
      tenantMetrics: {
        totalTenants,
        activeTenants,
        newTenants,
        growthRate: tenantGrowthRate,
      },
      userMetrics: {
        totalUsers,
        activeUsers,
        activeUserPercentage,
        newUsers,
        growthRate: userGrowthRate,
      },
      revenueMetrics: {
        mrr,
        arr,
        activeSubscriptions: subscriptions.length,
        growthRate: revenueGrowthRate,
      },
      storageMetrics: {
        totalStorageGB,
        growthRate: storageGrowthRate,
      },
    });
  } catch (error) {
    console.error("Error fetching dashboard overview:", error);
    return NextResponse.json(
      { error: "Failed to fetch dashboard overview" },
      { status: 500 }
    );
  }
}
