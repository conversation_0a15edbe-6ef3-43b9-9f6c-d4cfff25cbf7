"use client";

import React, { useState, useCallback } from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Upload, Users, ArrowRight, ArrowLeft } from "lucide-react";

import toast from "react-hot-toast";
import { CSVUploadZone } from "./csv-upload-zone";
import { MemberPreviewTable } from "./member-preview-table";
import { ImportProgressComponent, ImportProgress } from "./import-progress";
import { parseCSV, CSVParseResult } from "@/lib/utils/csv-parser";
import { useLanguage } from "@/lib/language-context";

interface BulkMemberImportDialogProps {
  tenantId: string;
  onImportComplete?: () => void;
  trigger?: React.ReactNode;
}

type ImportStep = "upload" | "preview" | "importing" | "complete";

export function BulkMemberImportDialog({
  tenantId,
  onImportComplete,
  trigger,
}: BulkMemberImportDialogProps) {
  const { t } = useLanguage();
  const [open, setOpen] = useState(false);
  const [currentStep, setCurrentStep] = useState<ImportStep>("upload");
  const [parseResult, setParseResult] = useState<CSVParseResult | null>(null);
  const [importProgress, setImportProgress] = useState<ImportProgress | null>(
    null
  );
  const [uploadError, setUploadError] = useState<string>("");

  const handleFileSelect = useCallback(async (file: File) => {
    setUploadError("");

    try {
      const content = await file.text();
      const result = parseCSV(content);

      setParseResult(result);

      if (result.errors.length > 0) {
        setUploadError(
          t("bulkImport.members.messages.csvParseError", { count: result.errors.length })
        );
      } else {
        setCurrentStep("preview");
      }
    } catch (error) {
      setUploadError(t("bulkImport.members.messages.csvReadError"));
      console.error("CSV parsing error:", error);
    }
  }, []);

  const handleStartImport = useCallback(async () => {
    if (!parseResult || parseResult.data.length === 0) return;

    setCurrentStep("importing");

    const progress: ImportProgress = {
      total: parseResult.data.length,
      processed: 0,
      successful: 0,
      failed: 0,
      isComplete: false,
      errors: [],
    };

    setImportProgress(progress);

    try {
      // Process members in batches
      const batchSize = 5;
      const members = parseResult.data;

      for (let i = 0; i < members.length; i += batchSize) {
        const batch = members.slice(i, i + batchSize);

        // Process batch
        const batchPromises = batch.map(async (member) => {
          try {
            const response = await fetch("/api/admin/bulk-import/members", {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
              },
              body: JSON.stringify({
                email: member.email,
                name: member.name,
                role: member.role,
                tenantId,
              }),
            });

            if (!response.ok) {
              const errorData = await response.json();
              throw new Error(errorData.error || "Failed to import member");
            }

            return { success: true, email: member.email };
          } catch (error) {
            return {
              success: false,
              email: member.email,
              error: error instanceof Error ? error.message : "Unknown error",
            };
          }
        });

        const batchResults = await Promise.all(batchPromises);

        // Update progress
        const newProgress = { ...progress };

        batchResults.forEach((result) => {
          newProgress.processed++;
          if (result.success) {
            newProgress.successful++;
          } else {
            newProgress.failed++;
            newProgress.errors.push({
              email: result.email,
              error: result.error || "Unknown error",
            });
          }
        });

        setImportProgress({ ...newProgress });

        // Small delay between batches to avoid overwhelming the server
        if (i + batchSize < members.length) {
          await new Promise((resolve) => setTimeout(resolve, 500));
        }
      }

      // Mark as complete
      setImportProgress((prev) =>
        prev ? { ...prev, isComplete: true } : null
      );
      setCurrentStep("complete");

      // Show success toast
      const successCount = progress.successful;
      if (successCount > 0) {
        toast.success(
          t("bulkImport.members.messages.successfullyImported", {
            count: successCount,
            plural: successCount !== 1 ? "er" : ""
          })
        );
      }
    } catch (error) {
      console.error("Import error:", error);
      toast.error(t("bulkImport.members.messages.importFailed"));
      setCurrentStep("preview");
    }
  }, [parseResult, tenantId]);

  const handleReset = useCallback(() => {
    setCurrentStep("upload");
    setParseResult(null);
    setImportProgress(null);
    setUploadError("");
  }, []);

  const handleClose = useCallback(() => {
    if (currentStep === "importing") {
      // Don't allow closing during import
      return;
    }
    onImportComplete?.();
    setOpen(false);
    // Reset after a delay to avoid visual glitch
    setTimeout(handleReset, 300);
  }, [currentStep, handleReset]);

  const canProceed =
    parseResult &&
    parseResult.data.length > 0 &&
    parseResult.errors.length === 0;

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {trigger || (
          <Button variant="outline" size="sm" className="gap-2">
            <Upload className="h-4 w-4" />
            {t("bulkImport.members.triggerButton")}
          </Button>
        )}
      </DialogTrigger>

      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            {t("bulkImport.members.title")}
          </DialogTitle>
          <DialogDescription>
            {t("bulkImport.members.description")}
          </DialogDescription>
        </DialogHeader>

        {/* Step Indicator */}
        <div className="flex items-center gap-2 py-4">
          <Badge variant={currentStep === "upload" ? "default" : "secondary"}>
            1. {t("bulkImport.members.steps.upload")}
          </Badge>
          <ArrowRight className="h-4 w-4 text-muted-foreground" />
          <Badge variant={currentStep === "preview" ? "default" : "secondary"}>
            2. {t("bulkImport.members.steps.preview")}
          </Badge>
          <ArrowRight className="h-4 w-4 text-muted-foreground" />
          <Badge
            variant={
              ["importing", "complete"].includes(currentStep)
                ? "default"
                : "secondary"
            }
          >
            3. {t("bulkImport.members.steps.import")}
          </Badge>
        </div>

        {/* Step Content */}
        <div className="min-h-[400px]">
          {currentStep === "upload" && (
            <CSVUploadZone
              onFileSelect={handleFileSelect}
              tenantId={tenantId}
              error={uploadError}
            />
          )}

          {currentStep === "preview" && parseResult && (
            <MemberPreviewTable parseResult={parseResult} />
          )}

          {(currentStep === "importing" || currentStep === "complete") &&
            importProgress && (
              <ImportProgressComponent progress={importProgress} />
            )}
        </div>

        <DialogFooter className="gap-2">
          {currentStep === "upload" && (
            <Button variant="outline" onClick={handleClose}>
              {t("bulkImport.members.buttons.cancel")}
            </Button>
          )}

          {currentStep === "preview" && (
            <>
              <Button variant="outline" onClick={handleReset}>
                <ArrowLeft className="h-4 w-4 mr-2" />
                {t("bulkImport.members.buttons.back")}
              </Button>
              <Button
                onClick={handleStartImport}
                disabled={!canProceed}
                className="gap-2"
              >
                <Upload className="h-4 w-4" />
                {t("bulkImport.members.buttons.import", { count: parseResult?.data.length || 0 })}
              </Button>
            </>
          )}

          {currentStep === "importing" && (
            <Button disabled>{t("bulkImport.members.buttons.importing")}</Button>
          )}

          {currentStep === "complete" && (
            <>
              <Button variant="outline" onClick={handleReset}>
                {t("bulkImport.members.buttons.importMore")}
              </Button>
              <Button onClick={handleClose}>{t("bulkImport.members.buttons.done")}</Button>
            </>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
