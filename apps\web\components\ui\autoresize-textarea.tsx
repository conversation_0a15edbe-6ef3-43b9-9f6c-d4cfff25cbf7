"use client";

import { cn } from "@/lib/utils";
import {
  useRef,
  useEffect,
  useCallback,
  type TextareaHTMLAttributes,
} from "react";

interface AutoResizeTextareaProps
  extends Omit<
    TextareaHTMLAttributes<HTMLTextAreaElement>,
    "value" | "onChange"
  > {
  value: string;
  onChange: (value: any) => void;
}

export function AutoResizeTextarea({
  className,
  value,
  onChange,
  ...props
}: AutoResizeTextareaProps) {
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const resizeTextarea = useCallback(() => {
    const textarea = textareaRef.current;
    if (!textarea) return;

    // Store the current scroll position
    const scrollTop = textarea.scrollTop;

    // Temporarily remove any height constraints
    textarea.style.removeProperty("height");
    textarea.style.removeProperty("min-height");
    textarea.style.removeProperty("max-height");

    // Set height to auto to get accurate scrollHeight
    textarea.style.height = "auto";

    // Calculate the new height based on content
    const minHeight = 40; // Minimum height in pixels
    const maxHeight = window.innerWidth >= 640 ? 200 : 150; // Max height responsive
    const scrollHeight = textarea.scrollHeight;
    const newHeight = Math.min(Math.max(scrollHeight, minHeight), maxHeight);

    // Apply the new height with !important to override any CSS
    textarea.style.setProperty("height", `${newHeight}px`, "important");
    textarea.style.setProperty("min-height", `${minHeight}px`, "important");

    // Handle overflow
    if (scrollHeight > maxHeight) {
      textarea.style.setProperty("overflow-y", "auto", "important");
      textarea.style.setProperty("max-height", `${maxHeight}px`, "important");
    } else {
      textarea.style.setProperty("overflow-y", "hidden", "important");
      textarea.style.removeProperty("max-height");
    }

    // Restore scroll position
    textarea.scrollTop = scrollTop;

    // Debug logging (only when value changes significantly)
    if (value.split("\n").length > 1) {
      console.log("AutoResize Debug:", {
        scrollHeight,
        newHeight,
        lineCount: value.split("\n").length,
        actualHeight: textarea.offsetHeight,
        computedHeight: window.getComputedStyle(textarea).height,
      });
    }
  }, [value]);

  useEffect(() => {
    resizeTextarea();
  }, [value, resizeTextarea]);

  // Also resize on mount to handle pre-filled values
  useEffect(() => {
    resizeTextarea();
  }, [resizeTextarea]);

  // Handle window resize for responsive behavior
  useEffect(() => {
    const handleResize = () => {
      resizeTextarea();
    };

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, [resizeTextarea]);

  return (
    <textarea
      {...props}
      value={value}
      ref={textareaRef}
      rows={1}
      onChange={(e) => {
        onChange(e);
        // Use requestAnimationFrame for smoother resizing
        requestAnimationFrame(() => {
          resizeTextarea();
        });
      }}
      onInput={() => {
        // Also trigger on input event for better responsiveness
        requestAnimationFrame(() => {
          resizeTextarea();
        });
      }}
      className={cn(
        "resize-none text-sm sm:text-base transition-all duration-200 ease-out scrollbar-hide",
        className
      )}
      style={{
        lineHeight: "1.5",
        scrollbarWidth: "none", // Firefox
        msOverflowStyle: "none", // IE/Edge
        boxSizing: "border-box",
      }}
    />
  );
}
