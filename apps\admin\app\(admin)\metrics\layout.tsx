"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { DateRangeSelectorSSR } from "@/components/metrics/date-range-selector-ssr";

import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { Download } from "lucide-react";
import { formatDateRangeForFileName } from "@/lib/export-utils";
import { LoadingOverlay } from "@/components/ui/loading-spinner";
import { useToast } from "@/components/ui/use-toast";
import { Toaster } from "@/components/ui/toaster";
import { useState, Suspense } from "react";
import { addDays } from "date-fns";

function MetricsLayout({ children }: { children: React.ReactNode }) {
  const pathname = usePathname();
  const router = useRouter();
  const searchParams = useSearchParams();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);

  // Get current date range from URL params
  const fromParam = searchParams?.get("from");
  const toParam = searchParams?.get("to");

  const dateRange = {
    from: fromParam ? new Date(fromParam) : addDays(new Date(), -30),
    to: toParam ? new Date(toParam) : new Date(),
  };

  // Get the current metrics section
  const currentSection = pathname?.split("/").pop();

  // All metrics pages are now SSR
  const isSSRPage = ["tenants", "finance", "users", "system"].includes(
    currentSection || ""
  );

  // Handle tab change
  const handleTabChange = (value: string) => {
    // Preserve current search params when changing tabs
    const params = new URLSearchParams(searchParams?.toString() || "");
    router.push(`/metrics/${value}?${params.toString()}`);
  };

  // Handle export
  const handleExport = async () => {
    try {
      setIsLoading(true);

      // Create date range params
      const dateParams = `from=${dateRange.from.toISOString()}&to=${dateRange.to.toISOString()}`;

      // Determine which metrics to export based on the current section
      const section = currentSection || "tenants";

      // Fetch the data for the current section
      let endpoint = "";
      let fileName = "";

      switch (section) {
        case "tenants":
          endpoint = `/api/metrics/export/tenants?${dateParams}`;
          fileName = `tenant_metrics_${formatDateRangeForFileName(
            dateRange
          )}.csv`;
          break;
        case "users":
          endpoint = `/api/metrics/export/users?${dateParams}`;
          fileName = `user_metrics_${formatDateRangeForFileName(
            dateRange
          )}.csv`;
          break;
        case "finance":
          endpoint = `/api/metrics/export/finance?${dateParams}`;
          fileName = `finance_metrics_${formatDateRangeForFileName(
            dateRange
          )}.csv`;
          break;
        case "system":
          endpoint = `/api/metrics/export/system?${dateParams}`;
          fileName = `system_metrics_${formatDateRangeForFileName(
            dateRange
          )}.csv`;
          break;
      }

      // Fetch the data
      const response = await fetch(endpoint);

      if (response.ok) {
        // Get the CSV content
        const csvContent = await response.text();

        // Create a blob with the CSV content
        const blob = new Blob([csvContent], {
          type: "text/csv;charset=utf-8;",
        });

        // Create a download link
        const link = document.createElement("a");

        // Create a URL for the blob
        const url = URL.createObjectURL(blob);

        // Set link properties
        link.setAttribute("href", url);
        link.setAttribute("download", fileName);

        // Add link to the document
        document.body.appendChild(link);

        // Click the link to download the file
        link.click();

        // Clean up
        document.body.removeChild(link);
        URL.revokeObjectURL(url);

        // Show success toast
        toast({
          title: "Export successful",
          description: `${fileName} has been downloaded.`,
        });
      } else {
        console.error("Failed to export metrics data");

        // Show error toast
        toast({
          title: "Export failed",
          description: "There was an error exporting the metrics data.",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error exporting metrics data:", error);

      // Show error toast
      toast({
        title: "Export failed",
        description: "There was an error exporting the metrics data.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      {isLoading && <LoadingOverlay />}
      <Toaster />

      <div className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Metrics</h1>
          <p className="text-muted-foreground">
            Detailed metrics and analytics for your SaaS platform
          </p>
        </div>
        <div className="flex flex-col space-y-2 md:flex-row md:items-center md:space-x-2 md:space-y-0">
          <Suspense
            fallback={
              <div className="h-10 w-[300px] bg-gray-200 rounded animate-pulse"></div>
            }
          >
            <DateRangeSelectorSSR />
          </Suspense>
          <Button
            variant="outline"
            size="icon"
            onClick={handleExport}
            disabled={isLoading}
          >
            <Download className="h-4 w-4" />
          </Button>
        </div>
      </div>

      <Tabs
        defaultValue={currentSection || "tenants"}
        onValueChange={handleTabChange}
        className="w-full"
      >
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="tenants">Tenants</TabsTrigger>
          <TabsTrigger value="users">Users</TabsTrigger>
          <TabsTrigger value="finance">Finance</TabsTrigger>
          <TabsTrigger value="system">System</TabsTrigger>
        </TabsList>
      </Tabs>

      {children}
    </div>
  );
}

// Loading fallback component
function MetricsLayoutFallback() {
  return (
    <div className="space-y-6">
      <div className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Metrics</h1>
          <p className="text-muted-foreground">
            Loading metrics and analytics...
          </p>
        </div>
      </div>
      <div className="animate-pulse">
        <div className="h-10 bg-gray-200 rounded mb-4"></div>
        <div className="h-64 bg-gray-200 rounded"></div>
      </div>
    </div>
  );
}

export default function MetricsLayoutNew({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <Suspense fallback={<MetricsLayoutFallback />}>
      <MetricsLayout>{children}</MetricsLayout>
    </Suspense>
  );
}
