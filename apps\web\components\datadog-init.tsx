'use client';

import { useEffect } from 'react';
import { initDatadog, setUser } from '@/lib/datadog';
import { useSession } from 'next-auth/react';

export default function DatadogInit() {
  const { data: session } = useSession();

  useEffect(() => {
    // Initialize Datadog
    initDatadog();

    // Set user information if available
    if (session?.user?.email) {
      setUser(session.user.email, {
        email: session.user.email,
        name: session.user.name,
      });
    }
  }, [session]);

  return null;
}
