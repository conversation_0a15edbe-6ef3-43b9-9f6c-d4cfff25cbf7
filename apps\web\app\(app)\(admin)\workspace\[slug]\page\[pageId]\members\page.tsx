import { getPage } from "@/services";
import { checkSharePointPageAccess } from "@/lib/sharepoint-page-access";
import { getPageMembership } from "@/lib/page-membership";
import { getTranslations } from "@/lib/server-i18n";
import { cookies } from "next/headers";
import { redirect } from "next/navigation";
import PageMembersPage from "@/components/page-members/page-members-page";

export default async function PageMembers({ params, searchParams }) {
  const { pageId, slug } = params;

  const tenantId = cookies().get("currentOrganizationId")?.value;
  const userId = cookies().get("userId")?.value;

  if (!tenantId || !userId) {
    return redirect("/sign-in");
  }

  // Get server-side translations
  const { t } = getTranslations();

  // Check if force refresh is requested
  const forceRefresh = searchParams?.refreshSharePointAccess === "true";

  const [page, sharePointAccess, pageMembership] = await Promise.all([
    getPage({
      workspaceSlug: slug,
      tenantId,
      pageId,
      userId,
    }),
    // Check SharePoint access for this page
    checkSharePointPageAccess(userId, tenantId, pageId, forceRefresh, t),
    // Get user's page membership
    getPageMembership(userId, pageId),
  ]);

  if (!page || !page.data) {
    return redirect(`/my-hub`);
  }

  // Check if user has permission to manage page members (ADMIN role required)
  if (!pageMembership || pageMembership.role !== "ADMIN") {
    return redirect(`/workspace/${slug}/page/${pageId}`);
  }

  return (
    <PageMembersPage
      tenantId={tenantId}
      page={page.data}
      workspaceSlug={slug}
      pageId={pageId}
      sharePointAccess={sharePointAccess}
      currentUserRole={pageMembership.role}
    />
  );
}
