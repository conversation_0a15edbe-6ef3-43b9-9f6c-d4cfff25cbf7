import React from "react";
import { DocsThemeConfig } from "nextra-theme-docs";
import { AppLogo } from "./components/applogo";
import { LanguageSwitcher } from "./components/LanguageSwitcher";
import { useDocsThemeConfig } from "@/components/theme/docs-theme-applier";

const config: DocsThemeConfig = {
  useNextSeoProps() {
    return {
      titleTemplate: `%s – Swiss Knowledge Hub`,
    };
  },
  search: {
    placeholder: "Search documentation...",
  },

  navbar: {
    extraContent: () => <LanguageSwitcher />,
  },
  docsRepositoryBase: "https://github.com/increscotech/apex-ai",
  navigation: {
    prev: true,
    next: true,
  },
  head: () => {
    // Apply theme configuration for docs
    useDocsThemeConfig();

    return (
      <>
        {/* Default favicon */}
        <link
          rel="icon"
          href="/favicon.png"
          type="image/png"
          media="(prefers-color-scheme: light)"
        />
        <link
          rel="icon"
          href="/favicon-dark.png"
          type="image/png"
          media="(prefers-color-scheme: dark)"
        />
      </>
    );
  },
  logo: <AppLogo />,
  footer: {
    component: null,
  },
  sidebar: {
    defaultMenuCollapseLevel: 1,
  },
  editLink: {
    component: null,
  },
  feedback: {
    content: null,
  },
};

export default config;
