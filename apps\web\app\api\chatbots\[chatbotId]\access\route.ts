import { NextRequest, NextResponse } from "next/server";
import db from "@/lib/shared-db";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/next-auth";

// CORS headers for cross-origin requests
const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
  "Access-Control-Allow-Headers": "Content-Type, Authorization, X-User-Token",
};

/**
 * Handle preflight OPTIONS requests for CORS
 */
export async function OPTIONS() {
  return new NextResponse(null, { status: 200, headers: corsHeaders });
}

/**
 * Check if user has access to a specific chatbot
 * For public chatbots: always allow
 * For private chatbots: check if user is member of any associated company
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { chatbotId: string } }
) {
  try {
    const session: any = await getServerSession(authOptions);
    if (!session?.userId) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401, headers: corsHeaders }
      );
    }

    const { chatbotId } = params;

    // Get chatbot details
    const chatbot = await db.chatbot.findUnique({
      where: { id: chatbotId },
      select: {
        id: true,
        access: true,
        companyIds: true,
        tenantId: true,
        isActive: true,
      },
    });

    if (!chatbot) {
      return NextResponse.json(
        { error: "Chatbot not found" },
        { status: 404, headers: corsHeaders }
      );
    }

    if (!chatbot.isActive) {
      return NextResponse.json(
        { error: "Chatbot is inactive" },
        { status: 403, headers: corsHeaders }
      );
    }

    // Public chatbots are accessible to everyone
    if (chatbot.access === "public") {
      return NextResponse.json(
        {
          hasAccess: true,
          reason: "public_chatbot",
        },
        { headers: corsHeaders }
      );
    }

    // For private chatbots, check company membership
    if (chatbot.access === "private") {
      if (!chatbot.companyIds || chatbot.companyIds.length === 0) {
        return NextResponse.json({
          hasAccess: false,
          reason: "no_companies_configured",
        });
      }

      // Check if user is a member of any of the chatbot's companies
      const userCompanyMemberships = await db.companyMember.findMany({
        where: {
          userId: session.userId,
          companyId: { in: chatbot.companyIds },
          status: "ACTIVE", // Only active memberships
        },
        select: {
          companyId: true,
          company: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      });

      if (userCompanyMemberships.length > 0) {
        return NextResponse.json(
          {
            hasAccess: true,
            reason: "company_member",
            companies: userCompanyMemberships.map((m) => m.company),
          },
          { headers: corsHeaders }
        );
      } else {
        return NextResponse.json({
          hasAccess: false,
          reason: "not_company_member",
        });
      }
    }

    // Default deny
    return NextResponse.json({
      hasAccess: false,
      reason: "unknown_access_type",
    });
  } catch (error) {
    console.error("Error checking chatbot access:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500, headers: corsHeaders }
    );
  }
}

/**
 * Get detailed access information for a chatbot
 * Including list of companies that have access
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { chatbotId: string } }
) {
  try {
    const session: any = await getServerSession(authOptions);
    if (!session?.userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { chatbotId } = params;

    // Get chatbot with company details
    const chatbot = await db.chatbot.findUnique({
      where: { id: chatbotId },
      select: {
        id: true,
        name: true,
        access: true,
        companyIds: true,
        tenantId: true,
        isActive: true,
      },
    });

    if (!chatbot) {
      return NextResponse.json({ error: "Chatbot not found" }, { status: 404 });
    }

    // Get company details for private chatbots
    let companies: any[] = [];
    if (chatbot.access === "private" && chatbot.companyIds.length > 0) {
      companies = await db.company.findMany({
        where: {
          id: { in: chatbot.companyIds },
        },
        select: {
          id: true,
          name: true,
          slug: true,
          members: {
            select: {
              userId: true,
              status: true,
              user: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                },
              },
            },
            where: {
              status: "ACTIVE",
            },
          },
        },
      });
    }

    // Check user's access
    const accessCheck = await fetch(
      `${request.nextUrl.origin}/api/chatbots/${chatbotId}/access`,
      {
        headers: {
          Cookie: request.headers.get("Cookie") || "",
        },
      }
    );
    const accessResult = await accessCheck.json();

    return NextResponse.json({
      chatbot: {
        id: chatbot.id,
        name: chatbot.name,
        access: chatbot.access,
        isActive: chatbot.isActive,
      },
      companies,
      userAccess: accessResult,
      totalAuthorizedUsers: companies.reduce(
        (total, company) => total + company.members.length,
        0
      ),
    });
  } catch (error) {
    console.error("Error getting chatbot access details:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
