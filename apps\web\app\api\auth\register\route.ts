import { hash } from "bcrypt";
import crypto from "crypto";
import db from "@/lib/shared-db";
import { NextResponse } from "next/server";
import Mailer from "@/lib/email/send-email";
import {
  getLanguageFromRequest,
  createServerTranslator,
} from "@/lib/server-translations";

export async function POST(request: any) {
  // Extract data from request
  const { email, password, name, organization, inviteSlug } =
    await request.json();

  const language = getLanguageFromRequest(request);
  const t = createServerTranslator(language);

  try {
    const emailHash = crypto.createHash("sha256").update(email).digest("hex");
    // Check if user already exists
    const existingUser = await db.user.findFirst({
      where: {
        emailHash,
      },
    });

    if (existingUser?.email === email) {
      return NextResponse.json(
        { error: t("api.errors.userAlreadyExists") },
        { status: 400 }
      );
    }

    // Hash the password
    const hashedPassword = await hash(password, 12);

    // Generate verification token
    const token = crypto.randomBytes(32).toString("hex");
    const expires = new Date();
    expires.setHours(expires.getHours() + 24); // Token expires in 24 hours

    // Check if this is an invite-based registration
    let inviteCompany: any = null;
    if (inviteSlug) {
      inviteCompany = await db.company.findFirst({
        where: {
          slug: {
            equals: inviteSlug,
            mode: "insensitive",
          },
          status: "active",
        },
        include: {
          tenant: true,
          members: true,
        },
      });

      // Check if company exists and has capacity
      if (!inviteCompany) {
        return NextResponse.json(
          { error: "Invalid or expired invitation" },
          { status: 400 }
        );
      }

      if (inviteCompany.members.length >= inviteCompany.maxSignups) {
        return NextResponse.json(
          { error: "Company has reached its maximum capacity" },
          { status: 400 }
        );
      }
    }

    // Use a transaction to create user and handle memberships
    const result = await db.$transaction(async (tx) => {
      // 1. Create the user
      const newUser = await tx.user.create({
        data: {
          email,
          emailHash,
          name,
          password: hashedPassword,
          emailVerified: null, // Set initial verification status
        },
      });

      // Store verification token
      await tx.verificationToken.create({
        data: {
          identifier: email,
          token,
          expires,
          type: "EMAIL_VERIFICATION",
        },
      });

      // 2. Handle invite-based registration
      if (inviteCompany) {
        // Add user to the organization (tenant) if not already a member
        await tx.membership.create({
          data: {
            userId: newUser.id,
            tenantId: inviteCompany.tenantId,
            role: "MEMBER", // Default role for organization
          },
        });

        // Add user to the company
        await tx.companyMember.create({
          data: {
            userId: newUser.id,
            companyId: inviteCompany.id,
            role: "MEMBER", // Default role for company
            status: "ACTIVE",
          },
        });

        return {
          user: newUser,
          tenant: inviteCompany.tenant,
          company: inviteCompany,
          isInviteRegistration: true,
        };
      }

      // 3. If organization details are provided (normal registration), create organization and membership
      if (organization && organization.name) {
        // Create the tenant/organization
        const newTenant = await tx.tenant.create({
          data: {
            name: organization.name,
            slug: `${
              organization?.slug ??
              organization?.name?.toLowerCase().replace(/\s+/g, "-")
            }-${Date.now()}`,
            description: organization.description,
            url: organization.url,
            isOnboarded: true, // Mark as onboarded since we're creating both in one step
          },
        });

        // Create membership with OWNER role
        await tx.membership.create({
          data: {
            userId: newUser.id,
            tenantId: newTenant.id,
            role: "OWNER", // Make the user an owner
          },
        });

        return { user: newUser, tenant: newTenant };
      }

      return { user: newUser };
    });

    // Send verification email
    const baseUrl =
      process.env.NEXT_PUBLIC_API_BASE_URL ||
      (process.env.NODE_ENV === "development" ? "http://localhost:3000" : "");
    const verificationUrl = `${baseUrl}/verify-email/${token}`;

    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h1 style="text-align: center; color: #4f46e5; margin-bottom: 24px;">Swiss Knowledge Hub</h1>
        <h2>Verify Your Email Address</h2>
        <p>Thank you for registering! Please verify your email address by clicking the link below:</p>
        <p>
          <a href="${verificationUrl}" style="display: inline-block; padding: 10px 20px; background-color: #4f46e5; color: white; text-decoration: none; border-radius: 5px;">
            Verify Email
          </a>
        </p>
        <p>Or copy and paste this link in your browser:</p>
        <p>${verificationUrl}</p>
        <p>This link will expire in 24 hours.</p>
      </div>
    `;

    await Mailer.send({
      from:
        process.env.NEXT_PUBLIC_SEND_EMAIL_FROM ||
        "<EMAIL>",
      to: email,
      subject: t("api.email.subjects.verifyEmail"),
      html,
    });

    // Return success response
    const responseMessage = result.isInviteRegistration
      ? `Registration successful! You've been added to ${result.company.name}. Please check your email to verify your account.`
      : "Registration successful. Please check your email to verify your account.";

    return NextResponse.json(
      {
        message: t("api.success.registrationSuccessful"),
        data: {
          userId: result.user.id,
          ...(result.tenant ? { tenantId: result.tenant.id } : {}),
          ...(result.company
            ? {
                companyId: result.company.id,
                companyName: result.company.name,
                isInviteRegistration: true,
              }
            : {}),
        },
      },
      { status: 201 }
    );
  } catch (error) {
    console.error("Error in register API:", error);
    return NextResponse.json(
      { error: t("api.errors.registrationFailed") },
      { status: 500 }
    );
  }
}
