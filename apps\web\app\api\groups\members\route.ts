import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import db from "@/lib/shared-db";
import { authOptions } from "@/lib/next-auth/src/auth-options";
import { withPermission } from "@/lib/permission-middleware";
import crypto from "crypto";

// Add a user to a group
async function addUserToGroup(request: Request) {
  try {
    const session: any = await getServerSession(authOptions);

    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { groupId, email, userId, tenantId } = await request.json();

    if (!groupId || (!email && !userId) || !tenantId) {
      return NextResponse.json(
        { error: "Group ID, (Email or User ID), and Tenant ID are required" },
        { status: 400 }
      );
    }
    const memberShipId = session?.memberships?.find(
      (m: any) => m.tenant.id === tenantId
    )?.id;

    // Verify the group exists and belongs to the tenant
    const [group] = await Promise.all([
      db.group.findFirst({
        where: {
          id: groupId,
          tenantId,
        },
      }),
    ]);
    if (!group) {
      return NextResponse.json(
        { error: "Group not found or does not belong to this tenant" },
        { status: 404 }
      );
    }
    // Get all workspaces assigned to this group
    const groupWorkspaces = await db.groupWorkspace.findMany({
      where: {
        groupId,
      },
      include: {
        workspace: true,
        customRole: true,
      },
    });

    // Find the user by email or userId
    let user: any;
    if (userId) {
      // Find user by ID
      user = await db.user.findUnique({
        where: {
          id: userId,
        },
        include: {
          membership: {
            where: {
              tenantId,
            },
          },
        },
      });
    } else {
      // Find user by email (existing logic)
      const emailHash = crypto.createHash("sha256").update(email).digest("hex");
      user = await db.user.findUnique({
        where: {
          emailHash,
        },
        include: {
          membership: {
            where: {
              tenantId,
            },
          },
        },
      });
    }

    if (!user) {
      return NextResponse.json(
        { error: "User not found. Add the user first" },
        { status: 404 }
      );
    }

    // Verify user is a member of the organization
    if (user.membership.length === 0) {
      return NextResponse.json(
        { error: "User must be a member of the organization first" },
        { status: 403 }
      );
    }

    // Check if user is already a member of the group
    const existingMember = await db.groupMember.findFirst({
      where: {
        userId: user.id,
        groupId,
      },
    });

    if (existingMember) {
      return NextResponse.json(
        { error: "User is already a member of this group" },
        { status: 400 }
      );
    }

    // Add the user to the group
    const groupMember = await db.groupMember.create({
      data: {
        userId: user.id,
        groupId,
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            image: true,
          },
        },
      },
    });

    // For each workspace in the group, ensure the user has access
    for (const groupWorkspace of groupWorkspaces) {
      // Check if the user already has direct access to the workspace
      const existingWorkspaceMember = await db.workspaceMember.findFirst({
        where: {
          userId: user.id,
          workspaceId: groupWorkspace.workspaceId,
        },
      });

      // If the user doesn't have direct access, create a workspace member entry
      if (!existingWorkspaceMember) {
        await db.workspaceMember.create({
          data: {
            userId: user.id,
            workspaceId: groupWorkspace.workspaceId,
            membershipId: memberShipId,
            customRoleId: groupWorkspace.customRoleId || group.customRoleId,
            role: "CUSTOM", // Default role for group-based access
          },
        });
      }
    }

    return NextResponse.json({
      message: "User added to group successfully",
      data: groupMember,
    });
  } catch (error) {
    console.error("Error adding user to group:", error);
    return NextResponse.json(
      { error: "Failed to add user to group" },
      { status: 500 }
    );
  }
}

// Remove a user from a group
async function removeUserFromGroup(request: Request) {
  try {
    const session: any = await getServerSession(authOptions);

    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const url = new URL(request.url);
    const groupId = url.searchParams.get("groupId");
    const userId = url.searchParams.get("userId");
    const tenantId = url.searchParams.get("tenantId");

    if (!groupId || !userId || !tenantId) {
      return NextResponse.json(
        { error: "Group ID, User ID, and Tenant ID are required" },
        { status: 400 }
      );
    }

    // Verify the group exists and belongs to the tenant
    const group = await db.group.findFirst({
      where: {
        id: groupId,
        tenantId,
      },
      include: {
        customRole: true,
      },
    });

    if (!group) {
      return NextResponse.json(
        { error: "Group not found or does not belong to this tenant" },
        { status: 404 }
      );
    }

    // Find the group member
    const groupMember = await db.groupMember.findFirst({
      where: {
        groupId,
        userId,
      },
    });

    if (!groupMember) {
      return NextResponse.json(
        { error: "User is not a member of this group" },
        { status: 404 }
      );
    }

    // Get all workspaces assigned to this group (direct assignments)
    const groupWorkspaces = await db.groupWorkspace.findMany({
      where: {
        groupId,
      },
      include: {
        workspace: true,
        customRole: true,
      },
    });

    // Get all workspaces accessible through this group's role assignments
    // This includes workspaces that have the same custom role as the group
    const roleBasedWorkspaces = group.customRoleId
      ? await db.workspace.findMany({
          where: {
            tenantId,
            workspaceMember: {
              some: {
                customRoleId: group.customRoleId,
                role: "CUSTOM",
              },
            },
          },
          include: {
            workspaceMember: {
              where: {
                userId,
                customRoleId: group.customRoleId,
                role: "CUSTOM",
              },
            },
          },
        })
      : [];

    // Combine all affected workspaces (both direct and role-based)
    const allAffectedWorkspaceIds = new Set([
      ...groupWorkspaces.map((gw) => gw.workspaceId),
      ...roleBasedWorkspaces
        .filter((w) => w.workspaceMember.length > 0)
        .map((w) => w.id),
    ]);

    // Remove the user from the group and clean up workspace access in a transaction
    await db.$transaction(async (tx) => {
      // FIRST: Check for other group access BEFORE removing the user from the current group
      const otherGroupAccessMap = new Map();

      for (const workspaceId of allAffectedWorkspaceIds) {
        // Check for access through other groups (direct workspace assignments)
        const otherGroupWorkspaceAccess = await tx.groupMember.findFirst({
          where: {
            userId,
            groupId: {
              not: groupId, // Exclude the current group we're removing from
            },
            group: {
              groupWorkspaces: {
                some: {
                  workspaceId,
                },
              },
            },
          },
        });

        // Check for access through other groups with same role assignments
        const otherGroupRoleAccess = group.customRoleId
          ? await tx.groupMember.findFirst({
              where: {
                userId,
                groupId: {
                  not: groupId, // Exclude the current group we're removing from
                },
                group: {
                  customRoleId: group.customRoleId,
                },
              },
            })
          : null;

        // User has other group access if either direct or role-based access exists
        const hasOtherGroupAccess = !!(otherGroupWorkspaceAccess || otherGroupRoleAccess);
        otherGroupAccessMap.set(workspaceId, hasOtherGroupAccess);
      }

      // SECOND: Remove the user from the group
      await tx.groupMember.delete({
        where: {
          id: groupMember.id,
        },
      });

      // THIRD: Clean up workspace memberships based on the pre-checked access
      for (const workspaceId of allAffectedWorkspaceIds) {
        const hasOtherGroupAccess = otherGroupAccessMap.get(workspaceId);

        if (!hasOtherGroupAccess) {
          // Check if user has direct workspace access (OWNER/ADMIN roles)
          const directAccess = await tx.workspaceMember.findFirst({
            where: {
              userId,
              workspaceId,
              role: {
                in: ["OWNER", "ADMIN"],
              },
            },
          });

          // Only remove access if user doesn't have direct OWNER/ADMIN access
          if (!directAccess) {
            // For direct group-workspace assignments
            const directGroupWorkspace = groupWorkspaces.find(
              (gw) => gw.workspaceId === workspaceId
            );
            if (directGroupWorkspace) {
              const targetCustomRoleId = directGroupWorkspace.customRoleId || group.customRoleId;

              await tx.workspaceMember.deleteMany({
                where: {
                  userId,
                  workspaceId,
                  role: "CUSTOM", // Only remove group-based access
                  customRoleId: targetCustomRoleId || null,
                },
              });
            }

            // For role-based workspace access
            if (group.customRoleId) {
              await tx.workspaceMember.deleteMany({
                where: {
                  userId,
                  workspaceId,
                  role: "CUSTOM", // Only remove group-based access
                  customRoleId: group.customRoleId,
                },
              });
            }
          }
        }
      }
    });

    return NextResponse.json({
      message: "User removed from group successfully",
      workspacesAffected: allAffectedWorkspaceIds.size,
    });
  } catch (error) {
    console.error("Error removing user from group:", error);
    return NextResponse.json(
      { error: "Failed to remove user from group" },
      { status: 500 }
    );
  }
}

// Get all members of a group
async function getGroupMembers(request: Request) {
  try {
    const url = new URL(request.url);
    const groupId = url.searchParams.get("groupId");
    const tenantId = url.searchParams.get("tenantId");

    if (!groupId || !tenantId) {
      return NextResponse.json(
        { error: "Group ID and Tenant ID are required" },
        { status: 400 }
      );
    }

    // Verify the group exists and belongs to the tenant
    const group = await db.group.findFirst({
      where: {
        id: groupId,
        tenantId,
      },
    });

    if (!group) {
      return NextResponse.json(
        { error: "Group not found or does not belong to this tenant" },
        { status: 404 }
      );
    }

    // Get all members of the group
    const groupMembers = await db.groupMember.findMany({
      where: {
        groupId,
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            image: true,
          },
        },
      },
    });

    return NextResponse.json({
      data: groupMembers,
      message: "Group members retrieved successfully",
    });
  } catch (error) {
    console.error("Error fetching group members:", error);
    return NextResponse.json(
      { error: "Failed to fetch group members" },
      { status: 500 }
    );
  }
}

// Export the handlers with permission checks
export const POST = withPermission(addUserToGroup, "UPDATE", "WORKSPACE");
export const DELETE = withPermission(
  removeUserFromGroup,
  "UPDATE",
  "WORKSPACE"
);
export const GET = withPermission(getGroupMembers, "READ", "WORKSPACE");
