/* eslint-disable no-unused-vars */
"use client";

import React, { useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  ArrowUpIcon,
  Globe,
  Plus,
  Upload,
  Brain,
  Server,
  Search,
} from "lucide-react";
import { AutoResizeTextarea } from "@/components/ui/autoresize-textarea";
import { useLanguage } from "@/lib/language-context";
import { Message, ImageAttachment } from "../types";
import { LLMScopeCapabilities } from "@/types/llm-scope";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { ImageUpload } from "./ImageUpload";
import { cn } from "@/lib/utils";

type SearchMode = "internal" | "web" | "hybrid" | "deep_research" | "mcp" | "";

interface ChatInputFormProps {
  input: string;
  setInput: (input: string) => void;
  handleKeyDown: (e: any) => void;
  handleChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
  handleAISubmit: (e: React.FormEvent<HTMLFormElement>) => void;
  setMessages: React.Dispatch<React.SetStateAction<Message[]>>;
  status: string;
  state: string;
  isMobile: boolean;
  includeWebResults: boolean;
  setIncludeWebResults: (include: boolean) => void;
  webSearchLimitExceeded?: boolean;
  selectedImages: ImageAttachment[];
  setImageContext: (imageContext: string) => void;
  setAudioContext: (audioContext: string) => void;
  setVideoContext: (videoContext: string) => void;
  setSelectedImages: (images: ImageAttachment[]) => void;
  llmCapabilities?: LLMScopeCapabilities;
  selectedSearchMode?: SearchMode;
  setSelectedSearchMode?: React.Dispatch<React.SetStateAction<SearchMode>>;
}

export const ChatInputForm: React.FC<ChatInputFormProps> = ({
  input,
  setInput,
  handleKeyDown,
  handleChange,
  handleAISubmit,
  setMessages,
  status,
  includeWebResults,
  setIncludeWebResults,
  webSearchLimitExceeded,
  selectedImages,
  setSelectedImages,
  llmCapabilities,
  selectedSearchMode: propSelectedSearchMode,
  setSelectedSearchMode: propSetSelectedSearchMode,

  setImageContext,
  setAudioContext,
}) => {
  const { t } = useLanguage();

  const handleImagesSelected = (images: ImageAttachment[]) => {
    setSelectedImages(images);
  };

  const handleRemoveImage = (imageId: string) => {
    setSelectedImages(selectedImages.filter((img) => img.id !== imageId));
  };

  const canSubmit = input.trim() !== "" || selectedImages.length > 0;

  // Check if web search is allowed by LLM scope
  const webSearchAllowed = llmCapabilities?.canAccessWebSearch ?? true;
  const hybridAllowed = llmCapabilities?.canDeepSearch ?? true;
  const mcpAllowed = llmCapabilities?.canAccessMCPServers ?? false;

  // Check if MCP_ONLY scope is active (only MCP allowed, no internal docs or web search)
  const isMCPOnlyScope =
    mcpAllowed &&
    !llmCapabilities?.canAccessInternalDocs &&
    !llmCapabilities?.canAccessWebSearch;

  // Use props for search mode or fallback to local state
  const selectedSearchMode = propSelectedSearchMode || "";
  const setSelectedSearchMode = propSetSelectedSearchMode || (() => {});

  // Auto-select MCP mode when MCP_ONLY scope is active
  useEffect(() => {
    if (
      llmCapabilities?.canAccessMCPServers &&
      !llmCapabilities?.canAccessInternalDocs &&
      !llmCapabilities?.canAccessWebSearch &&
      selectedSearchMode !== "mcp"
    ) {
      // This indicates MCP_ONLY scope
      setSelectedSearchMode("mcp");
    }
  }, [llmCapabilities, selectedSearchMode, setSelectedSearchMode]);

  let initialSelectedModes: Set<"internal" | "web"> = new Set();
  if (selectedSearchMode === "hybrid") {
    initialSelectedModes = new Set(["internal", "web"]);
  } else if (selectedSearchMode === "internal") {
    initialSelectedModes = new Set(["internal"]);
  } else if (selectedSearchMode === "web") {
    initialSelectedModes = new Set(["web"]);
  }

  // Track selected modes for hybrid logic
  const [selectedModes, setSelectedModes] =
    React.useState<Set<"internal" | "web">>(initialSelectedModes);

  // Update search mode based on selected modes
  React.useEffect(() => {
    if (selectedModes.size === 0) {
      // setSelectedSearchMode("");
      setIncludeWebResults(false);
    } else if (selectedModes.size === 2) {
      setSelectedSearchMode("hybrid");
      setIncludeWebResults(true);
    } else if (selectedModes.has("internal")) {
      setSelectedSearchMode("internal");
      setIncludeWebResults(false);
    } else if (selectedModes.has("web")) {
      setSelectedSearchMode("web");
      setIncludeWebResults(true);
    }
  }, [selectedModes, setSelectedSearchMode, setIncludeWebResults]);

  // Handle search mode changes
  const handleSearchModeChange = (
    mode: "internal" | "web" | "deep_research" | "mcp"
  ) => {
    if (mode === "deep_research") {
      if (selectedSearchMode === "deep_research") {
        setSelectedSearchMode("");
        setIncludeWebResults(false);
      } else {
        setSelectedSearchMode("deep_research");
        setIncludeWebResults(true);
      }
      return;
    }

    if (mode === "mcp") {
      if (selectedSearchMode === "mcp") {
        setSelectedSearchMode("");
      } else {
        setSelectedSearchMode("mcp");
      }
      return;
    }

    // Handle internal and web mode toggles
    const newSelectedModes = new Set(selectedModes);
    if (newSelectedModes.has(mode)) {
      newSelectedModes.delete(mode);
    } else {
      newSelectedModes.add(mode);
    }
    setSelectedModes(newSelectedModes);
  };

  return (
    <div className="flex flex-col items-center w-full">
      {/* Enhanced Image Upload Section */}

      {/* Chat input form */}
      <div className="w-full flex items-start">
        <form
          onSubmit={(e) => {
            e.preventDefault();
            if (!canSubmit) return;
            // User message will be added in handleAISubmit, so just call it directly
            handleAISubmit(e);
          }}
          className={cn(
            "w-full max-w-full bg-background border border-input rounded-[16px] px-3 sm:px-4 py-2 sm:py-3 shadow-lg transition-all duration-300 ease-out mb-6 flex flex-col",
            selectedImages.length > 0 && "border-primary/30 shadow-primary/10",
            input.length > 100 && "shadow-xl border-primary/20" // Enhanced styling for longer inputs
          )}
        >
          <AutoResizeTextarea
            onKeyDown={handleKeyDown}
            onChange={(e: any) => handleChange(e)}
            value={input}
            placeholder={
              selectedImages.length > 0
                ? t("chat.enterMessageWithImages")
                : t("chat.enterMessage")
            }
            className="w-full bg-transparent text-sm sm:text-base focus:outline-none px-1 py-1 sm:px-2 placeholder:text-muted-foreground/60 resize-none"
            disabled={status === "streaming" || status === "submitted"}
          />

          {/* Action buttons section */}
          <div className="flex items-center justify-between w-full">
            <div className="flex items-center gap-1 mr-2 overflow-x-auto scrollbar-hide min-w-0 flex-1">
              {/* Add Button with Dropdown */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    type="button"
                    size="icon"
                    variant="ghost"
                    className="h-7 w-7 rounded-full hover:bg-muted/50 shrink-0"
                    disabled={status === "streaming" || status === "submitted"}
                  >
                    <Plus className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent
                  align="start"
                  side="top"
                  sideOffset={8}
                  className="w-56"
                >
                  <DropdownMenuItem
                    onClick={() => {
                      // Trigger the hidden file input
                      const fileInput = document.querySelector(
                        'input[type="file"]'
                      ) as HTMLInputElement;
                      if (fileInput) {
                        fileInput.click();
                      }
                    }}
                    disabled={selectedImages.length >= 5}
                    className="flex items-center gap-3 cursor-pointer"
                  >
                    <Upload className="h-4 w-4" />
                    <div className="flex flex-col">
                      <span className="font-medium">
                        {t("chat.addFilesAndImages")}
                      </span>
                    </div>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>

              {/* Internal Search Button */}
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      type="button"
                      size="sm"
                      variant={
                        selectedModes.has("internal") ? "default" : "ghost"
                      }
                      className="h-7 px-2 rounded-full text-xs shrink-0"
                      disabled={
                        !llmCapabilities?.canAccessInternalDocs ||
                        isMCPOnlyScope ||
                        status === "streaming" ||
                        status === "submitted"
                      }
                      onClick={() => handleSearchModeChange("internal")}
                    >
                      <Brain className="h-4 w-4" />
                      <span className="ml-1 hidden md:inline">
                        {t("chatInput.internal")}
                      </span>
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent side="top">
                    <p>
                      {!llmCapabilities?.canAccessInternalDocs
                        ? t("chat.searchModeTooltips.internalSearchDisabled")
                        : isMCPOnlyScope
                          ? t("chat.searchModeTooltips.mcpOnlyRestriction")
                          : t("chat.searchModeTooltips.internalSearch")}
                    </p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>

              {/* Web Search Button */}
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      type="button"
                      size="sm"
                      variant={selectedModes.has("web") ? "default" : "ghost"}
                      className="h-7 px-2 rounded-full text-xs shrink-0"
                      disabled={
                        !webSearchAllowed ||
                        isMCPOnlyScope ||
                        status === "streaming" ||
                        status === "submitted"
                      }
                      onClick={() => handleSearchModeChange("web")}
                    >
                      <Globe className="h-4 w-4" />
                      <span className="ml-1 hidden md:inline">
                        {t("chatInput.web")}
                      </span>
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent side="top">
                    <p>
                      {!webSearchAllowed
                        ? t("chat.searchModeTooltips.webSearchDisabled")
                        : isMCPOnlyScope
                          ? t("chat.searchModeTooltips.mcpOnlyRestriction")
                          : t("chat.searchModeTooltips.webSearch")}
                    </p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>

              <Button
                type="button"
                size="sm"
                variant={selectedSearchMode === "mcp" ? "default" : "ghost"}
                className="h-7 px-2 rounded-full text-xs shrink-0"
                disabled={
                  status === "streaming" ||
                  status === "submitted" ||
                  !llmCapabilities?.canAccessMCPServers
                }
                onClick={() => handleSearchModeChange("mcp")}
              >
                <Server className="h-4 w-4" />
                <span className="ml-1 hidden md:inline">
                  {t("chatInput.mcp")}
                </span>
              </Button>

              {/* Deep Research Button */}
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      type="button"
                      size="sm"
                      variant={
                        selectedSearchMode === "deep_research"
                          ? "default"
                          : "ghost"
                      }
                      className="h-7 px-2 rounded-full text-xs shrink-0"
                      disabled={
                        !hybridAllowed ||
                        status === "streaming" ||
                        status === "submitted"
                      }
                      onClick={() => handleSearchModeChange("deep_research")}
                    >
                      <Search className="h-4 w-4" />
                      <span className="ml-1 hidden md:inline">
                        {t("chatInput.deepResearch")}
                      </span>
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent side="top">
                    <p>
                      {!hybridAllowed
                        ? t("chat.searchModeTooltips.deepResearchDisabled")
                        : t("chat.searchModeTooltips.deepResearch")}
                    </p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
            {/* Submit Button */}
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    size="icon"
                    className="rounded-full h-8 w-8 sm:h-10 sm:w-10 shrink-0"
                    disabled={
                      !canSubmit ||
                      status === "streaming" ||
                      status === "submitted"
                    }
                  >
                    <ArrowUpIcon className="h-3 w-3 sm:h-4 sm:w-4" />
                  </Button>
                </TooltipTrigger>
              </Tooltip>
            </TooltipProvider>
          </div>
          {/* Always render ImageUpload component so file input is available */}
          <ImageUpload
            onImagesSelected={handleImagesSelected}
            selectedImages={selectedImages}
            setImageContext={setImageContext}
            setAudioContext={setAudioContext}
            onRemoveImage={handleRemoveImage}
            disabled={status === "streaming" || status === "submitted"}
          />
        </form>
      </div>
    </div>
  );
};
