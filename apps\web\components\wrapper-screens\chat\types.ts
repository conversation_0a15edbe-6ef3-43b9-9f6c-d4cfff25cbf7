export interface Source {
  content: string;
  metadata: {
    workspace_id?: string;
    fileId?: string;
    page?: number;
    fileName?: string;
    workspace?: {
      slug?: string;
      name?: string;
    };
    relevanceScore?: number;
    relevantText?: string;
    [key: string]: any;
  };
}

export interface ImageAttachment {
  id: string;
  url: string;
  name: string;
  type: string;
  size: number;
  preview?: string; // Base64 preview for display
  data?: string; // Base64 data for CopilotKit
  imageContext?: string; // Image context from CopilotKit
  audioContext?: string; // Audio transcription context from Azure Video Indexer
  videoContext?: string; // Video analysis context from Azure Video Indexer
  videoJobId?: string; // Job ID for async video processing
  videoProcessingAsync?: boolean; // Flag indicating async video processing
}

export interface Message {
  id?: string;
  role: "user" | "assistant";
  content: string;
  alternatives?: string[]; // Legacy field - Array of alternative (regenerated) responses
  currentAlternativeIndex?: number; // Legacy field - Index of the currently displayed alternative (-1 for original)
  sources?: Source[]; // Document sources/citations
  originalMessageId?: string; // If this is a regenerated message, points to the original message
  regeneratedMessages?: Message[]; // Messages that are regenerations of this one
  editedMessages?: Message[]; // Messages that are edits of this one
  images?: ImageAttachment[]; // Image attachments
  metadata?: {
    feedback?: "like" | "dislike";
    images?: ImageAttachment[]; // Image attachments
    originalResponse?: boolean; // Indicates this is the original response
    editedResponse?: boolean; // Indicates this is an edited response
    editTriggeredResponse?: boolean; // Indicates this is a response triggered by an edit
    isEditing?: boolean; // Indicates this message is currently being edited
    regeneratedResponse?: boolean; // Indicates this is a regenerated response
    hidden?: boolean; // To hide messages in the UI
    isSelected?: boolean; // Indicates if this regenerated message is currently selected
    activeRegeneratedIndex?: number | null; // Index of the currently active regenerated message
    generationLevel?: number; // Level of regeneration (1 = first regeneration, 2 = regeneration of regeneration, etc.)
    isStreaming?: boolean; // Indicates if this message is currently streaming content
    includeWebResults?: boolean; // Indicates if web search should be included for this message
    hasImages?: boolean; // Indicates if this message contains images
    // CopilotKit specific metadata
    searchMode?: string; // Search mode used for this message
    selectedMCPServers?: string[]; // Selected MCP server IDs for this message
    tools_used?: string[]; // Tools used by CopilotKit agent
    elapsed_time?: number; // Response time
    thinking?: string; // DeepSeek R1 thinking process
    imageContext?: string; // Image context from CopilotKit
    audioContext?: string; // Audio transcription context from Azure Video Indexer
    isDeepAnswerRequest?: boolean; // Indicates this is a deep answer request
    isDeepAnswer?: boolean; // Indicates this is a deep answer response
    originalMessageId?: string; // Reference to the original message for deep answers
    // Deep Research Agent fields
    researchProgress?: {
      phase?: string;
      message?: string;
      progress?: number;
      iteration?: number;
      confidence?: number;
      gaps_identified?: string[];
      research_plan?: {
        main_topic?: string;
        subtopics?: string[];
        research_questions?: string[];
      };
      iterations?: Array<{
        iteration: number;
        phase: string;
        confidence: number;
        gaps_identified: string[];
        findings_preview: string;
      }>;
    };
    research_summary?: {
      iterations_conducted?: number;
      final_confidence?: number;
      quality_score?: number;
      subtopics_covered?: number;
      sources_consulted?: number;
    };
    iterations?: Array<{
      iteration: number;
      phase: string;
      confidence: number;
      gaps_identified: string[];
      findings_preview: string;
    }>;
  };
  createdAt?: string | Date; // For sorting regenerated messages
}

export interface DisplayIndices {
  [messageId: string]: number;
}

export interface RegeneratedMessageCounts {
  [messageId: string]: number;
}

export interface ChatFormProps {
  userId: string;
  chats: {
    id: string;
    title?: string;
    messages: Message[];
  } | null;
  tenantId: string;
  userName: string;
  settings: any;
}
