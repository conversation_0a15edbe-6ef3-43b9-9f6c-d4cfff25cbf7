"use client";

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { useLanguage } from "@/lib/language-context";

interface RevokeAndDeleteConfirmationProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  provider: string;
  isLoading?: boolean;
}

export function RevokeAndDeleteConfirmation({
  isOpen,
  onClose,
  onConfirm,
  provider,
  isLoading = false,
}: RevokeAndDeleteConfirmationProps) {
  const { t } = useLanguage();

  return (
    <AlertDialog open={isOpen} onOpenChange={onClose}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>
            {t("workspace.revokeAndDelete")}
          </AlertDialogTitle>
          <AlertDialogDescription className="space-y-2">
            <p>{t("workspace.revokeAndDeleteConfirm")}</p>
            <p className="text-sm text-muted-foreground">
              {t("workspace.revokeAndDeleteWarning", { provider })}
            </p>
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel disabled={isLoading}>
            {t("common.cancel")}
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={onConfirm}
            disabled={isLoading}
            className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
          >
            {isLoading
              ? t("workspace.revokingAndDeleting")
              : t("workspace.revokeAndDelete")}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
