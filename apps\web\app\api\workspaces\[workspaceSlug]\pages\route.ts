import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import db from "@/lib/shared-db";
import { authOptions } from "@/lib/next-auth/src/auth-options";

// Get pages for a workspace
export async function GET(
  request: Request,
  { params }: { params: { workspaceSlug: string } }
) {
  try {
    const session: any = await getServerSession(authOptions);
    const url = new URL(request.url);
    const userIdFromParams = url.searchParams.get("userId");

    // Use userId from params if session is null (server-side call)
    // or use session.userId if available (client-side call)
    const userId = userIdFromParams || session?.userId;

    if (!userId) {
      return NextResponse.json(
        {
          error:
            "Unauthorized - userId is required either in session or as a query parameter",
        },
        { status: 401 }
      );
    }

    const decodedWorkspaceSlug = decodeURI(params.workspaceSlug);

    const workspace = await db.workspace.findUnique({
      where: {
        slug: decodedWorkspaceSlug,
      },
      include: {
        pages: true,
      },
    });

    if (!workspace) {
      return NextResponse.json(
        { error: "Workspace not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({
      pages: workspace.pages,
    });
  } catch (error) {
    console.error("Error fetching pages:", error);
    return NextResponse.json(
      { error: "Failed to fetch pages" },
      { status: 500 }
    );
  }
}

// Create a new page
export async function POST(
  request: Request,
  { params }: { params: { workspaceSlug: string } }
) {
  try {
    const session: any = await getServerSession(authOptions);
    const url = new URL(request.url);
    const userIdFromParams = url.searchParams.get("userId");

    // Use userId from params if session is null (server-side call)
    // or use session.userId if available (client-side call)
    const userId = userIdFromParams || session?.userId;

    if (!userId) {
      return NextResponse.json(
        {
          error:
            "Unauthorized - userId is required either in session or as a query parameter",
        },
        { status: 401 }
      );
    }

    const { name } = await request.json();

    if (!name) {
      return NextResponse.json(
        { error: "Page name is required" },
        { status: 400 }
      );
    }

    const decodedWorkspaceSlug = decodeURI(params.workspaceSlug);

    const workspace = await db.workspace.findUnique({
      where: {
        slug: decodedWorkspaceSlug,
      },
    });

    if (!workspace) {
      return NextResponse.json(
        { error: "Workspace not found" },
        { status: 404 }
      );
    }

    const page = await db.page.create({
      data: {
        name,
        workspaceId: workspace.id,
      },
    });

    return NextResponse.json(
      {
        message: "Page created successfully",
        data: page,
      },
      { status: 201 }
    );
  } catch (error) {
    console.error("Error creating page:", error);
    return NextResponse.json(
      { error: "Failed to create page" },
      { status: 500 }
    );
  }
}
