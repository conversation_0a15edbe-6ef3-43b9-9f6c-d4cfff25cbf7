import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/next-auth";
import db from "@/lib/shared-db";

export async function GET(
  _request: NextRequest,
  { params }: { params: { fileId: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { fileId } = params;

    if (!fileId) {
      return NextResponse.json(
        { error: "File ID is required" },
        { status: 400 }
      );
    }

    // Get the file with its current status using Prisma
    const file = await db.file.findUnique({
      where: {
        id: fileId,
      },
      select: {
        id: true,
        vectorizationStatus: true,
        vectorizedAt: true,
        updatedAt: true,
      },
    });

    if (!file) {
      return NextResponse.json(
        { error: "File not found" },
        { status: 404 }
      );
    }

    // Return the current status
    return NextResponse.json({
      id: file.id,
      vectorizationStatus: file.vectorizationStatus || "PENDING",
      vectorizedAt: file.vectorizedAt,
      updatedAt: file.updatedAt,
    });

  } catch (error) {
    console.error("Error fetching file status:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
