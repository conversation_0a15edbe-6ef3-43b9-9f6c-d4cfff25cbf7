"use client";

import React, { useState } from "react";

import { usePathname } from "next/navigation";

import { Avatar, AvatarFallback, AvatarImage } from "../ui/avatar";
import { Input } from "../ui/input";
import { Button } from "../ui/button";
import { Upload, User } from "lucide-react";
import { useLanguage } from "@/lib/language-context";
import { useTranslatedToast } from "@/hooks/use-translated-toast";

export default function ImageUpload({ id, updatedAt }) {
  const { t } = useLanguage();
  const toast = useTranslatedToast();
  const [blobUrl, setBlobUrl] = useState("");
  const [file, setFile] = useState("");
  const fileTypes = [
    "image/jpeg",
    "image/jpg",
    "image/png",
    "image/webp",
    "image/heic",
    "application/pdf",
  ];

  const isFileValid = (uploadedFile: File) => {
    if (!fileTypes.includes(uploadedFile.type)) {
      toast.error("toast.invalidFileFormat");
      return false;
    }
    if (uploadedFile?.size > 10e6) {
      toast.error("toast.fileSizeTooLarge");
      return false;
    }
    return true;
  };
  const handleSave = async (file: any) => {
    toast.dismiss();
    toast.loading("toast.uploadingImage");
  };
  const getImageData = async (event: any) => {
    const isValid = isFileValid(event[0]);
    const dataTransfer = new DataTransfer();
    Array.from(event).forEach((image) => dataTransfer.items.add(image as any));
    const files = dataTransfer.files;

    if (isValid) {
      setFile((files as any)?.[0]);
      const displayUrl = URL.createObjectURL((files as any)?.[0]);
      return displayUrl;
    }
  };
  const handleImageUpload = async (event: any) => {
    const file = event?.target?.files ?? event;
    const imageData: any = await getImageData(file);
    setBlobUrl(imageData);
    handleSave(file?.[0]);
    return;
  };

  return (
    <div className="py-3">
      <div className="flex gap-x-6">
        <Avatar className="h-[100px] w-[100px] rounded border">
          <AvatarImage
            src={
              blobUrl ||
              `${process.env.NEXT_PUBLIC_CAMPED_ACCOUNTS_CLOUDFRONT_URL}/${process.env.NEXT_PUBLIC_ACCOUNTS_ENVIRONMENT}/user-profile/${id}?date=${updatedAt}`
            }
            alt="@camped"
          />
          <AvatarFallback className="rounded">
            <User />
          </AvatarFallback>
        </Avatar>

        <div className="ml-4 space-y-2">
          <div className="flex items-center gap-x-2">
            <div className="flex flex-col gap-2">
              <h1 className="font-semibold">{t("image.picture")}</h1>
              <div className="text-xs text-gray-500">
                {t("image.supportedFormats")}
              </div>
              <Input
                type="file"
                accept=".jpeg,.png,.jpg,.webp,.heic"
                id="attachment"
                name="attachment"
                className="hidden"
                onChange={handleImageUpload}
                onClick={(event: any) => {
                  const hello = event.target.value;
                }}
              />
              <Button type="button" variant="outline">
                <Upload className="mr-2 h-4 w-4" />
                <label htmlFor="attachment" className="file-label">
                  {t("image.uploadImage")}
                </label>
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
