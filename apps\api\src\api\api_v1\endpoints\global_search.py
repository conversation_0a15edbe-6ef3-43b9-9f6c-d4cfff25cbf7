from fastapi import APIRouter, Depends, Query, HTTPException
from typing import List, Dict, Any, Optional
from pydantic import BaseModel
from motor.motor_asyncio import AsyncIOMotorDatabase
from bson import ObjectId
import logging
from datetime import datetime
import os
from src.api.deps import get_db
from src.api.auth import validate_token
from src.utils.encryption import decrypt_message_content, decrypt_field
from src.utils.simple_encryption import decrypt_message_content_simple, is_simple_encryption_available


logger = logging.getLogger(__name__)

router = APIRouter()

class SearchResult(BaseModel):
    id: str
    type: str  # 'file', 'page', 'folder', 'workspace', 'chat', 'message'
    title: str
    content: Optional[str] = None
    snippet: Optional[str] = None
    workspace_id: Optional[str] = None
    slug: Optional[str] = None
    workspace_name: Optional[str] = None
    created_at: Optional[str] = None
    updated_at: Optional[str] = None
    score: Optional[float] = None
    metadata: Optional[Dict[str, Any]] = None

class GlobalSearchResponse(BaseModel):
    results: List[SearchResult]
    total_count: int
    categories: Dict[str, int]
    has_more: bool
    search_time_ms: int
    error: Optional[str] = None

class SearchFilters(BaseModel):
    types: Optional[List[str]] = None  # ['file', 'page', 'folder', 'workspace', 'chat', 'message']
    workspace_ids: Optional[List[str]] = None
    date_from: Optional[str] = None
    date_to: Optional[str] = None

async def get_user_role_and_permissions(db: AsyncIOMotorDatabase, user_id: str, tenant_id: str) -> Dict[str, Any]:
    """Get user's role and permissions within the tenant"""
    try:
        # Get user's membership in the tenant
        membership = await db.Membership.find_one({
            "userId": ObjectId(user_id),
            "tenantId": ObjectId(tenant_id)
        })

        if not membership:
            raise HTTPException(status_code=403, detail="User not found in tenant")

        role = membership.get("role", "MEMBER")

        # Get accessible workspace IDs based on role
        accessible_workspaces = []

        if role in ["ADMIN", "OWNER"]:
            # Admin and Owner can access all workspaces in the tenant
            workspaces = await db.Workspace.find({"tenantId": ObjectId(tenant_id)}).to_list(None)
            accessible_workspaces = [str(ws["_id"]) for ws in workspaces]
        else:
            # For MEMBER and CUSTOM roles, get workspaces they have access to
            workspace_members = await db.WorkspaceMember.find({
                "userId": ObjectId(user_id)
            }).to_list(None)

            for wm in workspace_members:
                workspace = await db.Workspace.find_one({
                    "_id": wm["workspaceId"],
                    "tenantId": ObjectId(tenant_id)
                })
                if workspace:
                    accessible_workspaces.append(str(workspace["_id"]))

        return {
            "role": role,
            "accessible_workspaces": accessible_workspaces,
            "can_search_all": role in ["ADMIN", "OWNER"],
            "can_search_workspaces": len(accessible_workspaces) > 0 or role == "CUSTOM"
        }
    except Exception as e:
        logger.error(f"Error getting user permissions: {str(e)}")
        raise HTTPException(status_code=500, detail="Error checking user permissions")

async def search_files(
    db: AsyncIOMotorDatabase,
    query: str,
    accessible_workspaces: List[str],
    limit: int = 10,
    skip: int = 0
) -> List[SearchResult]:
    """Search files with text matching"""
    try:
        search_filter = {
            "workspaceId": {"$in": [ObjectId(ws_id) for ws_id in accessible_workspaces]},
            "$or": [
                {"name": {"$regex": query, "$options": "i"}},
                {"content": {"$regex": query, "$options": "i"}}
            ]
        }

        files = await db.File.find(search_filter).skip(skip).limit(limit).to_list(None)
        results = []
        for file in files:
            # Get workspace name
            workspace = await db.Workspace.find_one({"_id": file["workspaceId"]})
            workspace_name = workspace.get("name", "") if workspace else ""
            slug = workspace.get("slug", "") if workspace else ""

            # Create snippet from content
            content = file.get("content", "")
            snippet = ""
            if content and query.lower() in content.lower():
                start_idx = max(0, content.lower().find(query.lower()) - 50)
                end_idx = min(len(content), start_idx + 200)
                snippet = content[start_idx:end_idx]
                if start_idx > 0:
                    snippet = "..." + snippet
                if end_idx < len(content):
                    snippet = snippet + "..."

            results.append(SearchResult(
                id=str(file["_id"]),
                type="file",
                title=file.get("name", ""),
                content=content[:500] if content else None,
                snippet=snippet,
                workspace_id=str(file["workspaceId"]),
                slug=slug,
                workspace_name=workspace_name,
                created_at=file.get("createdAt").isoformat() if file.get("createdAt") else None,
                updated_at=file.get("updatedAt").isoformat() if file.get("updatedAt") else None,
                metadata={
                    "type": file.get("type"),
                    "extension": file.get("extension"),
                    "size": file.get("size")
                }
            ))

        return results
    except Exception as e:
        logger.error(f"Error searching files: {str(e)}")
        return []

async def search_pages(
    db: AsyncIOMotorDatabase,
    query: str,
    accessible_workspaces: List[str],
    limit: int = 10,
    skip: int = 0
) -> List[SearchResult]:
    """Search pages with text matching"""
    try:
        search_filter = {
            "workspaceId": {"$in": [ObjectId(ws_id) for ws_id in accessible_workspaces]},
            "$or": [
                {"name": {"$regex": query, "$options": "i"}},
                {"content": {"$regex": query, "$options": "i"}}
            ]
        }

        pages = await db.Page.find(search_filter).skip(skip).limit(limit).to_list(None)

        results = []
        for page in pages:
            # Get workspace name
            workspace = await db.Workspace.find_one({"_id": page["workspaceId"]})
            workspace_name = workspace.get("name", "") if workspace else ""

            # Create snippet from content
            content = page.get("content", "")
            snippet = ""
            if content and query.lower() in content.lower():
                start_idx = max(0, content.lower().find(query.lower()) - 50)
                end_idx = min(len(content), start_idx + 200)
                snippet = content[start_idx:end_idx]
                if start_idx > 0:
                    snippet = "..." + snippet
                if end_idx < len(content):
                    snippet = snippet + "..."

            results.append(SearchResult(
                id=str(page["_id"]),
                type="page",
                title=page.get("name", ""),
                content=content[:500] if content else None,
                snippet=snippet,
                workspace_id=str(page["workspaceId"]),
                workspace_name=workspace_name,
                slug=workspace.get("slug", "") if workspace else "",
                created_at=page.get("createdAt").isoformat() if page.get("createdAt") else None,
                updated_at=page.get("updatedAt").isoformat() if page.get("updatedAt") else None,
                metadata={}
            ))

        return results
    except Exception as e:
        logger.error(f"Error searching pages: {str(e)}")
        return []

async def search_folders(
    db: AsyncIOMotorDatabase,
    query: str,
    accessible_workspaces: List[str],
    limit: int = 10,
    skip: int = 0
) -> List[SearchResult]:
    """Search folders with text matching"""
    try:
        search_filter = {
            "workspaceId": {"$in": [ObjectId(ws_id) for ws_id in accessible_workspaces]},
            "name": {"$regex": query, "$options": "i"}
        }

        folders = await db.Folder.find(search_filter).skip(skip).limit(limit).to_list(None)

        results = []
        for folder in folders:
            # Get workspace name
            workspace = await db.Workspace.find_one({"_id": folder["workspaceId"]})
            workspace_name = workspace.get("name", "") if workspace else ""

            results.append(SearchResult(
                id=str(folder["_id"]),
                type="folder",
                title=folder.get("name", ""),
                workspace_id=str(folder["workspaceId"]),
                workspace_name=workspace_name,
                slug=workspace.get("slug", "") if workspace else "",
                created_at=folder.get("createdAt").isoformat() if folder.get("createdAt") else None,
                updated_at=folder.get("updatedAt").isoformat() if folder.get("updatedAt") else None,
                metadata={}
            ))

        return results
    except Exception as e:
        logger.error(f"Error searching folders: {str(e)}")
        return []

async def search_workspaces(
    db: AsyncIOMotorDatabase,
    query: str,
    accessible_workspaces: List[str],
    limit: int = 10,
    skip: int = 0
) -> List[SearchResult]:
    """Search workspaces with text matching"""
    try:
        search_filter = {
            "_id": {"$in": [ObjectId(ws_id) for ws_id in accessible_workspaces]},
            "$or": [
                {"name": {"$regex": query, "$options": "i"}},
                {"description": {"$regex": query, "$options": "i"}}
            ]
        }

        workspaces = await db.Workspace.find(search_filter).skip(skip).limit(limit).to_list(None)

        results = []
        for workspace in workspaces:
            results.append(SearchResult(
                id=str(workspace["_id"]),
                type="workspace",
                title=workspace.get("name", ""),
                content=workspace.get("description", ""),
                workspace_id=str(workspace["_id"]),
                slug=workspace.get("slug", ""),
                workspace_name=workspace.get("name", ""),
                created_at=workspace.get("createdAt").isoformat() if workspace.get("createdAt") else None,
                updated_at=workspace.get("updatedAt").isoformat() if workspace.get("updatedAt") else None,
                metadata={
                    "slug": workspace.get("slug")
                }
            ))

        return results
    except Exception as e:
        logger.error(f"Error searching workspaces: {str(e)}")
        return []

async def search_chats(
    db: AsyncIOMotorDatabase,
    query: str,
    user_id: str,
    tenant_id: str,
    limit: int = 10,
    skip: int = 0
) -> List[SearchResult]:
    """Search user's chats with text matching"""
    try:
        search_filter = {
            "userId": ObjectId(user_id),
            "tenantId": ObjectId(tenant_id),
            "title": {"$regex": query, "$options": "i"}
        }

        chats = await db.Chat.find(search_filter).skip(skip).limit(limit).to_list(None)

        results = []
        for chat in chats:
            results.append(SearchResult(
                id=str(chat["_id"]),
                type="chat",
                title=chat.get("title", "Untitled Chat"),
                created_at=chat.get("createdAt").isoformat() if chat.get("createdAt") else None,
                updated_at=chat.get("updatedAt").isoformat() if chat.get("updatedAt") else None,
                metadata={
                    "message_count": await db.Message.count_documents({"chatId": chat["_id"]})
                }
            ))

        return results
    except Exception as e:
        logger.error(f"Error searching chats: {str(e)}")
        return []

async def search_messages(
    db: AsyncIOMotorDatabase,
    query: str,
    user_id: str,
    tenant_id: str,
    limit: int = 10,
    skip: int = 0
) -> List[SearchResult]:
    """Search user's messages with text matching (handles encrypted content)"""
    try:
        # First get user's chats
        user_chats = await db.Chat.find({
            "userId": ObjectId(user_id),
            "tenantId": ObjectId(tenant_id)
        }).to_list(None)

        chat_ids = [chat["_id"] for chat in user_chats]

        if not chat_ids:
            return []

        # Get all messages from user's chats (we need to decrypt first, then search)
        # We can't search encrypted content directly, so we get more messages and filter after decryption
        all_messages = await db.Message.find({
            "chatId": {"$in": chat_ids}
        }).to_list(None)

        # Decrypt messages and filter by search query
        matching_messages = []
        for message in all_messages:
            try:
                # Try primary decryption method first
                decrypted_message = decrypt_message_content(message.copy())
                decrypted_content = decrypted_message.get("content", "")

                # If primary decryption didn't change the content, try simple decryption
                if decrypted_content == message.get("content", "") and is_simple_encryption_available():
                    decrypted_message = decrypt_message_content_simple(message.copy())
                    decrypted_content = decrypted_message.get("content", "")

                # Check if the decrypted content matches the search query
                if decrypted_content and query.lower() in decrypted_content.lower():
                    # Store the decrypted content in the message for later use
                    message["decrypted_content"] = decrypted_content
                    matching_messages.append(message)

            except Exception as decrypt_error:
                logger.warning(f"Failed to decrypt message {message.get('_id')}: {str(decrypt_error)}")
                # If decryption fails, try searching the original content as fallback
                original_content = message.get("content", "")
                if original_content and query.lower() in original_content.lower():
                    message["decrypted_content"] = original_content
                    matching_messages.append(message)

        # Apply pagination to the filtered results
        paginated_messages = matching_messages[skip:skip + limit]

        results = []
        for message in paginated_messages:
            # Get chat title
            chat = await db.Chat.find_one({"_id": message["chatId"]})
            chat_title = chat.get("title", "Untitled Chat") if chat else "Unknown Chat"

            # Use decrypted content for display
            content = message.get("decrypted_content", message.get("content", ""))
            snippet = ""
            if content and query.lower() in content.lower():
                start_idx = max(0, content.lower().find(query.lower()) - 50)
                end_idx = min(len(content), start_idx + 200)
                snippet = content[start_idx:end_idx]
                if start_idx > 0:
                    snippet = "..." + snippet
                if end_idx < len(content):
                    snippet = snippet + "..."

            results.append(SearchResult(
                id=str(message["_id"]),
                type="message",
                title=f"Message in {chat_title}",
                content=content[:500] if content else None,
                snippet=snippet,
                created_at=message.get("createdAt").isoformat() if message.get("createdAt") else None,
                updated_at=message.get("updatedAt").isoformat() if message.get("updatedAt") else None,
                metadata={
                    "role": message.get("role"),
                    "chat_id": str(message["chatId"]),
                    "chat_title": chat_title
                }
            ))

        return results
    except Exception as e:
        logger.error(f"Error searching messages: {str(e)}")
        return []

@router.get("/search", response_model=GlobalSearchResponse)
async def global_search(
    query: str = Query(..., description="The search query"),
    tenant_id: str = Query(..., description="The tenant ID"),
    user_id: str = Query(..., description="The user ID"),
    types: Optional[str] = Query(None, description="Comma-separated list of types to search (file,page,folder,workspace,chat,message)"),
    workspace_ids: Optional[str] = Query(None, description="Comma-separated list of workspace IDs to filter by"),
    limit: int = Query(20, description="Maximum number of results to return"),
    skip: int = Query(0, description="Number of results to skip"),
    db: AsyncIOMotorDatabase = Depends(get_db)
):
    """
    Perform a global search across all accessible content based on user role.

    Role-based access:
    - ADMIN/OWNER: Can search files, pages, folders, workspaces, and their AI conversations
    - CUSTOM: Can search files, pages, folders, workspaces they have access to, and their AI conversations
    - MEMBER: Can only search their AI conversations (messages and chat titles)
    """
    start_time = datetime.now()

    try:
        # Get user permissions
        permissions = await get_user_role_and_permissions(db, user_id, tenant_id)
        role = permissions["role"]
        accessible_workspaces = permissions["accessible_workspaces"]

        # Parse search types
        search_types = []
        if types:
            search_types = [t.strip() for t in types.split(",")]
        else:
            # Default types based on role
            if role in ["ADMIN", "OWNER"]:
                search_types = ["file", "page", "folder", "workspace", "chat", "message"]
            elif role == "CUSTOM":
                search_types = ["file", "page", "folder", "workspace", "chat", "message"]
            else:  # MEMBER
                search_types = ["chat", "message"]

        # Filter workspace IDs if provided
        if workspace_ids:
            requested_workspace_ids = [ws_id.strip() for ws_id in workspace_ids.split(",")]
            # Only include workspaces the user has access to
            accessible_workspaces = [ws_id for ws_id in requested_workspace_ids if ws_id in accessible_workspaces]

        all_results = []
        categories = {}

        # Search based on role and requested types
        if role != "MEMBER":  # ADMIN, OWNER, or CUSTOM with workspace access
            if "file" in search_types and accessible_workspaces:
                file_results = await search_files(db, query, accessible_workspaces, limit//6, skip)
                all_results.extend(file_results)
                categories["file"] = len(file_results)

            if "page" in search_types and accessible_workspaces:
                page_results = await search_pages(db, query, accessible_workspaces, limit//6, skip)
                all_results.extend(page_results)
                categories["page"] = len(page_results)

            if "folder" in search_types and accessible_workspaces:
                folder_results = await search_folders(db, query, accessible_workspaces, limit//6, skip)
                all_results.extend(folder_results)
                categories["folder"] = len(folder_results)

            if "workspace" in search_types and accessible_workspaces:
                workspace_results = await search_workspaces(db, query, accessible_workspaces, limit//6, skip)
                all_results.extend(workspace_results)
                categories["workspace"] = len(workspace_results)

        # All roles can search their own chats and messages
        if "chat" in search_types:
            chat_results = await search_chats(db, query, user_id, tenant_id, limit//6, skip)
            all_results.extend(chat_results)
            categories["chat"] = len(chat_results)

        if "message" in search_types:
            message_results = await search_messages(db, query, user_id, tenant_id, limit//6, skip)
            all_results.extend(message_results)
            categories["message"] = len(message_results)

        # Sort results by relevance (you can implement more sophisticated scoring)
        all_results.sort(key=lambda x: x.updated_at or x.created_at or "", reverse=True)

        # Apply final limit
        final_results = all_results[:limit]

        end_time = datetime.now()
        search_time_ms = int((end_time - start_time).total_seconds() * 1000)

        return GlobalSearchResponse(
            results=final_results,
            total_count=len(all_results),
            categories=categories,
            has_more=len(all_results) > limit,
            search_time_ms=search_time_ms
        )

    except Exception as e:
        logger.error(f"Error in global search: {str(e)}")
        raise HTTPException(status_code=500, detail="Search failed")
