#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Simple JavaScript minifier
function minifyJS(code) {
  return code
    // Remove comments (single line and multi-line)
    .replace(/\/\*[\s\S]*?\*\//g, '')
    .replace(/\/\/.*$/gm, '')
    
    // Remove unnecessary whitespace
    .replace(/\s+/g, ' ')
    .replace(/\s*([{}();,=+\-*/<>!&|])\s*/g, '$1')
    .replace(/;\s*}/g, ';}')
    .replace(/{\s*/g, '{')
    .replace(/}\s*/g, '}')
    .replace(/\s*;\s*/g, ';')
    
    // Remove trailing semicolons before }
    .replace(/;}/g, '}')
    
    // Remove spaces around operators
    .replace(/\s*=\s*/g, '=')
    .replace(/\s*\+\s*/g, '+')
    .replace(/\s*-\s*/g, '-')
    .replace(/\s*\*\s*/g, '*')
    .replace(/\s*\/\s*/g, '/')
    .replace(/\s*<\s*/g, '<')
    .replace(/\s*>\s*/g, '>')
    .replace(/\s*!\s*/g, '!')
    .replace(/\s*&\s*/g, '&')
    .replace(/\s*\|\s*/g, '|')
    
    // Remove spaces around parentheses and brackets
    .replace(/\s*\(\s*/g, '(')
    .replace(/\s*\)\s*/g, ')')
    .replace(/\s*\[\s*/g, '[')
    .replace(/\s*\]\s*/g, ']')
    
    // Remove unnecessary spaces
    .replace(/\s*,\s*/g, ',')
    .replace(/\s*:\s*/g, ':')
    .replace(/\s*\?\s*/g, '?')
    
    // Clean up any remaining multiple spaces
    .replace(/\s{2,}/g, ' ')
    .trim();
}

// Main minification function
function minifySDK() {
  const inputPath = path.join(__dirname, '../public/sdk/chatbot-sdk.js');
  const outputPath = path.join(__dirname, '../public/sdk/chatbot-sdk.min.js');
  
  try {
    console.log('Reading SDK file...');
    const originalCode = fs.readFileSync(inputPath, 'utf8');
    
    console.log('Minifying...');
    let minifiedCode = minifyJS(originalCode);
    
    // Add a header comment
    const header = `/*! Swiss Knowledge Hub Chatbot SDK v1.0 - Minified */\n`;
    minifiedCode = header + minifiedCode;
    
    console.log('Writing minified file...');
    fs.writeFileSync(outputPath, minifiedCode, 'utf8');
    
    const originalSize = originalCode.length;
    const minifiedSize = minifiedCode.length;
    const reduction = ((originalSize - minifiedSize) / originalSize * 100).toFixed(1);
    
    console.log(`✅ Minification complete!`);
    console.log(`📊 Original size: ${originalSize.toLocaleString()} bytes`);
    console.log(`📊 Minified size: ${minifiedSize.toLocaleString()} bytes`);
    console.log(`📊 Size reduction: ${reduction}%`);
    
  } catch (error) {
    console.error('❌ Error during minification:', error.message);
    process.exit(1);
  }
}

// Run the minifier
if (require.main === module) {
  minifySDK();
}

module.exports = { minifySDK, minifyJS };
