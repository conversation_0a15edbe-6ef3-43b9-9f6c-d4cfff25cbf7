"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Building, Eye, EyeOff, Loader2, Mail, User } from "lucide-react";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { verifyInvitation, acceptInvitation } from "@/services";
import toast from "react-hot-toast";
import { useLanguage } from "@/lib/language-context";

// Create form schemas based on user status
const createFormSchemas = (t: any) => ({
  loggedInSchema: z.object({}),
  loggedOutExistingUserSchema: z.object({}),
  loggedOutNewUserSchema: z
    .object({
      name: z
        .string()
        .min(2, { message: t("invitationAcceptance.nameMinLength") }),
      password: z
        .string()
        .min(8, { message: t("invitationAcceptance.passwordMinLength") }),
      confirmPassword: z.string(),
    })
    .refine((data) => data.password === data.confirmPassword, {
      message: t("invitationAcceptance.passwordsDoNotMatch"),
      path: ["confirmPassword"],
    }),
});

export default function InvitationPage({
  params,
}: {
  params: { token: string };
}) {
  const router = useRouter();
  const { data: session } = useSession();
  const { t } = useLanguage();
  const [invitationData, setInvitationData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  // Create schemas with translations
  const schemas = createFormSchemas(t);

  // Determine whether to use logged in or logged out schema based on session and user existence
  const getFormSchema = () => {
    if (session) return schemas.loggedInSchema;
    if (invitationData?.userExists) return schemas.loggedOutExistingUserSchema;
    return schemas.loggedOutNewUserSchema;
  };

  const formSchema = getFormSchema();

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema as any),
    defaultValues: {
      name: "",
      password: "",
      confirmPassword: "",
    },
  });

  // Reset form when invitation data changes (to handle schema changes)
  useEffect(() => {
    form.reset({
      name: "",
      password: "",
      confirmPassword: "",
    });
  }, [invitationData?.userExists, form]);

  // Fetch invitation data
  useEffect(() => {
    const fetchInvitation = async () => {
      setLoading(true);
      setError(null);

      const result = await verifyInvitation(params.token);

      if (result.error) {
        setError(result.error);
      } else {
        setInvitationData(result.data);
      }

      setLoading(false);
    };

    if (params.token) {
      fetchInvitation();
    }
  }, [params.token]);

  // Handle form submission
  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    setSubmitting(true);
    setError(null);

    try {
      const data: any = { token: params.token };

      // Add password and name if user is not logged in and is a new user
      if (!session && !invitationData?.userExists) {
        // Type check for new user schema values
        const newUserValues = values as z.infer<
          typeof schemas.loggedOutNewUserSchema
        >;
        if (newUserValues.password && newUserValues.name) {
          data.password = newUserValues.password;
          data.name = newUserValues.name;
        }
      }

      const result = await acceptInvitation(data);

      if (result.error) {
        toast.error(result.error);
        setError(result.error);
      } else {
        toast.success(t("invitationAcceptance.invitationAcceptedSuccess"));

        // Redirect to dashboard or sign-in based on session status
        if (session) {
          router.push("/dashboard");
        } else {
          router.push("/sign-in");
        }
      }
    } catch (error: any) {
      toast.error(t("invitationAcceptance.failedToAccept"));
      setError(error.message || t("invitationAcceptance.failedToAccept"));
    } finally {
      setSubmitting(false);
    }
  };

  if (loading) {
    return (
      <div className="flex min-h-screen flex-col items-center justify-center">
        <div className="flex flex-col items-center gap-2">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <p>{t("invitationAcceptance.verifyingInvitation")}</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex min-h-screen flex-col items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardHeader className="space-y-1">
            <CardTitle className="text-2xl font-bold">
              {t("invitationAcceptance.invitationError")}
            </CardTitle>
            <CardDescription>
              {t("invitationAcceptance.couldNotVerify")}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="bg-destructive/10 text-destructive rounded-lg p-4 mb-4">
              {error}
            </div>
          </CardContent>
          <CardFooter>
            <Button asChild className="w-full">
              <Link href="/">{t("invitationAcceptance.returnToHome")}</Link>
            </Button>
          </CardFooter>
        </Card>
      </div>
    );
  }

  // Render the invitation acceptance form
  return (
    <div className="flex min-h-screen flex-col items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="space-y-1">
          <div className="flex justify-center mb-6">
            {/* Logo or avatar for the organization */}
            <div className="rounded-full bg-primary/10 p-4">
              <Building className="h-8 w-8 text-primary" />
            </div>
          </div>
          <CardTitle className="text-center text-2xl font-bold">
            {t("invitationAcceptance.joinOrganization", {
              tenantName: invitationData?.tenantName,
            })}
          </CardTitle>
          <CardDescription className="text-center">
            {t("invitationAcceptance.invitedByAs", {
              inviterName: invitationData?.inviterName,
              role: invitationData?.role.toLowerCase(),
            })}
            {!session && invitationData?.userExists && (
              <span className="block mt-2 text-sm text-muted-foreground">
                {t("invitationAcceptance.welcomeBack", {
                  name: invitationData?.existingUserName,
                })}
              </span>
            )}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <div className="flex items-center gap-2 p-3 bg-muted/50 rounded-md">
                <Mail className="h-5 w-5 text-muted-foreground" />
                <span className="text-sm font-medium">
                  {invitationData?.email}
                </span>
              </div>

              {/* Show password fields only for new users who are not logged in */}
              {!session && !invitationData?.userExists && (
                <>
                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          {t("invitationAcceptance.yourName")}
                        </FormLabel>
                        <FormControl>
                          <Input
                            placeholder={t(
                              "invitationAcceptance.enterFullName"
                            )}
                            {...field}
                            autoComplete="name"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="password"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          {t("invitationAcceptance.createPassword")}
                        </FormLabel>
                        <FormControl>
                          <div className="relative">
                            <Input
                              placeholder="••••••••"
                              type={showConfirmPassword ? "text" : "password"}
                              {...field}
                            />
                            <button
                              type="button"
                              className="absolute right-3 top-2.5 text-muted-foreground"
                              onClick={() =>
                                setShowConfirmPassword(!showConfirmPassword)
                              }
                            >
                              {showConfirmPassword ? (
                                <EyeOff className="h-4 w-4" />
                              ) : (
                                <Eye className="h-4 w-4" />
                              )}
                            </button>
                          </div>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="confirmPassword"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          {t("invitationAcceptance.confirmPassword")}
                        </FormLabel>
                        <FormControl>
                          <div className="relative">
                            <Input
                              placeholder="••••••••"
                              type={showConfirmPassword ? "text" : "password"}
                              {...field}
                            />
                            <button
                              type="button"
                              className="absolute right-3 top-2.5 text-muted-foreground"
                              onClick={() =>
                                setShowConfirmPassword(!showConfirmPassword)
                              }
                            >
                              {showConfirmPassword ? (
                                <EyeOff className="h-4 w-4" />
                              ) : (
                                <Eye className="h-4 w-4" />
                              )}
                            </button>
                          </div>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </>
              )}

              {/* Show message for existing users who are not logged in */}
              {!session && invitationData?.userExists && (
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <div className="flex items-center gap-2">
                    <User className="h-5 w-5 text-blue-600" />
                    <div>
                      <p className="text-sm font-medium text-blue-900">
                        {t("invitationAcceptance.accountFound")}
                      </p>
                      <p className="text-sm text-blue-700">
                        {t("invitationAcceptance.accountExistsMessage")}{" "}
                        <Link
                          href={`/sign-in?callbackUrl=/invitations/${params.token}`}
                          className="underline font-medium"
                        >
                          {t("invitationAcceptance.signInFirst")}
                        </Link>{" "}
                        {t("invitationAcceptance.accountExistsMessageEnd")}
                      </p>
                    </div>
                  </div>
                </div>
              )}

              <Button type="submit" className="w-full" disabled={submitting}>
                {submitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    {t("invitationAcceptance.processing")}
                  </>
                ) : session ? (
                  t("invitationAcceptance.acceptInvitation")
                ) : invitationData?.userExists ? (
                  t("invitationAcceptance.acceptInvitation")
                ) : (
                  t("invitationAcceptance.createAccountAndAccept")
                )}
              </Button>
            </form>
          </Form>
        </CardContent>
        <CardFooter className="flex flex-col space-y-2">
          <div className="text-sm text-center text-muted-foreground">
            {session ? (
              <p>
                {t("invitationAcceptance.notCurrentUser", {
                  name: session.user?.name,
                })}{" "}
                <Link href="/api/auth/signout" className="underline">
                  {t("invitationAcceptance.signOut")}
                </Link>
              </p>
            ) : invitationData?.userExists ? (
              <p>
                {t("invitationAcceptance.wantToSignInFirst")}{" "}
                <Link
                  href={`/sign-in?callbackUrl=/invitations/${params.token}`}
                  className="underline"
                >
                  {t("invitationAcceptance.signIn")}
                </Link>
              </p>
            ) : (
              <p>
                {t("invitationAcceptance.alreadyHaveAccount")}{" "}
                <Link
                  href={`/sign-in?callbackUrl=/invitations/${params.token}`}
                  className="underline"
                >
                  {t("invitationAcceptance.signIn")}
                </Link>
              </p>
            )}
          </div>
        </CardFooter>
      </Card>
    </div>
  );
}
